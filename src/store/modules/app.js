import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  headerbar: {
    opened: Cookies.get('headerbarStatus') ? !!+Cookies.get('headerbarStatus') : true,
    withoutAnimation: false
  }
}

const mutations = {
  TOGGLE_HEADERBAR: state => {
    state.headerbar.opened = !state.headerbar.opened
    state.headerbar.withoutAnimation = false
    if (state.headerbar.opened) {
      Cookies.set('headerbarStatus', 1)
    } else {
      Cookies.set('headerbarStatus', 0)
    }
  },
  CLOSE_HEADERBAR: (state, withoutAnimation) => {
    Cookies.set('headerbarStatus', 0)
    state.headerbar.opened = false
    state.headerbar.withoutAnimation = withoutAnimation
  },
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  }
}

const actions = {
  toggleHeaderBar({ commit }) {
    commit('TOGGLE_HEADERBAR')
  },
  closeHeaderBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_HEADERBAR', withoutAnimation)
  },
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
