import { asyncRoutes, constantRoutes } from '@/router'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }, roles) {
    return new Promise(resolve => {
      let accessedRoutes
      if (roles.includes('admin')) {
        accessedRoutes = asyncRoutes || []
      } else {
        accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)
      }
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  },
  hasPageAccessAuth({ commit }, toPath){
    const allMenus = JSON.parse(sessionStorage.getItem('private_menu_pts'))
    const searchArray = (arr, condition) => {
      // 结果数组，用于存储符合条件的成员
      const result = []

      // 递归函数，用于遍历数组
      function search(arr) {
        for (let i = 0; i < arr.length; i++) {
          const item = arr[i]

          // 如果当前元素符合条件，将其添加到结果数组中
          if (condition(item)) {
            result.push(item)
          }

          // 如果当前元素是一个数组，则继续递归搜索
          if (Array.isArray(item.children)) {
            search(item.children)
          }
        }
      }

      // 开始搜索
      search(arr)

      return result
    }

    const filteredArray = searchArray(allMenus, (item) => {
      if(item.path === toPath){
        return item
      }
    })

    // 正常情况下，只会匹配到一个页面，即是说这里是一个length为1的数组
    return (filteredArray && filteredArray.length > 0)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
