import { login, logout } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'

const state = {
  token: getToken(),
  name: '',
  realName: '',
  roles: [],
  userId: '',
  deptId: ''
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_REAL_NAME: (state, name) => {
    state.realName = name
  },
  SET_USER_ID: (state, userId) => {
    state.userId = userId
  },
  SET_DEPT_ID: (state, deptId) => {
    state.deptId = deptId
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo).then(res => {
        // 登录成功时
        if (res && res.data && res.data.access_token) {
          const { data } = res
          const { realName, userId, deptId, deptName, firstDeptId, secondDeptId, authorities } = data
          commit('SET_TOKEN', data.access_token)// access_token属于有特殊业务功能的值，所以本地和store都存了
          setToken(data.access_token)

          commit('SET_REAL_NAME', realName)// 中文的
          sessionStorage.setItem('realName', realName)
          sessionStorage.setItem('userId', userId)
          sessionStorage.setItem('deptId', deptId)
          sessionStorage.setItem('deptName', deptName)
          sessionStorage.setItem('firstDeptId', firstDeptId)
          sessionStorage.setItem('secondDeptId', secondDeptId)
          sessionStorage.setItem('authorities', JSON.stringify(authorities))
          commit('SET_USER_ID', userId)
          commit('SET_DEPT_ID', deptId)

          resolve(res)
        } else { // 登录失败，比如账号密码错误之类的
          reject(res?.data?.msg || '请稍后再试')
        }
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout 退出登录
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        removeToken()
        sessionStorage.removeItem('realName')
        resetRouter()

        commit('SET_REAL_NAME', '')// 中文的
        commit('SET_USER_ID', '')
        commit('SET_DEPT_ID', '')

        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, { root: true })

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      removeToken()
      sessionStorage.clear()// 清除所有的本地存储
      resolve()
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
