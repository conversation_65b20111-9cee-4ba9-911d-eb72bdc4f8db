<!-- 方案批复处理页面 -->
<template>
  <div class="proposal_approval_detail page-anchor-parentpage">
    <div class="operate-btn">
      <el-button type="primary"  @click="submit()">提交</el-button>
      <!-- <el-button type="primary"  @click="save()">保存</el-button> -->
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
    ></mssWorkFlowHandel>
    <infos labelPosition="top" ref="infos" @getWorkCode="getWorkCode"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService,beforeSubmitService} from "@/api/inbound_line/proposal_approval_api.js"
export default {
  name: 'proposal_approval_detail',
  components:{infos},
  data() {
    return {
      boId: '',
      workflowCode: '',
      completeTaskUrl:"",//流程处理地址
      returnAddress:'/inbound_line/proposal_approval',//流程提交成功返回路由地址
    }
  },
  created() {
    this.boId = this.$route.query.boId
  },
  methods: {
    getWorkCode(param){
      this.workflowCode=param
      this.$nextTick(()=>{
        this.$refs.workFlow.init()
      })
    },
    // 返回上一页
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/proposal_approval'
      })
    },

    // 保存
    save(cb){
      this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid)=>{
        if(valid){
          // 处理数据
          let param={
            ...this.$refs.infos.$refs.basicForm.modelForm,
            detail:this.$refs.infos.$refs.lineForm.modelForm
          }
          if(this.$refs.infos.showlineTwo){
            param.detailOther=this.$refs.infos.$refs.lineTwoForm.modelForm
          }
          saveService(param).then(res=>{
            if(res.code=='0000'){
              if(cb){
                cb(param)
              }else{
                this.$message.success('保存成功')
              }
            }
          })
        }else{
            return
          }
      })

    },

    // 提交--先保存后提交
    submit(){
      // this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid)=>{
      //   if(valid){
          this.completeTaskUrl = this.$refs.infos.basicForm.completeTaskUrl
          this.$refs.workFlow.opendialogInitNextPath()
      //   }else{
      //       return
      //     }
      // })

    },
     //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      let name = { code: 'name', value: this.$refs.infos.$refs.basicForm.modelForm.name }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit(){
      let str=""
      await this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid)=>{
        str += valid?'':'有必填信息未填写完整，请检查; '
      })
      let res=await beforeSubmitService({boId:this.boId})
      if(res.data && res.data.code!=0){
        str+=res.data.msg
      }
      return str
    },

    beforeNode() {
      const fileNameArr = {
        netManger:{ userName: this.$refs.infos.basicForm.netManagerName, userId: this.$refs.infos.basicForm.netManagerId }
      }
      // 设置默认处理人
      return fileNameArr
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
