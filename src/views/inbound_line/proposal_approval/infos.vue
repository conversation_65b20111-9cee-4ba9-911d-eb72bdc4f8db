<!-- 方案批复公共信息（处理与查看公用） -->
<template>
  <div class="proposal_approval_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="集客专线信息">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          labelPosition="left"
          :form="lineForm"
          :disableForm="true"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListTwo" class="page-anchor-point" v-if="showlineTwo"></div>
    <mssCard title="集客专线信息" v-if="showlineTwo">
      <div slot="content">
        <mssForm
          ref="lineTwoForm"
          labelWidth="200px"
          :config="lineConfig"
          labelPosition="left"
          :form="lineTwoForm"
          :disableForm="true"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="kcboId"
      :boId="kcboId"
      businessType="GroupCustomerLineSubmit"
      :nodeName="nodeName"
      :fileTypeFlag="true"
      :dealPage="dealPage"
    ></mssAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory v-if="businessType" :boId="boId" :workflowCode="businessType"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  queryDetailService,
} from "@/api/inbound_line/proposal_approval_api.js";
import Data from '../formSetting'
export default {
  name: "proposal_approval_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      disableForm: false,
      dealPage:false,
      kcboId:'',
      basicConfig: [
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "集客专线名称",
          type: "input",
          prop: "lineName",
          span: 24,
        },
        {
          label: "投资金额",
          type: "input",
          prop: "projectReplyInvest",
          span: 12,
        },
        {
          label: "设备费（元）",
          type: "input",
          mode:'number',
          prop: "equipAmount",
          span: 12,
        },
        {
          label: "材料费（元）",
          type: "input",
          mode:'number',
          prop: "materialAmount",
          span: 12,
        },
        {
          label: "施工费（元）",
          type: "input",
          mode:'number',
          prop: "constructionAmount",
          span: 12,
        },
        {
          label: "设计费（元）",
          type: "input",
          mode:'number',
          prop: "designAmount",
          span: 12,
        },
        {
          label: "其他费用（元）",
          type: "input",
          prop: "otherFees",
          span: 12,
        },
        {
          label: "设计单位",
          type: "input",
          mode:'number',
          prop: "providerName",
          span: 12,
        },
        {
          label: "批复单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "批复人员",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "批复时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "勘查光缆长度皮长公里",
          type: "input",
          mode:'number',
          prop: "fillLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'fillLength',this.dealNum(value))
            }
          }
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig:JSON.parse(JSON.stringify( Data.lineSet)),
      lineForm: {},
      lineTwoForm: {},
      showlineTwo: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "A端<br>信息",
          id: "sectionList",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
      this.disableForm = true;
    }else{
      for (let i in this.basicConfig) {
        // if(['0','1','2','3','9','10','11','12'].includes(i))
        this.basicConfig[i].disabled =true;
      }
    }
    for (let i in this.lineConfig) {
      this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
    }
    // 浏览器传参
    this.boId = this.$route.query.boId
    this.getDetailsData()
  },
  methods: {
    dealNum(value){
      let newval= ('' + value) // 第一步：转成字符串
      .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
      .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
      .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
      .match(/^\d*(\.?\d{0,3})/g)[0] || ''
      return  newval
    },
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.lineForm = {
          ...res.data.detail,
          outsideNum:res.data.outsideNum,
          insideNum:res.data.insideNum
        };
        this.businessType=res.data.businessType
        this.$emit("getWorkCode",this.businessType)
        this.kcboId=res.data.groupCustomerLineSubmitId
        if (res.data.detailOther) {
          this.lineTwoForm = {
            ...res.data.detailOther,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
          this.pageAnchorConfig.splice(2, 0, {
            text: "Z端<br>信息",
            id: "sectionListTwo",
          });
          this.showlineTwo = true;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
