<!-- 方案批复 -->
<template>
  <div class="proposal_approval_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="集客专线方案批复列表">
      <div slot="headerBtn">
        <el-button type="primary" @click="download">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="tableHeader"
          :api="tableApi"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService ,downloadService} from "@/api/inbound_line/proposal_approval_api.js";
import { commonDown } from "@/utils/btn";
export default {
  name: "proposal_approval_list",
  data() {
    return {
      formConfig: [
        {
          label: "方案批复名称",
          type: "input",
          fieldName: "name",
        },
        {
          label: "计费编码",
          type: "input",
          fieldName: "linenumber",
        },
        {
          label: "集客专线名称",
          type: "input",
          fieldName: "linename",
        },
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "建设单位",
          type: "input",
          fieldName: "city",
        },
        {
          label: "设计单位",
          type: "input",
          fieldName: "providername",
        },
        {
          label: "业务部项目经理",
          type: "input",
          fieldName: "projectMangerName",
        },
        {
          label: "发起时间",
          type: "date1",
          fieldName: "beginDate",
        },
        {
          label: "完成时间",
          type: "date1",
          fieldName: "endDate",
        },
        {
          label: "处理流水号",
          type: "input",
          fieldName: "serialno",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
      ],
      tableHeader: [
        {
          prop: "name",
          label: "方案批复名称",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "serialno",
          label: "处理流水号",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "linename",
          label: "集客专线名称",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "linenumber",
          label: "计费编码",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "city",
          label: "建设单位",
          align: "center",
          width:100,
          tooltip:true
        },
        {
          prop: "providername",
          label: "设计单位",
          align: "center",
          width:210,
          tooltip:true
        },
        {
          prop: "projectMangerName",
          label: "业务部项目经理",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "发起时间",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "finishDate",
          label: "完成时间",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "creatorName",
          label: "拟稿人",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "拟稿时间",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          minwidth:120,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          fixed:'right',
          width:120,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{},
      loadParam:{}
    };
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标11是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.formConfig[11], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(param)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(param)
      );
      this.$refs.table.getTableData(param);
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 导出
    download() {
      commonDown({ ...this.loadParam}, downloadService);
    },

    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/inbound_line/proposal_approval/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/inbound_line/proposal_approval/view",
            query: { boId: row.id },
          });
          break;
      }
    },
  },
};
</script>
