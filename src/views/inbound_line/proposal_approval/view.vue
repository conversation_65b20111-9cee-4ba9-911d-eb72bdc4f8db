<!-- 方案批复查看页面 -->
<template>
  <div class="proposal_approval_look page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <infos labelPosition="left"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService} from "@/api/inbound_line/proposal_approval_api.js"
export default {
  name: 'proposal_approval_look',
  components:{infos},
  data() {
    return {

    }
  },
  created() {
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/proposal_approval'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
