<!-- 成本项目 -->
<template>
  <div class="cost_item_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-upload
          class="upload-btn"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>

        <el-button type="primary" @click="download">导出</el-button>
        <el-button type="primary" @click="downMode">下载模板</el-button>
        <el-button @click="del">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          selection
          serial
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  queryListService,
  downloadService,
  importService,
  delService,
} from "@/api/inbound_line/cost_item_api.js";
import { commonMultDel, commonDown } from "@/utils/btn";
export default {
  name: "cost_item_list",
  data() {
    return {
      formConfig: [
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "建设单位",
          type: "input",
          fieldName: "constructUnit",
        },
      ],
      tableHeader: [
        {
          prop: "name",
          label: "项目名称",
          minWidth: 260,
          tooltip:true,
          align: "center",
        },
        {
          prop: "code",
          label: "项目编码",
          minWidth: 130,
          tooltip:true,
          align: "center",
        },
        {
          prop: "constructOrgName",
          label: "建设单位",
          align: "center",
          tooltip:true,
        },
        {
          prop: "projectReplyDate",
          label: "立项时间",
          minWidth: 120,
          tooltip:true,
          align: "center",
        },
        {
          prop: "projectReplyInvest",
          label: "投资金额(元)",
          minWidth: 110,
          tooltip:true,
          align: "center",
        },
        {
          prop: "businessManagerName",
          label: "有线业务部项目经理名称",
          minWidth: 160,
          tooltip:true,
          align: "center",
        },
        {
          prop: "netManagerName",
          label: "网络部项目经理名称",
          minWidth: 130,
          tooltip:true,
          align: "center",
        },
      ],
      tableApi: queryListService,
      loadParam: {},
      staticSearchParam:{}
    };
  },
  mounted() {},
  methods: {
    // 查询
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },

    // 重置
    reset() {
      this.search();
    },

    // 导入
    importFile(params) {
      let param = new FormData();
      param.append("file", params.file);
      importService(param).then((res) => {
        if (res.code == "0000") {
          this.$message.success("导入成功");
          this.search();
        }
      });
    },

    // 导出
    download() {
      commonDown({ ...this.loadParam, exportType: 2 }, downloadService);
    },

    // 导出模板
    downMode() {
      commonDown({ ...this.loadParam, exportType: 1 }, downloadService);
    },

    // 删除
    del() {
      commonMultDel.call(this,
      {data:this.$refs.table.multipleSelection, delApi:delService, sucCb:(res) => {//成功后的一些操作}}）
        if (res.data == this.$refs.table.multipleSelection.length) {
          this.$message.success("删除成功");
        } else if(res.data==0) {
          this.$message.warning("已绑定流程数据不可删除");
        }else{
          this.$message.warning("部分删除成功，存在已绑定流程的数据");
        }
        this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
        this.search();
      }})
    },
  },
};
</script>

<style lang="scss" scoped>
.cost_item_list {
  .upload-btn {
    display: inline-block;
    margin-right: 10px;
  }
}
</style>
