<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="prospecting_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="headerBtn">
        <span style="color: red;font-weight: bold;">注：光缆施工距离为0，由项目经理选择投资/成本属性;大于0小于500米默认成本类；大于等于500米默认投资类</span>
      </div>
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="集客专线信息">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :form="lineForm"
          :isChange="true"
          :disableForm="disableForm"
          @onChange="changeForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListTwo" class="page-anchor-point" v-if="showlineTwo"></div>
    <mssCard title="集客专线信息" v-if="showlineTwo">
      <div slot="content">
        <mssForm
          ref="lineTwoForm"
          labelWidth="200px"
          :config="lineConfigTwo"
          :labelPosition="labelPosition"
          :form="lineTwoForm"
          :isChange="true"
          :disableForm="disableForm"
          @onChange="changeForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :fileTypeFlag="true"
      :dealPage="dealPage"
    ></mssAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory v-if="businessType" :boId="boId" :workflowCode="businessType"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      dialog-width="60%"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      :tableSingleChoice="true"
      rowKey="projectId"
      @confirm="projectConfirm"
    ></mssTableSearchDialog>
  </div>
</template>

<script>
import {
  queryDetailService,
  getProjectListService
} from "@/api/inbound_line/prospecting_api.js";
import Data from '../formSetting'
import _ from 'lodash'
export default {
  name: "prospecting_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      dealPage:false,
      projectTableApi: getProjectListService,
      searchFieldList: [
        {
          label: "项目名称",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          fieldName: "projectCode",
        },
      ],
      projectColumns: [
        {
          label: "项目名称",
          prop: "projectName",
          minWidth: 200,
        },
        {
          label: "项目编码",
          prop: "projectCode",
          minWidth: 100,
        },
        {
          label: "建设单位名称",
          prop: "orgName",
          minWidth: 200,
        },
      ],
      basicConfig: [
        {
          label: "项目名称",
          type: "input",
          prop: "groupCustomerProjectName",
          span: 12,
          eventListeners: {
            focus: this.openProjectSelectDialog, // 打开选择项目的弹窗
          },
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "设计单名",
          type: "input",
          prop: "name",
          span: 24,
        },
        {
          label: "设计单位名称",
          type: "input",
          prop: "providerName",
          span: 12,
        },
        {
          label: "专线投资归属类型",
          type: "select",
          options:[],
          prop: "investTypeId",
          span: 12,
          eventListeners: {
            change: this.changeType, // 打开选择项目的弹窗
          },
        },
        {
          label: "设备费（元）",
          type: "input",
          mode:'number',
          prop: "equipAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'equipAmount',this.dealNum(value))
            }
          },
        },
        {
          label: "材料费（元）",
          type: "input",
          mode:'number',
          prop: "materialAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'materialAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "施工费（元）",
          type: "input",
          mode:'number',
          prop: "constructionAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'constructionAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "设计费（元）",
          type: "input",
          mode:'number',
          prop: "designAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'designAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "其他费用（元）",
          type: "input",
          mode:'number',
          prop: "otherFees",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'otherFees',this.dealNum(value))
            }
          }
        },
        {
          label: "勘查光缆长度皮长公里",
          type: "input",
          mode:'number',
          readonly:true,
          prop: "fillLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'fillLength',this.dealNum3(value,num))
            }
          }
        },

        {
          label: "拟稿单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "拟稿部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig: JSON.parse(JSON.stringify(Data.lineSet)),
      lineConfigTwo: JSON.parse(JSON.stringify(Data.lineSet)),
      disableForm:false,
      lineForm: {},
      lineTwoForm: {},
      showlineTwo: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "A端<br>信息",
          id: "sectionList",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
      for (let i in this.lineConfig) {
        this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
      }
      for (let i in this.lineConfigTwo) {
        this.lineConfigTwo[i].label = `${this.lineConfigTwo[i].label}：`;
      }
      this.disableForm=true
    }else{
      this.basicConfig[0].readonly = true;
      for (let i in this.basicConfig) {
        if(['1','2','3','11','12','13','14'].includes(i))
        this.basicConfig[i].disabled = true;
      }
      for (let i in this.lineConfig) {
        if(i<17){
          this.lineConfig[i].disabled = true;
        }
        if(this.lineConfig[i].label=='专线接入模型'){
          this.lineConfig[i].prop="accessModelId"
        }
        if(this.lineConfig[i].label=='满足程度'){
          this.lineConfig[i].prop="satisfactionId"
        }
        if(this.lineConfig[i].prop=='totalfare'){
          this.lineConfig[i].eventListeners={
            change: (value)=>{
              this.$set(this.$refs.lineForm.modelForm,'totalfare',this.dealNum(value))
            }
          }
        }
        if(['sheathlength1','sheathlength2','sheathlength3','sheathlength4'].includes(this.lineConfig[i].prop)){
          this.lineConfig[i].eventListeners={
            change: (value)=>{
              this.$set(this.$refs.lineForm.modelForm,this.lineConfig[i].prop,this.dealNum3(value))
            }
          }
        }
      }
      for (let i in this.lineConfigTwo) {
        if(i<17){
          this.lineConfigTwo[i].disabled = true;
        }
        if(this.lineConfigTwo[i].label=='专线接入模型'){
          this.lineConfigTwo[i].prop="accessModelId"
        }
        if(this.lineConfigTwo[i].label=='满足程度'){
          this.lineConfigTwo[i].prop="satisfactionId"
        }
        if(this.lineConfigTwo[i].prop=='totalfare'){
          this.lineConfigTwo[i].eventListeners={
            change: (value)=>{
              this.$set(this.$refs.lineTwoForm.modelForm,'totalfare',this.dealNum(value))
            }
          }
        }
        if(['sheathlength1','sheathlength2','sheathlength3','sheathlength4'].includes(this.lineConfigTwo[i].prop)){
          this.lineConfigTwo[i].eventListeners={
            change: (value)=>{
              this.$set(this.$refs.lineTwoForm.modelForm,this.lineConfigTwo[i].prop,this.dealNum3(value))
            }
          }
        }
      }
      this.dealPage=true
    }
    // 浏览器传参
    this.boId = this.$route.query.boId
    this.$set(this.lineConfig[18], 'options', await this.$dictOptions({parentValue: "AccessModel",appCode: "001001"}))
    this.$set(this.lineConfig[19], 'options', await this.$dictOptions({parentValue: "Satisfaction",appCode: "001001"}))
    this.$set(this.lineConfigTwo[18], 'options', await this.$dictOptions({parentValue: "AccessModel",appCode: "001001"}))
    this.$set(this.lineConfigTwo[19], 'options', await this.$dictOptions({parentValue: "Satisfaction",appCode: "001001"}))
    this.$set(this.basicConfig[4], 'options', await this.$dictOptions({parentValue: "InvestType",appCode: "001001"}))
    this.getDetailsData()
  },
  methods: {
    dealNum(value){
      let newval= ('' + value) // 第一步：转成字符串
      .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
      .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
      .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
      .match(/^\d*(\.?\d{0,2})/g)[0] || ''
      return  newval
    },
    dealNum3(value){
      let newval= ('' + value) // 第一步：转成字符串
      .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
      .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
      .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
      .match(/^\d*(\.?\d{0,3})/g)[0] || ''
      return  newval
    },
    // 获取详情数据
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.businessType=res.data.businessType
        this.$emit("getWorkCode",this.businessType)
        this.lineForm = {
          ...res.data.detail,
          outsideNum:res.data.outsideNum,
          insideNum:res.data.insideNum
        };
        if (res.data.detailOther) {
          this.lineTwoForm = {
            ...res.data.detailOther,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
          this.pageAnchorConfig.splice(2, 0, {
            text: "Z端<br>信息",
            id: "sectionListTwo",
          });
          this.showlineTwo = true;
        }
      });
    },
    // 处理类型的校验
    dealoption(){
      let num=Number(this.lineForm.totalsheathlength||0)+ Number(this.lineTwoForm.totalsheathlength||0)
      num=num*1000
      if(num>0 && num<500){
        this.basicConfig[4].options.forEach(item=>{
          if(item.label=='投资类'){
            item.disabled=true
          }
        })
        this.basicConfig[0].disabled = false
        this.basicForm.investTypeId='InvestType002'
        this.basicConfig[0].rules={ required: true, message: '请选择项目名称', trigger: 'change'}
      }else if(num>=500){
        this.basicConfig[4].options.forEach(item=>{
          if(item.label=='成本类'){
            item.disabled=true
          }
        })
        this.basicConfig[0].disabled = true
        this.basicForm.investTypeId='InvestType001'
        this.$nextTick(()=>{
          this.$set(this.$refs.basicForm.modelForm,'groupCustomerProjectId','')
          this.$set(this.$refs.basicForm.modelForm,'groupCustomerProjectName','')
          this.$set(this.$refs.basicForm.modelForm,'projectCode','')
        })

      }else{
        if(this.basicForm.investTypeId=='InvestType001'){
          this.basicConfig[0].disabled = true
        }else{
          this.basicConfig[0].disabled = false
        }
      }
    },
    changeType(a){
      if(a == 'InvestType002'){
        this.basicConfig[0].disabled = false
        this.basicConfig[0].rules={ required: true, message: '请选择项目名称', trigger: 'change'}
      }else{
        this.basicConfig[0].disabled = true
        this.basicConfig[0].rules={}
        this.$set(this.$refs.basicForm.modelForm,'groupCustomerProjectId','')
        this.$set(this.$refs.basicForm.modelForm,'groupCustomerProjectName','')
        this.$set(this.$refs.basicForm.modelForm,'projectCode','')
      }
    },
    // 获取数据--计算总数据
    changeForm(param){
      param.totalsheathlength =
      ( Number(param.sheathlength1||0) + Number(param.sheathlength2||0) + Number(param.sheathlength3||0) + Number(param.sheathlength4||0)).toFixed(3);
      this.$set(this.$refs.basicForm.modelForm,'fillLength',(Number(this.$refs.lineForm.modelForm.totalsheathlength||0)+Number(this.$refs.lineTwoForm?.modelForm?.totalsheathlength||0))).toFixed(3)
    },
    // 打开选择项目的弹窗2
    openProjectSelectDialog() {
      this.$refs.projectNameSearchDialog.openDialog();
    },

    projectConfirm(data) {
      if (data && data[0]) {
        this.$refs.basicForm.modelForm = {
          ...this.$refs.basicForm.modelForm,
          groupCustomerProjectId:data[0].projectId,
          groupCustomerProjectName:data[0].projectName,
          projectCode:data[0].projectCode
        };
      }
    },

  },
};
</script>

<style lang="scss" >
.prospecting_info
.emphasize{
  .el-form-item__label{
    color: red;
  }
}
.detail{
  .emphasize{
  .el-form-item__label{
    color: #333;
  }
}
}
</style>
