<!-- 勘查查看页面 -->
<template>
  <div class="prospecting_look page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <infos labelPosition="left"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService} from "@/api/inbound_line/prospecting_api.js"
export default {
  name: 'prospecting_look',
  components:{infos},
  data() {
    return {

    }
  },
  created() {
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/prospecting'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
