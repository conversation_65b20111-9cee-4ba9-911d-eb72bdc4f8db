<!-- 勘查处理页面 -->
<template>
  <div class="prospecting_detail page-anchor-parentpage">
    <div class="operate-btn">
      <el-button type="primary" @click="submit()">提交</el-button>
      <el-button type="primary" @click="save()">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
    ></mssWorkFlowHandel>
    <infos labelPosition="top" ref="infos" :formRules="formRules" @getWorkCode="getWorkCode"> </infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import _ from 'lodash'
import { saveService ,beforeSubmitService} from '@/api/inbound_line/prospecting_api.js'
export default {
  name: 'prospecting_detail',
  components: { infos },
  data() {
    return {
      boId: '',
      workflowCode: '',
      formDataCopy: {},//表单新值
      formInitData: {},//表单初始值
      completeTaskUrl:"",//流程处理地址
      returnAddress:'/inbound_line/prospecting',//流程提交成功返回路由地址
      formRules:{}
    }
  },
  created() {
    this.boId = this.$route.query.boId
  },
  methods: {
    getWorkCode(param){
      this.workflowCode=param
      this.$nextTick(()=>{
        this.$refs.workFlow.init()
      })
    },
    // 返回上一页
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/prospecting'
      })
    },

    // 保存
    save(cb) {
      if(this.validate().flag1 && this.validate().flag2 && this.validate().flag3){
      // 处理数据
      let param = {
        ...this.$refs.infos.$refs.basicForm.modelForm,
        detail: this.$refs.infos.$refs.lineForm.modelForm
      }
      if (this.$refs.infos.showlineTwo) {
        param.detailOther = this.$refs.infos.$refs.lineTwoForm.modelForm
      }
      if(_.round((Number(param.detail.totalfare||0) + Number(param.detailOther?.totalfare||0)),2)==
        _.round((Number(param.equipAmount||0) + Number(param.materialAmount||0)+Number(param.constructionAmount||0) + Number(param.designAmount||0)+Number(param.otherFees||0) ),2)){
          saveService(param).then((res) => {
          if (res.code == '0000') {
            if (cb) {
              cb(param)
            } else {
              this.$message.success('保存成功')
            }
          }
        })
      }else{
        this.$message.warning("请确保专线信息的总投资=设备费+材料费+施工费+设计费+其他费用")
      }

      }
    },
    // 提交--先保存后提交
    submit() {
      // this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid)=>{
      //   if(valid){
      //     this.completeTaskUrl = this.$refs.infos.basicForm.completeTaskUrl
      //     this.$refs.workFlow.opendialogInitNextPath()
      //   }else{
      //       return
      //     }
      // })
      // if(this.validate().flag1 && this.validate().flag2 && this.validate().flag3){
      //   // 处理数据
      // let param = {
      //   ...this.$refs.infos.$refs.basicForm.modelForm,
      //   detail: this.$refs.infos.$refs.lineForm.modelForm
      // }
      // if (this.$refs.infos.showlineTwo) {
      //   param.detailOther = this.$refs.infos.$refs.lineTwoForm.modelForm
      // }
      // if(_.round((Number(param.detail.totalfare||0) + Number(param.detailOther?.totalfare||0)),2)==
      //   _.round((Number(param.equipAmount||0) + Number(param.materialAmount||0)+Number(param.constructionAmount||0) + Number(param.designAmount||0)+Number(param.otherFees||0) ),2)){
          this.completeTaskUrl = this.$refs.infos.basicForm.completeTaskUrl
        this.$refs.workFlow.opendialogInitNextPath()
      // }else{
      //   this.$message.warning("请确保专线信息的总投资=设备费+材料费+施工费+设计费+其他费用")
      // }

      // }
    },
    // 判断校验必填
    validate(){
      let obj={
        flag1:false,
        flag2:false,
        flag3:true
      }
      this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          obj.flag1=true
        }
      });
      this.$refs.infos.$refs.lineForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          obj.flag2=true
        }
      });
      if(this.$refs.infos.showlineTwo)this.$refs.infos.$refs.lineTwoForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          obj.flag3=true
        }else{
          obj.flag3=false
        }
      });
      return obj
    },
    //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      workFlowPrams.push({ code: 'name', value: this.$refs.infos.$refs.basicForm.modelForm.name })
      workFlowPrams.push({ code: 'investTypeId', value: this.$refs.infos.$refs.basicForm.modelForm.investTypeId })
      return workFlowPrams
    },
    beforeNode() {
      const fileNameArr = {
        kccityManger:{ userName: this.$refs.infos.basicForm.creatorName, userId: this.$refs.infos.basicForm.creatorId }
      }
      // 设置默认处理人
      return fileNameArr
    },
    async beforeSubmit(){
      let str=""
      if(this.validate().flag1 && this.validate().flag2 && this.validate().flag3){
        // 处理数据
        let param = {
          ...this.$refs.infos.$refs.basicForm.modelForm,
          detail: this.$refs.infos.$refs.lineForm.modelForm
        }
        if (this.$refs.infos.showlineTwo) {
          param.detailOther = this.$refs.infos.$refs.lineTwoForm.modelForm
        }
        if(_.round((Number(param.detail.totalfare||0) + Number(param.detailOther?.totalfare||0)),2)!==
          _.round((Number(param.equipAmount||0) + Number(param.materialAmount||0)+Number(param.constructionAmount||0) + Number(param.designAmount||0)+Number(param.otherFees||0) ),2)){
          str += '请确保专线信息的总投资=设备费+材料费+施工费+设计费+其他费用 '
        }
      }else{
        str +='有必填信息未填写完整，请检查! '
      }
      let res=await beforeSubmitService({boId:this.boId})
      if(res.data && res.data.code!=0){
        str+=res.data.msg
      }
      return str
    },
    // 必填校验
    initFormRules(param) {
      this.formRules=param
      if(this.formRules['isCityManager'] && this.formRules['isCityManager'].length){
        this.$refs.infos.dealoption()
      }
    },
    // 只读表单
    getReadonlyList(param) {
      if(param.length){
        this.$refs.infos.basicConfig.forEach(item=>{
          if(param.includes(item.prop)){
            item.disabled = true;
          }
        })
        this.$refs.infos.lineConfig.forEach(item=>{
          if(param.includes(item.prop)){
            item.disabled = true;
          }
        })
        this.$refs.infos.lineConfigTwo.forEach(item=>{
          if(param.includes(item.prop)){
            item.disabled = true;
          }
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
