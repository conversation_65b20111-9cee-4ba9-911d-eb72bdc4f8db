<!-- 勘查 -->
<template>
  <div class="prospecting_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @changeSelect="changeSelect"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="集客专线勘查列表">
      <div slot="headerBtn">
        <el-button type="primary" @click="download">导出</el-button>
      </div>
      <div slot="content">
        <mssTable ref="table" :staticSearchParam="staticSearchParam" :columns="tableHeader" :api="tableApi"></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService,downloadService } from "@/api/inbound_line/prospecting_api.js";
import { queryAreaListService } from "@/api/common_api"
import {  commonDown } from "@/utils/btn";
export default {
  name: "prospecting_list",
  data() {
    return {
      formConfig: [
        {
          label: "集客专线名称",
          type: "input",
          fieldName: "lineName",
        },
        {
          label: "计费编码",
          type: "input",
          fieldName: "lineNumber",
        },
        {
          label: "处理流水号",
          type: "input",
          fieldName: "serialno",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
        {
          label: '地市',
          type: 'select',
          fieldName: 'city',
          itemAs:true,
          options: []
        },
        {
          label: '区县',
          type: 'select',
          fieldName: 'county',
          itemAs:true,
          options: []
        },
      ],
      tableHeader: [
        {
          prop: "city",
          label: "地市",
          align: "center",
          width:80,
          tooltip: true,
        },
        {
          prop: "name",
          label: "设计单名称",
          align: "center",
          width:300,
          tooltip: true,
        },
        {
          prop: "designname",
          label: "设计单位",
          align: "center",
          width: 180,
          tooltip: true,
        },
        {
          prop: "serialno",
          label: "处理流水号",
          align: "center",
          width:160,
          tooltip: true,
        },
        {
          prop: "lineName",
          label: "集客专线名称",
          align: "center",
          width: 300,
          tooltip: true,
        },
        {
          prop: "customerAddress",
          label: "集客详细地址",
          align: "center",
          width: 300,
          tooltip: true,
        },
        {
          prop: "lineNumber",
          label: "计费编码",
          align: "center",
          width: 180,
          tooltip: true,
        },
        {
          prop: "creatorName",
          label: "拟稿人",
          align: "center",
          tooltip: true,
          width: 120,
        },
        {
          prop: "createDate",
          label: "拟稿时间",
          align: "center",
          tooltip: true,
          width: 150,
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          tooltip: true,
          width: 100,
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          fixed:'right',
          width: 100,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{},
      loadParam: {},
    };
  },
  created(){
    this.getStatusList()
    this.getAreaList('-2', 4)
  },
  methods: {
    async getStatusList() {
      // 下标3是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.formConfig[3], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    getAreaList(parentId, index) {
        queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
            if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
                list.push({ label: item.name, value: item.id })
            })
            this.$set(this.formConfig[index], 'options', list)
            }
        })
    },
    changeSelect(name, val){
      if(name=='city'){
        this.getAreaList(val.value, 5)
        this.$set(this.$refs.searchForm.searchForm,'county','')
      }
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(JSON.stringify(param))
      obj.city=obj.city?obj.city.label:''
      obj.county=obj.county?obj.county.label:''
      this.staticSearchParam=obj
      this.loadParam = obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj);
      })
    },

    // 重置
    reset(param) {
      this.search(param);
    },
    // 导出
    download() {
      commonDown({ ...this.loadParam}, downloadService);
    },
    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/inbound_line/prospecting/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/inbound_line/prospecting/view",
            query: { boId: row.id },
          });
          break;
      }
    },
  },
};
</script>
