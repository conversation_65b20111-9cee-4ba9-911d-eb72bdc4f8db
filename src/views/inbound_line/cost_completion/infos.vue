<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="cost_completion_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="集客专线信息">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          labelPosition="left"
          :disableForm="true"
          :form="lineForm"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListTwo" class="page-anchor-point" v-if="showlineTwo"></div>
    <mssCard title="集客专线信息" v-if="showlineTwo">
      <div slot="content">
        <mssForm
          ref="lineTwoForm"
          labelWidth="200px"
          :config="lineConfig"
          labelPosition="left"
          :form="lineTwoForm"
          :disableForm="true"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :fileTypeFlag="true"
      :dealPage="dealPage"
    ></mssAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory v-if="businessType" :boId="boId" :workflowCode="businessType"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

  </div>
</template>

<script>
import {
  queryDetailService,
} from "@/api/inbound_line/cost_completion_api.js";
import Data from '../formSetting'
export default {
  name: "cost_completion_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      boId: "",
      businessType: "GroupCustomerComplete",
      nodeName: "草稿",
      dealPage:false,
      disableForm: false,
      basicConfig: [
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "施工任务单编码",
          type: "input",
          prop: "groupConsTaskOrderCode",
          span: 24,
        },
        {
          label: "施工任务单名称",
          type: "input",
          prop: "groupConsTaskOrderName",
          span: 24,
        },
        {
          label: "发起时间",
          type: "input",
          prop: "materialAmount",
          span: 12,
        },
        {
          label: "完工时间",
          type: "datePicker",
          prop: "completeDate",
          span: 12,
        },
        {
          label: "勘查光缆长度皮长公里",
          type: "input",
          prop: "fillLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'fillLength',this.dealNum(value))
            }
          }
        },
        {
          label: "实际光缆长度皮长公里",
          type: "input",
          mode:'number',
          prop: "realLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'realLength',this.dealNum(value))
            }
          }
        },
        {
          label: "光缆差异长度皮长公里",
          type: "input",
          prop: "diffLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'diffLength',this.dealNum(value))
            }
          }
        },
        {
          label: "差异比例",
          type: "input",
          prop: "diffRate",
          span: 12,
        },
        {
          label: "拟稿单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "拟稿部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig: JSON.parse(JSON.stringify( Data.lineSet)),
      lineForm: {},
      lineTwoForm: {},
      showlineTwo: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "A端<br>信息",
          id: "sectionList",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
      this.disableForm = true;
    }else{
      for (let i in this.basicConfig) {
        if(['0','1','2','3','4','6','8','9','10','11','12','13'].includes(i)){
          this.basicConfig[i].disabled = true;
        }
      }
      this.dealPage=true
    }
    for (let i in this.lineConfig) {
      this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
    }
    // 浏览器传参
    this.boId = this.$route.query.boId
    this.getDetailsData()
  },
  methods: {
    dealNum(value){
      let newval= ('' + value) // 第一步：转成字符串
      .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
      .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
      .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
      .match(/^\d*(\.?\d{0,3})/g)[0] || ''
      return  newval
    },
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.businessType=res.data.businessType
        this.$emit("getWorkCode",this.businessType)
        this.lineForm = {
          ...res.data.detail,
          outsideNum:res.data.outsideNum,
          insideNum:res.data.insideNum
        };
        if (res.data.detailOther) {
          this.lineTwoForm = {
            ...res.data.detailOther,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
          this.pageAnchorConfig.splice(2, 0, {
            text: "Z端<br>信息",
            id: "sectionListTwo",
          });
          this.showlineTwo = true;
        }
      });
    },

  },
};
</script>

<style lang="scss" scoped>
</style>
