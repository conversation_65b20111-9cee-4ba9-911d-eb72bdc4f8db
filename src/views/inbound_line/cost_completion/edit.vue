<!-- 成本类完工处理页面 -->
<template>
  <div class="cost_completion_detail page-anchor-parentpage">
    <div class="operate-btn">
      <el-button type="primary"  @click="submit()">提交</el-button>
      <el-button type="primary"  @click="save()">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
    ></mssWorkFlowHandel>
    <infos labelPosition="top" ref="infos" :formRules="formRules" @getWorkCode="getWorkCode"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService,beforeSubmitService} from "@/api/inbound_line/cost_completion_api.js"
export default {
  name: 'cost_completion_detail',
  components:{infos},
  data() {
    return {
      boId: '',
      workflowCode: '',
      completeTaskUrl:"",//流程处理地址
      returnAddress:'/inbound_line/cost_completion',//流程提交成功返回路由地址
      formRules:{}
    }
  },
  created() {
    this.boId = this.$route.query.boId
  },
  methods: {
    getWorkCode(param){
      this.workflowCode=param
      this.$nextTick(()=>{
        this.$refs.workFlow.init()
      })
    },
    // 返回上一页
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/cost_completion'
      })
    },

    // 保存
    save(cb){
      this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid)=>{
        if(valid){
          // 处理数据
          let param={
            ...this.$refs.infos.$refs.basicForm.modelForm,
            detail:this.$refs.infos.$refs.lineForm.modelForm
          }
          if(this.$refs.infos.showlineTwo){
            param.detailOther=this.$refs.infos.$refs.lineTwoForm.modelForm
          }
          saveService(param).then(res=>{
            if(res.code=='0000'){
              this.$nextTick(()=>{
                this.$refs.infos.$refs.basicForm.modelForm=res.data||{}
              })
              if(cb){
                cb(param)
              }else{
                this.$message.success('保存成功')
              }
            }
          })
        }else{
          return
        }
      })
    },

    // 提交--先保存后提交
    submit(){
      // this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid)=>{
      //   if(valid){
          this.completeTaskUrl = this.$refs.infos.basicForm.completeTaskUrl
          this.$refs.workFlow.opendialogInitNextPath()
      //   }else{
      //       return
      //     }
      // })

    },
     //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      let name = { code: 'name', value: this.$refs.infos.$refs.basicForm.modelForm.name }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit(){
      let str=""
      await this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid)=>{
        str += valid?'':'有必填信息未填写完整，请检查; '
      })
      let res=await beforeSubmitService({boId:this.boId})
      if(res.data && res.data.code!=0){
        str +=res.data.msg
      }
      return str
    },

    beforeNode() {
      const fileNameArr = {
        jkcostManger:{ userName: this.$refs.infos.basicForm.businessManagerName, userId: this.$refs.infos.basicForm.businessManagerId }
      }
      // 设置默认处理人
      return fileNameArr
    },
    // 必填校验
    initFormRules(param) {
      this.formRules=param
    },
    // 只读表单
    getReadonlyList(param) {
      if(param.length){
        this.$refs.infos.basicConfig.forEach(item=>{
          if(param.includes(item.prop)){
            item.disabled = true;
          }
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
