<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="invest_completion_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="headerBtn">
        <span :style="tipContent === '紧急' ? 'color: red;' : 'color: #00B050;' + 'font-weight: bold;'">注：当前流程为{{ tipContent }}流程</span>
      </div>
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListOne" class="page-anchor-point" v-if="showlineOne"></div>
    <mssCard title="集客专线信息" v-if="showlineOne">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :form="lineForm"
          :disableForm="disableForm1"
          :rules="formRules"
        ></mssForm>
<!--        <mssForm-->
<!--          ref="otherForm"-->
<!--          labelWidth="200px"-->
<!--          :config="otherConfig"-->
<!--          :labelPosition="labelPosition"-->
<!--          :form="otherForm"-->
<!--          :disableForm="disableForm"-->
<!--          :rules="formRules"-->
<!--        ></mssForm>-->
      </div>
    </mssCard>

    <div id="sectionListTwo" class="page-anchor-point" v-if="showlineTwo"></div>
    <mssCard title="集客专线信息" v-if="showlineTwo">
      <div slot="content">
        <mssForm
          ref="lineTwoForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :form="lineTwoForm"
          :disableForm="disableForm1"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="approveList" class="page-anchor-point" v-if="approveShow"></div>
    <mssCard title="审批记录" v-if="approveShow">
      <div slot="content">
        <mssTable
          ref="table"
          :columns="tableHeader"
          :api="tableApi"
          :staticSearchParam="staticSearchParam"
          :pagination="false"
          :serial="false"
        ></mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <ImgAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :fileTypeFlag="true"
      :dealPage="dealPage"
    ></ImgAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
          v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      :dialog-width="dialogWidth"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      @confirm="projectConfirm"
      :dialogTitle="dialogTitle"
      :tableSingleChoice="true"
      :rowKey="rowKey"
      :tableQueryParams="tableQueryParams"
    ></mssTableSearchDialog>
    <mssTableSearchDialog1
      ref="projectNameSearchDialog1"
      :dialog-width="dialogWidth"
      :autoCall="false"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      @confirm="projectConfirm"
      :dialogTitle="dialogTitle"
      :tableSingleChoice="true"
      :rowKey="rowKey"
      :tableQueryParams="tableQueryParams"
    ></mssTableSearchDialog1>
  </div>
</template>

<script>
import { queryDetailService } from "@/api/inbound_line/extend/invest_completion_api.js";
import {
  getProjectListService,
  getTaskListService,
} from "@/api/inbound_line/extend/sorting_tank_api.js";
import Data from "../formSetting";
import ImgAttachment from "../img_attachment/index.vue";
import {queryPmsHandlingSituationListService} from "@/api/inbound_line/extend/pms_handling_situation_api";
import {getDeviceType, queryZzDeviceResource} from "@/api/inbound_line/extend/zz_resource_api";
export default {
  name: "extend_invest_completion_info",
  components: {
    ImgAttachment
  },
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {};
      },
    },
    nodeCode: {
      type: String,
      default: () => "",
    },
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return '96%'
      }else{
        return '70%'
      }
    }
  },
  data() {
    return {
      boId: "",
      tipContent: "紧急",
      businessType: "",
      nodeName: "草稿",
      dealPage: false,
      disableForm: false,
      disableForm1: true,
      projectTableApi: getProjectListService,
      searchFieldList: [],
      projectColumns: [],
      dialogTitle: "",
      rowKey:"",
      tableSingleChoice: true,
      tableQueryParams:{},
      basicConfig: [
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
          readonly: true,
          eventListeners: {
            focus: this.openProjectSelectDialog, // 打开选择项目的弹窗
          },
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "建设单位",
          type: "input",
          prop: "city",
          span: 12,
        },
        {
          label: "任务编码",
          type: "input",
          prop: "taskCode",
          span: 12,
        },
        {
          label: "任务名称",
          type: "input",
          prop: "taskName",
          readonly: true,
          eventListeners: {
            focus: this.clickNative, // 打开选择项目的弹窗
          },
          span: 12,
        },
        {
          label: "完工时间",
          type: "datePicker",
          prop: "completeDate",
          span: 12,
        },
        {
          label: "勘查光缆长度皮长公里",
          type: "input",
          mode:'number',
          prop: "fillLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'fillLength',this.dealNum(value))
            }
          }
        },
        {
          label: "实际光缆长度皮长公里",
          type: "input",
          mode:'number',
          prop: "realLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'realLength',this.dealNum(value))
            }
          }
        },
        {
          label: "光缆差异长度皮长公里",
          type: "input",
          mode:'number',
          prop: "diffLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'diffLength',this.dealNum(value))
            }
          }
        },
        {
          label: "差异比例",
          type: "input",
          prop: "diffRate",
          span: 12,
        },

        {
          label: "设计批复站点名称",
          type: "input",
          prop: "designApprovalSite",
          span: 12,
        },
        {
          label: "设计批复文号",
          type: "input",
          prop: "designApprovalNo",
          span: 12,
        },
        {
          label: "施工费订单号",
          type: "input",
          prop: "costOrderNo",
          span: 12,
        },
        {
          label: "SCM系统领用申请单号",
          type: "input",
          prop: "scmApplyNumber",
          span: 12,
        },
        {
          label: "拟稿单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "拟稿部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig: JSON.parse(JSON.stringify(Data.lineSet)),
      lineForm: {},
      lineTwoForm: {},
      showlineOne: false,
      showlineTwo: false,
      approveShow: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
      otherConfig: [
        {
          label: "专线状态",
          type: "input",
          prop: "schedule",
          span: 12,
        },
        {
          label: "是否完成",
          type: "select",
          options: [
            { label: "是", value: true },
            { label: "否", value: false },
          ],
          prop: "ifdone",
          span: 12,
        },
        // {
        //   label: "专线地址",
        //   type: "input",
        //   prop: "customerAddress",
        //   span: 24,
        // },
        // {
        //   label: "IRMS工单名",
        //   type: "input",
        //   prop: "irmsName",
        //   span: 12,
        // },
        // {
        //   label: "IRMS工单号",
        //   type: "input",
        //   prop: "irmsWorkoder",
        //   span: 12,
        // },
        // {
        //   label: "未完成原因",
        //   type: "input",
        //   mode: "textarea",
        //   prop: "nodoneReason",
        //   span: 24,
        // },
        // {
        //   label: "驳回原因",
        //   type: "input",
        //   mode: "textarea",
        //   prop: "irmsReason",
        //   span: 24,
        // },
        // {
        //   label: "退回原因",
        //   type: "input",
        //   mode: "textarea",
        //   prop: "attribute1",
        //   span: 24,
        // },
        // {
        //   label: "退回时间",
        //   type: "input",
        //   prop: "attribute2",
        //   span: 12,
        // },
        {
          label: "是否免勘查开通",
          type: "select",
          options: [
            { label: "是", value: true },
            { label: "否", value: false },
          ],
          prop: "ifnodesign",
          span: 12,
        },
        {
          label: "发单时间",
          type: "input",
          prop: "sendDate",
          span: 12,
        },
        // {
        //   label: "实际完工时间",
        //   type: "datePicker",
        //   prop: "completeDate",
        //   span: 12,
        // },
      ],
      otherForm: {},
      city: '',
      county: '',
      realLength: null,
      tableHeader: [
        {
          prop: "dataType",
          label: "流程名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "currentNodeName",
          label: "审批环节",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "optionText",
          label: "审批意见",
          align: "center",
          minWidth: 200,
          tooltip:true
        },
        {
          prop: "approveUserName",
          label: "审批人",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "preferredMobile",
          label: "审批人手机号",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "secondOrgName",
          label: "审批部门",
          align: "center",
          minWidth: 200,
          tooltip:true
        },
        {
          prop: "receiveDate",
          label: "环节到达时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "updateDate",
          label: "环节处理时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
      ],
      tableApi: queryPmsHandlingSituationListService,
      staticSearchParam:{},
      deviceTypeList: [],
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
      for (let i in this.lineConfig) {
        this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
      }
      for (let i in this.otherConfig) {
        this.otherConfig[i].label = `${this.otherConfig[i].label}：`;
      }
      this.disableForm = true;
    } else {
      for (let i in this.basicConfig) {
        if (['1','2','3','6','8','9','10','11','12','13','14','15','16','17'].includes(i)) {
          this.basicConfig[i].disabled = true;
        }
      }
      for (let i in this.otherConfig) {
        if (i < 11) {
          this.otherConfig[i].disabled = true;
        }
      }
      this.dealPage = true;
    }
    // 浏览器传参
    this.boId = this.$route.query.boId;
    this.$set(this.lineConfig[23], 'options', await this.$dictOptions({parentValue: "AccessModel",appCode: "001001"}))
    this.$set(this.lineConfig[24], 'options', await this.$dictOptions({parentValue: "Satisfaction",appCode: "001001"}))
    this.getDetailsData();
  },
  // activated() {
  //   this.boId = this.$route.query.boId;
  //   this.getDetailsData();
  // },
  watch: {
    nodeCode: function (val) {
      this.nodeCodeChange()
    },
  },
  methods: {
    dealNum(value){
      let newval= ('' + value) // 第一步：转成字符串
        .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
        .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
        .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
        .match(/^\d*(\.?\d{0,3})/g)[0] || ''
      return  newval
    },
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.realLength = this.basicForm.realLength;
        this.businessType = res.data.businessType;
        if ("ExtendGroupCustomerInvestComplete1" === this.businessType) {
          this.tipContent = "非紧急";
          this.disableForm1 = false;
          for (let i in this.lineConfig) {
            this.lineConfig[i].disabled = true;
            if(this.lineConfig[i].label=='专线接入模型'){
              this.lineConfig[i].prop="accessModelId"
              this.lineConfig[i].disabled = false;
            }
            if(this.lineConfig[i].label=='满足程度'){
              this.lineConfig[i].prop="satisfactionId"
              this.lineConfig[i].disabled = false;
            }
          }
        }
        if (this.basicForm.taskName == "利旧站点无需施工") {
          for(let i in this.basicConfig) {
            if(this.basicConfig[i].prop == "realLength"){
              this.basicConfig[i].disabled = true;
            }
          }
        }
        this.$emit("getWorkCode", this.businessType);
        if (res.data.detailOther) {
          this.city = res.data.detailOther.city;
          this.county = res.data.detailOther.county;
          this.lineTwoForm = {
            ...res.data.detailOther,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
          this.pageAnchorConfig.splice(1, 0, {
            text: "Z端<br>信息",
            id: "sectionListTwo",
          });
          this.otherForm = {
            ...res.data,
            ...res.data.detailOther,
          };
          this.showlineTwo = true;
        }
        if (res.data.detail) {
          this.city = res.data.detail.city;
          this.county = res.data.detail.county;
          this.lineForm = {
            ...res.data.detail,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
          this.otherForm = {
            ...res.data,
            ...res.data.detail,
          };
          this.pageAnchorConfig.splice(1, 0, {
            text: "A端<br>信息",
            id: "sectionListOne",
          });
          this.showlineOne = true;
        }
        this.nodeCodeChange()
      });
    },
    nodeCodeChange() {
      if ("ExtendGroupCustomerInvestComplete1" === this.businessType) {
        if (this.nodeCode == "managerFile") {
          if (this.lineForm.lineNumber) {
            this.staticSearchParam.lineNumber = this.lineForm.lineNumber;
            this.approveShow = true;
          } else if (this.lineTwoForm.lineNumber) {
            this.staticSearchParam.lineNumber = this.lineTwoForm.lineNumber;
            this.approveShow = true;
          }
          if (this.approveShow) {
            this.pageAnchorConfig.splice(this.pageAnchorConfig.length - 2, 0, {
              text: "审核<br>记录",
              id: "approveList",
            });
          }
        }
      }
      if (this.nodeCode == "consUnit") {
        if (this.disableForm1) {
          this.disableForm1 = false;
          for (let i in this.lineConfig) {
            this.lineConfig[i].disabled = true;
          }
        }
        this.lineConfig[39].disabled = false;
        this.lineConfig[40].disabled = false;
        this.lineConfig[51].disabled = false;
        this.lineConfig[39].eventListeners = {
          focus: () => this.openZzDeviceResource('zzDeviceResource'), // 打开选择项目的弹窗
        };
        this.lineConfig[40].eventListeners = {
          focus: () => this.openZzDeviceResource('zzDeviceResource1'), // 打开选择项目的弹窗
        };
        this.getDeviceTypeList();
      }
    },
    getDeviceTypeList() {
      getDeviceType().then((res) => {
        this.deviceTypeList = res.data;
      });
    },
    clickNative(param) {
      if(this.$refs.basicForm.modelForm.projectCode){
        this.openTaskDialog()
      }else{
        this.$message.warning('请先选择项目')
      }
    },
    openZzDeviceResource(type) {
      this.projectTableApi = queryZzDeviceResource;
      this.searchFieldList = [
        {
          label: "设备类型",
          type: "select",
          fieldName: "deviceType",
          options: this.deviceTypeList,
          rules: [
            {
              required: true,
              message: "请选择设备类型",
              trigger: "blur",
            },
          ],
        },
        {
          label: "设备名称",
          type: "input",
          fieldName: "deviceName",
        },
      ];
      this.projectColumns = [
        {
          label: "地市",
          prop: "city",
          minWidth: 100,
        },
        {
          label: "区县",
          prop: "county",
          minWidth: 100,
        },
        {
          label: "设备名称",
          prop: "deviceName",
          minWidth: 150,
        },
        {
          label: "设备ID",
          prop: "deviceId",
          minWidth: 100,
        },
        {
          label: "设备UUID",
          prop: "deviceUuid",
          minWidth: 100,
        },
        {
          label: "设备型号",
          prop: "deviceModel",
          minWidth: 100,
        },
        {
          label: "所属接入点名称",
          prop: "accessName",
          minWidth: 150,
        },
        {
          label: "所属接入点类型",
          prop: "accessType",
          formatter: function (row) {
            let val;
            if (row.accessType==1) {
              val = "分纤箱"
            } else if (row.accessType==2) {
              val = "光交箱"
            } else {
              val = row.accessType
            }
            return val
          },
          minWidth: 150,
        },
        {
          label: "所属站点名称",
          prop: "siteName",
          minWidth: 150,
        },
        {
          label: "所属机房名称",
          prop: "roomName",
          minWidth: 150,
        },
        {
          label: "所属机房ID",
          prop: "roomId",
          minWidth: 100,
        },
      ];
      this.tableQueryParams={
        city:this.city,
        county:this.county
      }
      this.dialogTitle = "选择设备";
      this.rowKey="projectId"
      this.tableSingleChoice = true;
      this.$refs.projectNameSearchDialog1.openDialog();
      this.type = type;
    },
    openProjectSelectDialog() {
      this.projectTableApi = getProjectListService;
      this.searchFieldList = [
        {
          label: "项目名称",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          fieldName: "projectCode",
        },
      ];
      this.projectColumns = [
        {
          label: "项目名称",
          prop: "projectName",
          minWidth: 200,
        },
        {
          label: "项目编码",
          prop: "projectCode",
          minWidth: 100,
        },
        {
          label: "建设单位名称",
          prop: "orgName",
          minWidth: 200,
        },
      ];
      this.tableQueryParams={
          orgName:this.city
        }
      this.dialogTitle = "选择项目";
      this.rowKey="projectId"
      this.tableSingleChoice = true;
      this.$refs.projectNameSearchDialog.openDialog();
      this.type = '';
    },
    openTaskDialog() {
      this.projectTableApi = getTaskListService;
      this.searchFieldList = [
        {
          label: "任务名称",
          fieldName: "taskName",
        },
        {
          label: "任务编码",
          fieldName: "taskCode",
        },
        {
          label: "地市",
          fieldName: "city",
        },
        {
          label: "区县",
          fieldName: "county",
        },
      ];
      this.projectColumns = [
        {
          label: "任务名称",
          prop: "taskName",
          minWidth: 200,
        },
        {
          label: "任务编码",
          prop: "taskCode",
          minWidth: 100,
        },
        {
          label: "地市公司",
          prop: "city",
          minWidth: 200,
        },
        {
          label: "区县公司",
          prop: "county",
          minWidth: 100,
        },
      ];
      this.dialogTitle = "选择任务";
      this.tableSingleChoice = true;
      this.rowKey="taskId"
      this.tableQueryParams={
          projectCode:this.$refs.basicForm.modelForm.projectCode,
          city:this.city,
          type: '1',
        }
      this.$refs.projectNameSearchDialog.openDialog();
      this.type ='task';
    },
    projectConfirm(data) {
      if (data && data[0]){
        if (this.type == "zzDeviceResource") {
          if (this.showlineOne) {
            this.$refs.lineForm.modelForm = {
              ...this.$refs.lineForm.modelForm,
              accessDeviceName: data[0].deviceName,
              accessDeviceId: data[0].deviceId,
              ismodifyresource: 1,
            };
          } else if (this.showlineTwo) {
            this.$refs.lineTwoForm.modelForm = {
              ...this.$refs.lineTwoForm.modelForm,
              accessDeviceName: data[0].deviceName,
              accessDeviceId: data[0].deviceId,
              ismodifyresource: 1,
            };
          }
        } else if (this.type == "zzDeviceResource1") {
          if (this.showlineOne) {
            this.$refs.lineForm.modelForm = {
              ...this.$refs.lineForm.modelForm,
              accessDeviceName1: data[0].deviceName,
              accessDeviceId1: data[0].deviceId,
              ismodifyresource: 1,
            };
          } else if (this.showlineTwo) {
            this.$refs.lineTwoForm.modelForm = {
              ...this.$refs.lineTwoForm.modelForm,
              accessDeviceName1:data[0].deviceName,
              accessDeviceId1: data[0].deviceId,
              ismodifyresource: 1,
            }
          }
        } else if(this.type =='task'){
          this.$refs.basicForm.modelForm = {
            ...this.$refs.basicForm.modelForm,
            taskName:data[0].taskName,
            taskCode:data[0].taskCode,
            taskId:data[0].taskId,
          };
          if (data[0].taskCode === " ") {
            this.$refs.basicForm.modelForm.realLength = 0;
          } else {
            this.$refs.basicForm.modelForm.realLength = this.realLength;
          }
        }else {
          this.$refs.basicForm.modelForm = {
            ...this.$refs.basicForm.modelForm,
            ...data[0],
            city: data[0].orgName || this.city,
          };
        }
      }

    },
  },
};
</script>

<style lang="scss" scoped>
</style>
