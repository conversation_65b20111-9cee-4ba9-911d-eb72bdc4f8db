<!-- 小区入网列表 -->
<template>
  <div class="apply_order_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="小区入网工单列表">
      <div slot="headerBtn">
        <el-button type="primary" @click="add">新增</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :staticSearchParam="staticSearchParam"
          :columns="tableHeader"
          :api="tableApi"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService ,delService} from "@/api/inbound_line/sorting_tank_api.js";
import { commonOneDel } from "@/utils/btn";
export default {
  name: "apply_order_list",
  data() {
    return {
      formConfig: [
        {
          label: "工单标题",
          type: "input",
          fieldName: "orderTitle",
        },
        {
          label: "PMS小区",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
      ],
      tableHeader: [
        {
          prop: "city",
          label: "地市",
          align: "center",
          width:"100px",
          tooltip:true
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
          width:"100px",
          tooltip:true
        },
        {
          prop: "orderTitle",
          label: "工单标题",
          align: "center",
          width:"360px",
          tooltip:true
        },
        {
          prop: "pms",
          label: "PMS小区",
          align: "center",
          width:"130px",
          tooltip:true
        },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          width:"130px",
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          width:"130px",
          tooltip:true
        },
        {
          prop: "createBy",
          label: "派单人",
          align: "center",
          width:"100px",
          tooltip:true
        },
        {
          prop: "createTime",
          label: "派单时间",
          align: "center",
          width:"100px",
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          width: "100px",
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          fixed:'right',
          width: "120px",
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    提交
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
                {row.isAllowDelete ? (
                  <span
                    class="table_btn "
                    onClick={() => {this.action(row, "del")}}
                  >
                    删除
                  </span>
                ) : (
                  ""
                )}
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{}
    };
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标5是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.formConfig[5], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(param)
      )
      this.$refs.table.getTableData(param);
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 新增
    add(){
      this.$router.push({
        path: "/inbound_line/community_network/order_list/add",
        query: { type:'add' },
      });
    },
     // 提交
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/inbound_line/sorting_tank/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/inbound_line/sorting_tank/view",
            query: { boId: row.id },
          });
          break;
        case "del":
          commonOneDel.call(this,row.id, delService, (res) => {
            if(res.code=='0000'){
              this.$message.success('删除成功')
              this.search()
            }
          })
          break;
      }
    },
  },
};
</script>
