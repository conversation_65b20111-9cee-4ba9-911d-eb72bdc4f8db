<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="apply_order_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
        ></mssForm>
      </div>
    </mssCard>
  </div>
</template>
<script>
import {

} from "@/api/inbound_line/sorting_tank_api.js";
import { commonDown,commonMultDel } from "@/utils/btn";
export default {
  name: "apply_order_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return '96%'
      }else{
        return '70%'
      }
    }
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      disableForm: false,
      dealPage:false,
      searchFieldList: [],
      projectColumns: [],
      dialogTitle:'',
      rowKey:"",
      tableSingleChoice:false,
      basicConfig: [
        {
          label: "地市",
          type: "input",
          prop: "city",
          span: 12,
          eventListeners: {
          //  focus: this.openProjectSelectDialog, // 打开选择地市的弹窗
          },
          rules:{required: true, message: '请选择地市', trigger: 'change'}
        },
        {
          label: "区县",
          type: "input",
          prop: "county",
          span: 12,
        },
        {
          label: "建设单位",
          type: "input",
          prop: "city",
          span: 12,
        },
        {
          label: "是否开通",
          type: "select",
          options: [
            { label: "是", value: "1" },
            { label: "否", value: "0" },
          ],
          prop: "isOpen",
          span: 12,
        },
        {
          label: "拟稿单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "拟稿部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      tableHeader: [
        {
          prop: "linenumber",
          label: "计费编码",
          align: "center",
          width: 130,
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
        },
        {
          prop: "city",
          label: "地市",
          align: "center",
        },
        {
          prop: "linename",
          label: "专线名称",
          align: "center",
        },
        {
          prop: "transmissioinLineName",
          label: "传输专业（业务端所属）",
          align: "center",
        },
        {
          prop: "customerAddress",
          label: "集客详细地址（精确到街道、号）",
          align: "center",
          width: 240,
          tooltip: true,
        },
        {
          prop: "managerTel",
          label: "客户经理联系电话",
          align: "center",
        },
        {
          prop: "customerSerLevel",
          label: "客户服务等级",
          align: "center",
        },
      ],
      tableTaskHeader: [
        {
          prop: "linenumber",
          label: "计费编码",
          align: "center",
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
        },
        {
          prop: "city",
          label: "地市",
          align: "center",
        },
        {
          prop: "linename",
          label: "专线名称",
          align: "center",
        },
        {
          prop: "taskCode",
          label: "任务编码",
          align: "center",
        },
        {
          prop: "taskName",
          label: "任务名称",
          align: "center",
        },
        {
          prop: "managerName",
          label: "任务经理UID",
          align: "center",
        },
      ],
      stationary: [],
      pageAnchorConfig: []
    };
  },
  watch:{
    boId(n,o){
      if(n){
        this.pageAnchorConfig= [
          {
            text: "基本<br>信息",
            id: "sectionBasic",
          },
          {
            text: "专线<br>信息",
            id: "sectionList",
          },
          {
            text: "任务<br>信息",
            id: "sectionListTwo",
          },
          {
            text: "附件<br>列表",
            id: "sectionAttachment",
          },
          {
            text: "流程<br>记录",
            id: "sectionOperation",
          },
        ]
      }else{
        this.pageAnchorConfig= [
          {
            text: "基本<br>信息",
            id: "sectionBasic",
          }
        ]
      }
    }
  },
  created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
      this.basicConfig[0].rules={}
      this.disableForm = true;
    } else {
      this.basicConfig[0].readonly = true;
      for (let i in this.basicConfig) {
        if (['1','2','4','5','6','7'].includes(i)) {
          this.basicConfig[i].disabled = true;
        }
      }
      this.dealPage=true
    }
    // 浏览器传参
    this.boId = this.$route.query.boId;
    if (this.boId) this.getDetailsData();
  },
  methods: {
    // 查询明细
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.stationary = res.data.groupCustomerArchiveDetailDtoList || [];
        this.businessType=res.data.businessType
        this.$emit("getWorkCode",this.businessType)
      });
    },

    // 弹框选项目|选勘查单
    openProjectSelectDialog(param) {
       // 选勘察单
      if(param=='order'){

        this.projectTableApi=getOrderListService
        this.searchFieldList= [
          {
            label: "专线名称",
            fieldName: "lineName",
          },
          {
            label: "计费编码",
            fieldName: "lineNumber",
          },
        ]
        this.projectColumns= [
          {
            label: "专线名称",
            prop: "linename",
            minWidth:300
          },
          {
            label: "计费编码",
            prop: "linenumber",
          },
          {
            label: "地市",
            prop: "city",
          },
          {
            label: "区县",
            prop: "county",
          },
          {
            label: "传输专线（业务端所属）",
            prop: "transmissioinLineName",
            minWidth:130
          },
        ]
        this.dialogTitle="选择勘查单"
        this.rowKey='groupCustomerLineDetailId'
        this.tableSingleChoice=false
      }else{ // 选项目
        this.projectTableApi=getProjectListService
        this.searchFieldList= [
          {
            label: "项目名称",
            fieldName: "projectName",
          },
          {
            label: "项目编码",
            fieldName: "projectCode",
          },
          {
            label: "建设单位名称",
            fieldName: "orgName",
          },
        ]
        this.projectColumns= [
          {
            label: "项目名称",
            prop: "projectName",
            minWidth: 200,
          },
          {
            label: "项目编码",
            prop: "projectCode",
            minWidth: 100,
          },
          {
            label: "建设单位名称",
            prop: "orgName",
            minWidth: 200,
          },
        ]
        this.dialogTitle="选择项目"
        this.rowKey='projectId'
        this.tableSingleChoice=true
      }
      this.$refs.projectNameSearchDialog.openDialog();
      this.type=param=='order'?'order':''
    },
    projectConfirm(data) {
        if(this.type){
          if(data.length>0){
            let ids=data.map(item=>{return item.groupCustomerLineDetailId}).join(',')
            bindOrderListService({
              groupCustomerArchiveId:this.boId,
              ids:ids
            }).then(res=>{
              if(res.code=='0000'){
                this.$message.success('勘察单添加成功')
                this.getDetailsData()
              }
            })
          }
        }else{ // 选项目
          if(data && data[0])
          this.$refs.basicForm.modelForm = {
            ...this.$refs.basicForm.modelForm,
            ...data[0],
            city:data[0].orgName
          }
        }
    },

    // 删除
    del() {
      commonMultDel.call(this,
      {data:this.$refs.table.multipleSelection, delApi:deldetailService, sucCb:(res) => {//成功后的一些操作}}）
        this.$message.success("删除成功")
        this.$refs.table.$refs.table.clearSelection()
        this.getDetailsData();
      }})
    },
  },
};
</script>

<style lang="scss" scoped>
.apply_order_add .upload-btn {
  display: inline-block;
  margin: 0 10px;
}
</style>
