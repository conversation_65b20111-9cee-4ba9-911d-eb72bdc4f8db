<!--紧急建设需求区县表单 -->
<template>
  <div class="urgent_county_user_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      labelWidth="150px"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="紧急建设需求区县列表">
<!--      <div slot="headerBtn">-->
<!--        <el-button type="primary" @click="download">导出</el-button>-->
<!--      </div>-->
      <div slot="content">
        <mssTable
          ref="table"
          :columns="tableHeader"
          :api="tableApi"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
    <mssChooseUser ref="chooseUser" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseUser>
  </div>
</template>

<script>
import { queryListService,updateUrgentCountryUser} from "@/api/inbound_line/extend/urgent_county_user_api.js";
import { commonDown } from "@/utils/btn";
export default {
  name: "extend_urgent_county_user_list",
  checkList: [],
  roleRow:{},
  data() {
    return {
      multSelect:false,
      formConfig: [
        {
          label: "地市",
          type: "input",
          fieldName: "city",
        },
        {
          label: "区县",
          type: "input",
          fieldName: "county",
        },
        {
          label: "姓名",
          type: "input",
          fieldName: "realName",
        },
        {
          label: "手机",
          type: "input",
          fieldName: "mobile",
        },
        {
          label: "emis账号",
          type: "input",
          fieldName: "emisAccount",
        }
      ],
      tableHeader: [
        {
          prop: "city",
          label: "地市",
          align: "center",
          width:225,
          tooltip:true
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
          width:225,
          tooltip:true
        },
        {
          prop: "realName",
          label: "姓名",
          align: "center",
          width:225,
          tooltip:true
        },
        {
          prop: "mobile",
          label: "手机",
          align: "center",
          width:225,
          tooltip:true
        },
        {
          prop: "emisAccount",
          label: "emis账号",
          align: "center",
          width:250,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          width: 150,
          fixed:'right',
          formatter: (row) => {
            return (
              <span
                class="table_btn"
                onClick={() => {
                  this.openChooseUserDailog(row);
                }}
              >
                  修改人员
                </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{},
      loadParam:{}
    };
  },
  methods: {
    search(param) {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(param)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(param)
      );
      this.$refs.table.getTableData(param);
    },
    openChooseUserDailog(row) {
      console.log(sessionStorage)
      this.roleRow = row
      const item = {
        excuterNames: row.roleName,
        excuterIds: row.userIds
      }
      const deptParams = { deptIds: sessionStorage.getItem('firstDeptId') }
      const personParams = { firstDeptId: sessionStorage.getItem('firstDeptId') }
      this.$refs.chooseUser.init(item, deptParams, personParams)
    },

    showCheckList({ checkList }) {
     console.log(checkList)
      let param = {
        "id": this.roleRow.id,
        "city":this.roleRow.city,
        "county":this.roleRow.county,
        "realName":checkList[0].realName,
        "mobile":checkList[0].mobile,
        "emisAccount":checkList[0].account,
      }

      updateUrgentCountryUser(param).then(res => {
        if (res.code == '0000') {
            this.$message.success('保存成功')
            //这里调用查询按钮方法
          this.search(this.staticSearchParam);
          }else {
            this.$message.success('保存失败')
        }
      })
    },

    // 重置
    reset(param) {
      this.search(param);

    },
    // 处理
    // action(row, type) {
    //   switch (type) {
    //     case "edit":
    //       this.$router.push({
    //         path: "inbound_line/extend/urgent_country/user/view",
    //         // query: { boId: row.id },
    //       });
    //       break;
    //     case "view":
    //       this.$router.push({
    //         path: "/inbound_line/extend/invest_construction_assignment/view",
    //         query: { boId: row.id },
    //       });
    //       break;
    //   }
    // },
  },
};
</script>
