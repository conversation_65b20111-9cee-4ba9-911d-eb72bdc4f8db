<!--施工分派  -->
<template>
  <div class="one_yard_end_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      labelWidth="150px"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="一码到底分派列表">
      <div slot="headerBtn">
        <el-button type="primary" @click="download">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="tableHeader"
          :api="tableApi"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService,downloadService} from "@/api/inbound_line/extend/one_yard_end_api.js";
import { commonDown } from "@/utils/btn";
export default {
  name: "extend_one_yard_end_list",
  data() {
    return {
      formConfig: [
        {
          label: "综资SN码",
          type: "input",
          fieldName: "zzSn",
        },
        {
          label: "一码到底SN码",
          type: "input",
          fieldName: "ymddSn",
        },
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
      ],
      tableHeader: [
        {
          prop: "zzSn",
          label: "综资SN码",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "ymddSn",
          label: "一码到底SN码",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "errorType",
          label: "错误类型",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "specName",
          label: "所属专业",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "resourceType",
          label: "资源类型",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "resourceName",
          label: "资源名称",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "resourceLogotype",
          label: "资源标识",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "deviceModel",
          label: "设备型号",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "installationLocation",
          label: "安装位置类型",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "locationPoints",
          label: "位置点",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "lifecycleStatus",
          label: "生命周期状态",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "equipmentManufacturers",
          label: "设备厂家",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "accessTime",
          label: "入网时间",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "city",
          label: "地市",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "creatorName",
          label: "拟稿人",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "拟稿时间",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "finishDate",
          label: "完成时间",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          width:120,
          fixed:'right',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{},
      loadParam:{}
    };
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标11是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.formConfig[4], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(param)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(param)
      );
      this.$refs.table.getTableData(param);
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 导出
    download() {
      commonDown({ ...this.loadParam }, downloadService);
    },

    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/inbound_line/extend/one_yard_end/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/inbound_line/extend/one_yard_end/view",
            query: { boId: row.id },
          });
          break;
      }
    },
  },
};
</script>
