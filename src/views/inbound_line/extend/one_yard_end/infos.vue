<!-- 施工分派公共信息（处理与查看公用） -->
<template>
  <div class="one_yard_end_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>


    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
          v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      dialog-width="70%"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      :dialogTitle="dialogTitle"
      :tableSingleChoice="true"
      :rowKey="rowKey"
      :tableQueryParams="tableQueryParams"
    ></mssTableSearchDialog>
  </div>
</template>

<script>
import {
  queryDetailService,
  getProviderListService,
  getContracListService,
} from "@/api/inbound_line/extend/one_yard_end_api.js";
import Data from "../formSetting";

export default {
  name: "extend_one_yard_end_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      disableForm: false,
      dealPage: false,
      searchFieldList: [],
      projectColumns: [],
      dialogTitle: "",
      rowKey: '',
      tableQueryParams: {},
      basicConfig: [
        {
          label: "综资SN码",
          type: "input",
          prop: "zzSn",
          span: 12,
        },
        {
          label: "一码到底SN码",
          type: "input",
          prop: "ymddSn",
          span: 12,
        },
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "错误类型",
          type: "input",
          mode: "textarea",
          prop: "errorType",
          span: 24,
        },
        {
          label: "反馈结果",
          type: "radio",
          prop: "feedbackResult",
          span: 12,
          options: [
            { label: '成功', value: '成功' },
            { label: '失败', value: '失败' }
          ]
        },
        {
          label: "失败信息",
          type: "input",
          prop: "failMassage",
          span: 12,
        },
        {
          label: "所属专业",
          type: "input",
          prop: "specName",
          span: 12,
        },
        {
          label: "资源类型",
          type: "input",
          prop: "resourceType",
          span: 12,
        },
        {
          label: "资源名称",
          type: "input",
          prop: "resourceName",
          span: 12,
        },
        {
          label: "资源标识",
          type: "input",
          prop: "resourceLogotype",
          span: 12,
        },
        {
          label: "设备型号",
          type: "input",
          prop: "deviceModel",
          span: 12,
        },
        {
          label: "安装位置类型",
          type: "input",
          prop: "installationLocation",
          span: 12,
        },
        {
          label: "位置点",
          type: "input",
          prop: "locationPoints",
          span: 12,
        },
        {
          label: "生命周期状态",
          type: "input",
          prop: "lifecycleStatus",
          span: 12,
        },
        {
          label: "设备厂家",
          type: "input",
          prop: "equipmentManufacturers",
          span: 12,
        },
        {
          label: "入网时间",
          type: "input",
          prop: "accessTime",
          span: 12,
        },
        {
          label: "地市",
          type: "input",
          prop: "city",
          span: 12,
        },
        {
          label: "区县",
          type: "input",
          prop: "county",
          span: 12,
        },
      ],
      basicForm: {},
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
        if (this.basicConfig[i].prop == 'feedbackResult') {
          this.basicConfig[i].type = 'input'
        }
      }
      this.disableForm = true;
    } else {
      for (let i in this.basicConfig) {
        if (["0", "1", "2", "3", "4", "7", "8", "9","10", "11", "12", "13", "14", "15", "16", "17","18"].includes(i)) {
          this.basicConfig[i].disabled = true;
        }
      }
      this.dealPage = true
    }
    // 浏览器传参
    this.boId = this.$route.query.boId;
    this.getDetailsData();
  },
  methods: {
    getDetailsData() {
      queryDetailService({boId: this.boId}).then((res) => {
        this.basicForm = res.data;
        this.businessType = res.data.businessType
        this.$emit("getWorkCode", this.businessType)
      });
    },

  },
};
</script>

<style lang="scss" scoped>
</style>
