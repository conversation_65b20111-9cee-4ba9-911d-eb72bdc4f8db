<!--施工分派  -->
<template>
  <div class="work_order_detail_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      :form="form"
      labelWidth="150px"
      @changeSelect="changeSelect"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="工单建设明细列表">
      <div slot="headerBtn">
        <el-button type="primary" @click="download">导出</el-button>
      </div>
      <div slot="content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="成本类" name="default"></el-tab-pane>
          <el-tab-pane label="投资类(紧急)" name="investUrgent"></el-tab-pane>
          <el-tab-pane label="投资类(非紧急)" name="invest"></el-tab-pane>
        </el-tabs>
        <mssTable
          ref="table"
          :columns="tableHeader"
          :api="tableApi"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  queryListService,
  queryListServiceInvest,
  queryListServiceInvestUrgent,
  downloadService
} from "@/api/inbound_line/extend/work_order_detail_api.js";
import { commonDown } from "@/utils/btn";
import {queryAreaListService} from "@/api/common_api";
export default {
  name: "extend_work_order_detail_list",
  props: {
  },
  data() {
    return {
      activeName: this.$route.query.activeName || 'default',
      formConfig: [
        {
          label: "专线名称",
          type: "input",
          fieldName: "irmsName",
        },
        {
          label: "专线类型",
          type: "input",
          fieldName: "lineName",
        },
        {
          label: "专线编号",
          type: "input",
          fieldName: "lineNumber",
        },
        {
          label: "编排单号",
          type: "input",
          fieldName: "irmsWorkoder",
        },
        {
          label: "前端单号",
          type: "input",
          fieldName: "attribute6",
        },
        {
          label: '地市',
          type: 'select',
          fieldName: 'city',
          itemAs:true,
          options: []
        },
        {
          label: '区县',
          type: 'select',
          fieldName: 'county',
          itemAs:true,
          options: []
        },
        {
          label: '流程状态',
          type: 'select',
          fieldName: 'entityStatus',
          itemAs:true,
          options: [
            { label: '已完成', value: '已完成' },
            { label: '流程中', value: '流程中' },
            { label: '撤销', value: '撤销' },
          ]
        },
        {
          label: '是否超时',
          type: 'select',
          fieldName: 'isTimeout',
          itemAs:true,
          options: [
            { label: '是', value: '是' },
          ]
        },
        {
          label: '统计时间',
          type: 'date2',
          fieldName: 'startDate',
          fieldName2: 'endDate'
        },
        {
          label: '是否项目类',
          type: 'select',
          fieldName: 'isProjectline',
          itemAs:true,
          options: [
            { label: '是', value: '是' },
            { label: '否', value: '否' },
          ]
        }
      ],
      form: {
        startDate: this.$route.query.startDate || '',
        endDate: this.$route.query.endDate || '',
        city: { label: this.$route.query.city || '', value: this.$route.query.city || '' },
        entityStatus: { label: this.$route.query.entityStatus || '', value: this.$route.query.entityStatus || '' },
        isTimeout: { label: this.$route.query.isTimeout || '', value: this.$route.query.isTimeout || '' },
        isProjectline: { label: this.$route.query.isProjectline || '', value: this.$route.query.isProjectline || '' },
        isCount: this.$route.query.isCount || '',
        isEnd: this.$route.query.isEnd || '',
        isEndTimeout: this.$route.query.isEndTimeout || '',
        isComplete: this.$route.query.isComplete || '',
        isCompleteTimeout: this.$route.query.isCompleteTimeout || '',
      },
      tableHeader: [],
      shareTableHeader: [
        {
          prop: "city",
          label: "地市",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "irmsName",
          label: "专线名称",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "lineName",
          label: "专线类型",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "buildType",
          label: "投资成本归属",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "lineNumber",
          label: "专线编号",
          align: "center",
          width: 150,
          tooltip:true
        },
        {
          prop: "irmsWorkoder",
          label: "编排单号",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "attribute6",
          label: "前端单号",
          align: "center",
          width: 150,
          tooltip:true
        },
        {
          prop: "isAgency",
          label: "是否紧急",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "beLongAreaType",
          label: "所属区域类型",
          align: "center",
          width: 150,
          tooltip:true
        },
        {
          prop: "isProjectline",
          label: "是否项目类",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "entityStatus",
          label: "工建流程状态",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "totalfare",
          label: "总费用",
          align: "center",
          width: 150,
          tooltip:true
        },
        {
          prop: "opticalCircuitLength",
          label: "光缆施工总距离",
          align: "center",
          width: 150,
          tooltip:true
        },
        {
          prop: "realLength",
          label: "实际光缆长度",
          align: "center",
          width: 150,
          tooltip:true
        },
        {
          prop: "modifyDate",
          label: "二编系统推送至工建辅助系统时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "startTime",
          label: "esop系统发起协议时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "sendTime",
          label: "esop系统推送二编系统时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "rejectTime",
          label: "二编驳回时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "rejectReason",
          label: "二编驳回原因",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "endTime",
          label: "二编归档调用时间",
          align: "center",
          width: 200,
          tooltip:true
        },
      ],
      defaultTableHeader: [
        {
          prop: "beginTime1",
          label: "成本类集客专线施工分派[项目经理]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime1",
        //   label: "成本类集客专线施工分派[项目经理]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName1",
          label: "成本类集客专线施工分派[项目经理]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime2",
          label: "成本类集客专线施工分派[施工单位接收确认]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime2",
        //   label: "成本类集客专线施工分派[施工单位接收确认]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName2",
          label: "成本类集客专线施工分派[施工单位接收确认]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime3",
          label: "成本类集客专线完工[施工单位发起完工]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime3",
        //   label: "成本类集客专线完工[施工单位发起完工]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName3",
          label: "成本类集客专线完工[施工单位发起完工]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime4",
          label: "成本类集客专线完工[项目经理审批]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime4",
        //   label: "成本类集客专线完工[项目经理审批]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName4",
          label: "成本类集客专线完工[项目经理审批]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime5",
          label: "成本类集客专线完工[等待编排系统审批]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime5",
        //   label: "成本类集客专线完工[等待编排系统审批]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName5",
          label: "成本类集客专线完工[等待编排系统审批]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
      ],
      investTableHeader: [
        {
          prop: "beginTime1",
          label: "投资归类及完工(非紧急)[项目经理归类]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime1",
        //   label: "投资归类及完工(非紧急)[项目经理归类]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName1",
          label: "投资归类及完工(非紧急)[项目经理归类]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime2",
          label: "投资归类及完工(非紧急)[施工单位发起完工]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime2",
        //   label: "投资归类及完工(非紧急)[施工单位发起完工]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName2",
          label: "投资归类及完工(非紧急)[施工单位发起完工]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime3",
          label: "投资归类及完工(非紧急)[项目经理审批]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime3",
        //   label: "投资归类及完工(非紧急)[项目经理审批]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName3",
          label: "投资归类及完工(非紧急)[项目经理审批]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime4",
          label: "投资归类及完工(非紧急)[等待编排系统审批]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime4",
        //   label: "投资归类及完工(非紧急)[等待编排系统审批]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName4",
          label: "投资归类及完工(非紧急)[等待编排系统审批]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
      ],
      investUrgentTableHeader: [
        {
          prop: "beginTime1",
          label: "投资类分派(紧急)[项目经理]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime1",
        //   label: "投资类分派(紧急)[项目经理]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName1",
          label: "投资类分派(紧急)[项目经理]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime2",
          label: "投资类分派(紧急)[施工单位接收确认]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime2",
        //   label: "投资类分派(紧急)[施工单位接收确认]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName2",
          label: "投资类分派(紧急)[施工单位接收确认]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime3",
          label: "投资完工(紧急)[施工单位发起完工]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime3",
        //   label: "投资完工(紧急)[施工单位发起完工]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName3",
          label: "投资完工(紧急)[施工单位发起完工]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime4",
          label: "投资完工(紧急)[项目经理审批]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime4",
        //   label: "投资完工(紧急)[项目经理审批]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName4",
          label: "投资完工(紧急)[项目经理审批]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "beginTime5",
          label: "投资完工(紧急)[等待编排系统审批]起",
          align: "center",
          width:200,
          tooltip:true
        },
        // {
        //   prop: "endTime5",
        //   label: "投资完工(紧急)[等待编排系统审批]止",
        //   align: "center",
        //   width:200,
        //   tooltip:true
        // },
        {
          prop: "userName5",
          label: "投资完工(紧急)[等待编排系统审批]处理人",
          align: "center",
          width:200,
          tooltip:true
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{
        startDate: this.$route.query.startDate || '',
        endDate: this.$route.query.endDate || '',
        city: this.$route.query.city || '',
        entityStatus: this.$route.query.entityStatus || '',
        isTimeout: this.$route.query.isTimeout || '',
        isProjectline: this.$route.query.isProjectline || '',
        isCount: this.$route.query.isCount || '',
        isEnd: this.$route.query.isEnd || '',
        isEndTimeout: this.$route.query.isEndTimeout || '',
        isComplete: this.$route.query.isComplete || '',
        isCompleteTimeout: this.$route.query.isCompleteTimeout || '',
      },
    };
  },
  created(){
    this.tableHeader = [...this.shareTableHeader, ...this.defaultTableHeader]
    this.activeNameChange()
    this.getAreaList('-2', 5)
  },
  methods: {
    handleClick(tab, event) {
      this.activeNameChange()
      this.$nextTick(() => {
        this.$refs.searchForm.handleSearch()
      })
    },
    activeNameChange() {
      if (this.activeName == 'default') {
        this.tableHeader = [...this.shareTableHeader, ...this.defaultTableHeader]
        this.tableApi = queryListService
      } else if (this.activeName == 'invest') {
        this.tableHeader = [...this.shareTableHeader, ...this.investTableHeader]
        this.tableApi = queryListServiceInvest
      } else {
        this.tableHeader = [...this.shareTableHeader, ...this.investUrgentTableHeader]
        this.tableApi = queryListServiceInvestUrgent
      }
    },
    getAreaList(parentId, index) {
      queryAreaListService({ parentId: parentId, typeCode: 'area', filter: "city" }).then(res => {
        if (res.code === '0000') {
          const list = []
          const cityLabel = this.form.city.label;
          let cityObj = null;
          res.data.forEach(item => {
            if (parentId === '-2' && cityLabel && item.name === cityLabel) {
              cityObj = { label: item.name, value: item.name, key: item.id }
            }
            list.push({ label: item.name, value: item.name, key: item.id })
          })
          this.$set(this.formConfig[index], 'options', list)
          if (cityObj) {
            this.changeSelect('city', cityObj)
          }
        }
      })
    },
    changeSelect(name, val){
      if(name=='city'){
        this.getAreaList(val.key, 6)
        this.$set(this.$refs.searchForm.searchForm,'county','')
      }
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(JSON.stringify(param))
      obj.city=obj.city?obj.city.label:''
      obj.county=obj.county?obj.county.label:''
      obj.entityStatus=obj.entityStatus?obj.entityStatus.label:''
      obj.isTimeout=obj.isTimeout?obj.isTimeout.value:''
      obj.isProjectline=obj.isProjectline?obj.isProjectline.value:''
      this.staticSearchParam=obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj);
      })
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 导出
    download() {
      commonDown({ ...this.staticSearchParam }, downloadService);
    },
  },
};
</script>
