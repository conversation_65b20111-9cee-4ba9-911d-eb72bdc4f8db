<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="prospecting_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :isChange="true"
          :disableForm="disableForm"
          @onChange="changeForm1"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="勘察设计信息">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :form="lineForm"
          :isChange="true"
          :disableForm="disableForm"
          @onChange="changeForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :fileTypeFlag="true"
      :dealPage="dealPage"
    ></mssAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory v-if="businessType" :boId="boId" :workflowCode="businessType"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      dialog-width="60%"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      :tableSingleChoice="true"
      rowKey="projectId"
      @confirm="projectConfirm"
      :dialogTitle="dialogTitle"
      :rowKey="rowKey"
      :tableQueryParams="tableQueryParams"
    ></mssTableSearchDialog>
  </div>
</template>

<script>
import { queryDetailService } from "@/api/inbound_line/extend/survey_construction_api.js";
import Data from "../formSetting1";
export default {
  name: "extend_survey_construction_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return '96%'
      }else{
        return '70%'
      }
    }
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      dealPage: false,
      disableForm: false,
      projectTableApi: function () {},
      searchFieldList: [],
      projectColumns: [],
      dialogTitle: "",
      rowKey:"",
      tableSingleChoice: true,
      tableQueryParams:{},
      basicConfig: [
        {
          label: "设计单名",
          type: "input",
          prop: "name",
          span: 12,
        },
        {
          label: "设计单位名称",
          type: "input",
          prop: "providerName",
          span: 12,
        },
        {
          label: "设备费（元）",
          type: "input",
          mode:'number',
          prop: "equipAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'equipAmount',this.dealNum(value))
            }
          },
        },
        {
          label: "材料费（元）",
          type: "input",
          mode:'number',
          prop: "materialAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'materialAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "施工费（元）",
          type: "input",
          mode:'number',
          prop: "constructionAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'constructionAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "设计费（元）",
          type: "input",
          mode:'number',
          prop: "designAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'designAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "其他费用（元）",
          type: "input",
          mode:'number',
          prop: "otherFees",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'otherFees',this.dealNum(value))
            }
          }
        },
        {
          label: "勘查光缆长度皮长公里",
          type: "input",
          mode:'number',
          readonly:true,
          prop: "fillLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'fillLength',this.dealNum3(value,num))
            }
          }
        },
        {
          label: "拟稿单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "拟稿部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig: JSON.parse(JSON.stringify(Data.lineSet)),
      lineForm: {},
      lineTwoForm: {},
      showlineOne: false,
      showlineTwo: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "勘察<br>设计<br>信息",
          id: "sectionList",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
      for (let i in this.lineConfig) {
        this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
      }
      this.disableForm=true
    } else {
      this.basicConfig[0].readonly = true;
      for (let i in this.basicConfig) {
        if(['0','1','8','9','10','11'].includes(i))
          this.basicConfig[i].disabled = true;
      }
      for (let i in this.lineConfig) {
        if(i<17){
          this.lineConfig[i].disabled = true;
        }
        if(this.lineConfig[i].prop=='city' || this.lineConfig[i].prop=='county' ||
          this.lineConfig[i].prop=='longitude' || this.lineConfig[i].prop=='latitude' ||
          this.lineConfig[i].prop=='scenarioType' || this.lineConfig[i].prop=='requestType'){
          this.lineConfig[i].disabled = true;
        }
        if(this.lineConfig[i].label=='专线接入模型'){
          this.lineConfig[i].prop="accessModelId"
        }
        if(this.lineConfig[i].label=='满足程度'){
          this.lineConfig[i].prop="satisfactionId"
        }
        if(this.lineConfig[i].prop=='totalfare'){
          this.lineConfig[i].eventListeners={
            change: (value)=>{
              this.$set(this.$refs.lineForm.modelForm,'totalfare',this.dealNum(value))
            }
          }
        }
        if(['sheathlength1','sheathlength2','sheathlength3','sheathlength4'].includes(this.lineConfig[i].prop)){
          this.lineConfig[i].eventListeners={
            change: (value)=>{
              this.$set(this.$refs.lineForm.modelForm,this.lineConfig[i].prop,this.dealNum3(value))
            }
          }
        }
      }
      this.dealPage=true
    }
    // 浏览器传参
    this.boId = this.$route.query.boId
    this.$set(this.lineConfig[18], 'options', await this.$dictOptions({parentValue: "AccessModel",appCode: "001001"}))
    this.$set(this.lineConfig[19], 'options', await this.$dictOptions({parentValue: "Satisfaction",appCode: "001001"}))
    this.getDetailsData()
  },
  methods: {
    dealNum(value){
      let newval= ('' + value) // 第一步：转成字符串
        .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
        .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
        .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
        .match(/^\d*(\.?\d{0,2})/g)[0] || ''
      return  newval
    },
    dealNum3(value){
      let newval= ('' + value) // 第一步：转成字符串
        .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
        .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
        .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
        .match(/^\d*(\.?\d{0,3})/g)[0] || ''
      return  newval
    },
    // 获取详情数据
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.businessType=res.data.businessType
        this.$emit("getWorkCode",this.businessType)
        if (res.data.detail) {
          this.lineForm = {
            ...res.data.detail,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
        }
      });
    },
    // 获取数据--计算总数据
    changeForm(param){
      param.totalsheathlength =
        ( Number(param.sheathlength1||0) + Number(param.sheathlength2||0) + Number(param.sheathlength3||0) + Number(param.sheathlength4||0)).toFixed(3);
      this.$set(this.$refs.basicForm.modelForm,'fillLength',(Number(this.$refs.lineForm.modelForm.totalsheathlength||0)+Number(this.$refs.lineTwoForm?.modelForm?.totalsheathlength||0))).toFixed(3)
    },
    // 获取数据--计算总投资
    changeForm1(param){
      this.$set(this.$refs.lineForm.modelForm, 'totalfare', ( Number(this.$refs.basicForm.modelForm.equipAmount||0) + Number(this.$refs.basicForm.modelForm.materialAmount||0) + Number(this.$refs.basicForm.modelForm.constructionAmount||0) +
        Number(this.$refs.basicForm.modelForm.designAmount||0) + Number(this.$refs.basicForm.modelForm.otherFees||0)).toFixed(2));
    },
    projectConfirm(data) {},
  },
};
</script>

<style lang="scss" >
.prospecting_info
.emphasize{
  .el-form-item__label{
    color: red;
  }
}
.detail{
  .emphasize{
    .el-form-item__label{
      color: #333;
    }
  }
}
</style>
