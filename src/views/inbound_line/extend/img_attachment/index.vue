<template>
  <div class="Attachment">
    <mssCard title="附件列表">
      <div class="Attachment-btn" slot="headerBtn">
        <div class="fileType" v-if="fileTypeFlag && dealPage">
          <span style="display: inline-block;width: 100px;">附件类型：</span>
          <el-select v-model="fileType" placeholder="请选择文件类型" ref="select" id="fileType">
            <el-option
              v-for="(item, index) in fileList"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
        <el-button size="mini" @click="downLoadAll">批量下载</el-button>
        <el-upload
          accept=".jpg,.png,.jpeg,.gif"
          class="fileUpload"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="uploadNewFile"
        >
          <el-button v-if="dealPage" size="mini" type="primary">上传附件</el-button>
        </el-upload>
      </div>
      <div slot="content">
        <slot name="filesTop"></slot>
        <mssTable
          ref="files"
          class="files"
          :columns="columns"
          :selection="true"
          :serial="false"
          :api="api"
          :staticSearchParam="tableParams"
        ></mssTable>
        <slot name="filesBottom"></slot>
      </div>
    </mssCard>
  </div>
</template>
<script>
import {
  getFilesDownloadService,
  getFilesDownloadService2,
  getFilesBatchDownloadService,
  getFilesBatchDownloadService2,
  delFiles,
  queryFilesListService,
  uploadFiles,
  getFileTypesService
} from '@/api/attachment'
export default {
  name: 'Attachment',
  props: {
    boId: {
      type: String,
      default: ''
    },
    businessType: {
      type: String,
      default: ''
    },
    nodeName: {
      type: String,
      default: ''
    },
    dealPage: {
      type: Boolean,
      default: true
    },
    fileTypeFlag: {
      type: Boolean,
      default: false
    },
    provincialFlag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      api: null,
      columnsConfig: [
        {
          prop: 'name',
          label: '附件名称',
          align: 'center'
        },
        {
          prop: 'typeName',
          label: '附件预览',
          formatter: (row) => {
            return (
              <el-popover
                placement="right"
                title=""
                trigger="hover">
                <img src={this.getFileUrl(row)}/>
                <img slot="reference" src={this.getFileUrl(row)} alt="" style="max-height: 50px;max-width: 50px"/>
              </el-popover>
            )
          }
        },
        {
          prop: 'attribute5',
          label: '上传节点',
          align: 'center'
        },
        {
          prop: 'loaderName',
          label: '上传人',
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '上传时间',
          align: 'center'
        },
        {
          prop: 'operate',
          label: '操作',
          align: 'center',
          formatter: (row) => {
            return (
              <span>
                <span
                  style="color:#33ACFB;cursor:pointer;margin-right:10px"
                  onClick={() => {
                    this.downFile(row)
                  }}
                >
                  下载
                </span>
                {this.dealPage &&
                row.loaderId == sessionStorage.getItem('userId') ? (
                  <span
                    style="color:#E40077;cursor:pointer;"
                    onClick={() => {
                      this.delBtn(row)
                    }}
                  >
                    删除
                  </span>
                ) : (
                  <span></span>
                )}
              </span>
            )
          }
        }
      ],
      columns: [],
      tableParams: {},
      fileType: '',
      fileList: []
    }
  },
  watch: {
    fileTypeFlag: {
      handler(n, o) {
        const columnsArr = this.columnsConfig.slice()
        if (n) {
          this.columns = columnsArr
        } else {
          columnsArr.splice(1, 1)
          this.columns = columnsArr
        }
      },
      immediate: true
    },
    provincialFlag:{
      handler(n,o){
        if(n){
          this.columns.splice(1,2)
        }
      },
      immediate:true
    }
  },
  created() {
    this.tableParams['boId'] = this.boId
    this.tableParams['businessType'] = this.businessType.toLowerCase()
    this.api = queryFilesListService
    this.fileTypeFlag && this.initFileTypeSelect()
  },
  methods: {
    initFileTypeSelect() {
      getFileTypesService({
        businessType: this.businessType.toLowerCase()
      }).then((res) => {
        if (res && res.code === '0000' && res.data) {
          this.fileList = res.data
        }
      })
    },
    getFileUrl(row){
      return window.g.baseUrl + '/senon-file-center/files/download/'+row.id+'?access_token='+(sessionStorage.getItem('access_token') || '')
    },
    downFile(item) {
      getFilesDownloadService2(item.id)
        .then(res => {
          const url = window.URL.createObjectURL(res.data);
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', item.name); // 设定下载文件的名字
          document.body.appendChild(link);
          link.click()
          // 清理和释放资源
          link.parentNode.removeChild(link)
          window.URL.revokeObjectURL(url) // 释放掉blob对象
        })
        .catch(e=>{
        })
    },
    // 去除文件名的后缀
    removeExtension(filename) {
      return filename.replace(/\.[^/.]+$/, '')
    },
    //批量下载
    downLoadAll() {
      let selectFiles = this.$refs.files.multipleSelection
      if (selectFiles.length === 0) {
        this.$message({
          showClose: true,
          message: '请选择附件',
          type: 'warning'
        })
      } else {
        let idAry = []
        selectFiles.forEach((item) => {
          idAry.push(item.id)
        })

        getFilesBatchDownloadService2({
          boId: this.boId,
          ids: idAry.join()
        })
          .then(res => {
            // 创建blob链接
            const url = window.URL.createObjectURL(new Blob([res.data]));
            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;

            // 可以设置文件名(这里用第一个文件的名字+.zip)
            // 第一个文件的名字，但是要去掉.后缀
            let firstFileName = selectFiles[0].name
            let downloadFileName = this.removeExtension(firstFileName) + '.zip'
            link.setAttribute('download', downloadFileName) // 设定下载文件的名字

            // 模拟点击下载
            document.body.appendChild(link);
            link.click()

            // 清理和释放资源
            link.parentNode.removeChild(link)
            window.URL.revokeObjectURL(url) // 释放掉blob对象
          })
          .catch(e=>{
          })
      }
    },
    delBtn(row) {
      this.$confirm('确定删除选中附件？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'inquiry',
        iconClass: 'el-icon-question',
        modal: false,
        center: false,
        subMessage: '' // 小消息，会以另一种样式显示
      }).then(() => {
        let req = {
          ids: row.id
        }
        delFiles(req).then((res) => {
          if (res && res.code == '0000') {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            })
            this.$refs.files.getTableData()
          }
        })
      })
    },
    uploadNewFile(params) {
      let param = new FormData()
      param.append('files', params.file)
      param.append('businessType', this.businessType.toLowerCase())
      param.append('boId', this.boId)
      param.append('access_token', sessionStorage.getItem('access_token'))
      param.append('attribute5', this.nodeName)
      if (this.fileTypeFlag) {
        if (this.fileType == '') {
          this.$message({
            message: '请选择文件类型',
            type: 'warning'
          })
          return false
        }
        param.append('typeName', this.$refs.select.selectedLabel)
        param.append('typeId', this.fileType)
      }
      uploadFiles(param)
        .then((res) => {
          if (res && res.data && res.code == '0000') {
            // 上传成功
            this.$message({
              showClose: true,
              message: '上传成功',
              duration: 1500,
              type: 'success'
            })
            this.$refs.files.getTableData()
          } else {
            this.$message({
              showClose: true,
              message: res.data.codeMsg || res.data.msg || '上传失败',
              duration: 1500,
              type: 'warning'
            })
          }
        })
        .catch(() => {
          this.$message({
            showClose: true,
            message: '上传失败',
            duration: 1500,
            type: 'warning'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.Attachment {
  .Attachment-btn {
    text-align: right;
    margin-bottom: 16px;
    .fileType {
      display: inline-flex;
    }
    .fileUpload {
      display: inline-block;
      margin-left: 10px;
    }
  }
}
</style>
