<!--施工分派  -->
<template>
  <div class="work_order_open_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      labelWidth="150px"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <div style="color:#f56c6c;"> *统计数据范围为2024年专线新流程二编系统推送工单数据</div>
    <mssCard title="工单建设统计报表">
      <div slot="headerBtn">
        <el-button type="primary" @click="download">导出</el-button>
      </div>
      <div slot="content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="汇总-地市" name="countCity"></el-tab-pane>
          <el-tab-pane label="汇总-区县" name="countCounty"></el-tab-pane>
        </el-tabs>
        <el-table
          v-show="activeName == 'countCity'"
          ref="countCityTable"
          :data="countCityTableData"
          border
          style="width: 100%">
          <el-table-column
            header-align="center"
            align="center"
            prop="city"
            min-width="100"
            label="地市">
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="整体工单数">
            <el-table-column
              header-align="center"
              align="center"
              prop="orderNum"
              min-width="120"
              label="工单数量">
              <template v-slot="scope">
                <el-button type="text" @click="orderNumClick('default', scope.row)">{{ scope.row.orderNum }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="openingDuration"
              min-width="120"
              label="开通时长">
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="timeoutOrderNum"
              min-width="120"
              label="超时工单数">
              <template v-slot="scope">
                <el-button type="text" @click="timeoutOrderClick('default', scope.row)">{{ scope.row.timeoutOrderNum }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="timeoutProportion"
              min-width="120"
              label="超时占比">
              <template v-slot="scope">
                <el-button type="text" :style="scope.row.timeoutProportion != '0.0%' ? 'color: red':'color: black'"
                           @click="timeoutOrderClick('default', scope.row)">
                  {{ scope.row.timeoutProportion }}
                </el-button>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="开通时限3天">
            <el-table-column
              header-align="center"
              align="center"
              label="成本类">
              <el-table-column
                header-align="center"
                align="center"
                prop="orderNum1"
                min-width="120"
                label="工单数量">
                <template v-slot="scope">
                  <el-button type="text" @click="orderNumClick('default', scope.row)">
                    {{ scope.row.orderNum1 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="openingDuration1"
                min-width="120"
                label="开通时长">
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutOrderNum1"
                min-width="120"
                label="超时工单数">
                <template v-slot="scope">
                  <el-button type="text" @click="timeoutOrderClick('default', scope.row)">{{ scope.row.timeoutOrderNum1 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutProportion1"
                min-width="120"
                label="超时占比">
                <template v-slot="scope">
                  <el-button type="text" :style="scope.row.timeoutProportion1 != '0.0%' ? 'color: red':'color: black'"
                             @click="timeoutOrderClick('default', scope.row)">
                    {{ scope.row.timeoutProportion1 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                label="环节时长">
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="acceptDuration1"
                  min-width="120"
                  label="受理时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="buildDuration1"
                  min-width="120"
                  label="建设时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="installDuration1"
                  min-width="120"
                  label="装维时长">
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              label="投资类（紧急）">
              <el-table-column
                header-align="center"
                align="center"
                prop="orderNum2"
                min-width="120"
                label="工单数量">
                <template v-slot="scope">
                  <el-button type="text" @click="orderNumClick('investUrgent', scope.row)">
                    {{ scope.row.orderNum2 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="openingDuration2"
                min-width="120"
                label="开通时长">
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutOrderNum2"
                min-width="120"
                label="超时工单数">
                <template v-slot="scope">
                  <el-button type="text" @click="timeoutOrderClick('investUrgent', scope.row)">{{ scope.row.timeoutOrderNum2 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutProportion2"
                min-width="120"
                label="超时占比">
                <template v-slot="scope">
                  <el-button type="text" :style="scope.row.timeoutProportion2 != '0.0%' ? 'color: red':'color: black'"
                             @click="timeoutOrderClick('investUrgent', scope.row)">
                    {{ scope.row.timeoutProportion2 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                label="环节时长">
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="acceptDuration2"
                  min-width="120"
                  label="受理时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="buildDuration2"
                  min-width="120"
                  label="建设时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="installDuration2"
                  min-width="120"
                  label="装维时长">
                </el-table-column>
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="开通时限5天">
            <el-table-column
              header-align="center"
              align="center"
              label="投资类（非紧急）">
              <el-table-column
                header-align="center"
                align="center"
                prop="orderNum3"
                min-width="120"
                label="工单数量">
                <template v-slot="scope">
                  <el-button type="text" @click="orderNumClick('invest', scope.row)">
                    {{ scope.row.orderNum3 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="openingDuration3"
                min-width="120"
                label="开通时长">
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutOrderNum3"
                min-width="120"
                label="超时工单数">
                <template v-slot="scope">
                  <el-button type="text" @click="timeoutOrderClick('invest', scope.row)">{{ scope.row.timeoutOrderNum3 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutProportion3"
                min-width="120"
                label="超时占比">
                <template v-slot="scope">
                  <el-button type="text"
                             :style="scope.row.timeoutProportion3 != '0.0%' ? 'color: red':'color: black'"
                             @click="timeoutOrderClick('invest', scope.row)">
                    {{ scope.row.timeoutProportion3 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                label="环节时长">
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="acceptDuration3"
                  min-width="120"
                  label="受理时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="designDuration3"
                  min-width="120"
                  label="勘察设计时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="buildDuration3"
                  min-width="120"
                  label="建设时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="installDuration3"
                  min-width="120"
                  label="装维时长">
                </el-table-column>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>

        <el-table
          v-show="activeName == 'countCounty'"
          ref="countCountyTable"
          :data="countCountyTableData"
          :span-method="objectSpanMethod"
          border
          style="width: 100%">
          <el-table-column
            header-align="center"
            align="center"
            prop="city"
            min-width="100"
            label="地市">
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            prop="county"
            min-width="100"
            label="区县">
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="整体工单数">
            <el-table-column
              header-align="center"
              align="center"
              prop="orderNum"
              min-width="120"
              label="工单数量">
              <template v-slot="scope">
                <el-button type="text" @click="orderNumClick1('default', scope.row)">{{ scope.row.orderNum }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="openingDuration"
              min-width="120"
              label="开通时长">
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="timeoutOrderNum"
              min-width="120"
              label="超时工单数">
              <template v-slot="scope">
                <el-button type="text" @click="timeoutOrderClick1('default', scope.row)">{{ scope.row.timeoutOrderNum }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="timeoutProportion"
              min-width="120"
              label="超时占比">
              <template v-slot="scope">
                <el-button type="text" :style="scope.row.timeoutProportion != '0.0%' ? 'color: red':'color: black'"
                           @click="timeoutOrderClick1('default', scope.row)">
                  {{ scope.row.timeoutProportion }}
                </el-button>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="开通时限3天">
            <el-table-column
              header-align="center"
              align="center"
              label="成本类">
              <el-table-column
                header-align="center"
                align="center"
                prop="orderNum1"
                min-width="120"
                label="工单数量">
                <template v-slot="scope">
                  <el-button type="text" @click="orderNumClick1('default', scope.row)">
                    {{ scope.row.orderNum1 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="openingDuration1"
                min-width="120"
                label="开通时长">
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutOrderNum1"
                min-width="120"
                label="超时工单数">
                <template v-slot="scope">
                  <el-button type="text" @click="timeoutOrderClick1('default', scope.row)">{{ scope.row.timeoutOrderNum1 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutProportion1"
                min-width="120"
                label="超时占比">
                <template v-slot="scope">
                  <el-button type="text" :style="scope.row.timeoutProportion1 != '0.0%' ? 'color: red':'color: black'"
                             @click="timeoutOrderClick1('default', scope.row)">
                    {{ scope.row.timeoutProportion1 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                label="环节时长">
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="acceptDuration1"
                  min-width="120"
                  label="受理时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="buildDuration1"
                  min-width="120"
                  label="建设时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="installDuration1"
                  min-width="120"
                  label="装维时长">
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              label="投资类（紧急）">
              <el-table-column
                header-align="center"
                align="center"
                prop="orderNum2"
                min-width="120"
                label="工单数量">
                <template v-slot="scope">
                  <el-button type="text" @click="orderNumClick1('investUrgent', scope.row)">
                    {{ scope.row.orderNum2 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="openingDuration2"
                min-width="120"
                label="开通时长">
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutOrderNum2"
                min-width="120"
                label="超时工单数">
                <template v-slot="scope">
                  <el-button type="text" @click="timeoutOrderClick1('investUrgent', scope.row)">{{ scope.row.timeoutOrderNum2 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutProportion2"
                min-width="120"
                label="超时占比">
                <template v-slot="scope">
                  <el-button type="text" :style="scope.row.timeoutProportion2 != '0.0%' ? 'color: red':'color: black'"
                             @click="timeoutOrderClick1('investUrgent', scope.row)">
                    {{ scope.row.timeoutProportion2 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                label="环节时长">
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="acceptDuration2"
                  min-width="120"
                  label="受理时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="buildDuration2"
                  min-width="120"
                  label="建设时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="installDuration2"
                  min-width="120"
                  label="装维时长">
                </el-table-column>
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="开通时限5天">
            <el-table-column
              header-align="center"
              align="center"
              label="投资类（非紧急）">
              <el-table-column
                header-align="center"
                align="center"
                prop="orderNum3"
                min-width="120"
                label="工单数量">
                <template v-slot="scope">
                  <el-button type="text" @click="orderNumClick1('invest', scope.row)">
                    {{ scope.row.orderNum3 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="openingDuration3"
                min-width="120"
                label="开通时长">
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutOrderNum3"
                min-width="120"
                label="超时工单数">
                <template v-slot="scope">
                  <el-button type="text" @click="timeoutOrderClick1('invest', scope.row)">{{ scope.row.timeoutOrderNum3 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="timeoutProportion3"
                min-width="120"
                label="超时占比">
                <template v-slot="scope">
                  <el-button type="text"
                             :style="scope.row.timeoutProportion3 != '0.0%' ? 'color: red':'color: black'"
                             @click="timeoutOrderClick1('invest', scope.row)">
                    {{ scope.row.timeoutProportion3 }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                label="环节时长">
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="acceptDuration3"
                  min-width="120"
                  label="受理时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="designDuration3"
                  min-width="120"
                  label="勘察设计时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="buildDuration3"
                  min-width="120"
                  label="建设时长">
                </el-table-column>
                <el-table-column
                  header-align="center"
                  align="center"
                  prop="installDuration3"
                  min-width="120"
                  label="装维时长">
                </el-table-column>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  queryListService,
  downloadService
} from "@/api/inbound_line/extend/work_order_open_api.js";
import {commonDown} from "@/utils/btn";

export default {
  name: "extend_work_order_open_list",
  data() {
    return {
      activeName: 'countCity',
      formConfig: [
        {
          label: '统计时间',
          type: 'date2',
          fieldName: 'startDate',
          fieldName2: 'endDate'
        },
        {
          label: '是否项目类',
          type: 'select',
          fieldName: 'isProjectline',
          itemAs:true,
          options: [
            { label: '是', value: '是' },
            { label: '否', value: '否' },
          ]
        }
      ],
      countCityTableData: [],
      countCountyTableData: [],
      staticSearchParam: {},
      loadParam: {}
    };
  },
  created() {
    // this.getList({});
  },
  methods: {
    handleClick(tab, event) {
    },
    getList(obj) {
      queryListService(obj).then(res => {
        this.countCityTableData = [...res.data.countCityList];
        this.countCountyTableData = [...res.data.countCountyList];
      })
    },
    // 查询
    search(param) {
      let obj = JSON.parse(JSON.stringify(param))
      obj.isProjectline=obj.isProjectline?obj.isProjectline.value:''
      this.staticSearchParam = obj
      this.loadParam = obj
      this.$nextTick(() => {
        this.getList(obj);
      })
    },
    orderNumClick(type, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: '已完成',
          isEnd: '1',
        },
      });
    },
    timeoutOrderClick(type, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: '已完成',
          isEnd: '1',
          isEndTimeout: '1',
        },
      });
    },
    orderNumClick1(type, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: '已完成',
          // county: row.county,
          isEnd: '1',
        },
      });
    },
    timeoutOrderClick1(type, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: '已完成',
          // county: row.county,
          isEnd: '1',
          isEndTimeout: '1',
        },
      });
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === this.countCountyTableData.length - 1) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
      }
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 导出
    download() {
      commonDown({...this.loadParam}, downloadService);
    },
  },
};
</script>
