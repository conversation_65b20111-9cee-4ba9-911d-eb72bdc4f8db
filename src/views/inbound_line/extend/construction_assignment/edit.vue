<!-- 施工分派处理页面 -->
<template>
  <div class="construction_assignment_detail page-anchor-parentpage">
    <div class="operate-btn">
      <el-button type="primary" @click="read()" v-if="disableForm">已阅</el-button>
      <el-button type="primary" @click="submit()" v-if="!disableForm">提交</el-button>
      <el-button type="primary" @click="save()" v-if="!disableForm">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      :dept-params="deptParams"
      @getNodeData="getNodeData"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
    ></mssWorkFlowHandel>
    <infos labelPosition="top" ref="infos" :formRules="formRules" @getWorkCode="getWorkCode"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService, beforeSubmitService} from "@/api/inbound_line/extend/construction_assignment_api.js"
import {readAlready} from "@/api/inbound_line/extend/group_customer_job";

export default {
  name: 'extend_construction_assignment_detail',
  components: {infos},
  data() {
    return {
      deptParams: {},
      boId: '',
      workflowCode: '',
      completeTaskUrl: "",//流程处理地址
      returnAddress: '/inbound_line/extend/construction_assignment',//流程提交成功返回路由地址
      formRules: {},
      disableForm: false,
      taskId: '',
    }
  },
  created() {
    this.boId = this.$route.query.boId
  },
  methods: {
    getWorkCode(param) {
      this.workflowCode = param
      this.$nextTick(() => {
        this.$refs.workFlow.init()
      })
    },
    // 返回上一页
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/extend/construction_assignment'
      })
    },

    // 保存
    save(cb) {
      this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          // 处理数据
          let param = {
            ...this.$refs.infos.$refs.basicForm.modelForm
          }
          param.durationPlanBeginDate = param.durationPlan[0] || ''
          param.durationPlanEndDate = param.durationPlan[1] || ''
          if (this.$refs.infos.showlineOne) {
            param.detail = this.$refs.infos.$refs.lineForm.modelForm
          }
          if (this.$refs.infos.showlineTwo) {
            param.detailOther = this.$refs.infos.$refs.lineTwoForm.modelForm
          }
          saveService(param).then(res => {
            if (res.code == '0000') {
              if (cb) {
                cb(param)
              } else {
                this.$message.success('保存成功')
              }
            }
          })
        } else {
          return
        }
      })
    },
    getNodeData(data) {
      this.nodeCode = data.nodeCode || ""
      this.taskId = data.taskId || ""
      if (this.nodeCode === 'read') {
        this.disableForm = true
      } else {
        this.disableForm = false
      }
    },
    read() {
      readAlready(this.taskId).then(res => {
        this.goBack();
      })
    },
    // 提交--先保存后提交
    submit() {
      // this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid)=>{
      //   if(valid){
      this.save((param) => {
        this.$refs.infos.basicForm = {
          ...this.$refs.infos.basicForm,
          ...param
        };
        this.completeTaskUrl = this.$refs.infos.basicForm.completeTaskUrl
        if (this.nodeCode == '1') {// 项目经理，根据施工单位过滤
          this.deptParams = {
            rootId: this.$refs.infos.$refs.basicForm.modelForm.constructCompanyId || '',
            orgChildId: this.$refs.infos.$refs.basicForm.modelForm.constructCompanyId || ''
          }
        }
        this.$refs.workFlow.opendialogInitNextPath()
      })
      //   }else{
      //       return
      //     }
      // })

    },
    //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      let name = {code: 'name', value: this.$refs.infos.$refs.basicForm.modelForm.name}
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit() {
      let str = ""
      await this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid) => {
        str += valid ? '' : '有必填信息未填写完整，请检查; '
      })
      let res = await beforeSubmitService({boId: this.boId})
      if (res.data && res.data.code != 0) {
        str += res.data.msg
      }
      return str
    },

    beforeNode() {
      const fileNameArr = {
        constructManger: {
          userName: this.$refs.infos.$refs.basicForm.modelForm.consDeptUserName,
          userId: this.$refs.infos.$refs.basicForm.modelForm.consDeptUserId
        }
      }
      // 设置默认处理人
      return fileNameArr
    },
    // 必填校验
    initFormRules(param) {

      this.formRules = param
      if (this.formRules["discountFactor"] && this.formRules["discountFactor"].length) {
        this.formRules["discountFactor"].push({
          required: true,
          validator: (rule, value, callback) => {
            if (Number(value) < 0 || Number(value) > 1) {
              callback(new Error("折扣系数只能是0-1之间的值"));
            } else {
              callback();
            }
          },
          trigger: "blur"
        })
      }
    },
    // 只读表单
    getReadonlyList(param) {
      const disableForm = this.disableForm;
      if (param.length || disableForm) {
        this.$refs.infos.basicConfig.forEach(item => {
          if (disableForm) {
            item.disabled = true;
          } else if (param.includes(item.prop)) {
            item.disabled = true;
          }
        })
        this.$refs.infos.lineConfig.forEach(item=>{
          if (disableForm) {
            item.disabled = true;
          } else if (param.includes(item.prop)){
            item.disabled = true
          }
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
