<!-- 施工分派查看页面 -->
<template>
  <div class="construction_assignment_look page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <infos labelPosition="left"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService} from "@/api/inbound_line/extend/construction_assignment_api.js"
export default {
  name: 'extend_construction_assignment_look',
  components:{infos},
  data() {
    return {

    }
  },
  created() {
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/extend/construction_assignment'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
