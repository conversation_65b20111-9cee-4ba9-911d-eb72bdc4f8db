<!-- 预覆盖建设查看页面 -->
<template>
  <div class="precovering_construction_look page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <infos labelPosition="left"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
export default {
  name: 'extend_precovering_construction_look',
  components:{infos},
  data() {
    return {

    }
  },
  created() {
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/extend/precovering_construction'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
