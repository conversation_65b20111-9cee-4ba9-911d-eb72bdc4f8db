<!-- 预覆盖建设公共信息（处理与查看公用） -->
<template>
  <div class="precovering_construction_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :isChange="true"
          :disableForm="disableForm"
          @onChange="changeForm1"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="勘察设计信息">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :form="lineForm"
          :isChange="true"
          :disableForm="disableForm"
          @onChange="changeForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :dealPage="dealPage"
      :fileTypeFlag="true"
    ></mssAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
          v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      dialog-width="70%"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      @confirm="projectConfirm"
      :dialogTitle="dialogTitle"
      :tableSingleChoice="true"
      :rowKey="rowKey"
      :tableQueryParams="tableQueryParams"
    ></mssTableSearchDialog>
  </div>
</template>

<script>
import { queryDetailService } from "@/api/inbound_line/extend/precovering_construction_api.js";
import {
  getProjectListService,
  getTaskListService,
} from "@/api/inbound_line/extend/sorting_tank_api.js";
import Data from "../formSetting2";
export default {
  name: "extend_precovering_construction_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      disableForm: false,
      dealPage:false,
      projectTableApi: function () {},
      searchFieldList: [],
      projectColumns: [],
      dialogTitle: "",
      rowKey:'',
      tableQueryParams:{},
      basicConfig: [
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
          eventListeners: {
            focus: this.openProjectSelectDialog, // 打开选择项目的弹窗
          },
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "任务名称",
          type: "input",
          prop: "taskName",
          readonly: true,
          eventListeners: {
            focus: this.clickNative, // 打开选择项目的弹窗
          },
          span: 12,
        },
        {
          label: "任务编码",
          type: "input",
          prop: "taskCode",
          span: 12,
        },
        {
          label: "设计单名",
          type: "input",
          prop: "name",
          span: 24,
        },
        {
          label: "设计单位名称",
          type: "input",
          prop: "providerName",
          span: 12,
        },
        {
          label: "站点名称",
          type: "input",
          prop: "siteName",
          span: 12,
        },
        {
          label: "设备费（元）",
          type: "input",
          mode:'number',
          prop: "equipAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'equipAmount',this.dealNum(value))
            }
          },
        },
        {
          label: "材料费（元）",
          type: "input",
          mode:'number',
          prop: "materialAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'materialAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "施工费（元）",
          type: "input",
          mode:'number',
          prop: "constructionAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'constructionAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "设计费（元）",
          type: "input",
          mode:'number',
          prop: "designAmount",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'designAmount',this.dealNum(value))
            }
          }
        },
        {
          label: "其他费用（元）",
          type: "input",
          mode:'number',
          prop: "otherFees",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'otherFees',this.dealNum(value))
            }
          }
        },
        {
          label: "勘查光缆长度皮长公里",
          type: "input",
          mode:'number',
          readonly:true,
          prop: "fillLength",
          span: 12,
          eventListeners: {
            change: (value)=>{
              this.$set(this.$refs.basicForm.modelForm,'fillLength',this.dealNum3(value,num))
            }
          }
        },
        {
          label: "拟稿单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "拟稿部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig: JSON.parse(JSON.stringify(Data.lineSet)),
      lineForm: {},
      lineTwoForm: {},
      showlineOne: false,
      showlineTwo: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "勘察<br>设计<br>信息",
          id: "sectionList",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
      city: '',
    };
  },
  created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
        if(this.basicConfig[i].prop=='durationPlan')
          this.basicConfig[i].type='input'
      }
      for (let i in this.lineConfig) {
        this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
      }
      this.disableForm = true;
    } else {
      for (let i in this.basicConfig) {
        if (["1","3", "4", "5", "13", "14","15","16"].includes(i)) {
          this.basicConfig[i].disabled = true;
        }
      }
      for (let i in this.lineConfig) {
        if (i < 9) {
          this.lineConfig[i].disabled = true;
        }
        if(this.lineConfig[i].prop=='city' || this.lineConfig[i].prop=='county' ||
          this.lineConfig[i].prop=='longitude' || this.lineConfig[i].prop=='latitude' ||
          this.lineConfig[i].prop=='scenarioType' || this.lineConfig[i].prop=='requestType'){
          this.lineConfig[i].disabled = true;
        }
      }
      this.dealPage = true;
    }

    // 浏览器传参
    this.boId = this.$route.query.boId;
    this.getDetailsData();
  },
  methods: {
    dealNum(value){
      let newval= ('' + value) // 第一步：转成字符串
        .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
        .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
        .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
        .match(/^\d*(\.?\d{0,2})/g)[0] || ''
      return  newval
    },
    dealNum3(value){
      let newval= ('' + value) // 第一步：转成字符串
        .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
        .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
        .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
        .match(/^\d*(\.?\d{0,3})/g)[0] || ''
      return  newval
    },
    // 获取详情数据
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.businessType = res.data.businessType
        this.$emit("getWorkCode", this.businessType)
        this.basicForm.durationPlan = ( res.data.durationPlanBeginDate || res.data.durationPlanEndDate)?[
          res.data.durationPlanBeginDate || "",
          res.data.durationPlanEndDate || "",
        ]:[];
        if(this.labelPosition == "left") {this.basicForm.durationPlan=this.basicForm.durationPlan.join('~')}
        this.city = res.data.detail.city;
        this.lineForm = {
          ...res.data.detail,
        };
      });
    },
    // 获取数据--计算总数据
    changeForm(param){
      param.totalsheathlength =
        ( Number(param.sheathlength1||0) + Number(param.sheathlength2||0) + Number(param.sheathlength3||0) + Number(param.sheathlength4||0)).toFixed(3);
      this.$set(this.$refs.basicForm.modelForm,'fillLength',(Number(this.$refs.lineForm.modelForm.totalsheathlength||0)+Number(this.$refs.lineTwoForm?.modelForm?.totalsheathlength||0))).toFixed(3)
    },
    // 获取数据--计算总投资
    changeForm1(param){
      this.$set(this.$refs.lineForm.modelForm, 'totalfare', ( Number(this.$refs.basicForm.modelForm.equipAmount||0) + Number(this.$refs.basicForm.modelForm.materialAmount||0) + Number(this.$refs.basicForm.modelForm.constructionAmount||0) +
        Number(this.$refs.basicForm.modelForm.designAmount||0) + Number(this.$refs.basicForm.modelForm.otherFees||0)).toFixed(2));
    },
    clickNative() {
      if(this.$refs.basicForm.modelForm.projectCode){
        this.openTaskDialog()
      }else{
        this.$message.warning('请先选择项目')
      }
    },
    openProjectSelectDialog() {
      this.projectTableApi = getProjectListService;
      this.searchFieldList = [
        {
          label: "项目名称",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          fieldName: "projectCode",
        },
      ];
      this.projectColumns = [
        {
          label: "项目名称",
          prop: "projectName",
          minWidth: 200,
        },
        {
          label: "项目编码",
          prop: "projectCode",
          minWidth: 100,
        },
        {
          label: "建设单位名称",
          prop: "orgName",
          minWidth: 200,
        },
      ];
      this.tableQueryParams={
        orgName:this.city,
        lineType: "0",
        projectNames: ["宽带","商客预覆盖","园区圈"]
      }
      this.dialogTitle = "选择项目";
      this.rowKey="projectId"
      this.tableSingleChoice = true;
      this.$refs.projectNameSearchDialog.openDialog();
      this.type = '';
    },
    openTaskDialog() {
      this.projectTableApi = getTaskListService;
      this.searchFieldList = [
        {
          label: "任务名称",
          fieldName: "taskName",
        },
        {
          label: "任务编码",
          fieldName: "taskCode",
        },
        {
          label: "地市",
          fieldName: "city",
        },
        {
          label: "区县",
          fieldName: "county",
        },
      ];
      this.projectColumns = [
        {
          label: "任务名称",
          prop: "taskName",
          minWidth: 200,
        },
        {
          label: "任务编码",
          prop: "taskCode",
          minWidth: 100,
        },
        {
          label: "地市公司",
          prop: "city",
          minWidth: 200,
        },
        {
          label: "区县公司",
          prop: "county",
          minWidth: 100,
        },
      ];
      this.dialogTitle = "选择任务";
      this.tableSingleChoice = true;
      this.rowKey="taskId"
      this.tableQueryParams={
        projectCode:this.$refs.basicForm.modelForm.projectCode,
        projectNames: ["宽带","商客预覆盖"],
        lineType: "0",
        city:this.city,
        type: "2",
      }
      this.$refs.projectNameSearchDialog.openDialog();
      this.type ='task';
    },

    projectConfirm(data) {
      if (data && data[0]) {
        if (this.type == 'taskId') {
          this.$refs.basicForm.modelForm = {
            ...this.$refs.basicForm.modelForm,
            taskName: data[0].taskName,
            taskCode: data[0].taskCode,
            taskId: data[0].taskId,
          };
        } else {
          this.$refs.basicForm.modelForm = {
            ...this.$refs.basicForm.modelForm,
            ...data[0],
            city: data[0].orgName || this.city
          };
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
