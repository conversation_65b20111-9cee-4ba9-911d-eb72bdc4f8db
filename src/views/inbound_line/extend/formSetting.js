export default {
  baseSet: 'Hello, World!', // 基本信息
  lineSet: [
    {
      label: "前端工单号",
      type: "input",
      prop: "outsideNum",
      span: 12,
    },
    {
      label: "编排工单号",
      type: "input",
      prop: "irmsWorkoder",
      span: 12,
    },
    {
      label: "地市",
      type: "input",
      prop: "city",
      span: 12,
    },
    {
      label: "区县",
      type: "input",
      prop: "county",
      span: 12,
    },
    {
      label: "专线编号即计费编号",
      type: "input",
      prop: "lineNumber",
      span: 12,
    },
    {
      label: "专线名称",
      type: "input",
      prop: "lineName",
      span: 12,
    },
    {
      label: "开通单创建时间",
      type: "input",
      prop: "sendTime",
      span: 24,
    },
    {
      label: "传输专线（业务端所属）",
      type: "input",
      prop: "transmissioinLineName",
      span: 12,
    },
    {
      label: "集客详细地址(精确街道、号)",
      type: "input",
      prop: "customerAddress",
      span: 12,
    },
    {
      label: "专线类型",
      type: "input",
      prop: "lineType",
      span: 12,
    },
    {
      label: "带宽容量(M)",
      type: "input",
      prop: "broadbandCapacity",
      span: 12,
    },
    {
      label: "客户经理",
      type: "input",
      prop: "managerName",
      span: 12,
    },
    {
      label: "客户经理联系人电话",
      type: "input",
      prop: "managerTel",
      span: 12,
    },
    {
      label: "客户联系人",
      type: "input",
      prop: "customerName",
      span: 12,
    },
    {
      label: "客户联系人电话",
      type: "input",
      prop: "customerTel",
      span: 12,
    },
    {
      label: "专线承载方式",
      type: "input",
      prop: "loadmode",
      span: 12,
    },
    {
      label: "SLA保障等级",
      type: "input",
      prop: "slalevel",
      span: 12,
    },
    {
      label: "工单主题",
      type: "input",
      prop: "irmsName",
      span: 24,
    },
    {
      label: "客户服务等级",
      type: "input",
      prop: "customerSerLevel",
      span: 24,
    },
    {
      label: "建设类型",
      type: "input",
      prop: "buildType",
      span: 12,
    },
    {
      label: "所属区域类型",
      type: "select",
      prop: "beLongAreaType",
      options: [{label:'商务楼宇、学校、医院、独立企业/机构',value:'1'},{label:'一般城区/郊县中心',value:'2'},{label:'其他区域',value:'3'}],
      span: 12,
    },
    {
      label: "是否项目制专线",
      type: "select",
      prop: "isProjectline",
      options: [{label:'是',value:'1'},{label:'否',value:'2'}],
      span: 12,
    },
    {
      label: "是否紧急需求",
      type: "select",
      prop: "isAgency",
      options: [{label:'是',value:'1'},{label:'否',value:'2'}],
      span: 12,
    },
    {
      label: "专线接入模型",
      type: "select",
      options:[],
      prop: "accessModelId",
      span: 12,
    },
    {
      label: "满足程度",
      type: "select",
      options:[],
      prop: "satisfactionId",
      span: 12,
    },
    {
      label: "归属综合业务接入区",
      type: "input",
      prop: "serviceArea",
      span: 24,
    },
    {
      label: "专线业务接入起点机房",
      type: "input",
      prop: "room",
      span: 12,
    },
    {
      label: "专线业务接入起点设备名称",
      type: "input",
      prop: "deviceName",
      span: 12,
    },
    {
      label: "专线光缆接入点",
      type: "input",
      prop: "occpName",
      span: 12,
    },
    {
      label: "专线业务接入分光器名称",
      type: "input",
      prop: "splitterName",
      span: 12,
    },
    {
      label: "利旧杆路/墙吊壁挂芯数",
      type: "input",
      prop: "fiberamount1",
      span: 12,
    },
    {
      label: "利旧杆路/墙吊壁挂皮长公里",
      type: "input",
      mode:'number',
      prop: "sheathlength1",
      span: 12,
    },
    {
      label: "新建杆路敷设光缆芯数",
      type: "input",
      prop: "fiberamount2",
      span: 12,
    },
    {
      label: "新建杆路敷设光缆皮长公里",
      type: "input",
      mode:'number',
      prop: "sheathlength2",
      span: 12,
    },
    {
      label: "管道光缆芯数",
      type: "input",
      prop: "fiberamount3",
      span: 12,
    },
    {
      label: "管道光缆皮长公里",
      type: "input",
      mode:'number',
      prop: "sheathlength3",
      span: 12,
    },
    {
      label: "直埋光缆芯数",
      type: "input",
      prop: "fiberamount4",
      span: 12,
    },
    {
      label: "直埋光缆皮长公里",
      type: "input",
      mode:'number',
      prop: "sheathlength4",
      span: 12,
    },
    {
      label: "光缆合计长度皮长公里",
      type: "input",
      mode:'number',
      readonly:true,
      prop: "totalsheathlength",
      span: 24,
      class:'emphasize',
    },
    {
      label: "接入设施名称",
      type: "input",
      readonly: true,
      prop: "accessDeviceName",
      span: 12,
    },
    {
      label: "接入设施1名称",
      type: "input",
      readonly: true,
      prop: "accessDeviceName1",
      span: 12,
    },
    {
      label: "基站侧新增设备1名称",
      type: "input",
      prop: "btsdevice1",
      span: 12,
    },
    {
      label: "基站侧新增设备1型号",
      type: "input",
      prop: "btsdevicetype1",
      span: 12,
    },
    {
      label: "基站侧新增设备2名称",
      type: "input",
      prop: "btsdevice2",
      span: 12,
    },
    {
      label: "基站侧新增设备2型号",
      type: "input",
      prop: "btsdevicetype2",
      span: 12,
    },
    {
      label: "客户侧新增设备1名称",
      type: "input",
      prop: "customerdevice1",
      span: 12,
    },
    {
      label: "客户侧新增设备1型号",
      type: "input",
      prop: "customerdevicetype1",
      span: 12,
    },
    {
      label: "客户侧新增设备2名称",
      type: "input",
      prop: "customerdevice2",
      span: 12,
    },
    {
      label: "客户侧新增设备2型号",
      type: "input",
      prop: "customerdevicetype2",
      span: 12,
    },
    {
      label: "计划工期（天）",
      type: "input",
      mode:'number',
      prop: "constructionperiod",
      span: 12,
    },
    {
      label: "总投资（元）",
      type: "input",
      mode:'number',
      prop: "totalfare",
      class:'emphasize',
      span: 12,
    },
    {
      label: "建设成本（万元）",
      type: "input",
      mode:'number',
      prop: "constructCost",
      span: 12,
    },
  ] // 集客专线信息
};
// lineSet: [
//   {
//     label: "外部编号",
//     type: "input",
//     prop: "outsideNum",
//     span: 12,
//   },
//   {
//     label: "内部编码",
//     type: "input",
//     prop: "insideNum",
//     span: 12,
//   },
//   {
//     label: "地市",
//     type: "input",
//     prop: "city",
//     span: 12,
//   },
//   {
//     label: "区县",
//     type: "input",
//     prop: "county",
//     span: 12,
//   },
//   {
//     label: "专线名称",
//     type: "input",
//     prop: "lineName",
//     span: 24,
//   },
//   {
//     label: "传输专线（业务端所属）",
//     type: "input",
//     prop: "transmissioinLineName",
//     span: 12,
//   },
//   {
//     label: "集客详细地址(精确街道、号)",
//     type: "input",
//     prop: "customerAddress",
//     span: 12,
//   },
//   {
//     label: "专线编号即计费编号",
//     type: "input",
//     prop: "lineNumber",
//     span: 12,
//   },
//   {
//     label: "处理流水号",
//     type: "input",
//     prop: "serialno",
//     span: 12,
//   },
//   {
//     label: "SLA保障等级",
//     type: "input",
//     prop: "SLALevel",
//     span: 12,
//   },
//   {
//     label: "专线承载方式",
//     type: "input",
//     prop: "loadmode",
//     span: 12,
//   },
//   {
//     label: "专线类型",
//     type: "input",
//     prop: "lineType",
//     span: 12,
//   },
//   {
//     label: "带宽容量(M)",
//     type: "input",
//     prop: "broadbandCapacity",
//     span: 12,
//   },
//   {
//     label: "客户经理",
//     type: "input",
//     prop: "managerName",
//     span: 12,
//   },
//   {
//     label: "客户经理联系人电话",
//     type: "input",
//     prop: "managerTel",
//     span: 12,
//   },
//   {
//     label: "客户联系人",
//     type: "input",
//     prop: "customerName",
//     span: 12,
//   },
//   {
//     label: "客户联系人电话",
//     type: "input",
//     prop: "customerTel",
//     span: 12,
//   },
//   {
//     label: "客户服务等级",
//     type: "input",
//     prop: "customerSerLevel",
//     span: 24,
//   },
//   {
//     label: "专线接入模型",
//     type: "select",
//     options:[],
//     prop: "accessModelName",
//     span: 12,
//   },
//   {
//     label: "满足程度",
//     type: "select",
//     options:[],
//     prop: "satisfactionName",
//     span: 12,
//   },
//   {
//     label: "归属综合业务接入区",
//     type: "input",
//     prop: "serviceArea",
//     span: 24,
//   },
//   {
//     label: "专线业务接入起点机房",
//     type: "input",
//     prop: "room",
//     span: 12,
//   },
//   {
//     label: "专线业务接入起点设备名称",
//     type: "input",
//     prop: "deviceName",
//     span: 12,
//   },
//   {
//     label: "专线光缆接入点",
//     type: "input",
//     prop: "occpName",
//     span: 12,
//   },
//   {
//     label: "专线业务接入分光器名称",
//     type: "input",
//     prop: "splitterName",
//     span: 12,
//   },
//   {
//     label: "利旧杆路/墙吊壁挂芯数",
//     type: "input",
//     prop: "fiberamount1",
//     span: 12,
//   },
//   {
//     label: "利旧杆路/墙吊壁挂皮长公里",
//     type: "input",
//     mode:'number',
//     prop: "sheathlength1",
//     span: 12,
//   },
//   {
//     label: "新建杆路敷设光缆芯数",
//     type: "input",
//     prop: "fiberamount2",
//     span: 12,
//   },
//   {
//     label: "新建杆路敷设光缆皮长公里",
//     type: "input",
//     mode:'number',
//     prop: "sheathlength2",
//     span: 12,
//   },
//   {
//     label: "管道光缆芯数",
//     type: "input",
//     prop: "fiberamount3",
//     span: 12,
//   },
//   {
//     label: "管道光缆皮长公里",
//     type: "input",
//     mode:'number',
//     prop: "sheathlength3",
//     span: 12,
//   },
//   {
//     label: "直埋光缆芯数",
//     type: "input",
//     prop: "fiberamount4",
//     span: 12,
//   },
//   {
//     label: "直埋光缆皮长公里",
//     type: "input",
//     mode:'number',
//     prop: "sheathlength4",
//     span: 12,
//   },
//   {
//     label: "光缆合计长度皮长公里",
//     type: "input",
//     mode:'number',
//     readonly:true,
//     prop: "totalsheathlength",
//     span: 24,
//     class:'emphasize',
//   },
//   {
//     label: "基站侧新增设备1名称",
//     type: "input",
//     prop: "btsdevice1",
//     span: 12,
//   },
//   {
//     label: "基站侧新增设备1型号",
//     type: "input",
//     prop: "btsdevicetype1",
//     span: 12,
//   },
//   {
//     label: "基站侧新增设备2名称",
//     type: "input",
//     prop: "btsdevice2",
//     span: 12,
//   },
//   {
//     label: "基站侧新增设备2型号",
//     type: "input",
//     prop: "btsdevicetype2",
//     span: 12,
//   },
//   {
//     label: "客户侧新增设备1名称",
//     type: "input",
//     prop: "customerdevice1",
//     span: 12,
//   },
//   {
//     label: "客户侧新增设备1型号",
//     type: "input",
//     prop: "customerdevicetype1",
//     span: 12,
//   },
//   {
//     label: "客户侧新增设备2名称",
//     type: "input",
//     prop: "customerdevice2",
//     span: 12,
//   },
//   {
//     label: "客户侧新增设备2型号",
//     type: "input",
//     prop: "customerdevicetype2",
//     span: 12,
//   },
//   {
//     label: "计划工期（天）",
//     type: "input",
//     mode:'number',
//     prop: "constructionperiod",
//     span: 12,
//   },
//   {
//     label: "总投资（元）",
//     type: "input",
//     mode:'number',
//     prop: "totalfare",
//     class:'emphasize',
//     span: 12,
//   },
// ]
