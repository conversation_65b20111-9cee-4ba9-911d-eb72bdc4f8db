export default {
  baseSet: 'Hello, World!', // 基本信息
  lineSet: [
    {
      label: "处理流水号",
      type: "input",
      prop: "serialNumber",
      span: 12,
    },
    {
      label: "场景类型",
      type: "input",
      prop: "scenarioType",
      span: 12,
    },
    {
      label: "业务类型",
      type: "input",
      prop: "businessType",
      span: 12,
    },
    {
      label: "专线类型",
      type: "input",
      prop: "lineType",
      span: 12,
    },
    {
      label: "集客详细地址(精确街道、号)",
      type: "input",
      prop: "customerAddress",
      span: 24,
    },
    {
      label: "客户经理",
      type: "input",
      prop: "managerName",
      span: 12,
    },
    {
      label: "客户经理联系人电话",
      type: "input",
      prop: "managerTel",
      span: 12,
    },
    {
      label: "客户联系人",
      type: "input",
      prop: "contactName",
      span: 12,
    },
    {
      label: "客户联系人电话",
      type: "input",
      prop: "contactTel",
      span: 12,
    },
    {
      label: "覆盖地域",
      type: "select",
      options: [
        {label:'市区',value:'市区'},
        {label:'县城',value:'县城'},
        {label:'乡镇',value:'乡镇'},
        {label:'农村',value:'农村'}
      ],
      prop: "coverTerritory",
      span: 12,
    },
    {
      label: "市场分类",
      type: "select",
      options: [
        {label:'小微市场',value:'小微市场'},
        {label:'非小微市场',value:'非小微市场'},
      ],
      prop: "marketClassification",
      span: 12,
    },
    {
      label: "标准地市场景",
      type: "select",
      options: [
        {label:'住宅小区',value:'住宅小区'},
        {label:'单位宿舍楼',value:'单位宿舍楼'},
        {label:'工业园区',value:'工业园区'},
        {label:'商业楼宇',value:'商业楼宇'},
        {label:'专业市场',value:'专业市场'},
        {label:'校园',value:'校园'},
        {label:'农村-自然村',value:'农村-自然村'},
        {label:'农村-行政村',value:'农村-行政村'},
        {label:'城中村',value:'城中村'},
        {label:'乡镇',value:'乡镇'},
        {label:'市区沿街商铺',value:'市区沿街商铺'},
        {label:'乡镇沿街商铺',value:'乡镇沿街商铺'},
        {label:'住宅区沿街商铺',value:'住宅区沿街商铺'},
      ],
      prop: "cityScene",
      span: 12,
    },
    {
      label: "小区ID",
      type: "input",
      prop: "communityId",
      span: 12,
    },
    {
      label: "所属地市",
      type: "input",
      prop: "city",
      span: 12,
    },
    {
      label: "所属区县",
      type: "input",
      prop: "county",
      span: 12,
    },
    {
      label: "覆盖住户数",
      type: "input",
      mode: "number",
      prop: "residentNum",
      span: 12,
    },
    {
      label: "末梢分光器设备数",
      type: "input",
      mode: "number",
      prop: "deviceNum",
      span: 12,
    },
    {
      label: "经度",
      type: "input",
      prop: "longitude",
      span: 12,
    },
    {
      label: "纬度",
      type: "input",
      prop: "latitude",
      span: 12,
    },
    {
      label: "新建/扩容",
      type: "select",
      options: [{label:'新建',value:'新建'},{label:'扩容',value:'扩容'}],
      prop: "newOrDilatation",
      span: 12,
    },
    {
      label: "是否新增宽带覆盖小区",
      type: "select",
      options: [{label:'是',value:'是'},{label:'否',value:'否'}],
      prop: "isNewCover",
      span: 12,
    },
    {
      label: "A端站点名称",
      type: "input",
      prop: "systemNameA",
      span: 12,
    },
    {
      label: "Z端站点名称",
      type: "input",
      prop: "systemNameZ",
      span: 12,
    },
    // {
    //   label: "光缆长度",
    //   type: "input",
    //   mode: "number",
    //   prop: "opticCableLength",
    //   span: 24,
    // },
    {
      label: "配置分纤箱数",
      type: "input",
      mode: "number",
      prop: "fiberSplitterNum",
      span: 12,
    },
    {
      label: "配置分光器端口数",
      type: "input",
      mode: "number",
      prop: "splitterPortNum",
      span: 12,
    },
    {
      label: "利旧杆路/墙吊壁挂芯数",
      type: "input",
      prop: "fiberamount1",
      span: 12,
    },
    {
      label: "利旧杆路/墙吊壁挂皮长公里",
      type: "input",
      mode: 'number',
      prop: "sheathlength1",
      span: 12,
    },
    {
      label: "新建杆路敷设光缆芯数",
      type: "input",
      prop: "fiberamount2",
      span: 12,
    },
    {
      label: "新建杆路敷设光缆皮长公里",
      type: "input",
      mode: 'number',
      prop: "sheathlength2",
      span: 12,
    },
    {
      label: "管道光缆芯数",
      type: "input",
      prop: "fiberamount3",
      span: 12,
    },
    {
      label: "管道光缆皮长公里",
      type: "input",
      mode: 'number',
      prop: "sheathlength3",
      span: 12,
    },
    {
      label: "直埋光缆芯数",
      type: "input",
      prop: "fiberamount4",
      span: 12,
    },
    {
      label: "直埋光缆皮长公里",
      type: "input",
      mode: 'number',
      prop: "sheathlength4",
      span: 12,
    },
    {
      label: "光缆合计长度皮长公里",
      type: "input",
      mode: 'number',
      readonly: true,
      prop: "totalsheathlength",
      span: 24,
      class: 'emphasize',
    },
    // {
    //   label: "计划工期（天）",
    //   type: "input",
    //   mode: 'number',
    //   prop: "constructionperiod",
    //   span: 12,
    // },
    {
      label: "总投资（元）",
      type: "input",
      mode: 'number',
      readonly:true,
      prop: "totalfare",
      class: 'emphasize',
      span: 12,
    },
    {
      label: "归属综合业务区",
      type: "input",
      prop: "serviceArea",
      span: 12,
    },
    {
      label: "关联机房名称",
      type: "input",
      prop: "roomName",
      span: 12,
    },
    {
      label: "二级分纤点名称（接入光交名称）",
      type: "input",
      prop: "pointName",
      span: 12,
    },
    {
      label: "二级分纤点配线光缆可用纤芯数（接入光交上行主干剩余纤芯）",
      type: "input",
      mode: "number",
      prop: "fiberCoresNum",
      span: 12,
    },
    {
      label: "第一级分光比",
      type: "select",
      options: [
        {label:'1:2',value:'1:2'},
        {label:'1:4',value:'1:4'},
        {label:'1:8',value:'1:8'},
        {label:'1:16',value:'1:16'},
        {label:'1:32',value:'1:32'},
        {label:'1:64',value:'1:64'},
      ],
      prop: "splitterRatio1",
      span: 12,
    },
    {
      label: "配置一级分光器数量",
      type: "input",
      mode: "number",
      prop: "splitterNum1",
      span: 12,
    },
    {
      label: "第二级分光比",
      type: "select",
      options: [
        {label:'1:2',value:'1:2'},
        {label:'1:4',value:'1:4'},
        {label:'1:8',value:'1:8'},
        {label:'1:16',value:'1:16'},
        {label:'1:32',value:'1:32'},
        {label:'1:64',value:'1:64'},
      ],
      prop: "splitterRatio2",
      span: 12,
    },
    {
      label: "配置二级分光器数量",
      type: "input",
      mode: "number",
      prop: "splitterNum2",
      span: 12,
    },
    {
      label: "配置PON口数",
      type: "input",
      mode: "number",
      prop: "ponNum",
      span: 12,
    },
    {
      label: "引入光缆芯数（接入光交至分纤箱光缆芯数）",
      type: "input",
      mode: "number",
      prop: "opticalCableNum",
      span: 12,
    },
    {
      label: "用户覆盖距离(最远用户与分纤箱直线距离)",
      type: "input",
      mode: "number",
      prop: "userCoverDistance",
      span: 12,
    },
    {
      label: "链路距离（OLT机房与最近分纤箱距离）",
      type: "input",
      mode: "number",
      prop: "linkDistance",
      span: 12,
    },
  ]
};
