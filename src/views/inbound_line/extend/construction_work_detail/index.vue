<!--施工分派  -->
<template>
  <div class="construction_work_detail_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      :form="form"
      labelWidth="150px"
      @changeSelect="changeSelect"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="商机勘察、预覆盖建设明细列表">
      <div slot="headerBtn">
        <el-button type="primary" @click="download">导出</el-button>
      </div>
      <div slot="content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="商机勘察" name="survey"></el-tab-pane>
          <el-tab-pane label="预覆盖建设" name="precovering"></el-tab-pane>
        </el-tabs>
        <mssTable
          ref="table"
          :columns="tableHeader"
          :api="tableApi"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  querySurveyListService,
  queryPrecoveringListService,
  downloadService
} from "@/api/inbound_line/extend/construction_work_detail_api.js";
import { commonDown } from "@/utils/btn";
import {queryAreaListService} from "@/api/common_api";
export default {
  name: "extend_construction_work_detail_list",
  props: {
  },
  data() {
    return {
      activeName: this.$route.query.activeName || 'survey',
      formConfig: [
        {
          label: "流水号",
          type: "input",
          fieldName: "serialNumber",
        },
        {
          label: '地市',
          type: 'select',
          fieldName: 'city',
          itemAs:true,
          options: []
        },
        {
          label: '区县',
          type: 'input',
          fieldName: 'county',
        },
        {
          label: "专线类型",
          type: "input",
          fieldName: "lineType",
        },
        {
          label: "场景类型",
          type: "input",
          fieldName: "scenarioType",
        },
        {
          label: '流程状态',
          type: 'select',
          fieldName: 'entityStatus',
          itemAs:true,
          options: [
            { label: '已完成', value: '已完成' },
            { label: '流程中', value: '流程中' },
            { label: '撤销', value: '撤销' },
          ]
        },
      ],
      form: {
        city: { label: this.$route.query.city || '', value: this.$route.query.city || '' },
        entityStatus: { label: this.$route.query.entityStatus || '', value: this.$route.query.entityStatus || '' },
      },
      tableHeader: [],
      shareTableHeader: [
        {
          prop: "serialNumber",
          label: "流水号",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "city",
          label: "地市",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "customerAddress",
          label: "详细地址(十级)",
          align: "center",
          width: 300,
          tooltip:true
        },
        {
          prop: "longitude",
          label: "经度",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "latitude",
          label: "纬度",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "lineType",
          label: "专线类型",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "scenarioType",
          label: "场景类型",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "entityStatus",
          label: "工单流程状态",
          align: "center",
          width: 100,
          tooltip:true
        },
      ],
      surveyTableHeader: [
        {
          prop: "receiveTime",
          label: "esop通知时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "beginTime1",
          label: "设计院勘察设计发起时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "beginTime2",
          label: "地市项目实施经理审核发起时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "finishDate",
          label: "流程结束时间",
          align: "center",
          width: 200,
          tooltip:true
        },
      ],
      precoveringTableHeader: [
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "taskCode",
          label: "任务编码",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "taskName",
          label: "任务名称",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "receiveTime",
          label: "esop通知时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "beginTime1",
          label: "预覆盖勘察发起时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "beginTime2",
          label: "预覆盖勘察结果审核发起时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "beginTime3",
          label: "预覆盖建设及归档发起时间",
          align: "center",
          width: 200,
          tooltip:true
        },
        {
          prop: "finishDate",
          label: "流程结束时间",
          align: "center",
          width: 200,
          tooltip:true
        },
      ],
      tableApi: querySurveyListService,
      staticSearchParam:{
        city: this.$route.query.city || '',
        entityStatus: this.$route.query.entityStatus || '',
      },
    };
  },
  created(){
    this.tableHeader = [...this.shareTableHeader, ...this.surveyTableHeader]
    this.activeNameChange()
    this.getAreaList('-2', 1)
  },
  methods: {
    handleClick(tab, event) {
      this.activeNameChange()
      this.$nextTick(() => {
        this.$refs.searchForm.handleSearch()
      })
    },
    activeNameChange() {
      if (this.activeName == 'survey') {
        this.tableHeader = [...this.shareTableHeader, ...this.surveyTableHeader]
        this.tableApi = querySurveyListService
      } else {
        this.tableHeader = [...this.shareTableHeader, ...this.precoveringTableHeader]
        this.tableApi = queryPrecoveringListService
      }
    },
    getAreaList(parentId, index) {
      queryAreaListService({ parentId: parentId, typeCode: 'area', filter: "city" }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.name, key: item.id })
          })
          this.$set(this.formConfig[index], 'options', list)
        }
      })
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(JSON.stringify(param))
      obj.city=obj.city?obj.city.label:''
      obj.entityStatus=obj.entityStatus?obj.entityStatus.label:''
      this.staticSearchParam=obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj);
      })
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 导出
    download() {
      commonDown({ ...this.staticSearchParam }, downloadService);
    },
  },
};
</script>
