<!--施工分派  -->
<template>
  <div class="invest_construction_assignment_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      labelWidth="150px"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="集客专线施工分派列表">
      <div slot="headerBtn">
        <el-button type="primary" @click="download">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="tableHeader"
          :api="tableApi"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService,downloadService} from "@/api/inbound_line/extend/invest_construction_assignment_api.js";
import { commonDown } from "@/utils/btn";
export default {
  name: "extend_invest_construction_assignment_list",
  data() {
    return {
      formConfig: [
        {
          label: "工程施工任务单编号",
          type: "input",
          fieldName: "code",
        },
        {
          label: "计费编号",
          type: "input",
          fieldName: "lineNumber",
        },
        {
          label: "工单主题",
          type: "input",
          fieldName: "irmsName",
        },
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "合同名称",
          type: "input",
          fieldName: "contractName",
        },
        {
          label: "合同编码",
          type: "input",
          fieldName: "contractNo",
        },
        // {
        //   label: "供应链订单编号",
        //   type: "input",
        //   fieldName: "serialno",
        // },
        {
          label: "施工方（乙方）名称",
          type: "input",
          fieldName: "constructCompanyName",
        },
        // {
        //   label: "地市",
        //   type: "input",
        //   fieldName: "city",
        // },
        // {
        //   label: "区县",
        //   type: "input",
        //   fieldName: "county",
        // },
        // {
        //   label: "详细地址",
        //   type: "input",
        //   fieldName: "customerAddress",
        // },
        // {
        //   label: "专线类型",
        //   type: "input",
        //   fieldName: "lineType",
        // },
        // {
        //   label: "工单开始时间",
        //   type: "input",
        //   fieldName: "modifyDate",
        // },
        // {
        //   label: "客户经理",
        //   type: "input",
        //   fieldName: "managerName",
        // },
        // {
        //   label: "联系电话",
        //   type: "input",
        //   fieldName: "managerTel",
        // },
        {
          label: "处理流水号",
          type: "input",
          fieldName: "serialno",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
      ],
      tableHeader: [
        {
          prop: "code",
          label: "工程施工任务单编号",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "irmsName",
          label: "工单主题",
          align: "center",
          width:200,
          tooltip:true
        },
        {
          prop: "serialno",
          label: "处理流水号",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "lineNumber",
          label: "计费编号",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "contractName",
          label: "合同名称",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "contractNo",
          label: "合同编码",
          align: "center",
          width:180,
          tooltip:true
        },
        // {
        //   prop: "serialno",
        //   label: "供应链订单编号",
        //   align: "center",
        //   width:180,
        //   tooltip:true
        // },
        {
          prop: "constructCompanyName",
          label: "施工方（乙方）名称",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "city",
          label: "地市",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "customerAddress",
          label: "详细地址",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "lineType",
          label: "专线类型",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "modifyDate",
          label: "工单开始时间",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "managerName",
          label: "客户经理",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "managerTel",
          label: "联系电话",
          align: "center",
          width:180,
          tooltip:true
        },
        {
          prop: "creatorName",
          label: "拟稿人",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "拟稿时间",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          width:120,
          fixed:'right',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{},
      loadParam:{}
    };
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标11是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.formConfig[9], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(param)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(param)
      );
      this.$refs.table.getTableData(param);
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 导出
    download() {
      commonDown({ ...this.loadParam }, downloadService);
    },

    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/inbound_line/extend/invest_construction_assignment/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/inbound_line/extend/invest_construction_assignment/view",
            query: { boId: row.id },
          });
          break;
      }
    },
  },
};
</script>
