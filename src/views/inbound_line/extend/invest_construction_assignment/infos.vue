<!-- 施工分派公共信息（处理与查看公用） -->
<template>
  <div class="invest_construction_assignment_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListOne" class="page-anchor-point" v-if="showlineOne"></div>
    <mssCard title="集客专线信息" v-if="showlineOne">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :form="lineForm"
          :isChange="true"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListTwo" class="page-anchor-point" v-if="showlineTwo"></div>
    <mssCard title="集客专线信息" v-if="showlineTwo">
      <div slot="content">
        <mssForm
          ref="lineTwoForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :form="lineTwoForm"
          :isChange="true"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :dealPage="dealPage"
      :fileTypeFlag="true"
    ></mssAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
        v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      dialog-width="70%"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      @confirm="projectConfirm"
      :dialogTitle="dialogTitle"
      :tableSingleChoice="true"
      :rowKey="rowKey"
      :tableQueryParams="tableQueryParams"
    ></mssTableSearchDialog>
  </div>
</template>

<script>
import {
  queryDetailService,
  getProviderListService,
  getContracListService,
} from "@/api/inbound_line/extend/invest_construction_assignment_api.js";
import Data from "../formSetting";
export default {
  name: "extend_invest_construction_assignment_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      disableForm: false,
      dealPage:false,
      projectTableApi: getContracListService,
      searchFieldList: [],
      projectColumns: [],
      dialogTitle: "",
      rowKey:'',
      tableQueryParams:{},
      basicConfig: [
        {
          label: "工程施工任务单编号",
          type: "input",
          prop: "code",
          span: 24,
        },
        {
          label: "施工方(乙方)名称",
          type: "input",
          prop: "constructCompanyName",
          span: 12,
          readonly:true,
          eventListeners: {
            focus: this.openProviderDialog, // 打开选择项目的弹窗
          },
        },
        {
          label: "建设方(甲方)经办部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "施工方(乙方)联系人",
          type: "input",
          prop: "constructCompanyContact",
          span: 12,
        },
        {
          label: "建设方(甲方)联系人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "施工方(乙方)联系方式",
          type: "input",
          prop: "constructCompanyContactWay",
          span: 12,
        },
        {
          label: "建设方(甲方)联系方式",
          type: "input",
          prop: "createContactWay",
          span: 12,
        },
        {
          label: "法定地址(乙方)",
          type: "input",
          prop: "legalAddress",
          span: 12,
        },
        {
          label: "法定代表人(乙方)",
          type: "input",
          prop: "representative",
          span: 12,
        },
        {
          label: "施工合同编号",
          type: "input",
          prop: "contractNo",
          span: 24,
        },
        {
          label: "施工合同名称",
          type: "input",
          prop: "contractName",
          span: 24,
          readonly:true,
          eventListeners: {
            focus: this.openContractDialog, // 打开选择项目的弹窗
          },
        },
        {
          label: "工程项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
        },
        {
          label: "工程地点",
          type: "input",
          prop: "projectSite",
          span: 12,
        },
        {
          label: "现场代表（甲方）",
          type: "input",
          prop: "partyADeputy",
          span: 12,
        },
        {
          label: "现场代表（乙方）",
          type: "input",
          prop: "partyBDeputy",
          span: 12,
        },
        {
          label: "工程项目其他情况",
          type: "input",
          mode: "textarea",
          prop: "otherSituation",
          span: 24,
        },
        {
          label: "预算总价",
          type: "input",
          mode:'number',
          prop: "budgetTotal",
          span: 12,
        },
        {
          label: "施工折扣系数",
          type: "input",
          mode:'number',
          prop: "discountFactor",
          span: 12,
        },
        {
          label: "安全生产费金额",
          type: "input",
          mode:'number',
          prop: "productionBudget",
          span: 12,
        },
        {
          label: "要求施工人员数量",
          type: "input",
          mode: "number",
          prop: "constructUserNum",
          span: 12,
        },
        {
          label: "工期要求（甲方）",
          type: "datePicker",
          daterange: true,
          format: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          prop: "durationPlan",
          span: 12,
        },

        {
          label: "特别约定",
          type: "input",
          mode: "textarea",
          prop: "special",
          span: 24,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig: JSON.parse(JSON.stringify(Data.lineSet)),
      lineForm: {},
      lineTwoForm: {},
      showlineOne: false,
      showlineTwo: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
        if (this.basicConfig[i].prop == 'durationPlan')
          this.basicConfig[i].type = 'input'
      }
      for (let i in this.lineConfig) {
        this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
      }
      this.disableForm = true;
    } else {
      for (let i in this.basicConfig) {
        if (["0", "1", "2", "3", "4", "5", "6", "9", "11"].includes(i)) {
          this.basicConfig[i].disabled = true;
        }
      }
      for (let i in this.lineConfig) {
        for (let i in this.lineConfig) {
          this.lineConfig[i].disabled = true;
          if(this.lineConfig[i].label=='专线接入模型'){
            this.lineConfig[i].prop="accessModelId"
            this.lineConfig[i].disabled = false;
          }
          if(this.lineConfig[i].label=='满足程度'){
            this.lineConfig[i].prop="satisfactionId"
            this.lineConfig[i].disabled = false;
          }
        }
      }
      this.dealPage=true
    }
    // 浏览器传参
    this.boId = this.$route.query.boId
    this.$set(this.lineConfig[23], 'options', await this.$dictOptions({parentValue: "AccessModel",appCode: "001001"}))
    this.$set(this.lineConfig[24], 'options', await this.$dictOptions({parentValue: "Satisfaction",appCode: "001001"}))
    this.getDetailsData()
  },
  methods: {
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.businessType=res.data.businessType
        this.$emit("getWorkCode",this.businessType)
        this.basicForm.durationPlan = ( res.data.durationPlanBeginDate || res.data.durationPlanEndDate)?[
          res.data.durationPlanBeginDate || "",
          res.data.durationPlanEndDate || "",
        ]:[];
        if(this.labelPosition == "left")this.basicForm.durationPlan=this.basicForm.durationPlan.join('~')
        if (res.data.detailOther) {
          this.lineTwoForm = {
            ...res.data.detailOther,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
          this.pageAnchorConfig.splice(1, 0, {
            text: "Z端<br>信息",
            id: "sectionListTwo",
          });
          this.showlineTwo = true;
        }
        if (res.data.detail) {
          this.lineForm = {
            ...res.data.detail,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
          this.pageAnchorConfig.splice(1, 0, {
            text: "A端<br>信息",
            id: "sectionListOne",
          });
          this.showlineOne = true;
        }
      });
    },

    // 弹框合同
    openContractDialog() {
      this.projectTableApi = getContracListService;
      this.searchFieldList = [
        {
          label: "合同名称",
          fieldName: "contractName",
        },
        {
          label: "合同编号",
          fieldName: "contractCode",
        },
      ];
      this.projectColumns = [
        {
          label: "合同名称",
          prop: "contractName",
          tooltip:true,
        },
        {
          label: "合同编号",
          prop: "contractCode",
          tooltip:true,
        },
        {
          label: "合同供应商名称",
          prop: "providername",
          tooltip:true,
        },
        {
          label: "合同供应商编号",
          prop: "providerNum",
          tooltip:true,
        },
        {
          label: "合同金额",
          prop: "totalInvest",
          tooltip:true,
        },
      ];
      if(this.basicForm.provideNumber){
        this.tableQueryParams={
          providerNum:this.basicForm.provideNumber
        }
        this.dialogTitle = "选择合同";
        this.rowKey="contractId"
        this.$refs.projectNameSearchDialog.openDialog();
        this.type = "contract";
      }else{
        this.$message.warning('请先选择施工方(乙方)名称')
      }

    },

    // 合作单位
    openProviderDialog() {
      this.projectTableApi = getProviderListService;
      this.searchFieldList = [
        {
          label: "合作单位名称",
          fieldName: "providerName",
        },
        {
          label: "合作单位编码",
          fieldName: "providerCode",
        },
      ];
      this.projectColumns = [
        {
          label: "合作单位名称",
          prop: "providerName",
        },
        {
          label: "合作单位编码",
          prop: "providerCode",
        },
        {
          label: "联系人",
          prop: "userName",
        },
        {
          label: "联系电话",
          prop: "telephone",
        },
      ];
      this.dialogTitle = "选择合作单位";

      this.tableQueryParams={
        providerType:'3001'
      }
      this.rowKey="providerId"
      this.$nextTick(()=>{
        this.$refs.projectNameSearchDialog.openDialog();
      })
      this.type = "provider";
    },

    projectConfirm(data) {
      const sgId=this.$refs.basicForm.modelForm.constructCompanyId||""
      if (data && data[0]){
        if(this.type=='provider'){
          this.basicForm=JSON.parse(JSON.stringify({
            ...this.$refs.basicForm.modelForm,
            constructCompanyContact:data[0].userName||'',
            constructCompanyContactWay:data[0].telephone||'',
            constructCompanyId:data[0].providerId||'',
            constructCompanyName:data[0].providerName||'',
            legalAddress:data[0].address||'',
            representative:data[0].legalUser||'',
            partyBDeputy:data[0].userName||'',
            provideNumber:data[0].providerCode||'',
          }))
          if(sgId!=data[0].providerId){
            // 清空合同，重新选合同
            this.basicForm.contractId=''
            this.basicForm.contractName=''
            this.basicForm.contractNo=''
            this.basicForm.orgName=''
            this.basicForm.usableInvest=''
          }
        }else{
          this.basicForm={
            ...this.$refs.basicForm.modelForm,
            contractId:data[0].contractId||'',
            contractName:data[0].contractName||'',
            contractNo:data[0].contractCode||'',
            orgName:data[0].orgName||'',
            usableInvest:data[0].usableInvest||'',
          }
        }
      }

    },
  },
};
</script>

<style lang="scss" scoped>
</style>
