<!--施工分派  -->
<template>
  <div class="work_order_count_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      labelWidth="150px"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <div style="color:#f56c6c;"> *统计数据范围为2024年专线新流程二编系统推送工单数据</div>
    <mssCard title="工单建设统计报表">
      <div slot="headerBtn">
        <el-button type="primary" @click="download">导出</el-button>
      </div>
      <div slot="content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="已开通集客专线建设情况" name="completed"></el-tab-pane>
          <el-tab-pane label="在途集客专线建设情况" name="process"></el-tab-pane>
        </el-tabs>
        <el-table
          v-show="activeName == 'completed'"
          ref="completedTable"
          :data="completedTableData"
          border
          style="width: 100%">
          <el-table-column
            header-align="center"
            align="center"
            prop="city"
            min-width="100"
            label="地市">
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            prop="orderNum"
            min-width="120"
            label="工单数量">
            <template v-slot="scope">
              <el-button type="text" @click="orderNumClick('default', '已完成', scope.row)">{{scope.row.orderNum}}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            prop="averageDuration"
            min-width="120"
            label="建设平均时长（受理-完工）">
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            prop="timeoutProportion"
            min-width="120"
            label="超时工单占比">
            <template v-slot="scope">
              <el-button type="text" :style="scope.row.timeoutProportion != '0.0%' ? 'color: red':'color: black'" @click="timeoutProportionClick('default', '已完成', scope.row)">{{scope.row.timeoutProportion}}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="成本">
            <el-table-column
              header-align="center"
              align="center"
              prop="orderNum1"
              min-width="120"
              label="工单数量">
              <template v-slot="scope">
                <el-button type="text" @click="orderNumClick('default', '已完成', scope.row)">{{scope.row.orderNum1}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="averageDuration1"
              min-width="120"
              label="建设平均时长（受理-完工）">
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="timeoutProportion1"
              min-width="120"
              label="超2天工单占比">
              <template v-slot="scope">
                <el-button type="text" :style="scope.row.timeoutProportion1 != '0.0%' ? 'color: red':'color: black'" @click="timeoutProportionClick('default', '已完成', scope.row)">{{scope.row.timeoutProportion1}}</el-button>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="紧急">
            <el-table-column
              header-align="center"
              align="center"
              prop="orderNum2"
              min-width="120"
              label="工单数量">
              <template v-slot="scope">
                <el-button type="text" @click="orderNumClick('investUrgent', '已完成', scope.row)">{{scope.row.orderNum2}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="averageDuration2"
              min-width="120"
              label="建设平均时长（受理-完工）">
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="timeoutProportion2"
              min-width="120"
              label="超2天工单占比">
              <template v-slot="scope">
                <el-button type="text" :style="scope.row.timeoutProportion2 != '0.0%' ? 'color: red':'color: black'" @click="timeoutProportionClick('investUrgent', '已完成', scope.row)">{{scope.row.timeoutProportion2}}</el-button>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="非紧急">
            <el-table-column
              header-align="center"
              align="center"
              prop="noUrgentOrderNum"
              min-width="120"
              label="工单数量">
              <template v-slot="scope">
                <el-button type="text" @click="orderNumClick('invest', '已完成', scope.row)">{{scope.row.noUrgentOrderNum}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="noUrgentAverageDuration"
              min-width="120"
              label="建设平均时长（受理-完工）">
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="noUrgentTimeoutProportion"
              min-width="120"
              label="超3天工单占比">
              <template v-slot="scope">
                <el-button type="text" :style="scope.row.noUrgentTimeoutProportion != '0.0%' ? 'color: red':'color: black'" @click="timeoutProportionClick('invest', '已完成', scope.row)">{{scope.row.noUrgentTimeoutProportion}}</el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>

        <el-table
          v-show="activeName == 'process'"
          ref="processTable"
          :data="processTableData"
          border
          style="width: 100%">
          <el-table-column
            header-align="center"
            align="center"
            prop="city"
            min-width="120"
            label="地市">
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="成本">
            <el-table-column
              header-align="center"
              align="center"
              prop="orderNum1"
              min-width="140"
              label="工单数量">
              <template v-slot="scope">
                <el-button type="text" @click="orderNumClick('default', '流程中', scope.row)">{{scope.row.orderNum1}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="overtimeOrderNum1"
              min-width="140"
              label="在途建设超2天工单数量（受理-完工）">
              <template v-slot="scope">
                <el-button type="text" :style="scope.row.overtimeOrderNum1 ? 'color: red':'color: black'" @click="timeoutProportionClick('default', '流程中', scope.row)">{{scope.row.overtimeOrderNum1}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
                header-align="center"
                align="center"
                prop="notFinishOrderNum1"
                min-width="140"
                label="已完成建设但未归档的数量">
              <template v-slot="scope">
                <el-button type="text" @click="notFinishOrderNumClick('default', '流程中', scope.row)">{{scope.row.notFinishOrderNum1}}</el-button>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="紧急">
            <el-table-column
              header-align="center"
              align="center"
              prop="orderNum2"
              min-width="140"
              label="工单数量">
              <template v-slot="scope">
                <el-button type="text" @click="orderNumClick('investUrgent', '流程中', scope.row)">{{scope.row.orderNum2}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="overtimeOrderNum2"
              min-width="140"
              label="在途建设超2天工单数量（受理-完工）">
              <template v-slot="scope">
                <el-button type="text" :style="scope.row.overtimeOrderNum2 ? 'color: red':'color: black'" @click="timeoutProportionClick('investUrgent', '流程中', scope.row)">{{scope.row.overtimeOrderNum2}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
                header-align="center"
                align="center"
                prop="notFinishOrderNum2"
                min-width="140"
                label="已完成建设但未归档的数量">
              <template v-slot="scope">
                <el-button type="text" @click="notFinishOrderNumClick('investUrgent', '流程中', scope.row)">{{scope.row.notFinishOrderNum2}}</el-button>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            header-align="center"
            align="center"
            label="非紧急">
            <el-table-column
              header-align="center"
              align="center"
              prop="noUrgentOrderNum"
              min-width="140"
              label="工单数量">
              <template v-slot="scope">
                <el-button type="text" @click="orderNumClick('invest', '流程中', scope.row)">{{scope.row.noUrgentOrderNum}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              prop="noUrgentOvertimeOrderNum"
              min-width="140"
              label="在途建设超3天工单数量（受理-完工）">
              <template v-slot="scope">
                <el-button type="text" :style="scope.row.noUrgentOvertimeOrderNum ? 'color: red':'color: black'" @click="timeoutProportionClick('invest', '流程中', scope.row)">{{scope.row.noUrgentOvertimeOrderNum}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
                header-align="center"
                align="center"
                prop="noUrgentNotFinishOrderNum"
                min-width="140"
                label="已完成建设但未归档的数量">
              <template v-slot="scope">
                <el-button type="text" @click="notFinishOrderNumClick('invest', '流程中', scope.row)">{{scope.row.noUrgentNotFinishOrderNum}}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              label="其中：在途建设超时工单">
              <el-table-column
                header-align="center"
                align="center"
                prop="noUrgentOrderNum1"
                min-width="140"
                label="未完成设计批复及派工工单数量">
                <template v-slot="scope">
                  <el-button type="text" @click="noUrgentOrderNum('invest', '流程中', '0', scope.row)">{{scope.row.noUrgentOrderNum1}}</el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="noUrgentOvertimeOrderNum1"
                min-width="140"
                label="超1天工单数量">
                <template v-slot="scope">
                  <el-button type="text"  :style="scope.row.noUrgentOvertimeOrderNum1 ? 'color: red':'color: black'" @click="noUrgentOvertimeClick('invest', '流程中', '0', scope.row)">{{scope.row.noUrgentOvertimeOrderNum1}}</el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="noUrgentOrderNum2"
                min-width="140"
                label="未完成施工工单数量">
                <template v-slot="scope">
                  <el-button type="text" @click="noUrgentOrderNum('invest', '流程中', '1', scope.row)">{{scope.row.noUrgentOrderNum2}}</el-button>
                </template>
              </el-table-column>
              <el-table-column
                header-align="center"
                align="center"
                prop="noUrgentOvertimeOrderNum2"
                min-width="140"
                label="在超2天工单数量">
                <template v-slot="scope">
                  <el-button type="text" :style="scope.row.noUrgentOvertimeOrderNum2 ? 'color: red':'color: black'" @click="noUrgentOvertimeClick('invest', '流程中', '1', scope.row)">{{scope.row.noUrgentOvertimeOrderNum2}}</el-button>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  queryListService,
  downloadService
} from "@/api/inbound_line/extend/work_order_count_api.js";
import {commonDown} from "@/utils/btn";

export default {
  name: "extend_work_order_count_list",
  data() {
    return {
      activeName: 'completed',
      formConfig: [
        {
          label: '统计时间',
          type: 'date2',
          fieldName: 'startDate',
          fieldName2: 'endDate'
        },
        {
          label: '是否项目类',
          type: 'select',
          fieldName: 'isProjectline',
          itemAs:true,
          options: [
            { label: '是', value: '是' },
            { label: '否', value: '否' },
          ]
        }
      ],
      completedTableData: [],
      processTableData: [],
      staticSearchParam: {},
      loadParam: {}
    };
  },
  created() {
    // this.getList({});
  },
  methods: {
    handleClick(tab, event) {
    },
    getList(obj) {
      queryListService(obj).then(res => {
        this.completedTableData = [...res.data.groupWorkOrderCountCompletedList];
        this.processTableData = [...res.data.groupWorkOrderCountProcessList];
      })
    },
    // 查询
    search(param) {
      let obj = JSON.parse(JSON.stringify(param))
      obj.isProjectline=obj.isProjectline?obj.isProjectline.value:''
      this.staticSearchParam = obj
      this.loadParam = obj
      this.$nextTick(() => {
        this.getList(obj);
      })
    },
    orderNumClick(type, status, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: status,
        },
      });
    },
    timeoutProportionClick(type, status, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: status,
          isTimeout: "是",
        },
      });
    },
    noUrgentOrderNum(type, status, complete, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: status,
          isComplete: complete,
        },
      });
    },
    noUrgentOvertimeClick(type, status, complete, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: status,
          isComplete: complete,
          isCompleteTimeout: '1',
        },
      });
    },
    notFinishOrderNumClick(type, status, row) {
      this.$router.push({
        path: "/inbound_line/extend/work_order_detail",
        query: {
          ...this.staticSearchParam,
          activeName: type,
          city: row.city,
          entityStatus: status,
          isCount: '1',
          isCompleteTimeout: '1',
        },
      });
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 导出
    download() {
      commonDown({...this.loadParam}, downloadService);
    },
  },
};
</script>
