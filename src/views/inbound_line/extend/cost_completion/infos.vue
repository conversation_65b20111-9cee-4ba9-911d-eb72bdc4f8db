<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="cost_completion_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListOne" class="page-anchor-point" v-if="showlineOne"></div>
    <mssCard title="集客专线信息" v-if="showlineOne">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :disableForm="disableForm1"
          :form="lineForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListTwo" class="page-anchor-point" v-if="showlineTwo"></div>
    <mssCard title="集客专线信息" v-if="showlineTwo">
      <div slot="content">
        <mssForm
          ref="lineTwoForm"
          labelWidth="200px"
          :config="lineConfig"
          :labelPosition="labelPosition"
          :form="lineTwoForm"
          :disableForm="disableForm1"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <ImgAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :fileTypeFlag="true"
      :dealPage="dealPage"
    ></ImgAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory v-if="businessType" :boId="boId" :workflowCode="businessType"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <mssTableSearchDialog1
      ref="projectNameSearchDialog1"
      :dialog-width="dialogWidth"
      :autoCall="false"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      @confirm="projectConfirm"
      :dialogTitle="dialogTitle"
      :tableSingleChoice="true"
      :rowKey="rowKey"
      :tableQueryParams="tableQueryParams"
    ></mssTableSearchDialog1>
  </div>
</template>

<script>
import {
  queryDetailService,
} from "@/api/inbound_line/extend/cost_completion_api.js";
import Data from '../formSetting'
import ImgAttachment from "../img_attachment/index.vue";
import {getDeviceType, queryZzDeviceResource} from "@/api/inbound_line/extend/zz_resource_api";

export default {
  name: "extend_cost_completion_info",
  components: {
    ImgAttachment
  },
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
    nodeCode: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      dealPage: false,
      disableForm: false,
      disableForm1: true,
      basicConfig: [
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "施工任务单编码",
          type: "input",
          prop: "groupConsTaskOrderCode",
          span: 24,
        },
        {
          label: "施工任务单名称",
          type: "input",
          prop: "groupConsTaskOrderName",
          span: 24,
        },
        {
          label: "发起时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "完工时间",
          type: "datePicker",
          prop: "completeDate",
          span: 12,
        },
        {
          label: "勘查光缆长度皮长公里",
          type: "input",
          mode: 'number',
          prop: "fillLength",
          span: 12,
          eventListeners: {
            change: (value) => {
              this.$set(this.$refs.basicForm.modelForm, 'fillLength', this.dealNum(value))
            }
          }
        },
        {
          label: "实际光缆长度皮长公里",
          type: "input",
          mode: 'number',
          prop: "realLength",
          span: 12,
          eventListeners: {
            change: (value) => {
              this.$set(this.$refs.basicForm.modelForm, 'realLength', this.dealNum(value))
            }
          }
        },
        {
          label: "光缆差异长度皮长公里",
          type: "input",
          mode: 'number',
          prop: "diffLength",
          span: 12,
          eventListeners: {
            change: (value) => {
              this.$set(this.$refs.basicForm.modelForm, 'diffLength', this.dealNum(value))
            }
          }
        },
        {
          label: "差异比例",
          type: "input",
          prop: "diffRate",
          span: 12,
        },
        {
          label: "拟稿单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "拟稿部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig: JSON.parse(JSON.stringify(Data.lineSet)),
      lineForm: {},
      lineTwoForm: {},
      showlineOne: false,
      showlineTwo: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
      city: '',
      county: '',
      deviceTypeList: [],
      projectTableApi: queryZzDeviceResource,
      searchFieldList: [],
      projectColumns: [],
      dialogTitle: "",
      rowKey: "",
      tableSingleChoice: true,
      tableQueryParams: {},
    };
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        return '96%'
      } else {
        return '70%'
      }
    }
  },
  async created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
      for (let i in this.lineConfig) {
        this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
      }
      this.disableForm = true;
    } else {
      for (let i in this.basicConfig) {
        if (['0', '1', '2', '3', '4', '6', '8', '9', '10', '11', '12', '13'].includes(i)) {
          this.basicConfig[i].disabled = true;
        }
      }
      for (let i in this.lineConfig) {
        this.lineConfig[i].disabled = true;
        this.nodeCodeChange();
      }
      this.dealPage = true
    }
    // 浏览器传参
    this.boId = this.$route.query.boId
    this.$set(this.lineConfig[23], 'options', await this.$dictOptions({parentValue: "AccessModel", appCode: "001001"}))
    this.$set(this.lineConfig[24], 'options', await this.$dictOptions({parentValue: "Satisfaction", appCode: "001001"}))
    this.getDetailsData()
  },
  watch: {
    nodeCode: function (val) {
      this.nodeCodeChange()
    },
  },
  methods: {
    dealNum(value) {
      let newval = ('' + value) // 第一步：转成字符串
        .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
        .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
        .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
        .match(/^\d*(\.?\d{0,3})/g)[0] || ''
      return newval
    },
    getDetailsData() {
      queryDetailService({boId: this.boId}).then((res) => {
        this.basicForm = res.data;
        this.businessType = res.data.businessType
        this.$emit("getWorkCode", this.businessType)
        if (res.data.detailOther) {
          this.city = res.data.detailOther.city;
          this.county = res.data.detailOther.county;
          this.lineTwoForm = {
            ...res.data.detailOther,
            outsideNum: res.data.outsideNum,
            insideNum: res.data.insideNum
          };
          this.pageAnchorConfig.splice(1, 0, {
            text: "Z端<br>信息",
            id: "sectionListTwo",
          });
          this.showlineTwo = true;
        }
        if (res.data.detail) {
          this.city = res.data.detail.city;
          this.county = res.data.detail.county;
          this.lineForm = {
            ...res.data.detail,
            outsideNum: res.data.outsideNum,
            insideNum: res.data.insideNum
          };
          this.pageAnchorConfig.splice(1, 0, {
            text: "A端<br>信息",
            id: "sectionListOne",
          });
          this.showlineOne = true;
        }
        this.nodeCodeChange()
      });
    },
    nodeCodeChange() {
      if (this.nodeCode == "consUnit") {
        this.lineConfig[39].disabled = false;
        this.lineConfig[40].disabled = false;
        this.lineConfig[51].disabled = false;
        this.lineConfig[39].eventListeners = {
          focus: () => this.openZzDeviceResource('zzDeviceResource'), // 打开选择项目的弹窗
        };
        this.lineConfig[40].eventListeners = {
          focus: () => this.openZzDeviceResource('zzDeviceResource1'), // 打开选择项目的弹窗
        };
        this.disableForm1 = false;
        this.getDeviceTypeList();
      }
    },
    getDeviceTypeList() {
      getDeviceType().then((res) => {
        this.deviceTypeList = res.data;
      });
    },
    openZzDeviceResource(type) {
      console.log(type)
      this.projectTableApi = queryZzDeviceResource;
      this.searchFieldList = [
        {
          label: "设备类型",
          type: "select",
          fieldName: "deviceType",
          options: this.deviceTypeList,
          rules: [
            {
              required: true,
              message: "请选择设备类型",
              trigger: "blur",
            },
          ],
        },
        {
          label: "设备名称",
          type: "input",
          fieldName: "deviceName",
        },
      ];
      this.projectColumns = [
        {
          label: "地市",
          prop: "city",
          minWidth: 100,
        },
        {
          label: "区县",
          prop: "county",
          minWidth: 100,
        },
        {
          label: "设备名称",
          prop: "deviceName",
          minWidth: 150,
        },
        {
          label: "设备ID",
          prop: "deviceId",
          minWidth: 100,
        },
        {
          label: "设备UUID",
          prop: "deviceUuid",
          minWidth: 100,
        },
        {
          label: "设备型号",
          prop: "deviceModel",
          minWidth: 100,
        },
        {
          label: "所属接入点名称",
          prop: "accessName",
          minWidth: 150,
        },
        {
          label: "所属接入点类型",
          prop: "accessType",
          formatter: function (row) {
            let val;
            if (row.accessType == 1) {
              val = "分纤箱"
            } else if (row.accessType == 2) {
              val = "光交箱"
            } else {
              val = row.accessType
            }
            return val
          },
          minWidth: 150,
        },
        {
          label: "所属站点名称",
          prop: "siteName",
          minWidth: 150,
        },
        {
          label: "所属机房名称",
          prop: "roomName",
          minWidth: 150,
        },
        {
          label: "所属机房ID",
          prop: "roomId",
          minWidth: 100,
        },
      ];
      this.tableQueryParams = {
        city: this.city,
        county: this.county
      }
      this.dialogTitle = "选择设备";
      this.rowKey = "projectId"
      this.tableSingleChoice = true;
      this.$refs.projectNameSearchDialog1.openDialog();
      this.type = type;
    },
    projectConfirm(data) {
      if (data && data[0]) {
        if (this.type == "zzDeviceResource") {
          if (this.showlineOne) {
            this.$refs.lineForm.modelForm = {
              ...this.$refs.lineForm.modelForm,
              accessDeviceName: data[0].deviceName,
              accessDeviceId: data[0].deviceId,
              ismodifyresource: 1,
            };
          } else if (this.showlineTwo) {
            this.$refs.lineTwoForm.modelForm = {
              ...this.$refs.lineTwoForm.modelForm,
              accessDeviceName: data[0].deviceName,
              accessDeviceId: data[0].deviceId,
              ismodifyresource: 1,
            };
          }
        } else if (this.type == "zzDeviceResource1") {
          if (this.showlineOne) {
            this.$refs.lineForm.modelForm = {
              ...this.$refs.lineForm.modelForm,
              accessDeviceName1: data[0].deviceName,
              accessDeviceId1: data[0].deviceId,
              ismodifyresource: 1,
            };
          } else if (this.showlineTwo) {
            this.$refs.lineTwoForm.modelForm = {
              ...this.$refs.lineTwoForm.modelForm,
              accessDeviceName1:data[0].deviceName,
              accessDeviceId1: data[0].deviceId,
              ismodifyresource: 1,
            }
          }
        }
      }
    }
  },
};
</script>

<style lang="scss" scoped>
</style>
