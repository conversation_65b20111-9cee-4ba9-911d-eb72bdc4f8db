<!-- 归类池 查看页面 -->
<template>
  <div class="sorting_tank_look page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <infos labelPosition="left"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService} from "@/api/inbound_line/sorting_tank_api.js"
export default {
  name: 'sorting_tank_look',
  components:{infos},
  data() {
    return {

    }
  },
  created() {
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/sorting_tank'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
