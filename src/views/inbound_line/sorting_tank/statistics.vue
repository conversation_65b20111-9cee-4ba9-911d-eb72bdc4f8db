<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-14 19:46:42
 * @Description: 集客专线归类情况统计表
-->

<template>
<div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
      :labelWidth="'150px'"
      @changeSelect="changeSelect"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          border
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { findArchiveReportListService } from "@/api/inbound_line/sorting_tank_api"
import { queryAreaListService } from "@/api/common_api"
export default {
  name: 'sortingTankStatistics',
  data() {
    return {
      searchConfig: [
        {
          label: '地市',
          type: 'select',
          fieldName: 'city',
          itemAs:true,
          options: []
        },
        {
          label: '区县',
          type: 'select',
          fieldName: 'county',
          itemAs:true,
          options: []
        },
        {
          label: '计费编号',
          type: 'input',
          fieldName: 'linenumber'
        },
        {
          label: '专线名称',
          type: 'input',
          fieldName: 'linename'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '设计勘察单名称',
          type: 'input',
          fieldName: 'submitName'
        },
        {
          label: '是否归类',
          type: 'select',
          options: [{label:'是',value:'1'},{label:'否',value:'0'}],
          fieldName: 'isArchive'
        },

      ],
      api: findArchiveReportListService,
      columns: [
        {
          label: '是否归类',
          prop: 'isArchive',
        },
        {
          label: '计费编号',
          prop: 'linenumber',
          minWidth: 150,
          tooltip:true
        },
        {
          label: '地市',
          prop: 'city'
        },
        {
          label: '区县',
          prop: 'county'
        },
        {
          label: '专线名称',
          prop: 'linename',
          minWidth: 200,
          tooltip:true
        },
        {
          label: '设计勘察单名称',
          prop: 'submitName',
          minWidth: 200,
          tooltip:true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 200,
          tooltip:true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 150,
          tooltip:true
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 150,
          tooltip:true
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 200,
          tooltip:true
        }
      ],
      staticSearchParam:{}
    }
  },
  created(){
    this.getAreaList('-2', 0)
  },
  methods: {
    getAreaList(parentId, index) {
        queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
            if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
                list.push({ label: item.name, value: item.id })
            })
            this.$set(this.searchConfig[index], 'options', list)
            }
        })
    },
    changeSelect(name, val){
      if(name=='city'){
        this.getAreaList(val.value, 1)
        this.$set(this.$refs.searchForm.searchForm,'county','')
      }
    },
    search() {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      obj.city=obj.city?obj.city.label:''
      obj.county=obj.county?obj.county.label:''
      this.staticSearchParam=obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj)
      })
    },
    reset() {
      this.search()
    }
  }
}
</script>

<style scoped>

</style>
