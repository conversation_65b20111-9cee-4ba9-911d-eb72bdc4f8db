<!-- 归类池 -->
<template>
  <div class="sorting_tank_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="集客专线归类池列表">
      <div slot="headerBtn">
        <el-button type="primary" @click="add">新增</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :staticSearchParam="staticSearchParam"
          :columns="tableHeader"
          :api="tableApi"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService ,delService} from "@/api/inbound_line/sorting_tank_api.js";
import { commonOneDel } from "@/utils/btn";
export default {
  name: "sorting_tank_list",
  data() {
    return {
      formConfig: [
        {
          label: "集客专线名称",
          type: "input",
          fieldName: "lineName",
        },
        {
          label: "计费编码",
          type: "input",
          fieldName: "lineNumber",
        },
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "处理流水号",
          type: "input",
          fieldName: "serialno",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
      ],
      tableHeader: [
        {
          prop: "lineName",
          label: "集客专线名称",
          align: "center",
          width:130,
          tooltip:true
        },
        {
          prop: "linenumber",
          label: "计费编码",
          align: "center",
          width:130,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          minwidth:170,
          tooltip:true
        },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          minwidth:120,
          tooltip:true
        },
        {
          prop: "serialno",
          label: "处理流水号",
          align: "center",
          width:160,
          tooltip: true,
        },
        {
          prop: "creatorName",
          label: "拟稿人",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "拟稿时间",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          width: 100,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          fixed:'right',
          width: 120,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
                {row.isAllowDelete ? (
                  <span
                    class="table_btn "
                    onClick={() => {this.action(row, "del")}}
                  >
                    删除
                  </span>
                ) : (
                  ""
                )}
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{}
    };
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标5是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.formConfig[5], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(param)
      )
      this.$refs.table.getTableData(param);
    },

    // 重置
    reset(param) {
      this.search(param);
    },

    // 新增
    add(){
      this.$router.push({
        path: "/inbound_line/sorting_tank/edit",
        query: { type:'add' },
      });
    },
     // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/inbound_line/sorting_tank/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/inbound_line/sorting_tank/view",
            query: { boId: row.id },
          });
          break;
        case "del":
          commonOneDel.call(this,row.id, delService, (res) => {
            if(res.code=='0000'){
              this.$message.success('删除成功')
              this.search()
            }
          })
          break;
      }
    },
  },
};
</script>
