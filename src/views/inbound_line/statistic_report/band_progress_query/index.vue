<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-28 14:14:39
 * @Description: 宽带进度统计报表
-->

<template>
	<div class="response_progress">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="searchForm"
			@search="search"
			@reset="reset"
      @changeSelect="changeSelect"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
				<el-button type="primary" @click="exportDetailMethod">导出明细</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	findBroadbandProcessListService,
	exportBroadbandProcessListService,
	exportBroadbandProcessDetailService
} from '@/api/inbound_line/statistical_report'
import { queryAreaListService } from '@/api/common_api.js'
import { commonDown } from "@/utils/btn";
export default {
	name: 'band_progress_query',
	data() {
		return {
			searchConfig: [
      {
					type: 'input',
					fieldName: 'projectName',
					label: '项目名称',
          span: 8
				},
				{
					type: 'input',
					fieldName: 'projectCode',
					label: '项目编码',
          span: 8
				},
        {
					type: 'date1',
					fieldName: 'planYear',
          dateType: 'year',
					label: '立项年份',
          valueFormat: 'yyyy',
          format: 'yyyy',
          span: 8
				},
        {
          type: 'select',
					fieldName: 'citys',
					label: '建设单位',
          selectMultiple: true,
					itemAs:true,
          span: 8
        },
				{
					type: 'select',
					fieldName: 'countys',
					label: '区县',
          selectMultiple: true,
					itemValue: 'label',
          span: 8
				},
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findBroadbandProcessListService,
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "projectName",
						label: "项目名称",
						minWidth: 140,
            tooltip: true
					},
          {
            prop: "projectCode",
            label: "项目编码",
            minWidth: 140,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row)}}>{row.projectCode}</span>
							)
						}
          },
					{
						prop: "city",
						label: "建设单位",
						minWidth: 120
					},
          {
						prop: "taskNum",
						label: "任务总数",
						minWidth: 120
					},
          {
						prop: "3",
						label: "CPMS系统任务点",
						multilevelColumn: [
							{
								prop: "cpmsReportNum",
								label: "任务点开工数量",
								minWidth: 160
							},
							{
								prop: "cpmsCompNum",
								label: "任务点完工数量",
								minWidth: 160
							},{
								prop: "cpmsTestNum",
								label: "验收测试数量",
								minWidth: 160
							},{
								prop: "cpmsLineNum",
								label: "割接上线数量",
								minWidth: 160
							},{
								prop: "cpmsDeliverNum",
								label: "割接交维数量",
								minWidth: 160
							},{
								prop: "cpmsCheckNum",
								label: "竣工验收数量",
								minWidth: 160
							},
						]
					},
          {
						prop: "",
						label: "家客综资系统任务点",
						multilevelColumn: [
							{
								prop: "netNum",
								label: "综资录入数量",
								minWidth: 160,
							},
							{
								prop: "netTestNum",
								label: "挂测数量",
								minWidth: 160
							},
              {
								prop: "netDeliverNum",
								label: "交维数量",
								minWidth: 160
							},{
								prop: "netAuditNum",
								label: "营业审核数量",
								minWidth: 160
							},
						]
					}
				]
			}
		}

	},
	created(){
		this.getAreaList('-2', 3)
	},
	methods: {
		getAreaList(parentId, index) {
      queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.searchConfig[index], 'options', list)
        }
      })
    },
    changeSelect(name, val){
			if(name == 'citys'){
				this.$set(this.$refs.searchForm.searchForm, 'countys', '')
				val.length && this.getAreaList(val[val.length-1].value, 4)
			}
		},
		search(form) {
      let req = {
				citys: form?.citys,
				countys: form?.countys
			}
      if(req.citys && req.citys.length){
				req.citys = req.citys.map(item => item.label).join()
			}
			if(req.countys && req.countys.length){
				req.countys = req.countys.join()
			}
      this.staticSearchParam = { ...form, ...req }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData({...this.$refs.searchForm.searchForm, ...req});
    },
    reset(form) {
      this.search(form)
    },
		exportMethod() {
			commonDown(this.staticSearchParam, exportBroadbandProcessListService);
		},
		exportDetailMethod(){
			commonDown(this.staticSearchParam, exportBroadbandProcessDetailService);
		},
		toDetail(row){
			this.$router.push({
				path: '/inbound_line/band_progress_query/detail',
				query: {
					projectCode: row.projectCode,
					params: JSON.stringify(this.staticSearchParam)
				}
			})
		}
	}
}
</script>

