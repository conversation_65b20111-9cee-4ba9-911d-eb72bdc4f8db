<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-28 14:27:29
 * @Description: 宽带进度详情
-->

<template>
	<div class="order_overdue_detail">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportDetailMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	findBroadbandProcessDetailervice,
	exportBroadbandProcessDetailService
} from '@/api/inbound_line/statistical_report'
import { commonDown } from "@/utils/btn";
export default {
	name: 'band_progress_query_detail',
	data() {
		return {
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findBroadbandProcessDetailervice,
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "projectName",
						label: "项目名称",
						minWidth: 140,
            tooltip: true
					},
					{
						prop: "projectCode",
						label: "项目编码",
						minWidth: 130,
            tooltip: true
					},
          {
						prop: "taskName",
						label: "任务名称",
						minWidth: 140,
            tooltip: true
					},
					{
						prop: "taskCode",
						label: "任务编码",
						minWidth: 160,
            tooltip: true
					},
					{
						prop: "key4",
						label: "任务点名称（CPMS设计编制）",
						multilevelColumn: [
							{
								label: '任务点基本信息',
								prop: '01',
								multilevelColumn: [
									{
										prop: 'market',
										label: '*小微市场/非小微市场',
										minWidth: 160
									},{
										prop: 'estate',
										label: '*标准小区/非标小区',
										minWidth: 160
									},{
										prop: 'businessArea',
										label: '*所属综合区名称',
										minWidth: 160
									},{
										prop: 'scenetype',
										label: '*小区场景',
										minWidth: 160
									}
								]
							},
							{
								label: '任务点六级地址',
								multilevelColumn: [
									{
										prop: 'city',
										label: '*地市名称',
										minWidth: 120
									},{
										prop: 'county',
										label: '*区县名称',
										minWidth: 120
									},{
										prop: 'towns',
										label: '*乡镇/街道办名称',
										minWidth: 160,
										tooltip: true
									},{
										prop: 'village',
										label: '*所在道路/行政村名称',
										minWidth: 160,
										tooltip: true
									},{
										prop: 'subdistrict',
										label: '所在小区/自然村/弄名称/学校名称',
										minWidth: 220,
										tooltip: true
									},{
										prop: 'areaName',
										label: '所在片区名称',
										minWidth: 160,
										tooltip: true
									}
								]
							},
							{
								label: '覆盖区域名称',
								prop: 'coverageAreaName',
								minWidth: 140,
								tooltip: true
							}
						]
					},
          {
						label: "设计工作量",
						multilevelColumn: [
							{
								prop: 'coverduser',
								label: '*勘察设计实际用户覆盖数',
								minWidth: 160
							},{
								prop: 'lineLength12',
								label: '*12芯光缆长度(公里)',
								minWidth: 160
							},{
								prop: 'lineLength24',
								label: '24芯光缆长度(公里)',
								minWidth: 160
							},{
								prop: 'lineLengt48',
								label: '48芯光缆长度(公里)',
								minWidth: 160
							},{
								prop: 'lightNum',
								label: '光交数量（台）',
								minWidth: 120
							},{
								prop: 'lightBoxNum',
								label: '分纤箱（台）',
								minWidth: 120
							},{
								prop: 'splitterNum',
								label: '*分光器',
								minWidth: 120
							},
						]
					},
          {
						label: "进度数据",
						multilevelColumn: [
							{
								label: "CPMS数据填报",
								multilevelColumn: [
									{
										prop: 'cpmsTaskStart',
										label: '任务点开工',
										minWidth: 120
									},{
										prop: 'cpmsTaskEnd',
										label: '任务点完工',
										minWidth: 120
									},{
										prop: 'cpmsTaskTest',
										label: '验收测试',
										minWidth: 120
									},{
										prop: 'cpmsTaskLine',
										label: '割接上线',
										minWidth: 120
									},{
										prop: 'cpmsTaskTransfer',
										label: '割接交维',
										minWidth: 120
									},{
										prop: 'cpmsTaskCheck',
										label: '竣工验收',
										minWidth: 120
									}
								]
							},
							{
								label: "综资匹配数据",
								multilevelColumn: [
									{
										prop: 'zzlrStatus',
										label: '综资录入',
										minWidth: 120
									},{
										prop: 'zzlrBegintime',
										label: '综资录入开始时间',
										minWidth: 160
									},{
										prop: 'zzlrEndtime',
										label: '综资录入完成时间',
										minWidth: 160
									},{
										prop: 'zzlrUser',
										label: '处理人',
										minWidth: 100
									},{
										prop: 'sjgcStatus',
										label: '数据挂测',
										minWidth: 120
									},{
										prop: 'sjgcBegintime',
										label: '数据挂测开始时间',
										minWidth: 160
									},{
										prop: 'sjgcEndtime',
										label: '数据挂测完成时间',
										minWidth: 160
									},{
										prop: 'jwStatus;',
										label: '交维',
										minWidth: 100
									},{
										prop: 'jwBegintime',
										label: '交维开始时间',
										minWidth: 140
									},{
										prop: 'jwEndtime',
										label: '交维完成时间',
										minWidth: 140
									},{
										prop: 'yyshStatus',
										label: '营业审核',
										minWidth: 100
									},{
										prop: 'yyshBegintime',
										label: '营业审核开始时间',
										minWidth: 160
									},{
										prop: 'yyshEndtime',
										label: '营业审核完成时间',
										minWidth: 160
									},{
										prop: 'yyshUser',
										label: '处理人',
										minWidth: 100
									},{
										prop:'zzDays',
										label: '时长'
									}
								]
							},

						],

					},
					{
						prop: "serialno",
						label: "费用信息",
						multilevelColumn: [
							{
								prop:'key128',
								label:"总投资"
							},
							{
								label:"工程费",
								multilevelColumn: [
									{
										prop: "key126",
										label:"建安费"
									},{
										prop: "key133",
										label:"施工费"
									},
								]
							},
							{
								label:"其他费",
								multilevelColumn: [
									{
										prop: "key107",
										label:"安全生产费",
										minWidth: 140
									},{
										prop: "key132",
										label:"设计费"
									},{
										prop:"key134",
										label:"监理费"
									}
								]
							},
						]
					},
          {
            prop: "key26",
            label: "综合站点状态",
						minWidth: 140
          },
          {
						label: "监理信息",
						multilevelColumn: [
							{
								prop: "key146",
								label:"监理分派",
								minWidth: 160,
								tooltip: true,
							},{
								prop: "key148",
								label:"监理人员",
								minWidth: 100
							},{
								prop:"key149",
								label:"联系方式",
								minWidth: 100
							},{
								prop:'key147',
								label:"所属监理单位",
								minWidth: 160,
								tooltip: true
							}
						]
					},
				]
			}
		}

	},
	created(){
		this.staticSearchParam = {
			...JSON.parse(this.$route.query.params),
			projectCode: this.$route.query.projectCode
		}
	},
	methods: {
		exportDetailMethod(){
			commonDown(this.staticSearchParam, exportBroadbandProcessDetailService);
		},
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/band_progress_query'
      })
    }
	}
}
</script>

