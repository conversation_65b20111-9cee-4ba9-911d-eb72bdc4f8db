<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-28 14:14:39
 * @Description: 6.宽带建设各环节时长统计
-->

<template>
	<div class="response_progress">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="searchForm"
			@search="search"
			@reset="reset"
			:form="searchFormData"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
					headerWrap
					@renderheader="renderheader"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	findNetScheduleDayListService,
	exportNetScheduleDayListService
} from '@/api/inbound_line/statistical_report'
import { queryAreaListService } from '@/api/common_api.js'
import { commonDown } from "@/utils/btn";
export default {
	name: 'band_construction_time',
	data() {
		return {
			searchConfig: [
				{
					type: 'select',
					fieldName: 'city',
					label: '地市',
					span: 8
				},
				{
					type: 'daterange',
					fieldName: 'time',
					label: '统计时间',
					span: 16,
					format: 'yyyy-MM-dd',
					valueFormat: 'yyyy-MM-dd'
				}
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findNetScheduleDayListService,
			searchFormData: {},
			startTime: '',
			endTime: ''
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "city",
						label: "地市",
						minWidth: 80
					},{
						prop: "county",
						label: "区县",
						minWidth:100,
					},
          {
            prop: "name",
            label: "任务名称",
            minWidth: 140,
						tooltip: true
          },
					{
						prop: "2",
						label: "区县工程人员/勘察设计人员",
						headerTitle:'',
						multilevelColumn: [
							{
								prop: "relevantDays",
								label: "一天响应时长（标准1天）",
								minWidth: 180,
							}
						]
					},
          {
						prop: "3",
						label: "省调度：项目经理/设计单位",
						headerTitle:'',
						multilevelColumn: [
							{
								prop: "replyDays",
								label: "设计批复时长（标准1天）",
								minWidth: 180
							}
						]
					},
          {
						prop: "4",
						label: "市调度：项目经理/施工单位",
						headerTitle:'',
						multilevelColumn: [
							{
								prop: "reportDats",
								label: "派工开工时长（标准1天）",
								minWidth: 180,
							},
						]
					},
					{
            prop: "21",
            label: "区县工程人员/施工单位",
						headerTitle:'',
						multilevelColumn: [
							{
								prop: "buildDays",
								label: "施工挂测时长（标准3天）",
								minWidth: 180,
							},
						]
          },
          {
            prop: "designOverTasknum",
            label: "区县工程人员/施工单位",
						headerTitle:'',
						multilevelColumn: [
							{
								prop: "checkDays",
								label: "验收时长（标准1天）",
								minWidth: 180,
							},
						]
          },
					{
						prop: "3",
						label: "区县全业务管理人员",
						multilevelColumn: [
							{
								prop: "reviewDays",
								label: "营业审核时长（标准0.1天）",
								minWidth: 180
							}
						]
					},
          {
						prop: "consDays",
						label: "建设时长",
						minWidth: 140
					},
				]
			}
		}

	},
	created(){
		this.endTime = this.$moment().format('YYYY-MM-DD');
		const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
		this.startTime =  this.$moment(start).format('YYYY-MM-DD');
		this.searchFormData = {
			time: [this.startTime, this.endTime]
		}
		this.staticSearchParam = {
			startDate: this.startTime,
			endDate: this.endTime
		}
		this.getAreaList()
	},
	methods: {
		getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.name })
          })
          this.$set(this.searchConfig[0], 'options', list)
        }
      })
    },
		search() {
      this.$refs.table.page.current = 1
      if(this.$refs.searchForm.searchForm.time){
        this.$refs.searchForm.searchForm.startDate=this.$refs.searchForm.searchForm.time[0]
        this.$refs.searchForm.searchForm.endDate=this.$refs.searchForm.searchForm.time[1]
      }
      this.staticSearchParam={
				...this.staticSearchParam,
				city: this.$refs.searchForm.searchForm.city,
				startDate: this.$refs.searchForm.searchForm.startDate,
				endDate: this.$refs.searchForm.searchForm.endDate,
			}
      this.$refs.table.getTableData(this.staticSearchParam);
    },

    // 重置
    reset() {
      this.searchFormData = {
				time: [this.startTime, this.endTime]
			}
      this.$refs.searchForm.searchForm.time = [this.startTime, this.endTime]
      this.search();
    },
		exportMethod() {
			commonDown(this.staticSearchParam, exportNetScheduleDayListService);
		},
		renderheader(h, column){
			return h('span', {}, [
				h('span', {}, column.label.split('/')[0]),
				h('br'),
				h('span', {}, column.label.split('/')[1])
			]);
		}
	}
}
</script>

