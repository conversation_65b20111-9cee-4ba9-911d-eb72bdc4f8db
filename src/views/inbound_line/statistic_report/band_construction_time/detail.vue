<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-28 14:27:29
 * @Description: 6.宽带建设各环节时长详情统计
-->

<template>
	<div class="order_overdue_detail">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportDetailMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	findInvestProcessDetailListService,
	exportInvestProcessDetailService
} from '@/api/inbound_line/statistical_report'
import { commonDown } from "@/utils/btn";
export default {
	name: 'band_construction_time_detail',
	data() {
		return {
			tableName: this.$route.meta.title,
			staticSearchParam: {
        city: this.$route.query.city
      },
			tableApi: findInvestProcessDetailListService,
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "key2",
						label: "项目名称",
						minWidth: 140,
            tooltip: true
					},
					{
						prop: "key1",
						label: "项目编码",
						minWidth: 130,
            tooltip: true
					},
          {
						prop: "key5",
						label: "任务名称",
						minWidth: 140,
            tooltip: true
					},
					{
						prop: "key4",
						label: "任务编码",
						minWidth: 130,
            tooltip: true
					},
          {
						prop: "lineName",
						label: "专线名称",
						minWidth: 130,
            tooltip: true
					},
          {
						prop: "lineNumber",
						label: "计费编号",
						minWidth: 140,
            tooltip: true
					},
					{
						prop: "serialno",
						label: "流水号",
						minWidth: 130,
            tooltip: true
					},
          {
            prop: "key7",
            label: "所属年份",
          },
          {
						prop: "key17",
						label: "地市",
						minWidth: 80,
					},
          {
						prop: "key18",
						label: "区县",
						minWidth: 80,
					},
					{
						prop: "key19",
						label: "建设单位",
            minWidth: 180,
            tooltip: true
					},
          {
						prop: "key20",
						label: "任务创建时间",
            minWidth: 120,
            tooltip: true
					},
          {
						prop: "key25",
						label: "任务经理",
            minWidth: 120,
					},{
						prop: "key28",
						label: "设计派工提交日期",
            minWidth: 140,
            tooltip: true
					},{
						prop: "key29",
						label: "设计派工接收日期",
            minWidth: 120,
            tooltip: true
					},{
						prop: "key30",
						label: "设计勘察完成时间",
            minWidth: 160
					},{
						prop: "key31",
						label: "设计编制完成时间",
            minWidth: 160
					},{
						prop: "key34",
						label: "发起电子会审时间",
            minWidth: 160
					},{
						prop: "key35",
						label: "设计会审完成时间",
            minWidth: 160
					},{
						prop: "key42",
						label: "设计批复创建时间",
            minWidth: 160
					},{
						prop: "key43",
						label: "设计批复完成时间",
            minWidth: 160
					},{
						prop: "key47",
						label: "施工派工提交日期",
            minWidth: 160
					},{
						prop: "key48",
						label: "施工派工接收日期",
            minWidth: 160
					},{
						prop: "completStartDate",
						label: "光缆施工完工发起时间",
            minWidth: 160
					},{
						prop: "completEndDate",
						label: "光缆施工完工完成时间",
            minWidth: 160
					}
				]
			}
		}

	},
	created(){},
	methods: {
		exportDetailMethod(){
			commonDown(this.staticSearchParam, exportInvestProcessDetailService);
		},
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/band_construction_time'
      })
    }
	}
}
</script>

