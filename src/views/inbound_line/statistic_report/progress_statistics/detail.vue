<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-26 17:05:20
 * @Description: 集客专线进度统计详情
-->

<template>
	<div class="order_overdue">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportDetailMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	findScheduleDetailService,
	exportScheduleDetailService
} from '@/api/inbound_line/statistical_report'
import { commonDown } from "@/utils/btn";
export default {
	name: 'progress_statistics_detail',
	data() {
		return {
			tableName: this.$route.meta.title,
			staticSearchParam: {
				...JSON.parse(this.$route.query.params),
				citys: this.$route.query.city
      },
			tableApi: findScheduleDetailService,
			loadParams: {}
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "linenumber",
						label: "计费编号",
						minWidth: 120,
						tooltip: true
					},
					{
						prop: "serialno",
						label: "流水号",
						minWidth: 140,
						tooltip: true
					},
          {
						prop: "city",
						label: "地市",
						minWidth: 80,
					},
					{
						prop: "county",
						label: "区县",
						minWidth: 100,
						tooltip: true
					},
					{
						prop: "linename",
						label: "专线名称",
            minWidth: 180,
            tooltip: true
					},
          {
						prop: "lineType",
						label: "专线类型",
						minWidth: 120,
						tooltip: true
					},
          {
						prop: "totalsheathlength",
						label: "光缆合计长度",
            minWidth: 120,
					},{
						prop: "investTypeName",
						label: "专线归属"
					},{
						prop: "totalfare",
						label: "总投资"
					},{
						prop: "sendDate",
						label: "开通单推送工程辅助系统时间",
            minWidth: 180
					},{
						prop: "startDate",
						label: "光缆施工开始时间",
            minWidth: 160
					},{
						prop: "endDate",
						label: "光缆施工完成时间",
            minWidth: 160
					},{
						prop: "archiveDate",
						label: "开通单归档时间",
            minWidth: 160
					},

				]
			}
		}

	},
	created(){},
	methods: {
		exportDetailMethod(){
			commonDown({...this.staticSearchParam, ...this.loadParams }, exportScheduleDetailService);
		},
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/progress_statistics'
      })
    }
	}
}
</script>

