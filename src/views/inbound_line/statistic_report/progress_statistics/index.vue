<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-26 15:30:00
 * @Description: 集客专线进度统计报表
-->

<template>
	<div class="order_overdue">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="SearchForm"
			@search="search"
			@reset="reset"
      @changeSelect="changeSelect"
			:form="searchFormData"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
				<el-button type="primary" @click="exportDetailMethod">导出明细</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
					:pagination="false"
					:serial="false"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	findScheduleListService,
	exportScheduleListService,
	exportScheduleDetailService
} from '@/api/inbound_line/statistical_report'
import { queryAreaListService } from '@/api/common_api.js'
import { commonDown } from "@/utils/btn";
export default {
	name: 'progress_statistics_list',
	data() {
		return {
			searchConfig: [
				{
					type: 'daterange',
					fieldName: 'createDate',
					label: '开通单创建时间',
          span: 12,
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd'
				},
				{
					type: 'daterange',
					fieldName: 'archiveDate',
					label: '开通单归档时间',
          span: 12,
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd'
				},
        {
          type: 'select',
					fieldName: 'citys',
					label: '建设单位',
          selectMultiple: true,
					itemAs:true,
          span: 12
        },
				{
					type: 'select',
					fieldName: 'countys',
					label: '区县',
          selectMultiple: true,
					itemValue: 'label',
          span: 12
				},
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findScheduleListService,
		}
	},
	computed:{
		columns:{
			get(){
				return [
          {
						prop: "city",
						label: "建设单位",
						minWidth: 100,
            formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row)}}>{row.city}</span>
							)
						}
					},
					{
						prop: "countys",
						label: "区县",
						minWidth: 100,
					},
					{
						prop: "investNum",
						label: "投资类集客专线总数",
						minWidth: 120,
					},
          {
						prop: "costNum",
						label: "成本类集客专线总数",
						minWidth: 120,
					},
					{
						prop: "3",
						label: "投资类集客专线",
						multilevelColumn: [
              {
                prop: "investSendNum",
                label: "编排系统推送开通单数据"
              },
              {
                prop: "investEndNum",
                label: "完工上报完成数量"
              },
							{
								prop:'investArchiveNum',
								label: "编排系统归档数量",
							},
						]
					},
          {
						prop: "3",
						label: "成本类集客专线",
						multilevelColumn: [
              {
                prop: "costSendNum",
                label: "编排系统推送开通单数据"
              },
              {
                prop: "costEndNum",
                label: "完工上报完成数量"
              },
							{
								prop:'costArchiveNum',
								label: "编排系统归档数量",
							},
						]
					}
				]
			}
		}

	},
	created(){
		this.endTime = this.$moment().format('YYYY-MM-DD');
		const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
		this.startTime =  this.$moment(start).format('YYYY-MM-DD');
		this.searchFormData = {
			createDate: [this.startTime, this.endTime]
		}
		this.staticSearchParam = {
			createStartDate: this.startTime,
			createEndDate: this.endTime
		}
		this.getAreaList('-2', 2)
	},
	methods: {
		getAreaList(parentId, index) {
      queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.searchConfig[index], 'options', list)
        }
      })
    },
    changeSelect(name, val){
			if(name == 'citys'){
				val.length && this.getAreaList(val[val.length-1].value, 3)
			}
		},
		search(searchData) {
      let req = {...searchData}

			this.$refs.table.page.current = 1
      if(req.archiveDate){
        req.archiveStartDate=req.archiveDate[0]
        req.archiveEndDate=req.archiveDate[1]
				delete req.archiveDate
      }
      if(req.createDate){
        req.createStartDate=req.createDate[0]
        req.createEndDate=req.createDate[1]
				delete req.createDate
      }
			if(req.citys && req.citys.length){
				req.citys = req.citys.map(item => item.label).join()
			}
			if(req.countys && req.countys.length){
				req.countys = req.countys.join()
			}

      this.staticSearchParam=JSON.parse(
        JSON.stringify({...req})
      )
      this.$refs.table.getTableData({...req});
		},
		reset(form) {
			this.searchFormData = {
				createDate: [this.startTime, this.endTime],
			}
			this.$refs.SearchForm.searchForm.archiveDate= []
			this.$refs.SearchForm.searchForm.createDate= [ this.startTime, this.endTime]
			this.search(form)
		},
		exportMethod() {
			commonDown(this.staticSearchParam, exportScheduleListService);
		},
		exportDetailMethod(){
			commonDown(this.staticSearchParam, exportScheduleDetailService);
		},
    toDetail(row){
			this.$router.push({
				path: '/inbound_line/progress_statistics/detail',
				query: {
					city: row.city==("全省" || "合计") ? '': row.city,
					params: JSON.stringify(this.staticSearchParam)
				}
			})
		}
	}
}
</script>

