<!-- 响应进度表 -->
<template>
	<div class="order_overdue">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="searchForm"
			@search="search"
			@reset="reset"
			:form="searchFormData"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
				<el-button type="primary" @click="exportDetailMethod">导出明细</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
					:pagination="false"
					:serial="false"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import { findNetScheduleListService, exportNetScheduleListService,exportNetScheduleDetailService } from "@/api/inbound_line/statistical_report";
import { queryAreaListService } from '@/api/common_api.js'
import { commonDown } from "@/utils/btn";
export default {
	name: 'response_progress_list',
	data() {
		return {
			searchConfig: [
				{
					type: 'select',
					fieldName: 'city',
					label: '地市',
					span: 8
				},
				{
					type: 'daterange',
					fieldName: 'time',
					label: '统计时间',
					span: 16,
					format: 'yyyy-MM-dd',
					valueFormat: 'yyyy-MM-dd'
				}
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findNetScheduleListService,
			searchFormData: {},
			startTime: '',
			endTime: ''
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "city",
						label: "地市",
						minWidth: 80,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row)}}>{row.city}</span>
							)
						}
					},
					{
						prop: "3",
						label: "网络规划平台响应情况",
						multilevelColumn: [
							{
								prop: "netSubNum",
								label: "网格长提交工单数（个）",
								minWidth: 160
							},{
								prop:'netDesNum',
								label: "设计人员提交工单数（个）",
								minWidth: 180
							},{
								prop:'netPNum',
								label: "纳入项目工单数（个）",
								minWidth: 160
							},{
								prop:'netTimelyNum',
								label: "一天响应及时工单数（个）",
								minWidth: 180
							},{
								prop:'netRate',
								label: "响应及时率（%）",
								minWidth: 160
							},{
								prop:'netAvgDay',
								label: "平均响应时长（天）",
								minWidth: 160
							},
						]
					},
					{
						prop: "4",
						label: "PMS建设情况（累计纳入项目工单）",
						multilevelColumn: [
							{
								prop: "pmsPNum",
								label: "纳入项目工单数（个）",
								minWidth: 160
							},{
								prop: "pmsReplyNum",
								label: "批复工单数（个）",
								minWidth: 160
							},{
								prop:'pmsReportNum',
								label: "开工工单数（个）",
								minWidth: 160
							},{
								prop:'pmsTradeNum',
								label: "通过营业审核工单数（个）",
								minWidth: 180
							},{
								prop:'pmsConsNum',
								label: "建设及时工单数（个）",
								minWidth: 160
							},{
								prop:'pmsConsRate',
								label: "建设及时率（%）",
								minWidth: 160
							},{
								prop:'pmsAvgDay',
								label: "平均建设时长（天）",
								minWidth: 160
							}
						]
					},
				]
			}
		}

	},
	created(){
		this.endTime = this.$moment().format('YYYY-MM-DD');
		const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
		this.startTime =  this.$moment(start).format('YYYY-MM-DD');
		this.searchFormData = {
			time: [this.startTime, this.endTime]
		}
		this.staticSearchParam = {
			startDate: this.startTime,
			endDate: this.endTime
		}
		this.getAreaList()
	},
	methods: {
		getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.name })
          })
          this.$set(this.searchConfig[0], 'options', list)
        }
      })
    },
		search() {
      this.$refs.table.page.current = 1
      if(this.$refs.searchForm.searchForm.time){
        this.$refs.searchForm.searchForm.startDate=this.$refs.searchForm.searchForm.time[0]
        this.$refs.searchForm.searchForm.endDate=this.$refs.searchForm.searchForm.time[1]
      }
      this.staticSearchParam={
				...this.staticSearchParam,
				city: this.$refs.searchForm.searchForm.city,
				startDate: this.$refs.searchForm.searchForm.startDate,
				endDate: this.$refs.searchForm.searchForm.endDate,
			}
      this.$refs.table.getTableData(this.staticSearchParam);
    },

    // 重置
    reset() {
      this.searchFormData = {
				time: [this.startTime, this.endTime]
			}
      this.$refs.searchForm.searchForm.time = [this.startTime, this.endTime]
      this.search();
    },
		exportMethod(){
			commonDown(this.staticSearchParam, exportNetScheduleListService)
		},
		exportDetailMethod(){
			commonDown(this.staticSearchParam, exportNetScheduleDetailService)
		},
		toDetail(row){
			let city = ""
			if(row.city == '全省' || row.city == '合计'){
				city = ''
			}else{
				city = row.city
			}
			this.$router.push({
				path: '/inbound_line/response_progress/detail',
				query: {
					city: city,
					params: JSON.stringify(this.staticSearchParam)
				}
			})
		}
	}
}
</script>


