<!-- 响应进度表--下转详情表 -->
<template>
	<div class="response_progress_detail">
		<div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
		<mssCard :title="tableName">
			<div slot="headerBtn">
			    <el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable 
					border
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				/>
			</div>
		</mssCard>
	</div>
</template>

<script>
import { findNetScheduleDetailService,exportNetScheduleDetailService } from "@/api/inbound_line/statistical_report";
import { commonDown } from "@/utils/btn";
export default {
	name: 'response_progress_detail',
	data() {
		return {
			tableName: this.$route.meta.title,
			staticSearchParam: {
				...JSON.parse(this.$route.query.params),
				city: this.$route.query.city,
			},
			tableApi: findNetScheduleDetailService
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "lifecode",
						label: "状态",
						minWidth: 100,
					},
					{
						prop: "flowstep",
						label: "当前流程节点",
						minWidth: 160,
					},
					{
						prop: "name",
						label: "名称",		
						minWidth: 160,
						tooltip: true
					},
					{
						prop: "city",
						label: "地市",
					},
					{
						prop: "county",
						label: "区县"
					},
					{
						prop: "totalprice",
						label: "投资估算",
            minWidth: 125,
					},
					{
						prop: "scenetype",
						label: "场景",
						minWidth: 100
					},
					{
						prop: "coverduser",
						label: "覆盖用户数",
						minWidth: 125
					},
					{
						prop: "tforce",
						label: "是否强标",
						minWidth: 100
					},
					{
						prop: "code",
						label: "编号",
						minWidth: 185,
						tooltip: true
					},
					{
						prop: "createusername",
						label: "创建人",
						minWidth: 120,
					},
					{
						prop: "createtime",
						label: "创建时间",
						minWidth: 160
					},

					{
						prop: "emergencylevel",
						label: "紧急程度",
						minWidth: 120
					},
					{
						prop: "submitdate",
						label: "网格长提交时间",
						minWidth: 160
					},
					{
						prop: "messagedate",
						label: "设计人员反馈时间",
						minWidth: 160
					},
					{
						prop: "cancelreason",
						label: "作废原因",
						minWidth: 160
					},{
						prop: "isApproval",
						label: "是否立项",
						minWidth: 120
					},{
						prop: "projectcode",
						label: "项目编码",
						minWidth: 140,
						tooltip: true,
					},{
						prop: "taskCode",
						label: "任务编码",
						minWidth: 140,
						tooltip: true,
					},{
						prop: "key20",
						label: "任务创建时间",
						minWidth: 140,
						tooltip: true,
					},{
						prop: "key34",
						label: "发起电子会审时间",
						minWidth: 160
					},{
						prop: "key35",
						label: "设计会审完成时间",
            minWidth: 160
					},{
						prop: "key42",
						label: "设计批复创建时间",
						minWidth: 160
					},{
						prop: "key43",
						label: "设计批复完成时间",
						minWidth: 160
					},
					{
						prop: "key47",
						label: "施工派工提交日期",
						minWidth: 160
					},{
						prop: "key48",
						label: "施工派工接收日期",
						minWidth: 160
					},{
						prop: "key52",
						label: "开工报告提交日期",
						minWidth: 160
					},{
						prop: "key53",
						label: "开工报告完成时间",
						minWidth: 160
					},{
						prop: "sjgcBegintime",
						label: "数据挂测开始时间",
						minWidth: 160
					},{
						prop: "sjgcEndtime",
						label: "数据挂测完成时间",
						minWidth: 160
					},
					{
						prop: "jwBegintime",
						label: "交维验收开始时间",
						minWidth: 160
					},
					{
						prop: "jwEndtime",
						label: "交维验收结束时间",
						minWidth: 160
					},
					{
						prop: "yyshBegintime",
						label: "营业审核开始时间",
						minWidth: 160
					},
					{
						prop: "yyshEndtime",
						label: "营业审核完成时间",
						minWidth: 160
					},
				]
			}
		}	
	},
	methods: {
		exportMethod(){
			commonDown(this.staticSearchParam, exportNetScheduleDetailService)
		},
		goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/response_progress'
      })
    }
	}
}
</script>


