<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-27 09:47:46
 * @Description: 成本类在途统计详情
-->
<template>
	<div class="order_overdue_detail">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportDetailMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	findCostProcessDetailService,
	exportCostProcessDetailService
} from '@/api/inbound_line/statistical_report'
import { commonDown } from "@/utils/btn";
export default {
	name: 'order_overdue_detail',
	data() {
		return {
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findCostProcessDetailService,
			loadParams: {}
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "lineNumber",
						label: "计费编号",
						minWidth: 140,
            tooltip: true
					},
					{
						prop: "serialno",
						label: "流水号",
						minWidth: 130,
            tooltip: true
					},
          {
						prop: "city",
						label: "地市",
						minWidth: 80,
					},
					{
						prop: "county",
						label: "区县",
						minWidth: 100,
						tooltip: true
					},
					{
						prop: "lineName",
						label: "专线名称",
            minWidth: 180,
            tooltip: true
					},
          {
						prop: "linetype",
						label: "专线类型",
            minWidth: 120,
            tooltip: true
					},
          {
						prop: "totalsheathlength",
						label: "光缆合计长度",
            minWidth: 120,
					},{
						prop: "totalfare",
						label: "总投资",
            tooltip: true
					},{
						prop: "lineStatus",
						label: "勘察单状态",
            minWidth: 120,
            tooltip: true
					},{
						prop: "lineStartDate",
						label: "勘察单开始时间",
            minWidth: 160
					},{
						prop: "lineEndDate",
						label: "勘察单结束时间",
            minWidth: 160
					},{
						prop: "replyStatus",
						label: "方案批复状态",
            minWidth: 160
					},{
						prop: "replyStartDate",
						label: "方案批复开始时间",
            minWidth: 160
					},{
						prop: "replyEndDate",
						label: "方案批复结束时间",
            minWidth: 160
					},{
						prop: "taskStatus",
						label: "施工任务单状态",
            minWidth: 160
					},{
						prop: "taskStartDate",
						label: "施工任务单开始时间",
            minWidth: 160
					},{
						prop: "taskEndDate",
						label: "施工任务单结束时间",
            minWidth: 160
					},{
						prop: "completedStatus",
						label: "成本完工单状态",
            minWidth: 160
					},{
						prop: "completedStartDate",
						label: "成本完工单开始时间",
            minWidth: 160
					},{
						prop: "completedEndDate",
						label: "成本完工单结束时间",
            minWidth: 160
					},

				]
			}
		}

	},
	created(){
		const city = this.$route.query.city
		const clickNumType = this.$route.query.clickNumType
		const otherParams = JSON.parse(this.$route.query.params)
		const map = {
			city: null,
			pnum: 21,
			overNum: 22
		}
		this.staticSearchParam = {
			clickNumType: map[clickNumType],
			...otherParams,
			city: city
		}
	},
	methods: {
		exportDetailMethod(){
			commonDown({...this.staticSearchParam, ...this.loadParams }, exportCostProcessDetailService);
		},
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/order_overdue'
      })
    }
	}
}
</script>

