<!-- 2.成本类-在途集客专线统计报表 -->
<template>
	<div class="order_overdue">
		<mssSearchForm 
			:searchConfig="searchConfig" 
			ref="searchForm" 
			@search="search" 
			@reset="reset"
			:form="searchFormData"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
				<el-button type="primary" @click="exportDetailMethod">导出明细</el-button>
			</div>
			<div slot="content">
				<mssTable 
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
					:pagination="false"
					:serial="false"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import { 
	findCostProcessListService,
	exportCostProcessListService,
	exportCostProcessDetailService
} from '@/api/inbound_line/statistical_report'
import { queryAreaListService } from '@/api/common_api.js'
import { commonDown } from "@/utils/btn";
export default {
	name: 'order_overdue',
	data() {
		return {
			searchConfig: [
				{
					type: 'select',
					fieldName: 'city',
					label: '地市'
				},
				{
					type: 'daterange',
					fieldName: 'time',
					label: '统计时间',
					span: 16,
					format: 'yyyy-MM-dd', 
					valueFormat: 'yyyy-MM-dd',
					span: 16
				}
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findCostProcessListService,
			searchFormData: {},
			startTime: '',
			endTime: ''
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "city",
						label: "地市",
						minWidth: 80,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row,"city")}}>{row.city}</span>
							)
						}
					},
					{
						prop: "pnum",
						label: "在途工单数量",
						minWidth: 120,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row,"pnum")}}>{row.pnum}</span>
							)
						}
					},
					{
						prop: "3",
						label: "业务受理至光缆施工完工流程",
						multilevelColumn: [
							{
								prop: "03",
								label: "施工派工",
								multilevelColumn: [
									{
										prop: "consTaskNum",
										label: "当前节点任务数"
									},
									{
										prop: "overOneDayNum",
										label: ">1天数量"
									}
								]
							},
							{
								prop:'04',
								label: "光缆施工完工",
								multilevelColumn: [
									{
										prop: "completeTaskNum",
										label: "当前节点任务数"
									},
									{
										prop: "overThreeDayNum",
										label: ">3天数量"
									}
								]
							}
						]
					},
					{
						prop: "overNum",
						label: "超时任务总数",
						minWidth: 140,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row,"overNum")}}>{row.overNum}</span>
							)
						}
					}
				]
			}
		}
			
	},
	created(){
		this.endTime = this.$moment().format('YYYY-MM-DD');
		const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
		this.startTime =  this.$moment(start).format('YYYY-MM-DD');
		this.searchFormData = {
			time: [this.startTime, this.endTime]
		}
		this.staticSearchParam = {
			startDate: this.startTime,
			endDate: this.endTime
		}
		this.getAreaList()
	},
	methods: {
		getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.name })
          })
          this.$set(this.searchConfig[0], 'options', list)
        }
      })
    },
		search() {
      this.$refs.table.page.current = 1
      if(this.$refs.searchForm.searchForm.time){
        this.$refs.searchForm.searchForm.startDate=this.$refs.searchForm.searchForm.time[0]
        this.$refs.searchForm.searchForm.endDate=this.$refs.searchForm.searchForm.time[1]
      }
      this.staticSearchParam={
				...this.staticSearchParam,
				city: this.$refs.searchForm.searchForm.city,
				startDate: this.$refs.searchForm.searchForm.startDate,
				endDate: this.$refs.searchForm.searchForm.endDate,
			}
      this.$refs.table.getTableData(this.staticSearchParam);
    },

    // 重置
    reset() {
			this.searchFormData = {
				time: [this.startTime, this.endTime]
			}
      this.$refs.searchForm.searchForm.time = [this.startTime, this.endTime]
      this.search();
    },
		exportMethod() {
			commonDown(this.staticSearchParam, exportCostProcessListService);
		},
		exportDetailMethod(){
			commonDown(this.staticSearchParam, exportCostProcessDetailService);
		},
		toDetail(row, clickNumType){
			this.$router.push({
				path: '/inbound_line/order_overdue/detail',
				query: {
					city: row.city==("全省" || "合计") ? '': row.city,
					clickNumType: clickNumType,
					params: JSON.stringify(this.staticSearchParam)
				}
			})
		}
	}
}
</script>

