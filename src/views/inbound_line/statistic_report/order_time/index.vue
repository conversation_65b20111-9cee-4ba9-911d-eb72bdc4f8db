<!-- 工单处理时长报表 -->
<template>
	<div class="response_progress">
		<mssSearchForm 
			:searchConfig="searchConfig" 
			ref="searchForm" 
			@search="search" 
			@reset="reset"
			:form="searchFormData"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
				<el-button type="primary" @click="exportDetailMethod">导出明细</el-button>
			</div>
			<div slot="content">
				<mssTable 
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
					:pagination="false"
					:serial="false"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import { 
	findCostCompListService, 
	exportCostCompListService,
	exportCostCompDetailService 
} from '@/api/inbound_line/statistical_report'
import { queryAreaListService } from '@/api/common_api.js'
import { commonDown } from "@/utils/btn";
export default {
	name: 'response_progress',
	data() {
		return {
			searchConfig: [
				{
					type: 'select',
					fieldName: 'city',
					label: '地市'
				},
				{
					type: 'daterange',
					fieldName: 'time',
					label: '统计时间',
					span: 16,
					format: 'yyyy-MM-dd', 
					valueFormat: 'yyyy-MM-dd',
					span: 16
				}
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findCostCompListService,
			searchFormData: {},
			startTime: '',
			endTime: ''
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "city",
						label: "地市",
						minWidth: 80,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row,"city")}}>{row.city}</span>
							)
						}
					},
					{
						prop: "2",
						label: "光缆施工完工数量",
						multilevelColumn: [
							{
								prop: "cnum",
								label: "已完工总数",
								minWidth: 100,
								formatter: row => {
									return (
										<span class='table_btn' onClick={()=>{this.toDetail(row,"cnum")}}>{row.cnum}</span>
									)
								}
							},
							{
								prop: "processAvgDate",
								label: "平均时长",
								minWidth: 100
							},
							{
								prop: "overNum",
								label: "时长超3.5天数量",
								minWidth: 120,
								formatter: row => {
									return (
										<span class='table_btn' onClick={()=>{this.toDetail(row,"overNum")}}>{row.overNum}</span>
									)
								}
							},
							{
								prop: "overRate",
								label: "时长超3.5天占比",
								minWidth: 120
							}
						]
					},
					{
						prop: "3",
						label: "流转工单统计",
						multilevelColumn: [
							{
								prop: "03",
								label: "施工派工",
								multilevelColumn: [
									{
										prop: 'taskAvgDate',
										label: "平均时长",
									},
									{
										prop: "taskMaxDate",
										label: "最大天数"
									}
								]
							},
							{
								prop:'04',
								label: "光缆施工完工",
								multilevelColumn: [{
									prop: "compAvgDate",
									label: "平均时长"
								},{
									prop: "compMaxDate",
									label: "最大天数"
								}]	
							}
						]
					},
				]
			}
		}
			
	},
	created(){
		this.endTime = this.$moment().format('YYYY-MM-DD');
		const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
		this.startTime =  this.$moment(start).format('YYYY-MM-DD');
		this.searchFormData = {
			time: [this.startTime, this.endTime]
		}
		this.staticSearchParam = {
			startDate: this.startTime,
			endDate: this.endTime
		}
		this.getAreaList()
	},
	methods: {
		getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.name })
          })
          this.$set(this.searchConfig[0], 'options', list)
        }
      })
    },
		search() {
      this.$refs.table.page.current = 1
      if(this.$refs.searchForm.searchForm.time){
        this.$refs.searchForm.searchForm.startDate=this.$refs.searchForm.searchForm.time[0]
        this.$refs.searchForm.searchForm.endDate=this.$refs.searchForm.searchForm.time[1]
      }
      this.staticSearchParam={
				...this.staticSearchParam,
				city: this.$refs.searchForm.searchForm.city,
				startDate: this.$refs.searchForm.searchForm.startDate,
				endDate: this.$refs.searchForm.searchForm.endDate,
			}
      this.$refs.table.getTableData(this.staticSearchParam);
    },

    // 重置
    reset() {
			this.searchFormData = {
				time: [this.startTime, this.endTime]
			}
      this.$refs.searchForm.searchForm.time = [this.startTime, this.endTime]
      this.search();
    },
		exportMethod() {
			commonDown(this.staticSearchParam, exportCostCompListService);
		},
		exportDetailMethod(){
			commonDown(this.staticSearchParam, exportCostCompDetailService);
		},
		toDetail(row, clickNumType){
			this.$router.push({
				path: '/inbound_line/order_time/detail',
				query: {
					city: row.city==("全省" || "合计") ? '': row.city,
					clickNumType: clickNumType,
					params: JSON.stringify(this.staticSearchParam)
				}
			})
		}
	}
}
</script>

