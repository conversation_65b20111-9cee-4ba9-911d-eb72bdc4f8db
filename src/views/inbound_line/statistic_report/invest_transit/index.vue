<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-28 14:14:39
 * @Description: 1.投资类-在途集客专线统计报表
-->

<template>
	<div class="response_progress">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="searchForm"
			@search="search"
			@reset="reset"
			:form="searchFormData"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
				<el-button type="primary" @click="exportDetailMethod">导出明细</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
					:pagination="false"
					:serial="false"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	findInvestProcessListService,
	exportInvestProcessListService,
	exportInvestProcessDetailService
} from '@/api/inbound_line/statistical_report'
import { queryAreaListService } from '@/api/common_api.js'
import { commonDown } from "@/utils/btn";
export default {
	name: 'invest_transit_list',
	data() {
		return {
			searchConfig: [
				{
					type: 'select',
					fieldName: 'city',
					label: '地市',
					span: 8
				},
				{
					type: 'daterange',
					fieldName: 'time',
					label: '统计时间',
					span: 16,
					format: 'yyyy-MM-dd',
					valueFormat: 'yyyy-MM-dd'
				}
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findInvestProcessListService,
			searchFormData: {},
			startTime: '',
			endTime: ''
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "city",
						label: "地市",
						minWidth: 80,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row,"city")}}>{row.city}</span>
							)
						}
					},
          {
            prop: "tasknum",
            label: "在途工单数量",
            minWidth: 100,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row,"tasknum")}}>{row.tasknum}</span>
							)
						}
          },
					{
						prop: "2",
						label: "设计编制预警",
						multilevelColumn: [
							{
								prop: "designCompileTasknum",
								label: "当前节点任务数",
								minWidth: 120,
							},
							{
								prop: "designCompileOverTasknum",
								label: ">1天数量",
								minWidth: 100
							},
						]
					},
          {
						prop: "3",
						label: "设计会审预警",
						multilevelColumn: [
							{
								prop: "designReviewTasknum",
								label: "当前节点任务数",
								minWidth: 120
							},
							{
								prop: "designReviewOverTasknum",
								label: ">1天数量",
								minWidth: 100
							},
						]
					},
          {
						prop: "4",
						label: "设计批复预警",
						multilevelColumn: [
							{
								prop: "designReplyTasknum",
								label: "当前节点任务数",
								minWidth: 120,
							},
							{
								prop: "designReplyOverTasknum",
								label: ">1天数量",
								minWidth: 100
							},
						]
					},
          {
            prop: "designOverTasknum",
            label: "设计环节超时任务数",
            minWidth: 130,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row,"designOverTasknum")}}>{row.designOverTasknum}</span>
							)
						}
          },
					{
						prop: "3",
						label: "施工派工（已派开通单）",
						multilevelColumn: [
							{
								prop: "consTasknum",
								label: "当前节点任务数",
								minWidth: 120
							},
							{
								prop:'consOverTasknum',
								label: ">1天数量",
								minWidth: 100
							}
						]
					},
          {
						prop: "3",
						label: "光缆施工完工",
						multilevelColumn: [
							{
								prop: "compTasknum",
								label: "当前节点任务数",
								minWidth: 120
							},
							{
								prop:'compOverTasknum',
								label: ">3天数量",
								minWidth: 100
							}
						]
					},
          {
            prop: "overTasknum",
            label: "超时任务总数",
						minWidth: 120,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row,"overTasknum")}}>{row.overTasknum}</span>
							)
						}
          }
				]
			}
		}

	},
	created(){
		this.endTime = this.$moment().format('YYYY-MM-DD');
		const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
		this.startTime =  this.$moment(start).format('YYYY-MM-DD');
		this.searchFormData = {
			time: [this.startTime, this.endTime]
		}
		this.staticSearchParam = {
			startDate: this.startTime,
			endDate: this.endTime
		}
		this.getAreaList()
	},
	methods: {
		getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.name })
          })
          this.$set(this.searchConfig[0], 'options', list)
        }
      })
    },
		search() {
      this.$refs.table.page.current = 1
      if(this.$refs.searchForm.searchForm.time){
        this.$refs.searchForm.searchForm.startDate=this.$refs.searchForm.searchForm.time[0]
        this.$refs.searchForm.searchForm.endDate=this.$refs.searchForm.searchForm.time[1]
      }
      this.staticSearchParam={
				...this.staticSearchParam,
				city: this.$refs.searchForm.searchForm.city,
				startDate: this.$refs.searchForm.searchForm.startDate,
				endDate: this.$refs.searchForm.searchForm.endDate,
			}
      this.$refs.table.getTableData(this.staticSearchParam);
    },

    // 重置
    reset() {
			this.searchFormData = {
				time: [this.startTime, this.endTime]
			}
      this.$refs.searchForm.searchForm.time = [this.startTime, this.endTime]
      this.search();
    },
		exportMethod() {
			commonDown(this.staticSearchParam, exportInvestProcessListService);
		},
		exportDetailMethod(){
			commonDown(this.staticSearchParam, exportInvestProcessDetailService);
		},
		toDetail(row, clickNumType){
			this.$router.push({
				path: '/inbound_line/invest_transit/detail',
				query: {
					city: row.city==("全省" || "合计") ? '': row.city,
					clickNumType: clickNumType,
					params: JSON.stringify(this.staticSearchParam)
				}
			})
		}
	}
}
</script>

