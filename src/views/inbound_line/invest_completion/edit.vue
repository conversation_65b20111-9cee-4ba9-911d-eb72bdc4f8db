<!-- 投资类完工处理页面 -->
<template>
  <div class="invest_completion_detail page-anchor-parentpage">
    <div class="operate-btn">
      <el-button type="primary"  @click="submit()">提交</el-button>
      <el-button type="primary"  @click="save()">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
    ></mssWorkFlowHandel>
    <infos labelPosition="top" ref="infos" :formRules="formRules" @getWorkCode="getWorkCode"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService,beforeSubmitService} from "@/api/inbound_line/invest_completion_api.js"
export default {
  name: 'invest_completion_detail',
  components:{infos},
  data() {
    return {
      boId: '',
      workflowCode: '',
      formDataCopy: {},//表单新值
      formInitData: {},//表单初始值
      completeTaskUrl:"",//流程处理地址
      returnAddress:'/inbound_line/invest_completion',//流程提交成功返回路由地址
      formRules:{}
    }
  },
  created() {
    this.boId = this.$route.query.boId
  },
  methods: {
    getWorkCode(param){
      this.workflowCode=param
      this.$nextTick(()=>{
        this.$refs.workFlow.init()
      })
    },
    // 返回上一页
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/invest_completion'
      })
    },

    // 保存
    save(cb){
      if(this.validate().flag1 && this.validate().flag2 ){
      // this.$refs.infos.$refs.otherForm.$refs.form.validateScroll((valid)=>{
        // if(valid){
          // 处理数据
          let param={
            ...this.$refs.infos.$refs.basicForm.modelForm,
            completeDate:this.$refs.infos.$refs.otherForm.modelForm.completeDate,
            detail:this.$refs.infos.$refs.lineForm.modelForm
          }
          if(this.$refs.infos.showlineTwo){
            param.detailOther=this.$refs.infos.$refs.lineTwoForm.modelForm
          }
          saveService(param).then(res=>{
            if(res.code=='0000'){
              if(cb){
                cb(res.data)
              }else{
                this.$message.success('保存成功')
              }
            }
          })
        // }else{
        //   return
        // }
      // })
      }else{
        if(cb) cb({})
      }
    },
    // 判断校验必填
    validate(){
      let obj={
        flag1:false,
        flag2:false,
      }
      this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          obj.flag1=true
        }
      });
      this.$refs.infos.$refs.otherForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          obj.flag2=true
        }
      });
      return obj
    },
    async beforeSubmit(){
      let str=""
      if(this.validate().flag1 && this.validate().flag2 ){
      }else{
        str +='有必填信息未填写完整，请检查! '
      }
      let res=await beforeSubmitService({boId:this.boId})
      if(res.data && res.data.code!=0){
        str +=res.data.msg
      }
      return str
    },
    // 提交--先保存后提交
    submit(){
      // if(this.validate().flag1 && this.validate().flag2 ){
      // this.$refs.infos.$refs.otherForm.$refs.form.validateScroll((valid)=>{
        // if(valid){
          this.save((param)=>{
            this.$refs.infos.basicForm = {
              ...this.$refs.infos.basicForm,
              ...param
            };
            this.completeTaskUrl = this.$refs.infos.basicForm.completeTaskUrl
            this.$refs.workFlow.opendialogInitNextPath()
          })
        // }else{
        //     return
        //   }
      // })
      // }

    },
     //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      let name = { code: 'name', value: this.$refs.infos.$refs.basicForm.modelForm.name }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode() {
      const fileNameArr = {
        consUnit:{ userName: this.$refs.infos.basicForm.consDeptUserName||'',
        userId: this.$refs.infos.basicForm.consDeptUserId||''}
      }
      // 设置默认处理人
      return fileNameArr
    },
    // 必填校验
    initFormRules(param) {
      this.formRules=param
    },
    // 只读表单
    getReadonlyList(param) {
      if(param.length){
        this.$refs.infos.basicConfig.forEach(item=>{
          if(param.includes(item.prop)){
            item.disabled = true
          }
        })
        // this.$refs.infos.basicConfig=JSON.parse(JSON.stringify(this.$refs.infos.basicConfig))
        this.$refs.infos.otherConfig.forEach(item=>{
          if(param.includes(item.prop)){
            item.disabled = true
          }
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
