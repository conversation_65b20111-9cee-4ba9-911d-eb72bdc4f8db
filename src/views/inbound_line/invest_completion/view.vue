<!-- 投资类完工查看页面 -->
<template>
  <div class="invest_completion_look page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <infos labelPosition="left"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
import {saveService} from "@/api/inbound_line/invest_completion_api.js"
export default {
  name: 'invest_completion_look',
  components:{infos},
  data() {
    return {

    }
  },
  created() {
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/inbound_line/invest_completion'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
