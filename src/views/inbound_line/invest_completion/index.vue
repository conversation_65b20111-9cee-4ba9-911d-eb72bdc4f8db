<!-- 投资类完工 -->
<template>
  <div class="invest_completion_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="集客专线投资类完工列表">
      <div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="tableHeader"
          :api="tableApi"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService, exportListService } from "@/api/inbound_line/invest_completion_api.js";
import { commonDown } from "@/utils/btn";
export default {
  name: "invest_completion_list",
  data() {
    return {
      formConfig: [
        {
          label: "集客专线名称",
          type: "input",
          fieldName: "linename",
        },
        {
          label: "计费编码",
          type: "input",
          fieldName: "linenumber",
        },
        // {
        //   label: "施工单位",
        //   type: "input",
        //   fieldName: "providerName",
        // },
        {
          label: "处理流水号",
          type: "input",
          fieldName: "serialno",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
      ],
      tableHeader: [
        {
          prop: "linename",
          label: "集客专线名称",
          align: "center",
          minwidth:200,
          tooltip:true
        },
        {
          prop: "linenumber",
          label: "计费编码",
          align: "center",
          minwidth:200,
          tooltip:true
        },
        {
          prop: "serialno",
          label: "处理流水号",
          align: "center",
          minwidth:200,
          tooltip:true
        },
        // {
        //   prop: "providerName",
        //   label: "施工单位",
        //   align: "center",
        //   minwidth:120
        // },
        {
          prop: "creatorName",
          label: "拟稿人",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "拟稿时间",
          align: "center",
          width:150,
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          width:120,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          width:120,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam:{}
    };
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标4是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.formConfig[3], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 查询
    search(param) {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(param)
      )
      this.$refs.table.getTableData(param);
    },

    // 重置
    reset(param) {
      this.search(param);
    },
    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/inbound_line/invest_completion/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/inbound_line/invest_completion/view",
            query: { boId: row.id },
          });
          break;
      }
    },
    exportMethod(){
      commonDown(this.staticSearchParam, exportListService);
    },
  },
};
</script>
