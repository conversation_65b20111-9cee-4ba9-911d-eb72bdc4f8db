<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="invest_completion_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="集客专线信息">
      <div slot="content">
        <mssForm
          ref="lineForm"
          labelWidth="200px"
          :config="lineConfig"
          :form="lineForm"
          :labelPosition="labelPosition"
          :disableForm="true"
        ></mssForm>
        <mssForm
          ref="otherForm"
          labelWidth="200px"
          :config="otherConfig"
          :labelPosition="labelPosition"
          :form="otherForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionListTwo" class="page-anchor-point" v-if="showlineTwo"></div>
    <mssCard title="集客专线信息" v-if="showlineTwo">
      <div slot="content">
        <mssForm
          ref="lineTwoForm"
          labelWidth="200px"
          :config="lineConfig"
          labelPosition="left"
          :form="lineTwoForm"
          :disableForm="true"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :fileTypeFlag="true"
      :dealPage="dealPage"
    ></mssAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
          v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      :dialog-width="dialogWidth"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      @confirm="projectConfirm"
      :dialogTitle="dialogTitle"
      :tableSingleChoice="true"
      :rowKey="rowKey"
      :tableQueryParams="tableQueryParams"
    ></mssTableSearchDialog>
  </div>
</template>

<script>
import { queryDetailService } from "@/api/inbound_line/invest_completion_api.js";
import {
  getProjectListService,
  getTaskListService,
} from "@/api/inbound_line/sorting_tank_api.js";
import Data from "../formSetting";
export default {
  name: "invest_completion_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return '96%'
      }else{
        return '70%'
      }
    }
  },
  data() {
    return {
      boId: "",
      businessType: "",
      nodeName: "草稿",
      dealPage: false,
      disableForm: false,
      projectTableApi: getProjectListService,
      searchFieldList: [],
      projectColumns: [],
      dialogTitle: "",
      rowKey:"",
      tableSingleChoice: true,
      tableQueryParams:{},
      basicConfig: [
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
          readonly: true,
          eventListeners: {
            focus: this.openProjectSelectDialog, // 打开选择项目的弹窗
          },
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "建设单位",
          type: "input",
          prop: "city",
          span: 12,
        },
        // {
        //   label: "施工单位",
        //   type: "input",
        //   prop: "projectCode",
        //   span: 12,
        // },
        {
          label: "任务编码",
          type: "input",
          prop: "taskCode",
          span: 12,
        },
        {
          label: "任务名称",
          type: "input",
          prop: "taskName",
          readonly: true,
          eventListeners: {
            focus: this.clickNative, // 打开选择项目的弹窗
          },
          span: 24,
        },

        {
          label: "拟稿单位",
          type: "input",
          prop: "applyOrgName",
          span: 12,
        },
        {
          label: "拟稿部门",
          type: "input",
          prop: "applyDeptName",
          span: 12,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "备注",
          type: "input",
          mode: "textarea",
          prop: "description",
          span: 24,
        },
      ],
      basicForm: {},
      lineConfig: JSON.parse(JSON.stringify(Data.lineSet)),
      lineForm: {},
      lineTwoForm: {},
      showlineTwo: false,
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "A端<br>信息",
          id: "sectionList",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
      otherConfig: [
        {
          label: "专线状态",
          type: "input",
          prop: "schedule",
          span: 12,
        },
        {
          label: "是否完成",
          type: "select",
          options: [
            { label: "是", value: true },
            { label: "否", value: false },
          ],
          prop: "ifdone",
          span: 12,
        },
        {
          label: "专线地址",
          type: "input",
          prop: "customerAddress",
          span: 24,
        },
        {
          label: "IRMS工单名",
          type: "input",
          prop: "irmsName",
          span: 12,
        },
        {
          label: "IRMS工单号",
          type: "input",
          prop: "irmsWorkoder",
          span: 12,
        },
        {
          label: "未完成原因",
          type: "input",
          mode: "textarea",
          prop: "nodoneReason",
          span: 24,
        },
        {
          label: "驳回原因",
          type: "input",
          mode: "textarea",
          prop: "irmsReason",
          span: 24,
        },
        {
          label: "退回原因",
          type: "input",
          mode: "textarea",
          prop: "attribute1",
          span: 24,
        },
        {
          label: "退回时间",
          type: "input",
          prop: "attribute2",
          span: 12,
        },
        {
          label: "是否免勘查开通",
          type: "select",
          options: [
            { label: "是", value: true },
            { label: "否", value: false },
          ],
          prop: "ifnodesign",
          span: 12,
        },
        {
          label: "发单时间",
          type: "input",
          prop: "sendDate",
          span: 12,
        },
        {
          label: "实际完工时间",
          type: "datePicker",
          prop: "completeDate",
          span: 12,
        },
      ],
      otherForm: {},
    };
  },
  created() {
    if (this.labelPosition == "left") {
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
      for (let i in this.otherConfig) {
        this.otherConfig[i].label = `${this.otherConfig[i].label}：`;
      }
      this.disableForm = true;
    } else {
      for (let i in this.basicConfig) {
        if (['1','2','3','5','6','7','8'].includes(i)) {
          this.basicConfig[i].disabled = true;
        }
      }
      for (let i in this.otherConfig) {
        if (i < 11) {
          this.otherConfig[i].disabled = true;
        }
      }
      this.dealPage = true;
    }
    for (let i in this.lineConfig) {
      this.lineConfig[i].label = `${this.lineConfig[i].label}：`;
    }
    // 浏览器传参
    this.boId = this.$route.query.boId;
    this.getDetailsData();
  },
  activated() {
    this.boId = this.$route.query.boId;
    this.getDetailsData();
  },
  methods: {
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.basicForm = res.data;
        this.lineForm = {
          ...res.data.detail,
          outsideNum:res.data.outsideNum,
          insideNum:res.data.insideNum
        };
        this.businessType = res.data.businessType;
        this.$emit("getWorkCode", this.businessType);
        this.otherForm = {
          ...res.data,
          ...res.data.detail,
        };
        if (res.data.detailOther) {
          this.lineTwoForm = {
            ...res.data.detailOther,
            outsideNum:res.data.outsideNum,
            insideNum:res.data.insideNum
          };
          this.pageAnchorConfig.splice(2, 0, {
            text: "Z端<br>信息",
            id: "sectionListTwo",
          });
          this.showlineTwo = true;
        }
      });
    },
    clickNative(param) {
      if(this.$refs.basicForm.modelForm.projectCode){
        this.openTaskDialog()
      }else{
        this.$message.warning('请先选择项目')
      }
    },
    openProjectSelectDialog() {
      this.projectTableApi = getProjectListService;
      this.searchFieldList = [
        {
          label: "项目名称",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          fieldName: "projectCode",
        },
      ];
      this.projectColumns = [
        {
          label: "项目名称",
          prop: "projectName",
          minWidth: 200,
        },
        {
          label: "项目编码",
          prop: "projectCode",
          minWidth: 100,
        },
        {
          label: "建设单位名称",
          prop: "orgName",
          minWidth: 200,
        },
      ];
      this.tableQueryParams={
          orgName:this.$refs.otherForm.modelForm.city
        }
      this.dialogTitle = "选择项目";
      this.rowKey="projectId"
      this.tableSingleChoice = true;
      this.$refs.projectNameSearchDialog.openDialog();
      this.type = '';
    },
    openTaskDialog() {
      this.projectTableApi = getTaskListService;
      this.searchFieldList = [
        {
          label: "任务名称",
          fieldName: "taskName",
        },
        {
          label: "任务编码",
          fieldName: "taskCode",
        },
        {
          label: "地市",
          fieldName: "city",
        },
        {
          label: "区县",
          fieldName: "county",
        },
      ];
      this.projectColumns = [
        {
          label: "任务名称",
          prop: "taskName",
          minWidth: 200,
        },
        {
          label: "任务编码",
          prop: "taskCode",
          minWidth: 100,
        },
        {
          label: "地市公司",
          prop: "city",
          minWidth: 200,
        },
        {
          label: "区县公司",
          prop: "county",
          minWidth: 100,
        },
      ];
      this.dialogTitle = "选择任务";
      this.tableSingleChoice = true;
      this.rowKey="taskId"
      this.tableQueryParams={
          projectCode:this.$refs.basicForm.modelForm.projectCode,
          city:this.$refs.otherForm.modelForm.city
        }
      this.$refs.projectNameSearchDialog.openDialog();
      this.type ='task';
    },
    projectConfirm(data) {
      if (data && data[0]){
        if(this.type){
          this.$refs.basicForm.modelForm = {
            ...this.$refs.basicForm.modelForm,
            taskName:data[0].taskName,
            taskCode:data[0].taskCode,
            taskId:data[0].taskId,
          };
        }else{
          this.$refs.basicForm.modelForm = {
            ...this.$refs.basicForm.modelForm,
            ...data[0],
            city: data[0].orgName,
          };
        }
      }

    },
  },
};
</script>

<style lang="scss" scoped>
</style>
