import { getProblemTemplateList, getProblemTemplate } from "@/api/pccw/review/index.js"

export default {
  data() {
    return {
      filteredData: [], // 问题列表-过滤
      allIssueOptions: [], // 问题列表-平级
      problemOptions: [], // 问题大类列表-树形
      smallIssueOptionsMap: {}, // 每个大类ID对应的小类选项
      subIssueOptionsMap: {}, // 每个大类ID对应的小类选项
    };
  },
  created() {
    this.loadProblemOptions(); // 初始化数据
  },
  methods: {
    // 刷新下级
    handleBigIssueChange(row, level) {
      // console.log("选择一级",row,level)
      if (level === 1) {
        row.problemSubcategories = null
        row.problemSegmentation = null
      } else if (level === 2) {
        row.problemSegmentation = null
      }
    },
    // 根据ID查找name
    findNameById(id) {
      const foundItem = this.filteredData.find(item => item.id === id);
      return foundItem ? foundItem.name : null; // 如果找到则返回name，否则返回null
    },
    // 根据name查找id
    findIdByName(name) {
      const foundItem = this.filteredData.find(item => item.name === name);
      return foundItem ? foundItem.id : null; // 如果找到则返回id，否则返回null
    },
    // 辅助函数：渲染下拉选择器
    renderSelect(name, value, options, placeholder) {
      return (
        <el-select v-model={value} placeholder={placeholder}>
              {options.map(item => (
                <el-option
                  key={item.id}
                  label={item.name}
                  value={item.id} // 使用ID作为value
                />
              ))}
            </el-select>
      );
    },
    // 辅助函数：根据parentId查找子选项
    findChildren(parentId, options) {
      return options.filter(option => option.parentId === parentId);
    },
    async loadProblemOptions() {
      // 加载问题大类列表.
      let q = {}
      const res = await getProblemTemplate(q)
      const res2 = await getProblemTemplateList(q)
      // console.log("映射0", res)
      if (res.code === '0000' && res2.code === '0000') {
        this.problemOptions = res.data.data
        this.filteredData = res2.data.map(item => ({
          id: item.id,
          name: item.name
        }));
        // console.log(this.filteredData);
        this.initSmallIssueOptionsMap(); // 加载完成后初始化小类选项映射
        this.initSubIssueOptionsMap(); // 加载完成后初始化小类选项映射
      }
    },
    initSmallIssueOptionsMap() {
      // 初始化小类选项映射
      this.smallIssueOptionsMap = {};
      // console.log("映射", this.problemOptions)
      this.problemOptions.forEach(bigIssue => {
        this.smallIssueOptionsMap[bigIssue.id] = bigIssue.children.map(smallIssue => ({
          label: smallIssue.name,
          value: smallIssue.id, // 使用ID作为value
        }));
      });
      // console.log("映射2", this.smallIssueOptionsMap)
    },
    initSubIssueOptionsMap() {
      // 初始化细分选项映射
      this.subIssueOptionsMap = {};
      this.problemOptions.forEach(bigIssue => {
        bigIssue.children.forEach(smallIssue => {
          this.subIssueOptionsMap[smallIssue.id] = smallIssue.children.map(subIssue => ({
            label: subIssue.name,
            value: subIssue.id, // 使用ID作为value
          }));
        });
      });
      // console.log("映射细分", this.subIssueOptionsMap)
    },
  }
  }
