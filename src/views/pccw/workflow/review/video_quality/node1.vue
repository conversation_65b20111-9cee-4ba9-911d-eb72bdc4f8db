<template>
  <mssCard :title="title">
    <div slot="headerBtn" v-if="node === nodeType&&approveType=='todo'">
      <el-button type="primary" @click="addTable">新增行</el-button>
      <el-button type="primary" @click="delBatch">删除行</el-button>
      <!-- 质检审核意见 -->
    </div>
    <div slot="content">
      <el-form ref="exemptionList">
        <mss-table
          :selectable="checkSelectable "
          :rowClassName="tableRowClassName"
          :stripe="true"
          :getChange="true"
          @getChange="handleGetChange"
          ref="table"
          selection
          :stationary="stationaryData"
          :pagination="false"
          :columns="tableHeader"
          :static-search-param="staticSearchParam"
        />
      </el-form>
    </div>
  </mssCard>
</template>

<script>
  import ProblemCategoryMixin from "@/views/pccw/workflow/review/mixins/ProblemCategoryMixin2.js"
  export default {
    props: {
      processOptions: Array, // 工序名称列表
      approveType: String,
      cardTitle: String,
      node: Number, // 节点名称
      nodeType: Number, // 引用组件的名称
      stationaryData: Array, // tableData.acceptanceReviewCommentsList 数据
      // tableHeader: Array,
      staticSearchParam: Object
      // problemOptions: Object,
    },
    mixins: [ProblemCategoryMixin],
    data() {
      return {
        title: this.cardTitle,
        nodeTable1: {
          prop: "acceptanceReviewComments",
          label: "验收审核意见",
          align: "left",
          tooltip: true,
          minWidth: "150px",
          formatter: row => {
            if (this.node != this.nodeType||this.approveType!='todo') {
              return (
                <span>
                  {row.acceptanceReviewComments == "1" ? "通过" : "不通过"}
                </span>
              )
            } else {
              return (
                <el-select
                  v-model={row.acceptanceReviewComments}
                  placeholder="请选择"
                >
                  <el-option label="通过" value={"1"} />
                  <el-option label="不通过" value={"2"} />
                </el-select>
              )
            }
          }
        },
        nodeTable2: {
          prop: "reviewOpinions",
          label: "验收整改复核意见",
          align: "left",
          tooltip: true,
          minWidth: "150px",
          formatter: row => {
            if(row.isShow&&row.isShow=='gray'){
              return (
                <span>
                  {row.reviewOpinions == "1" ? "通过" : "不通过"}
                </span>
              )
            }
            if (this.node != this.nodeType||this.approveType!='todo') {
              return (
                <span>{row.reviewOpinions == "1" ? "通过" : "不通过"}</span>
              )
            } else {
              return (
                <el-select v-model={row.reviewOpinions} placeholder="请选择">
                  <el-option label="通过" value={"1"} />
                  <el-option label="不通过" value={"2"} />
                </el-select>
              )
            }
          }
        },
        // 质检审核意见
        tableHeader: [
          {
            prop: "rectificationNotice",
            label: "是否下发整改通知",
            align: "left",
            tooltip: true,
            minWidth: "130px",
            formatter: row => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                    <span>{row.rectificationNotice == "1" ? "是" : "否"}</span>
                )
              }
              // 节点名称 和 组件名称相同
              if (this.node == this.nodeType&&this.approveType=='todo') {
                if (this.node===1&&row.acceptanceReviewComments === "1"||
                this.node===2&&row.reviewOpinions === "1"
                ) {
                  return <span>否</span>
                } else {
                  return (
                    <el-select
                      v-model={row.rectificationNotice}
                      placeholder="请选择"
                    >
                      <el-option label="是" value={"1"} />
                      <el-option label="否" value={"2"} />
                    </el-select>
                  )
                }
              } else {
                return (
                  <span>{row.rectificationNotice == "1" ? "是" : "否"}</span>
                )
              }
            }
          },
          {
            prop: "problemCategories",
            label: "问题大类",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column, cellValue, index) => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.problemCategories}</span>
                )
              }
              if (this.node == this.nodeType&&this.approveType=='todo'){
                if (this.node===1&&row.acceptanceReviewComments === "1"||
                this.node===2&&row.reviewOpinions === "1"
                ) {
                  return <span></span>
                } else {
                  // 查询，但是问题大类可能没值
                  if (this.problemOptions && this.problemOptions.length > 0) {
                    return (
                      <el-select
                        v-model={row.problemCategories}
                        placeholder="请选择"
                        onChange={() => this.handleBigIssueChange(row, 1)}
                      >
                        {this.problemOptions.map(item => (
                          <el-option
                            key={item.id}
                            label={item.name}
                            value={item.name}
                          />
                        ))}
                      </el-select>
                    )
                  } else {
                    // 先显示，后续会赋值
                    return (
                      <span>
                        <el-select
                          v-model={row.problemCategories}
                          placeholder="请选择"
                          onChange={() => this.handleBigIssueChange(row, 1)}
                        ></el-select>
                      </span>
                    )
                  }
                }
              } else {
                return <span>{row.problemCategories}</span>
              }
            }
          },
          {
            prop: "problemSubcategories",
            label: "问题小类",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column) => {
              if(row.isShow&&row.isShow=='gray'){
return <span>{row.problemSubcategories}</span>
              }
              if (this.node == this.nodeType&&this.approveType=='todo'){
                if (this.node===1&&row.acceptanceReviewComments === "1"||
                this.node===2&&row.reviewOpinions === "1"
                ) {
                  return <span></span>
                } else {
                  const bigIssueName = row.problemCategories
                  const bigIssueId = this.findIdByName(bigIssueName)
                  const smallIssueOptions = bigIssueId
                    ? this.smallIssueOptionsMap[bigIssueId]
                    : [] // 根据ID获取小类选项
                  return (
                    <el-select
                      v-model={row.problemSubcategories}
                      placeholder="请选择"
                      onChange={() => this.handleBigIssueChange(row, 2)}
                    >
                      {smallIssueOptions.map(item => (
                        <el-option
                          key={item.value}
                          label={item.label}
                          value={item.label}
                        />
                      ))}
                    </el-select>
                  )
                }
              } else {
                return <span>{row.problemSubcategories}</span>
              }
            }
          },
          {
            prop: "problemSegmentation",
            label: "问题细分",
            align: "left",
            tooltip: true,
            minWidth: "200px",
            formatter: (row, column) => {
              if(row.isShow&&row.isShow=='gray'){
              return <span>{row.problemSegmentation}</span>
                            }
              if (this.node == this.nodeType&&this.approveType=='todo'){
                if (this.node===1&&row.acceptanceReviewComments === "1"||
                this.node===2&&row.reviewOpinions === "1"
                ) {
                  return <span></span>
                } else {
                  console.log("问题细分",row.problemSubcategories)
                  const name = row.problemSubcategories
                  const id = this.findIdByName(name)
                  const subIssueOptions = id ? this.subIssueOptionsMap[id] : [] // 根据ID获取小类选项
                  console.log("subIssueOptions",subIssueOptions)
                  if (subIssueOptions) {
                    return (
                      <el-select
                        v-model={row.problemSegmentation}
                        placeholder="请选择"
                      >
                        {subIssueOptions.map(item => (
                          <el-option
                            key={item.value}
                            label={item.label}
                            value={item.label}
                          />
                        ))}
                      </el-select>
                    )
                  } else {
                    return (
                      <el-select
                        v-model={row.problemSegmentation}
                        placeholder="请选择"
                      ></el-select>
                    )
                  }
                }
              } else {
                return <span>{row.problemSegmentation}</span>
              }
            }
          },
          {
            prop: "severity",
            label: "严重程度",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column, cellValue, index) => {
             if(row.isShow&&row.isShow=='gray'){
             return <span>{row.severity}</span>
                           }
              if (this.node == this.nodeType&&this.approveType=='todo'){
                if (this.node===1&&row.acceptanceReviewComments === "1"||
                this.node===2&&row.reviewOpinions === "1"
                ) {
                  return <span></span>
                } else {
                  return (
                    // <el-form-item prop={`importanceLevel${index}`}>
                    <el-select
                      v-model={row.severity}
                      onChange={value => {
                        // this.form[`severity{index}`] = value;
                      }}
                    >
                      <el-option label={"一般问题"} value={"一般问题"} />
                      <el-option label={"次严重问题"} value={"次严重问题"} />
                      <el-option
                        label={"较为严重问题"}
                        value={"较为严重问题"}
                      />
                      <el-option label={"严重问题"} value={"严重问题"} />
                      <el-option label={"特别严重"} value={"特别严重"} />
                    </el-select>
                  )
                }
              } else {
                return <span>{row.severity}</span>
              }
            }
          },
          {
            prop: "processName",
            label: "工序名称",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column) => {
              if(row.isShow&&row.isShow=='gray'){
              return <span>{row.processName}</span>
                            }
              if (this.node == this.nodeType&&this.approveType=='todo'){
                if (this.node===1&&row.acceptanceReviewComments === "1"||
                this.node===2&&row.reviewOpinions === "1"
                ) {
                  return <span></span>
                } else {
                  return (
                    <el-select v-model={row.processName} placeholder="请选择">
                      {this.processOptions.map(item => (
                        <el-option
                          key={item.value}
                          label={item.label}
                          value={item.value}
                        />
                      ))}
                    </el-select>
                  )
                }
              } else {
                return <span>{row.processName}</span>
              }
            }
          },
          {
            prop: "rectificationDeadline",
            label: "整改期限",
            align: "left",
            minWidth: "150px",
            formatter: row => {
              if(row.isShow&&row.isShow=='gray'){
              return <span>{row.rectificationDeadline}</span>
                            }
              if (this.node == this.nodeType&&this.approveType=='todo'){
                if (this.node===1&&row.acceptanceReviewComments === "1"||
                this.node===2&&row.reviewOpinions === "1"
                ) {
                  return <span></span>
                } else {
                  return (
                    <el-date-picker
                      v-model={row.rectificationDeadline}
                      type="date"
                      placeholder="选择日期"
                      style="width: 100%;"
                      value-format="yyyy-MM-dd"
                    ></el-date-picker>
                  )
                }
              } else {
                return <span>{row.rectificationDeadline}</span>
              }
            }
          },
          {
            prop: "rectificationStatus",
            label: "整改状态",
            align: "left",
            tooltip: true,
            formatter: row => {
              if(row.isShow&&row.isShow=='gray'){
              return <span>{row.rectificationStatus}</span>
                            }
              if (this.node == this.nodeType&&this.approveType=='todo'){
                return <span></span>
              } else {
                return (
                  <span>
                    {row.rectificationStatus}
                  </span>
                )
              }
            }
          },
          {
            prop: "completionTime",
            label: "整改完成时间",
            align: "left",
            tooltip: true,
            formatter: row => {
              if(row.isShow&&row.isShow=='gray'){
              return <span>{row.completionTime}</span>
                            }
              if (this.node == this.nodeType&&this.approveType=='todo'){
                return <span></span>
              } else {
                return <span>{row.completionTime}</span>
              }
            }
          }
        ]
      }
    },
    created() {


      //
      if (this.nodeType === 1) {
        this.tableHeader.unshift(this.nodeTable1)
      }
      if (this.nodeType === 2) {
        this.tableHeader.unshift(this.nodeTable2)
      }
    },
    methods: {
      // this.stationaryData 为需要置灰的数据列表
      checkSelectable (row) {
        if(this.nodeType === 2&&row.isShow&&row.isShow=='gray'){
        	return false
        } else {
        	return true
        }
      },
      tableRowClassName ({row, rowIndex}) {
            if(this.nodeType === 2&&row.isShow&&row.isShow=='gray'){
      		return 'disabledRow'
      	} else {
      		return ''
      	}
      },
      addTable() {
        this.$emit("addTable") // 触发父组件的 addTable 事件
      },
      delBatch() {
        this.$emit("delBatch") // 触发父组件的 delBatch 事件
      },
      handleGetChange(value) {
        this.$emit("getChange", value) // 触发父组件的 getChange 事件并传递参数
      }
    }
  }
</script>

<style lang="scss" scoped>
::v-deep .disabledRow {
	cursor: not-allowed;
	pointer-events: none;
	color: #ccc; // 改当前行的字体颜色

  .el-checkbox__inner{
    // color: blue;
  }
}

</style>
