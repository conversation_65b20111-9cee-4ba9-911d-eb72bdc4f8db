<template>
  <div>
    <mssCard :title="title">
      <div slot="headerBtn">
        <el-button @click="$router.go(-1)">返回</el-button>
      </div>
      <div slot="content" v-if="searchParam">
        <mssTable
          ref="table"
          selection
          :api="api"
          :columns="columns"
          :stationary="stationary"
          :static-search-param="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import qs from "qs"
  import {
    getMsgList,
    getRectificationFrontList,
    getRectificationList,
    getRectificationOverdueList
  } from "@/api/pccw/review/video_check/index.js"
  export default {
    props: {
      searchParam: null
    },
    data() {
      return {
        title: "视频验收抽查工单详情",
        // 静态列表的参数
        staticSearchParam: {
          id: ""
        },
        searchConfig: [],
        api: getMsgList,
        columns: [
          {
            prop: "workOrderType",
            label: "工单类型",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "constructionUnit",
            label: "建设单位",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "userName",
            label: "用户名称",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "workOrderNumber",
            label: "工单编号",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "projectNumber",
            label: "项目编号",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "taskNumber",
            label: "任务编号",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "rectificationDeadline",
            label: "整改期限",
            align: "center",
            tooltip: true,
            width: 200
          }
        ],
        stationary: null //静态数据
      }
    },
    created() {
      // this.initial()
      // this.table = getRectificationFrontList
      const query = this.$route.query
      console.log("msg", query)
      const type = query.type
      console.log("type", type)
      const id = query.id
      this.staticSearchParam.id = id
      console.log("id", id)
      // switch (type) {
      //   case '1':
      //     this.api = getRectificationFrontList;
      //     break
      //   case '2':
      //     this.api = getRectificationList;
      //     break
      //   case '3':
      //     this.api = getRectificationOverdueList;
      //     break
      // }
    },
    methods: {}
  }
</script>
