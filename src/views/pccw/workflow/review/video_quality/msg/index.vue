<template>
  <div class="MessageDetails" v-loading="pageLoading">
    <div style="margin-top: 30px"></div>
    <mssCard title="消息详情">
      <div slot="content">
        <template v-if="condition === '1'">
          {{ msgData.content }}
        </template>
      </div>
    </mssCard>
    <div v-if="flag"> </div>
    <msgTable :searchParam="msgData"></msgTable>
  </div>
</template>

<script>
  import msgTable from "./table/index.vue"
  export default {
    components: {
      msgTable
    },
    name: "MessageDetails",
    data() {
      return {
        msgData: {
          content: ""
        },
        condition: "1",
        daysLeft: "", // 剩余日期
        systemDate: "", // 系统日期
        department: "", // 同一部门的所有待办人
        flag: false,
        searchParam: {},
        showTitle: true,
        closeData: "",
        formattedDate: "",
        id: "",
        messageType: "",
        messageTitle: "",
        pageLoading: false
      }
    },
    created() {
      // 浏览器传参
      const query = this.$route.query
      console.log(query)
      this.msgData = query
      // this.getData()
    },
    methods: {
      getData() {}
    }
  }
</script>

<style scoped lang="scss">
  ::v-deep .msg-url {
    text-decoration: underline;
    color: #02a7f0;
  }

  .MessageDetails {
    .message-title {
    }
  }
</style>
