<template>
  <mss-card title="省公司审核意见">
    <div slot="headerBtn" v-if="node === 3&&flag">
      <el-button type="primary" @click="addTable">新增行</el-button>
      <el-button type="primary" @click="delBatch">删除行</el-button>
    </div>
    <div slot="content">
      <el-form ref="exemptionList">
        <mss-table
          :getChange="true"
          @getChange="handleGetChange"
          ref="table"
          selection
          :stationary="stationaryData"
          :pagination="false"
          :columns="tableHeader"
          :static-search-param="staticSearchParam"
        />
      </el-form>
    </div>
  </mss-card>
</template>

<script>
  import { getProblemClassification } from "@/api/pccw/review/index.js"

  export default {
    props: {
      node: Number,
      stationaryData: Array, // tableData.qualityOpinionList 数据
      // tableHeader: Array,
      staticSearchParam: Object
    },
    data() {
      return {
        flag: true,
        tableHeader: [
          {
            prop: "questionClassification",
            label: "问题分类",
            align: "left",
            tooltip: true,
            formatter: (row, column) => {
              if (this.flag) {
                return (
                  <el-select
                    v-model={row.questionClassification}
                    placeholder="请选择"
                  >
                    {this.problemclassificationList.map(item => (
                      <el-option
                        key={item.value}
                        label={item.label}
                        value={item.value}
                      />
                    ))}
                  </el-select>
                )
              } else {
                return <span>{row.questionClassification}</span>
              }
            }
          },
          {
            prop: "problemDescription",
            label: "问题说明",
            align: "left",
            formatter: row => {
              if (this.flag) {
                return <el-input v-model={row.problemDescription}></el-input>
              } else {
                return <span>{row.problemDescription}</span>
              }
            }
          },

          {
            prop: "rectificationDeadline",
            label: "整改期限",
            align: "left",
            formatter: row => {
              if (this.flag) {
                return (
                  <span>
                    <span>
                      <el-date-picker
                        v-model={row.rectificationDeadline}
                        type="date"
                        placeholder="选择日期"
                        style="width: 100%;"
                        value-format="yyyy-MM-dd"
                      ></el-date-picker>
                    </span>
                  </span>
                )
              } else {
                return <span>{row.rectificationDeadline}</span>
              }
            }
          }
        ],
        problemclassificationList: []
      }
    },
    created() {
      // 获取问题分类
      this.getProblem()

     let approveType = decodeURIComponent(this.$route.query.type) // 获取类型信息
      if (approveType != "todo"||this.node!=3) {
        this.flag = false
      }
    },
    methods: {
      async getProblem() {
        let query = {
          limit: "1000",
          page: "1"
        }
        let res = await getProblemClassification(query)
        console.log("问题分类", res.data)
        // this.processOptions = res.data
        // this.processOptions = [
        //   { label: "工程建设中心", value: "工程建设中心" },
        //   { label: "网络部", value: "网络部" }
        // ]
        // this.processOptions.push("添加测试");
        this.problemclassificationList = this.convertData(res.data.data)
        console.log("问题分类", this.problemclassificationList)
        // const list = res.data
        // this.$set(this.tableHeader[6], 'processOptions', list)
      },
      convertData(dataArray) {
        return dataArray.map(item => ({
          label: item.problemClassification,
          value: item.problemClassification
        }))
      },
      addTable() {
        this.$emit("addTable") // 触发父组件的 addTable 事件
      },
      delBatch() {
        this.$emit("delBatch") // 触发父组件的 delBatch 事件
      },
      handleGetChange(value) {
        this.$emit("getChange", value) // 触发父组件的 getChange 事件并传递参数
      }
    }
  }
</script>
