<template>
  <div>
    <flow-detail
      ref="flow"
      :flow="flow"
      :showTransact="true"
      :showApproval="showApproval"
      :showReturn="showReturn"
      :type="approveType"
      @submit-approval="handleApprovalSubmission"
      :ignoreTypeCheck="false"
    >
      <template v-slot:custom-buttons>
        <!-- <el-button type="primary" @click="save()">保存</el-button> -->
        <!-- 自定义退回 -->
        <template>
          <el-button
            v-show="
              node === 3 && approveType == 'todo' && !flow.name.includes('退回')
            "
            type="primary"
            @click="returnReview()"
            >退回</el-button
          >
          <el-button

            v-show="!showApproval&&approveType == 'todo'"
            type="primary"
            @click="submitTask()"
            >提交</el-button
          >
        </template>
      </template>
      <div slot="content">
        <div v-if="node != 5">
          <!-- 基础信息 -->
          <mssCheckForm
            title="视频验收整改复核工单-基础信息"
            ref="searchForm"
            :form="headData"
            :search-config="headConfig"
          ></mssCheckForm>
          <!-- 第一节点 -->
          <Node1
            cardTitle="验收审核"
            :nodeType="Number(1)"
            :approveType="approveType"
            :node="node"
            :processOptions="processOptions"
            :stationary-data="tableData.qualityOpinionList"
            :table-header="tab"
            :static-search-param="staticSearchParam"
            @addTable="addTable"
            @delBatch="delBatch"
            @getChange="handleGetChange"
          />
          <!-- 第二节点 -->
          <div v-if="node === 2 || node === 3 || node === 4">
            <Node1
              cardTitle="验收整改复核"
              :nodeType="Number(2)"
              :approveType="approveType"
              :node="node"
              :processOptions="processOptions"
              :stationary-data="tableData.rectificationOpinionList"
              :table-header="tab"
              :static-search-param="staticSearchParam"
              @addTable="addTable"
              @delBatch="delBatch"
              @getChange="handleGetChange"
            />
          </div>
          <!-- 第三节点 -->
          <div v-if="node === 3 || node === 4">
            <Node3
              :node="node"
              :stationary-data="tableData.provinceRecOpinionList"
              :table-header="tab"
              :static-search-param="staticSearchParam"
              @addTable="addTable"
              @delBatch="delBatch"
              @getChange="handleGetChange"
            />
          </div>
          <mssCard title="附件信息">
            <div slot="headerBtn">
              <template>
                <el-upload
                  class="upload-demo"
                  action="#"
                  :http-request="uploadNewFile"
                  :show-file-list="false"
                >
                  <el-button
                    slot="trigger"
                    size="small"
                    type="primary"
                    v-if="approveType == 'todo' && node != 3"
                    >选择文件</el-button
                  >
                </el-upload>
              </template>
            </div>
            <div slot="content">
              <!-- 附件信息 -->
              <mssTable
                ref="table"
                :pagination="false"
                :columns="fileInfo"
                :stationary="fileInfoList"
                :static-search-param="staticSearchParam"
              />
            </div>
          </mssCard>
        </div>
      </div>
    </flow-detail>
  </div>
</template>

<script>
  // 组件
  import Node1 from "@/views/pccw/workflow/review/video_quality/node1.vue"
  import Node3 from "@/views/pccw/workflow/review/video_quality/node3.vue"
  import { headConfigData } from "./index.js"
  import ProblemCategoryMixin from "@/views/pccw/workflow/review/mixins/ProblemCategoryMixin.js"
  import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue"
  import {
    getProblemTemplateList,
    getProblemTemplate,
    getProblemClassification
  } from "@/api/pccw/review/index.js"
  import {
    getReviewWorkOrder,
    queryProcessNameByTemplate,
    queryReviewWorkOpinion,
    importReviewWorkOrderFile,
    submitReviewWorkOrder
  } from "@/api/pccw/review/process_quality/index.js"
  import {
    getVideoAcceptance, // 查询
    getRectificationTasks,
    getReviewComments, // 查询省公司审核意见
    deleteRectificationTasks, // 删除意见
    deleteReviewComments,
    addRectificationTasks,
    addReviewComments,
    deleteFile,
    downloadFile,
    getFileList,
    uploadVideo,
    checkFile,
    returnTask,
    returnFlow,
    submit
  } from "@/api/pccw/review/video_check/index.js"
  export default {
    components: {
      FlowDetail,
      Node1,
      Node3
    },
    mixins: [ProblemCategoryMixin],
    data() {
      return {
        showApproval: false,
        showReturn: false,
        allowApproval: false,
        tab: "",
        node: 5, //节点
        // 勾选的数据
        selection: {
          noIds: [], // 没有id的数组
          ids: [], // 通用删除数组
          qualityOpinionList: [], // 质检审核意见
          rectificationOpinionList: [], // 整改复核意见
          provinceRecOpinionList: [] // 省公司审核意见
        },
        // 表格数据
        tableData: {
          qualityOpinionList: [], // 质检审核意见
          rectificationOpinionList: [], // 整改复核意见
          provinceRecOpinionList: [] // 省公司审核意见
        },
        // 搜索静态条件
        staticSearchParam: {
          businessId: ""
        },
        staticSearchForm: {},
        tableApi: getReviewWorkOrder,
        // 文件信息
        fileInfo: [
          {
            prop: "fileType",
            label: "附件类型",
            align: "center",
            tooltip: true
          },
          {
            prop: "fileName",
            label: "文件名",
            align: "center",
            tooltip: true
          },
          {
            prop: "createTime",
            label: "上传时间",
            align: "center",
            tooltip: true
          },
          {
            prop: "createName",
            label: "上传人",
            align: "center",
            tooltip: true
          },
          {
            label: "操作",
            align: "center",
            fixed: "right",
            minWidth: "60px",
            formatter: row => {
              const flag = this.approveType === "todo" && this.node != 3
              return (
                <span>
                  <span
                    class="table_btn mr10"
                    onClick={() => {
                      this.downloadRow(row)
                    }}
                  >
                    下载
                  </span>
                  {flag && (
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.delRow(row)
                      }}
                    >
                      删除
                    </span>
                  )}
                </span>
              )
            }
          }
        ],
        // 头表数据
        headData: {},
        //表头字段配置
        headConfig: headConfigData,
        // 流程的
        flow: {},
        approveType: "",
        showTransact: false,
        businessId: "",
        fileList: [],
        fileInfoList: [],
        processTemplateName: null, // 工序模板名称
        processOptions: [], // 问题大类
        returnApi:'returnTask'
      }
    },
    created() {
      // 流程的
      this.approveType = decodeURIComponent(this.$route.query.type) // 获取类型信息
      console.log(
        "this.approveTye",
        this.approveType,
        this.approveTye != "todo"
      )
      if (this.approveType != "todo") {
        // this.fileInfo.pop()
      }

      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow) // 获取流程对象
      this.businessId = this.flow.businessId // 根据这个去后端查数据
      this.staticSearchParam.businessId = this.businessId
      console.log("流程数据",this.flow)
      this.setNode() // 设置节点信息
      this.getFileInfo() // 查询文件
      this.getInfo() // 基础信息,工序名称列表查询
      this.getReviewWorkOpinion(3) // 审核意见-查询  三个list
    },
//     mounted() {
//       if(this.flow.flowKey===
// "provincial_video_check_flow"
//       &&this.flow.nodeName==="被抽查人"){
//         this.showApproval=true
//       }
//     },
    methods: {
      // 点击退回按钮，只调用了一个接口
      returnReview() {
        this.$confirm("此操作将退回流程, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(async () => {
        let f3 = this.tableData.provinceRecOpinionList.map(item => ({
          ...item,
          businessId: this.businessId
        }))
        let query = {
          businessId: this.businessId,
          reviewCommentsLine: f3,
          taskId: this.flow.taskId
        }

        let res = await returnFlow(query)
        // let res=null
        // if(this.returnApi === 'return'){
        //   res = await returnFlow(query)
        // }else{
        //    res = await returnTask(query)
        // }

        console.log("返回", res)
        if (res.code === "0000") {
          this.$message.success("退回成功!")
          // 跳转页面并刷新
          this.$router
            .push({
              path: "/home"
            })
            .then(() => {
              this.$router.go(0) // 刷新当前页面
            })
        } else {
          this.$message.error("退回失败!")
        }
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消退回"
            })
          })
      },
      // 新提交方法
      async submitTask() {
        // 处理退回单据

        console.log(this.flow)
        // 注意，workflowName前面有个空格
        if (
          this.flow.workflowName === " 视频验收-省公司" &&
          this.flow.nodeName === "被抽查人"&&
          this.fileInfoList.length<1
        ) {
          this.$message.error("还未上传文件!")
          return
        }

        const saveResult = await this.save()
        console.log("save函数的返回值", saveResult)
        if (saveResult) {
          let query = "businessId=" + this.businessId + "&type=" + this.node
          // 查询是否能提交
          const res = await checkFile(query)
          if (res.code === "0000") {
            let q = {
              businessId: this.businessId,
              processStatus: "20",
              taskId: this.flow.taskId
            }
            const submitRes = await submit(q)
            if (submitRes.code === "0000") {
              this.$message.success("提交成功!")
              // 跳转页面并刷新
              this.$router
                .push({
                  path: "/home"
                })
                .then(() => {
                  this.$router.go(0) // 刷新当前页面
                })
            } else {
              this.$message.error("提交失败!")
            }
          } else {
            this.$message.error("不允许提交!")
          }
        } else {
          this.$message.error("保存失败!")
        }
      },
      // 处理提交事件的方法
      async handleApprovalSubmission() {
        this.$refs.flow.allowApproval = false
        this.$refs.flow.$refs.approvalDialog.approvalDialog = false
        // 先保存
        try {
          const saveResult = await this.save()
          console.log("save函数的返回值", saveResult)
          if (saveResult) {
            this.handleApprovalSubmission2()
          }
        } catch (error) {
          return
        }
      },
      // 处理提交事件的方法
      async handleApprovalSubmission2() {
        let query = "businessId=" + this.businessId + "&type=" + this.node
        // 查询是否能提交
        const res = await checkFile(query)
        if (res.code === "0000") {
          this.$refs.flow.allowApproval = true
          this.$refs.flow.$refs.approvalDialog.approvalDialog = true
        } else {
          this.$refs.flow.allowApproval = false
          this.$refs.flow.$refs.approvalDialog.approvalDialog = false
        }
      },
      setNode() {
        name = this.flow.name
        if (name.includes("视频验收抽查单据")) {
          this.node = 1
        } else if (name.includes("审核不通过退回单据")) {
          if (this.flow.nodeName == "工程管理经理主抽查") {
            this.showReturn = true
            this.node = 3
          } else {
            this.node = 4
          }
        } else if (name.includes("省公司抽查审核单据")) {
          this.node = 3
        } else if (name.includes("整改完成复核单据")) {
          this.node = 2
        } else {
          this.node = 5
        }
        console.log("抽查节点", this.node)
      },
      selectProblem(val) {
        console.log("val", val)
      },
      formatter(row, column, cellValue, index) {
        console.log(11111, row, column, cellValue, index)
        if (cellValue === "是") {
          return "是"
        } else {
          return "否"
        }
      },
      async getFileInfo() {
        let q = {
          businessId: this.businessId
        }
        const res = await getFileList(q)
        console.log("查询文件结果", res.data)
        this.fileInfoList = res.data
      },
      async uploadNewFile(file) {
        console.log("上传", file)
        let formData = new FormData()
        formData.append("file", file.file)
        formData.append("businessId", this.businessId)
        formData.append("fileType", "整改支撑附件")
        const res = await uploadVideo(formData)

        console.log("上传结果2", res)
        if (res.code == "0000") {
          // 刷新附件信息
          this.getFileInfo()
        }
      },
      addTable(node) {
        console.log("数据测试123", this.tableData)
        let q = {
          // acceptanceReviewComments: "1", // 通过
          // reviewOpinions: "1", // 通过
          // rectificationNotice: "2", // 不下发整改通知
          acceptanceReviewComments: "", // 通过
          reviewOpinions: "", // 通过
          rectificationNotice: "", // 不下发整改通知
          problemCategories: "",
          problemSubcategories: "",
          problemSegmentation: "",
          severity: "",
          processName: "",
          rectificationDeadline: "",
          rectificationStatus: "",
          completionTime: ""
        }
        let q3 = {}
        switch (this.node) {
          case 1:
            this.tableData.qualityOpinionList.push(q)
            break
          case 2:
            this.tableData.rectificationOpinionList.push(q)
            break
          case 3:
            this.tableData.provinceRecOpinionList.push(q3)
            break
          default:
        }

        console.log("this.tableData", this.tableData)
      },
      // 保存或修改
      async save() {
        let q = {}
        console.log("this.tableData", this.tableData)
        // 复制原始的 item 对象，修改判断部分属性
        switch (this.node) {
          case 1:
            let f = this.tableData.qualityOpinionList.map(item => {
              const shouldClearFields = item.acceptanceReviewComments === "1"
              return {
                ...item,
                businessId: this.businessId,
                type: 1,
                rectificationNotice: shouldClearFields
                  ? "2"
                  : item.rectificationNotice,
                problemCategories: shouldClearFields
                  ? ""
                  : item.problemCategories,
                problemSubcategories: shouldClearFields
                  ? ""
                  : item.problemSubcategories,
                problemSegmentation: shouldClearFields
                  ? ""
                  : item.problemSegmentation,
                severity: shouldClearFields ? "" : item.severity,
                processName: shouldClearFields ? "" : item.processName,
                rectificationDeadline: shouldClearFields
                  ? ""
                  : item.rectificationDeadline
              }
            })
            q = f
            break
          case 2:
            let f2 = this.tableData.rectificationOpinionList.map(item => {
              const shouldClearFields2 = item.reviewOpinions === "1"
              return {
                ...item,
                businessId: this.businessId,
                type: 2,
                rectificationNotice: shouldClearFields2
                  ? "2"
                  : item.rectificationNotice,
                problemCategories: shouldClearFields2
                  ? ""
                  : item.problemCategories,
                problemSubcategories: shouldClearFields2
                  ? ""
                  : item.problemSubcategories,
                problemSegmentation: shouldClearFields2
                  ? ""
                  : item.problemSegmentation,
                severity: shouldClearFields2 ? "" : item.severity,
                processName: shouldClearFields2 ? "" : item.processName,
                rectificationDeadline: shouldClearFields2
                  ? ""
                  : item.rectificationDeadline
              }
            })
            q = f2
            break
          case 3:
            let f3 = this.tableData.provinceRecOpinionList.map(item => ({
              ...item,
              businessId: this.businessId
            }))
            q = f3
            break
          case 4:
            q = []
            break
          default:
            q = []
        }
        console.log(q)
        // type为1：验收审核意见；type为2：验收整改复核意见
        let res = null
        if (this.node === 1 || this.node === 2) {
          res = await addRectificationTasks(q)
        } else {
          res = await addReviewComments(q) // 省公司意见
        }
        if (res.code === "0000") {
          this.$message.success("保存成功!")
          this.getReviewWorkOpinion(this.node) // 刷新信息
          return true
        } else {
          this.$message.error("保存失败！")
          return false
        }
        console.log("保存结果", res)
      },
      // 获取整改任务
      // 全查，传3
      async getReviewWorkOpinion(type) {
        console.log("查询类型", type)
        // 1质检审核意见 2整改复核意见 3省公司审核意见
        if (type != 2) {
          let query = {
            businessId: this.businessId,
            type: "1"
          }
          let res = await getRectificationTasks(query)
          // console.log("this.质检审核意见", res)
          if (res.code === "0000") {
            this.tableData.qualityOpinionList = res.data
          }
        }
        if (type === 1) return
        let query2 = {
          businessId: this.businessId,
          type: "2"
        }
        let res2 = await getRectificationTasks(query2)
        console.log("this.res2", res2)
        if (res2.code === "0000") {
          this.tableData.rectificationOpinionList = res2.data
        }

        if (type === 2) return
        // 3、获取省公司审核意见
        let query3 = {
          businessId: this.businessId
        }
        let res3 = await getReviewComments(query2)
        console.log("this.res3", res3)
        if (res3.code === "0000") {
          this.tableData.provinceRecOpinionList = res3.data
        }
      },
      // 工序名称列表查询
      async getProcessName() {
        console.log("this.processTemplateName", this.processTemplateName)
        let query = {
          processName: this.processTemplateName
        }
        let res = await queryProcessNameByTemplate(query)
        // 使用函数转换数据
        this.processOptions = this.convertData(res.data)
      },
      convertData(dataArray) {
        return dataArray.map(item => ({
          label: item,
          value: item
        }))
      },
      // 删除文件行
      async delRow(row) {
        console.log(row)
        let q = {
          kbId: row.kbId
        }
        const res = await deleteFile(q)
        if (res.code == "0000") {
          this.getFileInfo() // 刷新附件信息
        }
      },
      // 下载文件行
      async downloadRow(row) {
        console.log(row)
        const res = await downloadFile(row.kbId)
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", row.fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      },
      // 删除多行
      async delBatch() {
        console.log(this.selection)
        if (this.selection.ids.length < 1 && this.selection.noIds.length < 1) {
          this.$message.error("未选择数据")
          return
        }
        // 移除没保存的数据
        switch (this.node) {
          case 1:
            // 从大到小遍历删除
            this.selection.noIds
              .sort((a, b) => b - a)
              .forEach(index => {
                this.tableData.qualityOpinionList.splice(index, 1)
              })
            break
          case 2:
            this.selection.noIds
              .sort((a, b) => b - a)
              .forEach(index => {
                this.tableData.rectificationOpinionList.splice(index, 1)
              })
            break
          case 3:
            this.selection.noIds
              .sort((a, b) => b - a)
              .forEach(index => {
                this.tableData.provinceRecOpinionList.splice(index, 1)
              })
            break
          default:
        }
        // 不用请求后端
        if (!this.selection.ids || this.selection.ids.length < 1) {
          return
        }
        let res = null
        if (this.node === 3) {
          res = await deleteReviewComments(this.selection.ids)
        } else {
          res = await deleteRectificationTasks(this.selection.ids)
        }
        console.log("删除结果", res.code)

        if (res.code != "0000") return

        this.getReviewWorkOpinion(this.node) // 刷新信息
        this.$message.success("删除成功!")
        // 删除之前保存的选择
        this.selection.ids = []
      },
      // 处理选择的数组
      handleGetChange(selection) {
        // 获取到子组件通过 $emit 传递的数据
        console.log("selection", selection)

        // 提取id并组成字符串
        // 过滤出具有 id 属性的对象
        let itemsWithId = selection.filter(
          item => item.id !== undefined && item.id !== null
        )

        // 判断过滤后的数组长度是否大于 1，然后进行连接操作
        let idString = ""
        if (itemsWithId.length > 1) {
          idString = itemsWithId.map(item => item.id).join(",")
        } else if (itemsWithId.length === 1) {
          idString = itemsWithId[0].id.toString() // 如果只有一个元素，直接取其 id
        }

        this.selection.ids = idString
        console.log("idString", idString)

        // 遍历勾选中的数据，获取索引值
        // 所有数据
        // let selectedRows = selection.map(row =>
        //   this.tableData.provinceRecOpinionList.indexOf(row)
        // )
        // console.log("selectedRows", selectedRows)
        // 过滤掉有id的
        this.selection.noIds = selection
          .map(row => {
            if (!row.id) {
              // 三个情况
              return this.tableData.provinceRecOpinionList.indexOf(row)
            } else {
              return null // 如果有 id 值，返回 null
            }
          })
          .filter(index => index !== null) // 过滤掉有 id 值的行的索引

        console.log("this.selection.noIds", this.selection.noIds)
        // 移除元素
      },
      // 基础信息
      async getInfo() {
        let query = {
          businessId: this.businessId
        }
        let res = await getVideoAcceptance(query)
        this.headData = res.data
        let receiptStatus = res.data.receiptStatus
        console.log("res.data.receiptStatus", res.data.receiptStatus)
        switch (receiptStatus) {
          case "1":
            res.data.receiptStatus = "草稿"
            break
          case "2":
            res.data.receiptStatus = "审批中"
            break
          case "3":
            res.data.receiptStatus = "退回"
            break
          case "4":
            res.data.receiptStatus = "审批完成"
            break
          default:
            break
        }

        // 退回api判断
        let backCount = res.data.backCount
        if(backCount&&backCount>0){
          this.returnApi = 'returnTask '
        }else if(backCount&&backCount===0){
          this.returnApi = 'return'
        }else{
          this.returnApi = 'returnTask '
        }

        this.processTemplateName = res.data.processTemplateName
        console.log("this.headData", res.data)
        console.log("this.headData2", this.headData)
        this.getProcessName()
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-form-item__label {
    text-align: left;
  }
</style>
