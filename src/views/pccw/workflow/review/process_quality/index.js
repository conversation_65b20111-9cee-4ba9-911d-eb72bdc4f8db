        //表头字段配置
export const headConfigData = [
          {
            label: "登录人",
            type: "input",
            fieldName: "userName"
          },
          {
            label: "建设单位",
            type: "input",
            fieldName: "constructUnit"
          },
          {
            label: "工序模板名称",
            type: "input",
            fieldName: "processTemplateName"
          },
          {
            label: "接收时间",
            type: "input",
            fieldName: "acceptanceTime"
          },
          {
            label: "工单编号",
            type: "input",
            fieldName: "workOrderNumber"
          },
          {
            label: "工单类型",
            type: "input",
            fieldName: "workOrderType"
          },
          {
            label: "项目编号",
            type: "input",
            fieldName: "projectNumber"
          },
          {
            label: "项目名称",
            type: "input",
            fieldName: "projectName"
          },
          {
            label: "任务编号",
            type: "input",
            fieldName: "taskNumber"
          },
          {
            label: "任务名称",
            type: "input",
            fieldName: "taskName"
          },
          {
            label: "施工单位",
            type: "input",
            fieldName: "constructionUnit"
          },
          {
            label: "创建时间",
            type: "input",
            fieldName: "createTime"
          },
          {
            label: "所属区域",
            type: "input",
            tooltip: true,
            fieldName: "affiliatedRegion"
          },
          {
            label: "区县",
            type: "input",
            fieldName: "district"
          },
          {
            label: "专业",
            type: "input",
            fieldName: "discipline"
          },
          {
            label: "流程状态",
            type: "input",
            fieldName: "processStatus"
          },
          {
            label: "施工单位自检进度",
            type: "input",
            fieldName: "constructionSelfInspectionProgress"
          },
          {
            label: "施工单位自检完成时间",
            type: "input",
            fieldName: "constructionSelfInspectionCompletionTime"
          },
          {
            label: "施工单位自检完成人",
            type: "input",
            fieldName: "constructionSelfInspectionCompleter"
          },
          {
            label: "集中化质检进度",
            type: "input",
            fieldName: "centralizedQualityInspectionProgress"
          },
          {
            label: "质检未通过次数",
            type: "input",
            fieldName: "qualityInspectionFailedTimes"
          },
          {
            label: "质检负责人",
            type: "input",
            fieldName: "qualityInspectionFailedTimes"
          },
          {
            label: "集中化质检结果",
            type: "input",
            fieldName: "centralizedQualityInspectionResult"
          },
          {
            label: "集中化质检人",
            type: "input",
            fieldName: "centralizedQualityInspectionPerson"
          },
          {
            label: "所属地市",
            type: "input",
            fieldName: "affiliatedCity"
          },
          {
            label: "工程管理经理(主)",
            type: "input",
            fieldName: "managementManager"
          },
          {
            label: "工程实施经理(主)",
            type: "input",
            fieldName: "implementationManager"
          },
          {
            label: "省公司稽核人员",
            type: "input",
            fieldName: "auditor"
          }
          // {
          //   label: "单据状态",
          //   type: "input",
          //   fieldName: "receiptStatus"
          // }
        ]
