<template>
  <mssCard :title="title">
    <div slot="headerBtn" v-if="node === nodeType&&approveType=='todo'">
      <el-button type="primary" @click="addTable">新增行</el-button>
      <el-button type="primary" @click="delBatch">删除行</el-button>
      <!-- 质检审核意见 -->
    </div>
    <div slot="content">
      <el-form ref="exemptionList">
        <mss-table
          :selectable="checkSelectable "
          :rowClassName="tableRowClassName"
          :getChange="true"
          @getChange="handleGetChange"
          ref="table"
          :selection="selection"
          :stationary="stationaryData"
          :pagination="false"
          :columns="tableHeader"
          :static-search-param="staticSearchParam"
        />
      </el-form>
    </div>
  </mssCard>
</template>

<script>
  import ProblemCategoryMixin from "@/views/pccw/workflow/review/mixins/ProblemCategoryMixin.js"
  export default {
    props: {
      processOptions:Array,// 工序名称列表
            approveType: String,
      cardTitle: String,
      node: Number, // 节点名称
      nodeType: Number,// 引用组件的名称
      stationaryData: Array, // tableData.qualityOpinionList 数据
      // tableHeader: Array,
      staticSearchParam: Object
      // problemOptions: Object,
    },
    mixins: [ProblemCategoryMixin],
    data() {
      return {
        selection:true,
        title: this.cardTitle,
        nodeTable1:{
            prop: "qualityOpinion",
            label: "质检审核意见",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: row => {
              if (this.node != this.nodeType||this.approveType!='todo') {
                return (
                  <span>{row.qualityOpinion == "0" ? "通过" : "不通过"}</span>
                )
              } else {
                return (
                  <el-select v-model={row.qualityOpinion} placeholder="请选择">
                    <el-option label="通过" value={"0"} />
                    <el-option label="不通过" value={"1"} />
                  </el-select>
                )
              }
            }
          },
        nodeTable2:{
            prop: "rectificationOpinion",
            label: "整改复核意见",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: row => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.rectificationOpinion == "0" ? "通过" : "不通过"}</span>
                )
              }
              if (this.node != this.nodeType||this.approveType!='todo') {
                return (
                  <span>{row.rectificationOpinion == "0" ? "通过" : "不通过"}</span>
                )
              } else {
                return (
                  <el-select v-model={row.rectificationOpinion} placeholder="请选择">
                    <el-option label="通过" value={"0"} />
                    <el-option label="不通过" value={"1"} />
                  </el-select>
                )
              }
            }
          },
        // 质检审核意见
        tableHeader: [
          {
            prop: "isRectificationNotice",
            label: "是否下发整改通知",
            align: "left",
            tooltip: true,
            minWidth: "130px",
            formatter: row => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.isRectificationNotice == "0" ? "是" : "否"}</span>
                )
              }
              const flag= this.node == this.nodeType&&this.approveType=='todo'

              // 节点名称 和 组件名称相同
              if (this.node == this.nodeType&&this.approveType=='todo') {
                if (this.node===1&&row.qualityOpinion === "0"||
                this.node===2&&row.rectificationOpinion === "0") {
                  return <span>否</span>
                } else {
                  return (
                    <el-select
                      v-model={row.isRectificationNotice}
                      placeholder="请选择"
                    >
                      <el-option label="是" value={"0"} />
                      <el-option label="否" value={"1"} />
                    </el-select>
                  )
                }
              } else {
                return (
                  <span>{row.isRectificationNotice == "0" ? "是" : "否"}</span>
                )
              }
            }
          },
          {
            prop: "bigIssueCategory",
            label: "问题大类",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column, cellValue, index) => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.bigIssueCategory}</span>
                )
              }
              if (this.node == this.nodeType&&this.approveType=='todo') {
                if (row.qualityOpinion === "0"||row.rectificationOpinion === "0") {
                  return <span></span>
                } else {
                  // 查询，但是问题大类可能没值
                  if (this.problemOptions && this.problemOptions.length > 0) {
                  return (
                    <el-select
                      v-model={row.bigIssueCategory}
                      placeholder="请选择"
                      onChange={() => this.handleBigIssueChange(row, 1)}
                    >
                      {this.problemOptions.map(item => (
                        <el-option
                          key={item.id}
                          label={item.name}
                          value={item.name}
                        />
                      ))}
                    </el-select>
                  )
                  } else {
                    // 先显示，后续会赋值
                    return (
                      <span>
                        <el-select
                          v-model={row.bigIssueCategory}
                          placeholder="请选择"
                          onChange={() => this.handleBigIssueChange(row, 1)}
                        ></el-select>
                      </span>
                    )
                  }
                }
              } else {
                return <span>{row.bigIssueCategory}</span>
              }
            }
          },
          {
            prop: "smallIssueCategory",
            label: "问题小类",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column) => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.smallIssueCategory}</span>
                )
              }
              if (this.node == this.nodeType&&this.approveType=='todo') {
                if (row.qualityOpinion === "0"||row.rectificationOpinion === "0") {
                  return <span></span>
                } else {
                  const bigIssueName = row.bigIssueCategory
                  const bigIssueId = this.findIdByName(bigIssueName)
                  const smallIssueOptions = bigIssueId
                    ? this.smallIssueOptionsMap[bigIssueId]
                    : [] // 根据ID获取小类选项
                  return (
                    <el-select
                      v-model={row.smallIssueCategory}
                      placeholder="请选择"
                      onChange={() => this.handleBigIssueChange(row, 2)}
                    >
                      {smallIssueOptions.map(item => (
                        <el-option
                          key={item.value}
                          label={item.label}
                          value={item.label}
                        />
                      ))}
                    </el-select>
                  )
                }
              } else {
                return <span>{row.smallIssueCategory}</span>
              }
            }
          },
          {
            prop: "problemSubdivide",
            label: "问题细分",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column) => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.problemSubdivide}</span>
                )
              }
              if (this.node == this.nodeType&&this.approveType=='todo') {
                if (row.qualityOpinion === "0"||row.rectificationOpinion === "0") {
                  return <span></span>
                } else {
                  const name = row.smallIssueCategory
                  const id = this.findIdByName(name)
                  const subIssueOptions = id ? this.subIssueOptionsMap[id] : [] // 根据ID获取小类选项
                  // console.log("subIssueOptions", subIssueOptions)
                  return (
                    <el-select
                      v-model={row.problemSubdivide}
                      placeholder="请选择"
                    >
                      {subIssueOptions.map(item => (
                        <el-option
                          key={item.value}
                          label={item.label}
                          value={item.label}
                        />
                      ))}
                    </el-select>
                  )
                }
              } else {
                return <span>{row.problemSubdivide}</span>
              }
            }
          },
          {
            prop: "importanceLevel",
            label: "严重程度",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column, cellValue, index) => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.importanceLevel}</span>
                )
              }
              if (this.node == this.nodeType&&this.approveType=='todo') {
                if (row.qualityOpinion === "0"||row.rectificationOpinion === "0") {
                  return <span></span>
                } else {
                  return (
                    // <el-form-item prop={`importanceLevel${index}`}>
                    <el-select
                      v-model={row.importanceLevel}
                      onChange={value => {
                        // this.form[`importanceLevel{index}`] = value;
                      }}
                    >
                      <el-option label={"一般问题"} value={"一般问题"} />
                      <el-option label={"次严重问题"} value={"次严重问题"} />
                      <el-option
                        label={"较为严重问题"}
                        value={"较为严重问题"}
                      />
                      <el-option label={"严重问题"} value={"严重问题"} />
                      <el-option label={"特别严重"} value={"特别严重"} />
                    </el-select>
                  )
                }
              } else {
                return <span>{row.importanceLevel}</span>
              }
            }
          },
          {
            prop: "processName",
            label: "工序名称",
            align: "left",
            tooltip: true,
            minWidth: "150px",
            formatter: (row, column) => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.processName}</span>
                )
              }
              if (this.node == this.nodeType&&this.approveType=='todo') {
                if (row.qualityOpinion === "0"||row.rectificationOpinion === "0") {
                  return <span></span>
                } else {
                  return (
                    <el-select v-model={row.processName} placeholder="请选择">
                      {this.processOptions.map(item => (
                        <el-option
                          key={item.value}
                          label={item.label}
                          value={item.value}
                        />
                      ))}
                    </el-select>
                  )
                }
              } else {
                return <span>{row.processName}</span>
              }
            }
          },
          {
            prop: "rectificationDeadline",
            label: "整改期限",
            align: "left",
            minWidth: "150px",
            formatter: row => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.rectificationDeadline}</span>
                )
              }
              if (this.node == this.nodeType&&this.approveType=='todo') {
                  if (row.qualityOpinion === "0"||row.rectificationOpinion === "0") {
                    return <span></span>
                  } else {
                    return (
                 <el-date-picker
                   v-model={row.rectificationDeadline}
                   type="date"
                   placeholder="选择日期"
                   style="width: 100%;"
                   value-format="yyyy-MM-dd"
                 ></el-date-picker>
                    )
                  }
                } else {
                  return <span>{row.rectificationDeadline}</span>
                }
              }
          },
          {
            prop: "isRectificationCompleted",
            label: "整改状态",
            align: "left",
            tooltip: true
            // formatter: row => {
            //   if(row.isShow&&row.isShow=='gray'){
            //     return (
            //          <span>{row.isRectificationCompleted}</span>
            //     )
            //   }
            //   if (this.node == this.nodeType&&this.approveType=='todo') {
            //     return <span></span>
            //   } else {
            //     return (
            //       <span>
            //         {row.isRectificationCompleted === "0" ? "通过" : "不通过"}
            //       </span>
            //     )
            //   }
            // }
          },
          {
            prop: "rectificationCompletedTime",
            label: "整改完成时间",
            align: "left",
            tooltip: true,
            formatter: row => {
              if(row.isShow&&row.isShow=='gray'){
                return (
                     <span>{row.rectificationCompletedTime}</span>
                )
              }
              if (this.node == this.nodeType&&this.approveType=='todo') {
                return <span></span>
              } else {
                return <span>{row.rectificationCompletedTime}</span>
              }
            }
          }
        ]
      }
    },
    created() {
      // 不显示选择框
      if(this.nodeType!=this.node){
        this.selection=false
      }
      if (this.nodeType === 1) {
        this.tableHeader.unshift(this.nodeTable1)
      }
      if (this.nodeType === 2) {
        this.tableHeader.unshift(this.nodeTable2)
      }
    },
    methods: {
      // this.stationaryData 为需要置灰的数据列表
      checkSelectable (row) {
        if(this.nodeType === 2&&row.isShow&&row.isShow=='gray'){
        	return false
        } else {
        	return true
        }
      },
      tableRowClassName ({row, rowIndex}) {
            if(this.nodeType === 2&&row.isShow&&row.isShow=='gray'){
      		return 'disabledRow'
      	} else {
      		return ''
      	}
      },
      addTable() {
        this.$emit("addTable") // 触发父组件的 addTable 事件
      },
      delBatch() {
        this.$emit("delBatch") // 触发父组件的 delBatch 事件
      },
      handleGetChange(value) {
        this.$emit("getChange", value) // 触发父组件的 getChange 事件并传递参数
      }
    }
  }
</script>
<style lang="scss" scoped>
::v-deep .disabledRow {
	cursor: not-allowed;
	pointer-events: none;
	color: #ccc; // 改当前行的字体颜色

  .el-checkbox__inner{
    // color: blue;
  }
}

</style>
