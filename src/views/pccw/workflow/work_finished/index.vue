<template>
  <div class="app-container">
    <mssSearchForm v-show="showSearch" ref="searchForm" :form="queryTodo" :search-config="queryParams"
      @reset="resetQuery" @search="handleQuery"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTableFlow ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="queryTodo"
          :customSize="queryTodo.size" border selection>
        </mssTableFlow>
      </div>
    </mssCard>
    <el-dialog title="流程图" :visible.sync="dialogXML" width="80%">
      <bpmn-diagram :procInstId="procInstId" v-if="dialogXML" />
    </el-dialog>
  </div>
</template>

<script>
  import {
    queryMyFinised
  } from "@/api/pccw/workflow/index.js";
  import BpmnDiagram from "@/views/pccw/workflow/common/BpmnDiagram.vue";

  export default {
    components: {
      BpmnDiagram,
    },
    data() {
      return {
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [{
            label: '任务名称',
            type: 'input',
            fieldName: 'title'
          },
          {
            label: '任务类型',
            type: 'input',
            fieldName: 'flowName'
          },
        ],
        procInstId: '',
        // 查询条件 - 我的待办
        queryTodo: {
          condition: {
            title: '',
            flowName: '',
            suspensionState: '',
            receiveStartTime: '',
            receiveEndTime: '',
            tenantId: '0',
            roleIds: [],
            deptId: '',
          },
        },
        // 表头
        tableHeader: [{
            prop: "title",
            label: "任务名称",
            align: "center",
            tooltip: true,
            width: 220,
            formatter: (row) => {
              return ( <
                span class = "table_cell_click"
                onclick = {
                  () => {
                    this.toDetail(row)
                  }
                } >
                {
                  row.title
                } <
                /span>
              )
            }
          },
          {
            prop: "flowName",
            label: "任务类型",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "approveTime",
            label: "审批时间",
            align: "center",
            tooltip: true,
            width: 220
          },
          {
            prop: "taskName",
            label: "当前节点",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "startUserName",
            label: "派发人",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '50px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.getFlowXml(row)
                    }
                  } >
                  监控 <
                  /span>
                } <
                /span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: queryMyFinised,
      };
    },
    created() {},
    methods: {
      // 点击任务名称
      toDetail(row) {
        console.log("row", row)
        const data = encodeURIComponent(JSON.stringify(row));
        this.$router.push({
          path: `/pccw_menu/pccw_workflow/approve_detail`,
          query: {
            flow: data,
              type:"finished"
            }
          })
      },
      // 查询流程图
      getFlowXml(row) {
        this.procInstId = row.procInstId
        this.dialogXML = true
      },
      /** 搜索按钮操作 */
      handleQuery(form) {
        console.log("form", form);
        this.queryTodo.condition.flowName = form.flowName
        this.queryTodo.condition.title = form.title
        // this.queryTodo = {
        //   ...form
        // }
        this.$refs.table.page.current = 1
        this.queryTodo = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      },
    }
  };
</script>


<style lang="scss" scoped>
  .table_cell_click {
    cursor: pointer;
    color: #02a7f0;

    .el-icon-data-analysis {
      font-size: 14px;
    }
  }
</style>
