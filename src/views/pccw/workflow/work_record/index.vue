<template>
  <div class="app-container">
    <mssSearchForm
      v-show="showSearch"
      ref="searchForm"
      :form="queryOwn"
      :search-config="queryParams"
      @reset="resetQuery"
      @search="handleQuery"
    ></mssSearchForm>
    <mssCard title="统一待办-查询结果">
      <div slot="headerBtn">
        <div slot="headerBtn">
          * 用于稽核march库的数据
          <el-button type="primary" @click="delBatch">批量删除</el-button>
                    <el-button type="primary" @click="downloadData()">导出所有数据</el-button>
        </div>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :static-search-param="queryOwn"
          border
          selection
          :getChange="true"
          @getChange="handleGetChange"
        >
        </mssTable>
      </div>
    </mssCard>

    <!-- 添加或修改运行时流程变量对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="150px">
        <el-form-item label="待办生成时间" prop="createTime">
          <el-input v-model="form.composeTime" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    exportData,
    getRecord,
    updateRecord,
    getRecordById,
    delBatchByidList
  } from "@/api/pccw/workflow/march.js"
  export default {
    components: {},
    data() {
      return {
        // 表单参数
        form: {},
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        loadingFlag: false, // 数据加载状态
        flow: null,
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [
          {
            label: "待办生成时间",
            type: "input",
            fieldName: "composeTime",
            width: 180
          },
          {
            label: "流程分类名称",
            type: "input",
            fieldName: "fileType",
            width: 180
          },
          {
            label: "业务id",
            type: "input",
            fieldName: "businessId",
            width: 180
          }
        ],
        procInstId: "",
        // 查询条件 - 我提交的
        queryOwn: {
          // total: '',
          // workType: '',
          // current: 1,
          // size: 6,
          // condition: {
          //   title: "",
          //   flowName: "",
          //   suspensionState: "",
          //   receiveStartTime: "",
          //   receiveEndTime: "",
          //   tenantId: "0",
          //   roleIds: [],
          //   deptId: ""
          // }
        },
        // 表头
        tableHeader: [
          {
            prop: "msgType",
            label: "0表示待办，1表示已办",
            align: "center",
            tooltip: true,
            width: 50
          },
          {
            prop: "title",
            label: "流程标题名称/标题",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "composeTime",
            label: "创建时间/待办生成时间",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "process",
            label: "目录区分/申请的：业务系统流程唯一标识",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "fileType",
            label: "流程分类名称/流程名称",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "targets",
            label: "办理人帐号",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "targetsName",
            label: "办理人名称",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "targetId",
            label: "执行人id/办理人id",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "startUserId",
            label: "起始人id/创建人id",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "preUserId",
            label: "发送人/上一环节-帐号",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "unid",
            label: "字母+任务id/应用系统中待办唯一ID/taskId",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "feature",
            label: "流程唯一标识/proId",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "activity",
            label: "节点/环节名",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "category",
            label: "流程分类名称/分类名称",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "link",
            label: "pc端数据打开链接",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "moaConfig",
            label: "moa适配 0表示未接入手机 1表示h5接入",
            align: "center",
            tooltip: true,
            width: 280
          },
          {
            prop: "taskId",
            label: "",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "moaLink",
            label: "moaLink 手机端链接",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "businessId",
            label: "业务id",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "createTime",
            label: "创建时间",
            align: "center",
            tooltip: true,
            width: 140
          },
          {
            prop: "updateTime",
            label: "更新时间",
            align: "center",
            tooltip: true,
            width: 140
          },
          {
            label: "修改",
            align: "center",
            fixed: "right",
            width: 80,
            minWidth: "50px",
            formatter: row => {
              return (
                <span>
                  {
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.handleUpdate(row)
                      }}
                    >
                      修改{" "}
                    </span>
                  }
                </span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: getRecord
      }
    },
    created() {},
    methods: {
      async downloadData() {
        let exportParams = {
          ...this.staticSearchParam
        }
        // let res = await exportData(qs.stringify(exportParams))
        let res = await exportData()
        console.log(res,"文件")
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", "统一待办记录.xlsx")
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      },
      handleGetChange(selection) {
        this.selectionList = selection
      },
      async delBatch() {
        if(!this.selectionList||this.selectionList.length<1){
          return this.$message.error("还未选择数据！")
        }
        let idList = this.selectionList.map(item => item.id)
        console.log(idList)

        let res = await delBatchByidList(idList)
        if (res.code === '0000') {
          this.$message.success(res.data)
          // 查询刷新
          this.$refs.table.getTableData()
        } else {
          this.$message.error("操作失败！")
        }
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        // this.reset()
        const id = row.id || this.ids
        getRecordById(id).then(response => {
          this.form = response.data
          this.open = true
          this.title = "修改流程记录"
        })
      },
      /** 提交按钮 */
      submitForm() {
        console.log("提交")
        this.$refs["form"].validate(valid => {
          if (valid) {
            console.log(this.form)
            if (this.form.id != null) {
              updateRecord(this.form).then(res => {
                if (res.code === "0000" && res.data === 1) {
                  this.$message.success("修改成功")
                  this.$refs.table.getTableData()
                }
                this.open = false
              })
            } else {
              addRecord(this.form).then(response => {
                this.$message.success("新增成功")
                this.open = false
                this.$refs.table.getTableData()
              })
            }
          }
        })
      },
      /** 搜索按钮操作 */
      handleQuery(form) {
        console.log("form", form)
        // this.queryOwn.condition.flowName = form.flowName
        // this.queryOwn.condition.title = form.title
        // this.queryOwn = {
        //   ...form
        // }
        this.$refs.table.page.current = 1
        // this.queryOwn = JSON.parse(
        //   JSON.stringify(this.$refs.searchForm.searchForm)
        // )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .table_cell_click {
    cursor: pointer;
    color: #02a7f0;

    .el-icon-data-analysis {
      font-size: 14px;
    }
  }
</style>
