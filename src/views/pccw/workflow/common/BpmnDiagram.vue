<template>
  <div>
    <div style=" top: 0px; left: 0px; width: 100%;">
      <el-row type="flex" justify="end">
        <el-button-group key="scale-control" size="medium">
          <el-button size="medium" type="default" :plain="true" :disabled="defaultZoom <= 0.3" icon="el-icon-zoom-out"
            @click="processZoomOut()" />
          <el-button size="medium" type="default"
            style="width: 90px;">{{ Math.floor(this.defaultZoom * 10 * 10) + "%" }}</el-button>
          <el-button size="medium" type="default" :plain="true" :disabled="defaultZoom >= 3.9" icon="el-icon-zoom-in"
            @click="processZoomIn()" />
          <el-button size="medium" type="default" icon="el-icon-c-scale-to-original" @click="processReZoom()" />
        </el-button-group>
      </el-row>
    </div>
    <div id="canvas"></div>
  </div>
</template>

<script>
  import {getFlowXml} from "@/api/pccw/workflow/index.js";
  import BpmnViewer from 'bpmn-js';
  import Modeling from 'bpmn-js/lib/features/modeling';
  import ZoomScrollModule from './zoomScroll.js';
  import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';
  import {
    Loading
  } from 'element-ui';
  export default {
    props: ['procInstId'],
    name: 'BpmnDiagram',
    components: {
    },
    data() {
      return {
        showCanvas: false,
        activeTab: 'three',
        defaultZoom: 1.0,
        viewer: undefined,
      };
    },
    watch: {
      procInstId: {
        handler(newVal, oldVal) {
          this.$nextTick(() => {
            this.getFlowXml();
          });
        },
        immediate: false
      }
    },
    methods: {
      processReZoom() {
        this.defaultZoom = 1;
        this.viewer.get('canvas').zoom('fit-viewport', 'auto');
      },
      processZoomIn(zoomStep = 0.1) {
        let newZoom = Math.floor(this.defaultZoom * 100 + zoomStep * 100) / 100;
        if (newZoom > 4) {
          throw new Error('[Process Designer Warn ]: The zoom ratio cannot be greater than 4');
        }
        this.defaultZoom = newZoom;
        this.viewer.get('zoomScroll').zoom(this.defaultZoom);
      },
      processZoomOut(zoomStep = 0.1) {
        let newZoom = Math.floor(this.defaultZoom * 100 - zoomStep * 100) / 100;
        if (newZoom < 0.2) {
          throw new Error('[Process Designer Warn ]: The zoom ratio cannot be less than 0.2');
        }
        this.defaultZoom = newZoom;
        this.viewer.get('zoomScroll').zoom(-this.defaultZoom);
      },
      // 获取流程图
      async getFlowXml() {
        const res = await getFlowXml(this.procInstId);
        this.xmlData = res.data.flowXmlData
        this.nodeData = res.data.flowNodeData
        if (this.xmlData && this.nodeData) {
          // 解决插件的bug
          await this.renderXml(() => {
            this.processReZoom();
            setTimeout(() => {
              this.processReZoom();
              this.showCanvas = true
            }, 2000);
            setTimeout(() => {
              this.processReZoom();
            }, 2000);
          });
        }
      },
      renderXml(callback) {
        if (this.viewer) {
          this.viewer.destroy();
        }
        this.viewer = new BpmnViewer({
          additionalModules: [
            Modeling,
            ZoomScrollModule,
            MoveCanvasModule
          ],
          container: '#canvas'
        });
        this.zoomScroll = this.viewer.get('zoomScroll');
        this.viewer.importXML(this.xmlData)
          .then(() => {
            // console.log('成功解析BPMN文件');
            var modeling = this.viewer.get('modeling');
            var elementsRegistry = this.viewer.get('elementRegistry');
            this.nodeData.forEach(node => {
              var nodeId = node.key;
              var nodeElement = elementsRegistry.get(nodeId);
              if (node.completed) {
                modeling.setColor(nodeElement, {
                  fill: "#e5f4dd",
                  stroke: "#4eb818"
                });
              }
            });
            return this.viewer.saveXML();
          })
          .then(xml => {
            // console.log('成功保存BPMN文件');
            if (callback && typeof callback === 'function') {
              callback();
            }
          })
          .catch(err => {
            console.error('出错:', err);
          });
      },
    },
    mounted() {
      this.$nextTick(() => {
        if (this.procInstId) {
          this.getFlowXml();
        }
      })
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .bjs-powered-by {
    display: none;
  }

  .djs-container {
    height: 680px;
    width: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .djs-container .viewport {
    height: 680px;
    width: 100%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  #canvas {
    width: 100%;
    height: 680px;
  }
</style>
