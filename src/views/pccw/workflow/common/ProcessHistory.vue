<template>
  <div>
    <div v-for="(item, index) in historyList" :key="index">
      <div class="title-item">
        <span class="title-name">流程环节：</span>{{ item.taskName }}
        <span class="title-name">处理人：</span>
        <!-- 多用戶 -->
        <div class="users">
          <div v-for="user in item.userList" :key="user.userId" class="user">
            <img v-if="user.userImage" :src="user.userImage" class="avatar">
            {{user.userName}}
          </div>
        </div>
      </div>
      <el-card class="history-item">
        <div v-if="index!=(historyList.length-1)">
          <div class="history-item">
            选择决策：
            <el-tag size="mini" v-if="item.viewState==1">等待完成</el-tag>
            <el-tag type="success" size="mini" v-else-if="item.viewState==2">审批完成</el-tag>
            <el-tag type="warning" size="mini" v-else-if="item.viewState==3">审批失败</el-tag>
            <el-tag type="danger" size="mini" v-else>其他</el-tag>
          </div>
          <div class="history-item">
            接收时间：
            <span class="time">{{ formatDate(item.receiveTime) }}</span>
          </div>
          <div v-if="item.comments.length > 0" class="history-item">
            <div v-for="comment in item.comments" :key="comment.operateTime" class="comment">
              <div class="time">
                <span class="itemText">
                  处理时间：
                </span>
                {{ formatDate(comment.operateTime) }}
              </div>
              <div class="comments" style="margin-top: 10px;">处理意见：{{ comment.comments }}</div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="history-item">
            提交时间：
            <span class="time">{{ formatDate(item.receiveTime) }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      historyList: {
        type: Array,
        required: true
      }
    },
    data() {
      return {
        // historyList: [] // 存储传入的流程历史记录数据
      };
    },
    methods: {
      formatDate(dateStr) {
        return dateStr;
      }
    },
    watch: {
      historyList(newVal) {
        if (newVal) {
          // console.log('创建historyList:', this.historyList);
        }
      }
    },
    created() {

    },
    mounted() {

    },
  };
</script>

<style lang="scss" scoped>
  .history-item {
    margin-top: 10px;
    margin-bottom: 20px;

    .history-item {
      margin: 10px 0;
    }
  }

  .item-header {
    // display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .title-item {
    display: flex;

    .title-name {
      font-weight: bold;
      margin: 0px 5px;
    }
  }

  .users {
    display: flex;
    align-items: center;

    .user {
      margin: 0px 2px;
    }

    .user::after {
      content: "、";
      margin-left: 4px;
    }

    .user:last-child::after {
      content: none;
    }
  }

  .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 5px;
  }

  .comments {
    margin-bottom: 10px;
  }
</style>
