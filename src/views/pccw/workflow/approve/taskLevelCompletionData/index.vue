<template>
  <div>
      <mssCard title="基本信息">
        <div slot="content" class="result">
          <div class="head">
            <div class="title">
              {{result.title}}
            </div>
      <div class="content-text">
        完工报告单号：{{result.completionReportNo}}
        <div class="right-column">
        完工报告名称：{{result.completionReportName}}
              <br>
<!--              测试数据
              文件是否：{{result.isFirstUploadComplete}}
              是否审批完成：{{result.isLastApprovalComplete}} -->
            </div>
            </div>
          </div>
        </div>
      </mssCard>
    <mssCard title="任务级竣工资料收集明细2">
      <div slot="headerBtn">
        <el-button type="primary" @click="downloadTemplate">下载模版</el-button>
      </div>
      <div slot="content">
        <mssTableTaskLevel ref="myTable" :columns="tableHeader" border selection :stationary="stationary"
          :api="tableApi" :static-search-param="staticSearchParam">
        </mssTableTaskLevel>
      </div>
    </mssCard>
    <select-download ref="selectDownload" :fileList="fileList"></select-download>
    <select-del ref="selectDel" :fileList="fileList" @message="handleMessage"></select-del>
  </div>
</template>

<script>
  import {
    downloadTemplate,
    delFile,
    downloadFile,
    getDatils,
    getList,
    updatePass
  } from "@/api/pccw/comprehensive/completion/index.js";

  import taskUpload from "./selectUpload.vue";
  import selectDownload from "./selectDownload.vue";
  import selectDel from "./selectDel.vue";
  import {
    createLogger
  } from "vuex";

  export default {
    props: ['businessId', 'taskName'],
    components: {
      taskUpload,
      selectDownload,
      selectDel
    },
    data() {
      return {
        staticSearchParam: {
          businessId: this.businessId
        },
        // tableApi: getDatils,
        tableApi: getList, // 新的
        tableKey: 0,
        result: {
          title: '',
          completionReportName: '',
          completionReportNo: ''
        },
        completionReportNo: '',
        completionReportName: '',
        fileList: [],
        projectCode: '',
        taskCode: '',
        completionReportNo: '',
        fileType: '',
        // 表头
        tableHeader: [{
            label: "建设单位",
            prop: "taskCompnayName",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: "项目编码",
            prop: "projectCode",
            align: "center",
            tooltip: true,
          },
          {
            label: "项目名称",
            prop: "projectName",
            align: "center",
            tooltip: true,
          },
          {
            label: '任务编码',
            prop: 'taskCode',
            minWidth: 200
          },
          {
            label: '任务名称',
            prop: 'taskName',
            minWidth: 200
          },
          {
            label: '任务专业类型',
            prop: 'taskTypeName',
            minWidth: 200
          },
          {
            label: '任务所属区域',
            prop: 'taskArea',
            minWidth: 200
          },
          {
            label: '工程实施经理(主)：',
            prop: 'engineImplementationManager',
            minWidth: 200
          },
          {
            label: "工程管理经理",
            prop: "engineManageManager",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: "完工报告完成时间",
            prop: "actualEndDate",
            align: "center",
            tooltip: true,
          },
          {
            label: "施工单位",
            prop: "constructionDept",
            align: "center",
            tooltip: true,
          },
          {
            label: '监理单位',
            prop: 'supervisionOrgName',
            minWidth: 100,
            tooltip: true
          }, {
            label: '监理单位负责人',
            prop: 'supervisionUserName',
            minWidth: 120,
            tooltip: true
          },
          {
            label: '结算表导入',
            fixed: 'right',
            minWidth: '130px',
            formatter: (row) => {
              return ( <
                span class = "table_operate" >
                <
                span class = "mr10" > {
                  /* 动态创建 taskUpload 组件 */
                } {
                  this.$createElement('taskUpload', {
                    ref: 'taskUpload',
                    props: {
                      row: row,
                      fileType: '结算表'
                    }
                  })
                } <
                /span> <
                span class = 'table_btn mr10'
                onClick = {
                  () => {
                    this.taskDownload(row, 'settlement')
                  }
                } > 下载 < /span> <
                span class = 'table_btn mr10'
                onClick = {
                  () => {
                    this.taskDel(row, 'settlement')
                  }
                } > 删除 < /span> < /
                span >
              );
            },
          },
          {
            label: '物资平衡表导入',
            fixed: 'right',
            minWidth: '130px',
            formatter: (row) => {
              return ( <
                span class = "table_operate" >
                <
                span class = "mr10" > {
                  /* 动态创建 taskUpload 组件 */
                } {
                  this.$createElement('taskUpload', {
                    ref: 'taskUpload',
                    props: {
                      row: row,
                      fileType: '物资平衡表'
                    }
                  })
                } <
                /span> <
                span class = 'table_btn mr10'
                onClick = {
                  () => {
                    this.taskDownload(row, 'material')
                  }
                } > 下载 < /span> <
                span class = 'table_btn mr10'
                onClick = {
                  () => {
                    this.taskDel(row, 'material')
                  }
                } > 删除 < /span> < /
                span >
              );
            },
          },
          {
            label: '竣工图纸导入',
            fixed: 'right',
            minWidth: '130px',
            formatter: (row) => {
              return ( <
                span class = "table_operate" >
                <
                span class = "mr10" > {
                  /* 动态创建 taskUpload 组件 */
                } {
                  this.$createElement('taskUpload', {
                    ref: 'taskUpload',
                    props: {
                      row: row,
                      fileType: '竣工图纸上传'
                    }
                  })
                } <
                /span> <
                span class = 'table_btn mr10'
                onClick = {
                  () => {
                    this.taskDownload(row, 'built')
                  }
                } > 下载 < /span> <
                span class = 'table_btn mr10'
                onClick = {
                  () => {
                    this.taskDel(row, 'built')
                  }
                } > 删除 < /span> < /
                span >
              );
            },
          },
          {
            label: "上传时间",
            prop: "uploadedDate",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: "是否审核通过",
            prop: "isPassedApprove",
            align: "center",
            tooltip: true,
            minWidth:'100px',
            fixed: 'right',
formatter: (row) => {
  const flag = this.taskName === "工程实施经理（主）";
  return (
    <span>
      {flag ? (
        <span class="btn-pass">
          <el-radio v-model={row.isPassedApprove} label='是' onChange={() => this.handleCheckPassed(row,true)}>是</el-radio>
          <el-radio v-model={row.isPassedApprove} label='否' onChange={() => this.handleCheckPassed(row,false)}>否</el-radio>
        </span>
      ) : (
        <span>是</span>
      )}
    </span>
  );
}
          },
        ],
        isPass: true,
        stationary: [], // 静态数据
        flow: {},
        procInstId: null,
        activeName: "first",
        historyData: [],
        type: '', // 路由跳转类型
        returnableList: [] // 退回任务节点列表数据
      };
    },
    created() {
      console.log("父组件数据", this.businessId)
      if (this.businessId) {
        this.getDetails()
      }
      console.log("this.stationary", this.stationary);
    },

    mounted() {
      this.$bus.$on('refresh', (data) => {
        this.refreshData()

        // 给
        // 文件是否：{{result.isFirstUploadComplete}}
        // 是否审批完成：{{result.isLastApprovalComplete}}
      })
      // this.$bus.$on('taskLevelData', () => {
      //   // 事件：打开审批对话框，赋值
      //   console.log('接收 taskLevelData 事件');
      //   this.$bus.$emit("setTaskLevelData", this.result);
      // });
    },

    methods: {
      async handleCheckPassed(row,bool) {
        if(bool){
        row.isPassedApprove = "是"
        }else{
          row.isPassedApprove = "否"
        }
        console.log("row", row);
        console.log("check", row.isPassedApprove);
        const data = []
        data.push(row)
        let res = await updatePass(data)
        console.log("res", res)
        this.refreshData()
      },
      refreshData() {
        this.$refs.myTable.refresh()
      },
      handleMessage() {

        console.log("消息来咯");
        this.$refs.myTable.refresh()
        console.log(this.$refs.myTable);
        // this.refreshData()
        // this.$forceUpdate()
      },
      taskDel(row, type) {
        if (type === 'settlement') {
          this.fileList = row.selectStatementFileList
        } else if (type === 'material') {
          this.fileList = row.selectMaterialsFileList
        } else if (type === 'built') {
          this.fileList = row.selectAsBuiltDrawingsFileList
        }
        this.$refs.selectDel.selectDel = true;
      },
      taskDownload(row, type) {
        if (type === 'settlement') {
          this.fileList = row.selectStatementFileList
        } else if (type === 'material') {
          this.fileList = row.selectMaterialsFileList
        } else if (type === 'built') {
          this.fileList = row.selectAsBuiltDrawingsFileList
        }
        this.$refs.selectDownload.downDialog = true;
      },
      async getDetails() {
        let query = {
          businessId: this.businessId
        }
        let res = await getDatils(query)
        console.log("shuju", res.data.isFirstUploadComplete);
        if (res.code === '0000') {
          // 只赋值头部数据，实现表格数据局部刷新
          this.result = res.data
          // this.stationary = res.data.data.data
          if (res.data.isFirstUploadComplete) {
            // 允许审批
            // this.$bus.$emit('approvalBtn', '审批')
          }
        }
      },
      // 下载模版
      async downloadTemplate() {
        const res = await downloadTemplate();
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', '模板.zip');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
      // 返回上一页
      goBack() {
        this.$router.go(-1);
      },
    }
  };
</script>


<style scoped lang="scss">
	.btn-pass {
		.el-radio {
			margin-right: 2px;

		}
	}

	.operate-btn {}
.title-top{
  font-size: 20px;
}
	.head {

		// display:flex;
		// flex-direction:row
		.title {
			font-size: 20px;
			text-align: center;
			border-bottom: 1px solid #e6ebf5;
			// justify-content:center;
			// align-items:center;
		}
.content-completion{
  margin: 50px;
}
		.content-text {
			margin-top: 10px;
.left-column {
  float: left; /* 左浮动 */
  width: 50%; /* 左列宽度 */
}
      .right-column {
        float: right; /* 右浮动 */
        width: 50%; /* 右列宽度 */
      }
		}
	}

	.table_operate {
		display: flex;
	}

	.table_btn {
		margin-top: 1px;
	}
</style>
