<template>
  <div class="upload-file">
    <el-upload multiple :http-request="(params)=>{uploadNewFile(params)}" action="string"
      :before-upload="handleBeforeUpload" :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed"
      :show-file-list="false" class="upload-file-uploader" ref="fileUpload">
      <!-- 上传按钮 -->
      <el-button type="text">{{ buttonName }}</el-button>
    </el-upload>
  </div>
</template>

<script>
  import {
    downloadTemplate,
    delDef,
    uploadFile,
    download
  } from "@/api/pccw/comprehensive/completion/index.js";
  import {
    getToken
  } from '@/utils/auth'

  export default {
    name: "FileUpload",
    props: {
      row: Object, // 声明一个名为 row 的 props
      buttonName: {
        type: String,
        default: '上传',
      },
      limit: {
        type: Number,
        default: 100,
      },
      // // 值
      // value: [String, Object, Array],
      // 数量限制
      // // 大小限制(MB)
      // fileSize: {
      //   type: Number,
      //   default: 5,
      // },
      // // 文件类型, 例如['png', 'jpg', 'jpeg']
      // fileType: {
      //   type: Array,
      //   default: () => ["doc", "docx", "xls", "xlsx", "ppt", "txt", "pdf"],
      //   // default: () => ["doc", "xls", "ppt", "txt", "pdf"],
      // },
      // // 是否显示提示
      // isShowTip: {
      //   type: Boolean,
      //   default: true
      // },
      // 文件类型， 竣工图纸上传  物资平衡表  结算表
      fileType: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        query: {
          projectCode: '',
          taskCode: '',
          completionReportNo: ''
        },
        fileList: []
      };
    },
    computed: {
      // 是否显示提示
      showTip() {
        return this.isShowTip && (this.fileType || this.fileSize);
      },
    },
    watch: {
      value: {
        handler(val) {
          if (val) {
            let temp = 1;
            // 首先将值转为数组
            const list = Array.isArray(val) ? val : this.value.split(',');
            // 然后将数组转为对象数组
            this.fileList = list.map(item => {
              if (typeof item === "string") {
                item = {
                  name: item,
                  url: item
                };
              }
              return item;
            });
          } else {
            this.fileList = [];
            return [];
          }
        },
        deep: true,
        immediate: true
      }
    },
    created() {
      // this.getList()
    },
    methods: {
      async uploadNewFile(params) {
        const formData = new FormData();
        formData.append('file', params.file);
        formData.append('projectCode', this.query.projectCode);
        formData.append('taskCode', this.query.taskCode);
        formData.append('completionReportNo', this.query.completionReportNo);
        // formData.append('fileType', this.fileType);
        formData.append('fileType', this.fileType);
        const res = await uploadFile(formData);
        console.log("上传结果", res);
        if (res.code === '0000') {
          this.$message.success('导入成功');
          this.sendMsg()
        }
      },
      // 查询文件数目
      getList(businessId) {
        // 父组件传值使用businessId查询
        let queryParams = {}
        if (businessId) {
          queryParams = {
            system: this.system,
            model: this.model,
            type: this.type,
            businessId: businessId
          }
        } else {
          queryParams = {
            system: this.system,
            model: this.model,
            type: this.type,
            businessId: this.businessId
          }
        }
        // console.log("调用getlist", businessId);
        // console.log("queryParams", queryParams);
        listFile(queryParams).then(response => {
          this.fileList = response.data
        })
        console.log(this.fileList);
      },
      // 上传前校检格式和大小
      handleBeforeUpload(file) {
        console.log("父组件传过来的数据", this.row);
        console.log(this.$props.row); // 在这里可以访问到 row 的值
        let row = this.$props.row
        this.query.projectCode = row.projectCode
        this.query.taskCode = row.taskCode
        this.query.completionReportNo = row.completionReportNo
        // // 校检文件类型
        // if (this.fileType) {
        //   const fileName = file.name.split('.');
        //   const fileExt = fileName[fileName.length - 1];
        //   const isTypeOk = this.fileType.indexOf(fileExt) >= 0;
        //   if (!isTypeOk) {
        //     this.$message.error(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`);
        //     return false;
        //   }
        // }
        // // 校检文件大小
        // if (this.fileSize) {
        //   const isLt = file.size / 1024 / 1024 < this.fileSize;
        //   if (!isLt) {
        //     this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
        //     return false;
        //   }
        // }
        // this.$modal.loading("正在上传文件，请稍候...");
        return true;
      },
      // 文件个数超出
      handleExceed() {
        this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`);
      },
      // 上传失败
      handleUploadError(err) {
        this.$message.error("上传文件失败，请重试" + err);
        // this.$modal.closeLoading()
      },
      sendMsg() {
        this.$bus.$emit('refresh')
      },
    }
  };
</script>

<style scoped lang="scss">
  .el-button {}

  .upload-file-uploader {
    margin-bottom: 5px;
  }

  .upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
  }

  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }

  .ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
  }
</style>
