<template>
  <div class="custom-dialog">
    <el-dialog title="审批" :visible.sync="approvalDialog" class="dialog" :modal-append-to-body="false">
      <el-form :label-position="labelPosition" label-width="100px" :model="approvalRo">
        <div v-if="nicknames">
        <el-form-item label="下一节点审批人" class="approvalOpinion">
            {{nicknames}}
        </el-form-item>
          </div>
        <el-form-item label="审批意见" class="approvalOpinion">
          <el-input type="textarea" v-model="approvalRo.approvalComments" :rows="5" resize='none'
            class="comments"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="consentApproval()">确定</el-button>
        <el-button @click="closed()">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {
    approveTask,
    getNextNodeApproverNames
  } from "@/api/pccw/workflow/index.js";
  export default {
    props: ['taskId', 'flowName', 'taskName'],
    data() {
      return {
        nicknames: null,
        impManger: '', // 实施经理id
        visManger: '', // 监工经理id
        approvalDialog: false,
        labelPosition: 'top',
        // 审批对象
        approvalRo: {
          nextTaskUsers: [], // 指定下一节点审批人
          taskId: "",
          taskIds: [],
          approvalComments: "", // 审批意见
          nextTaskUsers: [],
          taskKey: "",
          userId: "",
          copyUserList: [],
          approveType: "", // 审批操作:Y同意N不同意
          returnType: 0,
          targetTaskKey: "",
          variables: {},
          userId: "",
          tenantId: "0"
        },
      };
    },
    created() {
      this.getNames()
    },
    methods: {
      async getNames() {
        const res = await getNextNodeApproverNames(this.taskId)
        let data = res.data
        let nicknameList = [];
        for (let i = 0; i < data.length; i++) {
          let nickname = data[i];
          // 去除括号和逗号
          nickname = nickname.replace(/[\[\],]/g, "");
          nicknameList.push(nickname);
        }
        // 将昵称列表转换为以逗号分隔的字符串
        this.nicknames = nicknameList.join(",");
      },
      closed() {
        this.approvalRo.approvalComments = ""
        this.approvalDialog = false
      },
      // 确认审批
      async consentApproval() {
        this.approvalRo.approveType = "Y"
        this.approvalRo.taskId = this.taskId
        const res = await approveTask(this.approvalRo);
        this.approvalDialog = false;
        if (res.code == "0000") {
          this.$message({ // 提示
            type: 'success',
            message: '审批成功'
          });
          // 跳转页面
          this.$router.push({
            path: `/pccw_menu/pccw_workflow/work_todo`
          })
        }
      },
    }
  };
</script>

<style scoped>

</style>
