<!-- 没有指定流程页面，则跳转到此页面 -->
<template>
  <div>
    <div class="operate-btn">
      <template v-if="type=='todo'">
        <el-button type="primary" @click="openApprovalDialog" v-if="showApproval">提交</el-button>
        <el-button type="primary" @click="openSendBackDialog" v-if="showReturn">退回</el-button>
        <el-button type="primary" @click="openTransactDialog" v-if="showTransact">转办</el-button>
      </template>
      <el-button @click="goBack">返回</el-button>
    </div>
    <!-- 子组件占位 -->
    <taskLevelCompletionData v-if="taskLevelFlag" :businessId="flow.businessId" :taskName="flow.taskName" />
    <mssCard title="流程记录">
      <div slot="content">
        <process-history :historyList="historyData" v-if="historyData.length>0" />
      </div>
    </mssCard>
    <template v-if="type=='todo'&&loadingFlag">
    <approval-dialog ref="approvalDialog" :taskId="flow.taskId" :flowName="flow.flowName" :taskName="flow.taskName" />
    <send-back-dialog ref="sendBackDialog" :taskId="flow.taskId" :returnableList="returnableList" />
    <transact-dialog ref="transactDialog" :taskId="flow.taskId" />
    </template>
  </div>
</template>

<script>
  import {
    getRecordList,
    getReturnList,
  } from "@/api/pccw/workflow/index.js";
  import ProcessHistory from "@/views/pccw/workflow/common/ProcessHistory.vue";
  import BpmnDiagram from "@/views/pccw/workflow/common/BpmnDiagram.vue";
  import ApprovalDialog from './ApprovalDialog.vue';
  import SendBackDialog from './SendBackDialog.vue';
  import TransactDialog from './TransactDialog.vue';
  import taskLevelCompletionData from '../taskLevelCompletionData/index.vue';

  export default {
    components: {
      ApprovalDialog,
      SendBackDialog,
      TransactDialog,
      BpmnDiagram,
      ProcessHistory,
      taskLevelCompletionData
    },
    data() {
      return {
        loadingFlag:false, // 数据加载状态
        isPermitApproval: true, // 竣工-是否允许提交
        taskLevel: null, // 竣工数据
        showApproval: true,
        showReturn: true,
        showTransact: true,
        taskLevelFlag: false,
        flow: {},
        procInstId: null,
        activeName: "first",
        historyData: [],
        type: '', // 路由跳转类型
        returnableList: [] // 退回任务节点列表数据
      };
    },
    mounted() {
      // 是否修改审批按钮状态
      this.$bus.$on('approvalBtn', (data) => {
        this.taskLevel = data
      })
    },
    created() {
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow); // 获取流程对象
      this.type = this.$route.query.type; // 获取类型信息
      this.procInstId = this.flow.procInstId
      this.loadingFlag=true
      // 不同审批判断
      console.log('竣工', this.flow);
      if (this.flow.flowName === "任务级竣工资料收集督办流程") {
        this.taskLevelFlag = true
      }
      if (this.flow.name&&this.flow.name.includes("完工任务级竣工资料收集")) {
        this.taskLevelFlag = true
      }
      if (this.procInstId) {
        this.initialize(); // 初始化页面数据
      }
    },
    methods: {
      // 审批（提交）对话框
      openApprovalDialog() {
        if (this.flow.flowName === "任务级竣工资料收集督办流程") {
          console.log('this.isPermitApproval', this.isPermitApproval);
          if (this.taskLevel.isFirstUploadComplete != "true") {
            this.$message({
              type: "warning",
              message: '文件还未上传完成！'
            });
            return
          }
          // 工程实施经理（主） 判断是否已审批
          if (this.flow.taskName == "工程实施经理（主）") {
            if (this.taskLevel.isLastApprovalComplete != "true") {
              this.$message({
                type: "warning",
                message: '审批还未完全选择完毕！'
              });
              return
            }
          }
        }
        this.$refs.approvalDialog.approvalDialog = true;
      },
      // 退回对话框
      async openSendBackDialog() {
        const res = await getReturnList(this.flow.taskId);
        if (res.data == null || res.data.length === 0) {
          this.$message({
            type: "warning",
            message: '当前节点无法退回！'
          });
          return;
        }
        this.returnableList = res.data
        this.$refs.sendBackDialog.sendBackDialog = true
      },
      // 转办对话框
      openTransactDialog() {
        this.$refs.transactDialog.selectUser = {};
        this.$refs.transactDialog.transactDialog = true;
      },
      initialize() {
        this.getRecordList()
      },
      async getRecordList() {
        const res = await getRecordList(this.procInstId);
        this.historyData = res.data;
      },
      getFlowXml() {
        this.$refs.bpmnDiagram.sendBackDialog = true
        this.dialogXML = true
      },
      goBack() {
        this.$router.go(-1); // 返回上一页
      },
    }
  };
</script>

<style scoped>

</style>
