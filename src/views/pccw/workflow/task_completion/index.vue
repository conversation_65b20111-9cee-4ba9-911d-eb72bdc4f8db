<!-- 没有指定流程页面，则跳转到此页面 -->
<template>
  <div>
    <div class="operate-btn">
      <template v-if="type=='todo'">
        <el-button type="primary" @click="openApprovalDialog" v-show="showApproval">提交</el-button>
        <el-button type="primary" @click="openSendBackDialog" v-show="showReturn">退回</el-button>
        <el-button type="primary" @click="openTransactDialog" v-show="showTransact">转办</el-button>
      </template>
      <el-button @click="goBack">返回</el-button>
    </div>
    <!-- 子组件占位 -->
    <template v-if="loadingFlag">
    <taskLevelCompletionData v-if="taskLevelFlag" :businessId="flow.businessId" :taskName="flow.nodeName" :flowName="flow.name"
     :isFinshed="type"
     />
    </template>
    <mssCard title="流程记录">
      <div slot="content">
        <process-history :historyList="historyData" v-if="historyData.length>0" />
      </div>
    </mssCard>
    <template v-if="type=='todo'&&loadingFlag">
      <approval-dialog ref="approvalDialog" :taskId="flow.taskId" :approvals="approvals" :flowName="flow.workflowName"
        :taskName="flow.nodeName" />
      <send-back-dialog ref="sendBackDialog" :taskId="flow.taskId" :returnableList="returnableList" />
      <transact-dialog ref="transactDialog" :taskId="flow.taskId" />
    </template>
  </div>
</template>

<script>
  import {
    getRecordList,
    getReturnList,
  } from "@/api/pccw/workflow/index.js";
  import ProcessHistory from "@/views/pccw/workflow/common/ProcessHistory.vue";
  import BpmnDiagram from "@/views/pccw/workflow/common/BpmnDiagram.vue";
  import ApprovalDialog from '@/views/pccw/workflow/flowDetail/ApprovalDialog.vue';
  import SendBackDialog from '@/views/pccw/workflow/flowDetail/SendBackDialog.vue';
  import TransactDialog from '@/views/pccw/workflow/flowDetail/TransactDialog.vue';
  import taskLevelCompletionData from '@/views/pccw/workflow/task_completion/taskLevelCompletionData/index.vue';

  export default {
    components: {
      ApprovalDialog,
      SendBackDialog,
      TransactDialog,
      BpmnDiagram,
      ProcessHistory,
      taskLevelCompletionData
    },
    data() {
      return {
        nextApprovers: [
          // {
          // "taskKey": "Activity_1k42zf4", // 监理专业负责人
          // "userId": "350"
          // },
          // {
          // "taskKey": "Activity_0mk99vm", // 工程实施经理（主）
          // "userId": "1"
          // }
        ], // 指定下一节点审批人

        approvals: false, // 是否指定审批人
        loadingFlag: false, // 数据加载状态
        isPermitApproval: true, // 竣工-是否允许提交
        taskLevel: null, // 竣工数据
        showApproval: true,
        showReturn: true,
        showTransact: false,
        taskLevelFlag: false,
        flow: {},
        procInstId: null,
        activeName: "first",
        historyData: [],
        type: '', // 路由跳转类型
        returnableList: [] // 退回任务节点列表数据
      };
    },
    mounted() {
      // 是否修改审批按钮状态
      this.$bus.$on('approvalBtn', (data) => {
        this.taskLevel = data
      })
    },
    created() {
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow); // 获取流程对象
      this.type = this.$route.query.type; // 获取类型信息
      this.procInstId = this.flow.procInstId
      this.loadingFlag = true
      // 不同审批判断
      console.log('竣工', this.flow);
      if (this.flow.workflowName === "任务级竣工资料收集督办流程") {
        this.taskLevelFlag = true
      }
      if (this.flow.name && this.flow.name.includes("完工任务级竣工资料收集")) {
        this.taskLevelFlag = true
      }
      if (this.procInstId) {
        this.initialize(); // 初始化页面数据
      }


    },
    methods: {
      // 审批（提交）对话框
      openApprovalDialog() {
        // console.log("this.flow", this.flow)
        // 主页跳转名字被一期换了
        // flowName -> workflowName 流程类型
        // taskName -> nodeName 节点名称
        // title -> name 流程标题
        // taskId -> taskId
        console.log('this.isPermitApproval', this.isPermitApproval);
        console.log('this.taskLevel', this.taskLevel);
        if (this.taskLevel&&this.taskLevel.isFirstUploadComplete&&this.taskLevel.isFirstUploadComplete != "true") {
          this.$message({
            type: "warning",
            message: '文件还未上传完成！'
          });
          return
        }
        // 工程实施经理（主） 判断是否已审批
        // 节点判断
        if(this.flow.nodeName=='施工专业负责人'){
          this.approvals=true
          let flowTaskUser={
            "taskKey":'Activity_1k42zf4',// 监理专业负责人
            "userId":''
          }
          this.nextApprovers[0] = flowTaskUser;
        }
        if(this.flow.nodeName=='监理专业负责人'){
          this.approvals=true
          let flowTaskUser={
            "taskKey":'Activity_0mk99vm', // 工程实施经理（主）
            "userId":''
          }
          this.nextApprovers[0] = flowTaskUser;
        }
        if (this.flow.nodeName == "工程实施经理（主）") {
          this.approvals=false // 最后一个节点不存在审批人
          if (this.taskLevel.isLastApprovalComplete != "true") {
            this.$message({
              type: "warning",
              message: '审批是否通过还未完全选择完毕！'
            });
            return
          }
        }
        console.log('his.approvals', this.approvals);
        this.$refs.approvalDialog.approvalDialog = true;
      },



      // 是否显示退回按钮
      async isSendBack() {
        const res = await getReturnList(this.flow.taskId);
        if (res.data == null || res.data.length === 0) {
          this.showReturn=false
        }
      },
      // 退回对话框
      async openSendBackDialog() {
        const res = await getReturnList(this.flow.taskId);
        if (res.data == null || res.data.length === 0) {
          this.$message({
            type: "warning",
            message: '当前节点无法退回！'
          });
          return;
        }
        this.returnableList = res.data
        this.$refs.sendBackDialog.sendBackDialog = true
      },
      // 转办对话框
      openTransactDialog() {
        this.$refs.transactDialog.selectUser = {};
        this.$refs.transactDialog.transactDialog = true;
      },
      initialize() {
        this.getRecordList()
        // 如果为待办
        if(this.type==='todo'){
          this.isSendBack()
        }
      },
      async getRecordList() {
        const res = await getRecordList(this.procInstId);
        this.historyData = res.data;
      },
      getFlowXml() {
        this.$refs.bpmnDiagram.sendBackDialog = true
        this.dialogXML = true
      },
      goBack() {
        this.$router.go(-1); // 返回上一页
      },
    }
  };
</script>

<style scoped>

</style>
