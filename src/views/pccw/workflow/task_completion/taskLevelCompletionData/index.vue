<template>
  <div>
    <flow-detail
      ref="flow"
      :flow="flow"
      :showTransact="true"
      :type="approveType"
      @submit-approval="handleApprovalSubmission"
    >
      <div slot="content">
        <mssCard title="基本信息">
          <div slot="content" class="result">
            <div class="head">
              <div class="title">
                {{ flow.name }}
                <!-- {{ flowName }} -->
                <!-- {{result.title}} -->
              </div>
              <div class="content-text">
                完工报告单号：{{ result.completionReportNo }}
                <div class="right-column">
                  完工报告名称：{{ result.completionReportName }}
                  <br />
                  <!--              测试数据
              文件是否：{{result.isFirstUploadComplete}}
              是否审批完成：{{result.isLastApprovalComplete}} -->
                </div>
              </div>
            </div>
          </div>
        </mssCard>
        <mssCard title="任务级竣工资料收集明细">
          <div slot="headerBtn" v-if="showButtons">
            <el-button type="primary" @click="downloadTemplate"
              >下载模版</el-button
            >
          </div>
          <div slot="content">
            <mssTable
            @tableDataChange="handleTableDataChange"
              ref="myTable"
              :columns="tableHeader"
              border
              selection
              :api="tableApi"
              :static-search-param="staticSearchParam"
            >
            </mssTable>
          </div>
        </mssCard>
        <select-download
          ref="selectDownload"
          :fileList="fileList"
        ></select-download>
        <select-del
          ref="selectDel"
          :fileList="fileList"
          @message="handleMessage"
        ></select-del>
      </div>
    </flow-detail>
  </div>
</template>

<script>
  import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue"
  import {
    downloadTemplate,
    delFile,
    downloadFile,
    getDatils, // 旧
    getList,
    updatePass, // 旧
    edit,
    isCanApproval,
    getCompletionReport
  } from "@/api/pccw/comprehensive/completion/index.js"

  import taskUpload from "./selectUpload.vue"
  import selectDownload from "./selectDownload.vue"
  import selectDel from "./selectDel.vue"
  import { createLogger } from "vuex"

  export default {
    // props: ['businessId', 'taskName', 'flowName',
    //   'isFinshed'// 是否已阅
    // ],  旧代码，先都改到data
    components: {
      taskUpload,
      selectDownload,
      selectDel,
      FlowDetail
    },
    data() {
      return {
        businessId: "",
        taskName: "",
        flowName: "",
        isFinshed: "",

        defaultYes: "", //控制显示
        showButtons: false, // 上传、删除 按钮控制
        // defaultYes:false,// 默认为是
        // showApprove:false,// 显示是/否 (文字)
        // selectApprove:false,// 显示是/否 (选择框)
        staticSearchParam: {
          businessId: this.businessId
        },
        // tableApi: getDatils,
        tableApi: getList,
        tableKey: 0,
        result: {
          title: "",
          completionReportName: "",
          completionReportNo: ""
        },
        completionReportNo: "",
        completionReportName: "",
        fileList: [],
        projectCode: "",
        taskCode: "",
        completionReportNo: "",
        fileType: "",
        // 表头
        tableHeader: [
          {
            label: "建设单位",
            prop: "taskCompnayName",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: "项目编码",
            prop: "projectCode",
            align: "center",
            tooltip: true
          },
          {
            label: "项目名称",
            prop: "projectName",
            align: "center",
            tooltip: true
          },
          {
            label: "任务编码",
            prop: "taskCode",
            minWidth: 200
          },
          {
            label: "任务名称",
            prop: "taskName",
            minWidth: 200
          },
          {
            label: "任务专业类型",
            prop: "taskTypeName",
            minWidth: 200
          },
          {
            label: "任务所属区域",
            prop: "taskArea",
            minWidth: 200,
  formatter: row => {
    return `${row.province}-${row.city}-${row.county}`;
  }
          },
          {
            label: "工程实施经理(主)",
            prop: "engineImplementationManager",
            minWidth: 200
          },
          {
            label: "工程管理经理",
            prop: "engineManageManager",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: "完工报告完成时间",
            prop: "actualEndDate",
            align: "center",
            tooltip: true
          },
          {
            label: "施工单位",
            prop: "constructionDept",
            align: "center",
            tooltip: true
          },
          {
            label: "施工单位负责人",
            prop: "constructionUserName",
            align: "center",
            tooltip: true
          },
          {
            label: "监理单位",
            prop: "supervisionOrgName",
            minWidth: 100,
            tooltip: true
          },
          {
            label: "监理单位负责人",
            prop: "supervisionUserName",
            minWidth: 120,
            tooltip: true
          },
          {
            label: "结算表导入",
            fixed: "right",
            minWidth: "130px",
            formatter: row => {
              // 第一个条件：防止报错 第二个条件：后端规定的
              const isDisabled =
                row &&
                Array.isArray(row.selectStatementFileList) &&
                row.selectStatementFileList.length >= 1
              // const isDisabled = row.selectStatementFileList&&row.selectStatementFileList[0].fileTypeStatus === "200";
              return (
                <span class="table_operate">
                  {isDisabled && (
                    <span class="table_btn mr10 isDisabled">上传</span>
                  )}
                  {!isDisabled && (
                    <span class="mr10">
                      {/* 动态创建 taskUpload 组件 */}
                      {this.showButtons &&
                        this.$createElement("taskUpload", {
                          ref: "taskUpload",
                          props: {
                            row: row,
                            businessId: this.businessId,
                            fileType: "结算表"
                          }
                        })}
                    </span>
                  )}
                  {!isDisabled && (
                    <span class="table_btn mr10 isDisabled">下载</span>
                  )}
                  {isDisabled && (
                    <span class="mr10 table_btn">
                      <span
                        onClick={() => {
                          this.taskDownload(row, "settlement")
                        }}
                      >
                        下载
                      </span>
                    </span>
                  )}
                  {this.showButtons && !isDisabled && (
                    <span class="mr10 table_btn isDisabled">删除</span>
                  )}
                  {this.showButtons && isDisabled && (
                    <span class="mr10 table_btn">
                      <span
                        onClick={() => {
                          this.taskDel(row, "settlement")
                        }}
                      >
                        删除
                      </span>
                    </span>
                  )}
                </span>
              )
            }
          },
          {
            label: "物资平衡表导入",
            fixed: "right",
            minWidth: "130px",

            formatter: row => {
              const isDisabled =
                row &&
                Array.isArray(row.selectMaterialsFileList) &&
                row.selectMaterialsFileList.length >= 1
              // const isDisabled = row.selectMaterialsFileList&&row.selectMaterialsFileList[0].fileTypeStatus === "200";
              return (
                <span class="table_operate">
                  {isDisabled && (
                    <span class="table_btn mr10 isDisabled">上传</span>
                  )}
                  {!isDisabled && (
                    <span class="mr10">
                      {/* 动态创建 taskUpload 组件 */}
                      {this.showButtons &&
                        this.$createElement("taskUpload", {
                          ref: "taskUpload",
                          props: {
                            row: row,
                            businessId: this.businessId,
                            fileType: "物资平衡表"
                          }
                        })}
                    </span>
                  )}
                  {!isDisabled && (
                    <span class="table_btn mr10 isDisabled">下载</span>
                  )}
                  {isDisabled && (
                    <span class="mr10 table_btn">
                      <span
                        onClick={() => {
                          this.taskDownload(row, "material")
                        }}
                      >
                        下载
                      </span>
                    </span>
                  )}
                  {this.showButtons && !isDisabled && (
                    <span class="mr10 table_btn isDisabled">删除</span>
                  )}
                  {this.showButtons && isDisabled && (
                    <span class="mr10 table_btn">
                      <span
                        onClick={() => {
                          this.taskDel(row, "material")
                        }}
                      >
                        删除
                      </span>
                    </span>
                  )}
                </span>
              )
            }
          },
          {
            label: "竣工图纸导入",
            fixed: "right",
            minWidth: "130px",
            formatter: row => {
              const isDisabled =
                row &&
                Array.isArray(row.selectAsBuiltDrawingsFileList) &&
                row.selectAsBuiltDrawingsFileList.length >= 1
              // const isDisabled = row.selectAsBuiltDrawingsFileList&&row.selectAsBuiltDrawingsFileList[0].fileTypeStatus === "200";
              return (
                <span class="table_operate">
                  {isDisabled && (
                    <span class="table_btn mr10 isDisabled">上传</span>
                  )}
                  {!isDisabled && (
                    <span class="mr10">
                      {/* 动态创建 taskUpload 组件 */}
                      {this.showButtons &&
                        this.$createElement("taskUpload", {
                          ref: "taskUpload",
                          props: {
                            row: row,
                            businessId: this.businessId,
                            fileType: "竣工图纸上传"
                          }
                        })}
                    </span>
                  )}
                  {!isDisabled && (
                    <span class="table_btn mr10 isDisabled">下载</span>
                  )}
                  {isDisabled && (
                    <span class="mr10 table_btn">
                      <span
                        onClick={() => {
                          this.taskDownload(row, "built")
                        }}
                      >
                        下载
                      </span>
                    </span>
                  )}
                  {this.showButtons && !isDisabled && (
                    <span class="mr10 table_btn isDisabled">删除</span>
                  )}
                  {this.showButtons && isDisabled && (
                    <span class="mr10 table_btn">
                      <span
                        onClick={() => {
                          this.taskDel(row, "built")
                        }}
                      >
                        删除
                      </span>
                    </span>
                  )}
                </span>
              )
            }
          },
          {
            label: "上传时间",
            prop: "uploadedDate",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: "是否审核通过",
            prop: "isApproved",
            align: "center",
            tooltip: true,
            minWidth: "100px",
            fixed: "right",
            formatter: row => {
              // 根据字符串变量的值来决定显示内容，默认为是
              let displayValue = ""

              if (this.defaultYes === "defaultYes") {
                displayValue = "是"
              } else if (this.defaultYes === "showApprove") {
                displayValue = (
                  <span>{row.isApproved === "是" ? "是" : "否"}</span>
                )
              } else if (this.defaultYes === "selectApprove") {
                displayValue = (
                  <span class="btn-pass">
                    <el-radio
                      v-model={row.isApproved}
                      label="是"
                      onChange={() => this.handleCheckPassed(row, true)}
                    >
                      是
                    </el-radio>
                    <el-radio
                      v-model={row.isApproved}
                      label="否"
                      onChange={() => this.handleCheckPassed(row, false)}
                    >
                      否
                    </el-radio>
                  </span>
                )
              }

              return <span>{displayValue}</span>
            }
          }
        ],
        isPass: true,
        flow: {},
        procInstId: null,
        activeName: "first",
        historyData: [],
        type: "", // 路由跳转类型
        returnableList: [] // 退回任务节点列表数据
      }
    },
    created() {
      this.approveType = decodeURIComponent(this.$route.query.type) // 获取类型信息
      let flow = JSON.parse(decodeURIComponent(this.$route.query.flow))
      this.flow = flow // 获取流程对象
      console.log("流程对象", this.flow)
      console.log("this.businessId", this.businessId)
      this.businessId = this.flow.businessId // 根据这个去后端查数据
      this.staticSearchParam.businessId = this.businessId

      this.flowName = flow.name
      this.taskName = flow.nodeName
      this.isFinshed = this.approveType

      if (this.businessId) {
        // this.getDetails()
        this.isShowButtons()
      }
    },
    mounted() {


      this.$bus.$on("refresh", data => {
        this.refreshData()
        // 给
        // 文件是否：{{result.isFirstUploadComplete}}
        // 是否审批完成：{{result.isLastApprovalComplete}}
      })
      // this.$bus.$on('taskLevelData', () => {
      //   // 事件：打开审批对话框，赋值
      //   console.log('接收 taskLevelData 事件');
      //   this.$bus.$emit("setTaskLevelData", this.result);
      // });
    },
    beforeDestroy() {
      // 取消事件监听器
      this.$bus.$off("refresh")
    },
    methods: {
      // 处理子组件发送的数据
      handleTableDataChange(tableData, page, newData) {
        // console.log('接收到的表数据:', tableData);
        // console.log('接收到的页码:', page);
        // console.log('接收到的额外数据:', newData);
        if(tableData&&tableData.length>0){
this.result =tableData[0]

        }
      },
      // 处理提交事件的方法
      async handleApprovalSubmission() {
        this.$refs.flow.allowApproval = false
        this.$refs.flow.$refs.approvalDialog.approvalDialog = false

        let data = {
          businessId: this.flow.businessId,
          node: this.flow.nodeName
        }
        // 判断是否可以提交
        let res = await isCanApproval(data) // 旧

        // 不可提交，返回
        if (res.code != "0000") {
          return
        }
        let nextApprovers = []
        let customApprovals = true
        // 节点判断，自定义下一节点审批人
        // 工程实施经理（主） 判断是否已审批
        if (this.flow.nodeName == "施工专业负责人") {
          this.approvals = true
          let flowTaskUser = {
            taskKey: "Activity_1k42zf4", // 监理专业负责人
            userId: ""
          }
          nextApprovers.push(flowTaskUser)
        }
        if (this.flow.nodeName == "监理专业负责人") {
          this.approvals = true
          let flowTaskUser = {
            taskKey: "Activity_0mk99vm", // 工程实施经理（主）
            userId: ""
          }
          nextApprovers.push(flowTaskUser)
        }
        if (this.flow.nodeName == "工程实施经理（主）") {
          customApprovals = false // 最后一个节点不存在审批人
        }
        // console.log('his.approvals', this.approvals);
        this.$refs.flow.$refs.approvalDialog.approvals = customApprovals
        this.$refs.flow.$refs.approvalDialog.nextApprovers = nextApprovers
        this.$refs.flow.$refs.approvalDialog.approvalDialog = true
      },
      isShowButtons() {
        // 施工专业负责人 this.showButtons=true
        // defaultYes 情况1：默认为是
        // showApprove 情况2：根据this.showSelectApprove 显示是/否 (文字)
        // selectApprove 情况3：根据this.showSelectApprove 显示是/否 (选择框)
        // console.log("this.stationary", this.stationary);
        // console.log("this.taskName", this.taskName);
        // console.log("this.isFinshed", this.isFinshed);
        if(this.isFinshed!='todo'){ // 已办界面
                   this.showButtons=false
                   // this.showSelectApprove=false //让选择框默认为"是"
                   return
        }

        if (this.taskName === "施工专业负责人") {
          this.showButtons = true
          this.defaultYes = "defaultYes"
        } else if (this.taskName === "监理专业负责人") {
          this.defaultYes = "defaultYes"
        } else if (this.taskName === "工程实施经理（主）") {
          if (this.approveType != "todo") {
            this.defaultYes = "showApprove"
          } else {
            this.defaultYes = "selectApprove"
          }
        }
        console.log("节点状态", this.defaultYes)
      },
      async handleCheckPassed(row, bool) {
        if (bool) {
          row.isApproved = "是"
        } else {
          row.isApproved = "否"
        }
        const data = []
        data.push(row)
        let res = await updatePass(data) // 旧

        let data2 = {
          id: row.id,
          isApproved: row.isApproved
        }
        let res2 = await edit(data2) // 新
        this.refreshData()
      },
      refreshData() {
        this.$refs.myTable.getTableData()
      },
      handleMessage() {
        console.log("消息来咯")
        this.$refs.myTable.refresh()
        // this.refreshData()
        // this.$forceUpdate()
      },
      taskDel(row, type) {
        if (type === "settlement") {
          this.fileList = row.selectStatementFileList
        } else if (type === "material") {
          this.fileList = row.selectMaterialsFileList
        } else if (type === "built") {
          this.fileList = row.selectAsBuiltDrawingsFileList
        }
        this.$refs.selectDel.selectDel = true
      },
      taskDownload(row, type) {
        if (type === "settlement") {
          this.fileList = row.selectStatementFileList
        } else if (type === "material") {
          this.fileList = row.selectMaterialsFileList
        } else if (type === "built") {
          this.fileList = row.selectAsBuiltDrawingsFileList
        }
        this.$refs.selectDownload.downDialog = true
      },
      async getDetails() {
        let query = {
          businessId: this.businessId
        }
        // let res = await getDatils(query)
        let res = await getCompletionReport(query)
        if (res.code === "0000") {
          // 只赋值头部数据，实现表格数据局部刷新
          this.result = res.data
          // this.stationary = res.data.data.data
          // if (res.data.isFirstUploadComplete) {
          //   // 允许审批
          //   // this.$bus.$emit('approvalBtn', '审批')
          // }
        }
      },
      // 下载模版
      async downloadTemplate() {
        const res = await downloadTemplate()
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", "模板.zip")
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      },
      // 返回上一页
      goBack() {
        this.$router.go(-1)
      }
    }
  }
</script>

<style scoped lang="scss">
  .isDisabled {
    color: #666666;
  }

  .btn-pass {
    .el-radio {
      margin-right: 2px;
    }
  }

  .operate-btn {
  }

  .title-top {
    font-size: 20px;
  }

  .head {
    // display:flex;
    // flex-direction:row
    .title {
      font-size: 20px;
      text-align: center;
      border-bottom: 1px solid #e6ebf5;
      // justify-content:center;
      // align-items:center;
    }

    .content-completion {
      margin: 50px;
    }

    .content-text {
      margin-top: 10px;

      .left-column {
        float: left;
        /* 左浮动 */
        width: 50%;
        /* 左列宽度 */
      }

      .right-column {
        float: right;
        /* 右浮动 */
        width: 50%;
        /* 右列宽度 */
      }
    }
  }

  .table_operate {
    display: flex;
  }

  .table_btn {
    margin-top: 1px;
  }
</style>
