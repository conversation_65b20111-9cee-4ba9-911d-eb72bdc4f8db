<template>
  <el-dialog title="选择删除" :visible.sync="selectDel" class="dialog" :modal="true">
    <mssTable ref="table" :columns="tableHeader" border :stationary="stationary" :pagination="false">
    </mssTable>
    </div>
  </el-dialog>
</template>

<script>
  import {
    delFile
  } from "@/api/pccw/comprehensive/completion/index.js";

  export default {
    watch: {
      fileList(newVal) {
        this.stationary = newVal;
      }
    },
    props: ["type", "fileList"],
    data() {
      return {
        // 表格静态数据
        stationary: [],
        selectDel: false,
        // 表头
        tableHeader: [{
            prop: "fileName",
            label: "文件名称",
            align: "left",
            tooltip: true,
            width: 500
          },
          {
            label: '操作',
            align: 'center',
            // fixed: 'right',
            minWidth: '80px',
            formatter: (row) => {
              return ( <
                span >
                <
                span class = "table_btn mr10"
                onClick = {
                  () => {
                    this.selectRow(row);
                  }
                } >
                选择 <
                /span> < /
                span >
              );
            }
          }
        ],
      };
    },
    created() {
      this.stationary = this.fileList
    },
    methods: {
      // 选择当行
      async selectRow(row) {
        let res = await delFile(row.kbId)
        console.log("res", res)
        console.log(row);
        // this.selectUserDialog=false
        // this.$emit('row-selected', row);// 传值父组件
        if (res.code === '0000') {
          this.selectDel = false
          this.$message.success('删除成功')
          // 刷新页面
          this.$bus.$emit('refresh')
        }
      },
    }
  };
</script>


<style lang="scss" scoped>
  .dialog {
    width: 110%;
  }
</style>
