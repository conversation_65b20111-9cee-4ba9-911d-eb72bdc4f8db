<template>
  <div class="app-container">
    <mssSearchForm
      v-show="showSearch"
      ref="searchForm"
      :form="queryOwn"
      :search-config="queryParams"
      @reset="resetQuery"
      @search="handleQuery"
    ></mssSearchForm>
    <mssCard title="流程变量-查询结果">
      <div slot="headerBtn">
        *.新增现在只建议添加竣工的
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :static-search-param="queryOwn"
          border
          selection
          :getChange="true"
          @getChange="handleGetChange"
        >
        </mssTable>
      </div>
    </mssCard>

    <!-- 添加或修改运行时流程变量对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="参数名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="流程实例id" prop="procInstId">
          <el-input v-model="form.procInstId" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="流程变量值" prop="text">
          <el-input v-model="form.text" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getVariable,
    addVariable,
    updateVariable,
    getVariableById
  } from "@/api/pccw/workflow/march.js"
  import { getToken } from "@/utils/auth"
  export default {
    components: {},
    data() {
      return {
        // 表单参数
        form: {},
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        loadingFlag: false, // 数据加载状态
        flow: null,
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [
          // {
          //   label: "任务名称",
          //   type: "input",
          //   fieldName: "title"
          // },
          {
            label: "流程实例id",
            type: "input",
            fieldName: "procInstId",
            width: 180
          },
          {
            label: "流程变量值",
            type: "input",
            fieldName: "text",
            width: 180
          }
        ],
        procInstId: "",
        // 查询条件 - 我提交的
        queryOwn: {
          // total: '',
          // workType: '',
          // current: 1,
          // size: 6,
          // condition: {
          //   title: "",
          //   flowName: "",
          //   suspensionState: "",
          //   receiveStartTime: "",
          //   receiveEndTime: "",
          //   tenantId: "0",
          //   roleIds: [],
          //   deptId: ""
          // }
        },
        // 表头
        tableHeader: [
          {
            prop: "title",
            label: "标题",
            align: "center",
            tooltip: true,
            width: 180,
            formatter: row => {
              return (
                <span
                  class="table_cell_click"
                  onclick={() => {
                    this.toDetail(row)
                  }}
                >
                  {row.title}
                </span>
              )
            }
          },
          {
            prop: "name",
            label: "参数名称",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "procInstId",
            label: "流程实例id",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "text",
            label: "流程变量值",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: "修改",
            align: "center",
            // fixed: "right",
            width: 80,
            minWidth: "50px",
            formatter: row => {
              return (
                <span>
                  {
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.handleUpdate(row)
                      }}
                    >
                      修改{" "}
                    </span>
                  }
                </span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: getVariable,
        selectionList: []
      }
    },
    created() {},
    methods: {
      // 表单重置
      // reset() {
      //   this.form = {
      //     id: null,
      //     name: null,
      //     procInstId: null,
      //     text: null
      //   }
      //   this.reset("form")
      // },
      /** 新增按钮操作 */
      handleAdd() {
        // this.reset()
        this.open = true
        this.title = "添加运行时流程变量"
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        // this.reset()
        const id = row.id_ || this.ids
        getVariableById(id).then(response => {
          this.form = response.data
          this.open = true
          this.title = "修改运行时流程变量"
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            console.log(this.form)
            if (this.form.id_ != null) {
              updateVariable(this.form).then(res => {
                if (res.code === "0000" && res.data === 1) {
                  this.$message.success("修改成功")
                  this.$refs.table.getTableData()
                }
                this.open = false
              })
            } else {
              addVariable(this.form).then(response => {
                this.$message.success("新增成功")
                this.open = false
                this.$refs.table.getTableData()
              })
            }
          }
        })
      },
      addRow() {
        let flowList = this.selectionList.map(item => {
          return {
            ...item,
            userId: item.startUserId
          }
        })
        this.delete(flowList, 2)
      },
      handleGetChange(selection) {
        this.selectionList = selection
      },
      // 点击任务名称
      toDetail(row) {
        console.log(row)
        // 获取flowkey
        let [_, flowKey] = row.procDefId.match(/^([^:]+)/) || []
        let newRow = {
          name: row.title,
          workflowName: row.flowName,
          nodeName: row.taskName,
          taskId: row.taskId,
          businessId: row.businessId,
          procInstId: row.procInstId,
          beginTime: row.creatTime,
          flowKey: flowKey
        }
        // const data = JSON.stringify(newRow);
        // this.$router.push({
        //   path: "/pccw_menu/workflow/review/video_check/check",
        //   query: {
        //     flow: data,
        //     type: "todo"
        //   }
        // });
        // const data = encodeURIComponent(JSON.stringify(newRow))
        // 转为一期字段
        navigateRouter(newRow, "todo") // 调用引入的方法
      },
      // 查询流程图
      getFlowXml(row) {
        this.procInstId = row.procInstId
        this.dialogXML = true
      },
      /** 搜索按钮操作 */
      handleQuery(form) {
        console.log("form", form)
        // this.queryOwn.condition.flowName = form.flowName
        // this.queryOwn.condition.title = form.title
        // this.queryOwn = {
        //   ...form
        // }
        this.$refs.table.page.current = 1
        // this.queryOwn = JSON.parse(
        //   JSON.stringify(this.$refs.searchForm.searchForm)
        // )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .table_cell_click {
    cursor: pointer;
    color: #02a7f0;

    .el-icon-data-analysis {
      font-size: 14px;
    }
  }
</style>
