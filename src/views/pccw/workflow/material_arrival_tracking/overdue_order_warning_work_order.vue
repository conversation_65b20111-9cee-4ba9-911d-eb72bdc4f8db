<template>
  <div>
    <div style="margin-top: 30px;"></div>
    <mssCard title="消息详情">
      <div slot="content">
        <p class="message-title" v-if="showTitle">{{messageTitle}}</p>
      </div>
    </mssCard>
    <mssCard :title="tableTitle">
      <div slot="headerBtn">
        <el-button type="primary" v-if="showButton" @click="openApproval">发起特殊原因审批工单</el-button>
      </div>
      <div slot="content">
        <mssTable v-if="warningId" ref="table" selection :api="api" :columns="currentColumns" :static-search-param="staticSearchParam" :key="tableKey" border>
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {findByMsgIdService} from "@/api/message_manager/message_manager_api";
import {overdueOrderWarningWorkOrderList} from "@/api/pccw/analysis_link/material_arrival_tracking/api.js";

export default {
  components: {
  },
  name: 'OverdueOrderWarningWorkOrder',
  data() {
    return {
        api: overdueOrderWarningWorkOrderList,
        tableKey: 0,
        showTitle:true,
        id: '',
        warningId: '',
        messageType: '',
        messageTitle: '',
        type: '',
        pageLoading: false,
        tableTitle: '未完成接收费用订单列表',
        showButton: true,//控制发起审批按钮
        changeColumns: true,//控制表格表头,为true时显示columns1,为false时显示columns2
        submitTime: '',
        columns1: [{
            prop: "city",
            label: "地市",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "projectNumber",
            label: '项目编号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "projectName",
            label: '项目名称',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "implementationManager",
            label: '工程实施经理',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "managementManager",
            label: '工程管理经理',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "orderNumber",
            label: '订单号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "contractNumber",
            label: '合同编号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "supplier",
            label: '供应商',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "orderRequester",
            label: '订单需求人',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "purchaser",
            label: '采购员',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "orderDeliveryDate",
            label: '订单下达时间',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "materialCode",
            label: '物料编码',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "materialName",
            label: '物料名称',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "materialUnit",
            label: '物料单位',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "quantity",
            label: '数量',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "receivedQuantity",
            label: '接收数量',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "receivedDate",
            label: '接收日期',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "unreceivedQuantity",
            label: '未接收数量',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "unreceivedDays",
            label: '未接收天数',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "notificationSent",
            label: '是否通报(超30天)',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "submitForReview",
            label: '是否提交审核',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "submitTime",
            label: '提交审核时间',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "approveTime",
            label: '审核通过时间',
            align: "center",
            tooltip: true,
            width: 200
          },
        ],
        columns2: [{
            prop: "city",
            label: "地市",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "projectNumber",
            label: '项目编号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "projectName",
            label: '项目名称',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "implementationManager",
            label: '工程实施经理',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "managementManager",
            label: '工程管理经理',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "orderNumber",
            label: '订单号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "contractNumber",
            label: '合同编号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "supplier",
            label: '供应商',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "orderRequester",
            label: '订单需求人',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "purchaser",
            label: '采购员',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "orderDeliveryDate",
            label: '订单下达时间',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "materialCode",
            label: '物料编码',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "materialName",
            label: '物料名称',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "materialUnit",
            label: '物料单位',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "quantity",
            label: '数量',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "receivedQuantity",
            label: '接收数量',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "receivedDate",
            label: '接收日期',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "unreceivedQuantity",
            label: '未接收数量',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "unreceivedDays",
            label: '未接收天数',
            align: "center",
            tooltip: true,
            width: 200
          }
        ],
    }
  },
  methods: {
    async getData() {
      this.pageLoading = true
      const res = await findByMsgIdService(this.id)
      this.messageTitle = res?.data?.title || ''
      if (res.code === '0000') {
        let subString1 = "物资到货跟踪订单预警"
        let subString2 = "物资到货跟踪订单通报"
        let title = res.data.title
        // 从title获取warningId, 使用warningId获取表格数据，设置表格标题（未完成接收费用订单列表/物资到货跟踪订单通报）
        if (title.includes(subString1)) {
          const regex = /\(([^)]+)\)/
          const matches = title.match(regex)
          this.warningId = matches ? matches[1] : null
          this.tableKey++;  // 强制更新表格
        }else if(title.includes(subString2)){
          this.tableTitle = '无特殊原因接收订单超期通报工单'
          const regex = /\(([^)]+)\)/
          const matches = title.match(regex)
          this.warningId = matches ? matches[1] : null
          this.tableKey++;  // 强制更新表格
        }
      }
      this.pageLoading = false
      const params =  {
        warningId: this.warningId,
        page: 1,
        limit: 10
      }
      // 获取提交审核时间, 如果有提交审核时间则不显示发起审批按钮
      overdueOrderWarningWorkOrderList(params).then(res => {
        if (res.code === '0000') {
          this.submitTime = res.data.data[0].submitTime
          if (this.submitTime) {
            this.showButton = false
          }
        }
      })

    },
    openApproval() {
      this.$router.push({
        path: '/pccw_menu/pccw_workflow/material_arrival_tracking/special_reasonOrder_approval_order',
        query: {
          warningId: this.warningId,
        }
      });
    }
  },
  created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    const flow = JSON.parse(urlQuery.flow);
    this.id = flow.id
    this.messageType = flow.messageType
    this.type = urlQuery.type
    //如果是通报消息
    if(this.type !== 'herald'){
      this.showButton = false
      this.changeColumns = false
    }
    this.getData()  // 获取消息详情
  },
  mounted() {
    
  },
  computed: {
    currentColumns() {
      return this.changeColumns ? this.columns1 : this.columns2;
    },
    staticSearchParam() {
      return {
        warningId: this.warningId,
      };
    },
  },
};
</script>

<style scoped>
/* Your component styles here */
</style>
