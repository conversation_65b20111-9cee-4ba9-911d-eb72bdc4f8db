<template>
    <div>
        <mssCard title="未完成接收费用订单列表">
          <div slot="headerBtn">
            <el-button type="primary" @click="$router.push('/home')">返回</el-button>
          </div>
          <div slot="content">
            <mssTable  ref="table" selection :api="api" :columns="columns" :static-search-param="staticSearchParam"  border>
            </mssTable>
          </div>
        </mssCard>
    </div>
</template>

<script>
import {overdueOrderWarningWorkOrderList} from "@/api/pccw/analysis_link/material_arrival_tracking/api.js";
import{submitSpecialReasonForAcceptingOrder} from "@/api/pccw/analysis_link/material_arrival_tracking/api.js";
export default {
    name: 'MaterialApproval',
    data() {
        return {
            api:overdueOrderWarningWorkOrderList,
            warningId: '',
            columns: [
                {
                  prop: "city",
                  label: "地市",
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "projectNumber",
                  label: '项目编号',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "projectName",
                  label: '项目名称',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "implementationManager",
                  label: '工程实施经理',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "orderNumber",
                  label: '订单号',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "contractNumber",
                  label: '合同编号',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "supplier",
                  label: '供应商',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "orderRequester",
                  label: '订单需求人',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "purchaser",
                  label: '采购员',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "orderDeliveryDate",
                  label: '订单下达时间',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "materialCode",
                  label: '物料编码',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "materialName",
                  label: '物料名称',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "materialUnit",
                  label: '物料单位',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "quantity",
                  label: '数量',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "receivedQuantity",
                  label: '接收数量',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "receivedDate",
                  label: '接收日期',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "unreceivedQuantity",
                  label: '未接收数量',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "unreceivedDays",
                  label: '未接收天数',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "isAssessment",
                  label: '是否考核',
                  align: "center",
                  tooltip: true,
                  width: 200,
                  formatter: (row) => {
                    return (
                      <el-select v-model={row.isAssessment} placeholder="请选择">
                        <el-option label="是" value={0}></el-option>
                        <el-option label="否" value={1}></el-option>
                      </el-select>
                    )
                  }
                },
                {
                  prop: "specialReason",
                  label: '特殊原因填写',
                  align: "center",
                  tooltip: true,
                  width: 200,
                  formatter: (row) => {
                    return <el-input v-model={row.specialReason} ></el-input>
                  }
                },
                {
                  prop: "delayDays",
                  label: '订单接收补偿天数',
                  align: "center",
                  tooltip: true,
                  width: 200,
                  formatter: (row) => {
                    return <el-input v-model={row.delayDays} ></el-input>
                  }
                },
                {
                  prop: "delayDays",
                  label: '操作',
                  align: "center",
                  tooltip: true,
                  width: 200,
                  formatter: (row) => {
                    return  <el-button type="primary"  onClick={() => {this.submitApproval(row)}}>提交审批</el-button>
                  }
                },
            ]
        };
    },
    methods: {
      submitApproval(row) {
        const rows = [row];
         for (let row of rows) {
            if (row.isAssessment === '' || row.isAssessment === null) {
              this.$message.error('请填写所有必填项');
              return;
            }
            if (!row.specialReason || !row.delayDays) {
              this.$message.error('请填写所有必填项');
              return;
            }else{
              if (isNaN(row.delayDays)) {
                this.$message.error('订单接收补偿天数必须为数字');
                return;
              }
            }
         }
        // Convert rows to an object
        const rowsObject = rows.reduce((obj, row, index) => {
          obj[index] = row;
          const req = obj[0];
          return req;
        }, {});
        submitSpecialReasonForAcceptingOrder(rowsObject).then((response) => {
           const data = response.code;
            if (data === '0000') {
              this.$message.success('提交成功');
              // this.$router.push('/home');
            } else {
              this.$message.error('提交失败');
            }
        }).catch(error => {
          this.$message.error('提交失败');
        });      
      },
    },
    mounted() {
        // Code to run when the component is mounted
    },
    created() {
        const warningId = this.$route.query.warningId;
        this.warningId = warningId;
    },
    computed: {
      staticSearchParam() {
        return {
          warningId: this.warningId,
        };
      },
    },
};
</script>

<style scoped>
/* Your component styles here */
</style>