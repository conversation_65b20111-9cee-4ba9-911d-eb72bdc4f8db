<template>
    <div>
      <flow-detail :flow="flow" :showTransact="false" :type="approveType">
      <div slot="content">
        <mssCard title="物资到货跟踪特殊原因审批">
          <div slot="content">
            物资到货跟踪特殊原因审批
          </div>
        </mssCard>
        <mssCard title="未完成接收费用订单列表">
          <div slot="content">
            <mssTable  ref="table" selection :api="api" :columns="columns" :static-search-param="staticSearchParam"  border>
            </mssTable>
          </div>
        </mssCard>
      </div>
    </flow-detail>
    </div>
</template>

<script>
import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue";
import {getSpecialReasonOrderApprovalPendingDetails} from "@/api/pccw/analysis_link/material_arrival_tracking/api.js";
export default {
    components: {
      FlowDetail
    },
    name: 'Todo',
    data() {
        return {
            api: getSpecialReasonOrderApprovalPendingDetails,
            flow: {},
            approveType: '',
            businessId: '',
            columns: [
                {
                  prop: "city",
                  label: "地市",
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "projectNumber",
                  label: '项目编号',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "projectName",
                  label: '项目名称',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "implementationManager",
                  label: '工程实施经理',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "orderNumber",
                  label: '订单号',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "contractNumber",
                  label: '合同编号',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "supplier",
                  label: '供应商',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "orderRequester",
                  label: '订单需求人',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "purchaser",
                  label: '采购员',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "orderDeliveryDate",
                  label: '订单下达时间',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "materialCode",
                  label: '物料编码',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "materialName",
                  label: '物料名称',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "materialUnit",
                  label: '物料单位',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "quantity",
                  label: '数量',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "receivedQuantity",
                  label: '接收数量',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "receivedDate",
                  label: '接收日期',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "unreceivedQuantity",
                  label: '未接收数量',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "unreceivedDays",
                  label: '未接收天数',
                  align: "center",
                  tooltip: true,
                  width: 200
                },
                {
                  prop: "specialReason",
                  label: '特殊原因填写',
                  align: "center",
                  tooltip: true,
                  width: 200,
                },
                {
                  prop: "delayDays",
                  label: '订单接收补偿天数',
                  align: "center",
                  tooltip: true,
                  width: 200,                  
                },
            ]
        };
    },
    methods: {
        // Your methods here
    },
    mounted() {
        // Code to run when the component is mounted
    },
    created() {
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow); // 获取流程对象
      this.approveType = this.$route.query.type; // 获取类型信息
      this.businessId = this.flow.businessId; // 获取业务id
    },
    computed: {
    staticSearchParam() {
      return {
        businessId: this.businessId,
      };
    },
  },
};
</script>

<style scoped>
/* Your component styles here */
</style>