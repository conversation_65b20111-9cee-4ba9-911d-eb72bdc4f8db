<template>
  <div>
    <mssCard title="申述-详情列表">
      <div slot="headerBtn" v-if="operateState === 'appeal_fill'">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="preservation">保存</el-button>
      </div>
      <div slot="content">
        <el-table
          :data="tableList"
          class="table"
          row-key="id"
          :header-cell-style="rowClass"
          default-expand-all
          border
          style="width: 100%"
        >
          <el-table-column
            prop="assessmentProject"
            label="考核项目"
            align="center"
          ></el-table-column>
          <el-table-column
            label="考核内容"
            align="center"
            prop="evaluationContent"
          >
          </el-table-column>
          <el-table-column
            prop="standardsAndRequirements"
            align="center"
            label="标准及要求"
          ></el-table-column>
          <el-table-column label="分值" prop="standardScore" align="center">
            <template scope="scope">
              {{ scope.row.standardScore }}
            </template>
          </el-table-column>
          <template>
            <el-table-column
              label="扣分事项说明"
              prop="deductionItem"
              align="center"
            >
            </el-table-column>
            <el-table-column label="出现次数" prop="times" align="center">
            </el-table-column>
            <el-table-column label="扣分值" prop="deductScore" align="center">
            </el-table-column>
          </template>
          <template v-if="operateState === 'appeal_fill'">
            <el-table-column
              label="申诉后扣分事项说明"
              prop="afterRequirements"
              align="center"
              width="150"
            >
              <template slot-scope="scope">
                <el-input
                  type="textarea"
                  v-model="scope.row.afterRequirements"
                  autocomplete="off"
                  style="width: 100%"
                >
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
              label="申诉后出现次数"
              prop="afterTimes"
              align="center"
            >
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.afterTimes"
                  autocomplete="off"
                  style="width: 80%"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column
              label="申诉后扣分值"
              prop="afterDeductScore"
              align="center"
            >
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.afterDeductScore"
                  autocomplete="off"
                  style="width: 80%"
                >
                </el-input>
              </template>
            </el-table-column>
          </template>

          <template v-else>
            <el-table-column
              label="申诉后扣分事项说明"
              prop="afterRequirements"
              align="center"
              width="150"
            >
            </el-table-column>
            <el-table-column
              label="申诉后出现次数"
              prop="afterTimes"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="申诉后扣分值"
              prop="afterDeductScore"
              align="center"
            >
            </el-table-column>
          </template>

          <el-table-column label="申诉说明" prop="appealReason" align="center">
          </el-table-column>
          <el-table-column label="操作" align="center" width="100px">
            <template slot-scope="scope">
              <el-button @click="getAppeal(scope.row)" type="text" size="small"
                >附件查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </mssCard>
    <el-dialog
      title="申诉"
      :visible.sync="pendingShow"
      :close-on-click-modal="false"
      @close="pendingShow = false"
      width="85%"
    >
      <mssTable
        ref="table"
        :columns="tableHeader2"
        :stationary="tableData2"
        :pagination="false"
        :serial="false"
      >
      </mssTable>
    </el-dialog>
  </div>
</template>
<script>
  import {
    getAppealDetailInFlow,
    saveNewDetailInFlow,
    downloadFiles
  } from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig"
  import { commonDown } from "@/utils/btn"
  export default {
    name: "details_list",
    components: {},
    props: {
      businessId: {
        type: String,
        default: ""
      },
      problemCollectionFlowId: {
        type: String,
        default: ""
      },
      operateState: {
        // 操作状态
        type: String,
        default: ""
      }
    },
    data() {
      return {
        loadParam: {},
        tableData: [],
        tableList: [],
        assessmentType: "",
        fill: {},
        pendingShow: false,
        tableHeader2: [
          {
            prop: "name",
            label: "附件名称",
            align: "center"
          },
          {
            prop: "operate",
            label: "操作",
            align: "center",
            formatter: row => {
              return (
                <span>
                  <span
                    style="color:#33ACFB;cursor:pointer;margin-right:10px"
                    onClick={() => {
                      this.downFile(row)
                    }}
                  >
                    下载
                  </span>
                </span>
              )
            }
          }
        ],
        tableData2: []
        // problemCollectionFlowId: ''
      }
    },
    methods: {
      goBack() {
        this.$router.go(-1) // 假设你想要的是返回上一页
      },
      async downFile(row) {
        console.log(row)
        let res = await downloadFiles(row)
        console.log(res, "文件")
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", row.name)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      },
      getAppeal(item) {
        this.tableData2 = []
        let val = item.fileNames.split(",")
        let id = item.fileIds.split(",")
        if (val.length > 1) {
          val.forEach((arr, index) => {
            this.tableData2.push({
              name: arr,
              id: id[index]
            })
          })
        } else {
          this.tableData2 = [
            {
              name: item.fileNames,
              id: item.fileIds
            }
          ]
        }
        this.pendingShow = true
        console.log(item)
      },
      async selectTableList() {
        console.log("详情列表businessId", this.businessId)
        let params = {
          problemCollectionFlowId: this.problemCollectionFlowId,
          businessId: this.businessId
        }
        try {
          const res = await getAppealDetailInFlow(params)
          console.log("res", res)
          if (res.code === "0000") {
            this.tableList = res.data
          }
        } catch (error) {
          console.error("Error fetching data:", error)
          // 处理异常情况
        }
      },
      //合并表头
      rowClass({ row, column, rowIndex, columnIndex }) {
        if (rowIndex === 1) {
          return {
            display: "none"
          }
        }
      },
      preservation() {
        for (let index = 0; index < this.tableList.length; index++) {
          if (
            this.tableList[index].afterRequirements === "" ||
            this.tableList[index].afterRequirements === null
          ) {
            return this.$message.error(
              `第${index + 1}行 申诉后扣分事项说明为空`
            )
          }
          if (
            this.tableList[index].afterTimes === "" ||
            this.tableList[index].afterTimes === null
          ) {
            return this.$message.error(`第${index + 1}行 申诉后出现次数为空`)
          }
          if (
            this.tableList[index].afterDeductScore === "" ||
            this.tableList[index].afterDeductScore === null
          ) {
            return this.$message.error(`第${index + 1}行 申诉后扣分值为空`)
          }
        }
        saveNewDetailInFlow(this.tableList).then(res => {
          if (res.code === "0000") {
            this.$message.success("信息保存成功")
          }
        })
      }
    },
    mounted() {
      this.selectTableList()
    }
  }
</script>

<style scoped>
  .upload-btn {
    display: inline-block;
    margin: 0 10px;
  }
  /* Your component's CSS styles go here */
</style>
