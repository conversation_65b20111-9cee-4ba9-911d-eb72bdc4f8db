<template>
  <div>

    <projectTaskStageAssessmentDialog
          :flowData="flowData"
          :businessId="businessId"
          :operateState="operateState"
          :deductionNode="deductionNode"
      v-if="currentDialog === 'surveyDesignStage'"
      ref="surveyDesignStageDialog"
      :titleName="configArr.surveyDesignStageDialog.titleName"
      :assessmentType="configArr.surveyDesignStageDialog.assessmentType"
    ></projectTaskStageAssessmentDialog>

    <projectTaskStageAssessmentDialog
          :flowData="flowData"
          :businessId="businessId"
          :operateState="operateState"
          :deductionNode="deductionNode"
      v-if="currentDialog === 'implementAcceptanceStage'"
      ref="implementAcceptanceStageDialog"
      :titleName="configArr.implementAcceptanceStageDialog.titleName"
      :assessmentType="configArr.implementAcceptanceStageDialog.assessmentType"
    ></projectTaskStageAssessmentDialog>

    <projectTaskStageAssessmentDialog
          :flowData="flowData"
          :businessId="businessId"
          :operateState="operateState"
          :deductionNode="deductionNode"
      v-if="currentDialog === 'prepareSupervisionUnit'"
      ref="prepareSupervisionUnitDialog"
      :titleName="configArr.prepareSupervisionUnitDialog.titleName"
      :assessmentType="configArr.prepareSupervisionUnitDialog.assessmentType"
    ></projectTaskStageAssessmentDialog>

    <projectTaskStageAssessmentDialog
          :flowData="flowData"
          :businessId="businessId"
          :operateState="operateState"
          :deductionNode="deductionNode"
      v-if="currentDialog === 'prepareConstructionUnit'"
      ref="prepareConstructionUnitDialog"
      :titleName="configArr.prepareConstructionUnitDialog.titleName"
      :assessmentType="configArr.prepareConstructionUnitDialog.assessmentType"
    ></projectTaskStageAssessmentDialog>

    <projectTaskStageAssessmentDialog
          :flowData="flowData"
          :businessId="businessId"
          :operateState="operateState"
          :deductionNode="deductionNode"
      v-if="currentDialog === 'implementationSupervisionUnit'"
      ref="implementationSupervisionUnitDialog"
      :titleName="configArr.implementationSupervisionUnitDialog.titleName"
      :assessmentType="
        configArr.implementationSupervisionUnitDialog.assessmentType
      "
    ></projectTaskStageAssessmentDialog>

    <projectTaskStageAssessmentDialog
           :flowData="flowData"
          :businessId="businessId"
          :operateState="operateState"
          :deductionNode="deductionNode"
      v-if="currentDialog === 'implementationConstructionUnit'"
      ref="implementationConstructionUnitDialog"
      :titleName="configArr.implementationConstructionUnitDialog.titleName"
      :assessmentType="
        configArr.implementationConstructionUnitDialog.assessmentType
      "
    ></projectTaskStageAssessmentDialog>

    <projectTaskStageAssessmentDialog
            :flowData="flowData"
          :businessId="businessId"
          :operateState="operateState"
          :deductionNode="deductionNode"
      v-if="currentDialog === 'acceptanceSupervisionUnit'"
      ref="acceptanceSupervisionUnitDialog"
      :titleName="configArr.acceptanceSupervisionUnitDialog.titleName"
      :assessmentType="configArr.acceptanceSupervisionUnitDialog.assessmentType"
    ></projectTaskStageAssessmentDialog>

    <projectTaskStageAssessmentDialog
      :flowData="flowData"
      v-if="currentDialog === 'acceptanceConstructionUnit'"
      ref="acceptanceConstructionUnitDialog"
      :titleName="configArr.acceptanceConstructionUnitDialog.titleName"
      :assessmentType="
        configArr.acceptanceConstructionUnitDialog.assessmentType
      "
    ></projectTaskStageAssessmentDialog>
  </div>
</template>

<script>
  import projectTaskStageAssessmentDialog from "./projectTaskStageAssessmentDialog.vue"

  export default {
    components: { projectTaskStageAssessmentDialog },
    data() {
      return {
        deductionNode:"",
        isFill:false,
        businessId:"",
        flowData:{
          proFlowId:""
        },
        showSave:true, // 扣分子组件是否显示保存
        currentDialog: null,
        configArr: {
          surveyDesignStageDialog: {
            titleName:
              "通信建设工程勘察设计服务项目考评表（勘察设计阶段-权重70%）",
            assessmentType: "surveyDesignStage"
          },
          implementAcceptanceStageDialog: {
            titleName:
              "通信建设工程勘察设计服务项目考评表（实施验收阶段-权重30%）",
            assessmentType: "implementAcceptanceStage"
          },
          prepareSupervisionUnitDialog: {
            titleName: "通信建设工程监理服务项目考评表（工程准备阶段-权重10%）",
            assessmentType: "prepareSupervisionUnit"
          },
          prepareConstructionUnitDialog: {
            titleName: "通信建设工程施工服务项目考评表（工程准备阶段-权重10%）",
            assessmentType: "prepareConstructionUnit"
          },
          implementationSupervisionUnitDialog: {
            titleName: "通信建设工程监理服务项目考评表（工程实施阶段-权重70%）",
            assessmentType: "implementationSupervisionUnit"
          },
          implementationConstructionUnitDialog: {
            titleName: "通信建设工程施工服务项目考评表（工程实施阶段-权重70%）",
            assessmentType: "implementationConstructionUnit"
          },
          acceptanceSupervisionUnitDialog: {
            titleName: "通信建设工程监理服务项目考评表（工程验收阶段-权重20%）",
            assessmentType: "acceptanceSupervisionUnit"
          },
          acceptanceConstructionUnitDialog: {
            titleName: "通信建设工程施工服务项目考评表（工程验收阶段-权重20%）",
            assessmentType: "acceptanceConstructionUnit"
          }
        }
      }
    },
    created() {
      this.operateState = this.$route.query.operateState; // 查看/填报
      this.deductionNode = this.$route.query.deductionNode;
      this.businessId = this.$route.query.businessId;
      const encodedData = this.$route.query.data;
      const decodedData = decodeURIComponent(encodedData);
      const row = JSON.parse(decodedData);

      console.log("路由跳转数据1", this.$route.query);
      this.currentDialog = row.stageType;
      this.flowData.proFlowId=row.id
    },
    activated() {

    },
    methods: {
      lookDetails(stage) {
        this.currentDialog = stage
      }
    }
  }
</script>
