/** * @author: yewenbin * @date: 2024-05-08 * @description:
合作单位后评估流程-配置管理-项目任务所在阶段考核表模板配置 */
<template>
  <div>
<!--   状态：{{this.operateState}}
    节点：{{this.deductionNode}}
    <!-- 三个角色的申述页面 -->
    <DetailsList ref="detailsList" v-if="operateState === 'appeal_fill' || operateState === 'appeal_chek' || operateState === 'admin_chek'"
:problemCollectionFlowId='flowData.proFlowId'
:operateState='operateState'
:businessId='businessId'
    ></DetailsList>
    <mssCard :title="titleName" v-else>
      <div slot="headerBtn">
        <el-button type="primary" v-if="operateState==='assess_fill'" @click="handleSave"
          >保存</el-button
        >
        <el-button @click="goBack">返回</el-button>
      </div>
      <div slot="content" class="common-form">
        <div v-show="!isEditVisible">
          <!--查看表格 -->
          <el-table
            :data="tableList"
            class="table"
            row-key="id"
            :header-cell-style="rowClass"
            :span-method="handleSpanMethod"
            show-summary
            :summary-method="getSummaries"
            default-expand-all
            border
          >
            <el-table-column
              prop="assessmentProject"
              label="考核项目"
              width="80"
              align="center"
            ></el-table-column>
            <el-table-column label="考核内容" align="center">
              <el-table-column label="考核内容" align="center" width="120px">
                <template scope="scope">
                  <div>{{ scope.row.assessmentContext }}</div>
                  <span>({{ scope.row.assessmentContextScore }}分)</span>
                </template>
              </el-table-column>
              <el-table-column
                label="考核内容类型"
                prop="assessmentContextDetails"
                align="center"
                width="120px"
              >
                <template scope="scope">
                  <div>{{ scope.row.assessmentContextDetails }}</div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column
              prop="standard"
              align="center"
              label="标准及要求"
            ></el-table-column>
            <el-table-column prop="demerit" label="扣分规则"></el-table-column>
            <el-table-column label="扣分（分/次）" align="center" width="100px">
              <template scope="scope">
                <div
                  >{{ scope.row.deductPointsScore }}分/{{
                    scope.row.deductPointsNumber
                  }}次</div
                >
              </template>
            </el-table-column>
            <el-table-column
              label="分值"
              prop="standardScore"
              align="center"
              width="80px"
            >
              <template scope="scope">
                <div v-show="scope.row.rowNumber !== 0">
                  {{ scope.row.standardScore }}
                </div>
              </template>
            </el-table-column>
            <template v-if="operateState !='appeal_msg'">
              <el-table-column
              label="汇总扣分说明"
              prop="standardScore"
              align="center"
              width="80px"
            >
              <template scope="scope">
                <div v-html="scope.row.deductionReasonHistory" ></div>
              </template>
            </el-table-column>
            <el-table-column
              label="汇总扣分次数"
              prop="standardScore"
              align="center"
              width="80px"
              v-if="operateState !='inter_fill'"
            >
              <template scope="scope">
                {{ scope.row.deductionNumHistory }}
              </template>
            </el-table-column>
            <el-table-column
              label="汇总扣分值"
              prop="standardScore"
              align="center"
              width="80px"
              v-if="operateState !='inter_fill'"
            >
              <template scope="scope">
                {{ scope.row.deductionValueHistory }}
              </template>
            </el-table-column>
            </template>

            <!-- 扣分开始 -->
            <template v-if="operateState ==='assess_fill'">
              <el-table-column
                label="扣分事项说明"
                align="center"
                width="180px"
              >
                <template slot-scope="scope">
                  <el-input
                    type="textarea"
                    v-model="scope.row.deductionReason"
                    autocomplete="off"
                    style="margin-left: 20px; width: 80%"
                    clearable
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="出现次数" align="center" width="120px">
                <template slot-scope="scope">
                  <el-input
                    @change="value => onChange(value, '出现次数', scope.$index)"
                    v-model="scope.row.deductionNum"
                    autocomplete="off"
                    style="width: 80%"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="扣分值" align="center" width="100px">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.deductionValue"
                    autocomplete="off"
                    style="width: 80%"
                    @change="value => onChange(value, '扣分值', scope.$index)"
                  ></el-input>
                </template>
              </el-table-column>
            </template>
            <template v-if="operateState ==='appeal_fill' || operateState ==='appeal_msg'">
              <el-table-column label="扣分事项说明" align="center" width="180px" prop="deductionReason">
              </el-table-column>
              <el-table-column label="扣分次数" align="center" width="120px" prop="deductionNum">
              </el-table-column>
              <el-table-column label="扣分值" align="center" width="100px" prop="deductionValue">
              </el-table-column>
              <el-table-column label="申诉原因" align="center" width="100px" prop="appealReason">
                <template scope="scope">
                  <span v-html="scope.row.appealReason"> </span>
                </template>
              </el-table-column>
              <el-table-column label="申诉附件" align="center" width="150px" prop="appealFileNames" v-if="operateState ==='appeal_fill'">
                <template scope="scope">
                  <span v-html="scope.row.appealFileNames"> </span>
                </template>
              </el-table-column>
            </template>
            <!-- 扣分结束 -->
            <el-table-column label="操作" align="center" width="100px" v-if="operateState ==='inter_fill'">
              <template slot-scope="scope">
                <el-button
                  @click="getAppeal(scope.row, 1)"
                  type="text"
                  size="small"
                  >申诉</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </mssCard>
    <AppealList ref="appealList" @getappealShow='getappealShow'></AppealList>
  </div>
</template>

<script>
  import { commonOneDel, commonMultDel, commonDown } from "@/utils/btn"
  import {
    editProjectTaskAssessmentConfig,
    editSuperviseIssueTypeConfig,
    selectProjectTaskAssessmentConfigList,
    getDeductionList,
    getReadingDetail
  } from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig"

  import {
    getAssessmentDeductionList,
    saveDeduction
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/deduction.js"
  import AppealList from "../../appeals_process/appeal_list.vue"
  import DetailsList from "./details_list.vue"
  import item from "@/layout/components/Sidebar/Item.vue"
  import it from "element-ui/src/locale/lang/it"
  export default {
    name: "projectTaskStageAssessmentDialog",
    components: { AppealList, DetailsList},
    props: {
      flowData: {
        type: Object,
        default: () => ({})
      },
      titleName: {
        type: String,
        default: ""
      },
      assessmentType: {
        type: String,
        default: ""
      },
      operateState: { // 操作状态
        type: String,
        default: ""
      },
      businessId: {
        type: String,
        default: ""
      },
      deductionNode: {
        type: String,
        default: ""
      }
    },
    data() {
      return {
        assessmentDeductionList: {},
        allowDeduction: true, // 是否显示扣分
        //是否编辑
        isEditVisible: false,
        //表格数据
        tableList: [],
        //分值单元格合并行数数组
        standardScoreRowspanArr: [],
        //考核内容单元格合并行数数组
        assessmentContextRowspanArr: [],
        //考核内容类型单元格合并行数数组
        assessmentContextDetailsRowspanArr: [],
        //标准及要求合并行数数组
        standardRowspanArr: [],
        //新增考核内容弹窗
        dialogVisible: false,
        //考核项目对象
        assessmentForm: {
          assessmentContext: "",
          assessmentContextScore: null
        },
        assessmentProject: "考评项目",
        //考核内容下拉框
        assessmentContextOptions: [],
        //考核内容对应的分数
        assessmentContextKey: [],
        //考核内容类型下拉框
        assessmentContextDetailsOptions: [],
        //是否显示考核内容类型，在查看页面全部为空时不显示
        isShowAssessmentContextDetails: true
      }
    },
    created() {
      this.initialization()
      if (this.operateState === 'appeal_chek' || this.operateState === 'admin_chek') {
        this.$refs.detailsList.operateState = this.operateState
        this.$refs.detailsList.problemCollectionFlowId = this.flowData.proFlowId
      }
    },
    mounted() {
    },
    computed: {},
    methods: {
      initialization () {
        if (this.operateState === 'appeal_fill' || this.operateState === 'appeal_msg') {
          this.appealWorkOrder("select")
        } else {
          this.selectTableList("select")
        }
      },
      getappealShow() {
        this.initialization()
      },
      async appealWorkOrder(stats) {
        let params = {
          stageType: this.assessmentType,
          problemCollectionFlowId: this.flowData.proFlowId,
          // problemCollectionFlowId: '1812693214486970372',
        }
        try {
          var res =[]
          if (this.operateState === 'appeal_fill') {
            res = await getDeductionList(params)
          } else {
            res = await getReadingDetail(params)
          }
          if (res.code === "0000") {
            this.tableList = res.data
            res.data.forEach(dataItem => {
              // 找到 tableList 中对应的项
              let match = this.tableList.find(
                item => item.id === dataItem.assessmentConfigId
              )
              if (match) {
                // 添加额外的历史信息
                match.deductionReasonHistory = dataItem.deductionReasonHistory
                match.deductionNumHistory = dataItem.deductionNumHistory
                match.deductionValueHistory = dataItem.deductionValueHistory
                match.version = dataItem.version
                match.deductionId = dataItem.id
              }
            })
            if (stats === "select") {
              this.isShowAssessmentContextDetails = false
              this.tableList.forEach(item => {
                if (
                  item.assessmentContextDetails !== undefined &&
                  item.assessmentContextDetails
                ) {
                  this.isShowAssessmentContextDetails = true
                }
              })
            } else {
              this.isShowAssessmentContextDetails = true
            }
            this.formatRowspanAndColspan()
          }
        } catch (error) {
          console.error("Error fetching data:", error)
          // 处理异常情况
        }
      },
      onChange(value, name, index) {
        if (name === "扣分值") {
          const regex = /^(0|([1-9][0-9]*))(\.[\d]+)?$/
          if (!regex.test(value)) {
            this.$message.error("请输入正确的数字类型")
            this.tableList[index].deductionValue = ""
          }
        } else {
          const regex = /^(0|([1-9][0-9]*))(\.[\d]+)?$/
          if (!regex.test(value)) {
            this.$message.error("请输入正确的数字类型")
            this.tableList[index].deductionNum = ""
          }
        }
      },
      getAppeal(row, val) {
        if (val === 1) {
          this.$refs.appealList.open(this.assessmentType, row, this.flowData.proFlowId)
        } else {
          this.$refs.detailsList.open(this.assessmentType, row, this.flowData.proFlowId)
        }

      },
      //保存按钮
      async handleSave() {
        let newList = this.tableList
          .map(item => ({
            assessmentConfigId: item.id,
            problemCollectionFlowId: this.flowData.proFlowId,
            deductionNum: item.deductionNum,
            deductionReason: item.deductionReason,
            deductionValue: item.deductionValue,
            assessmentContext: item.assessmentContext,
            version: item.version,
            id: item.deductionId
          }))
          .filter(item => {
            // 过滤条件：保留至少有一个字段有值的项
            return (
              item.deductionNum || item.deductionReason || item.deductionValue
            )
          })
        //   if (!this.isScope(tempSum, assessmentContextScore)) {
        //     this.$message.error("第" + (i + 1) + "行，分值不能大于考核内容总分值");
        //     return;
        //   }
        // }
        // editProjectTaskAssessmentConfig(this.tableList).then(res => {
          console.log("保存",this.businessId)
        let q = {
          assessmentDeductionList: newList,
          deductionNode: this.deductionNode,
          businessId :this.businessId
        }
        const res = await saveDeduction(q)
        if (res.code === "0000") {
          this.$message.success("保存成功")
          // this.getHisDeducation() // 查询扣分
          this.selectTableList("select")
          // this.isEditVisible = false
        }
        //  else {
        //   this.$message.success("保存失败，请刷新后重试")
        // }
      },
      // 查询历史扣分制
      async getHisDeducation() {
        let q = {
          problemCollectionFlowId: this.flowData.proFlowId,
          stageType: this.assessmentType
        }
        const res = await getAssessmentDeductionList(q)
        if (res.code === "0000") {
          res.data.forEach(dataItem => {
            // 找到 tableList 中对应的项
            let match = this.tableList.find(
              item => item.id === dataItem.assessmentConfigId
            )
            if (match) {
              // 添加额外的历史信息
              match.deductionReasonHistory = dataItem.deductionReasonHistory
              match.deductionNumHistory = dataItem.deductionNumHistory
              match.deductionValueHistory = dataItem.deductionValueHistory
              match.version = dataItem.version
              match.deductionId = dataItem.id
            }
          })
        }
      },
      goBack() {
        this.$router.go(-1) // 返回上一页
      },
      //-----------------------
      //查询表格数据
      async selectTableList(stats) {
        let params = {
          assessmentType: this.assessmentType
        }
        try {
          const res = await selectProjectTaskAssessmentConfigList(params)

          if (res.code === "0000") {
            this.tableList = res.data.data
            await this.getHisDeducation() // 等待插入扣分数据完成

            if (stats === "select") {
              this.isShowAssessmentContextDetails = false
              this.tableList.forEach(item => {
                if (
                  item.assessmentContextDetails !== undefined &&
                  item.assessmentContextDetails
                ) {
                  this.isShowAssessmentContextDetails = true
                }
              })
            } else {
              this.isShowAssessmentContextDetails = true
            }
            this.formatRowspanAndColspan()
          }
        } catch (error) {
          console.error("Error fetching data:", error)
          // 处理异常情况
        }
      },
      //合并表头
      rowClass({ row, column, rowIndex, columnIndex }) {
        if (rowIndex === 1) {
          return {
            display: "none"
          }
        }
      },
      //合并单元格
      handleSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
          if (rowIndex > 0) {
            return {
              rowspan: 0,
              colspan: 0
            }
          } else {
            return {
              rowspan: this.tableList.length,
              colspan: 1
            }
          }
          // } else if (columnIndex === 2) {
        } else if (columnIndex === 1) {
          //考核内容
          if (this.isShowAssessmentContextDetails) {
            const _row = this.assessmentContextRowspanArr[rowIndex]
            const _col = _row > 0 ? 1 : 0
            return {
              rowspan: _row,
              colspan: _col
            }
          } else {
            //不显示考核内容类型
            const _row = this.assessmentContextRowspanArr[rowIndex]
            const _col = _row > 0 ? 2 : 0
            return {
              rowspan: _row,
              colspan: _col
            }
          }
          // } else if (columnIndex === 3) {
        } else if (columnIndex === 2) {
          //考核内容类型
          if (this.isShowAssessmentContextDetails) {
            const _row = this.assessmentContextDetailsRowspanArr[rowIndex]
            const _col = _row > 0 ? 1 : 0
            return {
              rowspan: _row,
              colspan: _col
            }
          } else {
            //不显示考核内容类型
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        } else if (columnIndex === 3) {
          //标准及要求
          const _row = this.standardRowspanArr[rowIndex]
          const _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
          // } else if (columnIndex === 1 || columnIndex === 7) {
        } else if (columnIndex === 6) {
          //序列、分值
          const _row = this.standardScoreRowspanArr[rowIndex]
          const _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      },
      /**
       * 合并单元格辅助
       */
      formatRowspanAndColspan() {
        this.assessmentContextRowspanArr = []
        this.assessmentContextDetailsRowspanArr = []
        this.standardRowspanArr = []
        this.standardScoreRowspanArr = []
        let assessmentContextIndexPos = null
        let assessmentContextDetailsIndexPos = null
        let standardIndexPos = null
        if (this.tableList && this.tableList.length > 0) {
          for (let i = 0; i < this.tableList.length; i++) {
            //处理 分值、序号所占行数
            this.standardScoreRowspanArr.push(this.tableList[i].rowNumber)
            if (i === 0) {
              this.assessmentContextRowspanArr.push(1)
              assessmentContextIndexPos = 0
              this.assessmentContextDetailsRowspanArr.push(1)
              assessmentContextDetailsIndexPos = 0
              this.standardRowspanArr.push(1)
              standardIndexPos = 0
            } else {
              // 判断当前元素与上一个元素是否相同(第1行和第2行)
              //考核内容
              if (
                this.tableList[i].assessmentContext &&
                this.tableList[i - 1].assessmentContext &&
                this.tableList[i].assessmentContext ===
                  this.tableList[i - 1].assessmentContext
              ) {
                this.assessmentContextRowspanArr.push(0)
                this.assessmentContextRowspanArr[assessmentContextIndexPos] += 1
              } else {
                this.assessmentContextRowspanArr.push(1)
                assessmentContextIndexPos = i
              }
              //考核内容类型
              if (
                this.tableList[i].assessmentContextDetails !== undefined &&
                this.tableList[i].assessmentContextDetails &&
                this.tableList[i - 1].assessmentContextDetails !== undefined &&
                this.tableList[i - 1].assessmentContextDetails &&
                this.tableList[i].assessmentContextDetails ===
                  this.tableList[i - 1].assessmentContextDetails
              ) {
                this.assessmentContextDetailsRowspanArr.push(0)
                this.assessmentContextDetailsRowspanArr[
                  assessmentContextDetailsIndexPos
                ] += 1
              } else {
                this.assessmentContextDetailsRowspanArr.push(1)
                assessmentContextDetailsIndexPos = i
              }
              //标准及要求
              if (
                this.tableList[i].standard &&
                this.tableList[i - 1].standard &&
                this.tableList[i].standard === this.tableList[i - 1].standard
              ) {
                this.standardRowspanArr.push(0)
                this.standardRowspanArr[standardIndexPos] += 1
              } else {
                this.standardRowspanArr.push(1)
                standardIndexPos = i
              }
            }
          }
        }
      },
      //单元格向下合并
      mergeDownCells(index) {
        let mergeData = index + this.tableList[index].rowNumber
        if (this.tableList[mergeData].rowNumber > 1) {
          this.$message.error("无法合并单元格")
          return
        }
        this.tableList[index].rowNumber += 1
        this.tableList[mergeData].rowNumber -= 1
        this.tableList[mergeData].serialNumber =
          this.tableList[index].serialNumber
        this.tableList[mergeData].standardScore = 0
        this.setSerialNumber()
        this.formatRowspanAndColspan()
      },
      //单元格拆分
      splitCells(index) {
        let indexNext = index + this.tableList[index].rowNumber - 1
        this.tableList[indexNext].rowNumber = 1
        this.tableList[indexNext].standardScore = null
        this.tableList[indexNext].serialNumber =
          this.tableList[index].serialNumber + 1
        this.tableList[index].rowNumber -= 1
        this.setSerialNumber()
        this.formatRowspanAndColspan()
      },
      //序号处理
      setSerialNumber() {
        let serialNumber = 0
        for (let i = 0; i < this.tableList.length; i++) {
          let rowNumber = this.tableList[i].rowNumber
          if (rowNumber > 0) {
            serialNumber += 1
            this.tableList[i].serialNumber = serialNumber
          } else {
            this.tableList[i].serialNumber = serialNumber
          }
        }
      },
      //表格排序移动
      rowDrop(type, row, index) {
        //判断是否存在合并单元格
        if (row.rowNumber > 1 || row.rowNumber === 0) {
          this.$message.error("合并单元格无法移动")
          return
        }
        if (type === "up") {
          if (row.sort == 1) {
            return
          }
          if (
            this.tableList[index - 1].rowNumber > 1 ||
            this.tableList[index - 1].rowNumber === 0
          ) {
            this.$message.error("合并单元格无法移动")
            return
          }
          this.tableList[index - 1].sort = this.tableList[index - 1].sort + 1
          this.tableList[index].sort = this.tableList[index].sort - 1
          var elem = this.tableList.splice(index, 1)
          this.tableList.splice(index - 1, 0, elem[0])
        } else if (type === "down") {
          if (row.sort == this.tableList.length) {
            return
          }
          if (
            this.tableList[index + 1].rowNumber > 1 ||
            this.tableList[index + 1].rowNumber === 0
          ) {
            this.$message.error("合并单元格无法移动")
            return
          }
          this.tableList[index + 1].sort = this.tableList[index + 1].sort - 1
          this.tableList[index].sort = this.tableList[index].sort + 1
          var elem = this.tableList.splice(index, 1)
          this.tableList.splice(index + 1, 0, elem[0])
        }
        this.setSerialNumber()
      },
      //合计
      getSummaries(param) {
        const { columns, data } = param
        const sums = []
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = "合计"
            return
          } else if (index === 6) {
            const values = data.map(item => Number(item[column.property]))
            if (!values.every(value => isNaN(value))) {
              sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!isNaN(value)) {
                  return prev + curr
                } else {
                  return prev
                }
              }, 0)
            } else {
              sums[index] = 0
            }
          }
        })

        return sums
      },

      //编辑按钮
      handleUpdate() {
        this.selectTableList("edit")
        this.isEditVisible = true
      },

      //判断分值是否超过考核内容分值
      isScope(standardScore, assessmentContextScore) {
        if (standardScore > assessmentContextScore) {
          return false
        }
        return true
      },
      //编辑考核内容按钮
      handleUpdateForm(index) {
        this.assessmentForm = this.tableList[index]
        this.dialogVisible = true
      },
      //薪资考核内容-考核内容总分值设置
      handleSelectChange() {
        let assessmentContextScore
        if (
          this.assessmentContextOptions &&
          this.assessmentContextOptions.length > 0
        ) {
          // 检查选中的值是否存在于选项中
          const selectedItemExists = this.assessmentContextOptions.some(
            item => item.value === this.assessmentForm.assessmentContext
          )
          console.log("selectedItemExists：", selectedItemExists)
          // 根据选中的值来决定是否改变 el-input 的状态和赋值
          if (selectedItemExists) {
            for (let i = 0; i < this.assessmentContextKey.length; i++) {
              let item = this.assessmentContextKey[i]
              if (item.key === this.assessmentForm.assessmentContext) {
                assessmentContextScore = item.value
                this.$set(
                  this.assessmentForm,
                  "assessmentContextScore",
                  assessmentContextScore
                )
                return
              }
            }
          }
        }
      },
      //新增考核内容数据
      dialogAddData() {
        this.$refs["assessmentForm"].validate(valid => {
          if (valid) {
            if (!this.assessmentForm.id) {
              let sortNum = this.tableList ? this.tableList.length + 1 : 1
              let serialNumber = 1
              if (this.tableList && this.tableList.length > 0) {
                serialNumber =
                  this.tableList[this.tableList.length - 1].serialNumber + 1
              }
              let data = {
                id: this.tableList.length + 1 + Date.now(),
                rowNumber: 1,
                serialNumber: serialNumber,
                assessmentProject: this.assessmentProject,
                assessmentContext: this.assessmentForm.assessmentContext,
                assessmentContextScore:
                  this.assessmentForm.assessmentContextScore,
                assessmentContextDetails:
                  this.assessmentForm.assessmentContextDetails,
                standard: this.assessmentForm.standard,
                deductPointsScore: this.assessmentForm.deductPointsScore,
                deductPointsNumber: this.assessmentForm.deductPointsNumber,
                demerit: this.assessmentForm.demerit,
                assessmentType: this.assessmentType,
                standardScore: null,
                sort: sortNum
              }
              this.tableList.push(data)
            }
            this.formatRowspanAndColspan()
            this.closeDialog()
          } else {
            return false
          }
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  .el-textarea__inner {
    resize: none;
  }
</style>
