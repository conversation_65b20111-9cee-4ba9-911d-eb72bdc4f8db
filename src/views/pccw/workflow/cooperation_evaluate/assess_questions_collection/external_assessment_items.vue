<template>
  <div>
    <div slot="content">
      <mssCard title="外部录入考核事项列表">
        <div slot="content">
          <mssTableCustom
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            :static-search-param="staticSearchParam"
            border
            :pagination="false"
          >
          </mssTableCustom>
        </div>
      </mssCard>
    </div>

    <el-dialog
      title="附件列表"
      :visible.sync="pendingShow"
      :close-on-click-modal="false"
      @close="pendingShow = false"
      width="50%"
    >
      <div>
        <mssTable
          ref="attachment"
          :columns="tableHeader2"
          :stationary="tableData"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="pendingShow = false"
          >确 定</el-button
        >
        <el-button
          size="mini"
          style="margin-left: 10px"
          @click="pendingShow = false"
          >取 消</el-button
        >
      </span>
    </el-dialog>

  </div>
</template>

<script>
  import {
    uploadFile,
    selectFileList,
    downloadFile,
    downloadTemplate,
    deleteFile,
    exportTotal,
    importEntryCheckData,
    deleteEntryCheckData
  } from "@/api/pccw/cooperation_evaluate/external"

  import { getList } from "@/api/pccw/report_select/task_transfer/maintain_finished/index.js"
  import {
    getEntryCheckList, // 考核事项
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/index.js"
  import { commonDown } from '@/utils/btn'
  export default {
    props: {
      scoreMonth: {
        type: String,
        required: true
      }
    },
    components: {},
    data() {
      return {
        // 表格数据API
        tableApi: getEntryCheckList,
        flow: {},
        approveType: "",
        showTransact: false,
        businessId: "",
        // 默认表格搜索条件
        staticSearchParam: {
          entryMonth: "2024-08"
        },
        // 表头
        tableHeader: [
          {
            prop: "constructionUnit",
            label: "建设单位",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectNumber",
            label: "项目编码",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "partnerName",
            label: "合作单位名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "entryMonth",
            label: "问题录入月份",
            align: "center",
            tooltip: true
          },
          {
            prop: "problemDesc",
            label: "考核问题说明",
            align: "center",
            tooltip: true
          },
          {
            prop: "problemDesc",
            label: "操作",
            align: "center",
            tooltip: true,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                    onClick= {() => {this.checkFile(row)}}
                    type="text">
                      查看附件
                    </el-button>
                  </span>
                </span>
              )
            },
          }
        ],
        tableData: [],
        fileData: {},
        pendingShow: false,
        tableHeader2: [
          {
            prop: "fileName",
            label: "附件名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "",
            label: "操作",
            align: "center",
            tooltip: true,
            minWidth: 50,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.getDownload(3, row)
                      }}
                    >
                      下载附件
                    </el-button>
                  </span>
                </span>
              )
            }
          }
        ],
      }
    },
    created() {

    },
    mounted() {},
    methods: {
      checkFile (row) {
        this.fileData = row
        selectFileList({ id: row.id,limit: 1000 }).then((res)=>{
          this.tableData = res.data.data
          this.pendingShow = true
        })
      },
      getDownload(val, row) {
        if (val === 1) {
          commonDown({ limit: -1 }, downloadTemplate)
        } else if (val === 2) {
          commonDown(
            { ...this.$refs.searchForm.searchForm, limit: -1 },
            exportTotal
          )
        } else if (val === 3) {
          commonDown({ ...row, limit: -1 }, downloadFile)
        }
      },
    }
  }
</script>

<style lang="scss" scoped></style>
