<template>
  <div>
    <div slot="content">
      <!--查询框 -->
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <mssCard title="合作单位考核问题台账报表 ">
        <div slot="headerBtn">
          <el-button type="primary" @click="downloadData()">导出</el-button>
        </div>
        <div slot="content">
          <mssTable
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            :static-search-param="staticSearchParam"
            border
          >
          </mssTable>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
    import qs from 'qs';
  import {
    exportData,
    reportList
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"

  export default {
    components: {},
    data() {
      return {
        // 表格数据API
        tableApi: reportList,

        searchConfig: {},
        //搜索字段配置
        searchConfig: [
          {
            label: "评分月份",
            type: "input",
            fieldName: "scoreMonth"
          },
          {
            label: "合作单位类型",
            type: "input",
            fieldName: "unitType"
          },
          {
            label: "合作单位名称",
            type: "input",
            fieldName: "unitName"
          },
          {
            label: "框架合同编码",
            type: "input",
            fieldName: "frameContCode"
          },
          {
            label: "项目编码",
            type: "input",
            fieldName: "projectCode"
          },
          {
            label: "项目名称",
            type: "input",
            fieldName: "projectName"
          },
          {
            label: "考核阶段",
            type: "select",
            options: [],
            fieldName: "stageType"
          },
          {
            label: "建设单位",
            type: "input",
            fieldName: "constructUnit"
          },
          {
            label: "区县",
            type: "input",
            fieldName: "district"
          },
          {
            label: "反馈人",
            type: "input",
            fieldName: "deductionName"
          },
          {
            label: "评分状态",
            type: "select",
            options: [],
            fieldName: "scoreState"
          }
        ],
        msgApproval: null,
        allDealt: true,
        flow: {},
        approveType: "",
        showTransact: false,
        businessId: "",
        // 默认表格搜索条件
        staticSearchParam: {},
        // 表头
        tableHeader: [
          {
            prop: "scoreMonth",
            label: "评分月份",
            align: "center"
          },
          {
            prop: "unitType",
            label: "合作单位类型",
            align: "center"
          },
          {
            prop: "unitName",
            label: "合作单位名称",
            align: "center"
          },
          {
            prop: "frameContCode",
            label: "框架合同编码",
            align: "center"
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center"
          },
          {
            prop: "projectName",
            minWidth: "200px",
            label: "项目名称",
            align: "center"
          },
          {
            prop: "stageTypeName",
            label: "考核阶段",
            align: "center"
            // formatter:this.formatStageType
          },
          {
            prop: "constructUnit",
            label: "建设单位",
            align: "center"
          },
          {
            prop: "city",
            label: "地市",
            align: "center"
          },
          {
            prop: "district",
            label: "区县",
            align: "center"
          },
          {
            prop: "demerit",
            label: "扣分规则",
            minWidth: "200px",
            align: "center"
          },
          {
            prop: "assessmentContextScore",
            label: "分值",
            align: "center"
          },
          {
            prop: "deductionValue",
            label: "扣分值",
            align: "center"
          },
          {
            prop: "deductionReason",
            label: "扣分原因",
            align: "center"
          },
          {
            prop: "deductionName",
            label: "反馈人",
            align: "center"
          },
          {
            prop: "scoreState",
            label: "评分状态",
            align: "center",
            // formatter: row => {
            //   if (row.scoreState) {
            //     const status = this.scoreStateList.find(
            //       item => item.value === row.scoreState
            //     )

            //     return status ? status.label : "未知"
            //   } else {
            //     return "未知"
            //   }
            // }
          }
        ],
        stageTypeMap: {
          surveyDesignStage: "设计单位勘察设计阶段",
          implementAcceptanceStage: "设计单位实施验收阶段",
          prepareConstructionUnit: "施工单位工程准备阶段",
          prepareSupervisionUnit: "监理单位工程准备阶段",
          implementationConstructionUnit: "施工单位工程实施阶段",
          implementationSupervisionUnit: "监理单位工程实施阶段",
          acceptanceConstructionUnit: "施工单位工程验收阶段",
          acceptanceSupervisionUnit: "监理单位工程验收阶段"
        },
        scoreStateList: []
      }
    },
    created() {},
    mounted() {
      // 设置第7个搜索字段的选项值
      const list = Object.entries(this.stageTypeMap).map(([value, label]) => ({
        label,
        value
      }))

      this.scoreStateList = [
        {
          label: "考核问题收集中",
          value: "考核问题收集中"
        },
        {
          label: "已完成收集",
          value: "已完成收集"
        },
        {
          label: "申诉中",
          value: "申诉中"
        },
        {
          label: "申诉完成",
          value: "申诉完成"
        }
      ]
      // 第7条
      this.$set(this.searchConfig[6], "options", list)
      console.log('下拉框',this.searchConfig[10])
      // 第11条
      this.$set(this.searchConfig[10], "options", this.scoreStateList)
    },
    methods: {
      formatStageType(row, column, cellValue, index) {
        return this.stageTypeMap[cellValue] || "未知阶段"
      },
      async downloadData(type) {
        let exportParams = {
          ...this.staticSearchParam
        }
        // 获取page对象
        let page=this.$refs.table.page
        console.log('page',page)
        exportParams.page=page.current
        exportParams.limit=page.size
        exportParams.exportType = type
        // let res = await exportData(qs.stringify(exportParams))
        let res = await exportData(qs.stringify(exportParams))
        console.log(res, "文件")
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", "合作单位考核问题台账报表.xlsx")
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      },
      handleQueryParameters() {},
      //重置
      reset() {
        this.search()
      },
      //搜索
      search() {
        this.$refs.table.page.current = 1
        // 将搜索表单的深拷贝到 this.staticSearchParam ，分页用
        this.staticSearchParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      }
    }
  }
</script>

<style lang="scss" scoped></style>
