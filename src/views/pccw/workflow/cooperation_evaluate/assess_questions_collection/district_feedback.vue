<template>
  <div>
    <div slot="content">
      <mssCard title="区县反馈人列表">
        <div slot="content">
          <mssTable ref="table2" :pagination="false" :api="tableApi2" :columns="tableHeader2"
            :static-search-param="staticSearchParam" border>
          </mssTable>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
  import {
    getFeedbackByBid
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"
  export default {
    props: {
      staticSearchParam: {
        type: Object,
        default: true
      }
    },
    components: {},
    data() {
      return {
        tableApi2: getFeedbackByBid,
        // 区县反馈人列表
        tableHeader2: [{
            prop: "constructionUnit",
            label: "地市",
            align: "center"
          },
          {
            prop: "county",
            label: "区县",
            align: "center"
          },
          {
            prop: "feedbackUser",
            label: "反馈人",
            align: "center"
          }
        ],
      }
    },
    created() {
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow) // 获取流程对象
    },
    mounted() {},
    methods: {
      handleQueryParameters() {}
    }
  }
</script>

<style lang="scss" scoped></style>
