<template>
  <div>
    <div slot="content">
      <!--查询框 -->
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <mssCard title="考核问题收集流程催办 ">
        <div slot="headerBtn">
          <!-- <el-button type="primary" @click="toUrge(all)">全量催办</el-button> -->
          <el-button type="primary" @click="toUrge()">催办</el-button>
        </div>
        <div slot="content">
          <mssTableCustom
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            :static-search-param="staticSearchParam"
            border
            selection="true"
            :getChange="true"
            @getChange="handleGetChange"
          >
          </mssTableCustom>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
  import {
    urgeList,
    handleUrge
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"
  import {
    getRecord,
    updateRecord,
    getRecordById,
    delBatchByidList
  } from "@/api/pccw/workflow/march.js"

  export default {
    components: {},
    data() {
      return {
        // 表格数据API
        tableApi: urgeList,
        // 默认表格搜索条件
        staticSearchParam: {},
        //搜索字段配置
        searchConfig: [
          {
            label: "待办标题",
            type: "input",
            fieldName: "title"
          },
          {
            label: "审批状态",
            type: "select",
            options: [],
            fieldName: "msgType"
          },
          {
            label: "处理环节",
            type: "select",
            options: [],
            fieldName: "activity"
          },
          {
            label: "建设单位",
            type: "input",
            fieldName: "constructUnit"
          },
          {
            label: "区县",
            type: "input",
            fieldName: "district"
          }
        ],
        msgApproval: null,
        allDealt: true,
        flow: {},
        approveType: "",
        showTransact: false,
        businessId: "",

        // 表头
        tableHeader: [
          {
            prop: "title",
            label: "待办标题",
            align: "center"
          },
          {
            prop: "msgType",
            label: "审批状态",
            align: "center",
            formatter: row => {
              return <span>{row.msgType == "0" ? "审批中" : "已完成"}</span>
            }
          },
          {
            prop: "activity",
            label: "处理环节",
            align: "center"
          },
          {
            prop: "constructUnit",
            label: "建设单位",
            align: "center"
          },
          {
            prop: "district",
            label: "区县",
            align: "center"
          },
          {
            prop: "targetsName",
            label: "处理人",
            align: "center"
          }
        ],
        selectionList: []
      }
    },
    created() {
      // this.msgApproval='状态未处理完毕'
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow) // 获取流程对象
    },
    mounted() {
      // 设置第二个搜索字段的选项值
      const list = [
        {
          label: "审批中",
          value: "0"
        },
        {
          label: "已完成",
          value: "1"
        }
      ]
      this.$set(this.searchConfig[1], "options", list)
      // 设置第三个搜索字段的选项值
      const optionsLabels = [
        "合作单位管理员下发待办",
        "区县工程事务员填报",
        "区县建维主管审批",
        "区县分管领导审批",
        "合作单位管理员汇总审核并填报",
        "工程实施经理主审核",
        "合作单位管理员汇总确认",
        "三级经理审核",
        "问题公示"
      ]
      const list2 = optionsLabels.map(label => ({
        label,
        value: label // 假设value和label相同，如果不同可以自定义
      }))

      // 现在list2包含了所有你需要的对象
      this.$set(this.searchConfig[2], "options", list2)
    },
    methods: {
      // 处理选择的数组
      handleGetChange(selection) {
        // 获取到子组件通过 $emit 传递的数据
        console.log("selection", selection)
        this.selectionList = selection
      },
      async toUrge(type) {
        if (type === "all") {
        }
        let res = await handleUrge(this.selectionList)
        console.log(res, "催办结果")
        if (res.code === "0000") {
          this.$message.success(res.data)
        }
      },
      async downloadData(type) {
        let exportParams = {
          ...this.staticSearchParam
        }
        exportParams.exportType = type
        // let res = await exportData(qs.stringify(exportParams))
        let res = await exportData()
        console.log(res, "文件")
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", "合作单位考核问题台账报表.xlsx")
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      },
      customAction() {
        // 打开审批对话框
        this.$refs.flowDetail.openApprovalDialog()
      },
      handleQueryParameters() {},
      // 搜索
      search() {
        this.$refs.table.page.current = 1
        this.staticSearchParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      // 重置
      reset() {
        this.search()
      }
    }
  }
</script>

<style lang="scss" scoped></style>
