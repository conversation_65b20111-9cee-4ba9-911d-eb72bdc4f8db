<template>
  <div>
    <div slot="content">
      <mssCard title="考核问题收集任务下发工单-基础信息">
        <div
          slot="content"
          style="
            line-height: 1.8; /* 增大行高 */
            margin-bottom: 10px; /* 每行底部增加额外空间 */
          "
        >
          <div class="content-text" v-if="node < 7">
            <span>要求反馈时间：</span>
            <template v-if="node == 1 && approveType === 'todo'">
              <el-date-picker
                v-model="requiredFeedbackDate"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </template>

            <template v-if="node != 1 && baseInfo.requiredFeedbackDate">
              <span v-if="baseInfo.requiredFeedbackDate != ''">{{
                baseInfo.requiredFeedbackDate
              }}</span>
              <span v-else>未填写</span>
            </template>
          </div>
          <div>当前节点：{{ flow.nodeName }} </div>
          <div v-if="nextNode"> 下一节点：{{ nextNode }} </div>
          <div v-if="nextNodePeople">
            下一节点审批人：{{ nextNodePeople }}
          </div>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
  import {
    getBaseInfoList,
    getRequiredFeedbackDate,
    getNextUsersName
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"
  export default {
    props: {
      flow: {
        type: Object
      },
      node: {
        type: Number
      },
      approveType: {
        type: String
      }
    },
    computed: {
      // 使用计算属性来根据flow.nodeName的值动态计算nextNode
      nextNode() {
        const nodeNameToNodeMap = {
          合作单位管理员下发待办: "区县工程事务员填报",
          区县工程事务员填报: "区县建维主管审批",
          区县建维主管审批: "区县分管领导审批",
          区县分管领导审批: "合作单位管理员汇总审核并填报",
          合作单位管理员汇总审核并填报: "工程实施经理主审核并填报",
          合作单位管理员审核并填报: "工程实施经理主审核并填报",
          工程实施经理主审核并填报: "合作单位管理员汇总确认",
          合作单位管理员汇总确认: "三级经理审核",
          三级经理审核: "合作单位管理员问题公示",
          合作单位管理员问题公示: "合作单位考核接口人发起申诉"
        }
        // 使用this.flow.nodeName作为键来从映射中获取对应的值
        return nodeNameToNodeMap[this.flow.nodeName] || "" // 如果找不到对应的键，则返回空字符串
      }
    },
    data() {
      return {
        nextNodePeople: null,
        // nextNode: "",
        requiredFeedbackDate: "",
        // 基础信息
        baseInfo: {
          id: "",
          businessId: this.flow.businessId,
          requiredFeedbackDate: ""
        }
      }
    },
    created() {
      if (this.flow && this.flow.businessId) {
        this.baseInfo.businessId = this.flow.businessId
      }

      this.getBaseInfo()
    },

    methods: {
      async getBaseInfo() {
        let query = {
          businessId: this.flow.businessId,
          node: this.node,
          nodeName: this.flow.nodeName
        }
        let res = await getRequiredFeedbackDate(query)
        console.log("res", res)
        if (res.code === "0000" && res.data) {
          this.baseInfo = res.data
          if (this.baseInfo) {
            this.requiredFeedbackDate = this.baseInfo.requiredFeedbackDate
          }
        }

        let res2 = await getNextUsersName(query)
        console.log("getNextUsersName数据", res2)

        console.log("this.baseInfo", this.baseInfo)
        console.log("this.requiredFeedbackDate", this.requiredFeedbackDate)

        let result = "系统管理员"
        switch (this.flow.nodeName) {
          case "合作单位管理员下发待办":
            result = res2.data
            break
          // case "区县工程事务员填报":
          //   result = "区县建维主管审批";
          //   break;
          case "区县分管领导审批":
          case "工程实施经理主审核并填报":
          case "三级经理审核":
            result = this.baseInfo.createName
            break
          case "区县工程事务员填报":  // 区县建维主管
          case "区县建维主管审批": // 区县分管领导
          case "合作单位管理员汇总审核并填报": // 实施经理数据
          case "合作单位管理员问题公示": // 接口人数据
          case "合作单位管理员汇总确认": // 三级经理数据
            result = res2.data
            break
        }
        console.log("result", result)
        if (result === "") {
          //测试数据
          result = "系统管理员"
        }
        this.nextNodePeople = result
      }
    }
  }
</script>

<style lang="scss" scoped></style>
