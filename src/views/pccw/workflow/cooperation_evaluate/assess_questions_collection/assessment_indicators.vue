<template>
  <div>
    <div slot="content">
      <mssCard title="考核指标">
        <div slot="content">
          <mssTableCustom
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            :static-search-param="staticSearchParam"
            border
            :pagination="false"
          >
          </mssTableCustom>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
  import { getList } from "@/api/pccw/report_select/task_transfer/maintain_finished/index.js"
  import {
    getEntryCheckList, // 考核事项
    getSuperviseIssue, // 发现问题
    getAssessmentIndex // 考核指标 考核指标不需要传entryMonth
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/index.js"
  export default {
    components: {},
    props: {
      scoreMonth: {
        type: String,
        required: true
      },
      constructUnit: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        // 表格数据API
        tableApi: getAssessmentIndex,
        // 默认表格搜索条件
        staticSearchParam: {
          // entryMonth: "2024-07-01"
        },
        // 表头
        tableHeader: [
          {
            prop: "constructUnit",
            label: "建设单位",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectName",
            label: "项目",
            align: "center",
            tooltip: true
          },
          {
            prop: "unitName",
            label: "施工单位",
            align: "center",
            tooltip: true
          },
          {
            label: "派工完工情况",
            align: "center",
            tooltip: true,
            children: [
              {
                prop: "requiredCompletionCount",
                label: "上月及以前应完工站点数",
                align: "center",
                tooltip: true
              },
              {
                prop: "actualCompletionCount",
                label: "上月实际完工站点数",
                align: "center",
                tooltip: true
              },
              {
                prop: "dispatchRate",
                label: "完工及时率",
                align: "center",
                tooltip: true
              }
            ]
          },
          {
            label: "已完工-关键工序上传及时率",
            align: "center",
            tooltip: true,
            children: [
              {
                prop: "shouldUploadnoCompleted",
                label: "已完工-上月及以前应上传关键工序数量",
                align: "center",
                tooltip: true
              },
              {
                prop: "haveUploadnoCompleted",
                label: "已完工-上月及以前已上传关键工序数量",
                align: "center",
                tooltip: true
              },
              {
                prop: "keyProcessTimelySubmissionRate",
                label: "已完工-关键工序提交及时率",
                align: "center",
                tooltip: true
              },
              {
                prop: "havePassednoCompleted",
                label: "已完工-关键工序审核通过数量",
                align: "center",
                tooltip: true
              }
            ]
          },
          {
            label: "已完工-关键工序上传及时率",
            align: "center",
            tooltip: true,
            children: [
              {
                prop: "shouldUploadnoUncompleted",
                label: "未完工-上月及以前应上传关键工序数量",
                align: "center",
                tooltip: true
              },
              {
                prop: "haveUploadnoUncompleted",
                label: "未完工-上月及以前已上传关键工序数量",
                align: "center",
                tooltip: true
              },
              {
                prop: "keyProcessTimelySubmissionRateUncompleted",
                label: "未完工-关键工序提交及时率",
                align: "center",
                tooltip: true
              },
              {
                prop: "havePassednoUncompleted",
                label: "未完工-关键工序审核通过数量",
                align: "center",
                tooltip: true
              }
            ]
          },
          {
            label: "QIP视频验收完成率",
            align: "center",
            tooltip: true,
            children: [
              {
                prop: "videoFinishScale",
                label: "上月应完成视频验收站点数",
                align: "center",
                tooltip: true
              },
              {
                prop: "videoFinishCount",
                label: "上月及以前已完成视频验收并通过站点",
                align: "center",
                tooltip: true
              },
              {
                prop: "videoRate",
                label: "视频验收完成率",
                align: "center",
                tooltip: true
              }
            ]
          }
        ]
      }
    },
    created() {
      this.staticSearchParam.constructUnit=this.constructUnit
      this.staticSearchParam.scoreMonth=this.scoreMonth

    },
    mounted() {},
    methods: {}
  }
</script>

<style lang="scss" scoped></style>
