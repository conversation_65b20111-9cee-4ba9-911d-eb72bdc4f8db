<!-- <template>
  <div>
    <div slot="content">
        <mssCard title="考核问题收集任务下发工单-基础信息">
          <div slot="content">
            流程名称flowKey：{{ flow.flowKey }}<br />
            节点名称nodeName：{{ flow.nodeName }}<br />
            流程名称workflowName：{{ flow.workflowName }}<br />
            taskId：{{ flow.taskId }}<br />
            node：{{ node }}<br />
            businessId:{{ flow.businessId }}<br />
            ------------------------
            <el-row :gutter="20">
              <el-col :span="9">
                <div class="content-text">
                  <span>要求反馈时间：</span>
                  <template v-if="node == 1">
                    <el-date-picker v-model="baseInfo.requiredFeedbackDate" type="date" placeholder="选择日期"
                      style="width: auto" value-format="yyyy-MM-dd"></el-date-picker>
                  </template>
                  <template v-else>
                    {{ baseInfo.requiredFeedbackDate }}
                  </template>
                </div>
              </el-col>
            </el-row>
          </div>
        </mssCard>
    </div>
  </div>
</template>

<script>
  import {
    getBaseInfoList
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"
  export default {
    props: {
      flow: {
        type: Object
      },
      node: {
        type: Number
      },
    },
    data() {
      return {
        // 基础信息
        baseInfo: {
          businessId: this.flow.businessId,
          createName: "",
          createTime: "",
          requiredFeedbackDate: "无"
        },
      }
    },
    created() {
      this.getBaseInfo()
    },
    methods: {
      async getBaseInfo() {
        let q = {
          businessId: this.flow.businessId
        }
        const res = await getBaseInfoList(q)
        if (res.code === "0000" && res.data && res.data.data) {
          this.baseInfo = res.data.data[0]
        }
      },
    }
  }
</script>

<style lang="scss" scoped></style> -->
