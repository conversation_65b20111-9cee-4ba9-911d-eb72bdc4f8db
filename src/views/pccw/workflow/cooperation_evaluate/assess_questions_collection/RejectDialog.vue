<template>
  <div>
    <div slot="content">
      <el-dialog
        title="请选择可驳回人员"
        :visible.sync="dialogVisible"
        width="30%"
        :before-close="handleClose"
      >
        <div>
          <!-- {{returnableList.flowList[0].deductedUserList}} -->
<!--          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            >全选</el-checkbox
          > -->
          <div style="margin: 15px 0"></div>
          <el-checkbox-group
            v-if='returnableList'
            v-model="checkedDatas"
            @change="handleCheckedCitiesChange"
          >
            <el-checkbox
              v-for="city in checkedList"
              :label="city.label"
              :key="city.value"
              >{{ city.label }}
              </el-checkbox>
          </el-checkbox-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="rejectFlow()"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
  import {
    startReject
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"
  export default {
    props: {
      // returnableList: {
      //   type: Object,
      //   required: true
      // },
      // node: {
      //   type: Number,
      //   required: true,
      //   default:0
      // },
    },
    components: {},
    watch: {
      'returnableList': function(newVal, oldVal) {
        console.log("子组件数据", this.returnableList,"节点",this.returnableList.node)
        // 驳回至实施经理
        if(this.returnableList.node === 5){
        let uniqueDeductedUserList = this.returnableList.flowList.reduce((acc, item) => {
          // 使用 provincialDepartmentManagerId 作为键来确保唯一性
          const key = item.provincialDepartmentManagerId;
          // 如果 Map 中还没有这个键，则添加新项
          if (!acc.has(key)) {
            acc.set(key, {
              label: item.projectImplementationManagerPrimary,
              value: item.projectImplementationManagerPrimaryId
            });
          }
          return acc;
        }, new Map());

        // map转数组
        this.returnableList.selectUsers = Array.from(uniqueDeductedUserList.values());
        // 驳回至区县
        }else if(this.returnableList.node === 3||this.returnableList.node === 4){
          console.log("驳回至区县2")
        this.returnableList.selectUsers=this.returnableList.flowList[0].deductedUserList // 单条的写法，多条再进行修改
        }
        this.checkedList=this.returnableList.selectUsers
      }
    },
    data() {
      return {
        checkedList:[],
        returnableList: null,
        checkAll: false,
        checkedDatas: [],
        // 驳回人员数据示例
        // deductedUserList: [
        //   {
        //     label: "用户1",
        //     value: "1"
        //   },
        //   {
        //     label: "用户2",
        //     value: "2"
        //   },
        //   {
        //     label: "用户3",
        //     value: "3"
        //   },
        //   {
        //     label: "用户4",
        //     value: "4"
        //   }
        // ],
        cities: ["上海", "北京", "广州", "深圳"],
        isIndeterminate: true,
        dialogVisible: false
      }
    },
    created() {

    },
    mounted() {},
    methods: {
      // 驳回
      async rejectFlow() {


     // this.returnableList.selectUsers = this.returnableList.selectUsers.filter(
     // item =>
     // if(this.checkedDatas)
     // item.taskName !== "退回考核接口人");

        // 过滤数据
        this.returnableList.selectUsers=this.checkedList.filter(item => this.checkedDatas.includes(item.label));
        console.log("要退回的用户",this.returnableList.selectUsers)

        const res = await startReject(this.returnableList)
        console.log("返回", res)
        if (res.code === "0000") {
          this.$message.success("退回成功!")
          // 关闭对话框
          this.dialogVisible = false
          this.returnableList = null
          // 通知父组件刷新数据
          this.$emit('RejectDialogChange')
        } else {
          this.$message.error("退回失败!")
        }
      },
      handleCheckAllChange(val) {
        this.checkedCities = val ? cityOptions : []
        this.isIndeterminate = false
      },
      handleCheckedCitiesChange(value) {
        let checkedCount = value.length
        this.checkAll = checkedCount === this.cities.length
        this.isIndeterminate =
          checkedCount > 0 && checkedCount < this.cities.length
      },
      handleClose(done) {
        this.$confirm("确认关闭？")
          .then(_ => {
            done()
          })
          .catch(_ => {})
          this.returnableList = null
      }
    }
  }
</script>

<style lang="scss" scoped></style>
