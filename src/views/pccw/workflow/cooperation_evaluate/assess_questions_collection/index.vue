<template>
  <div v-loading="pageLoading">
    <flow-detail
      ref="flow"
      :flow="flow"
      :showTransact="false"
      :showReturn="showReturn"
      :showApproval="showApproval"
      :approvalName="approvalName"
      :type="approveType"
      @submit-approval="handleApprovalSubmission"
    >
      <template v-slot:custom-buttons v-if="approveType === 'todo'">
        <el-button
          type="primary"
          v-if="workType === 'canReject'"
          @click="rejectFlow()"
          >驳回</el-button
        >
        <template v-if="node == 1">
          <el-button type="primary" @click="save()">保存</el-button>
          <el-button type="primary" @click="sends()">一键派发</el-button>
        </template>
        <template v-if="node == 7">
          <el-button type="primary" @click="problemPublicity()"
            >问题公示</el-button
          >
        </template>
      </template>
      <div slot="content">
<!--                   <mssCard title="测试使用">
          <div
            slot="content"
            style="
              line-height: 1.8; /* 增大行高 */
              margin-bottom: 10px; /* 每行底部增加额外空间 */
            "
          >
            workType:{{ workType }} <br />
            name:{{ flow.name }} <br />
            流程名称flowKey：{{ flow.flowKey }}<br />
            节点名称nodeName：{{ flow.nodeName }}<br />
            流程名称workflowName：{{ flow.workflowName }}<br />
            taskId：{{ flow.taskId }}<br />
            node：{{ node }}<br />
            businessId:{{ flow.businessId }}<br />
          </div>
        </mssCard> -->
        <!-- 收集-基础信息 -->
        <BaseInfo
          :approveType="approveType"
          :flow="flow"
          :node="node"
          ref="BaseInfo"
        />
        <!-- 考核-结果列表 -->
        <mssCard title="考核结果列表">
          <div slot="content">
            <mssTableCustom
              @tableDataChange="handleTableDataChange"
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              :static-search-param="staticSearchParam"
              :selectable="checkSelectable"
              :rowClassName="tableRowClassName"
              border
              selection="selection"
              :getChange="true"
              @getChange="handleGetChange"
            >
            </mssTableCustom>
          </div>
        </mssCard>
        <!-- <div > -->
        <!-- 区县反馈人列表 -->
        <DistrictFeedback
          :static-search-param="staticSearchParam"
          v-if="node === 1"
        />
        <!-- </div> -->
        <!-- <ExternalAssessmentItems v-if="node!=1&&node!=2"/> -->
        <ExternalAssessmentItems
          v-if="showAssess && scoreMonth"
          :scoreMonth="scoreMonth"
        />
        <!-- 考核指标 -->
<!--        <AssessmentIndicators
          v-if="showAssess && constructUnit && scoreMonth"
          :scoreMonth="scoreMonth + '-01'"
          :constructUnit="constructUnit"
        /> -->
        <RejectDialog
          @RejectDialogChange="handleRejectDialogChange"
          ref="rejectDialog"
          :rejectList="rejectList"
          :node="node"
        />
      </div>
    </flow-detail>
  </div>
</template>

<script>
  import RejectDialog from "./RejectDialog.vue"
  import DistrictFeedback from "./district_feedback.vue"
  import BaseInfo from "./base_info.vue"
  import ExternalAssessmentItems from "./external_assessment_items.vue"
  import AssessmentIndicators from "./assessment_indicators.vue"
  import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue"
  // api
  import {
    getList,
    getFeedbackByBid,
    editBaseInfo,
    startDistrictFeedbackFlow,
    startReject,
    judgeReject,
    startProblemPublicity
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"
  export default {
    components: {
      FlowDetail,
      ExternalAssessmentItems,
      AssessmentIndicators,
      DistrictFeedback,
      BaseInfo,
      RejectDialog
    },
    data() {
      return {
              pageLoading: false,
        constructUnit: null,
        state: "assess_chek",
        // collectionBaseFlag: false,
        showAssess: false, // 展示考核列表、考核指标
        workType: "", // 1.收集，2.可驳回收集 canReject 3.驳回 reject
        scoreMonth: "",
        showReturn: false,
        approvalName: "提交",
        selection: false,
        node: null, // 节点
        templateInfo: false,
        flow: {
          name: "", // 标题
          flowKey: "", // 流程key
          composeTime: "", // 时间
          workflowName: "", // 流程名称
          nodeName: "", // 节点名称
          procInstId: "", // 实例名称
          taskId: "" // 任务id
        },
        approveType: "",
        showTransact: false,
        showApproval: true,
        businessId: "",
        // 表单
        // 表格数据API
        tableApi: getList,
        // tableApi: getList,
        // 默认表格搜索条件
        staticSearchParam: {
          businessId: "",
          taskId: ""
        },
        cuzPaginationOpt: {
          size: 3
        },
        // 表头
        tableHeader: [
          // {
          //   prop: "id",
          //   label: "id，之后删除",
          //   align: "center"
          // },
          {
            prop: "scoreMonth",
            label: "评分月份",
            align: "center"
          },
          {
            prop: "unitType",
            label: "合作单位类型",
            align: "center"
          },
          {
            prop: "unitName",
            label: "合作单位名称",
            align: "center"
          },
          {
            prop: "frameContCode",
            label: "框架合同编码",
            align: "center"
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center"
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center"
          },
          {
            prop: "constructUnit",
            label: "建设单位",
            align: "center"
          },
          {
            prop: "city",
            label: "地市",
            align: "center"
          },
          {
            prop: "district",
            label: "区县",
            align: "center"
          },
          {
            prop: "projectImplementationManagerPrimary",
            label: "工程实施经理-主",
            align: "center"
          },
          {
            prop: "unitAdminName",
            label: "合作单位管理员",
            align: "center"
          },
          {
            prop: "surveyDesignDeduction",
            label: "勘察设计阶段扣分值",
            align: "center"
          },
          {
            prop: "surveyDesignDescription",
            label: "勘察设计阶段考核事项说明",
            minWidth: "120px",
            align: "center",
            useHtml: true
          },
          {
            prop: "implementationAcceptDeduction",
            label: "实施验收阶段扣分值",
            align: "center"
          },
          {
            prop: "implementationAcceptDescription",
            label: "实施验收阶段考核事项说明",
            minWidth: "120px",
            align: "center",
            useHtml: true
          },
          {
            prop: "projectPreparationDeduction",
            label: "工程准备阶段扣分值",
            align: "center"
          },
          {
            prop: "projectPreparationDescription",
            label: "工程准备阶段考核事项说明",
            minWidth: "120px",
            align: "center",
            useHtml: true
          },
          {
            prop: "projectImplementationDeduction",
            label: "工程实施阶段扣分值",
            align: "center"
          },
          {
            prop: "projectImplementationDescription",
            label: "工程实施阶段考核事项说明",
            minWidth: "120px",
            align: "center",
            useHtml: true
          },
          {
            prop: "projectAcceptanceDeduction",
            label: "工程验收阶段扣分值",
            align: "center"
          },
          {
            prop: "projectAcceptanceDescription",
            label: "工程验收阶段考核事项说明",
            minWidth: "120px",
            align: "center",
            useHtml: true
          },
          {
            fixed: "right",
            prop: "deductedUserName",
            label: "反馈人",
            align: "center",
            useHtml: true
          },
          {
            label: "操作",
            align: "center",
            fixed: "right",
            minWidth: "100px",
            formatter: (row, column, cell, event) => {
              const buttonClassName =
                this.approveType === "todo" &&
                row.style &&
                row.style == "gray" &&
                this.workType != "reject"
                  ? "table_btn mr10 gray-style"
                  : "table_btn mr10"
              return (
                <span>
                  <span
                    class={buttonClassName}
                    onClick={() => {
                      this.details(row)
                    }}
                  >
                    {this.buttonName}
                  </span>
                </span>
              )
            }
          },
          {
            prop: "",
            label: "状态",
            fixed: "right",
            align: "center",
            formatter: row => {
              const displayText = row.totalValueHistory ? "已填写" : "未填写"
              return <span>{displayText}</span>
            }
          }
        ],
        buttonName: "查看",
        rejectList: {
          node: "",
          businessId: "",
          flowList: null
        }
      }
    },
    mounted() {},
    created() {
      this.approveType = decodeURIComponent(this.$route.query.type) // 获取类型信息
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow) // 获取流程对象
      this.businessId = this.flow.businessId // 根据这个去后端查数据

      // 设置其他businessId
      switch (this.flow.flowKey) {
        case "assess_admin_sends_flow":
          this.node = 1
          this.showApproval = false
          // this.approvalName="一键派发"
          this.staticSearchParam.businessId = this.businessId
          break
        case "assess_district_flow":
        case "assess_district_flow_reject": // 驳回流程
          this.node = 2
          if (this.flow.nodeName === "区县工程事务员填报"
          ||this.flow.nodeName === "合作单位管理员审核并填报"
          ) {
            this.buttonName = "填报"
            this.state = "assess_fill"
          } else {
            this.buttonName = "查看"
            this.showReturn = true
          }
          // this.$refs.flow.showApproval = true
          this.showApproval = true
          this.staticSearchParam.districtBusinessId = this.businessId
          break
        case "assess_admin_summary_flow":
          this.workType = "canReject"
          this.buttonName = "填报"
          this.state = "assess_fill"
          this.node = 3
          this.koufen = false
          this.selection = true
          this.staticSearchParam.businessId = this.businessId
          break
        case "assess_impman_flow":
          // 这个节点还存在拒绝的情况
          this.workType = "canReject"
          this.buttonName = "填报"
          this.state = "assess_fill"
          this.node = 4
          this.selection = true
          this.staticSearchParam.impmanBusinessId = this.businessId
          break
        case "assess_affirm_flow": // 驳回至实施经理
          this.workType = "canReject"
          this.node = 5
          this.selection = true
          this.staticSearchParam.businessId = this.businessId
          break
        case "assess_three_level_flow":
          this.node = 6
          this.staticSearchParam.threeLevelBusinessId = this.businessId
          this.selection = true
          if (this.flow.nodeName === "三级经理审核") {
            this.showReturn = true
          }
          break
        case "assess_disclosure_flow":
          this.node = 7
          this.staticSearchParam.businessId = this.businessId
          this.showApproval = false
          // this.approvalName = "问题公示"
          this.selection = true
          break
        case "interface_business_id":
          this.node = 8
          this.staticSearchParam.businessId = this.businessId
          break
        default:
          break
      }

      if (this.node > 2 && this.node < 7) {
        this.showAssess = true
      }

      this.staticSearchParam.taskId = this.flow.taskId
      if (this.flow.name.includes("驳回")) {
        this.workType = "reject"
      }

      this.rejectList.node = this.node
      this.rejectList.businessId = this.businessId
      if (this.approveType != "todo") {
        this.buttonName = "查看"
      }
    },
    methods: {
      // 处理子组件发送的数据
      handleTableDataChange(tableData, page, newData) {
        if (tableData && tableData.length > 0) {
          this.constructUnit = tableData[0].constructUnit
          this.scoreMonth = tableData[0].scoreMonth
        }
      },
      // 问题公示/ 管理员发送流程 - > 接口人
      async problemPublicity() {
        let res = await startProblemPublicity(this.flow)
        console.log("发起结果", res.data)

        if (res.code === "0000") {
          this.$message.success("派发成功")
          // 跳转页面并刷新
          this.$router
            .push({
              path: "/home"
            })
            .then(() => {
              this.$router.go(0) // 刷新当前页面
            })
        }
      },


      // 处理提交前事件的方法
      async handleApprovalSubmission() {
        this.$refs.flow.allowApproval = false
        this.$refs.flow.$refs.approvalDialog.approvalDialog = false

        // 无需判断的节点
        if(this.node===2){
          this.$refs.flow.allowApproval = true
          this.$refs.flow.$refs.approvalDialog.approvalDialog = true
        }else{
          const res = await judgeReject(this.rejectList)
          // 判断是否可提交
          console.log(res)
          if (res.data && res.data === true) {
            this.$refs.flow.allowApproval = true
            this.$refs.flow.$refs.approvalDialog.approvalDialog = true
          } else {
            this.$message.error("还有下发的驳回工单未处理，暂时无法提交!")
          }
        }

      },
      // 判断是否可以退回
      async judgeIsApproval() {
        const res = await judgeReject(this.rejectList === "" || date === null)
        console.log(res)
      },
      // 处理驳回子组件事件 - 刷新数据
      handleRejectDialogChange() {
        this.$refs.table.getTableData()
        this.$router.go(0) // 刷新当前页面
      },
      // this.stationaryData 为需要置灰的数据列表
      checkSelectable(row) {
        return true
        const flag =
          this.approveType === "todo" &&
          row.style &&
          row.style == "gray" &&
          this.workType != "reject"
        if (flag) {
          return false
        } else {
          return true
        }
      },
      tableRowClassName({ row, rowIndex }) {
        const flag =
          this.approveType === "todo" &&
          row.style &&
          row.style == "gray" &&
          this.workType != "reject"
        if (flag) {
          return "disabledRow"
        } else {
          return ""
        }
      },
      // 处理选择的数组
      handleGetChange(selection) {
        // 获取到子组件通过 $emit 传递的数据
        console.log("selection", selection)
        this.rejectList = { ...this.rejectList, flowList: selection };
        this.$refs.rejectDialog.returnableList = this.rejectList
      },
      // 驳回
      async rejectFlow() {
        if (
          this.rejectList.flowList === null ||
          this.rejectList.flowList.length === 0
        ) {
          this.$message.error("还未选择驳回数据")
          return
        }

        // 驳回至实施经理
        if ((this.node === 5)) {
          // 驳回至区县
        } else {
          console.log("驳回至区县123", this.rejectList)
          // 单条的写法，多条再进行修改
          if (this.rejectList.flowList.length > 1) {
            this.$message.error("请选择单条数据")
            return
          }
          if (!this.rejectList.flowList[0].deductedUserList) {
            this.$message.error("该条数据没人扣分，无需驳回！")
            return
          }
        }

        console.log("父组件数据", this.rejectList)
        this.$refs.rejectDialog.dialogVisible = true
        this.$refs.rejectDialog.returnableList = this.rejectList

        // const res = await startReject(this.rejectList)
        // console.log("返回", res)
        // if (res.code === "0000") {
        //   this.$message.success("退回成功!")
        //   this.$refs.table.getTableData()
        //   // 跳转页面并刷新
        //   // this.$router.push({
        //   //   path: '/home'
        //   // }).then(() => {
        //   //   this.$router.go(0); // 刷新当前页面
        //   // });
        // } else {
        //   this.$message.error("退回失败!")
        // }
        // 刷新页面
      },
      async save() {
        let baseInfo = this.$refs.BaseInfo.baseInfo
        let date = this.$refs.BaseInfo.requiredFeedbackDate
        if (date === "" || date === null) {
          this.$message.error("还未选择反馈时间")
          return
        }
        let q = {
          id: baseInfo.id,
          requiredFeedbackDate: date
        }
        let res = await editBaseInfo(q)
        if (res.code === "0000") {
          this.$message.success("保存成功")
        }
      },
      // 管理员一键派发
      async sends() {
        let date = this.$refs.BaseInfo.requiredFeedbackDate
        console.log("date", date)
        if (date === undefined || date === null || date === "") {
          this.$message.error("还未保存反馈时间")
          return
        }
        this.pageLoading = true
        let res = await startDistrictFeedbackFlow(this.flow)
        if (res.code === "0000") {
          this.$message.success("派发成功")
           this.pageLoading = false
          // 跳转页面并刷新
          this.$router
            .push({
              path: "/home"
            })
            .then(() => {
              this.$router.go(0) // 刷新当前页面
            })
        }
        this.pageLoading = false
      },
      details(row) {
        const data = encodeURIComponent(JSON.stringify(row)) // 将对象转换为字符串并进行编码
        if (this.approveType != "todo") {
          this.state = "assess_chek"
        }
        // 扣分节点(1:区县；2:合作单位管理员；3:实施经理)
        let deductionNode = 0
        switch (this.node) {
          case 2:
            deductionNode = 1
            break
          case 3:
            deductionNode = 2
            break
          case 4:
            deductionNode = 3
            break
          default:
            break
        }

        this.$router.push({
          path: "/pccw_menu/workflow/cooperation_evaluate/assess_questions_collection/stage_assessment",
          query: {
            data: data,
            operateState: this.state,
            deductionNode: deductionNode,
            businessId: this.businessId
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .gray-style {
    color: #ccc; /* 或者使用其他方式来置灰，如背景色等 */
  }

  .content-text {
    margin: 2px 20px;
    display: flex;
    justify-content: space-between;
    /* 控制内容之间的间距 */
    align-items: center;
    /* 控制内容垂直方向的对齐方式 */
  }

  ::v-deep .disabledRow {
    cursor: not-allowed;
    pointer-events: none;
    color: #ccc; // 改当前行的字体颜色

    .el-checkbox__inner {
      // color: blue;
    }
  }
</style>
