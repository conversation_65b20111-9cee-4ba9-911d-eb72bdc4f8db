<template>
    <div>
      <el-dialog
        title="申诉"
        :visible.sync="pendingShow"
        :close-on-click-modal='false'
        @close='getpending()'
        width="85%">
        <mssCard title="申诉明细">
        <div slot="content">
        <el-table
          :data="tableList"
          class="table"
          row-key="id"
          :header-cell-style="rowClass"
          default-expand-all
          border
          style="width: 100%"
        >
          <!-- <el-table-column
            prop="assessmentProject"
            label="考核项目"
            width="80"
            align="center"
          ></el-table-column>
          <el-table-column label="考核内容" align="center">
            <el-table-column label="考核内容" align="center" width="120px">
              <template scope="scope">
                <div>{{ scope.row.assessmentContext }}</div>
                <span>({{ scope.row.assessmentContextScore }}分)</span>
              </template>
            </el-table-column>
            <el-table-column
              label="考核内容类型"
              prop="assessmentContextDetails"
              align="center"
              width="120px"></el-table-column>
          </el-table-column>
          <el-table-column
            prop="standard"
            align="center"
            label="标准及要求"
          ></el-table-column>
          <el-table-column prop="demerit" label="扣分规则"></el-table-column>
          <el-table-column label="扣分（分/次）" align="center" width="100px">
            <template scope="scope">
              <div >{{ scope.row.deductPointsScore }}分/{{ scope.row.deductPointsNumber}}次</div>
            </template>
          </el-table-column>
          <el-table-column
            label="分值"
            prop="standardScore"
            align="center"
            width="80px">
          </el-table-column>
          <el-table-column
            label="历史扣分事项说明"
            prop="deductionReasonHistory"
            align="center"
            width="80px"></el-table-column> -->
            <el-table-column label="扣分事项说明" align="center" prop="deductionReason">
            </el-table-column>
<!--            <el-table-column label="扣分次数" align="center" prop="deductionNum">
            </el-table-column>
            <el-table-column label="扣分值" align="center" prop="deductionValue">
            </el-table-column> -->
            <el-table-column label="申诉原因" align="center" width="100px" prop="appealReason">
              <template scope="scope">
                <span v-html="scope.row.appealReason"> </span>
              </template>
            </el-table-column>
            <el-table-column label="申诉附件" align="center" width="150px" prop="appealFileNames">
              <template scope="scope">
                <span v-html="scope.row.appealFileNames"> </span>
              </template>
            </el-table-column>
<el-table-column label="操作" align="center" width="100px">

  <template slot-scope="scope">


    <el-button
      @click="getAppeal(scope.row)"
      type="text"
      size="small"
    >
  <span v-if="scope.row.appealReason && scope.row.appealFileNames">
    修改
  </span>
      <span v-else>新增</span>
    </el-button>

    <el-button
      @click="deleteAppeal(scope.row)"
      type="text"
      size="small"
      style="color: red;"
    >
       <span v-if="scope.row.appealReason">
    删除
         </span>
    </el-button>




  </template>
</el-table-column>
        </el-table>
        </div>
        </mssCard>
      </el-dialog>
      <Information ref="information" @onClose='onClose'></Information>
    </div>
</template>
<script>
import { getAllList } from "@/api/pccw/cooperation_evaluate/external";
import {deleteAppeal, getDeductionDetail, saveDetail} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig"
import Information from "./information.vue"
export default {
name: 'appeal_list',
components: {Information},
data() {
  return {
    loadParam: {},
    tableApi: getAllList,
    pendingShow: false,
    tableData: [],
    tableList: [],
    assessmentType: '',
    whole: {},
    fill: {},
    flow: {}
  };
},
methods: {
  getpending() {
    this.pendingShow = false
    this.$emit('getappealShow')
  },
  onClose () {
    this.selectTableList()
  },
  getAppeal (item) {
    console.log("item1",item)
    console.log("whole",this.whole)
    let data = {
      formId:item.id,
      id:item.evaluateDetailId,
      deductionReason: item.deductionReason,
      deductionNum: item.deductionNum,
      deductionValue: item.deductionValue,
      lineId: this.whole.problemCollectionFlowId,
      evaluationContent: this.whole.assessmentContext,
      assessmentProject: this.whole.assessmentProject,
      serialNumber: this.whole.serialNumber,
      standardsAndRequirements: this.whole.standard,
      deductionStatement: this.whole.demerit,
      score: this.whole.rowNumber,
      deductionId: this.whole.id,
      deductionDetailId: item.id,
      deductionItem: item.deductionReason,
      times: item.deductionNum,
      deductScore: item.deductionValue,
      deductedUserName: item.deductionName,
      deductedUserId: item.deductionBy,
      deductedUserPermission: item.deductionNode,
      mainBusinessId: this.flow.businessId,
      standardScore: this.whole.standardScore
    }
    console.log("data",data)
    this.$refs.information.open(data)
  },
  deleteAppeal(row) {
    // 处理删除逻辑，弹出确认框并执行删除操作
    this.$confirm('确定删除该项吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      let data = {
        id:row.id,
        deductionDetailId:row.evaluateDetailId
      }
      deleteAppeal(data).then((res) => {
        if (res.code === '0000') {
          this.$message.success(res.data)
          this.selectTableList()
        }
      })
    }).catch(() => {
      console.log('删除已取消');
    });
  },
  async selectTableList() {
    console.log("看看",this.whole)
    let params = {
      formId: this.whole.deductionId
    }
    try {
      const res = await getDeductionDetail(params)
      if (res.code === "0000") {
        this.tableList = res.data
      }
    } catch (error) {
      console.error("Error fetching data:", error)
      // 处理异常情况
    }
  },
  //合并表头
  rowClass({ row, column, rowIndex, columnIndex }) {
    if (rowIndex === 1) {
      return {
        display: "none"
      }
    }
  },
  open (assessmentType, row, problemCollectionFlowId) {
    let flow = decodeURIComponent(this.$route.query.data)
    this.flow = JSON.parse(flow) // 获取流程对象
    this.whole = row
    this.whole.problemCollectionFlowId = problemCollectionFlowId
    this.assessmentType = assessmentType
    this.selectTableList()
    this.pendingShow = true
  }
},
mounted() {},
};
</script>

<style scoped>
  .gray-button {
    color: #ccc; /* 灰色文字 */
    cursor: not-allowed; /* 禁止鼠标悬停时的指针样式 */
    /* 可以根据需要添加更多样式，如背景色等 */
  }
.upload-btn {
  display: inline-block;
  margin: 0 10px;
}
/* Your component's CSS styles go here */
</style>
