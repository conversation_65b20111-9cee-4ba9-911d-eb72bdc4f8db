<template>
  <div>
    <el-dialog
      title="请填写申诉信息"
      :visible.sync="pendingShow"
      :close-on-click-modal='false'
      @close='pendingShow = false'
      width="85%">
      <el-form ref="searchData" :model="searchData" label-width="80px">
        <el-card class="card">
          <el-descriptions title="" :column="2" border>
            <el-descriptions-item label="扣分事项说明">
              <span>{{ searchData.deductionReason }}</span>
            </el-descriptions-item>
<!--            <el-descriptions-item label="扣分次数">
              <span>{{ searchData.deductionNum }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="扣分值" >
              {{ searchData.deductionValue }}
            </el-descriptions-item> -->
            <el-descriptions-item label="申诉说明">
              <el-input
                resize='none'
                type="textarea"
                :rows="3"
                placeholder="请输入内容"
                v-model="searchData.appealReason">
              </el-input>
            </el-descriptions-item>
            <el-descriptions-item label="附件上传">
              <el-upload
              ref="newFile"
              class="upload-btn"
              action="string"
              :show-file-list="false"
              :auto-upload="true"
              :http-request="importFile">
              <el-button type="primary">附件上传</el-button>
            </el-upload>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-form>

          <mssCard title="附件信息">
      <div slot="content">
          <mssTable
            ref="table"
            :pagination="false"
            :columns="fileInfo"
            :stationary="fileList"
          />
        </div>
      </mssCard>
        <span slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="submitOrder">确 定</el-button>
          <el-button size="mini" style="margin-left: 10px" @click="closeDialog">取 消</el-button>
        </span>
    </el-dialog>
  </div>
</template>
<script>
import { uploadFile,
selectFileList,
         saveDetail,
         deleteFile,
          downloadFiles} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig"
export default {
  data() {
   return {
     // 文件信息
     fileInfo: [
       {
         prop: "fileType",
         label: "附件类型",
         align: "center",
         tooltip: true
       },
       {
         prop: "fileName",
         label: "文件名",
         align: "center",
         tooltip: true
       },
       {
         prop: "createDate",
         label: "上传时间",
         align: "center",
         tooltip: true
       },
       {
         label: "操作",
         align: "center",
         fixed: "right",
         minWidth: "60px",
         formatter: row => {
           const flag = true
           return (
             <span>
               <span
                 class="table_btn mr10"
                 onClick={() => {
                   this.downloadRow(row)
                 }}
               >
                 下载
               </span>
               {flag && (
                 <span
                   class="table_btn mr10"
                   onClick={() => {
                     this.delRow(row)
                   }}
                 >
                   删除
                 </span>
               )}
             </span>
           )
         }
       }
       ],
    pendingShow: false,
    searchData: {},
    textarea: '',
    file: true,
    fileList:[]
   }
  },
  created(){

  },
  methods: {
    // 删除文件行
    async delRow(row) {
      const res = await deleteFile(row)
      if (res.code == "0000") {
        this.getFile() // 刷新附件信息
      }
    },
    // 下载文件
    async downloadRow(row) {
        let res = await downloadFiles(row)
        console.log(res, "文件")
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", row.fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
    },
    async getFile(){
      let res= await selectFileList(this.searchData)
      console.log("附件",res)
      this.fileList=res.data.data
    },
    importFile (params) {
      const param = new FormData()
      param.append('file', params.file)
      param.append('fileType', '合作单位申诉附件')
      param.append('formId', this.searchData.deductionDetailId)
      uploadFile(param).then((res) => {
        if (res.code === '5000') {
          this.$message.warning(res.msg)
        } else if (res.code === '0000') {
          this.file = false
          this.$message.success('附件上传成功')
          this.getFile() // 刷新附件信息
          this.$emit('onClose')
        }
      })
    },
    submitOrder() {
      console.log('this.searchData',this.searchData)
      if (this.searchData.appealReason === '' || this.searchData.appealReason === undefined || this.searchData.appealReason === null) {
        this.$message.warning('请填写申诉说明')
        return
      }
      // if (!this.fileList||this.fileList.length<1) {
      //   this.$message.warning('请上传附件')
      //   return
      // }
      let data = this.searchData
      saveDetail(data).then((res) => {
        if (res.code === '0000') {
          this.pendingShow = false
          this.$emit('onClose')
        } else {
          this.$message.warning(res.msg)
        }

      })
    },
    closeDialog() {
     this.pendingShow = false
    },
    open(item) {
      console.log('item2222',item)
      this.searchData = item
      this.pendingShow = true
      this.getFile()
    },
  }
}
</script>
<style>
.card {
  margin-bottom: 20px;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  border-radius: 4px;
}
.title {
  text-align: center;
}
.heading {
  margin-top: 0;
  font-size: 24px;
  font-weight: bold;
}
.el-descriptions-item__content{
  width: 43% !important;
}
</style>

