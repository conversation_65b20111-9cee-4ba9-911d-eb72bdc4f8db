<!-- <template>
  <div>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          selection
          :staticSearchParam="loadParam">
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>

import { listSafetyPaymentQuery} from '@/api/pccw/implementation_link/safety_payment_query/query_list';
export default {
name: 'stay_read',
data() {
  return{
    // 表格数据API
    tableApi: listSafetyPaymentQuery,
    //搜索字段配置
    tableHeader: [
        {
            prop: "assessmentProject",
            label: "考核项目",
            align: "center",
            tooltip: true,
            minWidth: 130
        },
        {
            prop: "creatorCompany",
            label: "合作单位类型",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "constructionUnit",
            label: "合作单位名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectNumber",
            label: "框架合同编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectName",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "taskName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "partnerName",
            label: "建设单位",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "entryMonth",
            label: "县区",
            align: "center",
            tooltip: true,
            minWidth: 130
        },
        {
            prop: "problemDesc",
            label: "工程实施经理-主",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "合作单位管理员",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "勘察设计阶段扣分值",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "勘察设计阶段考核事项说明",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "实施验收阶段扣分值",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "实施验收阶段考核事项说明",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "工程准备阶段扣分值",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "工程准备阶段考核事项说明",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "工程实施阶段扣分值",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "工程实施阶段考核事项说明",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "工程验收阶段扣分值",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "工程验收阶段考核事项说明",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "problemDesc",
            label: "反馈人",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "操作",
            align: "center",
            tooltip: true,
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
            return (
              <span>
                <span class="mr10">
                  <el-button
                    onClick= {() => {this.checkFile(row)}}
                    type="text">
                    查看
                  </el-button>
                </span>
              </span>
            );
            },
        }
    ],
    loadParam: {}
  }
},
computed: {},
created() {},
methods: {
  checkFile () {

  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  reset() {
    this.search()
  },
},

}
</script> -->
