/**
* @author: ty
* @date: 2023-07-22
* @description: 消息详情展示页面 -原始
*/
<template>
  <div class="MessageDetails" v-loading="pageLoading">
    <div style="margin-top: 30px;"></div>
    <mssCard title="消息详情">
      <div slot="content">
        <p class="message-title">以下 {{messageTitle}} , 已申述完成。</p>
      </div>
    </mssCard>
    <mssCard title="考核结果列表">
      <div slot="content">
        <mssTableCustom
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :static-search-param="staticSearchParam"
          border
          selection="selection"
        >
        </mssTableCustom>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import {
    getList
  } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"
import {findByMsgIdService} from "@/api/message_manager/message_manager_api";
export default {
  name: 'MessageDetails',
  data(){
    return {
      id: '',
      messageType: '',
      messageTitle: '',
      pageLoading: false,
    // 默认参数
    staticSearchParam:{
      appealBusinessId:''
    },
    // 表头
    tableHeader: [
      // {
      //   prop: "appealDeadline",
      //   label: "申诉截止日期",
      //   align: "center"
      // },
      {
        prop: "scoreMonth",
        label: "评分月份",
        align: "center"
      },
      {
        prop: "unitType",
        label: "合作单位类型",
        align: "center"
      },
      {
        prop: "unitName",
        label: "合作单位名称",
        align: "center"
      },
      {
        prop: "frameContCode",
        label: "框架合同编码",
        align: "center"
      },
      {
        prop: "projectCode",
        label: "项目编码",
        align: "center"
      },
      {
        prop: "projectName",
        label: "项目名称",
        align: "center"
      },
      {
        prop: "constructUnit",
        label: "建设单位",
        align: "center"
      },
      {
        prop: "city",
        label: "地市",
        align: "center"
      },
      {
        prop: "district",
        label: "区县",
        align: "center"
      },
      {
        prop: "projectImplementationManagerPrimary",
        label: "工程实施经理-主",
        align: "center"
      },
      {
        prop: "unitAdminName",
        label: "合作单位管理员",
        align: "center"
      },
      {
        prop: "surveyDesignDeduction",
        label: "勘察设计阶段扣分值",
        align: "center"
      },
         {
            prop: "surveyDesignDescription",
            label: "勘察设计阶段考核事项说明",
            minWidth: "150px",
            align: "center",
            useHtml: true
          },
          {
            prop: "implementationAcceptDescription",
            label: "实施验收阶段考核事项说明",
            minWidth: "150px",
            align: "center",
            useHtml: true
          },
          {
            prop: "projectPreparationDescription",
            label: "工程准备阶段考核事项说明",
            minWidth: "150px",
            align: "center",
            useHtml: true
          },
          {
            prop: "projectImplementationDescription",
            label: "工程实施阶段考核事项说明",
            minWidth: "150px",
            align: "center",
            useHtml: true
          },
          {
            prop: "projectAcceptanceDescription",
            label: "工程验收阶段考核事项说明",
            minWidth: "150px",
            align: "center",
            useHtml: true
          },
      {
        label: "操作",
        align: "center",
        fixed: "right",
        minWidth: "100px",
        formatter: (row, column, cell, event) => {
          return (
            <span>
              <span
              class="table_btn mr10"
                onClick={() => {
                  this.details(row)
                }}
              >
                查看
              </span>
            </span>
          )
        }
      }
      // {
      //   prop: "afterTotalDeductionReasons",
      //   label: "申诉后考核事项说明",
      //   align: "center",
      //   fixed: "right",
      //   minWidth: "150px",
      //   useHtml: true
      // },
    ],
    // 表格数据API
    tableApi: getList,
    }
  },
  created(){
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.messageId
    this.messageType = urlQuery.type
    this.getData()

    // 获取列表数据
    this.staticSearchParam.appealBusinessId = urlQuery.appealBusinessId
  },
  methods: {
    details(row) {
      const data = encodeURIComponent(JSON.stringify(row))
      let state = "appeal_msg"
      this.$router.push({
        path: "/pccw_menu/workflow/cooperation_evaluate/assess_questions_collection/stage_assessment",
        query: {
          data: data,
          operateState: state
        }
      })
    },
    getData(){
      this.pageLoading = true
      findByMsgIdService(this.id)
      .then(res => {
        this.messageTitle = res?.data?.title || ''
      })
      .finally(_ => {
        this.pageLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .MessageDetails{
    .message-title{

    }
  }
</style>
