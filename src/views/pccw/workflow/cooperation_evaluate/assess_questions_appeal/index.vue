<template>
  <div>
    <flow-detail
      ref="flow"
      :flow="flow"
      :showTransact="false"
      :showReturn="showReturn"
      :showApproval="showApproval"
      :approvalName="approvalName"
      :type="approveType"
      @submit-approval="handleApprovalSubmission"
    >
      <template v-slot:custom-buttons v-if="approveType === 'todo'">
        <el-button type="primary" v-if="adminReject" @click="startAdmin(0)"
          >拒绝</el-button
        >
        <el-button type="primary" v-if="adminSubmit" @click="startAdmin(1)">
          <!-- {{ node === 10 ? '结束' : '提交' }} -->
          提交
        </el-button>
        <el-button type="primary" v-if="node === 10" @click="startAppeal()"
          >申诉</el-button
        >
      </template>

      <div slot="content">
        <mssCard title="基础信息">
          <div
            slot="content"
            style="
              line-height: 1.8; /* 增大行高 */
              margin-bottom: 10px; /* 每行底部增加额外空间 */
            "
          >
            <div>当前节点：{{ flow.nodeName }} </div>
            <div v-if="nextNode"> 下一节点：{{ nextNode }} </div>
            <div v-if="nextNodePeople">
              下一节点审批人2：{{ nextNodePeople }}
            </div>

<!--                      workType:{{ workType }} <br />
            name:{{ flow.name }} <br />
            流程名称flowKey：{{ flow.flowKey }}<br />
            节点名称nodeName：{{ flow.nodeName }}<br />
            流程名称workflowName：{{ flow.workflowName }}<br />
            taskId：{{ flow.taskId }}<br />
            node：{{ node }}<br />
            businessId:{{ flow.businessId }}<br /> -->
          </div>
        </mssCard>
        <!-- 申诉-搜索 -->
        <mssSearchForm
          ref="searchForm"
          :form="staticSearchParam"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
          v-if="node === 10"
        ></mssSearchForm>
        <!-- 考核-结果列表 -->
        <mssCard title="考核结果列表">
          <div slot="content">
            <mssTableCustom
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              :static-search-param="staticSearchParam"
              :selectable="checkSelectable"
              :rowClassName="tableRowClassName"
              border
              selection="selection"
              :getChange="true"
              @getChange="handleGetChange"
            >
            </mssTableCustom>
          </div>
        </mssCard>
      </div>
    </flow-detail>
  </div>
</template>

<script>
    import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue"
    // api
    import {
      getNextUsersName,
      getList,
      startAppealByInter
    } from "@/api/pccw/cooperation_evaluate/assess_questions_collection/admin_sends_flow.js"

    export default {
      components: {
        FlowDetail
      },
      computed: {
        nextNode() {
          let result = ""
          if(this.flow.nodeName==='合作单位管理员受理'){
                    switch (this.flow.flowKey) {
                      case "appeal_district":
                        result = "区县工程事务员填报"
                        break
                      case "appeal_impman":
                        result = "工程实施经理主审核"
                        break
                    }
                    return result
          }else{
            const nodeNameToNodeMap = {
              "合作单位管理员受理":"工程实施经理主申诉处理",
              "合作单位管理员申诉处理":"工程实施经理主审核",
              "合作单位管理员汇总审核":"三级经理审核",
              "工程实施经理主审核": "三级经理审核",
              "三级经理审核": "合作单位管理员确认",
              "退回考核接口人": "合作单位管理员申诉处理",
              "区县工程事务员申诉处理": "区县建维主管审核",
              "区县建维主管审核": "区县分管领导审核",
              "区县分管领导审核": "合作单位管理员汇总审核",
              "合作单位管理员汇总审核": "工程实施经理主审核",
            };
            // 使用this.flow.nodeName作为键来从映射中获取对应的值
            result = nodeNameToNodeMap[this.flow.nodeName] || ""
          }
          return result;
        }
      },
      data() {
        return {
          nextNodePeople: null,
          showApproval:false,
          variables:{},
          adminReject:false,
          adminSubmit:false,
          returnType: 1, // 1为默认退回1节点，2为指定退回节点列表
          appealDeadline1: "", // 申诉截止日期
          //搜索字段配置
          searchConfig: [
            {
              label: "合作单位类型",
              fieldName: "unitType",
              type: "input"
            },
            {
              label: "合作单位名称",
              fieldName: "unitName",
              type: "input"
            },
            {
              label: "框架合同编码",
              fieldName: "frameContCode",
              type: "input"
            },
            {
              label: "项目编码",
              fieldName: "projectCode",
              type: "input"
            },
            {
              label: "项目名称",
              fieldName: "projectName",
              type: "input"
            },
            {
              label: "合作单位名称",
              fieldName: "unitName",
              type: "input"
            },
            {
              label: "建设单位",
              fieldName: "constructUnit",
              type: "input"
            }
          ],
          collectionBaseFlag: false,
          showAssess: false, // 展示考核列表、考核指标
          workType: "", // 1.收集，2.可驳回收集 canReject 3.驳回 reject
          scoreMonth: "",
          showReturn: false,
          approvalName: "提交",
          selection: false,
          node: null, // 节点
          templateInfo: false,
          flow: {
            name: "", // 标题
            flowKey: "", // 流程key
            composeTime: "", // 时间
            workflowName: "", // 流程名称
            nodeName: "", // 节点名称
            procInstId: "", // 实例名称
            taskId: "" // 任务id
          },
          approveType: "",
          showTransact: false,
          showApproval: true,
          businessId: "",
          // 表单
          // 表格数据API
          tableApi: getList,
          // tableApi: getList,
          // 默认表格搜索条件
          staticSearchParam: {
            businessId: "",
            taskId: "",
            interfaceBusinessId: ""
          },
          cuzPaginationOpt: {
            size: 3
          },
          // 表头
          tableHeader2:[],
          tableHeader: [
            {
              prop: "appealDeadline1",
              label: "申诉截止日期",
              align: "center",
              formatter: () => {
                return <span>{this.appealDeadline1}</span>
              }
            },
            // {
            //   prop: "id",
            //   label: "id,之后删除",
            //   align: "center"
            // },
            {
              prop: "scoreMonth",
              label: "评分月份",
              align: "center"
            },
            {
              prop: "unitType",
              label: "合作单位类型",
              align: "center"
            },
            {
              prop: "unitName",
              label: "合作单位名称",
              align: "center"
            },
            {
              prop: "frameContCode",
              label: "框架合同编码",
              align: "center"
            },
            {
              prop: "projectCode",
              label: "项目编码",
              align: "center"
            },
            {
              prop: "projectName",
              label: "项目名称",
              align: "center"
            },
            {
              prop: "constructUnit",
              label: "建设单位",
              align: "center"
            },
            {
              prop: "city",
              label: "地市",
              align: "center"
            },
            {
              prop: "district",
              label: "区县",
              align: "center"
            },
            {
              prop: "projectImplementationManagerPrimary",
              label: "工程实施经理-主",
              align: "center"
            },
            {
              prop: "unitAdminName",
              label: "合作单位管理员",
              align: "center"
            },
            {
              prop: "surveyDesignDeduction",
              label: "勘察设计阶段扣分值",
              align: "center"
            },
            {
              prop: "surveyDesignDescription",
              label: "勘察设计阶段考核事项说明",
              minWidth: "150px",
              align: "center",
              useHtml: true
            },
            {
              prop: "implementationAcceptDeduction",
              label: "实施验收阶段扣分值",
              align: "center"
            },
            {
              prop: "implementationAcceptDescription",
              label: "实施验收阶段考核事项说明",
              minWidth: "150px",
              align: "center",
              useHtml: true
            },
            {
              prop: "projectPreparationDeduction",
              label: "工程准备阶段扣分值",
              align: "center"
            },
            {
              prop: "projectPreparationDescription",
              label: "工程准备阶段考核事项说明",
              minWidth: "150px",
              align: "center",
              useHtml: true
            },
            {
              prop: "projectImplementationDeduction",
              label: "工程实施阶段扣分值",
              align: "center"
            },
            {
              prop: "projectImplementationDescription",
              label: "工程实施阶段考核事项说明",
              minWidth: "150px",
              align: "center",
              useHtml: true
            },
            {
              prop: "projectAcceptanceDeduction",
              label: "工程验收阶段扣分值",
              align: "center"
            },
            {
              prop: "projectAcceptanceDescription",
              label: "工程验收阶段考核事项说明",
              minWidth: "150px",
              align: "center",
              useHtml: true
            },
            {
              label: "操作",
              align: "center",
              fixed: "right",
              minWidth: "100px",
              formatter: (row, column, cell, event) => {
                const buttonClassName =
                  row.style === "gray" && this.node === 10
                    ? "table_btn mr10 gray-style"
                    : "table_btn mr10"
                return (
                  <span>
                    <span
                      class={buttonClassName}
                      onClick={() => {
                        this.details(row)
                      }}
                    >
                      {this.buttonName}
                    </span>
                  </span>
                )
              }
            },
            {
              prop: "",
              label: "流程状态",
              align: "center",
              fixed: "right",
              formatter: row => {
                const displayText = row.appealBusinessId ? "已提交" : "未提交"
                return <span>{displayText}</span>
              }
            },
            {
              prop: "",
              label: "填报状态",
              align: "center",
              fixed: "right",
              formatter: row => {
                const displayText =
                  row.appealExist && row.appealExist == "t" ? "已填报" : "未填报"
                return <span>{displayText}</span>
              }
            }
          ],
          isFill: false,
          buttonName: "查看",
          buttonState: "",
          rejectList: {
            node: "",
            businessId: "",
            flowList: null
          }
        }
      },
      created() {
        this.approveType = decodeURIComponent(this.$route.query.type) // 获取类型信息
        let flow = JSON.parse(decodeURIComponent(this.$route.query.flow))
        this.flow = flow // 获取流程对象
        this.businessId = this.flow.businessId // 根据这个去后端查数据

        // 计算申诉截止日期
        let dateString = flow.beginTime
        let datePart = dateString.split(" ")[0] // "2024-07-23"
        // 将日期部分转换为日期对象
        let startDate = new Date(datePart)
        startDate.setDate(startDate.getDate() + 3) // 增加三天
        this.appealDeadline1 = `${startDate.getFullYear()}-${(
          "0" +
          (startDate.getMonth() + 1)
        ).slice(-2)}-${("0" + startDate.getDate()).slice(-2)}`

        // 打印验证
        // console.log("Formatted Date:", formattedDate);
        console.log("dateString", dateString)
        console.log("this.appealDeadline", this.appealDeadline1)

        // },
        //   mounted() {
        // 设置其他businessId
        switch (this.flow.flowKey) {
          case "appeal_interfaceman_flow":
            this.approvalName='结束'
            this.node = 10
            this.buttonName = "填报"
            this.buttonState = "inter_fill"
            this.staticSearchParam.interfaceBusinessId = this.businessId
            console.log("查询参数", this.staticSearchParam)
            console.log("this.tableHeader", this.tableHeader)

              // 22：操作
             // 要移除的列的索引
           let removeIndices = [14, 16, 18, 20];
           this.tableHeader = this.tableHeader.filter((_, index) => !removeIndices.includes(index));

             break
          case "appeal_district":
          case "appeal_admin":
          case "appeal_impman":
            this.tableHeader = this.tableHeader.slice(1, -2)
            let element = {
              prop: "deductedUserName",
              label: "反馈人",
              align: "center",
              useHtml: true
            }
            this.tableHeader.push(element)
            this.node = 11
            this.showReturn = true
            this.staticSearchParam.appealBusinessId = this.businessId

            let nodeName = this.flow.nodeName

            if (nodeName.includes("申诉处理")) {
              this.buttonName = "填报"
              this.buttonState = "appeal_fill"
              if (nodeName.includes("合作单位管理员申诉处理")){
                this.adminReject=true
                this.adminSubmit=true
                this.showApproval = false
                this.showReturn = false
              }
            } else if (nodeName.includes("合作单位管理员受理")) {
              this.adminReject=true
              this.adminSubmit=true
              this.showApproval = false
              this.showReturn = false
              this.buttonName = "查看"
              this.buttonState = "admin_chek"
            } else if (nodeName.includes("退回考核接口人")) {
              this.showReturn = false
            } else {
              this.buttonName = "查看"
              this.buttonState = "appeal_chek"
            }
            break
          default:
            break
        }
        if (this.approveType != "todo") {
          this.buttonName = "查看"
        }
        if (this.node > 1 && this.node < 8) {
          this.showAssess = true
        }
        if (this.node < 8) {
          this.collectionBaseFlag = true
        }
        this.staticSearchParam.taskId = this.flow.taskId
        if (this.flow.name.includes("驳回")) {
          this.workType = "reject"
        } else {
          this.workType = ""
        }

        let variables= {
          "isReject":'false'
        }
        this.variables=variables
        console.log("this.variables",this.variables)
      },
      mounted() {
        if (
          this.approveType === "todo" &&
          this.node === 11 &&
          this.flow.nodeName != "合作单位管理员受理" &&
          this.flow.nodeName != "合作单位管理员申述处理"
        ) {
          // console.log(this.$refs.flow, "this.$refs.flow");
          // console.log(this.$refs.flow.$refs, "this.$refs.flow.$refs");
          console.log(
            this.$refs.flow.$refs.sendBackDialog,
            "this.$refs.flow.$refs.sendBackDialog"
          )
          // this.$refs.flow.$refs.returnDialog.returnType=2
          this.$refs.flow.$refs.sendBackDialog.returnType = 2
          // this.returnType=2
        }
      },
      methods: {
        startAdmin(type){
          if(type===0){
            this.variables.isReject='true'
            console.log('管理员拒绝')
          }else{
            this.variables.isReject='false'
          }
          this.$bus.$emit('adminRejectToInterfaceMan',this.variables)
          this.$refs.flow.$refs.approvalDialog.approvalDialog = true
        },
        // 处理提交前事件的方法
        async handleApprovalSubmission() {
          // if(!this.adminReject){
          //   console.log('不是管理员提交')
          // this.variables.isReject='false'
          // this.$bus.$emit('adminRejectToInterfaceMan',this.variables)
          // }else{
          //   this.variables.isReject='true'
          //   this.$bus.$emit('adminRejectToInterfaceMan',this.variables)
          // }
  },
        // this.stationaryData 为需要置灰的数据列表
        checkSelectable(row) {
          const flag =
            this.approveType === "todo" &&
            row.style &&
            row.style == "gray" &&
            this.node === 10
          if (flag) {
            return false
          } else {
            return true
          }
        },
        tableRowClassName({ row, rowIndex }) {
          const flag =
            this.approveType === "todo" &&
            row.style &&
            row.style == "gray" &&
            this.node === 10
          if (flag) {
            return "disabledRow"
          } else {
            return ""
          }
        },
        // 处理选择的数组
        handleGetChange(selection) {
          // 获取到子组件通过 $emit 传递的数据
          console.log("selection", selection)
          this.rejectList.flowList = selection
        },
        // 申诉
        async startAppeal() {
          let list = this.rejectList.flowList
          if (!list || list.length === 0) {
            this.$message.error("您还未选择数据!")
          }

          const allAppealed = this.rejectList.flowList.every(
            row => row.appealExist === "t"
          )
          console.log(allAppealed)
          if (!allAppealed) {
            this.$message.error("您选择的数据还未填写申述!")
            return
          }

          this.rejectList.node = this.node
          this.rejectList.businessId = this.businessId
          const res = await startAppealByInter(this.rejectList)
          console.log("返回", res)
          if (res.code === "0000") {
            this.$message.success(res.data)
            this.$refs.table.getTableData()
            // 跳转页面并刷新
            // this.$router.push({
            //   path: '/home'
            // }).then(() => {
            //   this.$router.go(0); // 刷新当前页面
            // });
          } else {
            this.$message.error("申述失败!")
          }
          // 刷新页面
        },
        async save() {
          let res = await editBaseInfo(this.baseInfo)
          console.log("问题分类", res.data)
          if (res.code === "0000") {
            this.$message.success("保存成功")
          }
        },
        async sends() {
          let res = await startDistrictFeedbackFlow(this.flow)
          console.log("发起结果", res.data)
          if (res.code === "0000") {
            this.$message.success("派发成功")
            // 跳转页面并刷新
            this.$router
              .push({
                path: "/home"
              })
              .then(() => {
                this.$router.go(0) // 刷新当前页面
              })
          }
        },

        details(row) {
          const data = encodeURIComponent(JSON.stringify(row)) // 将对象转换为字符串并进行编码
          // let state = ""

          // if (this.buttonName === "填报") {
          //   state = "appeal_fill"
          // } else {
          //   state = "appeal_chek"
          // }
          // console.log("跳转数据state",state)
          this.$router.push({
            path: "/pccw_menu/workflow/cooperation_evaluate/assess_questions_collection/stage_assessment",
            query: {
              data: data,
              operateState: this.buttonState,
              // operateState: "assess_fill",
              deductionNode: this.node,
              businessId:this.businessId
              // isFill: isDeduct
              // key: row.businessId
            }
          })
        },

        //重置
        reset(form) {
          this.search(form)
        },
        //搜索
        search() {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        }
      }
    }
</script>

<style lang="scss" scoped>
  .gray-style {
    color: #ccc; /* 或者使用其他方式来置灰，如背景色等 */
  }

  .content-text {
    margin: 2px 20px;
    display: flex;
    justify-content: space-between;
    /* 控制内容之间的间距 */
    align-items: center;
    /* 控制内容垂直方向的对齐方式 */
  }

  ::v-deep .disabledRow {
    cursor: not-allowed;
    pointer-events: none;
    color: #ccc; // 改当前行的字体颜色

    .el-checkbox__inner {
      // color: blue;
    }
  }
</style>
