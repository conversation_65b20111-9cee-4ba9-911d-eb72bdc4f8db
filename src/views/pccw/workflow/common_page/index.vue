<template>
  <div>
    <flow-detail :flow="flow" :showTransact="false" :type="approveType">
      <div slot="content">
       <mssCard title="审批表单">
          <div slot="content">
            暂无数据...
          </div>
        </mssCard>
      </div>
    </flow-detail>
  </div>
</template>

<script>
  import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue"

  export default {
    components: {
      FlowDetail
    },
    data() {
      return {
        flow: {},
        approveType: '',
        showTransact: true, // 流程委派
        businessId:''
      };
    },
    created() {
      this.approveType = decodeURIComponent(this.$route.query.type); // 获取类型信息
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow); // 获取流程对象
      this.businessId = this.flow.businessId; // 根据这个去后端查数据
    },
    methods: {

    }
  };
</script>


<style lang="scss" scoped>

</style>
