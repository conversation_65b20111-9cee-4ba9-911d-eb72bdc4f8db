<template>
  <div>
    <div class="operate-btn">
      <el-button type="primary" @click="toSave">发起流程</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssCard title="发起流程">
      <div slot="content" class="common-form">
        <el-form :model="form" :rules="rules" ref="form" label-width="100px">
          <el-form-item label="流程名称">
            <el-input v-model="form.title"></el-input>
          </el-form-item>
          <el-form-item label="流程key">
            <el-input v-model="form.processKey"></el-input>
          </el-form-item>
          <el-form-item label="businessId">
            <el-input v-model="form.businessId"></el-input>
          </el-form-item>
          <!-- <el-form-item label="用户id"> -->
          <el-form-item label="发起人id/施工经理">
            <el-input v-model="form.userId"></el-input>
          </el-form-item>
          <el-form-item label="监察经理">
            <el-input v-model="form.variables.visManager"></el-input>
          </el-form-item>
          <el-form-item label="工程实施经理（主）">
            <el-input v-model="form.variables.impManager"></el-input>
          </el-form-item>
          <!--
    <el-form-item label="角色名称" prop="type">
      <el-select v-model="form.type" placeholder="请选择角色">
        <el-option label="工程管理经理" value="1"></el-option>
        <el-option label="超级管理员" value="3"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="选择用户" class="sendMan is-required">
      <el-button @click="toSelectUser">选择</el-button>
    </el-form-item>
    <el-form-item label="用户账号" prop="userAccount">
      <el-input v-model="form.userAccount" @click="openDialog" disabled></el-input>
    </el-form-item>
    <el-form-item label="用户名称" prop="context">
      <el-input v-model="form.context" disabled></el-input>
    </el-form-item> -->
        </el-form>
        <!-- <div v-if="resData"> -->
        <!-- 响应数据：{{resData}} -->
        <!-- </div> -->
      </div>
    </mssCard>
    <select-user ref="selectUserDialog" @row-selected="handleRowSelected" />
  </div>
</template>

<script>
  import SelectUser from "./SelectUser.vue"
  import {startFlow} from "@/api/pccw/workflow/index.js";

  export default {
    components: {
      SelectUser
    },
    data() {
      return {
        rules: {
          resData:{},
          type: [
            {
              required: true,
              message: "请选择角色",
              trigger: "change"
            }
          ],
          userAccount: [
            {
              required: true,
              message: "请选择用户",
              trigger: "blur"
            }
          ],
          context: [
            {
              required: true,
              message: "请选择用户",
              trigger: "blur"
            }
          ]
        },
        labelPosition: "right",
        dialogVisible: false,
        form: {
          processKey: "task_level_completion_initiation_flow",
          title: "",
          businessId: "",
          userId: "",
          variables: {
            impManager: "",
            visManager:null
          }
          // type: '', // 角色名称对应的类型
          // id: '',
          // context: '',
          // unit: '',
          // userAccount: '',
        }
      }
    },
    methods: {
      toSave() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submitData()
          } else {
            return
          }
        })
      },
      async submitData() {
        const res = await startFlow(this.form)
        if (res.code === "0000") {
          this.$message.success("流程发起成功")
          // this.$router.push({
          //   path: "/pccw_menu/report_select/purchase_order"
          // })
          this.resData=res.data
        }
      },
      goBack() {
        this.$router.go(-1) // 返回上一个路由
      },
      toSelectUser() {
        this.$refs.selectUserDialog.selectUserDialog = true
      },
      // 处理子组件传值
      handleRowSelected(row) {
        this.form.context = row.context
        this.form.unit = row.unit
        this.form.userAccount = row.userAccount
        this.form.userId = row.userId
        // console.log("子组件数据", row);
      },
      openDialog() {
        this.dialogVisible = true
      }
    }
  }
</script>

<style lang="scss" scoped>
  .el-form-item {
    margin: 20px 0px;
  }

  ::v-deep .el-form-item__label {
    padding-right: 12px;
  }
</style>
