import router from '@/router'

/**
 * 路由跳转方法
 * @param {Object} row - 跳转数据
 * @param {string} type - 类型 ("todo" 或 "finished")
 */
export function navigateRouter(row, type) {
  console.log(`${type === 'todo' ? '待办' : '已办'} row123`, row);
  // console.log("判断",row.name.includes('视频验收'))
  const data = JSON.stringify(row);
  console.log("判断123",row.flowKey.includes("task_level_completion_initiation_flow"))
  const pathMappings = {
        'task_level_completion_initiation_flow': '/pccw_menu/pccw_workflow/task_completion',
    '安全生产费': '/pccw_menu/implementation_link/secure_produce_pendingSave',
    '工序质量': '/pccw_menu/workflow/review/process_quality/check',
    '视频验收': '/pccw_menu/workflow/review/video_check/check',
    '考核问题收集': '/pccw_menu/workflow/cooperation_evaluate/assess_questions_collection',
    '考核问题申诉': '/pccw_menu/workflow/cooperation_evaluate/assess_questions_appeal',
    // 适用于 finished 的路径
    '任务已完工，请在40天内完成割接交维': '/pccw_menu/pccw_workflow/maintain_unfinished',
    '任务已达资产预定可使用状态，请同步完成割接交维和资产交维，割接交维结束次月月末前完成转资': '/pccw_menu/pccw_workflow/maintain_finished',
    '超割接交维结束的次月月末时间未转资督办单': '/pccw_menu/pccw_workflow/supervise',
    '项目已结审，请在审计定案后的一个月内完成审减确认和转资数量在资产系统的提单修改': '/pccw_menu/pccw_workflow/control'
  };

  let path = null;

  if (type === 'todo') {
    // name：任务名称
    // workflowName：任务类型
    if (row.name.includes('工序质量')) {
      path = pathMappings['工序质量'];
    } else if (row.name.includes('安全生产费')) {
      path = pathMappings['安全生产费'];
    } else if (row.name.includes('视频验收')) {
      path = pathMappings['视频验收'];
    } else if (row.workflowName.includes('考核问题申诉')||row.workflowName.includes('考核问题申述')
    ||row.flowKey.includes('appeal_')
    ) {
      path = pathMappings['考核问题申诉'];
    } else if (row.workflowName.includes('考核问题收集')||row.flowKey.includes('assess_')) {
      path = pathMappings['考核问题收集'];
    }else if (row.flowKey.includes("task_level_completion_initiation_flow")) {
      console.log("包含竣工")
      path = pathMappings["task_level_completion_initiation_flow"];
    }

  } else if (type === 'finished') {
    if (Object.keys(pathMappings).includes(row.name)) {
      path = pathMappings[row.name];
    } else if (row.name.includes('安全生产费')) {
      path = pathMappings['安全生产费'];
    } else if (row.workflowName.includes('视频验收')) {
      path = pathMappings['视频验收'];
    } else if (row.workflowName.includes('工序质量')) {
      path = pathMappings['工序质量'];
    } else if (row.workflowName.includes('考核问题申诉')||row.workflowName.includes('考核问题申述')
    ||row.flowKey.includes('appeal_')
    ) {
      console.log("已办映射考核问题申诉")
      path = pathMappings['考核问题申诉'];
    } else if (row.workflowName.includes('考核问题收集')||row.flowKey.includes('assess_')) {
      console.log("已办映射222")
      path = pathMappings['考核问题收集'];
    } else if (row.flowKey.includes("task_level_completion_initiation_flow")) {
      console.log("包含竣工")
      path = pathMappings["task_level_completion_initiation_flow"];
    }
  }

  // 代码里写的路径
  if (path) {
    console.log("进来了",path)
    router.push({
      path: path,
      query: {
        flow: data,
        type: type
      }
    });
  // 统一待办/已办计算出的路径
  } else {
    console.log('统一待办跳转', row.url);
    router.push(row.url);
  }
}
