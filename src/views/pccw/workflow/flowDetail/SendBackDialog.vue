<template>
  <el-dialog title="退回" :visible.sync="sendBackDialog" class="dialog" :modal-append-to-body="false">
    <el-form :label-position="labelPosition" label-width="100px">
      <el-form-item label="指定退回节点" v-if="returnType ===1">
        <template v-if="returnableList.length>0">
          {{returnableList[0].taskName}}
<!--         <el-radio v-model="returnRo.returnType" label="1">申请人</el-radio>
          <el-radio v-model="returnRo.returnType" label="2">任一节点</el-radio> -->
        </template>
      </el-form-item>
      <el-form-item label="可选退回节点列表" class="nodeList" v-if="returnType ===2">
        <el-select v-model="returnRo.targetTaskKey" placeholder="请选择任务节点">
          <el-option v-for="item in returnableList" :key="item.taskKey" :label="item.taskName" :value="item.taskKey"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审批意见" class="approvalOpinion">
        <el-input type="textarea" v-model="returnRo.approvalComments" :rows="2" resize='none' class="comments"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="consentReturn()">确定</el-button>
      <el-button @click="sendBackDialog = false">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {
    approveReturn
  } from "@/api/pccw/workflow/index.js";
export default {
    props: {
      // 1为默认退回1节点，2为指定退回节点列表
      // returnType: {
      //   type: Number,
      //   default: 1
      // },
      // 退回任务节点列表数据
      // returnableList: {
      //   type: Array,
      //   default: () => []
      // },
      taskId: {
        type: String,
        default: ''
      }
    },
  data() {
    return {
      returnableList:[],
      returnType:1,
      sendBackDialog: false,
      labelPosition: 'top',
        returnRo: {
          taskId: "",
          approvalComments: "",
          copyUserList: [],
          returnType: '1', // 1.退回申请人 2.退回任一节点
          targetTaskKey: "", // 退回任一节点的key
          userId: "",
          tenantId: "0"
        },
    };
  },
  created() {
    console.log("组件的returnType",this.returnType)
    this.returnRo.taskId=this.taskId
  },
  methods: {
      // 确认退回
      async consentReturn() {
        const res = await approveReturn(this.returnRo);
        if(res.msg=="success"){
          this.$message({
            type: 'success',
            message: '退回成功'
          });
          // 跳转页面并刷新
          this.$router.push({
            path: '/home'
          }).then(() => {
            this.$router.go(0); // 刷新当前页面
          });
        }

      },
  }
};
</script>

<style lang="scss" scoped>
.el-select{
  width: 100%;
}
</style>
