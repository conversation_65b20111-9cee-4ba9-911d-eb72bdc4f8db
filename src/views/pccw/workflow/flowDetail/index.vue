<template>
  <div>
    <div class="operate-btn">
      <!-- 自定义按钮插槽 -->
      <!-- <slot name="custom-buttons"></slot> -->
      <slot
        name="custom-buttons"
        v-if="ignoreTypeCheck || type === 'todo'"
      ></slot>
      <template v-if="type == 'todo'">
        <el-button
          type="primary"
          @click="openApprovalDialog"
          v-if="showApproval"
          >{{approvalName}}</el-button
        >
        <!-- showReturn手动控制，flagReturn查是否可退回 -->
        <el-button
          type="primary"
          @click="openSendBackDialog"
          v-if="showReturn && flagReturn"
          returnType="returnType"
          >退回</el-button
        >
        <el-button
          type="primary"
          @click="openTransactDialog"
          v-if="showTransact"
          >转办</el-button
        >
      </template>
      <el-button @click="goBack">返回</el-button>
    </div>
    <!-- 审批表单 -->
    <slot name="content"></slot>
    <div class="process-record">
      <mssCard title="流程记录">
        <div slot="content">
          <process-history
            :historyList="historyData"
            v-if="historyData.length > 0"
          />
        </div>
      </mssCard>
    </div>
    <template v-if="type == 'todo' && loadingFlag">
      <approval-dialog
        ref="approvalDialog"
        :taskId="flow.taskId"
        :flowName="flow.flowName"
        :nodeName="flow.nodeName"
        :nextApprovers="nextApprovers"
      />
      <send-back-dialog
        ref="sendBackDialog"
        :taskId="flow.taskId"
      />
      <transact-dialog ref="transactDialog" :taskId="flow.taskId" />
    </template>
  </div>
</template>

<script>
  import { getRecordList, getReturnList } from "@/api/pccw/workflow/index.js"
  import ProcessHistory from "@/views/pccw/workflow/common/ProcessHistory.vue"
  import BpmnDiagram from "@/views/pccw/workflow/common/BpmnDiagram.vue"
  import ApprovalDialog from "./ApprovalDialog.vue"
  import SendBackDialog from "./SendBackDialog.vue"
  import TransactDialog from "./TransactDialog.vue"

  export default {
    components: {
      ApprovalDialog,
      SendBackDialog,
      TransactDialog,
      BpmnDiagram,
      ProcessHistory
    },
    props: {
      // 流程对象
      flow: {
        type: Object,
        required: true
      },
      // 流程类型  todo则显示全部按钮
      type: {
        type: String,
        default: "default"
      },
      // 提交按钮名称
      approvalName: {
        type: String,
        default: "提交"
      },
      // 用于控制是否忽略 type 的检查 ,false则已办不示自定义按钮
      ignoreTypeCheck: {
        type: Boolean,
        default: true
      },
      // 用于控制是否自定义审批人
      customApproval: {
        type: Boolean,
        default: false
      },
      showApproval: {
        type: Boolean,
        default: true
      },
      // 不允许提交审批并弹框提示
      msgApproval: {
        type: String
      },
      showReturn: {
        type: Boolean,
        default: true
      },
      showTransact: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        nextApprovers:[], // 用于接收下一节点审批人列表
        returnType:1,
        allowApproval: true,// 允许提交审批
        flagReturn: true, // 通过查询，控制是否显示退回按钮
        loadingFlag: false, // 数据加载状态
        procInstId: null,
        activeName: "first",
        historyData: [],
        returnableList: [] // 退回任务节点列表数据
      }
    },
    mounted() {
    },
    created() {
      this.procInstId = this.flow.procInstId
      this.loadingFlag = true
      if (this.procInstId) {
        this.initialize() // 初始化页面数据
      }
      console.log(this.flow);
    },
    methods: {
      // 审批（提交）对话框
      openApprovalDialog() {
        // 触发父组件点击提交按钮事件，可在此控制校验
        this.$emit("submit-approval")
        if(!this.allowApproval){
          return
        }
        // 设置了msgApproval的值
        if (this.msgApproval) {
          this.$message({
            type: "warning",
            message: this.msgApproval
          })
          return
        }
        this.$refs.approvalDialog.approvalDialog = true
      },
      // 是否显示退回按钮
      async isShowSendBack() {
        const res = await getReturnList(this.flow.taskId)
        if (res.data == null || res.data.length === 0) {
          this.flagReturn = false
        }
      },
      // 退回对话框
      async openSendBackDialog() {
        const res = await getReturnList(this.flow.taskId)
        if (res.data == null || res.data.length === 0) {
          this.$message({
            type: "warning",
            message: "当前节点无法退回！"
          })
          return
        }
        console.log("显示退回节点",res.data)
        // 移除指定节点
        this.returnableList = res.data.filter(item => item.taskName !== "退回考核接口人");

        this.$refs.sendBackDialog.returnableList = this.returnableList
        this.$refs.sendBackDialog.sendBackDialog = true
      },
      // 转办对话框
      openTransactDialog() {
        this.$refs.transactDialog.selectUser = {}
        this.$refs.transactDialog.transactDialog = true
      },
      initialize() {
        this.getRecordList()
        // 如果为待办
        if (this.type === "todo") {
          this.isSendBack()
        }
      },
      // 是否显示退回按钮
      async isSendBack() {
        const res = await getReturnList(this.flow.taskId)
        if (res.data == null || res.data.length === 0) {
          this.flagReturn = false
        }
      },
      async getRecordList() {
        const res = await getRecordList(this.procInstId)
        this.historyData = res.data
      },
      getFlowXml() {
        this.$refs.bpmnDiagram.sendBackDialog = true
        this.dialogXML = true
      },
      goBack() {
        this.$router.go(-1) // 返回上一页
      }
    }
  }
</script>

<style scoped></style>
