<template>
    <el-dialog title="选择待办人" :visible.sync="selectUserDialog" class="dialog" :modal="false">
    <mssSearchForm v-show="showSearch" ref="searchForm"  :search-config="queryParams"
      @reset="resetQuery" @search="handleQuery"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTableFlow ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="queryUser"
         border>
        </mssTableFlow>
      </div>
    </mssCard>
  </div>
  </el-dialog>
</template>

<script>
  import {
    enableDelegate
  } from "@/api/pccw/workflow/index.js";

  export default {
    data() {
      return {
        selectUserDialog: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [
          {
            label: '姓名',
            type: 'input',
            fieldName: 'nickName'
          }
        ],
        // 查询条件
        queryUser: {
          nickName:'',
        },
        // 表头
        tableHeader: [
          {
            prop: "nickName",
            label: "姓名",
            align: "center",
            tooltip: true,
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '50px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.selectRow(row)
                    }
                  } >
                  选择 <
                  /span>
                } <
                /span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: enableDelegate,
      };
    },
    created() {},
    methods: {
      // 选择当行
      selectRow(row) {
        this.selectUserDialog=false
        this.$emit('row-selected', row);// 传值父组件
      },
      /** 搜索按钮操作 */
      handleQuery(form) {
        // console.log("form", form);
        this.queryUser.nickName = form.nickName
        this.$refs.table.page.current = 1
        this.queryUser = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      },
    }
  };
</script>


<style lang="scss" scoped>

::v-deep .el-dialog{
  width: 60%;
}
  .table_cell_click {
    cursor: pointer;
    color: #02a7f0;

    .el-icon-data-analysis {
      font-size: 14px;
    }
  }
</style>
