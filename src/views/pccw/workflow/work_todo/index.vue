<template>
  <div class="app-container">
    <mssSearchForm v-show="showSearch" ref="searchForm" :form="queryTodo" :search-config="queryParams"
      @reset="resetQuery" @search="handleQuery"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="toSelectUser" >更换用户</el-button>
      <div v-if="selectUser">
        {{selectUser.nickName}}
      </div>
      </div>
      <div slot="content">
        <mssTableFlow ref="myTable" :api="tableApi" :columns="tableHeader" :static-search-param="queryTodo"
          :customSize="queryTodo.size" border selection>
        </mssTableFlow>
      </div>
    </mssCard>
    <el-dialog title="流程图" :visible.sync="dialogXML" width="80%">
      <bpmn-diagram :procInstId="procInstId" v-if="dialogXML" />
    </el-dialog>
    <select-user ref="selectUserDialog" @row-selected="handleRowSelected" />
  </div>
</template>

<script>
  import SelectUser from './SelectUser.vue';
  import {
    queryTodoList
  } from "@/api/pccw/workflow/index.js";
  import {
    getToken
  } from "@/utils/auth";
  import BpmnDiagram from "@/views/pccw/workflow/common/BpmnDiagram.vue";


  export default {
    components: {
      SelectUser,
      BpmnDiagram,
    },
    data() {
      return {
        selectUser: {}, // 选择的用户信息
        procInstId: '',
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [{
            label: '任务名称',
            type: 'input',
            fieldName: 'title'
          },
          {
            label: '任务类型',
            type: 'input',
            fieldName: 'flowName'
          },
        ],
        // 查询条件 - 我的待办
        queryTodo: {
          condition: {
            title: '',
            flowName: '',
            suspensionState: '',
            receiveStartTime: '',
            receiveEndTime: '',
            tenantId: '0',
            roleIds: [],
            deptId: '',
            userId:null,
          },
        },
        // 表头
        tableHeader: [{
            prop: "title",
            label: "任务名称1",
            align: "center",
            tooltip: true,
            width: 220,
            formatter: (row) => {
              return ( 
              <span class = "table_cell_click"
                onclick = {
                  () => {
                    this.toDetail(row)
                  }
                } > {
                  row.title
                } </span>
              )
            }
          },
          {
            prop: "flowName",
            label: "任务类型",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "createTime",
            label: "发起时间",
            align: "center",
            tooltip: true,
            width: 220
          },
          {
            prop: "taskName",
            label: "当前节点",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "startUserName",
            label: "派发人",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "suspensionState",
            label: "挂起状态",
            align: "center",
            tooltip: true,
            width: 120,
            formatter: (row, column, cellValue) => {
              if (row.suspensionState == 1) {
                return '正常';
              } else {
                return '已挂起';
              }
            }
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '50px',
            formatter: (row) => {
              return ( <span > {
                  <span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.getFlowXml(row)
                    }
                  } >
                  监控 </span>
                } </span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: queryTodoList,
      };
    },
    created() {

    },
    watch: {
      selectUser: {
        handler(newVal, oldVal) {
          if (newVal && Object.keys(newVal).length > 0) {
            // 当 selectUser 的值改变且不为空时执行重新发起请求的操作
            this.fetchData();
          }
        },
        deep: true
      }
    },
    methods: {
        fetchData() {
          console.log('发起新请求');
          this.queryTodo.condition.userId=this.selectUser.userId
          this.$refs.myTable.refresh()
        },
      // 处理子组件传值
      handleRowSelected(row) {
        this.selectUser = row
        console.log(this.selectUser);
      },
      toSelectUser() {
      this.$refs.selectUserDialog.selectUserDialog = true;
      },
      // 点击任务名称
      toDetail(row) {
        const data = encodeURIComponent(JSON.stringify(row));
        this.$router.push({
          path: `/pccw_menu/pccw_workflow/approve_detail`,
          query: {
            flow: data,
            type: "todo"
          }
        })
      },
      // 查询流程图
      getFlowXml(row) {
        this.procInstId = row.procInstId
        this.dialogXML = true
      },
      /** 搜索按钮操作 */
      handleQuery(form) {
        console.log("form", form);
        this.queryTodo.condition.flowName = form.condition.flowName
        this.queryTodo.condition.title = form.condition.title
        this.$refs.myTable.page.current = 1
        this.queryTodo = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.myTable.getTableData()
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      },

    }
  };
</script>


<style lang="scss" scoped>
  .table_cell_click {
    cursor: pointer;
    color: #02a7f0;

    .el-icon-data-analysis {
      font-size: 14px;
    }
  }
</style>
