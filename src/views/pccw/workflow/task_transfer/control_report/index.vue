<template>
  <div>
        <!--查询框 -->
        <mssSearchForm ref="searchForm"
        :form="staticSearchForm"
        :search-config="searchConfig"
        @reset="reset"
          @search="search"></mssSearchForm>
        <mssCard title="结算审减后转资数量修改情况查询表">
          <div slot="content">
            <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam"
              border>
            </mssTable>
          </div>
        </mssCard>
  </div>
</template>

<script>
  import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue";
  import {
    getList,
  } from "@/api/pccw/report_select/task_transfer/control/index.js";

  export default {
    components: {
      FlowDetail
    },
    data() {
      return {
        // 搜索静态条件
        staticSearchForm: {

        },
        //搜索字段配置
        searchConfig: [
          {
            label: '建设单位',
            type: 'input',
            fieldName: 'constructionUnit'
          },
          {
              label: '项目编码',
              type: 'input',
              fieldName: 'projectCode'
            },
            {
                label: '资产标签号',
                type: 'input',
                fieldName: 'assetTagNumber'
              },
        ],
        msgApproval: '状态未处理完毕',
        allDealt: true,
        flow: {},
        approveType: '',
        showTransact: false,
        businessId: '',
        // 默认表格搜索条件
        staticSearchParam: {
          manFlowId: null,
          impFlowId: null
        },
        // 表头
        tableHeader: [{
            prop: "constructionUnit",
            label: "建设单位",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "auditApplicationId",
            label: "审计编号",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectManagerName",
            label: "送审项目经理",
            align: "center",
            tooltip: true,
          },
          {
            prop: "engineeringManagementManagerMain",
            label: "工程管理经理(主)",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectManagerFirstSubmissionTime",
            label: "项目经理第一次提交流程时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "auditProcessEndTime",
            label: "审计流程结束时间",
            align: "center",
            tooltip: true,
          },

          {
            prop: "assetSystemQuantityModifyTime",
            label: "资产系统数量修改时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "assetTagNumber",
            label: "资产标签号",
            align: "center",
            tooltip: true,
          },
          {
            prop: "modifiedTransferAssetQuantity",
            label: "修改后的转资数量值",
            align: "center",
            tooltip: true,
          },
          // {
          //   prop: "dealState",
          //   label: "处理状态",
          //   align: "center",
          //   tooltip: true,
          //   formatter: function(row, column, cellValue) {
          //     return cellValue === "1" ? "已处理" : "未处理";
          //   }
          // },
        ],
        // 表格数据API
        tableApi: getList,
      };
    },
    created() {
      this.handleQueryParameters() // 处理初始搜索条件
    },
    mounted() {
      // 是否修改审批按钮消息
      this.$bus.$on('tableDataChange', (tableData, page) => {
        this.allDealt = tableData.every(item => item.dealState === "1");
        if (!this.allDealt) {
          this.msgApproval = '状态未处理完毕';
        }
      });
    },

    methods: {
      customAction(){
        // 打开审批对话框
        this.$refs.flowDetail.openApprovalDialog()
      },
      handleQueryParameters() {

      },
      //重置
      reset(form) {
          this.search(form)
      },
      //搜索
      search() {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
      },
    }
  };
</script>


<style lang="scss" scoped>

</style>
