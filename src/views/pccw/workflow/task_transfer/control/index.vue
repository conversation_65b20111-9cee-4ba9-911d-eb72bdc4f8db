<template>
  <div>
    <flow-detail
      :flow="flow"
      :showTransact="false"
      :showReturn="false"
      :type="approveType"
      :msgApproval="msgApproval"
      :ignoreTypeCheck="false"
      ref="flowDetail"
    >
      <template v-slot:custom-buttons>
        <el-button type="primary" @click="save()">保存</el-button>
      </template>
      <div slot="content">
        <!--查询框 -->
        <mssSearchForm
          ref="searchForm"
          :form="staticSearchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <mssCard title="结算审减后转资数量修改单待办">
          <div slot="content">
            <el-form
              ref="form"
              :model="form"
              :rules="form.rules"
              :data="form.tableData"
            >
              <mssTableCustom
                ref="table"
                :api="tableApi"
                :columns="tableHeader"
                :static-search-param="staticSearchParam"
                border
              >
              </mssTableCustom>
            </el-form>
          </div>
        </mssCard>
      </div>
    </flow-detail>
  </div>
</template>

<script>
  import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue"
  import {
    getList,
    editList
  } from "@/api/pccw/report_select/task_transfer/control/index.js"

  export default {
    components: {
      FlowDetail
    },
    data() {
      return {
        form: {
          tableData: [], // 表格数据
          rules: {
            modifiedTransferAssetQuantity: [
              { required: false, message: "不能为空", trigger: "blur" },
              {
                validator: (rule, value, callback) => {
                  const reg =
                    /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/
                  if (reg.test(value)) {
                    callback()
                  } else {
                    callback(new Error("请填写正确的数字"))
                  }
                },
                trigger: "blur"
              }
            ],
            assetSystemQuantityModifyTime: [
              { required: false, message: "不能为空", trigger: "blur" }
            ],
            assetTagNumber: [
              { required: false, message: "不能为空", trigger: "blur" }
            ]
            // modifiedTransferAssetQuantity: [
            //  { required: true, type: 'number', message: '必须为数字', trigger: 'blur' }
            // ],
          }
        },
        // 搜索静态条件
        staticSearchForm: {},
        //搜索字段配置
        searchConfig: [
          {
            label: "建设单位",
            type: "input",
            fieldName: "constructionUnit"
          },
          {
            label: "项目编码",
            type: "input",
            fieldName: "projectCode"
          }
        ],
        msgApproval: "状态未处理完毕",
        allDealt: true,
        flow: {},
        approveType: "",
        showTransact: false,
        businessId: "",
        // 默认表格搜索条件
        staticSearchParam: {
          manFlowId: null,
          impFlowId: null
        },
        // 表头
        tableHeader: [
          {
            prop: "constructionUnit",
            label: "建设单位",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "auditApplicationId",
            label: "审计编号",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectManagerName",
            label: "送审项目经理",
            align: "center",
            tooltip: true
          },
          {
            prop: "engineeringManagementManagerMain",
            label: "工程管理经理(主)",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectManagerFirstSubmissionTime",
            label: "项目经理第一次提交流程时间",
            align: "center",
            tooltip: true
          },
          {
            prop: "auditProcessEndTime",
            label: "审计流程结束时间",
            align: "center",
            tooltip: true
          },
          {
            prop: "assetSystemQuantityModifyTime",
            label: "资产系统数量修改时间",
            align: "center",
            tooltip: false,
            formatter: (row, column, cellValue, index) => {
              if (row.dealState === "1") {
                return cellValue
              } else {
                return (
                  <div>
                    <el-form-item
                      prop={`tableData[${index}].assetSystemQuantityModifyTime`}
                      rules={this.form.rules.assetSystemQuantityModifyTime}
                    >
                      <el-input
                        v-model={row.assetSystemQuantityModifyTime}
                      ></el-input>
                    </el-form-item>
                  </div>
                )
              }
            }
          },
          {
            prop: "assetTagNumber",
            label: "资产标签号",
            align: "center",
            minWidth: 120,
            tooltip: false,
            formatter: (row, column, cellValue, index) => {
              if (row.dealState === "1") {
                return cellValue
              } else {
                return (
                  <div>
                    <el-form-item
                      prop={`tableData[${index}].assetTagNumber`}
                      rules={this.form.rules.assetTagNumber}
                    >
                      <el-input v-model={row.assetTagNumber}></el-input>
                    </el-form-item>
                  </div>
                )
              }
            }
          },
          {
            prop: "modifiedTransferAssetQuantity",
            label: "修改后的  转资数量值",
            align: "center",
            tooltip: false,
            formatter: (row, column, cellValue, index) => {
              const displayedValue = Number(cellValue) || "" // 如果 cellValue 不是数字，则默认为 0

              console.log(
                "console.log(typeof displayedValue)",
                typeof displayedValue
              )

              if (row.dealState === "1") {
                return cellValue
              } else {
                return (
                  <div>
                    <el-form-item
                      prop={`tableData[${index}].modifiedTransferAssetQuantity`}
                      rules={this.form.rules.modifiedTransferAssetQuantity}
                    >
                      <el-input
                        v-model={row.modifiedTransferAssetQuantity}
                        type="number"
                      ></el-input>
                    </el-form-item>
                  </div>
                )
              }
            }
          },
          {
            prop: "dealState",
            label: "处理状态",
            align: "center",
            tooltip: true,
            formatter: function (row, column, cellValue) {
              return cellValue === "1" ? "已处理" : "未处理"
            }
          }
        ],
        // 表格数据API
        tableApi: getList
      }
    },
    created() {
      const flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow) // 获取流程对象
      this.approveType = decodeURIComponent(this.$route.query.type) // 获取类型信息
      this.businessId = this.flow.businessId // 根据这个去后端查数据
      this.handleQueryParameters() // 处理初始搜索条件
    },
    mounted() {
      // 是否修改审批按钮消息
      this.$bus.$on("tableDataChange", (tableData, page, data) => {
        this.form.tableData = tableData //保存表格数据
        // 是否全部处理完毕
        if (data.allDealState != "1") {
          this.msgApproval = "状态未处理完毕"
        } else {
          this.msgApproval = null
        }
      })
    },

    methods: {
      save(row, column, cellValue, index) {
        this.$refs.form.validate(valid => {
          if (valid) {
            // 表单校验通过，继续发起请求
            this.batchAdd()
          } else {
            // 表单校验失败，给出提示或中止请求
            console.log("表单校验未通过")
          }
        })
      },
      async batchAdd() {
        const res = await editList(this.form.tableData)
        if (res.code != "0000") return
        console.log(res)
        this.$message.success(res.data)
        this.$refs.table.getTableData()
      },
      customAction() {
        // 打开审批对话框
        this.$refs.flowDetail.openApprovalDialog()
      },
      handleQueryParameters() {
        // 提取前缀
        let prefix = this.businessId.substring(0, 2)
        if (prefix === "IM") {
          this.staticSearchParam.impFlowId = this.businessId
        } else if (prefix === "MM") {
          this.staticSearchParam.manFlowId = this.businessId
        } else {
          this.staticSearchParam.type = "1"
        }
      },
      //重置
      reset(form) {
        this.search(form)
      },
      //搜索
      search() {
        this.$refs.table.page.current = 1
        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      }
    }
  }
</script>

<style lang="scss" scoped></style>
