<template>
  <div class="app-container">
    <mssCard title="任务转资不及时统计明细">
      <div slot="headerBtn">
        <el-button type="primary" @click="downloadData">导出</el-button>
        <el-button @click="$router.go(-1)">返回</el-button>
      </div>
      <div slot="content">
        <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam" border
          selection>
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import qs from 'qs';
  import {
    detailsNotTimeWarningList,
    exportNotTimeWarningList
  } from "@/api/pccw/report_select/task_transfer/index.js";

  export default {
    components: {

    },
    data() {
      return {
        // 默认表格搜索条件
        staticSearchParam: {
          // granularity: "profession",
          startDate: "",
          // engineeringManagementManagerMain: "王红",
          // department: "业务支撑网室",
        },
        // 表头
        tableHeader: [{
            prop: "companyName",
            label: "公司名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectCode",
            label: "项目编号",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectType",
            label: "项目类型",
            align: "center",
            tooltip: true,
          },
          {
            label: "任务编号",
            prop: "taskCode",
            align: "center",
            tooltip: true
          },
          {
            label: "任务名称",
            prop: "taskName",
            align: "center",
            tooltip: true
          },
          {
            label: "工程管理专业类型",
            prop: "taskEngineeringDiscipline",
            align: "center",
            tooltip: true
          },
          {
            label: "任务终止状态",
            prop: "isTaskTerminated",
            align: "center",
            tooltip: true
          },
          {
            label: "交维开始日期",
            prop: "maintenanceStartDate",
            align: "center",
            tooltip: true
          },
          {
            label: "交维结束日期",
            prop: "maintenanceEndDate",
            align: "center",
            tooltip: true
          },
          {
            label: "第一次转资完成时间",
            prop: "firstCapitalizationCompletionTime",
            align: "center",
            tooltip: true
          },
          {
            label: "已签收资产条目数",
            prop: "signedAssetEntriesCount",
            align: "center",
            tooltip: true
          },
          {
            label: "EAM流程中资产条目数",
            prop: "eamAssetEntriesCount",
            align: "center",
            tooltip: true
          },
          {
            label: "转资次数",
            prop: "capitalizationTimes",
            align: "center",
            tooltip: true
          },
          {
            label: "完工日期",
            prop: "completionDate",
            align: "center",
            tooltip: true
          },
          {
            label: "任务转资截止时间",
            prop: "taskDeadline",
            align: "center",
            tooltip: true
          },
          {
            label: "ERP未转资金额",
            prop: "erpUntransferredAmount",
            align: "center",
            tooltip: true
          },
          {
            label: "建设单位",
            prop: "constructUnit",
            align: "center",
            tooltip: true
          },
          {
            label: "工程实施经理（主）",
            prop: "engineeringImplementationManagerMain",
            align: "center",
            tooltip: true
          },
          {
            label: "工程实施经理（辅）",
            prop: "engineeringImplementationManagerAuxiliary",
            align: "center",
            tooltip: true
          },
          {
            label: "工程管理经理（主）",
            prop: "engineeringManagementManagerMain",
            align: "center",
            tooltip: true
          },
          {
            label: "归属科室",
            prop: "department",
            align: "center",
            tooltip: true
          },
          {
            label: "预警时间",
            prop: "alertTimeRemark",
            align: "center",
            tooltip: true
          }

        ],
        // 表格数据API
        tableApi: detailsNotTimeWarningList,
      };
    },
    created() {
      this.staticSearchParam.exportType = "exportAlert"

      // 获取当前日期
      let today = new Date();
      // 获取昨天日期
      let yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      // 格式化昨天日期为 "YYYY-MM-DD" 格式
      let formattedYesterday = yesterday.toISOString().split('T')[0];
      this.staticSearchParam.startDate = formattedYesterday


      const row = this.$route.query.row
      const type = this.$route.query.type

      if (type == "impManager") {
        this.staticSearchParam.granularity = "city"
        this.staticSearchParam.engineeringImplementationManagerMain = row.engineeringImplementationManagerMain
        this.staticSearchParam.businessEntity = row.city
        // 管理经理
      } else {
        this.staticSearchParam.granularity = "profession"
        this.staticSearchParam.engineeringManagementManagerMain = row.engineeringManagementManagerMain
        this.department = row.department
      }

      console.log(row)
    },
    methods: {
      async downloadData() {
        // console.log("t",this.staticSearchParam);
        let res = await exportNotTimeWarningList(qs.stringify(this.staticSearchParam))
        console.log("res", res);
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', '转资不及时统计列表.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
    }
  };
</script>


<style lang="scss" scoped>

</style>
