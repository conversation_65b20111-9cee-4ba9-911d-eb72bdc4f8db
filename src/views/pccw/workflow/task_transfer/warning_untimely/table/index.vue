<template>
  <div>
    <mssCard :title="title">
      <div slot="headerBtn">
        <el-button type="primary" @click="downloadData">导出</el-button>
        <el-button @click="$router.go(-1)">返回</el-button>
      </div>
      <div slot="content" v-if="searchParam">
        <mssTable ref="table" selection :api="api" :columns="columns" :stationary="stationary"
          :static-search-param="searchParam" border>
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import qs from 'qs';
import {
  summaryNotTimeWarningList, exportNotTimeWarningList
} from "@/api/pccw/report_select/task_transfer/index.js";
export default {
  props: {
    searchParam: null,
  },
  data() {
    return {
      type: null,
      title: '转资不及时统计列表',
      // 静态列表的参数
      staticSearchParam: {

      },
      searchConfig: [{
        label: '地市',
        type: 'input',
        fieldName: 'qualityCode'
      },
      {
        label: '项目名称',
        type: 'input',
        fieldName: 'projectName'
      },
      {
        label: '项目编号',
        type: 'input',
        fieldName: 'projectCode'
      },
      {
        label: '所在部门',
        type: 'select',
        options: [],
        fieldName: 'qualityCode'
      },
      {
        label: '费用类型',
        type: 'select',
        options: [],
        fieldName: 'status'
      },

      ],
      api: summaryNotTimeWarningList,
      columns3: [{
        prop: "businessEntity",
        label: "地市",
        align: "center",
        tooltip: true,
      },
      {
        prop: "engineeringImplementationManagerMain",
        label: "工程实施经理（主）",
        align: "center",
        tooltip: true,
      },
      ],
      columns: [{
        prop: "engineeringManagementManagerMain",
        label: "工程管理经理（主）",
        align: "center",
        tooltip: true,
      },
      {
        prop: "department",
        label: "专业科室",
        align: "center",
        tooltip: true,
      },
      {
        prop: "totalAmount",
        label: "已交维未转资总金额",
        align: "center",
        tooltip: true,
      },
      {
        prop: "totalTasks",
        label: "已交维未进行转资总任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "firstTransferTasks",
        label: "已交维未进行第一次转资任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "firstTransferAmount",
        label: "已交维未进行第一次转资任务金额",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTasks1To3",
        label: "1-3天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTasks4To7",
        label: "4-7天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTasks8To15",
        label: "8~15天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTasks16To30",
        label: "16~30预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "overdueTasks",
        label: "已超期未转资任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTime1To3",
        label: "距交维结束标准时间1~3天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTime4To7",
        label: "距交维结束标准时间4~7天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTime8To10",
        label: "距交维结束标准时间8~10天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "overdueMaintenanceTasks",
        label: "已超期未交维任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: 'operate',
        label: '操作',
        width: 100,
        formatter: (row) => {
          if (row.city && row.city === "合计") {
            return '';
          } else {
            return (
              <el-button
                type="text"
                onClick={() => {
                  this.detail(row)
                }}
              >
                查看
              </el-button>
            );
          }
        }
      }
      ],
      stationary: null,//静态数据
    }
  },
  created() {
    // this.staticSearchParam = {
    //   a: '1123',
    //   dispatchTime: this.dispatchTime,
    //   manager: this.manager
    // }
    this.initial()
  },

  methods: {
    detail(row) {
      this.$router.push({
        path: '/pccw_menu/acceptance_link/task_transfer/warning_untimely/details',
        query: {
          row: row,
          type: this.type
        }
      })
    },
    initial() {
      var currentUrl = window.location.href;
      // 工程实施经理（主、辅）收到的消息页面
      if (currentUrl.includes("/implementation_manager")) {
        this.columns[0].prop = 'city'
        this.columns[0].label = '地市'
        this.columns[1].prop = 'engineeringImplementationManagerMain'
        this.columns[1].label = '工程实施经理（主）'
        this.type = "impManager"
        // 管理经理
      } else {
        this.type = "manManager"
      }

      // 初始值
      this.staticSearchParam = this.searchParam
      this.staticSearchParam.exportType = "exportAlert"


    },
    async downloadData() {
      console.log("t", this.staticSearchParam);
      let res = await exportNotTimeWarningList(qs.stringify(this.staticSearchParam))
      console.log("res", res);
      const url = window.URL.createObjectURL(new Blob([res.data]));
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', '转资不及时统计列表.xlsx');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  }
}
</script>
