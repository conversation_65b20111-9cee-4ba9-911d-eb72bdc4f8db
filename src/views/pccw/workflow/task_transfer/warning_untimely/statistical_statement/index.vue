<template>
  <div>
    <div slot="content">
      <!--查询框 -->
      <mssSearchForm ref="searchForm" :form="staticSearchParam" :search-config="searchConfig" @reset="reset"
        @search="search" @changeSelect="changeSelect"></mssSearchForm>
      <mssCard title="转资不及时预警统计报表">
        <div slot="headerBtn">
          <el-button type="primary" @click="downloadData('exportSummary')">导出</el-button>
          <el-button type="primary" @click="downloadData('exportAll')">一键导出</el-button>
        </div>
        <div slot="content">
          <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam" border>
          </mssTable>
        </div>
      </mssCard>
    </div>
    </flow-detail>
  </div>
</template>

<script>
import qs from 'qs';
import {
  summaryNotTimeWarningList,
  exportNotTimeWarningList
} from "@/api/pccw/report_select/task_transfer/index.js";
export default {
  components: {

  },
  data() {
    return {
      //搜索字段配置
      searchConfig: [],
      //共同搜索字段配置
      searchConfigCommon: [{
        label: '统计维度',
        type: 'select',
        options: [{
          label: '公司类型维度',
          value: 'company'
        },
        {
          label: '地市、工程实施经理维度',
          value: 'city'
        },
        {
          label: '工程管理经理主、专业科室维度',
          value: 'profession'
        }
        ],
        itemAs: true,
        fieldName: 'granularity2'
      },
      {
        label: '日期',
        fieldName: 'startDate',
        type: "date1",
        format: 'yyyy-MM-dd',
        valueFormat: 'yyyy-MM-dd',
      },
      ],
      // 默认表格查询条件
      staticSearchParam: {
        granularity: 'company',
        startDate: ""
      },
      // 表头
      tableHeader: [],
      tableHeaderCommon: [{
        prop: "totalAmount",
        label: "已交维未转资总金额",
        align: "center",
        tooltip: true,
      },
      {
        prop: "totalTasks",
        label: "已交维未进行转资总任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "firstTransferTasks",
        label: "已交维未进行第一次转资任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "firstTransferAmount",
        label: "已交维未进行第一次转资任务金额",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTasks1To3",
        label: "1-3天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTasks4To7",
        label: "4-7天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTasks8To15",
        label: "8~15天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTasks16To30",
        label: "16~30预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "overdueTasks",
        label: "已超期未转资任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTime1To3",
        label: "距交维结束标准时间1~3天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTime4To7",
        label: "距交维结束标准时间4~7天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "warningTime8To10",
        label: "距交维结束标准时间8~10天预警任务数",
        align: "center",
        tooltip: true,
      },
      {
        prop: "overdueMaintenanceTasks",
        label: "已超期未交维任务数",
        align: "center",
        tooltip: true,
      }
      ],
      // 表格数据API
      tableApi: summaryNotTimeWarningList,
    };
  },
  created() {
    this.handleQueryParameters() // 处理初始搜索条件

  },
  mounted() {
    // 将 searchConfig 数组中的每个对象转换为响应式的
    // this.searchConfig.forEach((config, index) => {
    //   this.$set(this.searchConfig, index, config);
    // });

    // 设置默认搜索条件
    this.addConfigCompany()
    // 设置默认表头 为公司类型维度
    this.addHeaderCompany()
  },

  methods: {
    addHeaderCompany() {
      this.tableHeader = JSON.parse(
        JSON.stringify(this.tableHeaderCommon)
      )
      const newTableHeader = {
        prop: "transferType",
        label: "公司类型",
        align: "center",
        tooltip: true,
      }
      this.tableHeader.unshift(newTableHeader)
    },
    addConfigCompany() {
      const newConfig = {
        label: '公司类型',
        type: 'input',
        fieldName: 'transferType'
      };
      // 重置条件
      this.searchConfig = JSON.parse(
        JSON.stringify(this.searchConfigCommon)
      )
      // 添加到末尾
      this.searchConfig.push(newConfig)
    },

    changeSelect(name, val) {
      console.log("切换", name, val)
      if (name === 'granularity2') {
        this.staticSearchParam.granularity = val.value //赋值下拉框值给搜索条件
        // 1、选择公司
        if (val.value === 'company') {
          // 更换搜索列表
          this.addConfigCompany()
          // 更换表格列表
          this.addHeaderCompany()
          // 2、选择地市
        } else if (val.value === 'city') {
          // 创建一个新的搜索配置对象
          const newConfig = [{
            label: '地市',
            fieldName: 'businessEntity',
            type: 'input'
          },
          {
            label: '工程实施经理（主）',
            fieldName: 'engineeringImplementationManagerMain',
            type: 'input'
          }
          ]
          // 添加到末尾
          this.searchConfig = JSON.parse(
            JSON.stringify(this.searchConfigCommon)
          )
          this.searchConfig.push(...newConfig)
          // 更换表格列表
          this.tableHeader = JSON.parse(
            JSON.stringify(this.tableHeaderCommon)
          )
          // 新对象数组
          const newTableHeaders = [{
            prop: "city",
            label: "地市",
            align: "center",
            tooltip: true,
          },
          {
            prop: "engineeringImplementationManagerMain",
            label: "工程实施经理（主）",
            align: "center",
            tooltip: true,
          }
          ];
          // 一次性添加多个新对象到数组开头
          this.tableHeader.unshift(...newTableHeaders);
          // 3、选择工程管理经理主
        } else {
          // 创建一个新的搜索配置对象
          const newConfig = [{
            label: '专业科室',
            fieldName: 'department',
            type: 'input'
          },
          {
            label: '工程管理经理（主）',
            fieldName: 'engineeringManagementManagerMain',
            type: 'input'
          }
          ]
          // 添加到末尾
          this.searchConfig = JSON.parse(
            JSON.stringify(this.searchConfigCommon)
          )
          this.searchConfig.push(...newConfig)
          // 更换表格列表
          this.tableHeader = JSON.parse(
            JSON.stringify(this.tableHeaderCommon)
          )
          // 新对象数组
          const newTableHeaders = [{
            prop: "engineeringManagementManagerMain",
            label: "工程管理经理（主）",
            align: "center",
            tooltip: true,
          },
          {
            prop: "department",
            label: "专业科室",
            align: "center",
            tooltip: true,
          }
          ];
          // 一次性添加多个新对象到数组开头
          this.tableHeader.unshift(...newTableHeaders);
        }
        this.$refs.table.getTableData()
      }
    },
    handleQueryParameters() {
      // 获取昨日日期
      // 获取当前日期
      let today = new Date();

      // 获取昨天日期
      let yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);

      // 格式化昨天日期为 "YYYY-MM-DD" 格式
      let formattedYesterday = yesterday.toISOString().split('T')[0];

      this.staticSearchParam.startDate = formattedYesterday

    },
    //重置
    reset(form) {
      this.$refs.searchForm.searchForm.startDate = ""
      console.log(this.$refs.searchForm.searchForm);
      console.log("this.searchConfig", this.searchConfig);
      this.searchConfig
      this.handleQueryParameters()
      this.$refs.table.getTableData(this.staticSearchParam);
    },
    //搜索
    search() {
      this.$refs.table.page.current = 1
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.$refs.table.getTableData(this.loadParam);
    },
    async downloadData(type) {
      let loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      let exportParams = {
        granularity: loadParam.granularity,
        startDate: loadParam.startDate
      };
      exportParams.exportType = type;

      // 创建一个新的空白页
      const newWindow = window.open('about:blank');
      newWindow.document.write('<p>下载中，请稍等...</p>');

      try {
        // 发送下载请求
        let res = await exportNotTimeWarningList(qs.stringify(exportParams));

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', '转资不及时预警统计报表.xlsx');

        // 将下载链接添加到新窗口中
        newWindow.document.body.appendChild(link);
        link.click();
        // 清理资源
        window.URL.revokeObjectURL(url);
        newWindow.document.body.removeChild(link);
      } catch (error) {
        console.error('下载失败：', error);
        newWindow.document.write('<p>下载失败，请重试。</p>');
      } finally {
        // 关闭新窗口
        newWindow.close();
      }
    },
  }
};
</script>


<style lang="scss" scoped></style>
