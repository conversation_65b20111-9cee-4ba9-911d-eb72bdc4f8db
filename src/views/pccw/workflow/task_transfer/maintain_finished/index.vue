<template>
  <div>
    <flow-detail
      :flow="flow"
      :showTransact="false"
      :showApproval="false"
      :showReturn="false"
      :type="approveType"
      :msgApproval="msgApproval"
      :ignoreTypeCheck="false"
      ref="flowDetail"
    >
      <div slot="content">
        <!--查询框 -->
        <mssSearchForm
          ref="searchForm"
          :form="staticSearchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <mssCard
          title="任务已达资产预定可使用状态，请同步完成割接交维和资产交维，割接交维结束次月月末前完成转资。"
        >
          <div slot="content">
            <mssTableCustom
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              :static-search-param="staticSearchParam"
              border
            >
            </mssTableCustom>
          </div>
        </mssCard>
      </div>
    </flow-detail>
  </div>
</template>

<script>
  import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue"
  import { getList } from "@/api/pccw/report_select/task_transfer/maintain_finished/index.js"

  export default {
    components: {
      FlowDetail
    },
    data() {
      return {
        // 搜索静态条件
        staticSearchForm: {},
        //搜索字段配置
        searchConfig: [
          {
            label: "建设单位",
            type: "input",
            fieldName: "constructionUnit"
          },
          {
            label: "项目编码",
            type: "input",
            fieldName: "projectCode"
          },
          {
            label: "任务编码",
            type: "input",
            fieldName: "taskCode"
          }
        ],
        msgApproval: null,
        allDealt: true,
        flow: {},
        approveType: "",
        showTransact: false,
        businessId: "",
        // 默认表格搜索条件
        staticSearchParam: {
          manFlowId: null,
          impFlowId: null
        },
        // 表头
        tableHeader: [
          {
            prop: "constructionUnit",
            label: "建设单位",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "taskCode",
            label: "任务编码",
            align: "center",
            tooltip: true
          },
          {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "completionDate",
            label: "任务完工日期",
            align: "center",
            tooltip: true
          },
          {
            prop: "engineeringManagementManagerMain",
            label: "工程管理经理(主)",
            align: "center",
            tooltip: true
          },
          {
            prop: "engineeringImplementationManagerMain",
            label: "工程实施经理(主)",
            align: "center",
            tooltip: true
          },
          {
            prop: "engineeringImplementationManagerAuxiliary",
            label: "工程实施经理(辅)",
            align: "center",
            tooltip: true
          },
          {
            prop: "maintenanceEndDate",
            label: "割接交维结束时间",
            align: "center",
            tooltip: true
          },
          {
            prop: "dealState",
            label: "处理状态",
            align: "center",
            tooltip: true,
            formatter: function (row, column, cellValue) {
              return cellValue === "1" ? "已处理" : "未处理"
            }
          }
        ],
        // 表格数据API
        tableApi: getList
      }
    },
    created() {
      // this.msgApproval='状态未处理完毕'
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow) // 获取流程对象
      this.approveType = decodeURIComponent(this.$route.query.type) // 获取类型信息
      this.businessId = this.flow.businessId // 根据这个去后端查数据
      this.handleQueryParameters()
    },
    mounted() {
      // 是否修改审批按钮消息
      this.$bus.$on("tableDataChange", (tableData, page, data) => {
        // 是否全部处理完毕
        if (data.allDealState != "1") {
          this.msgApproval = "状态未处理完毕"
        } else {
          this.msgApproval = null
        }
      })
    },
    methods: {
      customAction() {
        // 打开审批对话框
        this.$refs.flowDetail.openApprovalDialog()
      },
      handleQueryParameters() {
        // 提取前缀
        let prefix = this.businessId.substring(0, 2)
        if (prefix === "IM") {
          this.staticSearchParam.impFlowId = this.businessId
        } else if (prefix === "MM") {
          this.staticSearchParam.manFlowId = this.businessId
        } else {
          this.staticSearchParam.type = "1"
        }
      },
      //重置
      reset(form) {
        this.search(form)
      },
      //搜索
      search() {
        this.$refs.table.page.current = 1
        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      }
    }
  }
</script>

<style lang="scss" scoped></style>
