<template>
  <div class="MessageDetails" v-loading="pageLoading">
    <div style="margin-top: 30px;"></div>
    <mssCard title="消息详情">
      <div slot="content">
        截至{{closeData}}晨，根据系统统计显示，您名下的任务转资提前滞后率情况统计如下，请根据业务进展情况进行相应的督促。
      </div>
    </mssCard>
    <div v-if="flag">
    <summaryNotTimeAlertList :searchParam="searchParam"></summaryNotTimeAlertList>
    </div>
  </div>
</template>

<script>
  import {
    findByMsgIdService
  } from "@/api/message_manager/message_manager_api";
  import summaryNotTimeAlertList from "./table/index.vue"
  export default {
    components: {
      summaryNotTimeAlertList
    },
    name: 'MessageDetails',
    data() {
      return {
        flag:false,
        searchParam:{},
        showTitle: true,
        closeData: '',
        formattedDate: '',
        id: '',
        messageType: '',
        messageTitle: '',
        pageLoading: false
      }
    },
    created() {
      // 浏览器传参
      const urlQuery = this.$route.query
      this.id = urlQuery.messageId
      this.messageType = urlQuery.type
      this.getData()

    },
    methods: {
      getData() {
        this.pageLoading = true
        findByMsgIdService(this.id)
          .then(res => {
            if (res.code === '0000') {
              // console.log("数据", res.data)
              // console.log("url数据", res.data.url)
              // 获取日期数据
              let title = res.data.title
              // 使用正则表达式匹配日期部分
              const regex = /(\d{4})年(\d{2})月(\d{2})日/;
              const match = title.match(regex);

              // 如果匹配成功，提取日期信息
              if (match) {
                const year = match[1];
                const month = match[2];
                const day = match[3];
                const dateStr = `${year}-${month}-${day}`;
                this.closeData = dateStr
              }

              // 查询 任务转资提前滞后率情况统计列表

             // 按照 & 符号分割成键值对数组
             const keyValuePairs = res.data.url.split('&');

             // 遍历键值对数组，再按照 = 符号分割键和值，并存入对象
             keyValuePairs.forEach(pair => {
                 const [key, value] = pair.split('=');
                 this.searchParam[key] = value;
             });
             this.flag=true // 渲染子组件
              console.log("this.",this.searchParam);
            }
          })
          .finally(_ => {
            this.pageLoading = false
          })
      },
      formatted() {
        // 将中文字符替换为英文字符
        const formattedDateStr = this.closeData.replace(/年|月/g, '-').replace('日', '');

        // 创建 Date 对象
        const date = new Date(formattedDateStr);

        // 获取年、月、日
        const year = date.getFullYear();
        const month = date.getMonth() + 1; // 月份从0开始，需要加1
        const day = date.getDate();

        // 组合成所需的格式
        this.formattedDate = `${year}-${month}-${day}`;
      }
    }
  }
</script>

<style scoped lang="scss">
  ::v-deep .msg-url {
    text-decoration: underline;
    color: #02a7f0;
  }

  .MessageDetails {
    .message-title {}
  }
</style>
