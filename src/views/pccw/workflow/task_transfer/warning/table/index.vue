<template>
  <div>
    <mssCard :title="title">
      <div slot="headerBtn">
        <el-button type="primary" @click="downloadData">导出</el-button>
        <el-button @click="$router.go(-1)">返回</el-button>
      </div>
      <div slot="content" v-if="searchParam">
        <mssTable ref="table" selection :api="api" :columns="columns" :stationary="stationary"
          :static-search-param="searchParam" border>
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import qs from 'qs';
  import {
    summaryNotTimeAlertList,
    exportNotTimeListAlert
  } from "@/api/pccw/report_select/task_transfer/index.js";
  export default {
    props: {
      searchParam: null,
    },
    data() {
      return {
        title: '任务转资提前滞后率情况统计列表',
        // 静态列表的参数
        staticSearchParam:{
          page: 1,
          limit: 10,
          auditSummaryKey: "",
          auditCityKey: ""
        },
        searchConfig: [{
            label: '地市',
            type: 'input',
            fieldName: 'qualityCode'
          },
          {
            label: '项目名称',
            type: 'input',
            fieldName: 'projectName'
          },
          {
            label: '项目编号',
            type: 'input',
            fieldName: 'projectCode'
          },
          {
            label: '所在部门',
            type: 'select',
            options: [],
            fieldName: 'qualityCode'
          },
          {
            label: '费用类型',
            type: 'select',
            options: [],
            fieldName: 'status'
          },
        ],
        api: null,
        columns: [{
            prop: "city",
            label: "地市",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "completedAssetNum",
            label: '已完工转资资产数',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "createTimeTransferLateNum",
            label: '创建时间转资滞后数',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "createTimeTransferLateRate",
            label: "创建时间转资滞后比例",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "createTimeTransferEarlyNum",
            label: "创建时间转资提前数",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "createTimeTransferEarlyRate",
            label: "创建时间转资提前比例",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "createTimeTransferOnTimeNum",
            label: "创建时间转资及时数",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "enableTimeTransferLateNum",
            label: "启用时间转资滞后数",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "enableTimeTransferLateRate",
            label: "启用时间转资滞后比例",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "enableTimeTransferEarlyNum",
            label: "启用时间转资提前数",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "enableTimeTransferEarlyRate",
            label: "启用时间转资提前比例",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "enableTimeTransferOnTimeNum",
            label: "启用时间转资及时数",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: 'operate',
            label: '操作',
            width: 100,
            formatter: (row) => {
              return ( <el-button type = "text"
                onClick = {
                  () => {
                    this.detail(row)
                  }
                } >
                查看 </el-button>
              )
            }
          }
        ],
        stationary: null, //静态数据
      }
    },
    created() {
      this.initial()
    },

    methods: {
      detail(row) {
        console.log("row",row)
        this.$router.push({
          path: '/pccw_menu/pccw_workflow/task_transfer/warning/details',
          query: {
            auditCityKey: row.city,
            auditSummaryKey :this.staticSearchParam.auditSummaryKey
          }
        })
      },
      async initial () {
        this.staticSearchParam.auditCityKey =this.searchParam.city
        this.staticSearchParam.auditSummaryKey=this.searchParam.auditSummaryKey
        console.log("bbb", this.staticSearchParam);
        const res=await summaryNotTimeAlertList(this.staticSearchParam)
        console.log("res", res);
        this.stationary  =res.data.tableData

      },

      async downloadData() {
        let res = await exportNotTimeListAlert(qs.stringify(this.staticSearchParam))
        console.log("res", res);
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', '任务转资提前滞后率情况统计列表.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
    }
  }
</script>
