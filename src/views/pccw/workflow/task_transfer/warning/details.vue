<template>
  <div class="app-container">

    <mssCard title="任务转资提前滞后率情况统计明细">
      <div slot="headerBtn">
      <el-button @click="$router.go(-1)">返回</el-button>
            </div>
      <div slot="content">
        <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam" border
          selection>
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import {
    summaryNotTimeAlertDetailList
  } from "@/api/pccw/report_select/task_transfer/index.js";

  export default {
    components: {

    },
    data() {
      return {
        // 默认表格搜索条件
        staticSearchParam: {
          "auditSummaryKey": "", //必传，预警url获取
          "auditCityKey": "" //必传，表格中获取
        },
        // 表头
        tableHeader: [{
          prop: "companyName",
            label: "公司名称"
          },
          {
            prop: "transferType",
            label: "类型"
          },
          {
            prop: "assetBook",
            label: "资产账簿"
          },
          {
            prop: "projectCode",
            label: "项目编号",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "constructUnit",
            label: "建设单位",
            align: "center",
            tooltip: true,
          },
          {
            prop: "engineeringImplementationManagerMain",
            label: "工程实施经理（主）"
          },
          {
            prop: "engineeringManagementManagerMain",
            label: "工程管理经理（主）"
          },
          {
            prop: "transferAdmin",
            label: "转资管理员"
          },
          {
            label: "任务编号",
            prop: "taskCode",
            align: "center",
            tooltip: true
          },
          {
            label: "任务描述",
            prop: "taskName",
            align: "center",
            tooltip: true
          },
          {
            label: "资产编号",
            prop: "assetId",
            align: "center",
            tooltip: true
          },
          {
            prop: "creationDate",
            label: "资产创建时间"
          },
          {
            prop: "activationDate",
            label: "资产启用时间"
          },
          {
            prop: "completionDate",
            label: "任务完工时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "completionDate",
            label: "任务完工时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "maintenanceEndDate",
            label: "交维结束时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "taskDeadline",
            label: "任务转资截止时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "creationDateAdvancedOrDelayed1",
            label: "转资提前/超期（创建时间）;负值提前,正值超期"
          },
          {
            prop: "activationDateAdvancedOrDelayed1",
            label: "转资提前/超期（启用时间）;负值提前,正值超期"
          },
          {
            prop: "transferConfigTime",
            label: "允许完工转资配置时间"
          },
          {
            prop: "taskCreationTime",
            label: "任务创建时间"
          },
          {
            prop: "transferRemark",
            label: "提前转资备注"
          }
        ],
        // 表格数据API
        tableApi: summaryNotTimeAlertDetailList,
      };
    },
    created() {
      const query = this.$route.query
      this.staticSearchParam.auditCityKey=query.auditCityKey
      this.staticSearchParam.auditSummaryKey=query.auditSummaryKey
    },
    methods: {
      downloadFile() {

      }
    }
  };
</script>


<style lang="scss" scoped>

</style>
