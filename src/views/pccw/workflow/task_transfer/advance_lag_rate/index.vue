<template>
  <div>
    <div slot="content">
      <!--查询框 -->
      <mssSearchForm ref="searchForm" :form="staticSearchParam" :search-config="searchConfig" @reset="reset"
        @changeSelect="changeSelect" @search="search"></mssSearchForm>
      <mssCard title="任务转资提前滞后率统计报表">
        <div slot="headerBtn">
          <el-button type="primary" @click="downloadData('exportSummary')">导出</el-button>
          <el-button type="primary" @click="downloadData('exportAll')">一键导出</el-button>
        </div>
        <div slot="content">
          <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam"
            :stationary="stationary" border :pagination='false'>
          </mssTable>
        </div>
      </mssCard>
    </div>
    </flow-detail>
  </div>
</template>

<script>
  import qs from 'qs';
  import {
    summaryNotTimeList,
    exportNotTimeList
  } from "@/api/pccw/report_select/task_transfer/index.js";
  import {
    getCityList
  } from '@/api/pccw/report_select/quarter_transfer_task.js'
  export default {
    components: {

    },
    data() {
      return {
        stationary: null,
        //搜索字段配置
        searchConfig: [
          //地市选择框
          {
            label: '地市',
            type: 'select',
            fieldName: 'businessEntity',
            itemAs: true,
            options: []
          },
          //月选择框
          {
            type: 'cycleDate',
            dateType: 'month',
            label: '月度',
            format: 'yyyy-MM',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            fieldName: 'startDate',
            clearable: false,
          },
        ],
        // 默认表格、搜索框查询条件
        staticSearchParam: {
          startDate: null,
        },
        // 表头
        tableHeader: [{
            prop: "city",
            label: "地市",
            align: "center",
            tooltip: true,
          },
          {
            prop: "completedAssetNum",
            label: "已完工转资资产数",
            align: "center",
            tooltip: true,
          },
          {
            prop: "createTimeTransferLateNum",
            label: "创建时间转资滞后数",
            align: "center",
            tooltip: true,
          },
          {
            prop: "createTimeTransferLateRate",
            label: "创建时间转资滞后比例",
            align: "center",
            tooltip: true,
            formatter: (row) => {
              return (
              <span>
                {row.createTimeTransferLateRate}%
              </span>
              )
            }
          },
          {
            prop: "createTimeTransferEarlyNum",
            label: "创建时间转资提前数",
            align: "center",
            tooltip: true,
          },
          {
            prop: "createTimeTransferEarlyRate",
            label: "创建时间转资提前比例",
            align: "center",
            tooltip: true,
            formatter: (row) => {
              return (
              <span>
                {row.createTimeTransferEarlyRate}%
              </span>
              )
            }
          },
          {
            prop: "createTimeTransferOnTimeNum",
            label: "创建时间转资及时数",
            align: "center",
            tooltip: true,
          },

          {
            prop: "enableTimeTransferLateNum",
            label: "启用时间转资滞后数",
            align: "center",
            tooltip: true,
          },
          {
            prop: "enableTimeTransferLateRate",
            label: "启用时间转资滞后比例",
            align: "center",
            tooltip: true,
            formatter: (row) => {
              return (
              <span>
                {row.enableTimeTransferLateRate}%
              </span>
              )
            }
          },
          {
            prop: "enableTimeTransferEarlyNum",
            label: "启用时间转资提前数",
            align: "center",
            tooltip: true,
          },
          {
            prop: "enableTimeTransferEarlyRate",
            label: "启用时间转资提前比例",
            align: "center",
            tooltip: true,
            formatter: (row) => {
              return (
              <span>
                {row.enableTimeTransferEarlyRate}%
              </span>
              )
            }
          },
          {
            prop: "enableTimeTransferOnTimeNum",
            label: "启用时间转资及时数",
            align: "center",
            tooltip: true,
          },
        ],
        // 表格数据API
        tableApi: summaryNotTimeList,
      };
    },
    created() {
      // 查询地市字典，parentId为地市父类 ID，index为地市选择框在 searchConfig 中的索引
      this.getAreaList('-2', 0)
      this.handleQueryParameters() // 处理初始搜索条
    },
    methods: {
      changeSelect(name, val) {
        console.log('点击改变：', name, val)
      },
      getAreaList(parentId, index) {
        getCityList().then(res => {
          console.log("城市列表3", res.data)
          if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
              list.push(
                item.name
              )
            })
            this.$set(this.searchConfig[index], 'options', list)
          }
        })
      },
      async downloadData(type) {
        let exportParams = {
          ...this.staticSearchParam
        };
        exportParams.exportType = type;

        // 创建一个新的空白页
        const newWindow = window.open('about:blank');
         newWindow.document.write('<p>下载中，请稍等...</p>');

        try {
          // 发送下载请求
          let res = await exportNotTimeList(qs.stringify(exportParams));

          // 创建下载链接
          const url = window.URL.createObjectURL(new Blob([res.data]));
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', '任务转资提前滞后率统计表.xlsx');

          // 将下载链接添加到新窗口中
          newWindow.document.body.appendChild(link);
          link.click();
          // 清理资源
          window.URL.revokeObjectURL(url);
          newWindow.document.body.removeChild(link);
        } catch (error) {
          console.error('下载失败：', error);
          newWindow.document.write('<p>下载失败，请重试。</p>');
          } finally {
            // 关闭新窗口
            newWindow.close();
          }
      },
      // 获取上个月的最后一天
      getLastDayOfPreviousMonth() {
        // 获取当前日期的 UTC 时间
        const currentDate = new Date();
        currentDate.setUTCHours(0, 0, 0, 0);
        // 将日期设置为本月的第一天
        currentDate.setUTCDate(1);
        // 将日期减去一天，即为上个月的最后一天
        currentDate.setUTCDate(0);
        // 设置时间为 "23:59:59"
        currentDate.setUTCHours(23, 59, 59, 999);
        // 格式化为日期字符串并返回
        return currentDate.toLocaleString('sv-SE', {
          timeZone: 'UTC'
        }).replace(' kl. ', ' ');
      },
      // 转换当前日期最后一天
      getLastDayOfMonth(originalDate) {
        // 判断给定日期是否是当月的最后一天

        // 创建一个表示给定日期的 Date 对象
        var date = new Date(originalDate)
        // 获取该日期的月份
        var month = date.getMonth() + 1; // 月份是从0开始计数的，所以需要加1
        // 获取该日期的年份
        var year = date.getFullYear();
        // 获取该日期的天数
        var day = date.getDate();
        // 获取当月的总天数
        var totalDays = new Date(year, month, 0).getDate();
        // 给定日期是当月的最后一天，直接返回
        if (day === totalDays) {
          return originalDate
        }

        // 获取下个月的第一天
        date.setUTCMonth(date.getUTCMonth() + 2, 0);
        // 获取本月的最后一天
        const lastDayOfMonth = new Date(date - 1);
        // 设置时间为 "23:59:59"
        lastDayOfMonth.setUTCHours(23, 59, 59, 999);
        // 格式化为日期字符串并返回
        return lastDayOfMonth.toLocaleString('sv-SE', {
          timeZone: 'UTC'
        }).replace(' kl. ', ' ');
      },
      handleQueryParameters() {
        this.staticSearchParam.startDate = this.getLastDayOfPreviousMonth();
      },
      //重置
      reset(form) {
        console.log(form);
        this.$refs.searchForm.searchForm.startDate = this.getLastDayOfPreviousMonth();
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
      },
      //搜索
      search() {
        this.$refs.table.page.current = 1
        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        console.log("原数据", this.loadParam.startDate)

        this.loadParam.startDate = this.getLastDayOfMonth(this.loadParam.startDate);

        this.$refs.table.getTableData(this.loadParam);
      },
    }
  };
</script>


<style lang="scss" scoped>

</style>
