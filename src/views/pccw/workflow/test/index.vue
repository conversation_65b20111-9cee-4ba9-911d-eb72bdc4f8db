<template>
  <div>
    <flow-detail :flow="flow" :showTransact="false" :type="approveType">
      <div slot="content">
        <mssCard title="审批的具体表单">
          <div slot="content">
            审批的具体表单
          </div>
        </mssCard>
      </div>
    </flow-detail>
  </div>
</template>

<script>
  import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue"

  export default {
    components: {
      FlowDetail
    },
    data() {
      return {
        flow: {
          name:'', // 标题
          flowKey:'', // 流程key
          composeTime:'', // 时间
          workflowName:'', // 流程名称
          nodeName:'', // 节点名称
          procInstId:'', // 实例名称
          taskId:'' // 任务id
        },
        approveType: '',
        showTransact: false,
        businessId:''
      };
    },
    created() {
      this.approveType = decodeURIComponent(this.$route.query.type); // 获取类型信息
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow); // 获取流程对象
      this.businessId = this.flow.businessId; // 根据这个去后端查数据
    },
    methods: {

    }
  };
</script>


<style lang="scss" scoped>

</style>
