<template>
  <div class="app-container">
    <mssSearchForm
      v-show="showSearch"
      ref="searchForm"
      :form="queryOwn"
      :search-config="queryParams"
      @reset="resetQuery"
      @search="handleQuery"
    ></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn">
        * 只能跳转部分待办
        <el-button type="primary" @click="delBatch">批量删除</el-button>
        <el-button type="primary" @click="toSelectUser">用户查询</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :static-search-param="queryOwn"
          border
          selection
          :getChange="true"
          @getChange="handleGetChange"
        >
        </mssTable>
      </div>
    </mssCard>
    <el-dialog title="流程图(隐藏)" :visible.sync="dialogXML" width="80%">
      <bpmn-diagram :procInstId="procInstId" v-if="dialogXML" />
    </el-dialog>


    <!-- <div v-if="flow"> -->
     <template v-if="loadingFlag">
       <transact-dialog
        ref="transactDialog"
        :taskId="flow.taskId" />
      <approval-dialog
        ref="approvalDialog"
        :taskId="flow.taskId"
        :flowName="flow.flowName"
        :taskName="flow.taskName"
      />

    </template>
    <!-- </div> -->
    <select-user ref="selectUserDialog"/>
  </div>
</template>

<script>
    import SelectUser from './SelectUser2.vue';
  import { navigateRouter } from "@/views/pccw/workflow/router_setting/review.js"
  import ApprovalDialog from "./ApprovalDialog.vue"
  import TransactDialog from "./TransactDialog.vue"
  // import ApprovalDialog from "@/views/pccw/workflow/flowDetail/ApprovalDialog.vue"
  import { deleteflows,revoke } from "@/api/pccw/workflow/index.js"
  import { queryMyList } from "@/api/pccw/workflow/march.js"
  import { getToken } from "@/utils/auth"
  import BpmnDiagram from "@/views/pccw/workflow/common/BpmnDiagram.vue"
  export default {
    components: {
      BpmnDiagram,
      ApprovalDialog,
      TransactDialog,
      SelectUser
    },
    data() {
      return {
        loadingFlag: false, // 数据加载状态
        flow: null,
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [
          {
            label: "流程标题",
            type: "input",
            fieldName: "title"
          },
          {
            label: "流程名称",
            type: "input",
            fieldName: "flowName"
          },
          {
            label: "当前审批人id",
            type: "input",
            fieldName: "curUserId"
          },
          {
            label: "发起时间",
            type: "input",
            fieldName: "createTime"
          },
          {
            label: "起始人id",
            type: "input",
            fieldName: "startUserId"
          },
          {
            label: "流程实例id",
            type: "input",
            fieldName: "procInstId"
          },
          {
            label: "业务id",
            type: "input",
            fieldName: "businessId"
          }
        ],
        procInstId: "",
        // 查询条件 - 我提交的
        queryOwn: {
          // total: '',
          // workType: '',
          // current: 1,
          // size: 6,
          condition: {
            title: "",
            flowName: "",
            suspensionState: "",
            receiveStartTime: "",
            receiveEndTime: "",
            tenantId: "0",
            roleIds: [],
            deptId: ""
          }
        },
        // 表头
        tableHeader: [
          {
            prop: "title",
            label: "标题",
            align: "center",
            tooltip: true,
            width: 180,
            formatter: row => {
              return (
                <span
                  class="table_cell_click"
                  onclick={() => {
                    this.toDetail(row)
                  }}
                >
                  {row.title}
                </span>
              )
            }
          },
          {
            prop: "businessId",
            label: "业务id",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "flowName",
            label: "流程名称",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "curUserId",
            label: "当前审批人id",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "startUserId",
            label: "起始人id",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "createTime",
            label: "发起时间",
            align: "center",
            tooltip: true,
            width: 220
          },
          {
            prop: "taskName",
            label: "当前节点",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "procDefId",
            label: "流程定义id",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "procInstId",
            label: "流程实例id",
            align: "center",
            tooltip: true,
            width: 180
          },
          {
            prop: "suspensionState",
            label: "挂起状态",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "delegationState",
            label: "委派状态",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "flowState",
            label: "流程状态",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: "流程图",
            align: "center",
            fixed: "right",
            width: 80,
            minWidth: "50px",
            formatter: row => {
              return (
                <span>
                  {" "}
                  {
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.getFlowXml(row)
                      }}
                    >
                      查看{" "}
                    </span>
                  }{" "}
                </span>
              )
            }
          },
          {
            label: "审批",
            align: "center",
            fixed: "right",
            width: 80,
            minWidth: "50px",
            formatter: row => {
              return (
              <span class="table_btn mr10">
                {
                  <div
                    onClick={() => {
                      this.openApprovalDialog(row)
                    }}
                  >
                    审批
                  </div>
                }
                {
                  <div
                    onClick={() => {
                      this.openTransactDialog(row)
                    }}
                  >
                    转办
                  </div>
                }
              </span>
              )
            }
          },
          {
            label: "取回",
            align: "center",
            fixed: "right",
            width: 80,
            minWidth: "50px",
            formatter: row => {
              return (
                <span>
                  {" "}
                  {
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.revokeRow(row)
                      }}
                    >
                      取回{" "}
                    </span>
                  }{" "}
                </span>
              )
            }
          },
          {
            label: "删除",
            align: "center",
            fixed: "right",
            width: 80,
            minWidth: "50px",
            formatter: row => {
              return (
                <span>
                  {" "}
                  {
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.delete(row, 1)
                      }}
                    >
                      删除{" "}
                    </span>
                  }{" "}
                </span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: queryMyList,
        selectionList: []
      }
    },
    created() {},
    methods: {
      toSelectUser() {
        this.$refs.selectUserDialog.selectUserDialog = true;
      },
      delBatch() {
        let flowList = this.selectionList.map(item => {
          return {
            ...item,
            userId: item.startUserId
          }
        })
        this.delete(flowList, 2)
      },
      handleGetChange(selection) {
        this.selectionList = selection
      },
      revokeRow(row) {
        this.$confirm("此操作将取回流程, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(async () => {
            console.log(row)
            let q={
              userId:row.startUserId,
              procInstId:row.procInstId
            }
            let res = await revoke(q) // 等待异步操作完成
            if (res.code === "0000") {
              this.$message.success(res.data)
              // 查询刷新
              this.$refs.table.getTableData()
            } else {
              this.$message.error("操作失败！")
            }
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消取回"
            })
          })
      },
      delete(row, type) {
        this.$confirm("此操作将永久删除, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(async () => {
            console.log(row)
            let deleteFlowRoList = []
            if (type == 1) {
              row.userId = row.startUserId
              deleteFlowRoList.push(row)
            } else {
              deleteFlowRoList = row
            }
            let res = await deleteflows(deleteFlowRoList) // 等待异步操作完成
            if (res.code === "0000") {
              this.$message.success(res.data)
              // 查询刷新
              this.$refs.table.getTableData()
            } else {
              this.$message.error("操作失败！")
            }
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除"
            })
          })
      },
      submitDelete(row) {},
      openApprovalDialog(row) {
        // 审批任务
        console.log(row)
        this.$refs.approvalDialog.taskId=row.taskId
        this.flow = row
        if(this.flow){
          this.loadingFlag = true
          if(this.loadingFlag){
            console.log(this.$refs.approvalDialog)
            this.$refs.approvalDialog.approvalDialog = true
            this.$refs.approvalDialog.approvalRo.userId = row.curUserId
          }
        }
      },
      openTransactDialog(row) {
        // 转办任务
        console.log(row)
        this.flow = row
        if(this.flow){
          this.loadingFlag = true
          this.$refs.transactDialog.taskId=row.taskId
          this.$refs.transactDialog.transactDialog = true
          this.$refs.transactDialog.transferRo.userId = row.curUserId
        }
      },
      // 点击任务名称
      toDetail(row) {

        console.log(row)
        // 获取flowkey
        let [_, flowKey] = row.procDefId.match(/^([^:]+)/) || [];
        let newRow={
          name:row.title,
          workflowName:row.flowName,
          nodeName:row.taskName,
          taskId:row.taskId,
          businessId:row.businessId,
          procInstId:row.procInstId,
          beginTime:row.creatTime,
          flowKey:flowKey
        }
        // const data = JSON.stringify(newRow);
        // this.$router.push({
        //   path: "/pccw_menu/workflow/review/video_check/check",
        //   query: {
        //     flow: data,
        //     type: "todo"
        //   }
        // });
        // const data = encodeURIComponent(JSON.stringify(newRow))
        // 转为一期字段
        navigateRouter(newRow, "todo") // 调用引入的方法
      },
      // 查询流程图
      getFlowXml(row) {
        this.procInstId = row.procInstId
        this.dialogXML = true
      },
      /** 搜索按钮操作 */
      handleQuery(form) {
        console.log("form", form)
        this.queryOwn.condition.flowName = form.flowName
        this.queryOwn.condition.title = form.title
        // this.queryOwn = {
        //   ...form
        // }
        this.$refs.table.page.current = 1
        this.queryOwn = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .table_cell_click {
    cursor: pointer;
    color: #02a7f0;

    .el-icon-data-analysis {
      font-size: 14px;
    }
  }
</style>
