<template>
  <div>
    <el-dialog title="转办" :visible.sync="transactDialog" class="transactDialog" :modal-append-to-body="false">
      <el-form :label-position="labelPosition" label-width="100px" :model="transferRo">
        <el-form-item label="转办说明" class="approvalOpinion">
          <el-input type="textarea" v-model="transferRo.transferReason" :rows="2" resize='none'
            class="comments"></el-input>
        </el-form-item>
        <el-form-item label="转办给" class="sendMan">
          <el-button @click="toSelectUser">选择</el-button>
          <div v-if="selectUser">
            用户：{{selectUser.nickName}}
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="consentTransact()">确定</el-button>
        <el-button @click="transactDialog = false">取消</el-button>
      </span>
      <select-user ref="selectUserDialog" @row-selected="handleRowSelected" />
    </el-dialog>
  </div>
</template>

<script>
  import {
    transferTask
  } from "@/api/pccw/workflow/index.js";
  import SelectUser from './SelectUser.vue';
  export default {
    props: ['taskId'],
    components: {
      SelectUser
    },
    data() {
      return {
        selectUserDialog: false,
        transactDialog: false,
        labelPosition: 'top',
        // 转办对象
        transferRo: {
          taskId: "", // 任务id
          assignee: "", // 转办人
          transferReason: "", // 转办说明
          userId: "",
          tenantId: ""
        },
        selectUser: {} // 选择的用户信息
      };
    },
    methods: {
      // 处理子组件传值
      handleRowSelected(row) {
        this.selectUser = row
      },
      // 处理确定按钮点击事件
      async consentTransact() {
        this.transferRo.assignee = this.selectUser.userId
        this.transferRo.taskId = this.taskId
        const res = await transferTask(this.transferRo)
        if (res.code === '0000') {
          this.$message({ // 提示
            type: 'success',
            message: '转办成功'
          });
          // 跳转页面并刷新
          // this.$router.push({
          //   path: '/home'
          // }).then(() => {
          //   this.$router.go(0); // 刷新当前页面
          // });
        }
      },
      toSelectUser() {
        this.$refs.selectUserDialog.selectUserDialog = true;
      }
    }
  };
</script>

<style scoped>
  /* 样式 */
</style>
