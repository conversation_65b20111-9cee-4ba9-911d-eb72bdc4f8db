<template>
  <div class="custom-dialog">
    <el-dialog title="审批" :visible.sync="approvalDialog" class="dialog" :modal-append-to-body="false">
      <el-form :label-position="labelPosition" label-width="100px" :model="approvalRo">
        <div v-if="nicknames">
          <el-form-item label="下一节点审批人" class="approvalOpinion">
            {{nicknames}}
          </el-form-item>
        </div>
        <el-form-item label="指定下一节点审批人" class="sendMan" v-if="approvals&&nextApprovers">
          <el-button @click="toSelectUser">选择</el-button>
          <div v-if="selectUser">
            当前审批人：{{selectUser.nickName}}
          </div>
        </el-form-item>

        <el-form-item label="审批意见" class="approvalOpinion">
          <el-input type="textarea" v-model="approvalRo.approvalComments" :rows="5" resize='none'
            class="comments"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="consentApproval()">确定</el-button>
        <el-button @click="closed()">取消</el-button>
      </span>
      <select-user ref="selectUserDialog" @row-selected="handleRowSelected" />
    </el-dialog>
  </div>
</template>

<script>
  import {
    approveTask,
    getNextNodeApproverNames
  } from "@/api/pccw/workflow/index.js";
  import SelectUser from './SelectUser.vue';
  export default {
    props: {
      taskId: {
        type: String,
        required: true
      },
      // 用于接收指定审批人列表
      nextApprovers: {
        type: Array,
        default: () => []
      },
      approvals: { //指定审批人
        type: Boolean,
        default: false
      },
    },
    components: {
      SelectUser
    },
    data() {
      return {
        selectUser: {}, // 选择的用户信息
        nicknames: null,
        impManger: '', // 实施经理id
        visManger: '', // 监工经理id
        approvalDialog: false,
        labelPosition: 'top',
        // 审批对象
        approvalRo: {
          nextTaskUsers: [], // 指定下一节点审批人
          taskId: "",
          taskIds: [],
          approvalComments: "", // 审批意见
          taskKey: "",
          userId: "",
          copyUserList: [],
          approveType: "", // 审批操作:Y同意N不同意
          returnType: 0,
          targetTaskKey: "",
          variables: {},
          userId: "",
          tenantId: "0"
        },
      };
    },
    created() {
      this.getNames()
    },
    methods: {
      toSelectUser() {
        this.$refs.selectUserDialog.selectUserDialog = true;
      },
      // 处理子组件传值
      handleRowSelected(row) {
        // console.log("row",row)
        this.selectUser = row
        // 选了人才赋值，不然还是维持原本的
        if (this.approvals && this.nextApprovers.length > 0) {
          this.nextApprovers[0].userId = row.userId
          this.approvalRo.nextTaskUsers = JSON.parse(JSON.stringify(this.nextApprovers))
        }
      },
      async getNames() {
        const res = await getNextNodeApproverNames(this.taskId)
        let data = res.data
        let nicknameList = [];
        if(data){
          for (let i = 0; i < data.length; i++) {
            let nickname = data[i];
            // 去除括号和逗号
            if (nickname != null && typeof nickname === 'string') {
            nickname = nickname.replace(/[\[\],]/g, "");
            nicknameList.push(nickname);
            }
          }
          // 将昵称列表转换为以逗号分隔的字符串
          this.nicknames = nicknameList.join(",");
        }

      },
      closed() {
        this.approvalRo.approvalComments = ""
        this.approvalDialog = false
      },
      // 确认审批
      async consentApproval() {
        this.approvalRo.approveType = "Y"
        this.approvalRo.taskId = this.taskId

        // 目前只适用于竣工，最后一个节点不存在审批人
        if (!this.nicknames &&this.approvals&& this.approvalRo.nextTaskUsers.length < 1) {
          this.$message.success('请先选择审批人')
          return
        }

        const res = await approveTask(this.approvalRo);
        this.approvalDialog = false;
        if (res.code == "0000") {
          this.$message.success('审批成功')
          // 跳转页面并刷新
          this.$router.push({
            path: '/home'
          }).then(() => {
            this.$router.go(0); // 刷新当前页面
          });
        }
      },
    }
  };
</script>

<style scoped>

</style>
