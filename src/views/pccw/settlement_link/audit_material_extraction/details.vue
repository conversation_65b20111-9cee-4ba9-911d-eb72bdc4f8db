<template>
  <div class="app-container">
    <mssCard title="送审附件明细">
      <div slot="content">
        <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam" border
          selection>
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import {
    getFinaceList,
    updateFinace
  } from "@/api/pccw/report_select/audit_statement_extraction.js";

  export default {
    components: {

    },
    data() {
      return {
        // 默认表格搜索条件
        staticSearchParam: {},
        // 表头
        tableHeader: [{
            prop: "fileName",
            label: "系统来源",
            align: "center",
            tooltip: true,
          },
          {
            prop: "fileName",
            label: "文件名",
            align: "center",
            tooltip: true,
          },
          {
            prop: "fileNumber",
            label: "状态",
            align: "center",
            tooltip: true,
          },
          {
            prop: "fileNumber",
            label: "下载时间",
            align: "center",
            tooltip: true,
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '100px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.downloadFile(row)
                    }
                  } >
                  下载 <
                  /span>
                } <
                /span>
              )
            }
          },
        ],
        // 表格数据API
        tableApi: getFinaceList,
      };
    },
    created() {

    },
    methods: {
      downloadFile() {

      }
    }
  };
</script>


<style lang="scss" scoped>

</style>
