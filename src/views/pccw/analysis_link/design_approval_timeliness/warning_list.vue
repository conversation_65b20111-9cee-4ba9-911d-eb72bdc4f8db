<!-- 设计预警 -->
<template>
  <div class="design_warning">
    <mssCard title="设计批复及时性预警">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="del">删除</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
          </mssTable>
        </el-form>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  queryListService,
  delService,
  saveService,
  saveAllService,
} from "@/api/design_link/warning_api.js";
import { commonMultDel, commonOneDel } from "@/utils/btn";
export default {
  name: "design_warning",
  data() {
    return {
      tableData: [],
      levelNoList: [
        { label: "一级", value: "1" },
        { label: "二级", value: "2" },
        { label: "三级", value: "3" },
        { label: "四级", value: "4" },
        { label: "五级", value: "5" },
      ],
      warnTypeList: [
        { label: "督办任务", value: "1" },
        { label: "短信", value: "2" },
      ],
      warnObjectList: [
        { label: "设计单位", value: "1" },
        { label: "项目经理", value: "2" },
      ],
      enableStatusList: [
        { label: "启用", value: "1" },
        { label: "停用", value: "0" },
      ],
      form: {},
      rules: {},
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "levelNo",
            label: "预警级别",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`levelNo${index}`}>
                  <el-select
                    v-model={row.levelNo}
                    onChange={(value) => {
                      this.form[`levelNo${index}`] = value;
                    }}
                  >
                    {this.levelNoList.length &&
                      this.levelNoList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.value} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            prop: "entityCreateDate",
            label: "预警时间门限设置",
            minWidth: 360,
            formatter: (row, column, cellValue, index) => {
              return (
                <div>
                  <span>系统当前时间</span>
                  <span>-</span>
                  <span>设计派工开始时间</span>
                  <span>{">"}</span>
                  <el-form-item prop={`entityCreateDate${index}`}>
                    <el-input-number
                      style="width:100px"
                      v-model={row.entityCreateDate}
                      onChange={(value) => {
                        this.form[`entityCreateDate${index}`] = value;
                      }}
                      controls-position="right"
                    />
                  </el-form-item>
                  <span>天</span>
                </div>
              );
            },
          },
          {
            prop: "warnType",
            label: "预警方式",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`warnType${index}`}>
                  <el-select
                    v-model={row.warnType}
                    onChange={(value) => {
                      this.form[`warnType${index}`] = value;
                    }}
                  >
                    {this.warnTypeList.length &&
                      this.warnTypeList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.value} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            prop: "warnDesc",
            label: "预警内容",
            minWidth: 240,
          },
          {
            prop: "warnObject",
            label: "预警对象",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`warnObject${index}`}>
                  <el-select
                    v-model={row.warnObject}
                    multiple
                    collapse-tags
                    onChange={(value) => {
                      this.form[`warnObject${index}`] = value;
                    }}
                  >
                    {this.warnObjectList.length &&
                      this.warnObjectList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.value} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            prop: "enableStatus",
            label: "规则状态",
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`enableStatus${index}`}>
                  <el-select
                    v-model={row.enableStatus}
                    onChange={(value) => {
                      this.form[`enableStatus${index}`] = value;
                    }}
                  >
                    {this.enableStatusList.length &&
                      this.enableStatusList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.value} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    getTable() {
      queryListService().then((res) => {
        if (res.code == "0000") {
          res.data.forEach((item) => {
            if (item.warnObject)
              item.warnObject = item.warnObject.split(",") || [];
          });
          this.tableData = res.data || [];
        }
      });
    },
    addTable() {
      if (this.tableData.length < 10) {
        let data = {
          levelNo: "",
          entityCreateDate: "",
          warnType: "",
          warnDesc: "任务工单名称+已超时，请及时处理！",
          warnObject: "",
          enableStatus: "",
        };
        saveService(data).then((res) => {
          if (res.code == "0000") {
            this.tableData.push({ ...data, id: res.data });
          }
        });
      } else {
        this.$message.warning("最多只能有10种数据");
      }
    },
    deleteTable(row) {
      commonOneDel.call(this, row.id, delService, (res) => {
        this.$message.success("删除成功");
        this.getTable();
      });
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            if (this.validateCopy(row)) {
              this.commondealName(row)
              let obj = JSON.parse(JSON.stringify(row));
              if (obj.warnObject) obj.warnObject = obj.warnObject.join(",");
              saveService(obj).then((res) => {
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
            } else {
              this.$message.warning(`相同预警级别、相同预警方式只能有一条数据，请修改数据`);
            }
          } else {
            return;
          }
        });
      });
    },
    save() {
      this.commonsetRule(this.tableData);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            if (this.validateAll()) {
              let arr = JSON.parse(JSON.stringify(this.tableData));
              arr.forEach((item) => {
                this.commondealName(item)
                if (item.warnObject && item.warnObject.length) {
                  item.warnObject = item.warnObject.join(",");
                }
              });
              saveAllService(arr).then((res) => {
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
            } else {
              this.$message.warning(`相同预警级别、相同预警方式只能有一条数据，请修改数据`);
            }
          } else {
            return;
          }
        });
      });
    },

    // 删除
    del() {
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: delService,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },

    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "enableStatus",
        "entityCreateDate",
        "levelNo",
        "warnObject",
        "warnType",
      ];
      a.forEach((item) => {
        if (index) {
          this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          for (let index in arr) {
            this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
            this.rules[`${item}${index}`] = {
              required: true,
              message: "该字段不能为空",
              trigger: ["blur", "change"],
            };
          }
        }
      });
    },


    // 校验当前数据是否有存在的情况
    validateCopy(row) {
      let n = 0;
      this.tableData.forEach((item) => {
        if (item.levelNo && item.levelNo == row.levelNo&&item.warnType==row.warnType) {
          n++;
        }
      });
      return n < 2;
    },

    // 校验所有的数据
    validateAll() {
      // let arr=[]
      // this.tableData.forEach(item=>{
      //   if(arr.indexOf(`${item.levelNo}-${item.warnType}`)<0){
      //     arr.push(`${item.levelNo}-${item.warnType}`)
      //   }
      // })
      // return arr.length==this.tableData.length
      const obj = {}
      for (let i = 0; i < this.tableData.length; i++) {
        const current = this.tableData[i]
        if (obj[`${current.levelNo}_${current.warnType}`]) {
          return false
        }
        obj[`${current.levelNo}_${current.warnType}`] = true
      }
      return true
    },
    // 处理其中文字
    commondealName(row){
      let a = [
        "enableStatus",
        "levelNo",
        "warnType",
      ]
      a.forEach(item=>{
        row[`${item}Name`]=this[`${item}List`].filter((p)=>{return p.value==row[item]})[0].label
      })
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


