<template>
    <div>
        <mssCard title="送审附件清单">
          <div slot="content">
            <mssTable
              ref="table1"
              :api="tableApi"
              :columns="tableHeader1"
              :staticSearchParam="loadParam"
              border
            >
            </mssTable>
          </div>
        </mssCard>
        <el-dialog
          title="送审附件明细"
          :visible.sync="dialogVisible"
          :close-on-click-modal='false' 
          @close='onClose'
          width="85%">
          <mssCard title="送审附件明细">
            <div slot="content">
              <mssTable
                ref="table2"
                :stationary="tableData"
                :columns="tableHeader2"
                :pagination="false"
                :serial="false"
              >
              </mssTable>
            </div>
          </mssCard>
        </el-dialog>
    </div>
</template>

<script>

import { commonDown } from '@/utils/btn'
import { getReviewAttachmentList, getReviewAttachmentDetail, downloadZip, downloadFile } from '@/api/pccw/analysis_link/audit_list/api'
export default {
  name: 'Detail',
  data() {
    return {
        loadParam:{},
        tableApi: getReviewAttachmentList,
        // 表头
        tableHeader1: [
          {
            prop: "fileName",
            label: "文件名",
            align: "center",
            tooltip: true,
          },
          {
            prop: "",
            label: "操作",
            align: "center",
            tooltip: true,
            formatter: (row) => {
              return (
                <span
                    class='table_btn'
                    onClick={() => { this.exportList1(row) }}
                  >
                  导出
                </span>
              )
            }
          },
          {
            prop: "fileNum",
            label: "文件数量",
            align: "center",
            tooltip: true,
          },
          {
            prop: "",
            label: "明细",
            align: "center",
            tooltip: true,
            formatter: (row) => {
              return (
                <span
                    class='table_btn'
                    onClick={() => { this.getDetailed(row) }}
                  >
                  查看
                </span>
              )
            }
          },
        ],
        tableHeader2: [
          {
            prop: "fileSource",
            label: "系统来源",
            align: "center",
            tooltip: true,
          },
          {
            prop: "fileName",
            label: "文件名",
            align: "center",
            tooltip: true,
          },
          {
            prop: "status",
            label: "状态",
            align: "center",
            tooltip: true,
          },
           {
            prop: "downloadTime",
            label: "下载时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "",
            label: "操作",
            align: "center",
            tooltip: true,
            formatter: (row) => {
              return (
                <span
                    class='table_btn'
                    onClick={() => { this.exportList2(row) }}
                  >
                  下载
                </span>
              )
            }
          },
        ],
        tableData: [],
        listId: '',
        dialogVisible: false     
    };
  },
  methods: {
    getDetailed (row) {
      // this.tableData = []
      this.listId = row.id
      this.detailList()
      this.dialogVisible = true
    },
    detailList () {
      let data = {
        listId: this.listId,
        limit: 9999,
        page: 1
      }
      getReviewAttachmentDetail(data).then((res)=>{
          if (res && res.code == '0000' && res.data) {
            if (res.data.data) {
              this.tableData =  res.data.data
            } else if (res.data) {
              this.tableData = res.data
            }
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: res.msg
            })
            this.tableData = []
          }
      })
    },
    open () {
      this.tableData = []
      this.dialogVisible = true
    },
    onClose () {
      this.dialogVisible = false
    },
    exportList1(row) {
       const params = {
        id: row.id,
        fileName: row.fileName
      }
      commonDown(params, downloadZip)
    },
    exportList2(row) {
       const params = {
        id: row.id,
        minioFileName: row.minioFileName,
        fileName: row.fileName
      }
      commonDown(params, downloadFile)
    }
  },
  mounted() {
      // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your component-specific styles go here */
</style>
