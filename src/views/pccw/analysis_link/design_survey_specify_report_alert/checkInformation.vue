<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
            <div slot="headerBtn">
                <el-upload
                  style="display: inline-block;margin-left: 10px;"
                  ref="newFile1"
                  class="upload-btn"
                  action="string"
                  :show-file-list="false"
                  :auto-upload="true"
                  :http-request="importFile1"
                >
                  <el-button type="primary">导入打卡数据</el-button>
                </el-upload>
                <el-upload
                  style="display: inline-block;margin:0 10px;"
                  ref="newFile2"
                  class="upload-btn"
                  action="string"
                  :show-file-list="false"
                  :auto-upload="true"
                  :http-request="importFile2"
                >
                  <el-button type="primary">设计院导入打卡数据</el-button>
                </el-upload>
                <el-button type="primary" @click="exportHandle(1)">导出打卡信息模板</el-button>
                <el-button type="primary" @click="exportHandle(2)">导出打卡数据</el-button>
              <el-button type="primary" @click="delBatch" style="margin-left: 10px;">批量删除</el-button>
            </div>
            <div slot="content">
                <mssTable
                  ref="table"
                  :api="tableApi"
                  :columns="tableHeader"
                  selection
                  :staticSearchParam="loadParam"
                  :getChange="true"
                  @getChange="handleGetChange"
                >
                </mssTable>
            </div>
        </mssCard>
        <ImportErrorMessage ref="importErrorMessage"></ImportErrorMessage>
    </div>
</template>

<script>

// 获取地市:queryAreaListService
import {queryAreaListService} from '@/api/common_api.js'
import {
  queryThirdClockData,
  exportClockTemp,
  exportClockInData,
  importClockInData,
  importThirdData,
  delBatchClock
} from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
import { commonDown } from '@/utils/btn'
import moment from "moment";
import ImportErrorMessage from '@/views/pccw/components/importErrorMessage/index'
export default {
name: 'CityList',
data() {
  return {
    tableApi: queryThirdClockData,
    loadParam: {},  // 用于存储搜索条件
    //搜索字段配置
    gridData:[],
    searchConfig: [
      {
        label: '地区',
        type: 'input',
        fieldName: 'area',
      },
      {
        label: '签到人',
        type: 'input',
        fieldName: 'signee',
      },
      {
        label: '签到时间',
        type: "daterange",
        fieldName: "date",
        format: 'yyyy-MM-dd',
        valueFormat: 'yyyy-MM-dd'
      },
      {
        label: '打卡类型',
        type: 'select',
        fieldName: 'clockinType',
        options: [
          {
            label: '钉钉打卡',
            value: '钉钉打卡'
          },
          {
            label: '智勘平台打卡',
            value: '智勘平台打卡'
          }
        ]
      },
      // {
      //   label: '状态',
      //   type: 'select',
      //   fieldName: 'status',
      //   options: [
      //     {
      //       label: '设计单位人员',
      //       value: '1'
      //     },
      //     {
      //       label: '移动人员',
      //       value: '2'
      //     },
      //     {
      //       label: '李超',
      //       value: '3'
      //     },
      //     {
      //       label: '其他',
      //       value: '4'
      //     },
      //   ]
      // },
      // {
      //   label: '部门名称',
      //   type: 'input',
      //   fieldName: 'deptName',
      // }
    ],
    //表格头部配置
    tableHeader: [
      {
        prop: "projectCode",
        label: "项目编码",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "projectName",
        label: "项目名称",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "taskCode",
        label: "任务编码",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "taskName",
        label: "任务名称",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "siteName",
        label: "站点名称",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "province",
        label: "省份",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "area",
        label: "地区",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "signee",
        label: "签到人",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "signUnit",
        label: "签到单位",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "cooperateUnitType",
        label: "合作单位类型",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "signType",
        label: "签到类型",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "signTime",
        label: "签到时间",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "longitudeLatitude",
        label: "签到地点经纬度",
        align: "center",
        tooltip: true,
        minWidth: 150
      },
      {
        prop: "clockinType",
        label: "打卡类型",
        align: "center",
        tooltip: true,
        minWidth: 150
      }
    ],
  }
},
methods: {
    importFile1 (params) {
      const param = new FormData()
      param.append('file', params.file)
      importClockInData(param).then((res) => {
        if (res.code === '5000') {
          this.$message.warning(res.msg)
        } else if (res.code === '0000') {
          if (res.data.length > 0){
            this.$refs.importErrorMessage.open(res.data)
          } else {
            this.$message.success('导入成功')
          }
          this.$refs.table.getTableData()
        }
      })
    },
  handleGetChange(selection) {
    this.gridData = selection
  },
  delBatch() {
    let idArray = this.gridData.map(item => String(item.id));
    this.delete(idArray)
  },
  delete(row) {
    this.$confirm("此操作将永久删除, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(async () => {
        console.log(row)
        let res = await delBatchClock(row) // 等待异步操作完成
        if (res.code === "0000") {
          this.$message.success(res.data)
          // 查询刷新
          this.$refs.table.getTableData()
        } else {
          this.$message.error("操作失败！")
        }
      })
      .catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除"
        })
      })
  },
    importFile2 (params) {
      const param = new FormData()
      param.append('file', params.file)
      importThirdData(param).then((res) => {
        if (res.code === '5000') {
          this.$message.warning(res.msg)
        } else if (res.code === '0000') {
          if (res.data.length > 0){
            this.$refs.importErrorMessage.open(res.data)
          }else {
            this.$message.success('导入成功')
          }
          this.$refs.table.getTableData()
        }
      })
    },
    //导出
    exportHandle(val) {
      this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      this.loadParam.startTime = this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[0] + ' ' + '00:00:00'  : ''
      this.loadParam.endTime = this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[1] + ' ' + '23:59:59'  : ''
      // this.loadParam.startTime = ''
      // this.loadParam.endTime = ''
      // this.loadParam.date = ''
      if (val == 1) {
        commonDown({ ...this.loadParam, limit: -1}, exportClockTemp)
      } else {
        commonDown({ ...this.loadParam, limit: -1}, exportClockInData)
      }
      // this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      // this.loadParam.startTime = this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[0] + '00:00:00'  : ''
      // this.loadParam.endTime = this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[1] + '23:59:59'  : ''
      // this.loadParam.startTime = ''
      // this.loadParam.endTime = ''
      // this.loadParam.date = ''
      // commonDown({ ...this.loadParam, limit: -1}, downloadCity);
    },
    //重置
    reset(form) {
      form.startTime = ''
      form.endTime = ''
      form.city = ''
      this.search(form)
    },
    //搜索
    search() {
      this.$refs.table.page.current = 1
      this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      this.loadParam.startTime = this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[0] + ' ' + '00:00:00' : ''
      this.loadParam.endTime = this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[1] + ' ' + '23:59:59' : ''
      this.loadParam.date = ''
      this.$refs.table.getTableData(this.loadParam);
    },
},
created() {
},
components: {
  ImportErrorMessage
}
}
</script>

<style scoped>
/* Your component's CSS styles go here */
</style>
