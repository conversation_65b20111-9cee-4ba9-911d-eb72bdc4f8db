<template>
  <div>
    <flow-detail :showReturn='showReturn' :showApproval='showApproval'  :flow="flow" :showTransact="false" :type="approveType" :msgApproval="msgApproval" ref="flowDetail">
       <el-button type="primary" slot="custom-buttons" @click="savePage" v-if="$route.query.type ==='todo' && saveStatus">保存提交</el-button>
      <div slot="content">
        <mssCard :title="title">
        <div slot="content">
          <el-form ref="editform" :model="form" :rules="rules">
            <mssTable
              ref="table"
              :columns="tableHeader"
              :staticSearchParam="loadParam"
              :stationary="tableData"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
      </div>
    </flow-detail>
  </div>
</template>

<script>
import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue";
import { getClockTaskList, updateClockTaskList } from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
export default {
    name: 'check_task_list',
    components: {
      FlowDetail
    },
    data() {
        return {
          loadParam: {},  // 用于存储搜索条件
          showSummary: true,
          dialogEmpower: false,
          dialogTitle: "打卡任务清单",
          levelNoList: [
            { label: "一级", value: "1" },
            { label: "二级", value: "2" },
            { label: "三级", value: "3" },
            { label: "四级", value: "4" },
            { label: "五级", value: "5" },
          ],
          tableData: [],
          form: {},
          rules: {},
          clockinType:['PMS打卡', '智勘平台打卡', '钉钉打卡'],
          approveType: '',
          flow: {},
          saveStatus: true, //提交审批
          showApproval: false, //提交
          showReturn: false, //退回
          title: '',
          msgApproval: ''
        }
    },
    created() {
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow); // 获取流程对象
      this.approveType = decodeURIComponent(this.$route.query.type); // 获取类型信息
      this.title = decodeURIComponent(this.$route.query.title);
      if (this.approveType == 'todo') {
        if (flow.nodeName == '设计单位人员') {
          this.saveStatus = true //提交审批
          this.showApproval =  false //提交
          this.showReturn =  false //退回
          this.title = '打卡任务清单'
        } else if (flow.nodeName == '设计统筹人员') {
          this.saveStatus = false //提交审批
          this.showApproval = true //提交
          this.showReturn =  true //退回
          this.title = '补充打卡信息清单'
        }
      } else {
        if (flow.nodeName == '设计单位人员') {
          this.title = '打卡任务清单'
        } else {
          this.title = '补充打卡信息清单'
        }
      }
      if (this.approveType ==='todo' && !this.saveStatus) {
        this.msgApproval = ''
      } else {
        this.msgApproval = '请先填写信息'
      }
      this.businessId = this.flow.businessId; // 根据这个去后端查数据
      this.loadParam = {businessId: this.flow.businessId}
      this.getPower()
   },
    computed: {
      tableHeader: {
        get() {
          return [
            {
                prop: "projectCode",
                label: "项目编码",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "projectName",
                label: "项目名称",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "taskCode",
                label: "任务编码",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "taskName",
                label: "任务名称",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "siteName",
                label: "站点名称",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "province",
                label: "省份",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "area",
                label: "地区",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
              prop: "signee",
              label: "签到人",
              minWidth: 200,
              formatter: (row, column, cellValue, index) => {
                if (this.approveType ==='todo' && this.saveStatus) {
                  return (
                    <el-form-item prop={`signee${index}`}>
                      <el-input 
                        v-model={row.signee}
                      >
                      </el-input>
                    </el-form-item>
                  );
                } else {
                  return row.signee
                }
              },
            },
            {
              prop: "signUnit",
              label: "签到单位",
              minWidth: 200,
              tooltip: true,
            },
            {
              prop: "cooperateUnitType",
              label: "合作单位类型",
              minWidth: 200,
              tooltip: true,
            },
            {
              prop: "signType",
              label: "签到类型",
              minWidth: 200,
              tooltip: true,
            },
            {
              prop: "signTime",
              label: "签到时间",
              minWidth: 300,
              formatter: (row, column, cellValue, index) => {
                if (this.approveType ==='todo' && this.saveStatus) {
                  return (
                    <div>
                      <el-form-item prop={`signTime${index}`}>
                        <el-date-picker
                          v-model={row.signTime}
                          type="datetime"
                          onChange={(value) => {
                              this.form[`signTime${index}`] = value;
                            }}
                          placeholder="选择日期时间">
                        </el-date-picker>
                      </el-form-item>
                    </div>
                  );
                } else {
                  return row.signTime
                }
              },
            },
            {
              prop: "longitudeLatitude",
              label: "签到经纬度",
              minWidth: 200,
              formatter: (row, column, cellValue, index) => {
                if (this.approveType ==='todo' && this.saveStatus) {
                  return (
                  <el-form-item prop={`longitudeLatitude${index}`}>
                    <el-input 
                      v-model={row.longitudeLatitude}
                    >
                    </el-input>
                  </el-form-item>
                );
                } else {
                  return row.longitudeLatitude
                }
              },
            },
            {
              prop: "clockinType",
              label: "打卡导入类型",
              minWidth: 200,
              formatter: (row, column, cellValue, index) => {
                if (this.approveType ==='todo' && this.saveStatus) {
                  return (
                  <el-form-item prop={`clockinType${index}`}>
                    <el-select
                      v-model={row.clockinType}
                      onChange={(value) => {
                        this.form[`clockinType${index}`] = value;
                      }}
                    >
                      {this.clockinType.length &&
                        this.clockinType.map((item) => {
                          return (
                            <el-option label={item} value={item} />
                          );
                        })}
                    </el-select>
                  </el-form-item>
                );
                } else {
                  return row.clockinType
                }
              },
            },
          ]
        }
      }
    },
    methods: {
       stringToBoolean(str) {
        return (str === "true") ? true : false;
      },
      getPower() {
        getClockTaskList({businessId: this.flow.businessId}).then((res)=>{
          this.tableData = res.data || []
        })
      },
      savePage() {
        this.commonsetRule(this.tableData);
        this.$nextTick(() => {
          this.$refs.editform.validate((validate) => {
            if (validate) {
              let val = []
              this.tableData.forEach(item => {
                val.push({
                  ...item,
                  signTime: this.$moment(item.signTime).format('YYYY-MM-DD HH:mm:ss')
                })
              });
              let params = {
                businessId: this.flow.businessId,
                clockTaskList: val
              }
              updateClockTaskList(params).then((res) => {
                if (res.code == "0000") {
                  this.$refs.flowDetail.msgApproval = ''
                  this.$message.success('信息保存成功');
                  this.$refs.flowDetail.openApprovalDialog()
                  // this.$router.push('/home')
                }
              });
            } else {
              return;
            }
          });
        });
        // this.dialogEmpower = true;
      },
      // 处理其中文字
      commondealName(row){
        let a = [
          "signee",
          "signUnit",
          "cooperateUnitType",
          "signTime",
          "longitudeLatitude",
          "clockinType"
        ]
        a.forEach(item=>{
          row[`${item}Name`]= this[`${item}List`].filter((p)=>{return p.value==row[item]})[0].label
        })
      },
      // 添加必填校验
      commonsetRule(arr, index) {
        this.form = {};
        this.rules = {};
        let a = [
          "signee",
          "signUnit",
          "cooperateUnitType",
          "signTime",
          "longitudeLatitude",
          "clockinType"
        ];
        a.forEach((item) => {
          if (index) {
            this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
            this.rules[`${item}${index}`] = {
              required: true,
              message: "该字段不能为空",
              trigger: ["blur", "change"],
            };
          } else {
            for (let index in arr) {
              this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
              this.rules[`${item}${index}`] = {
                required: true,
                message: "该字段不能为空",
                trigger: ["blur", "change"],
              };
            }
          }
        });
      },
      
    }
}
</script>

<style>
.card {
  margin-bottom: 20px;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  border-radius: 4px;
}
.title {
  text-align: center;
}

.heading {
  margin-top: 0;
  font-size: 24px;
  font-weight: bold;
}




</style>