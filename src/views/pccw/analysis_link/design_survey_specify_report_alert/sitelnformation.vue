<template>
    <div>
      <!--站点信息-->
      <mssCard title="查询结果">
        <div slot="headerBtn">
            <el-upload
              style="display: inline-block;margin-left: 10px;"
              ref="newFile"
              class="upload-btn"
              action="string"
              :show-file-list="false"
              :auto-upload="true"
              :http-request="importFile"
            >
              <el-button type="primary">导入</el-button>
            </el-upload>
            <el-button type="primary" @click="exportHandle" style="margin-left: 10px;">导出模板</el-button>
          <el-button type="primary" @click="delBatch" style="margin-left: 10px;">批量删除</el-button>
        </div>
        <div slot="content">
            <mssTable
              :api="tableApi"
              ref="table"
              :columns="tableHeader"
              border
              selection
              :staticSearchParam="loadParam"
              :getChange="true"
              @getChange="handleGetChange"
            >
            </mssTable>
        </div>
      </mssCard>
      <!-- <el-dialog title="错误信息" :visible.sync="dialogTableVisible">
          <el-table :data="gridData" border>
            <el-table-column property="lineNum" label="行数" width="100"></el-table-column>
            <el-table-column property="message" label="信息" ></el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogTableVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogTableVisible = false">确 定</el-button>
          </div>
      </el-dialog> -->
      <ImportErrorMessage ref="importErrorMessage"></ImportErrorMessage>
    </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
import { getSiteInfo, downloadSite, importExcelSite, delBatch } from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
import moment from "moment";
import ImportErrorMessage from '@/views/pccw/components/importErrorMessage/index'
import {deleteflows} from "@/api/pccw/workflow";
export default {
name: 'sitelnformation',
data() {
  return {
    tableApi: getSiteInfo,
    loadParam: {},
    //表格头部配置
    tableHeader: [
        {
            prop: "designUnit",
            label: "设计单位",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "speciality",
            label: "一级专业",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "city",
            label: "地市",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "district",
            label: "区县",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "specialitySegmentation",
            label: "项目管理专业",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "siteName",
            label: "站点名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "taskCode",
            label: "任务编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "siteLongitude",
            label: "站点经度",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "siteLatitude",
            label: "站点纬度",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "designApprovalDate",
            label: "设计批复日期",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectImplementationManager",
            label: "工程实施经理",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectManagementManager",
            label: "工程管理经理",
            align: "center",
            tooltip: true,
            minWidth: 150
        }
    ],
    dialogTableVisible:false,
    gridData:[],
  };
},
methods: {
  exportHandle() {
      commonDown({}, downloadSite);
  },
  handleGetChange(selection) {
    this.gridData = selection
  },
  delBatch() {
    let idArray = this.gridData.map(item => String(item.id));
    this.delete(idArray)
  },
  delete(row) {
    this.$confirm("此操作将永久删除, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(async () => {
        console.log(row)
        let res = await delBatch(row) // 等待异步操作完成
        if (res.code === "0000") {
          this.$message.success(res.data)
          // 查询刷新
          this.$refs.table.getTableData()
        } else {
          this.$message.error("操作失败！")
        }
      })
      .catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除"
        })
      })
  },
  //重置
  reset(form) {
      this.search(form)
  },
  //搜索
  search() {
    this.$refs.table.page.current = 1
    this.loadParam = JSON.parse(
      JSON.stringify(this.$refs.searchForm.searchForm)
    )
    this.loadParam.clockinDate = moment(this.loadParam.clockinDate).format('yyyy-MM-dd HH:mm:ss')
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  importFile(params) {
    const param = new FormData()
    param.append('file', params.file)
    importExcelSite(param).then((res) => {
      if (res.code === '5000') {
          this.$message.warning(res.msg)
      } else if (res.code === '0000') {
        if (res.data.length > 0){
          this.$refs.importErrorMessage.open(res.data)
        }else {
          this.$message.success('导入成功')
        }
        this.$refs.table.getTableData()
      }
    })
  },
},
mounted() {
    // Code to run when the component is mounted goes here
},
components: {
  ImportErrorMessage
}
};
</script>

<style scoped>
/* Your component's CSS styles go here */
</style>
