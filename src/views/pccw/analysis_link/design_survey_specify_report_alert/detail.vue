<template>
    <div>
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <!--结果展示框-->
      <mssCard title="查询结果">
        <div slot="headerBtn">
            <el-upload
              ref="newFile"
              class="upload-btn"
              action="string"
              :show-file-list="false"
              :auto-upload="true"
              :http-request="importFile"
            >
              <el-button type="primary">导入</el-button>
            </el-upload>
            <el-button type="primary" @click="exportHandle">导出</el-button>
        </div>
        <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              border
              selection
              :staticSearchParam="loadParam"
            >
            </mssTable>
        </div>
      </mssCard>
      <el-dialog title="错误信息" :visible.sync="dialogTableVisible">
          <el-table :data="gridData" border>
            <el-table-column property="lineNum" label="行数" width="100"></el-table-column>
            <el-table-column property="message" label="信息" ></el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogTableVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogTableVisible = false">确 定</el-button>
          </div>
        </el-dialog>
    </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
import { listDesignSurveySpecifyReportAlert, downloadService, importExcelService } from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
import moment from "moment";
export default {
name: 'Detail',
data() {
  return {
    loadParam: {},
    tableApi: listDesignSurveySpecifyReportAlert,
    //搜索字段配置
    searchConfig: [
        {
            label: '设计单位',
            type: 'input',
            fieldName: 'designUnit'
        },
        {
            label: '项目管理专业',
            type: 'input',
            fieldName: 'specialitySegmentation'
        },
        {
            label: '工程实施经理',
            type: 'input',
            fieldName: 'projectImplementationManagerPrimary'
        },
        // {
        //     label: '任务名称',
        //     type: 'input',
        //     fieldName: 'task_name'
        // },
        {
            label: '一级专业',
            type: 'input',
            fieldName: 'speciality'
        },
        {
            label: '工程管理经理',
            type: 'input',
            fieldName: 'projectManagementManagerPrimary'
        },
        {
            label: '项目编码',
            type: 'input',
            fieldName: 'projectCode'
        },
        {
            label: '站点名称',
            type: 'input',
            fieldName: 'siteName'
        },
        {
            label: '地市',
            type: 'input',
            fieldName: 'city'
        },
        {
            label: '项目名称',
            type: 'input',
            fieldName: 'projectName'
        },
        {
            label: '打卡时间',
            type: 'date1',
            fieldName: 'clockinDate'
        },
        {
            label: '区县',
            type: 'input',
            fieldName: 'district'
        },
        {
            label: '任务编码',
            type: 'input',
            fieldName: 'taskCode'
        },
        {
            label: '设计批复日期',
            type: 'date1',
            fieldName: 'designApprovalDate'
        },
        {
            label: '年份',
            type: 'year',
            fieldName: 'year'
        },
    ],
    //表格头部配置
    tableHeader: [
        {
            prop: "designUnit",
            label: "设计单位",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "speciality",
            label: "一级专业",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "city",
            label: "地市",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "district",
            label: "区县",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "specialitySegmentation",
            label: "项目管理专业",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "siteName",
            label: "站点名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        // {
        //     prop: "task_name",
        //     label: "任务名称",
        //     align: "center",
        //     tooltip: true,
        //     minWidth: 150
        // },
        {
            prop: "taskCode",
            label: "任务编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "siteLongitude",
            label: "站点经度",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "siteLatitude",
            label: "站点纬度",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "designApprovalDate",
            label: "设计批复日期",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "signee",
            label: "打卡人员",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "clockinDate",
            label: "打卡时间",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "clockinLongitude",
            label: "打卡经度",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "clockinLatitude",
            label: "打卡纬度",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "signinType",
            label: "签到类型",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "siteDeviation",
            label: "站点偏差",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "deviationStandard",
            label: "偏差标准",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "isClockinDateTimely",
            label: "打卡时间是否及时",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "isClockinDeviationQualified",
            label: "打卡偏差是否合格",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "isClockinQualified",
            label: "打卡是否合格",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectImplementationManagerPrimary",
            label: "工程实施经理",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectManagementManagerPrimary",
            label: "工程管理经理",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "clockinType",
            label: "打卡类型",
            align: "center",
            tooltip: true,
            minWidth: 150
        }    
    ],
    gridData:[],
    dialogTableVisible: false,
  };
},
methods: {
    //导出
    exportHandle() {
        let arr = {
          ...this.$refs.searchForm.searchForm
        }
        arr.year = this.$refs.searchForm.searchForm.year? moment(this.$refs.searchForm.searchForm.year).format('YYYY') : '' 
        commonDown({ ...this.loadParam, limit: -1}, downloadService);
    },
    //重置
    reset(form) {
      this.loadParam = {}
      this.search(form)
    },
    //搜索
    search() {
        this.$refs.table.page.current = 1
        let arr = {
          ...this.$refs.searchForm.searchForm
        }
        arr.year = this.$refs.searchForm.searchForm.year? moment(this.$refs.searchForm.searchForm.year).format('YYYY') : '' 
        this.$refs.table.getTableData(arr)
    },
    importFile(params) {
      const param = new FormData()
      param.append('file', params.file)
      importExcelService(param).then((res) => {
        if (res.code === '5000') {
            this.$message.warning(res.msg)
        } else if (res.code === '0000') {
          if (res.data.length > 0){
            this.dialogTableVisible = true
            this.gridData = res.data
          }else {
            this.$message.success('导入成功')
          }
          this.$refs.commonTable.getTableData()
        }
      })
    },
},
mounted() {
    // Code to run when the component is mounted goes here
},
};
</script>

<style scoped>
/* Your component's CSS styles go here */
</style>
