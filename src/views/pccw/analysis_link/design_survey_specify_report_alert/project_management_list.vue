<template>
    <div>
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <!--结果展示框-->
      <mssCard title="查询结果">
        <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
        </div>
        <div slot="content">
            <mssTable
              :serial="false"
              :customSize="20"
              :pagination="false"
              :staticSearchParam="loadParam"
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
            >
            </mssTable>
        </div>
    </mssCard>
    </div>
</template>

<script>

import { commonDown } from '@/utils/btn'
import { getProMgtMgrSummary, downloadEngineering } from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
import moment from "moment";
export default {
  name: 'ProjectManagementList',
  data() {
    return {
      loadParam: {},
      tableApi: getProMgtMgrSummary,
      //搜索字段配置
      searchConfig: [
          {
              label: '打卡时间段',
              type: 'week',
              fieldName: 'selectDate'
          },
          {
              label: '工程管理经理',
              type: 'input',
              fieldName: 'projectManagementManager',
          },
      ],
      //表格头部配置
      tableHeader: [
        {
            prop: "projectManagementManager",
            label: "工程管理经理",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
          prop: "",
          label: "设计批复站点数",
          align: "center",
          tooltip: true,
          multilevelColumn: [
            {
              prop: "addSitesThisWeek",
              label: "本周新增站点数",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "clockSitesThisWeek",
              label: "本周打卡站点数",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "allAddSites",
              label: "累计站点数",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "allClockSites",
              label: "累计打卡站点数",
              align: "center",
              tooltip: true,
              minWidth: 150
            }
          ],
        },
        {
            prop: "",
            label: "打卡情况",
            align: "center",
            tooltip: true,
            multilevelColumn: [
              {
                prop: "addClockThisWeek",
                label: "本周新增打卡数",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                  prop: "",
                  label: "其中",
                  align: "center",
                  tooltip: true,
                  multilevelColumn: [
                      {
                          prop: "pmsClockinCount",
                          label: "PMS打卡数",
                          align: "center",
                          tooltip: true,
                          minWidth: 150
                      },
                      {
                          prop: "zhikanClockinCount",
                          label: "智勘平台打卡数",
                          align: "center",
                          tooltip: true,
                          minWidth: 150
                      },
                      {
                          prop: "ddingClockinCount",
                          label: "钉钉打卡数",
                          align: "center",
                          tooltip: true,
                          minWidth: 150
                      },
                  ],
              },
              {
                  prop: "clockinRateThisWeek",
                  label: "本周打卡率",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
              {
                  prop: "clockinRateAccumulated",
                  label: "累计打卡率",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
              {
                  prop: "chain",
                  label: "环比",
                  align: "center",
                  tooltip: true,

              },
            ],    
        },
        {
            prop: "",
            label: "打卡经纬度与站点经纬度",
            align: "center",
            tooltip: true,
            multilevelColumn: [
                {
                    prop: "matchNumber",
                    label: "相符数",
                    align: "center",
                    tooltip: true,
                    minWidth: 150
                },
                {
                    prop: "matchRate",
                    label: "相符率",
                    align: "center",
                    tooltip: true,
                    minWidth: 150
                },
            ],
        },
      ],
    };
  },
  methods: {
      //导出
      exportHandle() {
          this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
          this.loadParam.startTime = this.loadParam.startDate ? this.loadParam.startDate : ''
          this.loadParam.endTime = this.loadParam.endDate ? this.loadParam.endDate : ''
          this.loadParam.startDate = ''
          this.loadParam.endDate = ''
          commonDown({ ...this.loadParam, limit: -1}, downloadEngineering);
      },
      //重置
      reset(form) {
        form.startDate = ''
        form.endDate = ''
        form.projectManagementManager = ''
        this.search(form)
      },
      //搜索
      search() {
        this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
        this.loadParam.startTime = this.loadParam.startDate ? this.loadParam.startDate : ''
        this.loadParam.endTime = this.loadParam.endDate ? this.loadParam.endDate : ''
        this.loadParam.startDate = ''
        this.loadParam.endDate = ''
        this.$refs.table.getTableData(this.loadParam);
      },
  },
  mounted() {
      // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
