<!-- 设计勘察专业偏差标准配置 -->
<template>
  <div class="design_warning">
    <mssCard title="设计勘察专业偏差标准配置">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            :pagination="false"
            ref="table"
            :stationary="tableData"
            :columns="tableHeader">
        </mssTable>
        </el-form>
      </div>
    </mssCard>
    <magnifyingGlass ref="magnifyingGlass" @showCheckList="showCheckList" :mult-select="multSelect"></magnifyingGlass>
  </div>
</template>

<script>
import { getDeviationConfig, inserDeviationtConfig, updateDeviationConfig, deleteDeviationConfig,getPrimaryMajor } from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
import { commonMultDel } from "@/utils/btn";
import magnifyingGlass from "@/views/pccw/components/magnifyingGlass/index.vue";
export default {
  components: {
    magnifyingGlass
  },
  name: "design_warning",
  data() {
    return {
      tableData: [],
      warnObjectList: [],
      form: {},
      rules: {},
      multSelect: false,
      coordinatorList: []
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "primaryMajor",
            label: "一级专业",
            minWidth: 360,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`primaryMajor${index}`}>
                  <el-select
                    v-model={row.primaryMajor}
                    onChange={(value) => {
                      this.form[`primaryMajor${index}`] = value;
                    }}
                  >
                    {this.coordinatorList.length &&
                      this.coordinatorList.map((item) => {
                        return (
                          <el-option label={item} value={item} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            prop: "projectManagementMajor",
            label: "项目管理专业",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`projectManagementMajor${index}`}>
                <el-input  v-model={row.projectManagementMajor}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '偏差标准项目管理专业')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "deviationStandard",
            label: "偏差标准(米)",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`deviationStandard${index}`}
                onChange={(value) => {
                  const regex = /^[0-9]*$/; // 正则表达式，匹配最多四位小数的数字
                  if (!regex.test(value)) {
                    // 如果输入值不符合要求，清空输入框
                    row.month = '';
                    this.$message.error('请输入数字');
                  }
                  this.form[`deviationStandard${index}`] = value
                }}
                >
                   <el-input  v-model={row.deviationStandard} ></el-input>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    showCheckList (val) {
      console.log(val)
      let realName = []
      let userId = []
      if (val.checkList.length > 0) {
        val.checkList.forEach(item => {
          realName.push(item.realName)
          userId.push(item.userId)
        })
      }
      if (val.deptParams == '偏差标准项目管理专业') {
        this.$set(this.tableData[val.index], 'projectManagementMajor' , realName.toString())
      }
    },
    openChooseUserDailog (row, index, deptParams) {
      if (row.primaryMajor === '' || row.primaryMajor === null) {
        return this.$message.warning(`请先选一级专业`)
      }
      let item = {}
      if (deptParams == '偏差标准项目管理专业') {
        item = {
          excuterNames:  row.projectManagementMajor ? row.projectManagementMajor : '',
          excuterIds:  row.projectManagementMajor ? row.projectManagementMajor : '',
          primaryMajor: row.primaryMajor
        }
      }
      this.$refs.magnifyingGlass.init(item, deptParams, index)
    },
    getTable() {
      getDeviationConfig({limit: -1}).then((res) => {
        if (res.code == "0000") {
          console.log(res.data)
          this.tableData =  res.data.data
        }
      });
      getPrimaryMajor().then((res)=>{
        this.coordinatorList = res.data || []
      })
    },
    addTable() {
      let data = {
        primaryMajor: "",
        projectManagementMajor: "",
        deviationStandard: ""
      };
      inserDeviationtConfig(data).then((res) => {
        if (res.code == "0000") {
          this.tableData.push({ ...data, id: res.data,list:[] });
        }
      });
    },
    deleteTable(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteDeviationConfig({ids:row.id}).then(res => {
            if (res.code == '0000') {
              this.$message.success("删除成功");
              this.getTable();
            } else {
              this.$message.error(`${res.msg || '删除失败'}`)
            }
          })
        }).catch(() => {})
    },
    saveONe(row, index) {
      this.commonsetRule(row,index);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
              let obj = JSON.parse(JSON.stringify(row));
              updateDeviationConfig(obj).then((res) => {
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
          } else {
            return;
          }
        });
      });
    },
    // 删除
    del() {
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: deleteDeviationConfig,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },

    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "primaryMajor",
        "projectManagementMajor",
        "deviationStandard"
      ];
      a.forEach(item => {
        if (arr[item] === '' || arr[item] == null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          this.rules[`${item}${index}`] = {}
        }
      });
      // a.forEach((item) => {
      //   if (index) {
      //     this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
      //     this.rules[`${item}${index}`] = {
      //       required: true,
      //       message: "该字段不能为空",
      //       trigger: ["blur", "change"],
      //     };
      //   } else {
      //     for (let index in arr) {
      //       this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
      //       this.rules[`${item}${index}`] = {
      //         required: true,
      //         message: "该字段不能为空",
      //         trigger: ["blur", "change"],
      //       };
      //     }
      //   }
      // });
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


