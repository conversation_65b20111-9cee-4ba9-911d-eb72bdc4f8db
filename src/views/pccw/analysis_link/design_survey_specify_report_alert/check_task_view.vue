<template>
  <div>
    <el-card class="card">
        <div class="title">
          <h1 class="heading">补充打卡信息清单</h1>
        </div>
    </el-card>
    <div class="operate-btn" >
      <el-button type="primary" @click="saveInfo">同意审批</el-button>
      <el-button type="primary" @click="saveInfo">驳回审批</el-button>
      <el-button type="primary" @click="saveInfo">关闭</el-button>
    </div>
    <mssCard title="补充打卡信息清单">
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :getSummaries="getSummaries"
          showSummary
        >
        </mssTable>
      </div>
    </mssCard>
    <mssSystemDialog
      :dialog-title="dialogTitle"
      :dialog-minWidth="'450px'"
      :custom-class="'dialogClass'"
      :show-dialog.sync="dialogEmpower"
      :footer="true"
      :canSubmit="true"
    > 
    </mssSystemDialog>

  </div>
</template>

<script>

import { checkTaskList } from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
export default {
    name: 'SafetyPaymentAlert',
    data() {
        return {
          formModel: {},
          showSummary: true,
          tableApi: checkTaskList,
          //总计
          total: 0,
          current: {
            page: 1,
            size: 10
          },
          dialogEmpower: false,
          dialogTitle: "补充打卡信息清单",
          levelNoList: [
            { label: "一级", value: "1" },
            { label: "二级", value: "2" },
            { label: "三级", value: "3" },
            { label: "四级", value: "4" },
            { label: "五级", value: "5" },
          ],
          tableHeader:[
            {
                prop: "taskName",
                label: "项目编码",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "taskName",
                label: "项目名称",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "taskName",
                label: "任务编码",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "taskName",
                label: "任务名称",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "taskName",
                label: "站点名称",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "taskName",
                label: "省份",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "taskName",
                label: "地区",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
              prop: "approvalTempDescription",
              label: "签到人",
              minWidth: 130,
            },
            {
              prop: "approvalTempDescription",
              label: "签到单位",
              minWidth: 130,
            },
            {
              prop: "designCoordinator",
              label: "合作单位类型",
              minWidth: 200,
            },
            {
              prop: "designCoordinator",
              label: "签到类型",
              minWidth: 200,
            },
            {
              prop: "approvalTempDescription",
              label: "签到时间",
              minWidth: 130,
            },
            {
              prop: "approvalTempDescription",
              label: "签到经纬度",
              minWidth: 130,
            },
            {
              prop: "designCoordinator",
              label: "打卡导入类型",
              minWidth: 200
            },
          ]
        }
    },
    created() {
      this.getPower()
    },
    methods: {
      getPower() {
        JSON.parse(sessionStorage.getItem('authorities')).forEach((item) => {
          this.powerData[item.authority] = true
        })
      },
      //计算总计数值
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = "汇总"; // 第一列显示汇总标题
            return;
          }

          const values = data.map((item) => Number(item[column.property]));

          //第6列：设计编制安全生产费汇总
          if(index === 5) {
            if (!values.every((value) => isNaN(value))) {
              sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr);
                if (!isNaN(value)) {
                  return prev + curr;
                } else {
                  return prev;
                }
              }, 0);
              sums[index] = sums[index].toFixed(2); // 自定义格式化汇总值
            } else {
              sums[index] = "";
            }
          }else{
            sums[index] = "";
          }
        });

        return sums;
      },
      saveInfo() {
        this.dialogEmpower = true;
      },
    }
}
</script>

<style>
.card {
  margin-bottom: 20px;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  border-radius: 4px;
}
.title {
  text-align: center;
}

.heading {
  margin-top: 0;
  font-size: 24px;
  font-weight: bold;
}




</style>