<template>
    <div>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="按地市" name="city" >
            <byCity ref="city"></byCity>
        </el-tab-pane>
        <el-tab-pane label="按设计单位" name="designUnit"  lazy>
            <byDesignUnitList ref="designUnit"></byDesignUnitList>
        </el-tab-pane>
        <el-tab-pane label="工程管理经理" name="projectManagement"  lazy>
            <byProjectManagementList ref="projectManagement"></byProjectManagementList>
        </el-tab-pane>
        <el-tab-pane label="工程实施经理" name="engineering"  lazy>
            <engineeringManager ref="engineering"></engineeringManager>
        </el-tab-pane>
        <el-tab-pane label="详情" name="detail" ref="detail" lazy>
            <detail ref="detail"></detail>
        </el-tab-pane>
        <el-tab-pane label="导入站点信息" name="sitelnformation"  lazy>
            <sitelnformation ref="sitelnformation"></sitelnformation>
        </el-tab-pane>
        <el-tab-pane label="设计勘察审批配置" name="designSurvey"  lazy>
            <designSurvey ref="designSurvey"></designSurvey>
        </el-tab-pane>
        <el-tab-pane label="设计勘察专业偏差标准配置" name="standardConfiguration" lazy>
            <standardConfiguration ref="standardConfiguration"></standardConfiguration>
        </el-tab-pane>
        <el-tab-pane label="打卡信息导入" name="checkInformation" lazy>
            <checkInformation ref="checkInformation"></checkInformation>
        </el-tab-pane>
        <el-tab-pane label="PMS打卡数据查询" name="pmsSignIn" lazy>
            <pmsSignIn ref="pmsSignIn"></pmsSignIn>
        </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
// Engineering Implementation Manager
import byCity from "@/views/pccw/analysis_link/design_survey_specify_report_alert/city_list.vue";
import byProjectManagementList from "@/views/pccw/analysis_link/design_survey_specify_report_alert/project_management_list.vue";
import byDesignUnitList from "@/views/pccw/analysis_link/design_survey_specify_report_alert/design_unit_list.vue";
import detail from "@/views/pccw/analysis_link/design_survey_specify_report_alert/detail.vue";
import engineeringManager from "@/views/pccw/analysis_link/design_survey_specify_report_alert/engineeringManager.vue";
import designSurvey from "@/views/pccw/analysis_link/design_survey_specify_report_alert/design_survey.vue";
import sitelnformation from "@/views/pccw/analysis_link/design_survey_specify_report_alert/sitelnformation.vue";
import standardConfiguration from "@/views/pccw/analysis_link/design_survey_specify_report_alert/standardConfiguration.vue";
import checkInformation from "@/views/pccw/analysis_link/design_survey_specify_report_alert/checkInformation.vue";
import pmsSignIn from "@/views/pccw/analysis_link/design_survey_specify_report_alert/pmsSignIn.vue";
export default {
  name: 'DesignSurveySpecifyReportAlert',
  components: {
      byCity,
      byProjectManagementList,
      byDesignUnitList,
      detail,
      engineeringManager,
      designSurvey,
      sitelnformation,
      standardConfiguration,
      checkInformation,
      pmsSignIn
  },
  data() {
      return {
        activeName: 'city',
      };
  },
  methods: {
    handleClick () {

    },
      // Your methods here
  },
  mounted() {
      // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your component styles here */
</style>
