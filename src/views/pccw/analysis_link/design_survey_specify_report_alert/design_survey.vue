<!-- 设计预警 -->
<template>
  <div class="design_warning">
    <mssCard title="设计勘察审批人员配置界面">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :pagination="false"
            :stationary="tableData"
            :columns="tableHeader"
          >
        </mssTable>
        </el-form>
      </div>
    </mssCard>
    <magnifyingGlass ref="magnifyingGlass" @showCheckList="showCheckList" :mult-select="multSelect"></magnifyingGlass>
  </div>
</template>

<script>
import { queryListService, insertConfig, updateConfig, delService,queryDesignCoordinator } from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
import { commonMultDel, commonOneDel } from "@/utils/btn";
import magnifyingGlass from "@/views/pccw/components/magnifyingGlass/index.vue";
export default {
  components: {
    magnifyingGlass
  },
  name: "design_warning",
  data() {
    return {
      tableData: [],
      levelNoList: [
        { label: "一级", value: "1" },
        { label: "二级", value: "2" },
        { label: "三级", value: "3" },
        { label: "四级", value: "4" },
        { label: "五级", value: "5" },
      ],
      warnObjectList: [
        { label: "中国移动通信集团设计院有限公司"},
        { label: "华信咨询设计研究院有限公司" },
        { label: "广东省电信规划设计院有限公司"},
        { label: "中电科普天科技股份有限公司"},
        { label: "中讯邮电咨询设计院有限公司"},
        // { label: "江西省建筑设计研究总院集团有限公司"},
        // { label: "安徽电信规划设计有限责任公司"},
        
      ],
      form: {},
      rules: {},
      multSelect: false
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "approvalTempDescription",
            label: "审批模板文字说明",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`approvalTempDescription${index}`}>
                  <el-input  v-model={row.approvalTempDescription} >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "designUnit",
            label: "设计单位",
            minWidth: 360,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`designUnit${index}`}>
                  <el-select
                    v-model={row.designUnit}
                    onChange={(value) => {
                      this.form[`designUnit${index}`] = value;
                    }}
                  >
                    {this.warnObjectList.length &&
                      this.warnObjectList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.label} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            prop: "designUnitPerson",
            label: "设计单位人员",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`designUnitPerson${index}`}>
                <el-input  v-model={row.designUnitPerson}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '设计单位人员')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "designCoordinatorId",
            label: "设计统筹人员",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`designCoordinatorId${index}`}>
                  <el-select
                    v-model={row.designCoordinatorId}
                    onChange={(value) => {
                      this.form[`designCoordinatorId${index}`] = value;
                    }}
                  >
                    {this.coordinatorList.length &&
                      this.coordinatorList.map((item) => {
                        return (
                          <el-option label={item.realName} value={item.userId} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    showCheckList (val) {
        let realName = []
        let userId = []
        if (val.checkList.length > 0) {
          val.checkList.forEach(item => {
            realName.push(item.realName)
            userId.push(item.userId)
          })
        }
      if (val.deptParams == '设计单位人员') {
        this.$set(this.tableData[val.index], 'designUnitPerson' , realName.toString())
        this.$set(this.tableData[val.index], 'designUnitPersonId' , userId.toString())
      }
    },
    openChooseUserDailog (row, index, deptParams) {
      if (row.designUnit === '' || row.designUnit === null) {
        return this.$message.warning(`请先选设计单位`)
      }
      let item = {}
      if (deptParams == '设计单位人员') {
        item = {
          excuterNames: row.designUnitPerson ? row.designUnitPerson : '',
          excuterIds: row.designUnitPersonId ? row.designUnitPersonId : '',
          designUnit: row.designUnit
        }
      }
      this.$refs.magnifyingGlass.init(item, deptParams, index)
    },
    getTable() {
      queryListService().then((res) => {
        if (res.code == "0000") {
          if (res.data.length > 0) {
            this.tableData = res.data
          } else {
            this.tableData = []
          }
        }
      });
      queryDesignCoordinator().then((res)=>{
        this.coordinatorList = res.data || []
      })
    },
    addTable() {
      if (this.warnObjectList.length < this.tableData.length) {
       return this.$message.warning(`列表不能大于设计单位条数`)
      } else {
        let data = {
          createBy: "",
          createTime: "",
          designCoordinatorId: "",
          designUnit: "",
          designUnitPerson: "",
          updateBy: "",
          updateTime: ""
        };
        insertConfig(data).then((res) => {
          if (res.code == "0000") {
            this.tableData.push({ ...data, id: res.data,list:[] });
          }
        });
      }
        
    },
    deleteTable(row) {
      // commonOneDel.call(this, row.id, delService, (res) => {
      //   this.$message.success("删除成功");
      //   this.getTable();
      // });
      this.$confirm('是否确认删除这条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delService({ids:row.id}).then(res => {
            if (res.code == '0000') {
              this.$message.success("删除成功");
              this.getTable();
            } else {
              this.$message.error(`${res.msg || '删除失败'}`)
            }
          })
        }).catch(() => {})
    },
    saveONe(row, index) {
      this.commonsetRule(row,index);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            if (this.validateCopy(row)) {
              let obj = JSON.parse(JSON.stringify(row));
              // if (obj.warnObject) obj.warnObject = obj.warnObject.join(",");
              updateConfig(obj).then((res) => {
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
            } else {
              this.$message.warning(`相同设计单位不能重复选择只能有一条数据，请修改数据`);
            }
          } else {
            return;
          }
        });
      });
    },
    // 删除
    del() {
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: delService,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },

    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "approvalTempDescription",
        "designUnit",
        "designUnitPerson",
        "designCoordinatorId"
      ];
      a.forEach(item => {
        if (arr[item] === '' || arr[item] == null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          this.rules[`${item}${index}`] = {}
        }
      });
      // a.forEach((item) => {
      //   if (index) {
      //     this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
      //     this.rules[`${item}${index}`] = {
      //       required: true,
      //       message: "该字段不能为空",
      //       trigger: ["blur", "change"],
      //     };
      //   } else {
      //     for (let index in arr) {
      //       this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
      //       this.rules[`${item}${index}`] = {
      //         required: true,
      //         message: "该字段不能为空",
      //         trigger: ["blur", "change"],
      //       };
      //     }
      //   }
      // });
    },
    // 校验当前数据是否有存在的情况
    validateCopy(row) {
      let n = 0;
      this.tableData.forEach((item) => {
        if (item.designUnit && item.designUnit == row.designUnit) {
          n++;
        }
      });
      return n < 2;
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


