<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <!-- v-if -->
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-button  type="primary" @click="exportMethod(1)">下载模板</el-button>
        <el-upload
          style="display: inline-block;margin-left: 10px;"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button  type="primary" @click="exportMethod(2)" style="margin-left: 10px;">导出</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          selection
          border
          :staticSearchParam="loadParam"
        >
        </mssTable>
      </div>
    </mssCard>
    <ExemptionAdded @onClose='onClose' ref="exemptionAdded"></ExemptionAdded>
  </div>
</template>

<script>

import { getQuery, importTemplate, getExport, lpsVideoAcceptanceDelete, importExcelService } from '@/api/pccw/analysis_link/videoAcceptance/exemptionList';
// 获取地市:
import ExemptionAdded from "@/views/pccw/analysis_link/video_acceptance/lpsVideoAcceptanceExempt/exemptionAdded.vue";
export default {
name: 'videoAcceptancePromotion',
components: {
  ExemptionAdded
},
data() {
  return{
    loadParam: {},
    // 表格数据API
    tableApi: getQuery,
    //搜索字段配置
    searchConfig: [
      {
        label: '项目编码',
        type: 'input',
        fieldName: 'projectNo',
      },
      {
        label: '项目名称',
        type: 'input',
        fieldName: 'projectName'
      },
      {
        label: '任务编码',
        type: 'input',
        fieldName: 'taskCode',
      },
      {
        label: '任务名称',
        type: 'input',
        fieldName: 'taskName'
      },
      {
        label: '任务创建时间',
        type: "daterange",
        format:'yyyy-MM-dd',
        valueFormat:'yyyy-MM-dd',
        fieldName: "date",
      },
      {
        label: '任务状态',
        type: 'select',
        fieldName: 'taskStatus',
        options: [{label:'已终止',value:'已终止'},{label:'未终止',value:'未终止'}]
      },
      {
        label: '建设单位',
        type: 'select',
        fieldName: 'constructUnit'
      },
      
    ],
    //表头
    tableHeader: [
      {
          prop: "projectNo",
          label: "项目编码",
          align: "center",
          tooltip: true,
          minWidth: 100
      },
      {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          tooltip: true,
          minWidth: 100
      },
      {
          prop: "taskCode",
          label: "任务编码",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "taskName",
          label: "任务名称",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "taskCreateTime",
          label: "任务创建时间",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "taskStatus",
          label: "任务状态",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "constructUnit",
          label: "建设单位",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "createTime",
          label: "加入时间",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "username",
          label: "操作人",
          align: "center",
          tooltip: true,
          minWidth: 150
      }
    ]
  }
},

created() {
  this.$set(this.searchConfig[6], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
},
methods: {
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
  },
  search() {
    this.$refs.table.page.current = 1
    let data = {
      ...this.$refs.searchForm.searchForm,
      taskCreateStartTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[0] : '' , //任务创建开始时间
      taskCreateEndTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[1] : '', //任务创建结束时间
    }
    this.loadParam = {...data}
    this.$refs.table.getTableData(data);
  },
  reset() {
    this.search()
  },
  importFile(params) {
    const param = new FormData()
    param.append('file', params.file)
    importExcelService(param).then((res) => {
      if (res.data.includes('preValidInfo')) {
        this.$message.warning(res.data)
      } else if (res.code === '0000') {
        this.$message.success('导入成功')
        this.$refs.table.getTableData()
      }
    })
  },
  exportMethod(val) {
    if (val == 1) {
      importTemplate({ ...this.loadParam, limit: -1}).then(res => {
        let fileName = "";
        fileName = "视频验收豁免清单模板.xlsx";
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
      })
    } else {
      let data = {
      ...this.$refs.searchForm.searchForm,
      taskCreateStartTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[0] : '' , //任务创建开始时间
      taskCreateEndTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[1] : '', //任务创建结束时间
      exportFlag: false
      }
      getExport(data).then(res => {
        let fileName = "";
        fileName = "视频验收豁免清单.xlsx";
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
      })
    }
  },
  getAddition () {
    this.$refs.exemptionAdded.open()
  },
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      console.log(this.$refs.table.multipleSelection)
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      lpsVideoAcceptanceDelete({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
    
  },
},

}
</script>

<style>

</style>