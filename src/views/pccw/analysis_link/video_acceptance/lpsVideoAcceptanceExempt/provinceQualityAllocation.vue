<template>
  <div>
      <mssCard title="省公司质量管理员">
        <div slot="headerBtn">
          <!-- <el-button type="primary" @click="getsynRoleByRPA">手动同步经理</el-button> -->
          <el-button type="primary" @click="addTable">新增</el-button>
        </div>
        <div slot="content">
          <el-form ref="exemptionList" :model="form" :rules="rules">
            <mssTable
              ref="table"
              selection
              :serial="false"
              :stationary="tableData"
              :columns="tableHeader"
              :pagination="false"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
      <magnifyingGlass ref="magnifyingGlass" @showCheckList="showCheckList" :mult-select="multSelect"></magnifyingGlass>
  </div>
</template>
<script>
import { queryList, addRole, addRoleDel, synRoleByRPA } from '@/api/pccw/analysis_link/videoAcceptance/exemptionList';
import magnifyingGlass from "@/views/pccw/components/magnifyingGlass/index.vue";
import { commonMultDel, commonOneDel } from "@/utils/btn";
export default {
  components: {
    magnifyingGlass
  },
  name: 'exemptionAdded',
  data () {
    return {
      dialogVisible: false,
      tableData: [],
      form: {},
      rules: {},
      multSelect: false
    }
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "account",
            label: "账号",
            minWidth: 130
          },
          {
            prop: "realName",
            label: "姓名",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`realName${index}`}>
                  <el-input  v-model={row.realName}
                    readonly onFocus={() => {
                      this.openChooseUserDailog(row, index, '省公司质量管理员')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "createBy",
            label: "操作人",
            minWidth: 130
          },
          {
            prop: "createTime",
            label: "添加时间",
            minWidth: 200,
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row, index);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    openChooseUserDailog (row, index, deptParams) { 
      let item = {
        excuterNames: row.realName ? row.realName : '',
        excuterIds: row.realName ? row.realName : '',
        firstDeptName: ''
      }
      this.$refs.magnifyingGlass.init(item, deptParams, index)
    },
    showCheckList (val) {
      console.log(val)
      this.$set(this.tableData[val.index], 'userId' , val.checkList[0].userId)
      this.$set(this.tableData[val.index], 'realName' , val.checkList[0].realName)
      this.$set(this.tableData[val.index], 'account' , val.checkList[0].account)
    },
    getsynRoleByRPA(){
     synRoleByRPA().then((res)=>{
      if (!res.code == '0000') {
        this.$message.success(res.data.message)
      }
      console.log(res)
     })
    },
    deleteTable (row, index) {
      if (row.userId == '') {
        this.tableData.splice(index, 1)
        this.$message.success("删除成功");
      } else {
        addRoleDel({ids:row.id}).then(res => {
          if (res.code == '0000') {
              this.$message.success("删除成功");
              this.getTable();
            } else {
              this.$message.error(`${res.msg || '删除失败'}`)
            }
        })
      }
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.exemptionList.validate((validate) => {
          if (validate) {
              let data = {
                ...row,
                createTime: this.$moment(row.createTime).format('YYYY-MM-DD')
              }
              addRole(data).then((res) => {
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
          } else {
            return;
          }
        });
      });
    },
    getTable () {
       queryList({ userId: '',roleName:'',roleId: 225, constractUnit:''}).then((res)=>{
          this.tableData = res.data.data
       })
    },
    open () {
      this.dialogVisible = true
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "realName",
      ];
      a.forEach(item => {
        if (arr[item] === '' || arr[item] == null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          this.rules[`${item}${index}`] = {}
        }
      });
    },
    // 校验当前数据是否有存在的情况
    validateCopy(row) {
      let n = 0;
      this.tableData.forEach((item) => {
        if (item.levelNo && item.levelNo == row.levelNo&&item.warnType==row.warnType) {
          n++;
        }
      });
      return n < 2;
    },
    // 删除
    del() {
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: addRoleDel,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },
    addTable() {
        let data = {
          "realName": "", //用户名称
          "firstDeptName": "", //第一部门
          "thirdDeptName": "", //第三部门
          "userId": "", //用户id
          "account": "", //用户账号
          "roleId": 225, //角色id
          "constractUnit": "", //建设单位
          "roleName": "省公司质量管理员", //角色名称
          "constructUnit": "", //序列增长
          "createTime": this.$moment().format('YYYY-MM-DD'), //创建时间
          "createBy": sessionStorage.getItem('realName'), //创建人
          "updateTime": "", //修改时间
          "updateBy": "", //修改人
        };
        addRole(data).then((res) => {
          if (res.code == "0000") {
            this.tableData.push({ ...data, id: res.data, ids: res.data});
          }
        });
    },
  }
} 
</script>
