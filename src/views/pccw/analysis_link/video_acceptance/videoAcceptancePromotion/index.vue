<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
      @changeSelect="changeSelect"
      :form="searchForm"
    >
    <div slot='customForm' class="content">
      <el-form :inline="true" ref="formInline" :model="formInline" class="search-form" :label-width="labelWidth.default">
        <el-row style="width: 100%;">
          <el-col :span="8" :offset="0"  v-if="whether === 'UNITDIM' || whether === 'UNITPROMANAGERDIM' || whether === 'UNITCONSTRUCTDIM' || whether === 'UNITCITYCONSTRUCTDIM' || whether === 'UNITPROCONSTRUCTDIM'">
            <el-form-item label="建设单位" style="width: 100%;" class="elSelect">
              <el-select multiple v-model="formInline.constructUnit" placeholder="--请选择--" style="width: 100%;">
                <el-option v-for="(item,index) in cityList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'UNITPROMANAGERDIM'">
            <el-form-item label="工程实施经理-主" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.projectImplementationManagerPrimary"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'PROMANAGEDIM'">
            <el-form-item label="项目管理专业" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.discipline"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'PROVINCEPROMANAGERDIM'">
            <el-form-item label="工程管理经理-主"  style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.projectManagementManagerPrimary"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'UNITCITYCONSTRUCTDIM'">
            <el-form-item label="区县"  style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.district"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'UNITPROCONSTRUCTDIM'">
            <el-form-item label="项目名称" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.projectName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'UNITCONSTRUCTDIM' || whether === 'UNITPROCONSTRUCTDIM' || whether === 'UNITCITYCONSTRUCTDIM'">
            <el-form-item label="施工单位" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.constructionUnit"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="exportMethod(1)" style="margin-left: 10px;">一键导出</el-button>
        <el-button  type="primary" @click="exportMethod(2)">导出</el-button>
        <el-button  type="primary" @click="getEditNotes">编辑备注</el-button>
      </div>
      <div slot="content">
        <Table1
          v-show="whether === 'UNITDIM'"
          ref="UNITDIM"
          :api="tableApi"
          :columns="UNITDIM"
          :staticSearchParam="loadParam"
        >
        </Table1>
        <Table1
          v-show="whether === 'PROMANAGEDIM'"
          ref="PROMANAGEDIM"
          :api="tableApi"
          :columns="PROMANAGEDIM"
          :staticSearchParam="loadParam"
        >
        </Table1>
        <Table1
          v-show="whether === 'PROVINCEPROMANAGERDIM'"
          ref="PROVINCEPROMANAGERDIM"
          :api="tableApi"
          :columns="PROVINCEPROMANAGERDIM"
          :staticSearchParam="loadParam"
        >
        </Table1>
        <Table2
          v-show="whether === 'UNITPROMANAGERDIM'"
          ref="UNITPROMANAGERDIM"
          :api="tableApi"
          :columns="UNITPROMANAGERDIM"
          :staticSearchParam="loadParam"
          :serial ='true'
        >
        </Table2>
        <Table2
          v-show="whether === 'UNITCONSTRUCTDIM'"
          ref="UNITCONSTRUCTDIM"
          :api="tableApi"
          :columns="UNITCONSTRUCTDIM"
          :staticSearchParam="loadParam"
          :serial ='true'
        >
        </Table2>
        <Table3
          v-show="whether === 'UNITCITYCONSTRUCTDIM'"
          ref="UNITCITYCONSTRUCTDIM"
          :api="tableApi"
          :columns="UNITCITYCONSTRUCTDIM"
          :staticSearchParam="loadParam"
        >
        </Table3>
        <Table3
          v-show="whether === 'UNITPROCONSTRUCTDIM'"
          ref="UNITPROCONSTRUCTDIM"
          :api="tableApi"
          :columns="UNITPROCONSTRUCTDIM"
          :staticSearchParam="loadParam"
        >
        </Table3>
      </div>
    </mssCard>
    <div v-html="characters"></div>
    <EditNotes @onClose='onClose' ref="editNotes"></EditNotes>
  </div>
</template>

<script>
//获取接口地址
import { queryDetailByDim, getNotesQuery, QIPexport, exportVideoStatisticsByDim } from '@/api/pccw/analysis_link/videoAcceptance/exemptionList';
// 获取地市:
import EditNotes from "./editNotes.vue";
import Table1 from "./Table1.vue";
import Table2 from "./Table2.vue";
import Table3 from "./Table3.vue";
import { commonDown } from '@/utils/btn'
export default {
name: 'videoAcceptancePromotion',
components: {
  EditNotes,
  Table1,
  Table2,
  Table3
},
data() {
  return{
    showSummary: true,
    // 表格数据API
    tableApi: queryDetailByDim,
    //搜索字段配置
    searchConfig: [
      {
        label: '统计维度',
        type: 'select',
        fieldName: 'statisticDim',
        options: [
          {label: '建设单位', value: 'UNITDIM'},
          {label: '项目管理专业', value: 'PROMANAGEDIM'},
          {label: '省管项目经理', value: 'PROVINCEPROMANAGERDIM'},
          {label: '建设单位项目经理', value: 'UNITPROMANAGERDIM'},
          {label: '建设单位、施工单位', value: 'UNITCONSTRUCTDIM'},
          {label: '建设单位区县、施工单位维度', value: 'UNITCITYCONSTRUCTDIM'},
          {label: '建设单位、项目、施工单位', value: 'UNITPROCONSTRUCTDIM'}
        ]
      },
      {
        label: '日期',
        fieldName: 'date',
        type: "date1",
        format:'yyyy-MM-dd',
        valueFormat:'yyyy-MM-dd',
      },
    ],
    // 建设单位
    UNITDIM: [
     {
        prop: "titleName",
        label: 'QlP视频验收推进进展(建设单位)',
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            prop: "titleName",
            label: '已完工需进行视频验收站点',
            multilevelColumn:[
              {
                label: '规模',
                prop: 'videoFinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工10天内未完成视频验收)',
                prop: 'videoTenunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工20天内未完成视频验收)',
                prop: 'videoTwnunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工30天内未完成视频验收)',
                prop: 'videoThiunfinishScale',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '超期占比',
                prop: 'videoUnfinishRate',
                minWidth: 100,
              },
              {
                label: '及时完成视频验收站点',
                prop: 'videoFinishPass',
                minWidth: 100,
              },
              {
                label: '超期完成视频验收站点',
                prop: 'videoUnfinishCount',
                minWidth: 100,
              },
              {
                label: '已完成视频验收并通过站点',
                prop: 'videofinishCount',
                minWidth: 100,
              },
              {
                label: '视频验收完成率',
                prop: 'videoRate',
                minWidth: 100,
              },
            ]
          },
          {
            prop: "titleName",
            label: '一次性视频验收通过率',
            multilevelColumn: [
              {
                label: '视频验收通过站点数',
                prop: 'videoCount',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过站点数',
                prop: 'videoPass',
                minWidth: 100,
              },
              {
                label: '省公司抽查发现的不合格站点数',
                prop: 'provinceVideoUnfinish',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过率',
                prop: 'videoPassRate',
                minWidth: 100,
              },
              {
                label: '得分',
                prop: 'videoPassRateSix',
                minWidth: 100,
              },
            ]
          }
        ]
      },
    ],
    // 项目管理专业
    PROMANAGEDIM: [
      {
        prop: "titleName",
        label: 'QlP视频验收推进进展(项目管理专业)',
        multilevelColumn: [
          {
            label: '项目管理专业',
            prop: 'discipline',
            minWidth: 100,
          },
          {
            prop: "titleName",
            label: '已完工需进行视频验收站点',
            multilevelColumn:[
              {
                label: '规模',
                prop: 'videoFinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工10天内未完成视频验收)',
                prop: 'videoTenunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工20天内未完成视频验收)',
                prop: 'videoTwnunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工30天内未完成视频验收)',
                prop: 'videoThiunfinishScale',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '超期占比',
                prop: 'videoUnfinishRate',
                minWidth: 100,
              },
              {
                label: '及时完成视频验收站点',
                prop: 'videoFinishPass',
                minWidth: 100,
              },
              {
                label: '超期完成视频验收站点',
                prop: 'videoUnfinishCount',
                minWidth: 100,
              },
              {
                label: '已完成视频验收并通过站点',
                prop: 'videofinishCount',
                minWidth: 100,
              },
              {
                label: '视频验收完成率',
                prop: 'videoRate',
                minWidth: 100,
              },
            ]
          },
          {
            prop: "titleName",
            label: '一次性视频验收通过率',
            multilevelColumn: [
              {
                label: '视频验收通过站点数',
                prop: 'videoCount',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过站点数',
                prop: 'videoPass',
                minWidth: 100,
              },
              {
                label: '省公司抽查发现的不合格站点数',
                prop: 'provinceVideoUnfinish',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过率',
                prop: 'videoPassRate',
                minWidth: 100,
              },
              {
                label: '得分',
                prop: 'videoPassRateSix',
                minWidth: 100,
              },
            ]
          }
        ]
      },
    ],
    // 省管项目经理
    PROVINCEPROMANAGERDIM: [
      {
        prop: "titleName",
        label: 'QlP视频验收推进进展(工程管理经理-主)',
        multilevelColumn: [
          {
            label: '工程管理经理-主',
            prop: 'projectManagementManagerPrimary',
            minWidth: 100,
          },
          {
            prop: "titleName",
            label: '已完工需进行视频验收站点',
            multilevelColumn:[
              {
                label: '规模',
                prop: 'videoFinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工10天内未完成视频验收)',
                prop: 'videoTenunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工20天内未完成视频验收)',
                prop: 'videoTwnunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工30天内未完成视频验收)',
                prop: 'videoThiunfinishScale',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '超期占比',
                prop: 'videoUnfinishRate',
                minWidth: 100,
              },
              {
                label: '及时完成视频验收站点',
                prop: 'videoFinishPass',
                minWidth: 100,
              },
              {
                label: '超期完成视频验收站点',
                prop: 'videoUnfinishCount',
                minWidth: 100,
              },
              {
                label: '已完成视频验收并通过站点',
                prop: 'videofinishCount',
                minWidth: 100,
              },
              {
                label: '视频验收完成率',
                prop: 'videoRate',
                minWidth: 100,
              },
            ]
          },
          {
            prop: "titleName",
            label: '一次性视频验收通过率',
            multilevelColumn: [
              {
                label: '视频验收通过站点数',
                prop: 'videoCount',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过站点数',
                prop: 'videoPass',
                minWidth: 100,
              },
              {
                label: '省公司抽查发现的不合格站点数',
                prop: 'provinceVideoUnfinish',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过率',
                prop: 'videoPassRate',
                minWidth: 100,
              },
              {
                label: '得分',
                prop: 'videoPassRateSix',
                minWidth: 100,
              },
            ]
          }
        ]
      },
    ],
    // 建设单位项目经理
    UNITPROMANAGERDIM: [
      {
        prop: "titleName",
        label: 'QlP视频验收推进进展(建设单位工程实施经理-主)',
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '工程实施经理-主',
            prop: 'projectImplementationManagerPrimary',
            minWidth: 100,
          },
          {
            prop: "titleName",
            label: '已完工需进行视频验收站点',
            multilevelColumn:[
              {
                label: '规模',
                prop: 'videoFinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工10天内未完成视频验收)',
                prop: 'videoTenunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工20天内未完成视频验收)',
                prop: 'videoTwnunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工30天内未完成视频验收)',
                prop: 'videoThiunfinishScale',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '超期占比',
                prop: 'videoUnfinishRate',
                minWidth: 100,
              },
              {
                label: '及时完成视频验收站点',
                prop: 'videoFinishPass',
                minWidth: 100,
              },
              {
                label: '超期完成视频验收站点',
                prop: 'videoUnfinishCount',
                minWidth: 100,
              },
              {
                label: '已完成视频验收并通过站点',
                prop: 'videofinishCount',
                minWidth: 100,
              },
              {
                label: '视频验收完成率',
                prop: 'videoRate',
                minWidth: 100,
              },
            ]
          },
          {
            prop: "titleName",
            label: '一次性视频验收通过率',
            multilevelColumn: [
              {
                label: '视频验收通过站点数',
                prop: 'videoCount',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过站点数',
                prop: 'videoPass',
                minWidth: 100,
              },
              {
                label: '省公司抽查发现的不合格站点数',
                prop: 'provinceVideoUnfinish',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过率',
                prop: 'videoPassRate',
                minWidth: 100,
              },
              {
                label: '得分',
                prop: 'videoPassRateSix',
                minWidth: 100,
              },
            ]
          }
        ]
      },
    ],
    // 建设单位、施工单位
    UNITCONSTRUCTDIM: [
      {
        prop: "titleName",
        label: 'QIP视频验收推进进展（建设单位施工单位）',
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            prop: "titleName",
            label: '已完工需进行视频验收站点',
            multilevelColumn:[
              {
                label: '施工单位',
                prop: 'constructionUnit',
                minWidth: 100,
              },
              {
                label: '规模',
                prop: 'videoFinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工10天内未完成视频验收)',
                prop: 'videoTenunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工20天内未完成视频验收)',
                prop: 'videoTwnunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工30天内未完成视频验收)',
                prop: 'videoThiunfinishScale',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '超期占比',
                prop: 'videoUnfinishRate',
                minWidth: 100,
              },
              {
                label: '及时完成视频验收站点',
                prop: 'videoFinishPass',
                minWidth: 100,
              },
              {
                label: '超期完成视频验收站点',
                prop: 'videoUnfinishCount',
                minWidth: 100,
              },
              {
                label: '已完成视频验收并通过站点',
                prop: 'videofinishCount',
                minWidth: 100,
              },
              {
                label: '视频验收完成率',
                prop: 'videoRate',
                minWidth: 100,
              },
            ]
          },
          {
            prop: "titleName",
            label: '一次性视频验收通过率',
            multilevelColumn: [
              {
                label: '视频验收通过站点数',
                prop: 'videoCount',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过站点数',
                prop: 'videoPass',
                minWidth: 100,
              },
              {
                label: '省公司抽查发现的不合格站点数',
                prop: 'provinceVideoUnfinish',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过率',
                prop: 'videoPassRate',
                minWidth: 100,
              },
              {
                label: '得分',
                prop: 'videoPassRateSix',
                minWidth: 100,
              },
            ]
          }
        ]
      },
    ],
    // 建设单位区县、施工单位维度
    UNITCITYCONSTRUCTDIM: [
      {
        prop: "titleName",
        label: 'QIP视频验收推进进展（建设单位区县施工单位）',
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '区县',
            prop: 'district',
            minWidth: 100,
          },
          {
            prop: "titleName",
            label: '已完工需进行视频验收站点',
            multilevelColumn:[
              {
                label: '施工单位',
                prop: 'constructionUnit',
                minWidth: 100,
              },
              {
                label: '规模',
                prop: 'videoFinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工10天内未完成视频验收)',
                prop: 'videoTenunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工20天内未完成视频验收)',
                prop: 'videoTwnunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工30天内未完成视频验收)',
                prop: 'videoThiunfinishScale',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '超期占比',
                prop: 'videoUnfinishRate',
                minWidth: 100,
              },
              {
                label: '及时完成视频验收站点',
                prop: 'videoFinishPass',
                minWidth: 100,
              },
              {
                label: '超期完成视频验收站点',
                prop: 'videoUnfinishCount',
                minWidth: 100,
              },
              {
                label: '已完成视频验收并通过站点',
                prop: 'videofinishCount',
                minWidth: 100,
              },
              {
                label: '视频验收完成率',
                prop: 'videoRate',
                minWidth: 100,
              },
            ]
          },
          {
            prop: "titleName",
            label: '一次性视频验收通过率',
            multilevelColumn: [
              {
                label: '视频验收通过站点数',
                prop: 'videoCount',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过站点数',
                prop: 'videoPass',
                minWidth: 100,
              },
              {
                label: '省公司抽查发现的不合格站点数',
                prop: 'provinceVideoUnfinish',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过率',
                prop: 'videoPassRate',
                minWidth: 100,
              },
              {
                label: '得分',
                prop: 'videoPassRateSix',
                minWidth: 100,
              },
            ]
          }
        ]
      },
    ],
    // 建设单位、项目、施工单位
    UNITPROCONSTRUCTDIM: [
      {
        prop: "titleName",
        label: 'QIP视频验收推进进展（建设单位项目施工单位）',
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '项目',
            prop: 'projectName',
            minWidth: 100,
          },
          {
            prop: "titleName",
            label: '已完工需进行视频验收站点',
            multilevelColumn:[
              {
                label: '施工单位',
                prop: 'constructionUnit',
                minWidth: 100,
              },
              {
                label: '规模',
                prop: 'videoFinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工10天内未完成视频验收)',
                prop: 'videoTenunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工20天内未完成视频验收)',
                prop: 'videoTwnunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工30天内未完成视频验收)',
                prop: 'videoThiunfinishScale',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '超期占比',
                prop: 'videoUnfinishRate',
                minWidth: 100,
              },
              {
                label: '及时完成视频验收站点',
                prop: 'videoFinishPass',
                minWidth: 100,
              },
              {
                label: '超期完成视频验收站点',
                prop: 'videoUnfinishCount',
                minWidth: 100,
              },
              {
                label: '已完成视频验收并通过站点',
                prop: 'videofinishCount',
                minWidth: 100,
              },
              {
                label: '视频验收完成率',
                prop: 'videoRate',
                minWidth: 100,
              },
            ]
          },
          {
            prop: "titleName",
            label: '一次性视频验收通过率',
            multilevelColumn: [
              {
                label: '视频验收通过站点数',
                prop: 'videoCount',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过站点数',
                prop: 'videoPass',
                minWidth: 100,
              },
              {
                label: '省公司抽查发现的不合格站点数',
                prop: 'provinceVideoUnfinish',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过率',
                prop: 'videoPassRate',
                minWidth: 100,
              },
              {
                label: '得分',
                prop: 'videoPassRateSix',
                minWidth: 100,
              },
            ]
          }
        ]
      },
    ],
    formInline: {
      constructUnit: [],
      discipline: '',
      projectImplementationManagerPrimary:'',
      projectManagementManagerPrimary: '',
      district: '',
      projectName: '',
      constructionUnit: ''
    },
    labelWidth: {
      default: '130px'
    },
    whether: 'UNITDIM',
    cityList: [],
    searchForm: {
      statisticDim: "UNITDIM",
      // date: this.$moment().format('YYYY-MM-DD')
    },
    loadParam: {
      statisticDim: "UNITDIM",
      // date: this.$moment().format('YYYY-MM-DD')
    },
    characters: '',
    firstLevelIndexArr: [],
    secondLevelIndexArr: [],
    indexDescArr: []
  }
},

created() {
  this.notesQuery ()
  this.cityList = JSON.parse(window.sessionStorage.getItem('constructionUnit'))
},
methods: {
  notesQuery () {
    getNotesQuery({code: this.loadParam.statisticDim}).then((res)=> {
      let name = []
      res.data.forEach(item => {
        name.push(item.detail+ '</br>')
      })
      this.characters = name.join('')
    })
  },
  onClose () {
    this.notesQuery()
  },
  getEditNotes () {
    let text ='UNITDIM,PROMANAGEDIM,PROVINCEPROMANAGERDIM,UNITPROMANAGERDIM,UNITCONSTRUCTDIM,UNITCITYCONSTRUCTDIM,UNITPROCONSTRUCTDIM'
    this.$refs.editNotes.open(text)
  },
  changeSelect (item) {
    if (item === 'statisticDim') {
      this.formInline = {
        constructUnit: [],
        discipline: '',
        projectImplementationManagerPrimary: '',
        projectManagementManagerPrimary: '',
        district: '',
        projectName: '',
        constructionUnit: ''
      }
      this.whether = this.$refs.searchForm.searchForm.statisticDim
      this.search()
      this.notesQuery()
    }
  },
  search() {
    this.$refs[this.whether].page.current = 1
    let form = {
      ...this.formInline,
      ...this.$refs.searchForm.searchForm,
      constructUnit: this.formInline.constructUnit.toString()
    }
    this.loadParam = {...form}
    this.$refs[this.whether].getTableData(form);
  },
  reset() {
    this.formInline = {
      constructUnit: [],
      discipline: '',
      projectImplementationManagerPrimary: '',
      projectManagementManagerPrimary: '',
      district: '',
      projectName: '',
      constructionUnit: ''
    }
    this.$refs.searchForm.searchForm.statisticDim = 'UNITDIM'
    this.$refs.searchForm.searchForm.date = ''
    this.whether = 'UNITDIM'
    this.search()
  },
  exportMethod(val) {
    if (val == 1) {
      let form = {
        ...this.formInline,
        ...this.$refs.searchForm.searchForm,
        constructUnit: this.formInline.constructUnit.toString(),
        ...this.$refs[this.whether].page,
        exportFlag: false
      }
      commonDown({ ...form, limit: -1}, QIPexport)
      // QIPexport(form).then(res => {
      //   let fileName = "";
      //   fileName = " QIP视频验收推进情况报表.xlsx";
      //   const url = window.URL.createObjectURL(new Blob([res.data]));
      //   const link = document.createElement('a');
      //   link.href = url;
      //   link.setAttribute('download', fileName);
      //   document.body.appendChild(link);
      //   link.click();
      // })
    } else {
      let form = {
        ...this.formInline,
        ...this.$refs.searchForm.searchForm,
        constructUnit: this.formInline.constructUnit.toString(),
        exportFlag: true
      }
      commonDown({ ...form, limit: -1}, exportVideoStatisticsByDim)
      // exportVideoStatisticsByDim(form).then(res => {
      //   let fileName = "";
      //   fileName = "QIP视频验收推进情况报表.xlsx";
      //   const url = window.URL.createObjectURL(new Blob([res.data]));
      //   const link = document.createElement('a');
      //   link.href = url;
      //   link.setAttribute('download', fileName);
      //   document.body.appendChild(link);
      //   link.click();
      // })
    }
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>