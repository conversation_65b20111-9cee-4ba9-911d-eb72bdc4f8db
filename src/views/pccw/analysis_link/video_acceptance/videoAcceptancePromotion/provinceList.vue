<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
      @openDialog="openDialog"
    ></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <!-- v-if -->
        <el-button  type="primary" @click="exportMethod">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          selection
        >
        </mssTable>
        <div>
          1.已完工需进行视频验收站点-规模：工单数据有完工时间的任务个数<br>
          2.预警站点（完工10天内未完成视频验收）：是否超期里超期时间≤10天并且标签是否验收通过为不通过+未验收+为空的任务个数<br>
          3.预警站点（完工20天内未完成视频验收）：是否超期里超期时间≤20天并且标签是否验收通过为不通过+未验收+为空的任务个数<br>
          4.预警站点（完工30天内未完成视频验收）：是否超期里超期时间≤30天并且标签是否验收通过为不通过+未验收+为空的任务个数<br>
          5.超期未完成视频验收站点：是否超期里超期时间＞30天并且标签是否验收通过为不通过+未验收+为空的任务个数<br>
          6.超期占比：超期未完成视频验收站点/已完工需进行视频验收站点-规模<br>
          7.超期完成视频验收站点：是否超期里超期时间＞30天并且标签是否验收通过为通过的任务个数<br>
          8.已完成视频验收并通过站点：标签是否验证通过为通过的任务个数<br>
          9.视频验收完成率：已完成视频验收并通过站点/已完工需进行视频验收站点-规模<br>
          10.视频验收通过站点数：工单数据标签是否验收通过为“通过”的任务数<br>
          11.一次性视频验收通过站点数：工单数据标签是否验收通过为“通过”，验收未通过为“0”的任务数<br>
          12.省公司抽查发现的不合格站点数：视频验收抽查的，工单审核不合格的数量<br>
          13.一次性视频验收通过率：（一次性视频验收通过站点数-省公司抽查发现的不合格站点数）/视频验收通过站点数<br>
          14.一次性视频验收通过率-得分：一次性视频验收通过率*6<br>
        </div>
      </div>
    </mssCard>
  </div>
</template>

<script>

import {listSafetyPaymentQuery, exportPageService} from '@/api/pccw/implementation_link/safety_payment_query/query_list';
import { commonDown } from '@/utils/btn';
// 获取地市:queryAreaListService
import {queryAreaListService} from '@/api/common_api.js'
export default {
name: 'lpsVideoAcceptanceExempt',
data() {
  return{
    //权限
    powerData: [],
    // 表格数据API
    tableApi: listSafetyPaymentQuery,
    //搜索字段配置
    searchConfig: [
      {
        label: '统计维度',
        type: 'select',
        fieldName: 'taskNumber',
        itemAs: true,
        options: [1,2,3]
      },
      {
        label: '日期',
        type: 'date1',
        fieldName: 'declarationDate'
      },
    ],
    //表头
    tableHeader: [
      {
        prop: "titleName",
        label: 'QIP视频验收推进进展（省管项目经理）',
        multilevelColumn: [
          {
            label: '项目经理',
            prop: 'discipline',
            minWidth: 100,
          },
          {
            prop: "titleName",
            label: '已完工需进行视频验收站点',
            multilevelColumn:[
              {
                label: '规模',
                prop: 'videoFinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工10天内未完成视频验收)',
                prop: 'videoTenunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工20天内未完成视频验收)',
                prop: 'videoTwnunfinishScale',
                minWidth: 100,
              },
              {
                label: '预警站点(完工30天内未完成视频验收)',
                prop: 'videoThiunfinishScale',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '超期占比',
                prop: 'videoUnfinishRate',
                minWidth: 100,
              },
              {
                label: '超期未完成视频验收站点',
                prop: 'videoUnfinishScale',
                minWidth: 100,
              },
              {
                label: '已完成视频验收并通过站点',
                prop: 'videofinishCount',
                minWidth: 100,
              },
              {
                label: '视频验收完成率',
                prop: 'videoRate',
                minWidth: 100,
              },
            ]
          },
          {
            prop: "titleName",
            label: '一次性视频验收通过率',
            multilevelColumn: [
              {
                label: '视频验收通过站点数',
                prop: 'videoCount',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过站点数',
                prop: 'videoPass',
                minWidth: 100,
              },
              {
                label: '省公司抽查发现的不合格站点数',
                prop: 'provinceVideoUnfinish',
                minWidth: 100,
              },
              {
                label: '一次性视频验收通过率',
                prop: 'videoPassRate',
                minWidth: 100,
              },
              {
                label: '得分',
                prop: 'videoPassRateSix',
                minWidth: 100,
              },
            ]
          }
        ]
      },
    ]
  }
},

created() {
},
methods: {
  //打开查询条件弹窗
  openDialog(val) {
    if (val == 'projectCodeDialog') {

    } else if (val == 'projectNameDialog') {

    } else if (val == 'taskNameDialog') {

    } else {

    }
  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },

  reset() {
    this.search()
  },
  exportMethod() {
    const params = {
      ...this.$refs.searchForm.searchForm,
      limit: this.$refs.table.page.size,
      page: this.$refs.table.page.current,
      exportType: 'all'
    }
    commonDown(params, exportPageService)
  },
},

}
</script>

<style>

</style>