<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
          <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              border
            >
            </mssTable>
          </div>
        </mssCard>
    </div>
</template>

<script>
import { commonDown } from '@/utils/btn'
import { getCityList } from '@/api/pccw/report_select/quarter_transfer_task.js'
import { getAllDetail, exportDesignStatisticsAllDetail } from '@/api/pccw/analysis_link/design_quality_reports_alters/api'
export default {
name: 'detail',
data() {
  return {
    tableApi: getAllDetail,
    searchConfig:[
      {
        label: '地市',
        type: 'select',
        fieldName: 'city'
      },
      {
        label: '项目编码',
        type: 'input',
        fieldName: 'projectCode',
      },
      {
        label: '项目名称',
        type: 'input',
        fieldName: 'projectName',
      },
      {
        label: '设计单位',
        type: 'input',
        fieldName: 'designCompany',
      },
      {
          label: '会审时间',
          type: 'date1',
          fieldName: 'auditTime'
      },
      {
        label: '年份',
        type: 'select',
        fieldName: 'projectYear'
      },      
    ],
    tableHeader:[
      {
        prop: "auditTime",
        label: "会审时间",
        align: "center",
        tooltip: true,
        width: 150
      },
      {
          prop: "designApprovalTime",
          label: "设计批复时间",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "batch",
          label: "批次",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "auditOrganizer",
          label: "会审组织人",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "specialty",
          label: "专业",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "city",
          label: "地市",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "taskName",
          label: "任务名称",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "taskCode",
          label: "任务编码",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "issueCategory",
          label: "发现问题类别",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "issueDescription",
          label: "问题具体描述",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "constructorPm",
          label: "涉及建设单位项目经理",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "designCompany",
          label: "涉及设计单位",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "designPersonnel",
          label: "涉及设计人员",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "remarks",
          label: "备注",
          align: "center",
          tooltip: true,
          width: 150
      },
      {
          prop: "totalTasks",
          label: "总任务数",
          align: "center",
          tooltip: true,
          width: 150
      }
    ],
  };
},
methods: {
  //导出
  exportHandle() {
      commonDown({ ...this.$refs.searchForm.searchForm, limit: -1, exportType: 2}, exportDesignStatisticsAllDetail);
  },
  //重置
  reset(form) {
      this.search(form)
  },
  //搜索
  search() {
      this.$refs.table.page.current = 1
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  getAreaList (parentId, index) {
    getCityList()
      .then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({label: item.name, value: item.name})
          })
          // this.searchConfig[0].options = list
          this.$set(this.searchConfig[0], 'options', list)
        }
      })
  }
    // Your methods go here
},
mounted() {
  this.getAreaList('-2', 0)
  let list = [{label: 2018, value: 2018},{label: 2019, value: 2019},{label: 2020, value: 2020},
  {label: 2021, value: 2021},{label: 2022, value: 2022},{label: 2023, value: 2023},{label: 2024, value: 2024},
  {label: 2025, value: 2025},{label: 2026, value: 2026},{label: 2027, value: 2027},{label: 2028, value: 2028}]
  this.$set(this.searchConfig[5], 'options', list)
    // Code to run when the component is mounted
},
};
</script>

<style scoped>
/* Your component styles go here */
</style>
