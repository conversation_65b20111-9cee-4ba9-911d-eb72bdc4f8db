<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
          <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              ref="table"
              :columns="tableHeader"
              :api="tableApi"
              border
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </div>
        </mssCard>
    </div>
</template>

<script>
import { commonDown } from '@/utils/btn'
import { getCityList } from '@/api/pccw/report_select/quarter_transfer_task.js'
import { getAreaDetails, exportDesignStatisticsArea } from '@/api/pccw/analysis_link/design_quality_reports_alters/api'
export default {
name: 'cityList',
data() {
  return {
    tableApi: getAreaDetails,
    loadParam: {},  // 用于存储搜索条件
    searchConfig:[
      {
        label: '地市',
        type: 'select',
        fieldName: 'city',
      },
      {
        label: '会审时间段',
        type: 'week',
        fieldName: 'selectDate',
      },    
    ],
    tableHeader:[
      {
        prop: "city",
        label: "地市",
        align: "center",
        tooltip: true,
        width: 150
      },
      {
        prop: "",
        label: "设计批复站点数",
        align: "center",
        tooltip: true,
        multilevelColumn: [
          {
            prop: "addSitesThisWeek",
            label: "本周新增站点数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "clockSitesThisWeek",
            label: "本周打卡站点数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "allAddSites",
            label: "累计站点数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "allClockSites",
            label: "累计打卡站点数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
        ]
      },
      {
        prop: "",
        label: "打卡情况",
        align: "center",
        tooltip: true,
        multilevelColumn: [
          {
            prop: "addClockThisWeek",
            label: "本周新增打卡数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "clockinRateThisWeek",
            label: "本周打卡率",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "clockinRateAccumulated",
            label: "累计打卡率",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "chain",
            label: "环比",
            align: "center",
            tooltip: true,
            minWidth: 150
          }
        ]
      },
      {
          prop: "designDisclosureRingRatio",
          label: "设计审核通过",
          align: "center",
          tooltip: true,
          multilevelColumn: [
            {
              prop: "designTotalThisWeek",
              label: "本周新增",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "designStatisticsTotalTasks",
              label: "总任务数(累计)",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "designStatisticsPassNum",
              label: "一次通过数",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "designStatisticsFailedNum",
              label: "一次未通过数",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "designStatisticsReviewThisWeekPassRate",
              label: "本周一次通过率",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "designStatisticsReviewAllPassRate",
              label: "累计一次通过率",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "designStatisticsChain",
              label: "环比",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
          ]
      },
    ],
  };
},
methods: {
  exportHandle () {
    this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
    this.loadParam.startTime = this.loadParam.startDate ? this.loadParam.startDate : ''
    this.loadParam.endTime = this.loadParam.endDate ? this.loadParam.endDate : ''
    this.loadParam.startDate = ''
    this.loadParam.endDate = ''
    commonDown({ ...this.loadParam, limit: -1, exportType: 2}, exportDesignStatisticsArea);
  },
  reset(form) {
      form.startDate = ''
      form.endDate = ''
      this.search(form)
  },
  //搜索
  search() {
      this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      this.loadParam.startTime = this.loadParam.startDate ? this.loadParam.startDate : ''
      this.loadParam.endTime = this.loadParam.endDate ? this.loadParam.endDate : ''
      this.loadParam.startDate = ''
      this.loadParam.endDate = ''
      this.$refs.table.getTableData(this.loadParam);
  },
  getAreaList (parentId, index) {
    getCityList()
      .then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({label: item.name, value: item.name})
          })
          // this.searchConfig[0].options = list
          this.$set(this.searchConfig[0], 'options', list)
        }
      })
  }
    // Your methods go here
},
mounted() {
  this.getAreaList('-2', 0)
    // Code to run when the component is mounted
},
};
</script>

<style scoped>
/* Your component styles go here */
</style>
