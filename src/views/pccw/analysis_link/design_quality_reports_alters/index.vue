<template>
    <div>  
      <div class="exemptionList">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="详情" name="detail" lazy>
            <detail ref="detail"></detail>
          </el-tab-pane>
          <el-tab-pane label="设计单位" name="designUnit" lazy>
            <designUnit ref="designUnit"></designUnit>
          </el-tab-pane>
          <el-tab-pane label="地市" name="cityList" lazy>
            <cityList ref="cityList"></cityList>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
</template>

<script>
import detail from "@/views/pccw/analysis_link/design_quality_reports_alters/detail.vue";
import designUnit from "@/views/pccw/analysis_link/design_quality_reports_alters/designUnit.vue";
import cityList from "@/views/pccw/analysis_link/design_quality_reports_alters/cityList.vue";
export default {
    name: 'exemptionList',
    components: {
      detail,
      designUnit,
      cityList
    },
    data(){
        return {
          activeName: 'detail',
        }
    },
    methods: {     
      handleClick () {

      },  
    },
}
</script>

<style scoped>
/* Your component's CSS code goes here */
</style>
