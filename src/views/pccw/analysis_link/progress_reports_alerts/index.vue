<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :form="''"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
            <div slot="headerBtn">
                <el-button type="primary" @click="exportHandle">导出</el-button>
            </div>
            <div slot="content">
                <mssTable
                  ref="table"
                  :api="tableApi"
                  :columns="tableHeader"
                  border
                  selection
                >
                </mssTable>
            </div>
        </mssCard>
    </div>
</template>

<script>
export default {
    name: 'ProgressReportsAlerts',
    data() {
        return {
            //搜索字段配置
            searchConfig: [
                {
                    label: '省份',
                    type: 'input',
                    fieldName: 'province',
                },
                {
                    label: '地市',
                    type: 'input',
                    fieldName: 'city'
                },
                {
                    label: '设计单位',
                    type: 'input',
                    fieldName: 'designOrg'
                },
            ],

            //表格头部配置
            tableHeader: [
                {},
                {},
                {},
            ],
        };
    },
    methods: {
        // Your methods here
    },
    mounted() {
        // Code to run when the component is mounted
    },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
