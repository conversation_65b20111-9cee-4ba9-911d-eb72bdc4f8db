<template>
    <div>
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="按地市" name="city" >
          <byCity ref="city"></byCity>
        </el-tab-pane>
        <el-tab-pane label="按设计院" name="designUnit"  lazy>
          <bydesignUnit ref="designUnit"></bydesignUnit>
        </el-tab-pane>
        <el-tab-pane label="详情" name="detail"  lazy>
          <byDetail ref="detail"></byDetail>
        </el-tab-pane>
      </el-tabs>
    </div>
</template>

<script>

import byCity from './city.vue'
import bydesignUnit from './designUnit.vue'
import byDetail from './detail.vue'

export default {
    name: 'DesignReviewReport',
    components: {
      byCity,
      bydesignUnit,
      byDetail
    },
    data() {
        return {
          activeName: 'city',
           
        }

    },


    methods: {
      handleClick() {

      },
    }
    
}
</script>

<style scoped>

</style>
