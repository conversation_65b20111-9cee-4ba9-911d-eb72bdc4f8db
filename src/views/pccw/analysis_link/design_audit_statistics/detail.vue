<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
        <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
        </div>
        <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              border
              selection
            >
            </mssTable>
        </div>
    </mssCard>
  </div>
</template>

<script>
import { commonDown } from '@/utils/btn'
import {tableList, downloadService} from '@/api/pccw/analysis_link/design_review_report/api.js'

export default {
    name: 'Detail',
    data() {
      return {
            tableApi: tableList,
            searchConfig:[
              {
                  label: '地市',
                  type: 'select',
                  fieldName: 'city'
              },

              {
                  label: '项目编码',
                  type: 'input',
                  fieldName: 'projectCode'
              },

              {
                  label: '项目名称',
                  type: 'input',
                  fieldName: 'projectName'
              },
              {
                  label: '设计单位',
                  type: 'input',
                  fieldName: 'designUnit'
              },
            ],
            tableHeader:[
                {
                  prop: "area",
                  label: "会审时间",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "projectCode",
                  label: "批复时间",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "projectName",
                  label: "批次",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "projectImplementManager",
                  label: "会审组织人",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "projectManager",
                  label: "专业",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "orderNumber",
                  label: "地市",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "contractNumber",
                  label: "项目名称",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "",
                  label: "项目编码",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "orderDemandPerson",
                  label: "任务名称",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "",
                  label: "任务编码",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "orderDemandTime",
                  label: "发现问题类别",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "materialCode",
                  label: "问题具体描述",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "materialName",
                  label: "涉及设计单位项目经理",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "materialUnit",
                  label: "涉及设计单位",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "quantity",
                  label: "设计设计人员",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "receiveQuantity",
                  label: "备注",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "receiveDate",
                  label: "总任务数",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
            ] 
      };
    },
    methods: {
      //导出
      exportHandle() {
            commonDown({ ...this.loadParam, limit: -1}, downloadService);
      },
      //重置
      reset(form) {
          this.search(form)
      },
      //搜索
      search() {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
      },
    },
    mounted() {
      // Code to run when the component is mounted
    },
};
</script>

<style scoped>
/* Your component styles here */
</style>
