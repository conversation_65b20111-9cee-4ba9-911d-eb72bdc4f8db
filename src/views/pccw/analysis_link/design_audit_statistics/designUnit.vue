<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
        <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
        </div>
        <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              border
              selection
            >
            </mssTable>
        </div>
    </mssCard>
  </div>
</template>

<script>
import { commonDown } from '@/utils/btn'
import {tableList, downloadService} from '@/api/pccw/analysis_link/design_review_report/api.js'
export default {
    name: 'DesignUnit',
    data() {
      return {
          tableApi: tableList,
          searchConfig:[
            {
                label: '设计单位',
                type: 'input',
                fieldName: 'designUnit'
            },
          ],
          tableHeader:[
          {
                prop: "designUnit",
                label: "设计单位",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                prop: "projectName",
                label: "项目名称",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                prop: "projectCode",
                label: "项目编码",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                prop: "taskCode",
                label: "任务编码",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                prop: "designReviewNotPassCount",
                label: "设计审查一次未通过数",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                prop: "designReviewNotPassRate",
                label: "设计审查一次通过率",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
          ]    
      };
    },
    methods: {
      //导出
      exportHandle() {
            commonDown({ ...this.loadParam, limit: -1}, downloadService);
      },
      //重置
      reset(form) {
          this.search(form)
      },
      //搜索
      search() {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
      },
    },
    mounted() {
      // Code to run when the component is mounted
    },
};
</script>

<style scoped>
/* Your component styles here */
</style>
