<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-upload
          style="display: inline-block;margin-left: 10px;"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportHandleTemp" style="margin-left: 10px;">下载导入模板</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :serial="true"
          :staticSearchParam="staticSearchParam"
          v-if="isReload"
        >
        </mssTable>
        <el-dialog title="错误信息" :visible.sync="dialogTableVisible">
          <el-table :data="gridData" border>
            <el-table-column property="lineNum" label="行数" width="100"></el-table-column>
            <el-table-column property="message" label="信息" ></el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogTableVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogTableVisible = false">确 定</el-button>
          </div>
        </el-dialog>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {commonDown} from "@/utils/btn";
import {
  queryHolidaysList,downloadHolidaysTemp,importHolidays,deleteHolidaysById
} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare";

export default {
  name: 'CompletionRecord',
  props: {
    options: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      isReload: true,
      staticSearchParam: {},
      tableApi:queryHolidaysList,
      gridData:[],
      dialogTableVisible:false,
      searchConfig:[
        {
          label: '年份',
          type: 'year',
          fieldName: 'year',
          valueFormat:'yyyy'
        },
      ],
      tableHeader:[
        {
          prop: "holidaysTime",
          label: "节假日期",
        },
        {
          prop: "deleteFlag",
          label: "是否生效",
          formatter:(row) =>{
            return (
              <span>
                {row.deleteFlag === 0?'生效':row.deleteFlag === 1? '失效':''}
              </span>
            )
          }
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '60px',
          formatter: (row) => {
            return ( <
                span > {
                <
                  span class = 'table_btn mr10'
                       onClick = {
                         () => {
                           this.delRow(row)
                         }
                       } >
                  删除 <
                  /span>
              } <
                /span>
            )
          }
        }

      ],
    };
  },
  methods: {
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...form }
      this.$refs.table.getTableData(form)
      console.log(this.staticSearchParam)
    },
    reset(form) {
      this.search(form)
    },
    importFile(params) {
      const param = new FormData()
      param.append('files', params.file)
      importHolidays(param).then((res) => {
        if (res.code === '5000') {
          this.$message.warning(res.msg)
        } else if (res.code === '0000') {
          if (res.data.length > 0){
            this.dialogTableVisible = true
            this.gridData = res.data
          }else {
            this.$message.success('导入成功')
          }
          this.$refs.commonTable.getTableData()
        }
      })
    },
    exportHandleTemp() {
      commonDown({ ...this.staticSearchParam}, downloadHolidaysTemp);
    },
    async delRow(row){
      const res=await deleteHolidaysById(row.id)
      if(res.code==="0000"){
        console.log("删除成功")
        // 刷新页面
        this.isReload = false;
        await this.$nextTick();
        this.isReload = true;
      }
    },
  },
  mounted() {

  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
