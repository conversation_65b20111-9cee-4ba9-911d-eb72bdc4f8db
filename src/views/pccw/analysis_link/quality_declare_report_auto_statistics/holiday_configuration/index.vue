<template>
  <div>
    <div slot="content">
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="节假日配置" name="holidayConfiguration" lazy>
            <byHolidayConfiguration
            ></byHolidayConfiguration>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>

import byHolidayConfiguration from "@/views/pccw/analysis_link/quality_declare_report_auto_statistics/holiday_configuration/holiday_configuration.vue";

export default {
  name: 'holidayConfiguration',
  components: {
    byHolidayConfiguration,
  },
  data() {
    return {
      options:[],
      activeName: 'holidayConfiguration',
    };
  },
  created() {

  },
  methods: {
    handleClick() {

    },

  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
