<template>
  <div>
    <div slot="content">
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="竣工备案整合表" name="completionRecordIntergration" lazy>
            <byCompletionRecordIntergration
            ></byCompletionRecordIntergration>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>

import byCompletionRecordIntergration from "@/views/pccw/analysis_link/quality_declare_report_auto_statistics/completion_record_integration/completion_record_intergration.vue";

export default {
  name: 'completionRecordIntergration',
  components: {
    byCompletionRecordIntergration,
  },
  data() {
    return {
      options:[],
      activeName: 'completionRecordIntergration',
    };
  },
  created() {

  },
  methods: {
    handleClick() {

    },

  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
