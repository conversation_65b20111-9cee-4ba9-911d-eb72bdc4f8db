<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-upload
          style="display: inline-block;margin-left: 10px;"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportHandleTemp" style="margin-left: 10px;">下载导入模板</el-button>
        <el-button type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :serial="true"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
        <el-dialog title="错误信息" :visible.sync="dialogTableVisible">
          <el-table :data="gridData" border>
            <el-table-column property="lineNum" label="行数" width="100"></el-table-column>
            <el-table-column property="message" label="信息" ></el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogTableVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogTableVisible = false">确 定</el-button>
          </div>
        </el-dialog>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {commonDown} from "@/utils/btn";
import {
  qualityRecordSetList, exportQualityRecord,downloadRecordComSetTemp,importLoadRecordCom
} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare";

export default {
  name: 'CompletionRecord',
  props: {
    options: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      staticSearchParam: {},
      tableApi:qualityRecordSetList,
      gridData:[],
      dialogTableVisible:false,
      searchConfig:[
        {
          label: '建设单位',
          type: 'input',
          fieldName: 'constructionUnit',
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'erpProjectCode'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectMatchName'
        },
        {
          label: '终验时间',
          type: 'month',
          fieldName: 'finalTimeQuery',
        },
        {
          label: '备案名称',
          type: 'input',
          fieldName: 'forRecordProjectName'
        },
        {
          label: '备案单位',
          type: 'input',
          fieldName: 'forRecordUnitName'
        },
        {
          label: '备案时间',
          type: 'month',
          fieldName: 'forRecordTimeQuery',
        },
        {
          label: '审批状态',
          type: 'select',
          fieldName: 'status',
          options: [
            {
              label: '不予审批',
              value: '0'
            },
            {
              label: '待审批',
              value: '1'
            },
            {
              label: '审批通过',
              value: '2'
            },
            {
              label: '已撤回',
              value: '3'
            },
          ]
        },
        // {
        //   label: '是否重复备案',
        //   type: 'select',
        //   fieldName: 'qualifiedStatus',
        //   options: [
        //     {
        //       label: '是',
        //       value: '1'
        //     },
        //     {
        //       label: '否',
        //       value: '0'
        //     },
        //   ]
        // },
        {
          label: '是否减免',
          type: 'select',
          fieldName: 'exemption',
          options: [
            {
              label: '是',
              value: '0'
            },
            {
              label: '否',
              value: '1'
            },
          ]
        },

      ],

      tableHeader:[
        {
          prop: "constructionUnit",
          label: "建设单位",
          minWidth: 150
        },
        {
          prop: "erpProjectCode",
          label: "ERP项目编码",
          minWidth: 150
        },
        {
          prop: "forRecordProjectName",
          label: "备案名称",
          minWidth: 150
        },
        {
          prop: "projectMatchName",
          label: "项目名称",
          minWidth: 150
        },

        {
          prop: "finalTime",
          label: "终验时间",
          minWidth: 150,
          formatter:(row) =>{
            return (
              <span>
                {row.finalTime?this.$moment(row.finalTime).format('yyyy-MM-DD'):''}
              </span>
            )
          }
        },
        {
          prop: "forRecordUnitName",
          label: "备案单位",
          minWidth: 150
        },
        {
          prop: "forRecordTime",
          label: "备案时间",
          minWidth: 150,
          formatter:(row) =>{
            return (
              <span>
                {row.forRecordTime?this.$moment(row.forRecordTime).format('yyyy-MM-DD'):''}
              </span>
            )
          }
        },
        {
          prop: "statusDesc",
          label: "审批状态",
          minWidth: 100,
        },
        {
          prop: "interProvincial",
          label: "是否跨省/市",
          minWidth: 80
        },
        {
          prop: "repeatRecord",
          label: "是否重复备案",
          minWidth: 80,
          formatter:(row) =>{
            return (
              <span>
                {row.repeatRecord === '0'?'是':row.repeatRecord === '1'? '否':''}
              </span>
            )
          }
        },
        {
          prop: "exemption",
          label: "是否减免",
          minWidth: 80,
          formatter:(row) =>{
            return (
              <span>
                {row.exemption === 0?'是':row.exemption === 1? '否':''}
              </span>
            )
          }
        },
      ],
    };
  },
  methods: {
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...form }
      this.$refs.table.getTableData(form)
      console.log(this.staticSearchParam)
    },
    reset(form) {
      this.search(form)
    },
    importFile(params) {
      const param = new FormData()
      param.append('files', params.file)
      importLoadRecordCom(param).then((res) => {
        if (res.code === '5000') {
          this.$message.warning(res.msg)
        } else if (res.code === '0000') {
          if (res.data.length > 0){
            this.dialogTableVisible = true
            this.gridData = res.data
          }else {
            this.$message.success('导入成功')
          }
          this.$refs.commonTable.getTableData()
        }
      })
    },
    exportHandle() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, exportQualityRecord);
    },
    exportHandleTemp() {
      commonDown({ ...this.staticSearchParam}, downloadRecordComSetTemp);
    }
  },
  mounted() {

  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
