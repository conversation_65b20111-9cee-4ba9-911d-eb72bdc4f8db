<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportHandle">导出</el-button>
        <el-button type="primary" @click="exportHandleAll">一键导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="columns"
          border
          :serial="false"
          :pagination="false"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>

import {commonDown} from "@/utils/btn";
import {recordStatistics, recordStatisticsTotalExport,recordStatisticsSheetDownload} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare";
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      staticSearchParam: {},
      searchConfig:[
        {
          label: '建设单位',
          type: 'input',
          fieldName: 'constructionUnit',
        },
        {
          label: '年度',
          type: 'year',
          fieldName: 'year',
          valueFormat:'yyyy'
        },
      ],
      tableApi:recordStatistics,
      completionRecordTitle:"",
    };
  },
  computed:{
    columns:{
      get(){
        return [
          {
            prop: "",
            label: this.completionRecordTitle,
            multilevelColumn: [
              {
                prop: "constructionUnit",
                label: "建设单位",
                minWidth: 120
              },
              {
                prop: "finalProjectNum",
                label: "终验项目数",
                minWidth: 80
              },
              {
                prop: "yesProjectNum",
                label: "可备案项目数",
                minWidth: 80
              },
              {
                prop: "inTimeProjectNum",
                label: "已及时备案",
                minWidth: 80
              },
              {
                prop: "overdueNoRecordProjectNum",
                label: "超期未备案数",
                minWidth: 80
              },
              {
                prop: "inTimePassRate",
                label: "备案及时率(%)",
                minWidth: 80
              }
            ]
          }
        ]
      }
    }

  },
  created() {
    this.getTitle(new Date().getFullYear())
  },
  methods: {
    // Your methods here
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.getTableData(form)
      this.getTitle(this.staticSearchParam.year)
      console.log(this.staticSearchParam)
    },
    reset(form) {
      this.search(form)
    },
    exportHandle() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, recordStatisticsTotalExport);
    },
    exportHandleAll() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, recordStatisticsSheetDownload);
    },
    getTitle(data) {
      this.completionRecordTitle = data + "年终验项目竣工备案情况"
    }
  },
  mounted() {
    // Code to run when the component is mounted
  },

};
</script>

<style scoped>
/* Your CSS styles here */
</style>
