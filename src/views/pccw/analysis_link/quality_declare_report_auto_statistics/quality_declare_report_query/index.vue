<template>
  <div>
    <div slot="content">
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="质监申报汇总" name="qualityDeclare" lazy>
            <byQualityDeclare
            ></byQualityDeclare>
          </el-tab-pane>
          <el-tab-pane label="竣工备案汇总" name="completionRecord" lazy>
            <byCompletionRecord
            ></byCompletionRecord>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>

import byQualityDeclare from "@/views/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_query/quality_declare_sum.vue";
import byCompletionRecord from "@/views/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_query/completion_record_sum.vue";
import {getConstructionUnit} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics";

export default {
  name: 'qualityDeclare',
  components: {
    byQualityDeclare,
    byCompletionRecord,
  },
  data() {
    return {
      activeName: 'qualityDeclare',
    };
  },
  created() {

  },
  methods: {
    handleClick() {
    },
  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
