<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportHandle">导出</el-button>
        <el-button type="primary" @click="exportHandleAll">一键导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="columns"
          border
          :serial="false"
          :pagination="false"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>

import {commonDown} from "@/utils/btn";
import {reportStatistics, reportStatisticsTotalExport,reportStatisticsSheetDownload} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare";

export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      staticSearchParam: {},
      searchConfig:[
        {
          label: '建设单位',
          type: 'input',
          fieldName: 'constructionUnit',
        },
        {
          label: '年度',
          type: 'year',
          fieldName: 'year',
          valueFormat:'yyyy'
        },
      ],
      tableApi:reportStatistics,
      qualityDeclareTitle:"",
    };
  },
  computed:{
    columns:{
      get(){
        return [
          {
            prop: "",
            label: this.qualityDeclareTitle,
            multilevelColumn: [
              {
                prop: "constructionUnit",
                label: "建设单位",
                minWidth: 120,
              },
              {
                prop: "allNum",
                label: "总计",
                minWidth: 80
              },
              {
                prop: "timeInNum",
                label: "已及时申报",
                minWidth: 80,
              },
              {
                prop: "noFiveDayTimeInNum",
                label: "未及时申报",
                minWidth: 80,
              },
              {
                prop: "noApproved",
                label: "申报不予审批",
                minWidth: 80,
              },
              {
                prop: "timePassRate",
                label: "及时申报率(%)",
                minWidth: 80,
              },
              {
                prop: "reportNameNoQualified",
                label: "申报名称不合格数量",
                minWidth: 80,
              },
              {
                prop: "inRecordNoRecordProjectNum",
                label: "可备案未备案",
                minWidth: 80
              }
            ]
          },
        ]
      }
    }

  },
  created() {
    this.getTitle(new Date().getFullYear())
  },
  methods: {
    // Your methods here
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.getTableData(form)
      this.getTitle(this.staticSearchParam.year)
      console.log(this.staticSearchParam)
    },
    reset(form) {
      this.search(form)
    },
    exportHandle() {
      commonDown({ ...this.staticSearchParam}, reportStatisticsTotalExport);
    },
    exportHandleAll() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, reportStatisticsSheetDownload);
    },
    getTitle(data) {
      this.qualityDeclareTitle = data + "年开工项目质监申报情况"
    }
  },
  mounted() {
    // Code to run when the component is mounted
  },

};
</script>

<style scoped>
/* Your CSS styles here */
</style>
