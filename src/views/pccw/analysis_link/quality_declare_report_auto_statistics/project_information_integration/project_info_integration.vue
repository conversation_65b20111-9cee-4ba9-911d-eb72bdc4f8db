<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :serial="true"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  queryProjectComprehensiveInfoAllList,exportProjectComprehensiveInfoAllList
} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare";
import {commonDown} from "@/utils/btn";

export default {
  name: 'QualityDeclare',
  props: {
    options: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      staticSearchParam: {},
      tableApi:queryProjectComprehensiveInfoAllList,
      searchConfig:[
        {
          label: '建设单位',
          type: 'input',
          fieldName: 'constructionUnit',
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'erpProjectCode'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '开工时间',
          type: 'month',
          fieldName: 'startWorkTimeQuery'
        },
        {
          label: '终验时间',
          type: 'month',
          fieldName: 'finalTimeQuery'
        },
        // {
        //   label: '立项批复时间',
        //   type: 'month',
        //   fieldName: 'startReportCompleteTime1'
        // },
        // {
        //   label: '统一设计批复时间',
        //   type: 'month',
        //   fieldName: 'startReportCompleteTime2'
        // },
        // {
        //   label: '第一批设计批复时间',
        //   type: 'month',
        //   fieldName: 'startReportCompleteTime3'
        // },
        // {
        //   label: '是否批复',
        //   type: 'select',
        //   fieldName: 'qualifiedStatus',
        //   options: [
        //     {
        //       label: '是',
        //       value: '1'
        //     },
        //     {
        //       label: '否',
        //       value: '0'
        //     },
        //   ]
        // },
        // {
        //   label: '设计变更批复完成时间',
        //   type: 'month',
        //   fieldName: 'startReportActualTime'
        // },
        // {
        //   label: '开工时间',
        //   type: 'month',
        //   fieldName: 'startReportActualTime'
        // },
        // {
        //   label: '开工完成（填报）时间',
        //   type: 'month',
        //   fieldName: 'startReportActualTime'
        // },
        // {
        //   label: '完工时间',
        //   type: 'month',
        //   fieldName: 'startReportActualTime'
        // },
        // {
        //   label: '要求完工时间',
        //   type: 'month',
        //   fieldName: 'startReportActualTime'
        // },
        // {
        //   label: '终验时间',
        //   type: 'month',
        //   fieldName: 'startReportActualTime'
        // },
        // {
        //   label: '工程管理经理（主）',
        //   type: 'input',
        //   fieldName: 'projectManager'
        // },
        // {
        //   label: '工程实施经理（主）',
        //   type: 'input',
        //   fieldName: 'builderProjectManager'
        // },
        {
          label: '系统归属',
          type: 'select',
          fieldName: 'type',
          options: [
            {
              label: '省内（老表）',
              value: '1'
            },
            {
              label: '省测（新表）',
              value: '0'
            },
          ]
        },
      ],

      tableHeader:[
        {
          prop: "constructionUnit",
          label: "建设单位",
          minWidth: 120
        },
        {
          prop: "erpProjectCode",
          label: "ERP对应项目编号",
          minWidth: 150
        },
        {
          prop: "erpProjectCloseTimeDesc",
          label: "ERP项目关闭时间",
          minWidth: 150
        },
        {
          prop: "projectName",
          label: "项目名称",
          minWidth: 80
        },
        {
          prop: "standardDurationSpecialtyClassification",
          label: "标准工期分类",
          minWidth: 80
        },
        {
          prop: "approvalTimeDesc",
          label: "立项批复时间",
          minWidth: 80
        },
        {
          prop: "designApprovalTimeDesc",
          label: "统一设计批复时间",
          minWidth: 80
        },
        {
          prop: "firstDesignApprovalTimeDesc",
          label: "第一批设计批复时间",
          width: 80
        },
        // {
        //   prop: "approvalStatus",
        //   label: "是否批复",
        //   minWidth: 80
        // },
        {
          prop: "designChangeApprovalTimeDesc",
          label: "设计批复变更完成时间",
          minWidth: 80
        },
        {
          prop: "startWorkTimeDesc",
          label: "开工时间",
          minWidth: 80
        },
        {
          prop: "startWorkDoneTimeDesc",
          label: "开工完成（填报时间）",
          minWidth: 80
        },
        {
          prop: "completionTimeDesc",
          label: "完工时间",
          minWidth: 80
        },
        {
          prop: "requiredCompletionTimeDesc",
          label: "要求完工时间",
          minWidth: 80
        },
        {
          prop: "finalAcceptanceTimeDesc",
          label: "终验时间",
          minWidth: 80
        },
        {
          prop: "projectImplementationManagerPrimary",
          label: "工程实施经理（主）",
          minWidth: 80
        },
        {
          prop: "projectManagementManagerPrimary",
          label: "工程管理经理（主）",
          minWidth: 80
        },
        {
          prop: "systemOwnership",
          label: "系统归属",
          minWidth: 80
        },

      ],
    };
  },
  created() {
  },
  methods: {
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...form }
      this.$refs.table.getTableData(form)
      console.log(this.staticSearchParam)
    },
    reset(form) {
      this.search(form)
    },
    exportHandle() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, exportProjectComprehensiveInfoAllList);
    }
  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your component styles here */
</style>
