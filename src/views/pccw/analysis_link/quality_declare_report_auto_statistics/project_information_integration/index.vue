<template>
  <div>
    <div slot="content">
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="项目信息整合表" name="projectInfoIntegration" lazy>
            <byProjectInfoIntegration
            ></byProjectInfoIntegration>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>

import byProjectInfoIntegration from "@/views/pccw/analysis_link/quality_declare_report_auto_statistics/project_information_integration/project_info_integration.vue";

export default {
  name: 'projectInfoIntegration',
  components: {
    byProjectInfoIntegration,
  },
  data() {
    return {
      activeName: 'projectInfoIntegration',
    };
  },
  created() {

  },
  methods: {
    handleClick() {

    },
  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
