<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-upload
          style="display: inline-block;margin-left: 10px;"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportHandleTemp" style="margin-left: 10px;">下载导入模板</el-button>
        <el-button type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :serial="true"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
    <el-dialog title="错误信息" :visible.sync="dialogTableVisible">
      <el-table :data="gridData" border>
        <el-table-column property="lineNum" label="行数" width="100"></el-table-column>
        <el-table-column property="message" label="信息" ></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogTableVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import {
  qualityInspectionSetList,exportQualityInspection, downloadInspectionSetTemp, importQualityLnspection
} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare";
import {commonDown} from "@/utils/btn";


export default {
  name: 'qualityDeclareIntergration',
  props: {
    options: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      dialogTableVisible:false,
      gridData:[],
      staticSearchParam: {},
      tableApi:qualityInspectionSetList,
      searchConfig:[
        {
          label: '建设单位',
          type: 'input',
          fieldName: 'constructionUnit'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'erpProjectCode'
        },
        {
          label: '申报名称',
          type: 'input',
          fieldName: 'pmsProjectName'
        },
        {
          label: "项目名称",
          type: 'input',
          fieldName: "projectMatchName"
        },
        {
          label: '开工时间',
          type: 'month',
          fieldName: 'startTimeQuery',
        },
        {
          label: '申报单位',
          type: 'input',
          fieldName: 'reportUnitName'
        },
        {
          label: '申报人',
          type: 'input',
          fieldName: 'reportPersonName'
        },
        {
          label: '申报日期',
          type: 'month',
          fieldName: 'reportTimeQuery',

        },
        {
          label: '审批状态',
          type: 'select',
          fieldName: 'status',
          options: [
            {
              label: '不予审批',
              value: '1'
            },
            {
              label: '待审批',
              value: '2'
            },
            {
              label: '已下发通知书',
              value: '0'
            },
            {
              label: '已撤销',
              value: '3'
            },
            {
              label: '已撤回',
              value: '4'
            },
          ]
        },
        {
          label: '申报名称是否合格',
          type: 'select',
          fieldName: 'qualityStatus',
          options: [
            {
              label: '合格',
              value: '0'
            },
            {
              label: '不合格',
              value: '1'
            },
          ]
        },
        {
          label: '是否减免',
          type: 'select',
          fieldName: 'exemptionStatus',
          options: [
            {
              label: '是',
              value: '0'
            },
            {
              label: '否',
              value: '1'
            },
          ]
        },

      ],

      tableHeader:[
        {
          prop: "constructionUnit",
          label: "建设单位",
          minWidth: 120
        },
        {
          prop: "erpProjectCode",
          label: "ERP项目编码",
          minWidth: 150
        },
        {
          prop: "pmsProjectName",
          label: "申报名称",
          minWidth: 150
        },
        {
          prop: "projectMatchName",
          label: "项目名称",
          minWidth: 150
        },
        {
          prop: "startTime",
          label: "开工时间",
          minWidth: 80,
          formatter:(row) =>{
            return (
              <span>
                {row.startTime?this.$moment(row.startTime).format('yyyy-MM-DD'):''}
              </span>
            )
          }
        },
        {
          prop: "reportUnitName",
          label: "申报单位",
          minWidth: 80
        },
        {
          prop: "provincesName",
          label: "省份",
          minWidth: 80
        },
        {
          prop: "cityName",
          label: "地市",
          minWidth: 70
        },
        {
          prop: "reportPersonName",
          label: "申报人",
          minWidth: 80
        },
        {
          prop: "reportTime",
          label: "申报日期",
          minWidth: 80,
          formatter:(row) =>{
            return (
              <span>
                {row.reportTime ? this.$moment(row.reportTime).format('yyyy-MM-DD'):''}
              </span>
            )
          }
        },
        {
          prop: "approvalProjectAmount",
          label: "立项批复投资额（万元）",
          minWidth: 80
        },
        {
          prop: "statusDesc",
          label: "审批状态",
          minWidth: 80
        },
        {
          prop: "interProvincial",
          label: "是否跨省/市",
          minWidth: 80
        },
        {
          prop: "qualityDesc",
          label: "是否合格",
          minWidth: 80
        },
        {
          prop: "exemptionStatus",
          label: "是否减免",
          minWidth: 80,
          formatter:(row) =>{
            return (
              <span>
                {row.exemptionStatus === 0?'是':row.exemptionStatus === 1? '否':''}
              </span>
            )
          }
        },
        {
          prop: "remark",
          label: "备注",
          minWidth: 80
        }

      ],
    };
  },
  methods: {
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...form }
      this.$refs.table.getTableData(form)
      console.log(this.staticSearchParam)
    },
    reset(form) {
      this.search(form)
    },
    importFile(params) {
      const param = new FormData()
      param.append('files', params.file)
      importQualityLnspection(param).then((res) => {
        if (res.code === '5000') {
          this.$message.warning(res.msg)
        } else if (res.code === '0000') {
          if (res.data.length > 0){
            this.dialogTableVisible = true
            this.gridData = res.data
          }else {
            this.$message.success('导入成功')
          }
          this.$refs.commonTable.getTableData()
        }
      })
    },
    exportHandle() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, exportQualityInspection);
    },
    exportHandleTemp() {
      commonDown({ ...this.staticSearchParam}, downloadInspectionSetTemp);
    }
  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your component styles here */
</style>
