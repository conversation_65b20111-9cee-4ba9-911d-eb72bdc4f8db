<template>
  <div>
    <div slot="content">
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="质监申报整合表" name="qualityDeclareIntergration" lazy>
            <byQualityDeclareIntergration
            ></byQualityDeclareIntergration>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>

import byQualityDeclareIntergration from "@/views/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_integration/quality_declare_intergration.vue";

export default {
  name: 'QualityDeclareReportIntergration',
  components: {
    byQualityDeclareIntergration
  },
  data() {
    return {
      activeName: 'qualityDeclareIntergration',
    };
  },
  created() {

  },
  methods: {
    handleClick() {

    },
  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
