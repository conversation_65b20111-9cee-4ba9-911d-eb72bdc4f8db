<template>
  <div>
    <div slot="content">
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="竣工备案原始表" name="completionRecordOriginal" lazy>
            <byCompletionRecordOriginal
            ></byCompletionRecordOriginal>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>

import byCompletionRecordOriginal from "@/views/pccw/analysis_link/quality_declare_report_auto_statistics/completion_record_original/completion_record_original.vue";

export default {
  name: 'completionRecordOriginal',
  components: {
    byCompletionRecordOriginal,
  },
  data() {
    return {
      activeName: 'completionRecordOriginal',
    };
  },
  created() {

  },
  methods: {
    handleClick() {

    },

  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
