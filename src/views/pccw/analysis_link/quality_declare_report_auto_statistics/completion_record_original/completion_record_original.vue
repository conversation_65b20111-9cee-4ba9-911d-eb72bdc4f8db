<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :serial="true"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  qualityCompletedList,
  exportQualityCompleted
} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare";
import {commonDown} from "@/utils/btn";

export default {
  name: 'QualityDeclare',
  props: {
    options: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      staticSearchParam: {},
      tableApi:qualityCompletedList,
      searchConfig:[
        {
          label: '备案工程名称',
          type: 'input',
          fieldName: 'forRecordProjectName',
        },
        {
          label: '备案单位',
          type: 'input',
          fieldName: 'forRecordUnitName'
        },
        {
          label: '备案时间',
          type: 'month',
          fieldName: 'forRecordTimeQuery'
        },
        {
          label: '匹配状态',
          type: 'select',
          fieldName: 'isMatch',
          options: [
            {
              label: '已匹配',
              value: '0'
            },
            {
              label: '未匹配',
              value: '1'
            },
          ]
        },
        {
          label: '受理状态',
          type: 'select',
          fieldName: 'statusDesc',
          options: [
            {
              label: '不予审批',
              value: '不予审批'
            },
            {
              label: '待审批',
              value: '待审批'
            },
            {
              label: '审批通过',
              value: '审批通过'
            },
            {
              label: '已撤回',
              value: '已撤回'
            },
          ]
        },
      ],

      tableHeader:[
        {
          prop: "forRecordProjectName",
          label: "备案工程名称",
          minWidth: 150
        },
        {
          prop: "forRecordUnitName",
          label: "备案单位",
          minWidth: 150
        },
        {
          prop: "forRecordTime",
          label: "备案时间",
          minWidth: 150,
          formatter:(row) =>{
            return (
              <span>
                {row.forRecordTime?this.$moment(row.forRecordTime).format('yyyy-MM-DD'):''}
              </span>
            )
          }
        },
        {
          prop: "statusDesc",
          label: "受理状态",
          minWidth: 150
        },
        {
          prop: "interProvincial",
          label: "是否跨省/市",
          minWidth: 150
        },
        {
          prop: "isMatch",
          label: "匹配状态",
          minWidth: 150,
          formatter:(row) =>{
            return (
              <span>
                {row.isMatch === 0?'已匹配':row.isMatch === 1?'未匹配':''}
              </span>
            )
          }
        },
      ],
    };
  },
  methods: {
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...form }
      this.$refs.table.getTableData(form)
      console.log(this.staticSearchParam)
    },
    reset(form) {
      this.search(form)
    },
    exportHandle() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, exportQualityCompleted);
    }
  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your component styles here */
</style>
