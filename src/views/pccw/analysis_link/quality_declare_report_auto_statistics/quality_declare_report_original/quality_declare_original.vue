<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :serial="true"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {commonDown} from "@/utils/btn";
import {
  qualityInspectionList,exportQualityData
} from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare";

export default {
  name: 'QualityDeclareOriginal',
  props: {
    options: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      staticSearchParam: {},
      tableApi:qualityInspectionList,
      searchConfig:[
        {
          label: '申报工程名称',
          type: 'input',
          fieldName: 'reportProjectName'
        },
        {
          label: '地市',
          type: 'input',
          fieldName: 'cityName'
        },
        {
          label: '申报人',
          type: 'input',
          fieldName: 'reportPersonName'
        },
        {
          label: '申报日期',
          type: 'month',
          fieldName: 'reportTimeQuery',
        },
        {
          label: '匹配状态',
          type: 'select',
          fieldName: 'isMatch',
          options: [
            {
              label: '已匹配',
              value: '0'
            },
            {
              label: '未匹配',
              value: '1'
            },
          ]
        },
        {
          label: '审批状态',
          type: 'select',
          fieldName: 'statusDesc',
          options: [
            {
              label: '不予审批',
              value: '不予审批'
            },
            {
              label: '待审批',
              value: '待审批'
            },
            {
              label: '已下发通知书',
              value: '已下发通知书'
            },
            {
              label: '已撤销',
              value: '已撤销'
            },
            {
              label: '已撤回',
              value: '已撤回'
            },
          ]
        },

      ],
      tableHeader:[
        {
          prop: "reportProjectName",
          label: "申报工程名称",
          minWidth: 80
        },
        {
          prop: "reportUnitName",
          label: "申报单位",
          minWidth: 80
        },
        {
          prop: "provincesName",
          label: "省份",
          minWidth: 80
        },
        {
          prop: "cityName",
          label: "地市",
          minWidth: 80
        },
        {
          prop: "reportPersonName",
          label: "申报人",
          minWidth: 80
        },
        {
          prop: "reportTime",
          label: "申报日期",
          minWidth: 80,
          formatter:(row) =>{
            return (
              <span>
                {row.reportTime?this.$moment(row.reportTime).format('yyyy-MM-DD'):''}
              </span>
            )
          }
        },
        {
          prop: "approvalProjectAmount",
          label: "立项批复投资额（万元）",
          minWidth: 80
        },
        {
          prop: "statusDesc",
          label: "审批状态",
          minWidth: 80
        },
        {
          prop: "interProvincial",
          label: "是否跨省/市",
          minWidth: 80
        },
        {
          prop: "isMatch",
          label: "匹配状态",
          minWidth: 80,
          formatter:(row) =>{
            return (
              <span>
                {row.isMatch === 0?'已匹配':row.isMatch === 1?'未匹配':''}
              </span>
            )
          }
        },

      ],
    };
  },
  methods: {
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...form }
      this.$refs.table.getTableData(form)
      console.log(this.staticSearchParam)
    },
    reset(form) {
      this.search(form)
    },
    exportHandle() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, exportQualityData);
    },
  },
  mounted() {

  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
