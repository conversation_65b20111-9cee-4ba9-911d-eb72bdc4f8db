<template>
  <div>
    <div slot="content">
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="质监申报原始表" name="QualityDeclareOriginal" lazy>
            <byQualityDeclareOriginal
            ></byQualityDeclareOriginal>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>

import byQualityDeclareOriginal from "@/views/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_original/quality_declare_original.vue";

export default {
  name: 'QualityDeclareOriginal',
  components: {
    byQualityDeclareOriginal
  },
  data() {
    return {
      activeName: 'QualityDeclareOriginal',
    };
  },
  created() {

  },
  methods: {
    handleClick() {

    },
  },
  mounted() {
    // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
