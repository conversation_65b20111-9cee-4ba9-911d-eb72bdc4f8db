<template>
    <div>
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <!--结果展示框-->
      <mssCard title="查询结果">
        <div slot="headerBtn" >
        <!-- v-if -->
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-button  type="primary" @click="getTemplate">下载模板</el-button>
        <el-upload
          style="display: inline;margin: 0 10px;"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
          >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button  type="primary" @click="exportMethod" style="margin-left: 10px;">导出</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
        <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              border
              selection
              :staticSearchParam="loadParam"
            >
            </mssTable>
        </div>
      </mssCard>
      <templateAdded @onClose='onClose' ref="templateAdded"></templateAdded>
    </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
import { templateList,  templateDelete, criticalProcessTemplateConfigure, getDownloadTemplate, importKeyProcessTemplateConfigure} from '@/api/pccw/analysis_link/key_processes/api'
import templateAdded from "@/views/pccw/analysis_link/process_quality_feedback/template_name/templateAdded.vue";
export default {
name: 'template_name',
components: {
  templateAdded
},
data() {
  return {
    loadParam: {},
    tableApi: templateList,
    //搜索字段配置
    searchConfig: [
        {
            label: '工序模版名称',
            type: 'input',
            fieldName: 'templateName'
        },
        {
            label: '工序数量',
            type: 'input',
            fieldName: 'processNumber'
        },
    ],
    //表格头部配置
    tableHeader: [
        {
            prop: "templateName",
            label: "工序模版名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "processNumber",
            label: "工序数量",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "createTime",
            label: "操作时间",
            align: "center",
            tooltip: true,
            minWidth: 150
        }
    ],
  };
},
methods: {
  importFile(params) {
    const param = new FormData()
    param.append('file', params.file)
    importKeyProcessTemplateConfigure(param).then((res) => {
      if (res.code === '5000') {
          this.$message.warning(res.msg)
      } else if (res.code === '0000') {
         if (res.data.includes('preValidInfo')) {
          this.$message.warning(res.data)
        } else if (res.code === '0000') {
          this.$message.success('导入成功')
          this.$refs.table.getTableData()
        }
      }
    })
  },
  getTemplate () {
    commonDown({}, getDownloadTemplate)
  },
  //删除
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      templateDelete({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  //下载
  exportMethod (val) {
    commonDown({ ...this.loadParam, limit: -1}, criticalProcessTemplateConfigure)
  },
  //新增
  getAddition () {
    this.$refs['templateAdded'].open('关键工序模板')
  },
  //子调父方法
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
  },
    //导出
    exportHandle() {
        // commonDown({ ...this.loadParam, limit: -1}, downloadService);
    },
    //重置
    reset(form) {
      this.$refs.searchForm.searchForm.processNumber = ''
      this.$refs.searchForm.searchForm.templateName = ''
      this.search(form)
    },
    //搜索
    search() {
        this.$refs.table.page.current = 1
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
},
mounted() {
    // Code to run when the component is mounted goes here
},
};
</script>

<style scoped>
/* Your component's CSS styles go here */
</style>
