<template>
  <div>
    <el-dialog
      :title="title+'新增'"
      :visible.sync="dialogVisible"
      :close-on-click-modal='false' 
      @close='onClose'
      width="85%">
      <mssCard :title="title">
        <div slot="headerBtn">
          <el-button type="primary" @click="addTable">新增</el-button>
        </div>
        <div slot="content">
          <el-form ref="exemptionList" :model="form" :rules="rules">
            <mssTable
              ref="table"
              selection
              :serial="false"
              :stationary="tableData"
              :columns="tableHeader"
              :pagination="false"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
    </el-dialog>
  </div>
</template>
<script>
import { lpsCriticalProcessTemplateConfigureAdd} from '@/api/pccw/analysis_link/key_processes/api'
export default {
  name: 'exemptionAdded',
  data () {
    return {
      dialogVisible: false,
      tableData: [],
      form: {},
      rules: {},
      unitList:[],
      title: ''
    }
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "templateName",
            label: "工序模版名称",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`templateName${index}`}>
                  <el-input 
                    v-model={row.templateName}
                  >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "processNumber",
            label: "工序数量",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`processNumber${index}`}>
                  <el-input-number
                    v-model={row.processNumber}
                  >
                  </el-input-number>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  methods: {
    open (name) {
      this.title = name
      this.tableData = []
      this.dialogVisible = true
    },
    onClose () {
      this.$emit('onClose')
      this.dialogVisible = false
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.exemptionList.validate((validate) => {
          if (validate) {
              lpsCriticalProcessTemplateConfigureAdd(row).then((res) => {
              if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
          } else {
            return;
          }
        });
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "templateName",
        "processNumber"
      ];
      a.forEach((item) => {
        if (arr[item] === "" || arr[item] === null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          }
        } else {
          this.rules[`${item}${index}`] = {
            required: false,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          }
        }
      });
    },
    addTable() {
      this.tableData.push({
        templateName: '',
        processNumber: ''
      })
    },
  }
} 
</script>
