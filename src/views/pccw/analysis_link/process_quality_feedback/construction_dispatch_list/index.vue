<template>
  <div>
    <!-- 施工派工规范性情况统计报表 -->
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
      @changeSelect="changeSelect"
      :form="searchForm"
    >
    <div slot='customForm' class="content">
      <el-form :inline="true" ref="formInline" :model="formInline" class="search-form" :label-width="labelWidth.default">
        <el-row style="width: 100%;">
          <el-col :span="8" :offset="0">
            <el-form-item label="建设单位" style="width: 100%;" class="elSelect">
              <el-select multiple v-model="formInline.constructUnitList" placeholder="--请选择--" style="width: 100%;">
                <el-option v-for="(item,index) in cityList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'PROVIPROMANAPRI'">
            <el-form-item label="工程实施经理-主"  style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.projectManagementManagerPrimary"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="exportMethod(1)">一建导出</el-button>
        <el-button  type="primary" @click="exportMethod(2)">导出</el-button>
        <el-button  type="primary" @click="getEditNotes">编辑备注</el-button>
      </div>
      <div slot="content">
        <div class="exemptionList">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'UNITDIM'">
            <Table1
                ref="UNITDIM"
                :api="tableApi"
                :columns="UNITDIM"
                :staticSearchParam="loadParam"
              >
            </Table1>
          </el-tabs>
          <!-- <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'PROMANAGEDIM'">
            <Table1
                ref="PROMANAGEDIM"
                :api="tableApi"
                :columns="PROMANAGEDIM"
                :staticSearchParam="loadParam"
              >
            </Table1>
          </el-tabs> -->
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'PROVIPROMANAPRI'">
            <Table2
                ref="PROVIPROMANAPRI"
                :api="tableApi"
                :columns="PROVIPROMANAPRI"
                :staticSearchParam="loadParam"
              >
            </Table2>
          </el-tabs>
          <div v-html="characters" style="margin-top:15px"></div>
        </div>
      </div>
    </mssCard>
    <EditNotes @onClose='onClose' ref="editNotes"></EditNotes>
  </div>
</template>

<script>
import { constructionQuery, normativenessQuery,exportConstructionDispatchStatistics,exportALL} from '@/api/pccw/analysis_link/key_processes/api'
import { commonDown } from '@/utils/btn'
import Table1 from "@/views/pccw/analysis_link/video_acceptance/videoAcceptancePromotion/Table1.vue";
import Table2 from "@/views/pccw/analysis_link/video_acceptance/videoAcceptancePromotion/Table2.vue";
import EditNotes from "./editNotes.vue";
export default {
name: 'crux_process_upload',
components: {
  Table1,
  Table2,
  EditNotes
},
data() {
  return{
    // 表格数据API
    tableApi: normativenessQuery,
    //搜索字段配置
    searchConfig: [
      {
        label: '统计维度',
        type: 'select',
        fieldName: 'dimension',
        options: [
          {label: '建设单位', value: 'UNITDIM'},
          // {label: '地市', value: 'PROMANAGEDIM'},
          {label: '建设单位、工程实施经理', value: 'PROVIPROMANAPRI'},
        ]
      },
      {
        label: '日期',
        fieldName: 'createTime',
        type: "date1",
        format:'yyyy-MM-dd',
        valueFormat:'yyyy-MM-dd',
      },
    ],
    UNITDIM: [
     {
        prop: "titleName",
        label: `施工派工规范性统计报表-建设单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '已上报开工站点数',
            prop: 'havaReportedSiteNO',
            minWidth: 100,
          },
          {
            label: '未完成施工派工站点',
            prop: 'havaNotCompletedConstructionSiteNO',
            minWidth: 100,
          },
          {
            label: '施工派工及时率',
            prop: 'dispatchTimelyRate',
            minWidth: 100,
          },
          {
            label: '未完成监理派工站点',
            prop: 'havaNotCompletedSupervisionSiteNO',
            minWidth: 100,
          },
          {
            label: '监理派工及时率',
            prop: 'supDispatchTimelyRate',
            minWidth: 100,
          },
          {
            label: '总体派工规范性',
            prop: 'overallNormRate',
            minWidth: 100,
          },
          {
            label: 'QIP创建工单数量',
            prop: 'qipSiteNO',
            minWidth: 100,
          },
          {
            label: 'QIP创建工单及时率',
            prop: 'qipWorkOrderTimelyRate',
            minWidth: 100,
          }
        ]
      },
    ],
    PROMANAGEDIM: [
     {
        prop: "titleName",
        label: `施工派工规范性统计报表-建设单位工程实施经理主`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '已上报开工站点数',
            prop: 'havaReportedSiteNO',
            minWidth: 100,
          },
          {
            label: '未完成施工派工站点',
            prop: 'havaNotCompletedConstructionSiteNO',
            minWidth: 100,
          },
          {
            label: '施工派工及时率',
            prop: 'dispatchTimelyRate',
            minWidth: 100,
          },
          {
            label: '未完成监理派工站点',
            prop: 'havaNotCompletedSupervisionSiteNO',
            minWidth: 100,
          },
          {
            label: '监理派工及时率',
            prop: 'supDispatchTimelyRate',
            minWidth: 100,
          },
          {
            label: '总体派工规范性',
            prop: 'overallNormRate',
            minWidth: 100,
          },
          {
            label: 'QIP创建工单数量',
            prop: 'qipSiteNO',
            minWidth: 100,
          },
          {
            label: 'QIP创建工单及时率',
            prop: 'qipWorkOrderTimelyRate',
            minWidth: 100,
          }
        ]
      },
    ],
    PROVIPROMANAPRI: [
     {
        prop: "titleName",
        label: `施工派工规范性统计报表-建设单位工程实施经理主`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '工程实施经理-主',
            prop: 'projectImplementationManagerPrimary',
            minWidth: 100,
          },
          {
            label: '已上报开工站点数',
            prop: 'havaReportedSiteNO',
            minWidth: 100,
          },
          {
            label: '未完成施工派工站点',
            prop: 'havaNotCompletedConstructionSiteNO',
            minWidth: 100,
          },
          {
            label: '施工派工及时率',
            prop: 'dispatchTimelyRate',
            minWidth: 100,
          },
          {
            label: '未完成监理派工站点',
            prop: 'havaNotCompletedSupervisionSiteNO',
            minWidth: 100,
          },
          {
            label: '监理派工及时率',
            prop: 'supDispatchTimelyRate',
            minWidth: 100,
          },
          {
            label: '总体派工规范性',
            prop: 'overallNormRate',
            minWidth: 100,
          },
          {
            label: 'QIP创建工单数量',
            prop: 'qipSiteNO',
            minWidth: 100,
          },
          {
            label: 'QIP创建工单及时率',
            prop: 'qipWorkOrderTimelyRate',
            minWidth: 100,
          }
        ]
      },
    ],
    formInline: {
      constructUnitList: [],
      discipline: '',
      projectImplementationManagerPrimary: '',
      district: '',
      projectName: '',
      constructionUnit: ''
    },
    labelWidth: {
      default: '130px'
    },
    whether: 'UNITDIM',
    cityList: [],
    searchForm: {
      dimension: "UNITDIM",
    },
    loadParam: {
      dimension: "UNITDIM",
    },
    characters: '',
    activeName: 'detail',
  }
},

created() {
  this.cityList = JSON.parse(window.sessionStorage.getItem('constructionUnit'))
  this.notesQuery()
},
methods: {
  getEditNotes () {
    let text ='UNITDIM,PROVIPROMANAPRI'
    this.$refs.editNotes.open(text)
  },
  handleClick () {
  }, 
  notesQuery () {
    constructionQuery({code: this.loadParam.dimension, type: 2}).then((res)=> {
      let name = []
      res.data.forEach(item => {
        name.push(item.detail+ '</br>')
      })
      this.characters = name.join('')
    })
  },
  onClose () {
    this.notesQuery()
  },
  changeSelect (item) {
    if (item === 'dimension') {
      this.formInline = {
        constructUnitList: [],
        projectManagementMajor: '',
        projectManagementManagerPrimary: '',
        district: '',
        projectName: '',
        projectName: '',
        constructionUnit: '',
        taskManager: ''
      }
      this.activeName = 'detail'
      this.whether = this.$refs.searchForm.searchForm.dimension
      this.search()
    }
  },
  search() {
    this.$refs[this.whether].page.current = 1
    let form = {
      ...this.formInline,
      ...this.$refs.searchForm.searchForm,
      constructUnitList: this.formInline.constructUnitList.toString()
    }
    this.loadParam = {...form}
    this.$refs[this.whether].getTableData(form);
    this.notesQuery()
  },
  reset() {
    this.formInline = {
        constructUnitList: [],
        projectManagementMajor: '',
        projectManagementManagerPrimary: '',
        district: '',
        projectName: '',
        projectName: '',
        constructionUnit: '',
        taskManager: ''
    }
    this.$refs.searchForm.searchForm.dimension = 'UNITDIM'
    this.$refs.searchForm.searchForm.date = ''
    this.whether = 'UNITDIM',
    this.search()
  },
  exportMethod (val) {
    let data = {
      ...this.formInline,
      ...this.$refs.searchForm.searchForm,
      constructUnitList: this.formInline.constructUnitList.toString(),
      dimension: this.whether
    }
    console.log(data)
    if (val === 1) {
      commonDown({ ...data,limit: -1}, exportALL)
    } else {
      commonDown({ ...data}, exportConstructionDispatchStatistics)
    }
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>