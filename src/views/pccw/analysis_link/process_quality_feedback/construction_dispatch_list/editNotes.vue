<template>
  <div>
    <el-dialog
      title="备注"
      :visible.sync="dialogVisible"
      :close-on-click-modal='false' 
      @close='onClose'
      width="85%">
      <mssCard title="备注">
        <div slot="headerBtn">
          <el-button type="primary" @click="addTable">新增</el-button>
        </div>
        <div slot="content">
          <el-form ref="exemptionList" :model="form" :rules="rules">
            <mssTable
              :height='600'
              ref="table"
              selection
              :serial="false"
              :stationary="tableData"
              :columns="tableHeader"
              :pagination="false"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
    </el-dialog>
  </div>
</template>
<script>
import { constructionQuery, constructionAdd, constructionDel} from '@/api/pccw/analysis_link/key_processes/api'
export default {
  name: 'editNotes',
  data () {
    return {
      dialogVisible: false,
      tableData: [],
      levelNoList: [
        {label: '建设单位', value: 'UNITDIM'},
        {label: '建设单位、工程实施经理', value: 'PROVIPROMANAPRI'}
      ],
      form: {},
      rules: {},
      unitList:[],
      code: ''
    }
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "detail",
            label: "备注内容",
            minWidth: 500,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`detail${index}`}>
                  <el-input 
                  type="input"
                  v-model={row.detail}
                  >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "code",
            label: "权限（可多选）",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`code${index}`}>
                  <el-select
                    multiple
                    v-model={row.code}
                    onChange={(value) => {
                      this.form[`code${index}`] = value;
                    }}
                  >
                    {this.levelNoList.length &&
                      this.levelNoList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.value} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  methods: {
    deleteTable (row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          constructionDel({ids: row.id}).then(res => {
            if (res.code == '0000') {
              this.$message.success("删除成功");
              this.open();
            } else {
              this.$message.error(`${res.msg || '删除失败'}`)
            }
          })
        }).catch(() => {})
    },
    addTable() {
      this.tableData.push({
        code: '',
        detail: ''
      })
    },
    open (code) {
      this.code = code
      this.tableData = []
      constructionQuery({code: this.code}).then((res)=> {
        res.data.forEach(item => {
          this.tableData.push({
            ...item,
           code: item.code.split(',')
          })
        })
        console.log(this.tableData)
      })
      this.dialogVisible = true
    },
    onClose () {
      this.$emit('onClose')
      this.dialogVisible = false
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.exemptionList.validate((validate) => {
          if (validate) {
             let data = {
              ...row,
              detail: row.detail,
              code: row.code.toString()
             }
              constructionAdd(data).then((res) => {
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
          } else {
            return;
          }
        });
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "detail",
        "code",
      ];
      a.forEach((item) => {
        if (index) {
          this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          for (let index in arr) {
            this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
            this.rules[`${item}${index}`] = {
              required: true,
              message: "该字段不能为空",
              trigger: ["blur", "change"],
            };
          }
        }
      });
    },
  }
} 
</script>
