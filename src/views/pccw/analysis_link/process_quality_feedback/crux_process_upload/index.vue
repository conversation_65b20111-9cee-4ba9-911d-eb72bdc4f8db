<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
      @changeSelect="changeSelect"
      :form="searchForm"
    >
    <div slot='customForm' class="content">
      <el-form :inline="true" ref="formInline" :model="formInline" class="search-form" :label-width="labelWidth.default">
        <el-row style="width: 100%;">
          <el-col :span="8" :offset="0"  v-if="whether === 'UNITDIM' || whether === 'UNITCONSTRUCTDIM' || whether === 'UNITCITYCONSTRUCTDIM' || whether === 'UNITPROCONSTRUCTDIM' || whether === 'CONANDIMPMANAGERDIM' || whether === 'UNITCITYCONTASKMANADIM'">
            <el-form-item label="建设单位" style="width: 100%;" class="elSelect">
              <el-select multiple v-model="formInline.constructUnitList" placeholder="--请选择--" style="width: 100%;">
                <el-option v-for="(item,index) in cityList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8" :offset="0" v-if="whether === 'PROMANAGEDIM'">
            <el-form-item label="项目管理专业" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.projectManagementMajor"></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="8" :offset="0" v-if="whether === 'PROVIPROMANAPRI'|| whether === 'PROMANAGEDIM'">
            <el-form-item label="工程管理经理-主"  style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.projectManagementManagerPrimary"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'CONANDIMPMANAGERDIM'">
            <el-form-item label="工程实施经理-主"  style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.projectImplementationManagerPrimary"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'UNITCITYCONSTRUCTDIM'|| whether === 'UNITCITYCONTASKMANADIM'">
            <el-form-item label="区县"  style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.district"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'UNITPROCONSTRUCTDIM'">
            <el-form-item label="项目名称" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.projectName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'UNITCONSTRUCTDIM' || whether === 'UNITPROCONSTRUCTDIM' || whether === 'UNITCITYCONSTRUCTDIM'">
            <el-form-item label="施工单位" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.constructionUnit"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === 'UNITCITYCONTASKMANADIM'">
            <el-form-item label="任务经理" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.taskManager"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="exportMethod(1)">一键导出</el-button>
        <el-button  type="primary" @click="exportMethod(2)">导出</el-button>
        <el-button  type="primary" @click="getEditNotes">编辑备注</el-button>
      </div>
      <div slot="content">
        <div class="exemptionList">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'UNITDIM'">
          <el-tab-pane label="已完工站点" name="已完工">
            <Table1
              ref="UNITDIM1"
              :api="tableApi"
              :columns="UNITDIM1"
              :staticSearchParam="loadParam"
            >
            </Table1>
          </el-tab-pane>
          <el-tab-pane label="未完工站点" name="未完工">
            <Table1
              ref="UNITDIM11"
              :api="tableApi"
              :columns="UNITDIM11"
              :staticSearchParam="loadParam"
            >
            </Table1>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'PROMANAGEDIM'">
          <el-tab-pane label="已完工站点" name="已完工">
            <Table1
              ref="PROMANAGEDIM1"
              :api="tableApi"
              :columns="PROMANAGEDIM1"
              :staticSearchParam="loadParam">
            </Table1>
          </el-tab-pane>
          <el-tab-pane label="未完工站点" name="未完工">
            <Table1
              ref="PROMANAGEDIM11"
              :api="tableApi"
              :columns="PROMANAGEDIM11"
              :staticSearchParam="loadParam">
            </Table1>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'PROVIPROMANAPRI'">
          <el-tab-pane label="已完工站点" name="已完工">
            <Table1
              ref="PROVIPROMANAPRI1"
              :api="tableApi"
              :columns="PROVIPROMANAPRI1"
              :staticSearchParam="loadParam">
            </Table1>
          </el-tab-pane>
          <el-tab-pane label="未完工站点" name="未完工">
            <Table1
              ref="PROVIPROMANAPRI11"
              :api="tableApi"
              :columns="PROVIPROMANAPRI11"
              :staticSearchParam="loadParam">
            </Table1>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'UNITCITYCONSTRUCTDIM'">
          <el-tab-pane label="已完工站点" name="已完工">
            <Table3
              ref="UNITCITYCONSTRUCTDIM1"
              :api="tableApi"
              :columns="UNITCITYCONSTRUCTDIM1"
              :staticSearchParam="loadParam">
            </Table3>
          </el-tab-pane>
          <el-tab-pane label="未完工站点" name="未完工">
            <Table3
              ref="UNITCITYCONSTRUCTDIM11"
              :api="tableApi"
              :columns="UNITCITYCONSTRUCTDIM11"
              :staticSearchParam="loadParam">
            </Table3>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'CONANDIMPMANAGERDIM'">
          <el-tab-pane label="已完工站点" name="已完工">
            <Table2
              ref="CONANDIMPMANAGERDIM1"
              :api="tableApi"
              :columns="CONANDIMPMANAGERDIM1"
              :staticSearchParam="loadParam">
            </Table2>
          </el-tab-pane>
          <el-tab-pane label="未完工站点" name="未完工">
            <Table2
              ref="CONANDIMPMANAGERDIM11"
              :api="tableApi"
              :columns="CONANDIMPMANAGERDIM11"
              :staticSearchParam="loadParam">
            </Table2>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'UNITCONSTRUCTDIM'">
          <el-tab-pane label="已完工站点" name="已完工">
            <Table2
              ref="UNITCONSTRUCTDIM1"
              :api="tableApi"
              :columns="UNITCONSTRUCTDIM1"
              :staticSearchParam="loadParam">
            </Table2>
          </el-tab-pane>
          <el-tab-pane label="未完工站点" name="未完工">
            <Table2
              ref="UNITCONSTRUCTDIM11"
              :api="tableApi"
              :columns="UNITCONSTRUCTDIM11"
              :staticSearchParam="loadParam">
            </Table2>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'UNITCITYCONTASKMANADIM'">
          <el-tab-pane label="已完工站点" name="已完工">
            <Table3
              ref="UNITCITYCONTASKMANADIM1"
              :api="tableApi"
              :columns="UNITCITYCONTASKMANADIM1"
              :staticSearchParam="loadParam"
            >
            </Table3>
          </el-tab-pane>
          <el-tab-pane label="未完工站点" name="未完工">
            <Table3
              ref="UNITCITYCONTASKMANADIM11"
              :api="tableApi"
              :columns="UNITCITYCONTASKMANADIM11"
              :staticSearchParam="loadParam">
            </Table3>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === 'UNITPROCONSTRUCTDIM'">
          <el-tab-pane label="已完工站点" name="已完工">
            <Table3
              ref="UNITPROCONSTRUCTDIM1"
              :api="tableApi"
              :columns="UNITPROCONSTRUCTDIM1"
              :staticSearchParam="loadParam">
            </Table3>
          </el-tab-pane>
          <el-tab-pane label="未完工站点" name="未完工">
            <Table3
              ref="UNITPROCONSTRUCTDIM11"
              :api="tableApi"
              :columns="UNITPROCONSTRUCTDIM11"
              :staticSearchParam="loadParam">
            </Table3>
          </el-tab-pane>
        </el-tabs>
        <div v-html="characters" style="margin-top:15px"></div>
      </div>
      </div>
    </mssCard>
    <EditNotes @onClose='onClose' ref="editNotes"></EditNotes>
  </div>
</template>

<script>
//获取接口地址
import { commonDown } from '@/utils/btn';
import moment from 'moment'
import EditNotes from "./editNotes.vue";
import { getQueryList, exportByDimension, cruxExport, getNotesQuery} from '@/api/pccw/analysis_link/key_processes/api'
import Table1 from "@/views/pccw/analysis_link/video_acceptance/videoAcceptancePromotion/Table1.vue";
import Table2 from "@/views/pccw/analysis_link/video_acceptance/videoAcceptancePromotion/Table2.vue";
import Table3 from "@/views/pccw/analysis_link/video_acceptance/videoAcceptancePromotion/Table3.vue";
export default {
name: 'crux_process_upload',
components: {
  EditNotes,
  Table1,
  Table2,
  Table3
},
data() {
  return {
    // 表格数据API
    tableApi: getQueryList,
    //搜索字段配置
    searchConfig: [
      {
        label: '统计维度',
        type: 'select',
        fieldName: 'dimension',
        options: [
          {label: '建设单位', value: 'UNITDIM'},
          {label: '项目管理专业', value: 'PROMANAGEDIM'},
          {label: '省公司工程管理经理', value: 'PROVIPROMANAPRI'},
          {label: '建设单位、区县、施工单位', value: 'UNITCITYCONSTRUCTDIM'},
          {label: '建设单位工程实施经理主', value: 'CONANDIMPMANAGERDIM'},
          {label: '建设单位、施工单位', value: 'UNITCONSTRUCTDIM'},
          {label: '建设单位区县、任务经理', value: 'UNITCITYCONTASKMANADIM'},
          {label: '建设单位、项目、施工单位', value: 'UNITPROCONSTRUCTDIM'},
        ]
      },
      {
        label: '日期',
        fieldName: 'createTime',
        type: "date1",
        format:'yyyy-MM-dd',
        valueFormat:'yyyy-MM-dd',
      },
    ],
    // 建设单位
    UNITDIM1: [
     {
        prop: "titleName",
        label: `系统已完工站点关键工序上传与审核进度-分建设单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRate',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRate',
            minWidth: 100,
          },
          {
            label: '全量审核通过站点数',
            prop: 'totalNumberOfAuditSites',
            minWidth: 100,
          },
          {
            label: '全量审核站点通过率',
            prop: 'fullReviewOfSitePassRate',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRate',
            minWidth: 100,
          },
        ]
      },
    ],
    UNITDIM11: [
     {
        prop: "titleName",
        label: `系统未完工站点关键工序上传与审核进度-分建设单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '全量审核通过站点数',
            prop: 'totalAuditSitesNoOfUnCompleted',
            minWidth: 100,
          },
          {
            label: '全量审核站点通过率',
            prop: 'fullReviewOfSitePassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRateOfUncompleted',
            minWidth: 100,
          },
        ]
      },
    ],
    // 项目管理专业
    PROMANAGEDIM1: [
      {
        prop: "titleName",
        label: `系统已完工站点关键工序上传与审核进度-分专业`,
        multilevelColumn: [
          {
            label: '项目管理专业',
            prop: 'projectManagementMajor',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRate',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRate',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRate',
            minWidth: 100,
          },
        ]
      },
    ],
    PROMANAGEDIM11: [
      {
        prop: "titleName",
        label: `系统未完工站点关键工序上传与审核进度-分专业`,
        multilevelColumn: [
          {
            label: '项目管理专业',
            prop: 'projectManagementMajor',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRateOfUncompleted',
            minWidth: 100,
          },
        ]
      },
    ],
    // 省公司工程实施经理
    PROVIPROMANAPRI1: [
      {
        prop: "titleName",
        label: `系统已完工站点关键工序上传与审核进度-分省公司工程管理经理`,
        multilevelColumn: [
          {
            label: '工程管理经理',
            prop: 'projectManagementManagerPrimary',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRate',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRate',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRate',
            minWidth: 100,
          },
        ]
      },
    ],
    PROVIPROMANAPRI11: [
      {
        prop: "titleName",
        label: `系统未完工站点关键工序上传与审核进度-分省公司工程管理经理`,
        multilevelColumn: [
          {
            label: '工程管理经理',
            prop: 'projectManagementManagerPrimary',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRateOfUncompleted',
            minWidth: 100,
          },
        ]
      },
    ],
    // 建设单位区县、施工单位维度
    UNITCITYCONSTRUCTDIM1: [
      {
        prop: "titleName",
        label: `系统已完工站点关键工序上传与审核进度-分建设单位分区县分施工单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '区县',
            prop: 'district',
            minWidth: 100,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRate',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRate',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRate',
            minWidth: 100,
          },
        ]
      },
    ],
    UNITCITYCONSTRUCTDIM11: [
      {
        prop: "titleName",
        label: `系统未完工站点关键工序上传与审核进度-分建设单位分区县分施工单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '区县',
            prop: 'district',
            minWidth: 100,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRateOfUncompleted',
            minWidth: 100,
          },
        ]
      },
    ],
    //建设单位、任务经理
    CONANDIMPMANAGERDIM1: [
      {
        prop: "titleName",
        label: `系统已完工站点关键工序上传与审核进度-分建设单位分工程实施经理-主`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '工程实施经理-主',
            prop: 'projectImplementationManagerPrimary',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRate',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRate',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRate',
            minWidth: 100,
          },
        ]
      },
    ],
    CONANDIMPMANAGERDIM11: [
      {
        prop: "titleName",
        label: `系统未完工站点关键工序上传与审核进度-分建设单位分工程实施经理-主`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '工程实施经理-主',
            prop: 'projectImplementationManagerPrimary',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRateOfUncompleted',
            minWidth: 100,
          },
        ]
      },
    ],
    // 建设单位、施工单位
    UNITCONSTRUCTDIM1: [
      {
        prop: "titleName",
        label: `系统已完工站点关键工序上传与审核进度-分建设单位分施工单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRate',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRate',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRate',
            minWidth: 100,
          },
        ]
      },
    ],
    UNITCONSTRUCTDIM11: [
      {
        prop: "titleName",
        label: `系统未完工站点关键工序上传与审核进度-分建设单位分施工单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRateOfUncompleted',
            minWidth: 100,
          },
        ]
      },
    ],
    // 建设单位区县、任务经理
    UNITCITYCONTASKMANADIM1: [
      {
        prop: "titleName",
        label: `系统已完工站点关键工序上传与审核进度-分建设单位分区县分任务经理`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '区县',
            prop: 'district',
            minWidth: 100,
          },
          {
            label: '任务经理',
            prop: 'taskManager',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRate',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRate',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRate',
            minWidth: 100,
          },
        ]
      },
    ],
    UNITCITYCONTASKMANADIM11: [
      {
        prop: "titleName",
        label: `系统未完工站点关键工序上传与审核进度-分建设单位分区县分任务经理`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '区县',
            prop: 'district',
            minWidth: 100,
          },
          {
            label: '任务经理',
            prop: 'taskManager',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRateOfUncompleted',
            minWidth: 100,
          },
        ]
      },
    ],
    // 建设单位、项目、施工单位
    UNITPROCONSTRUCTDIM1: [
      {
        prop: "titleName",
        label: `系统已完工站点关键工序上传与审核进度-分建设单位分项目分施工单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '项目',
            prop: 'projectName',
            minWidth: 100,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRate',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfCompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRate',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRate',
            minWidth: 100,
          },
        ]
      },
    ],
    UNITPROCONSTRUCTDIM11: [
      {
        prop: "titleName",
        label: `系统未完工站点关键工序上传与审核进度-分建设单位分项目分施工单位`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 100,
          },
          {
            label: '项目',
            prop: 'projectName',
            minWidth: 100,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 100,
          },
          {
            label: '可上传站点数量',
            prop: 'canUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '应上传关键工序数量',
            prop: 'shouldUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序数量',
            prop: 'haveUploadNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序提交及时率',
            prop: 'keyProcessTimelySubmissionRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过数量',
            prop: 'havePassedNoOfUncompleted',
            minWidth: 100,
          },
          {
            label: '关键工序审核通过率',
            prop: 'keyProcessAuditPassRateOfUncompleted',
            minWidth: 100,
          },
          {
            label: '已上传关键工序合格率',
            prop: 'keyProcessePassRateOfUncompleted',
            minWidth: 100,
          },
        ]
      },
    ],
    formInline: {
      constructUnitList: [],
      projectName: '',
      projectManagementManagerPrimary: '',
      district: '',
      projectName: '',
      projectName: '',
      constructionUnit: '',
      taskManager: ''
    },
    labelWidth: {
      default: '130px'
    },
    whether: 'UNITDIM',
    cityList: [],
    searchForm: {
      dimension: "UNITDIM",
      status: '已完工',
    },
    loadParam: {
      dimension: "UNITDIM",
      status: '已完工',
      createTime: ''
    },
    characters: '',
    activeName: '已完工'
  }
},

created() {
  this.notesQuery ()
  this.cityList = JSON.parse(window.sessionStorage.getItem('constructionUnit'))
},
methods: {
  handleClick () {
    let table = ''
    if (this.activeName == '已完工') {
      table = this.whether +'1'
    } else {
      table = this.whether +'11'
    }
    this.$refs[table].page.current = 1
    let form = {
      ...this.formInline,
      ...this.$refs.searchForm.searchForm,
      status: this.activeName,
      constructUnitList: this.formInline.constructUnitList.toString(),
    }
    this.loadParam = {...form}
    this.$refs[table].getTableData(form);
    this.notesQuery()
  },
  notesQuery () {
    getNotesQuery({code: this.loadParam.dimension, type: 1}).then((res)=> {
      let name = []
      res.data.forEach(item => {
        name.push(item.detail+ '</br>')
      })
      this.characters = name.join('')
    })
  },
  onClose () {
    this.notesQuery()
  },
  getEditNotes () {
    let text ='UNITDIM,PROMANAGEDIM,PROVIPROMANAPRI,UNITCITYCONSTRUCTDIM,CONANDIMPMANAGERDIM,UNITCONSTRUCTDIM,UNITCITYCONTASKMANADIM,UNITPROCONSTRUCTDIM'
    this.$refs.editNotes.open(text)
  },
  changeSelect (item) {
    if (item === 'dimension') {
      this.formInline = {
        constructUnitList: [],
        projectName: '',
        projectManagementManagerPrimary: '',
        district: '',
        projectName: '',
        projectName: '',
        constructionUnit: '',
        taskManager: '',
        status: '已完工'
      }
      this.activeName = '已完工'
      this.whether = this.$refs.searchForm.searchForm.dimension
      this.search()
    }
  },
  search () {
    console.log(this.activeName)
    let table = ''
    if (this.activeName == '已完工') {
      table = this.whether +'1'
    } else {
      table = this.whether +'11'
    }
    this.$refs[table].page.current = 1
    let form = {
      ...this.formInline,
      ...this.$refs.searchForm.searchForm,
      constructUnitList: this.formInline.constructUnitList.toString(),
      status: this.activeName,
    }
    this.loadParam = {...form}
    this.$refs[table].getTableData(form);
    this.notesQuery()
  },
  reset() {
    this.formInline = {
        constructUnitList: [],
        projectName: '',
        projectManagementManagerPrimary: '',
        district: '',
        projectName: '',
        projectName: '',
        constructionUnit: '',
        taskManager: '',
        status: this.activeName,
    }
    this.$refs.searchForm.searchForm.dimension = 'UNITDIM'
    this.whether = 'UNITDIM',
    this.$refs.searchForm.searchForm.date = ''
    this.search()
  },
  exportMethod(val) {
    const params = {
        ...this.formInline,
        ...this.$refs.searchForm.searchForm,
        constructUnitList: this.formInline.constructUnitList.toString(),
        status: this.activeName
      }
    if (val == 1) {
      commonDown({ ...params, limit: -1}, cruxExport)
    } else {
      commonDown(params, exportByDimension)
    }

  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>
