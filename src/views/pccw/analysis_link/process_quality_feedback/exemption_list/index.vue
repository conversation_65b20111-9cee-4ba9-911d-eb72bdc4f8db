<template>
    <div>
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <!--结果展示框-->
      <mssCard title="查询结果">
        <div slot="headerBtn" >
        <!-- v-if -->
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-button  type="primary" @click="exportMethod(1)">下载模板</el-button>
        <el-upload
          style="display: inline-block;margin-left: 10px;"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button  type="primary" @click="exportMethod(2)" style="margin-left: 10px;">导出</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
        <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              border
              selection
              :staticSearchParam="loadParam"
            >
            </mssTable>
        </div>
      </mssCard>
      <exemptionAdded @onClose='onClose' ref="exemption"></exemptionAdded>
    </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
import { listQuery, downloadTemplate, inventoryDelete, importCriticalProcessExemptData, criticalProcessExemptData} from '@/api/pccw/analysis_link/key_processes/api'
import exemptionAdded from "@/views/pccw/analysis_link/process_quality_feedback/exemption_list/exemptionAdded.vue";
import moment from "moment";
export default {
name: 'Detail',
components: {
  exemptionAdded
},
data() {
  return {
    loadParam: {},
    tableApi: listQuery,
    //搜索字段配置
    searchConfig: [
        {
            label: '项目名称',
            type: 'input',
            fieldName: 'projectName'
        },
        {
            label: '项目编码',
            type: 'input',
            fieldName: 'projectNo'
        },
        {
            label: '任务名称',
            type: 'input',
            fieldName: 'taskName'
        },
        {
            label: '任务编码',
            type: 'input',
            fieldName: 'taskCode'
        },
        {
          type: 'cycleDate',
          dateType: 'month',
          label: '任务创建时间',
          format: 'yyyy-MM',
          valueFormat: 'yyyy-MM',
          fieldName: 'taskCreateTime',
          clearable: false,
        },
        {
            label: '任务状态',
            type: 'select',
            fieldName: 'taskStatus',
            options: [{label:'已终止',value:'已终止'},{label:'未终止',value:'未终止'}]
        },
        {
            label: '建设单位',
            type: 'select',
            fieldName: 'constructUnit'
        },
    ],
    //表格头部配置
    tableHeader: [
        {
            prop: "projectNo",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "taskCode",
            label: "任务编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "taskCreateTime",
            label: "任务创建时间",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "taskStatus",
            label: "任务状态",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "constructUnit",
            label: "建设单位",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "updateTime",
            label: "加入时间",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "username",
            label: "操作人",
            align: "center",
            tooltip: true,
            minWidth: 150
        }
    ],
  };
},
methods: {
  //删除
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      console.log(this.$refs.table.multipleSelection)
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      inventoryDelete({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  //下载
  exportMethod (val) {
    if (val === 1) {
      commonDown({ ...this.$refs.searchForm.searchForm, limit: -1}, downloadTemplate)
    } else {
      commonDown({ ...this.$refs.searchForm.searchForm, limit: -1}, criticalProcessExemptData)
    }
  },
  //新增
  getAddition () {
    this.$refs['exemption'].open('关键工序豁免清单')
  },
  //子调父方法
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
  },
  //重置
  reset(form) {
    this.search(form)
  },
  //搜索
  search() {
      this.$refs.table.page.current = 1
      console.log(this.$refs.searchForm.searchForm)
      this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      // this.loadParam.clockinDate = moment(this.loadParam.clockinDate).format('yyyy-MM-dd HH:mm:ss')
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  importFile(params) {
    const param = new FormData()
    param.append('file', params.file)
    importCriticalProcessExemptData(param).then((res) => {
      if (res.data.includes('preValidInfo')) {
        this.$message.warning(res.data)
      } else if (res.code === '0000') {
        this.$message.success('导入成功')
        this.$refs.table.getTableData()
      }
    })
  },
},
mounted() {
  this.$set(this.searchConfig[6], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
    // Code to run when the component is mounted goes here
},
};
</script>

<style scoped>
/* Your component's CSS styles go here */
</style>
