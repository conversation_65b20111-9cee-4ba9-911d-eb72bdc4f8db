<template>
  <div>
    <el-dialog
      :title="title+'新增'"
      :visible.sync="dialogVisible"
      :close-on-click-modal='false' 
      @close='onClose'
      width="85%">
      <mssCard :title="title">
        <div slot="headerBtn">
          <el-button type="primary" @click="addTable">新增</el-button>
        </div>
        <div slot="content">
          <el-form ref="exemptionList" :model="form" :rules="rules">
            <mssTable
              ref="table"
              selection
              :serial="false"
              :stationary="tableData"
              :columns="tableHeader"
              :pagination="false"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
    </el-dialog>
  </div>
</template>
<script>
import { lpsCriticalProcessExemptAdd, lpsConstructionDispatchAdd} from '@/api/pccw/analysis_link/key_processes/api'
import { queryAreaListService } from '@/api/common_api.js'
export default {
  name: 'exemptionAdded',
  data () {
    return {
      dialogVisible: false,
      tableData: [],
      levelNoList: [
        { label: "已终止", value: "已终止" },
        { label: "未终止", value: "未终止" }
      ],
      form: {},
      rules: {},
      unitList:[],
      title: ''
    }
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "projectNo",
            label: "项目编码",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`projectNo${index}`}>
                  <el-input 
                    v-model={row.projectNo}
                  >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "taskCode",
            label: "任务编码",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`taskCode${index}`}>
                  <el-input 
                    v-model={row.taskCode}
                  >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "taskCreateTime",
            label: "任务创建时间",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`taskCreateTime${index}`} >
                  <el-date-picker 
                    style="width: 90%"
                    v-model={row.taskCreateTime}
                    type="date"
                  >
                  </el-date-picker>
                </el-form-item>
              );
            },
          },
          {
            prop: "taskStatus",
            label: "任务状态",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`taskStatus${index}`}>
                  <el-select
                    v-model={row.taskStatus}
                    onChange={(value) => {
                      this.form[`taskStatus${index}`] = value;
                    }}
                  >
                    {this.levelNoList.length &&
                      this.levelNoList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.value} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            prop: "constructUnit",
            label: "建设单位",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`constructUnit${index}`}>
                  <el-select
                    v-model={row.constructUnit}
                    onChange={(value) => {
                      this.form[`constructUnit${index}`] = value;
                    }}
                  >
                    {this.unitList.length &&
                      this.unitList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.value} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  methods: {
    open (name) {
      this.title = name
      this.unitList = JSON.parse(window.sessionStorage.getItem('constructionUnit'))
      this.tableData = []
      this.dialogVisible = true
    },
    onClose () {
      this.$emit('onClose')
      this.dialogVisible = false
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.exemptionList.validate((validate) => {
          if (validate) {
              let data = {
                ...row,
                taskCreateTime: this.$moment(row.taskCreateTime).format('YYYY-MM-DD')
              }
              if (this.title === '施工派工豁免清单') {
                lpsConstructionDispatchAdd(data).then((res) => {
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                });
              } else {
                lpsCriticalProcessExemptAdd(data).then((res) => {
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                });
              }
              
          } else {
            return;
          }
        });
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "projectNo",
        "taskCode",
        "taskCreateTime",
        "taskStatus",
        "constructUnit"
      ];
      a.forEach((item) => {
        if (arr[item] === "" || arr[item] === null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          }
        } else {
          this.rules[`${item}${index}`] = {
            required: false,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          }
        }
      });
    },
    addTable() {
      this.tableData.push({
        projectNo: '',
        taskCode: '',
        taskCreateTime: '',
        taskStatus: '',
        constructUnit: ''
      })
    },
  }
} 
</script>
