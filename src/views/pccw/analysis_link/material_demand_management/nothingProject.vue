<template>
    <div>
        <!--查询框 -->
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
          <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              :staticSearchParam="loadParam"
              border
              selection
            >
            </mssTable>
          </div>
        </mssCard>
    </div>
</template>

<script>

import { listByMaterialAndUnit, downloadService } from '@/api/pccw/analysis_link/material_demand_management/api'
import { commonDown } from '@/utils/btn'
// 获取地市:queryAreaListService
import {queryAreaListService} from '@/api/common_api.js'

export default {
    name: 'MeterialDemandManagement',
    data() {
        return{
          tableApi: listByMaterialAndUnit,
          currentTableHeader: null,  // 用于存储当前的表头配置
          searchTrigger: 0,  // 新增一个变量
          loadParam: {},  // 用于存储搜索条件
          searchConfig:[
            {
                label: '地市',
                type: 'input',
                fieldName: 'unit'
            },
            {
                label: '物料名称',
                type: 'input',
                fieldName: 'materialName'
            },
            {
                label: '物料编码',
                type: 'input',
                fieldName: 'materialCode'
            },
          ],
          tableHeader:[
            {
              prop: "unit",
              label: "地市",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "materialCode",
              label: "物料编码",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "materialName",
              label: "物料名称",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "measuringUnit",
              label: "物料单位",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "designQuantity",
              label: "设计量",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "materialRequisitioned",
              label: "领料量",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "unreceivedQuantity",
              label: "采购未接收数量",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "availableQuantity",
              label: "库存可用量",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
            {
              prop: "needPurchasedQuantity",
              label: "需采购数量",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
              
          ],
        }
    },
    created() {
      //初始化展示地区+项目编码+物料编码维度汇总表
      this.getAreaList('-2', 0)
    },
    methods: {
      /**
       * 地市
       * @param parentId
       * @param index
       */
       getAreaList(parentId, index) {
        queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
          if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
              list.push({ label: item.name, value: item.name })
            })
            this.$set(this.searchConfig[index], 'options', list)
          }
        })
      },
      //判断是地区+项目编码+物料编码维度汇总表还是地区+物料编码维度汇总表（区别在于是否显示项目编号列）
      isShowProjectNumberColumn(){
        if (this.$refs.searchForm.searchForm && this.$refs.searchForm.searchForm.projectNumber 
        && this.$refs.searchForm.searchForm.projectNumber.trim() !== ''
        ) {
          return true;
        }else{
          return false;
        }
      },
      // 查询
      search() {
        this.$nextTick(() => {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.tableApi = listByMaterialAndUnit;
          this.searchTrigger++;  // 改变变量的值
        });
      },
      // 重置
      reset() {
        this.$nextTick(() => {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.tableApi = listByMaterialAndUnit;
          this.searchTrigger++;  // 改变变量的值   
        });
      },
      // 导出
      exportHandle() {
        //判断情况和search方法一致
        commonDown({ ...this.$refs.searchForm.searchForm, dimensionType: 2, limit: -1}, downloadService);
        this.searchTrigger++;  // 改变变量的值   
      },
    },
    watch: {
      searchTrigger() {  // 监听新的变量
        this.$nextTick(() => {
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        });
      }
    },
}
</script>

<style scoped>
/* Your component styles here */
</style>
