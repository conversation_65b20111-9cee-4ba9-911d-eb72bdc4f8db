<template>
    <div>  
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="按地市、项目、物料维度" name="haveProject">
            <haveProject ref="haveProject"></haveProject>
          </el-tab-pane>
          <el-tab-pane label="按地市、物料维度" name="nothingProject">
            <nothingProject ref="nothingProject"></nothingProject>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
</template>

<script>
import haveProject from "./haveProject.vue";
import nothingProject from "./nothingProject.vue";
export default {
    name: 'material_demand_management',
    components: {
      haveProject,
      nothingProject
    },
    data(){
        return {
          activeName: 'haveProject',
        }
    },
    methods: {     
      handleClick () {

      },  
    },
}
</script>

<style scoped>
/* Your component's CSS code goes here */
</style>
