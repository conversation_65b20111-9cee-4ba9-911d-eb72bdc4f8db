<template>
  <div>
    <flow-detail :flow="flow" :showTransact="false" :type="approveType" :msgApproval="msgApproval" ref="flowDetail">
      <div slot="content">
        <mssCard :title="title">
        <div slot="content">
          <el-form ref="editform" :model="form" :rules="rules">
            <mssTable
              ref="table"
              selection
              :serial="false"
              :columns="tableHeader"
              :staticSearchParam="loadParam"
              :api="tableApi"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
      </div>
    </flow-detail>
  </div>
</template>
<script>
import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue";
import { listTodoService } from '@/api/pccw/analysis_link/material_demand_management/api'
export default {
  components: {
      FlowDetail
  },
  data () {
    return {
      tableApi: listTodoService,
      loadParam: {},  // 用于存储搜索条件
      dialogVisible: false,
      tableData: [],
      tableHeader: [
        {
          prop: "unit",
          label: "地巿",
          minWidth: 150,
        },
        {
          prop: "projectNumber",
          label: "项目编号",
          minWidth: 150,
        },
        {
          prop: "materialCode",
          label: "物料编码",
          minWidth: 150,
        },
        {
          prop: "materialName",
          label: "物料名称",
          minWidth: 150,
        },
        {
          prop: "measuringUnit",
          label: "物料单位",
          minWidth: 150,
        },
        {
          prop: "designQuantity",
          label: "设计量",
          minWidth: 150,
        },
        {
          prop: "materialRequisitioned",
          label: "领料量",
          minWidth: 200,
        },
        {
          prop: "unreceivedQuantity",
          label: "采购未接收量",
          minWidth: 150,
        },
        {
          prop: "availableQuantity",
          label: "库存可用量",
          minWidth: 200,
        },
        {
          prop: "needPurchasedQuantity",
          label: "需采购数量",
          minWidth: 200,
        }
      ],
      msgApproval: '',
      title: '',
      form: {},
      rules: {},
    }
  },
  created () {
    let flow = decodeURIComponent(this.$route.query.flow)
    this.flow = JSON.parse(flow); // 获取流程对象
    this.approveType = decodeURIComponent(this.$route.query.type); // 获取类型信息
    this.title = decodeURIComponent(this.$route.query.title);
    if (this.approveType == 'todo') {
        this.saveStatus = true //提交审批
        this.showApproval =  false //提交
        this.showReturn =  false //退回
      }
    this.loadParam = {
      businessId: this.flow.businessId,
      sendingTimeB: this.$moment(this.flow.sendingTimeB).format('YYYY-MM-DD')
    }
    console.log(this.flow)
    // this.messageDetail()
  },
  methods: {
    messageDetail(){
      listTodoService(this.loadParam).then((res)=>{
        this.tableData = res.data.data || [] 
      })
    }
  }
} 
</script>
