<template>
  <div>
    <!-- <el-card class="card">
        <div class="title">
          <h1 class="heading">物资需求采购提醒工单</h1>
        </div>
    </el-card> -->
    <div class="operate-btn" >
      <el-button type="primary" @click="saveInfo">关闭</el-button>
    </div>
    <mssCard title="物资需求采购提醒工单">
      <div slot="content">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :columns="tableHeader"
            :api="tableApi"
            :staticSearchParam="loadParam"
          >
          </mssTable>
      </div>
    </mssCard>
  </div>
</template>
<script>
import { getMessageDetail } from '@/api/pccw/analysis_link/material_demand_management/api'
export default {
  data () {
    return {
      tableApi:getMessageDetail,
      dialogVisible: false,
      tableData: [],
      tableHeader: [
        {
          prop: "unit",
          label: "地巿",
          minWidth: 150,
        },
        {
          prop: "projectNumber",
          label: "项目编号",
          minWidth: 150,
        },
        {
          prop: "materialCode",
          label: "物料编码",
          minWidth: 150,
        },
        {
          prop: "materialName",
          label: "物料名称",
          minWidth: 150,
        },
        {
          prop: "measuringUnit",
          label: "物料单位",
          minWidth: 150,
        },
        {
          prop: "designQuantity",
          label: "设计量",
          minWidth: 150,
        },
        {
          prop: "materialRequisitioned",
          label: "领料量",
          minWidth: 200,
        },
        {
          prop: "unreceivedQuantity",
          label: "采购未接收量",
          minWidth: 150,
        },
        {
          prop: "availableQuantity",
          label: "库存可用量",
          minWidth: 200,
        },
        {
          prop: "needPurchasedQuantity",
          label: "需采购数量",
          minWidth: 200,
        }
      ],
      dialogTitle: "物资需求采购提醒工单",
      dialogEmpower: false,
      loadParam: {}
    }
  },
  created () {
    let flow = decodeURIComponent(this.$route.query.flow)
    this.flow = JSON.parse(flow); // 获取流程对象
    this.approveType = decodeURIComponent(this.$route.query.type); // 获取类型信息
    this.title = decodeURIComponent(this.$route.query.title);
    this.loadParam = {
      sendingPersonId: this.flow.acceptor,
      sendingTimeB: this.$moment(this.flow.createDate).format('YYYY-MM-DD')
    }
    console.log(this.flow)
  },
  methods: {
    saveInfo () {
      this.$router.go(-1); // 返回上一页
    }
  }
} 
</script>
