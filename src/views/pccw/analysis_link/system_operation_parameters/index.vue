<!-- 设计预警 -->
<template>
  <div class="design_warning">
    <mssCard title="系统运行参数配置">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="del">删除</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
          </mssTable>
        </el-form>
      </div>
    </mssCard>
  </div>
</template>

<script>

import {
  queryListService,
  delService,
  saveService,
  saveAllService,
} from "@/api/pccw/comprehensive/system_operation_parameters.js";
import { commonMultDel, commonOneDel } from "@/utils/btn";
export default {
  name: "SystemOperationParameters",
  data() {
    return {
      tableData: [],
      form: {},
      rules: {},
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "projectSiteApproval",
            label: "项目站点级批复及时率",
            minWidth: 120,
            formatter: (row, column, cellValue, index) => {
              return (
                <div>
                  <el-form-item prop={`projectSiteApproval${index}`}>
                    <el-input-number
                      style="width:100px"
                      v-model={row.projectSiteApproval}
                      onChange={(value) => {
                        this.form[`projectSiteApproval${index}`] = value;
                      }}
                      controls-position="right"
                    />
                  </el-form-item>
                  <span>%</span>
                </div>
              );
            },
          },
          {
            prop: "daysOfProjectManager",
            label: "触发预警待办至项目经理的天数",
            minWidth: 120,
            formatter: (row, column, cellValue, index) => {
              return (
                <div>
                  <el-form-item prop={`daysOfProjectManager${index}`}>
                    <el-input-number
                      style="width:100px"
                      v-model={row.daysOfProjectManager}
                      onChange={(value) => {
                        this.form[`daysOfProjectManager${index}`] = value;
                      }}
                      controls-position="right"
                    />
                  </el-form-item>
                  <span>天</span>
                </div>
              );
            },
          },
          {
            prop: "daysOfConstructionManager",
            label: "触发预警待办至工建经理的天数",
            minWidth: 120,
            formatter: (row, column, cellValue, index) => {
              return (
                <div>
                  <el-form-item prop={`daysOfConstructionManager${index}`}>
                    <el-input-number
                      style="width:100px"
                      v-model={row.daysOfConstructionManager}
                      onChange={(value) => {
                        this.form[`daysOfConstructionManager${index}`] = value;
                      }}
                      controls-position="right"
                    />
                  </el-form-item>
                  <span>天</span>
                </div>
              );
            },
          },
          {
            prop: "daysOfLocalLeaders",
            label: "触发预警待办至地市分管领导的天数",
            minWidth: 120,
            formatter: (row, column, cellValue, index) => {
              return (
                <div>
                  <el-form-item prop={`entityCreateDate${index}`}>
                    <el-input-number
                      style="width:100px"
                      v-model={row.daysOfLocalLeaders}
                      onChange={(value) => {
                        this.form[`entityCreateDate${index}`] = value;
                      }}
                      controls-position="right"
                    />
                  </el-form-item>
                  <span>天</span>
                </div>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          }
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    getTable() {
      queryListService().then((res) => {
        if (res.code == "0000") {
          res.data.forEach((item) => {
            if (item.warnObject)
              item.warnObject = item.warnObject.split(",") || [];
          });
          this.tableData = res.data || [];
        }
      });
    },
    addTable() {
      if (this.tableData.length < 10) {
        let data = {
          id: null,
          projectSiteApproval: "0",
          daysOfProjectManager: "0",
          daysOfConstructionManager: "0",
          daysOfLocalLeaders: "0"
        };
        saveService(data).then((res) => {
          if (res.code == "0000") {
            this.tableData.push({ ...data, id: res.data });
          }
        });

      } else {
        this.$message.warning("最多只能有10种数据");
      }
      this.getTable()
    },
    deleteTable(row) {
      commonOneDel.call(this, row.id, delService, (res) => {
        this.$message.success("删除成功");
        this.getTable();
      });
    },
    saveONe(row, index) {
      // this.commonsetRule(row, index);
      // this.$nextTick(() => {
      //   this.$refs.editform.validate((validate) => {
      //     if (validate) {
      //       if (this.validateCopy(row)) {
      //         this.commondealName(row)
      //         let obj = JSON.parse(JSON.stringify(row));
      //         if (obj.warnObject) obj.warnObject = obj.warnObject.join(",");
      //         saveService(obj).then((res) => {
      //           if (res.code == "0000") {
      //             this.$message.success("保存成功");
      //           }
      //         });
      //       } else {
      //         this.$message.warning(`相同预警级别、相同预警方式只能有一条数据，请修改数据`);
      //       }
      //     } else {
      //       return;
      //     }
      //   });
      // });
      this.commondealName(row)
      let obj = JSON.parse(JSON.stringify(row));
      saveService(obj).then((res) => {
        if (res.code == "0000") {
          this.$message.success("保存成功");
        }
      });
    },
    save() {
      let arr = JSON.parse(JSON.stringify(this.tableData));
      saveAllService(arr).then((res) => {
        if (res.code == "0000") {
          this.$message.success("保存成功");
        }
      });

      this.commonsetRule(this.tableData);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            if (this.validateAll()) {
              let arr = JSON.parse(JSON.stringify(this.tableData));
              arr.forEach((item) => {
                this.commondealName(item)
                if (item.warnObject && item.warnObject.length) {
                  item.warnObject = item.warnObject.join(",");
                }
              });
              saveAllService(arr).then((res) => {
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
            }/* else {
              this.$message.warning(`相同预警级别、相同预警方式只能有一条数据，请修改数据`);
            }*/
          } else {
            return;
          }
        });
      });
    },

    // 删除
    del() {
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: delService,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },

    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "id",
        "projectSiteApproval",
        "daysOfProjectManager",
        "daysOfConstructionManager",
        "daysOfLocalLeaders",
      ];
      a.forEach((item) => {
        if (index) {
          this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          for (let index in arr) {
            this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
            this.rules[`${item}${index}`] = {
              required: true,
              message: "该字段不能为空",
              trigger: ["blur", "change"],
            };
          }
        }
      });
    },


    // 校验当前数据是否有存在的情况
    validateCopy(row) {
      let n = 0;
      this.tableData.forEach((item) => {
        if (item.projectSiteApproval==row.projectSiteApproval && item.daysOfProjectManager == row.daysOfProjectManager && item.daysOfConstructionManager==row.daysOfConstructionManager && item.daysOfLocalLeaders==row.daysOfLocalLeaders){
          n++;
        }
      });
      return n < 2;
    },

    // 校验所有的数据
    validateAll() {
      // let arr=[]
      // this.tableData.forEach(item=>{
      //   if(arr.indexOf(`${item.levelNo}-${item.warnType}`)<0){
      //     arr.push(`${item.levelNo}-${item.warnType}`)
      //   }
      // })
      // return arr.length==this.tableData.length
      const obj = {}
      for (let i = 0; i < this.tableData.length; i++) {
        const current = this.tableData[i]
        if (obj[`${current.levelNo}_${current.warnType}`]) {
          return false
        }
        obj[`${current.levelNo}_${current.warnType}`] = true
      }
      return true
    },
    // 处理其中文字
    commondealName(row){

    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


