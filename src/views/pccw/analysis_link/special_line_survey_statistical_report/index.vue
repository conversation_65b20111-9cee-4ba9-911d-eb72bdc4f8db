<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
          @changeSelect="changeSelect"
          :staticSearchParam="staticSearchParam"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
            <div slot="headerBtn">
                <el-button type="primary" @click="exportHandle">导出</el-button>
            </div>
            <div slot="content">
                <mssTable
                  ref="table"
                  :api="tableApi"
                  :columns="tableHeader"
                  border
                  :serial="false"
                >
                </mssTable>
            </div>
        </mssCard>
    </div>
</template>

<script>
import {queryAreaListService} from "@/api/common_api";
import {commonDown} from "@/utils/btn";
import {exportSummaryReport, getSummaryReport} from "@/api/pccw/analysis_link/special_line_survey_statistical_report";

export default {
    name: 'specialLineSurveyStatisticalReport',
    data() {
        return {
          staticSearchParam:{},
          tableApi: getSummaryReport,
            //搜索字段配置
            searchConfig: [
              {
                type: 'date2',
                label: '归档时间',
                fieldName: 'startTime',
                fieldName2: 'endTime',
              },
              {
                label: '地市',
                type: 'select',
                fieldName: 'city',
                options:[]
              },
              {
                label: '区县',
                type: 'select',
                fieldName: 'district',
                options:[]
              },
              {
                label: '设计院',
                type: 'input',
                fieldName: 'designUnit',
              }
            ],
            //表格头部配置
            tableHeader: [
              {
                prop: "city",
                label: "地市",
                minWidth: 120,
              },
              {
                prop: "district",
                label: "区县",
                minWidth: 80
              },
              {
                prop: "designUnit",
                label: "设计院",
                minWidth: 80,
              },
              {
                prop: "surveyFormNum",
                label: "勘察单数量",
                minWidth: 80,
              },
              {
                prop: "surveyFormTotalDuration",
                label: "勘察单总时长",
                minWidth: 80,
              },
              {
                prop: "constructionSystemDuration",
                label: "建设系统时长",
                minWidth: 80,
              },
              {
                prop: "secondSystemDuration",
                label: "二编系统时长",
                minWidth: 80,
              },
              {
                prop: "overtimeOrderNum",
                label: "超时工单数",
                minWidth: 80,
              },
              {
                prop: "overtimeOrderProportion",
                label: "超时工单占比",
                minWidth: 80,
              },
              {
                prop: "surveyForArchiveTime",
                label: "勘察单归档时间",
                minWidth: 80,
              }
            ],
        };
    },
    created() {
      this.getAreaList('-2', 1)
    },
    methods: {
      getAreaList(parentId, index) {
        queryAreaListService({ parentId: parentId, typeCode: 'area', filter: 'city' }).then(res => {
          if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
              list.push({ label: item.name, value: item.id })
            })
            this.$set(this.searchConfig[index], 'options', list)
          }
        })
      },
      changeSelect(name, val){
        if(name === 'city'){
          this.$set(this.$refs.searchForm.searchForm,'county','')
          this.getAreaList(val, 2)
        }
      },
      search(form) {
        this.staticSearchParam = JSON.parse(
          JSON.stringify(form)
        )
        this.$refs.table.getTableData(form)
      },
      reset(form) {
        this.search(form)
      },
      exportHandle() {
        console.log("导出参数：",this.staticSearchParam)
        commonDown({ ...this.staticSearchParam}, exportSummaryReport);
      }
    },
    mounted() {
        // Code to run when the component is mounted
    },
};
</script>

<style scoped>
/* Your component styles here */
</style>
