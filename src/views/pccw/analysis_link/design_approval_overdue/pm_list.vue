<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :form="staticSearchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button v-if="powerData['comprehensive_acceptance_export']" type="primary" @click="exportHandle">导出
        </el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :static-search-param="staticSearchParam"
          border
          selection
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import {listPM, downloadService} from "@/api/pccw/comprehensive/design_approval_overdue/pm_list"
import {commonDown} from "@/utils/btn";


export default {
  name: "DesignApprovalOverduePMList",
  data() {
    return {
      //权限
      powerData: [],
      //搜索字段配置
      searchConfig: [
        {
          label: '项目年度',
          type: 'select',
          fieldName: 'projectYear',
          options: [
            {label: '2023', value: '2023'},
            {label: '2022', value: '2022'},
            {label: '2021', value: '2021'},
            {label: '2020', value: '2020'}
          ]
        },
        {
          label: '项目经理',
          type: 'input',
          fieldName: 'projectManager',
        },
      ],
      // 表头
      tableHeader: [
        {
          prop: "projectManager",
          label: "省工建项目经理",
          align: "center",
          tooltip: true,
          width: 150
        },
        {
          prop: "projectNumber",
          label: "项目数",
          align: "center",
          tooltip: true,
          width: 150
        },
        {
          prop: "3",
          label: "第一批设计批复",
          multilevelColumn: [{
            prop: "03",
            label: "23年立项项目",
            minWidth: 300,
            multilevelColumn: [
              {
                prop: "numberOfFirstDesignApproval",
                label: "预警数（30天以内）",
                minWidth: 150,
              },
              {
                prop: "rateOfFirstDesignApproval",
                label: "及时率",
                minWidth: 150
              },
            ]
          }
          ]
        },
        {
          prop: "4",
          label: "整体设计批复",
          multilevelColumn: [{
            prop: "03",
            label: "23年立项项目",
            minWidth: 300,
            multilevelColumn: [
              {
                prop: "numberOfOverallDesignApproval",
                label: "预警数（30天以内）",
                minWidth: 150,
              },
              {
                prop: "rateOfOverallDesignApproval",
                label: "及时率",
                minWidth: 150
              },
            ]
          }
          ]
        },
        {
          prop: "5",
          label: "站点级设计批复",
          multilevelColumn: [
            {
              prop: "numberOfSiteDesignApproval",
              label: "不及时率项目数",
              minWidth: 150,
            },
            {
              prop: "rateOfSiteDesignApproval",
              label: "批复及时率",
              minWidth: 150
            },
          ]
        },
      ],
      // 表格数据API
      tableApi: listPM,
      // 搜索静态条件
      staticSearchForm: {

      },
      // 表格静态参数
      staticSearchParam: {

      },
      // 导出参数
      loadParam: {

      },
    }
  },
  created() {
    this.getPower()
    console.log("什么结果：", this.powerData['comprehensive_acceptance_add'])
    console.log("什么结果：", this.powerData['IssueCollect'])
  },
  methods: {
    getPower() {
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item) => {
        this.powerData[item.authority] = true
      })
      console.log(this.powerData, "权限信息")
    },


    /** 搜索按钮操作 */
    search(form) {
      this.staticSearchParam = {...form}
      this.$refs.table.page.current = 1

      this.staticSearchParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )

      this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
    },
    // 表单重置
    reset(form) {
      this.search(form)
    },

    /** 新增按钮操作 */
    addHandle() {

    },
    /** 修改按钮操作 */
    editHandle(row) {

    },
    /** 删除按钮操作 */
    delBatchHandle(row) {

    },
    /** 导出按钮操作 */
    exportHandle() {
      this.loadParam.limit = this.$refs.table.page.total
      console.log("导出参数：", this.loadParam)
      commonDown({...this.loadParam}, downloadService);
    }
  }
}
</script>
