<template>
  <div class="home">
    <div class="work-order">
      <div
        class="box-item canClick"
        v-for="(item, index) in workOrder"
        :key="index"
        @click="changOrder(item)"
      >
        <img
          class="item-icon"
          :src="require('@/assets/images/' + item.icon + '.png')"
          alt="item icon"
        />
        <div class="item-text">
          <span>{{ item.name }}</span>
          <span>
            <span>
              {{ item.num }}
            </span>
            个
          </span>
        </div>
      </div>
    </div>
    <div class="work-content">
      <div class="left">
        <div v-show="curWorkOrder == '我的待办'">
          <mssSearchForm
            :searchConfig="searchConfig"
            ref="SearchForm"
            @search="search"
            @reset="reset"
            :form="searchForm"
          ></mssSearchForm>
          <mssCard title="待办任务列表">
            <div slot="content">
              <mssTable
                key="todo"
                ref="todo"
                :columns="todoColumns"
                :api="todoApi"
                :customSize="5"
                :staticSearchParam="todoStaticSearchParam"
                @tableDataChange="todoTableDataChange"
              ></mssTable>
            </div>
          </mssCard>
        </div>
        <div v-show="curWorkOrder == '我的待阅'">
          <mssSearchForm
            :searchConfig="searchConfig"
            ref="SearchForm"
            @search="search"
            @reset="reset"
            :form="searchForm"
          ></mssSearchForm>
          <mssCard title="待阅任务列表">
            <div slot="content">
              <mssTable
                key="toread"
                ref="toread"
                :columns="toreadColumns"
                :api="toreadApi"
                :customSize="5"
                :staticSearchParam="toreadStaticSearchParam"
                @tableDataChange="toreadTableDataChange"
              ></mssTable>
            </div>
          </mssCard>
        </div>
        <div v-show="curWorkOrder == '我的已办'">
          <mssSearchForm
            :searchConfig="searchConfig"
            ref="SearchForm"
            @search="search"
            @reset="reset"
            :form="searchForm"
          ></mssSearchForm>
          <mssCard title="已办任务列表">
            <div slot="content">
              <mssTable
                key="done"
                ref="done"
                :columns="todoColumns"
                :api="todoApi"
                :customSize="5"
                :staticSearchParam="doneStaticSearchParam"
                @tableDataChange="doneTableDataChange"
              ></mssTable>
            </div>
          </mssCard>
        </div>
        <div v-show="curWorkOrder == '我的已阅'">
          <mssSearchForm
            :searchConfig="searchConfig"
            ref="SearchForm"
            @search="search"
            @reset="reset"
            :form="searchForm"
          ></mssSearchForm>
          <mssCard title="已阅任务列表">
            <div slot="content">
              <mssTable
                key="read"
                ref="read"
                :columns="toreadColumns"
                :api="toreadApi"
                :customSize="5"
                :staticSearchParam="readStaticSearchParam"
                @tableDataChange="readTableDataChange"
              ></mssTable>
            </div>
          </mssCard>
        </div>
      </div>
    </div>
    <menuDailog ref="menuDailog" @refresh="getTabsList"></menuDailog>
    <flowDialog
      :key="flowKey"
      ref="flowDialog"
      @closeFlowDialog="closeFlowDialog"
    ></flowDialog>
  </div>
</template>

<script>
  import {
    todoRouter,
    finishedRouter,
    msgSetting
  } from "@/views/pccw/workflow/router_setting/review.js"
  import { personalMsgsService } from "@/api/message_manager/message_manager_api"
  import { getShortCutService } from "@/api/user.js"
  import { getDetailsById } from "@/api/staging_point/inbound_outbound_api"
  import menuDailog from "@/views/home/<USER>"
  import flowDialog from "@/views/home/<USER>"
  import BpmnDiagram from "@/views/pccw/workflow/common/BpmnDiagram.vue"
  import { getPCCWTaskService } from "@/api/pccw/workflow/flowable"
  export default {
    name: "Home",
    components: {
      menuDailog,
      flowDialog,
      BpmnDiagram
    },
    data() {
      return {
        procInstId: "",
        dialogXML: false,
        workOrder: [
          {
            name: "我的待办",
            icon: "icon_db",
            num: 0
          },
          {
            name: "我的待阅",
            icon: "icon_dy",
            num: 0
          },
          {
            name: "我的已办",
            icon: "icon_yb",
            num: 0
          },
          {
            name: "我的已阅",
            icon: "icon_yy",
            num: 0
          }
        ],
        todoColumns: [
          {
            label: "任务名称",
            prop: "name",
            tooltip: true,
            formatter: row => {
              return (
                <span
                  class="table_cell_click"
                  onclick={() => {
                    this.toDetail(row)
                  }}
                >
                  {row.name}
                </span>
              )
            }
          },
          {
            label: "任务类型",
            prop: "workflowName",
            tooltip: true,
            width: 200
          },
          { label: "所处环节", prop: "nodeName", tooltip: true, width: 180 },
          { label: "派发人", prop: "preUserName", tooltip: true, width: 120 },
          { label: "派发时间", prop: "beginTime", tooltip: true, width: 200 },
          {
            label: "监控",
            prop: "jiankong",
            fixed: "right",
            width: 80,
            formatter: row => {
              return (
                <span
                  class="table_cell_click"
                  onclick={() => {
                    this.openFlow(row)
                  }}
                >
                  <i class="el-icon-data-analysis" title="查看流程图"></i>
                </span>
              )
            }
          }
        ],
        toreadColumns: [
          {
            label: "消息",
            prop: "title",
            tooltip: true,
            formatter: row => {
              return (
                <a
                  href="javascript:;"
                  onClick={() => {
                    this.operateHandle(row)
                  }}
                >
                  {row.title}
                </a>
              )
            }
          },
          {
            label: "派发人",
            prop: "creatorName",
            width: 100
          },
          {
            label: "接收人",
            prop: "acceptorName",
            width: 100
          },
          {
            label: "派发时间",
            prop: "createDate",
            width: 200
          },
          {
            label: "消息类型",
            prop: "messageTypeName",
            width: 100
          }
        ],
        todoApi: getPCCWTaskService,
        toreadApi: personalMsgsService,
        todoStaticSearchParam: {
          taskShow: 1,
          state: 0,
          userId: sessionStorage.getItem("userId"),
          workflowCode: '物资到货跟踪省本部流程'
        },
        doneStaticSearchParam: {
          taskShow: 1,
          state: 1,
          userId: sessionStorage.getItem("userId"),
          workflowCode: '物资到货跟踪省本部流程'
        },
        toreadStaticSearchParam: {
          acceptorId: sessionStorage.getItem("userId"),
          status: 1,
          searchValue: '物资到货跟踪订单'
        },
        readStaticSearchParam: {
          acceptorId: sessionStorage.getItem("userId"),
          status: 2,
          searchValue: '物资到货跟踪订单'
        },
        curWorkOrder: "我的待办",
        quickLinksData: [],
        activeName: "-1",
        singlePage: [
          "UsageRptMod",
          "SafetyReportCity",
          "SafetyReport",
          "StorageTempProc",
          "Inventory",
          "UsageRpt",
          "ReceiveAudit"
        ],
        sheetType: {
          "001001004001": "outbound",
          "001001004002": "inbound",
          "001001004003": "return"
        },
        sheetTypeCn: {
          "001001004001": "出库",
          "001001004002": "入库",
          "001001004003": "退库"
        },
        flowKey: 1,
        workflowCode: [],
        todoFlag: true,
        doneFlag: true,
        toreadFlag: true,
        readFlag: true,
        searchForm: {
          searchValue: '物资到货跟踪订单',
          workflowCode: '物资到货跟踪省本部流程'
        }
      }
    },
    created() {
      this.getTabsList()
      this.workflowCode = [
        "物资到货跟踪省本部流程",
        "物资到货跟踪地方流程",
      ].map(label => ({
        label,
        value: label
      }))
    },
    computed: {
      searchConfig () {
        if (
          this.curWorkOrder == "我的待办" ||
          this.curWorkOrder == "我的已办"
        ) {
          return [
            {
              label: "任务名称",
              type: "input",
              fieldName: "name"
            },
            {
              label: "任务类型",
              type: "select",
              fieldName: "workflowCode",
              filterable: true,
              options: this.workflowCode
            }
          ]
        } else {
          return [
            {
              label: "消息标题",
              type: "input",
              fieldName: "searchValue",
              disabled:'disabled'
            }
          ]
        }
      }
    },
    methods: {
      changOrder(item) {
        this.curWorkOrder = item.name
        this.$nextTick(() => {
          this.$refs.todo.$refs.table.doLayout()
          this.$refs.done.$refs.table.doLayout()
        })
      },
      toDetail(row) {
        console.log("toDetail")
        if (row.isTask == "boco") {
          if (row.workflowCode == "OutputInput") {
            let query = row.url.split("?")[1].split("&"),
              boId
            query.forEach(item => {
              if (item.includes("boId=")) {
                boId = item.split("=")[1]
              }
            })
            getDetailsById(boId).then(res => {
              if (res && res.code == "0000") {
                if (this.curWorkOrder == "我的待办") {
                  this.$router.push({
                    path: `/staging_point/material_${
                      this.sheetType[res.data.sheetType]
                    }_edit`,
                    query: {
                      boId: boId,
                      operateType: "edit",
                      storageTempId: res.data.storageId,
                      pathlabel: encodeURIComponent(
                        `物资${this.sheetTypeCn[res.data.sheetType]}处理页`
                      ),
                      fromPath: "unifyTodo"
                    }
                  })
                } else {
                  this.$router.push({
                    path: `/staging_point/material_${
                      this.sheetType[res.data.sheetType]
                    }_edit`,
                    query: {
                      boId: boId,
                      operateType: "details",
                      storageTempId: res.data.storageId,
                      pathlabel: encodeURIComponent(
                        `物资${this.sheetTypeCn[res.data.sheetType]}详情页`
                      ),
                      fromPath: "unifyTodo"
                    }
                  })
                }
              }
            })
          } else if (this.singlePage.includes(row.workflowCode)) {
            let url
            if (this.curWorkOrder == "我的待办") {
              url =
                row.url +
                `&operateType=edit&pathlabel=${encodeURIComponent(
                  row.workflowName + "处理页"
                )}&fromPath=unifyTodo`
            } else if (this.curWorkOrder == "我的已办") {
              url =
                row.url +
                `&operateType=details&pathlabel=${encodeURIComponent(
                  row.workflowName + "详情页"
                )}&fromPath=unifyTodo`
            }
            this.$router.push(url)
          } else {
            let url
            if (this.curWorkOrder == "我的待办") {
              url = row.url
            } else {
              url = row.url.replace("edit", "view")
            }
            this.$router.push(url + "&fromPath=unifyTodo")
          }
        } else if (row.isTask == "pccw") {
          // 二期待办处理
          const data = JSON.stringify(row)
          console.log("data", row)
          // console.log("name" + row.name);
          // console.log("data", data)
          if (this.curWorkOrder == "我的待办") {
            if (row.workflowName == "测试流程") {
              this.$router.push({
                path: `/pccw_menu/pccw_workflow/test`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
            } else if (row.workflowName.includes("安全工作填报流程")) {
              this.$router.push({
                path: `/pccw_menu/safety_production/workdOrderPending`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
            } else if (row.name.includes("安全生产费")) {
              this.$router.push({
                path: `/pccw_menu/implementation_link/secure_produce_pendingSave`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
            } else if (row.name.includes("物资需求采购待办")) {
              this.$router.push({
                path: `/pccw_menu/analysis_link/material_demand_handle`,
                query: {
                  flow: data,
                  type: "todo",
                  title: "物资需求采购待办工单"
                }
              })
            } else if (row.workflowName == "打卡任务清单审批流程") {
              this.$router.push({
                path: `/pccw_menu/analysis_link/check_task_list`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
            } else if (row.name == "物资到货跟踪特殊原因审批") {
              this.$router.push({
                path: `/pccw_menu/pccw_workflow/material_arrival_tracking/todo`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
              // 完工任务级竣工资料收集
            } else if (row.workflowName == "任务级竣工资料收集督办流程") {
              this.$router.push({
                path: `pccw_menu/pccw_workflow/task_completion`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
              // 任务转资
            } else if (row.name == "任务已完工，请在40天内完成割接交维") {
              this.$router.push({
                path: `pccw_menu/pccw_workflow/maintain_unfinished`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
            } else if (
              row.name ==
              "任务已达资产预定可使用状态，请同步完成割接交维和资产交维，割接交维结束次月月末前完成转资"
            ) {
              this.$router.push({
                path: `pccw_menu/pccw_workflow/maintain_finished`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
            } else if (row.name == "超割接交维结束的次月月末时间未转资督办单") {
              this.$router.push({
                path: `pccw_menu/pccw_workflow/supervise`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
            } else if (
              row.name ==
              "项目已结审，请在审计定案后的一个月内完成审减确认和转资数量在资产系统的提单修改"
            ) {
              this.$router.push({
                path: `pccw_menu/pccw_workflow/control`,
                query: {
                  flow: data,
                  type: "todo"
                }
              })
            } else {
              console.log("其他", row)
              todoRouter(row, "todo") // 调用引入的方法
            }
            // 二期我的已办 跳转添加
          } else {
            // console.log("已办")
            const data = JSON.stringify(row)
            if (row.workflowName == "测试流程") {
              this.$router.push({
                path: `/pccw_menu/pccw_workflow/test`,
                query: {
                  flow: data,
                  type: "finished"
                }
              })
            } else if (row.workflowName.includes("安全工作填报流程")) {
              this.$router.push({
                path: `/pccw_menu/safety_production/workdOrderPending`,
                query: {
                  flow: data,
                  type: "finished"
                }
              })
            } else if (row.name.includes("安全生产费")) {
              this.$router.push({
                path: `/pccw_menu/implementation_link/secure_produce_pendingSave`,
                query: {
                  flow: data,
                  type: "finished",
                  title: "安全生产费待办工单"
                }
              })
            } else if (row.name.includes("物资需求采购待办")) {
              this.$router.push({
                path: `/pccw_menu/analysis_link/material_demand_handle`,
                query: {
                  flow: data,
                  type: "finished",
                  title: "物资需求采购待办工单"
                }
              })
            } else if (row.workflowName == "打卡任务清单审批流程") {
              this.$router.push({
                path: `/pccw_menu/analysis_link/check_task_list`,
                query: {
                  flow: data,
                  type: "finished"
                }
              })
            } else if (row.name == "物资到货跟踪特殊原因审批") {
              this.$router.push({
                path: `/pccw_menu/pccw_workflow/material_arrival_tracking/todo`,
                query: {
                  flow: data,
                  type: "finished"
                }
              })
            } else if (row.workflowName == "任务级竣工资料收集督办流程") {
              this.$router.push({
                path: `pccw_menu/pccw_workflow/task_completion`,
                query: {
                  flow: data,
                  type: "finished"
                }
              })
            } else if (row.workflowName.includes("任务转资")) {
              finishedRouter(row, "finished")
            } else if (row.workflowName == "工序质量") {
              todoRouter(row, "finished")
            } else {

              finishedRouter(row, "finished")
            }
          }
        }
      },
      // 点击标题名称，跳转页面
      operateHandle(row) {
        console.log("row点击标题", row)
        const data = encodeURIComponent(JSON.stringify(row))
        if (row.title.includes("物资需采购提醒")) {
          this.$router.push({
            path: `/pccw_menu/analysis_link/material_demand_remind`,
            query: {
              flow: data,
              type: "herald",
              curWorkOrder: this.curWorkOrder,
              messageId: row.id,
              messageType: row.messageType
            }
          })
        } else if (row.title.includes("设计及时性周报:工建经理维度")) {
          this.$router.push({
            path: `/pccw_menu/analysis_link/weekly_provincial_overall`,
            query: {
              flow: data,
              curWorkOrder: this.curWorkOrder,
              type: "herald",
              messageId: row.id,
              messageType: row.messageType
            }
          })
        } else if (row.title.includes("设计及时性周报:地市维度")) {
          this.$router.push({
            path: `/pccw_menu/analysis_link/weekly_city_overall`,
            query: {
              flow: data,
              curWorkOrder: this.curWorkOrder,
              type: "herald",
              messageId: row.id,
              messageType: row.messageType
            }
          })
        } else if (row.title.includes("第一批设计批复预警")) {
          this.$router.push({
            path: `/pccw_menu/analysis_link/approval_warning`,
            query: {
              flow: data,
              type: "herald",
              messageId: row.id,
              messageType: row.messageType
            }
          })
        } else if (row.title.includes("整体设计批复预警")) {
          this.$router.push({
            path: `/pccw_menu/analysis_link/overall_approval_warning`,
            query: {
              flow: data,
              type: "herald",
              messageId: row.id,
              messageType: row.messageType
            }
          })
        } else if (row.title.includes("物资到货跟踪订单预警")) {
          //超期订单预警工单
          this.$router.push({
            path: `/pccw_menu/pccw_workflow/material_arrival_tracking/overdue_order_warning_work_order`,
            query: {
              flow: JSON.stringify(row),
              type: "alert",
              messageId: row.id,
              messageType: row.messageType
            }
          })
        } else if (row.title.includes("物资到货跟踪订单通报")) {
          //通报超期接收订单工单
          this.$router.push({
            path: `/pccw_menu/pccw_workflow/material_arrival_tracking/overdue_order_warning_work_order`,
            query: {
              flow: JSON.stringify(row),
              type: "herald",
              messageId: row.id,
              messageType: row.messageType
            }
          })
        } else if (row.title.includes("任务转资提前滞后率情况统计")) {
          //任务转资提前滞后率情况统计
          this.$router.push({
            path: `/pccw_menu/pccw_workflow/task_transfer/warning`,
            query: {
              flow: JSON.stringify(row),
              type: "herald",
              messageId: row.id,
              messageType: row.messageType
            }
          })
        } else {
          // 区分消息的消息类型 messageType: 0一般消息 1待确认后消除 2业务触发消除
          this.$router.push({
            path: row.url || "/message/view",
            query: {
              messageId: row.id,
              messageType: row.messageType
            }
          })
        }
      },
      todoTableDataChange(data, page) {
        if (this.todoFlag) {
          this.todoFlag = false
          this.workOrder[0].num = page.total || 0
        }
      },
      toreadTableDataChange(data, page) {
        if (this.toreadFlag) {
          this.toreadFlag = false
          this.workOrder[1].num = page.total || 0
        }
      },
      doneTableDataChange(data, page) {
        if (this.doneFlag) {
          this.doneFlag = false
          this.workOrder[2].num = page.total || 0
        }
      },
      readTableDataChange(data, page) {
        if (this.readFlag) {
          this.readFlag = false
          this.workOrder[3].num = page.total || 0
        }
      },
      toMenu(item) {
        this.$router.push({
          path: item
        })
      },
      // 获取tab数据
      getTabsList() {
        getShortCutService({ typeId: 1 }).then(res => {
          this.quickLinksData = res.data
        })
      },
      // 打开弹框配置自定义快速导航
      openPerSet() {
        this.$refs.menuDailog.init(this.quickLinksData)
      },
      openFlow(row) {
        if (row.isTask == "boco") {
          this.$refs.flowDialog.init(row)
        } else if (row.isTask == "pccw") {
          this.procInstId = row.procInstId
          this.dialogXML = true
        }
      },
      closeFlowDialog() {
        this.flowKey++
      },
      //搜索
      search(searchForm) {
        if (this.curWorkOrder == "我的待办") {
          this.$refs.todo.page.current = 1
          searchForm.searchValue = ''
          this.todoStaticSearchParam = {
            taskShow: 1,
            state: 0,
            userId: sessionStorage.getItem("userId"),
            ...searchForm
          }
          this.$refs.todo.getTableData(searchForm)
        } else if (this.curWorkOrder == "我的已办") {
          this.$refs.done.page.current = 1
          searchForm.searchValue = ''
          this.doneStaticSearchParam = {
            taskShow: 1,
            state: 1,
            userId: sessionStorage.getItem("userId"),
           ...searchForm
          }
          this.$refs.done.getTableData(searchForm)
        } else if (this.curWorkOrder == "我的待阅") {
          this.$refs.toread.page.current = 1
          searchForm.workflowCode = ''
          this.toreadStaticSearchParam = {
            acceptorId: sessionStorage.getItem("userId"),
            status: 1,
            ...searchForm
          }
          this.$refs.toread.getTableData(searchForm)
        } else if (this.curWorkOrder == "我的已阅") {
          this.$refs.read.page.current = 1
          searchForm.workflowCode = ''
          this.readStaticSearchParam = {
            acceptorId: sessionStorage.getItem("userId"),
            status: 2,
            ...searchForm
          }
          this.$refs.read.getTableData(searchForm)
        }
        this.searchForm = {
          searchValue: '物资到货跟踪订单',
          workflowCode: '物资到货跟踪省本部流程'
        }
      },
      //重置
      reset(searchForm) {
        searchForm.searchValue = '物资到货跟踪订单'
        searchForm.workflowCode = '物资到货跟踪省本部流程'
        this.search(searchForm)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .home {
    .work-order {
      display: flex;
      justify-content: space-between;
      background-color: transparent;
      margin: 0 0 10px;

      .canClick {
        cursor: pointer;
      }
      .box-item {
        width: 24%;
        position: relative;
        height: 4.5rem;
        line-height: 4.5rem;
        border-radius: 5px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        padding-left: 2rem;
        img {
          width: 3.2rem;
          height: 3.2rem;
        }

        .item-text {
          padding-right: 0.2rem;
          display: flex;
          align-items: left;
          flex-direction: column;
          margin-left: 1.5rem;
          font-size: 14px;
          color: #666;
          span:first-child {
            line-height: 1rem;
            height: 1.5rem;
          }

          span:last-child {
            line-height: 1rem;
            border-radius: 1rem;
            position: relative;
            span {
              color: #000;
              font-size: 1.3rem;
              font-weight: bold;
            }
          }
        }
      }
    }
    .work-content {
      display: flex;
      justify-content: space-between;
      .left {
        width: 100%;
        .card {
          margin-bottom: 10px;
        }
      }
    }
    .table_cell_click {
      cursor: pointer;
      color: #02a7f0;
      .el-icon-data-analysis {
        font-size: 14px;
      }
    }
    .more-setting {
      cursor: pointer;
    }
    ::v-deep .cus_menu {
      height: 68px;
      .el-tabs__header {
        margin: 0;
        border-bottom: none;
      }
      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        font-size: 22px;
      }
      .el-tabs__nav {
        border: none;
      }
      .el-tabs__item {
        border-left: none;
        &.is-active {
          color: #303133;
        }
        .cus-label {
          text-align: center;
          .el-icon-date {
            font-size: 18px;
          }
          div {
            line-height: 1;
          }
        }
      }
      .el-tabs__nav-scroll {
        line-height: 44px;
        .el-tabs__item {
          padding: 0 40px;
        }
      }
    }
    .nodata {
      text-align: center;
      color: #666;
    }
    .right-notice {
      height: 60vh;
    }
  }
</style>
