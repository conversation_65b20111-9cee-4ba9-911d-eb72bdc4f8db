<template>
    <div>
      <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
            <div slot="headerBtn">
                <el-button type="primary" @click="exportHandle">导出</el-button>
            </div>
            <div slot="content">
                <mssTable
                  ref="table"
                  :api="tableApi"
                  :columns="tableHeader"
                  :staticSearchParam="loadParam"
                  border
                  selection
                >
                </mssTable>
            </div>
        </mssCard>
    </div>
</template>

<script>

import { listDetail, downloadDetailService } from "@/api/pccw/analysis_link/material_arrival_tracking/api";
import { commonDown } from '@/utils/btn'
// 获取地市:queryAreaListService
import {queryAreaListService} from '@/api/common_api.js'

export default {
    name: 'Detail',
    data() {
        return {
          tableApi: listDetail,
          loadParam: {},  // 用于存储搜索条件
          searchConfig:[
            {
                label: '订单下达时间段',
                span: '10',
                type: 'date2',
                fieldName: 'orderDeliveryDateFrom',
                fieldName2: 'orderDeliveryDateTo'
            },
            {
                span: '10',
                label: '地市',
                type: 'select',
                fieldName: 'city'
            },

            {
                span: '10',
                label: '项目编码',
                type: 'input',
                fieldName: 'projectCode'
            },

            {
                span: '10',
                label: '项目名称',
                type: 'input',
                fieldName: 'projectName'
            },
            {
                span: '10',
                label: '工程管理经理（主）',
                type: 'input',
                fieldName: 'managementManager'
            },
            {
                span: '10',
                label: '工程实施经理（主）',
                type: 'input',
                fieldName: 'implementationManager'
            },
          ],
          tableHeader:[
              {
                prop: "city",
                label: "地市",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "projectNumber",
                label: "项目编号",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "projectName",
                label: "项目名称",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "implementationManager",
                label: "工程实施经理（主）",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "managementManager",
                label: "工程管理经理（主）",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "orderNumber",
                label: "订单号",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "contractNumber",
                label: "合同编号",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "supplier",
                label: "供应商",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "orderRequester",
                label: "订单需求人",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "purchaser",
                label: "采购员",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "orderDeliveryDate",
                label: "订单下达时间",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "materialCode",
                label: "物料编码",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "materialName",
                label: "物料名称",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "materialUnit",
                label: "物料单位",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "quantity",
                label: "数量",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "receivedQuantity",
                label: "接收数量",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "receivedDate",
                label: "接收日期",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "unreceivedQuantity",
                label: "未接收数量",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "unreceivedDays",
                label: "未接收天数（天）",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "notificationSent",
                label: "是否需通报（超30天）",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "submitForReview",
                label: "是否提交审核",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "submitTime",
                label: "提交审核时间",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "approveTime",
                label: "审核通过时间",
                align: "center",
                tooltip: true,
                width: 150
              },
          ]
        };
    },
    methods: {
      /**
       * 地市
       * @param parentId
       * @param index
       */
       getAreaList(parentId, index) {
        console.log('parentId', parentId)
        console.log('index', index)
        queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
          if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
              list.push({ label: item.name, value: item.name })
            })
            this.$set(this.searchConfig[index], 'options', list)
          }
        })
      },
      //导出
      exportHandle() {
          commonDown({ ...this.loadParam, limit: -1}, downloadDetailService);
      },
      //重置
      reset(form) {
          this.search(form)
      },
      //搜索
      search() {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
      },
    },
    mounted() {
        // Code to run when the component is mounted
    },
    created() {
      this.getAreaList('-2', 1)
    },
};
</script>

<style scoped>
/* Your component styles here */
</style>
