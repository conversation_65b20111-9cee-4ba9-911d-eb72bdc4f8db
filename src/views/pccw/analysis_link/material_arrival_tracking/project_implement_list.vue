<template>
    <div>
      <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
      ></mssSearchForm>
      <!--结果展示框-->
      <mssCard title="查询结果">
          <div slot="headerBtn">
              <el-button type="primary" @click="exportHandle">导出</el-button>
          </div>
          <div slot="content">
              <mssTable
                ref="table"
                :api="tableApi"
                :columns="tableHeader"
                :staticSearchParam="loadParam"
                border
                selection
              >
              </mssTable>
          </div>
      </mssCard>
      <ClickDetail ref="clickDetail"></ClickDetail>
    </div>
</template>

<script>

import { listProjectImplement, downloadImplementService } from "@/api/pccw/analysis_link/material_arrival_tracking/api";
import { commonDown } from '@/utils/btn'
import {queryAreaListService} from '@/api/common_api.js'
import ClickDetail from './clickDetail.vue'
export default {
    name: 'ProjectImplementList',
    components: {
      ClickDetail
    },
    data() {
      return {
          tableApi: listProjectImplement,
          loadParam: {},  // 用于存储搜索条件
          searchConfig:[
            {
                label: '订单下达时间段',
                type: 'date2',
                fieldName: 'orderDeliveryDateFrom',
                fieldName2: 'orderDeliveryDateTo'
            },
            {
                label: '项目实施经理',
                type: 'input',
                fieldName: 'implementationManager'
            },
            {
                label: '地市',
                type: 'select',
                fieldName: 'city'
            },
          ],
          tableHeader:[
              {
                prop: "city",
                label: "地市",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                prop: "implementationManager",
                label: "项目实施经理",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                prop: "timePeriod",
                label: "时间段",
                align: "center",
                tooltip: true,
                minWidth: 150
              },
              {
                prop: "totalOrderNumber",
                label: "总订单数",
                align: "center",
                tooltip: true,
                minWidth: 150,
                formatter: row => {
                  return (
                    <span
                      class="table_cell_click"
                      onclick={() => {
                        this.openFlow(row, '总订单数')
                      }}
                    >
                    {row.totalOrderNumber}
                    </span>
                  )
                }
              },
              {
                prop: "overdueOrdersNumber",
                label: "超期订单数",
                align: "center",
                tooltip: true,
                minWidth: 150,
                formatter: row => {
                  return (
                    <span
                      class="table_cell_click"
                      onclick={() => {
                        this.openFlow(row, '超期订单数')
                      }}
                    >
                    {row.overdueOrdersNumber}
                    </span>
                  )
                }
              },
              {
                prop: "overdueRate",
                label: "超期率",
                align: "center",
                tooltip: true,
                minWidth: 150,
                formatter: (val) => {
                  let label = ''
                  if (val.overdueRate !== null && val.overdueRate !== undefined) {
                    label = val.overdueRate + '%'
                  } else {
                    label = '0%';
                  }
                  return label
                }
              },
              {
                prop: "averageOverdueDuration",
                label: "平均超期时长",
                align: "center",
                tooltip: true,
                minWidth: 150,
                formatter: (val) => {
                  let label = ''
                  if (val.averageOverdueDuration !== null && val.averageOverdueDuration !== undefined) {
                    label = val.averageOverdueDuration + '天'
                  } else {
                    label = '0天';
                  }
                  return label
                }
              },
          ],
      };
    },
    methods: {
        openFlow (row, a) {
          var item = {
            limit: 10,
            page: 1,
            city: row.city,
            implementationManager: row.implementationManager,
            orderDeliveryDateFrom: row.timePeriod.split("—")[0],
            orderDeliveryDateTo: row.timePeriod.split("—")[1]
          }
          if (a === '超期订单数') {
            item.notificationSent = '是'
          }
          console.log(item)
          this.$refs.clickDetail.open(item)
        },
        getAreaList(parentId, index) {
          queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
            if (res.code === '0000') {
              const list = []
              res.data.forEach(item => {
                list.push({ label: item.name, value: item.name })
              })
              this.$set(this.searchConfig[index], 'options', list)
            }
          })
        },
        //导出
        exportHandle() {
          commonDown({ ...this.loadParam, limit: -1}, downloadImplementService);
        },
        //重置
        reset(form) {
          this.search(form)
        },
        //搜索
        search() {
          // this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        },
    },
    created() {
      this.getAreaList('-2', 3)
    },
};
</script>

<style lang="scss" scoped>
.table_cell_click {
  cursor: pointer;
  color: #02a7f0;
  .el-icon-data-analysis {
    font-size: 14px;
  }
}
/* Your CSS styles here */
</style>
