<template>
    <div>
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="按地市" name="city" >
          <byCity ref="city"></byCity>
        </el-tab-pane>
        <el-tab-pane label="按项目管理经理" name="projectManager"  lazy>
          <byProjectManager ref="projectManager"></byProjectManager>
        </el-tab-pane>
        <el-tab-pane label="按项目实施经理" name="projectImp" lazy>
          <byProjectImplementManager ref="projectImp"></byProjectImplementManager>
        </el-tab-pane>
        <el-tab-pane label="详情" name="detail"  lazy>
          <byDetail ref="detail"></byDetail>
        </el-tab-pane>
      </el-tabs>
    </div>
</template>

<script>

import byCity from "@/views/pccw/analysis_link/material_arrival_tracking/city_list.vue";
import byProjectManager from "@/views/pccw/analysis_link/material_arrival_tracking/project_manage_list.vue";
import byProjectImplementManager from "@/views/pccw/analysis_link/material_arrival_tracking/project_implement_list.vue";
import byDetail from "@/views/pccw/analysis_link/material_arrival_tracking/detail.vue";

export default {
    name: 'MeterialArrivalTracking',
    components: {
        byCity,
        byProjectManager,
        byProjectImplementManager,
        byDetail
    },
    data() {
        return{
          activeName: 'city',   
        }
    },


    methods: {

      handleClick() {

      },
    },
} 
</script>

<style scoped>
/* Your component styles here */
</style>
