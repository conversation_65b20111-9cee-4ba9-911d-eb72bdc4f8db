<template>
    <div>
      <el-dialog
        title="详情"
        :visible.sync="dialogVisible"
        :close-on-click-modal='false' 
        @close='onClose'
        width="85%">
        <!--结果展示框-->
        <mssCard title="查询结果">
            <div slot="content">
                <mssTable
                  ref="table"
                  :api="tableApi"
                  :columns="tableHeader"
                  :staticSearchParam="loadParam"
                  border
                  selection
                >
                </mssTable>
            </div>
        </mssCard>
      </el-dialog>  
    </div>
</template>

<script>

import { listDetail } from "@/api/pccw/analysis_link/material_arrival_tracking/api";
export default {
    name: 'Detail',
    data() {
        return {
          tableApi: listDetail,
          loadParam: {},  // 用于存储搜索条件
          tableHeader:[
              {
                prop: "city",
                label: "地市",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "projectNumber",
                label: "项目编号",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "projectName",
                label: "项目名称",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "implementationManager",
                label: "工程实施经理（主）",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "managementManager",
                label: "工程管理经理（主）",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "orderNumber",
                label: "订单号",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "contractNumber",
                label: "合同编号",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "supplier",
                label: "供应商",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "orderRequester",
                label: "订单需求人",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "purchaser",
                label: "采购员",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "orderDeliveryDate",
                label: "订单下达时间",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "materialCode",
                label: "物料编码",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "materialName",
                label: "物料名称",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "materialUnit",
                label: "物料单位",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "quantity",
                label: "数量",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "receivedQuantity",
                label: "接收数量",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "receivedDate",
                label: "接收日期",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "unreceivedQuantity",
                label: "未接收数量",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "unreceivedDays",
                label: "未接收天数（天）",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "notificationSent",
                label: "是否需通报（超30天）",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "submitForReview",
                label: "是否提交审核",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "submitTime",
                label: "提交审核时间",
                align: "center",
                tooltip: true,
                width: 150
              },
              {
                prop: "approveTime",
                label: "审核通过时间",
                align: "center",
                tooltip: true,
                width: 150
              },
          ],
          dialogVisible: false
        };
    },
    methods: {
      onClose () {
        this.dialogVisible = false
      },
      open (item) {
        this.dialogVisible = true
        this.loadParam = item
        console.log(this.loadParam);
        this.$refs.table.getTableData(this.loadParam);
      },
    },
    mounted() {
        // Code to run when the component is mounted
    },
    created() {

    },
};
</script>

<style scoped>
/* Your component styles here */
</style>
