<template>
    <div>
      <div v-show="isShow" style="position:relative">
       <div id="main1" style="width:100%; height: 650px" ></div>
        <el-button @click="search(1);isShow = false" class="province-wide">全省</el-button>
      </div>
      <div v-show='!isShow'>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
          :form="searchForm"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
            <div slot="headerBtn">
                <el-button type="primary" @click="returnChart()">返回图表</el-button>
                <el-button type="primary" @click="exportHandle">导出</el-button>
            </div>
            <div slot="content">
                <mssTable
                  :serial='false'
                  ref="table"
                  :api="tableApi"
                  :columns="tableHeader"
                  :staticSearchParam="loadParam"
                  :pagination='false'
                  border
                >
                </mssTable>
            </div>
        </mssCard>
        <ClickDetail ref="clickDetail"></ClickDetail>
      </div>
    </div>
</template>
<script>
import { listCity, downloadCityService } from "@/api/pccw/analysis_link/material_arrival_tracking/api";
import { commonDown } from '@/utils/btn'
// 获取地市:
import {queryAreaListService} from '@/api/common_api.js'
import * as echarts from 'echarts'
import ClickDetail from './clickDetail.vue'
export default {
    name: 'city_list',
    components: {
      ClickDetail
    },
    data() {
        return {
            tableApi: listCity,
            loadParam: {},  // 用于存储搜索条件
            searchConfig:[
              {
                  label: '订单下达时间段',
                  type: 'date2',
                  fieldName: 'orderDeliveryDateFrom',
                  fieldName2: 'orderDeliveryDateTo'
              },
              {
                  label: '地市',
                  type: 'select',
                  fieldName: 'city'
              },
            ],
            tableHeader:[
                {
                  prop: "city",
                  label: "地市",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "timePeriod",
                  label: "时间段",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
                },
                {
                  prop: "totalOrderNumber",
                  label: "总订单数",
                  align: "center",
                  tooltip: true,
                  minWidth: 150,
                  formatter: row => {
                    return (
                      <span
                        class="table_cell_click"
                        onclick={() => {
                          this.openFlow(row, '总订单数')
                        }}
                      >
                      {row.totalOrderNumber}
                      </span>
                    )
                  }
                },
                {
                  prop: "overdueOrdersNumber",
                  label: "超期订单数",
                  align: "center",
                  tooltip: true,
                  minWidth: 150,
                  formatter: row => {
                    return (
                      <span
                        class="table_cell_click"
                        onclick={() => {
                          this.openFlow(row, '超期订单数')
                        }}
                      >
                      {row.overdueOrdersNumber}
                      </span>
                    )
                  }
                },
                {
                  prop: "overdueRate",
                  label: "超期率",
                  align: "center",
                  tooltip: true,
                  minWidth: 150,
                  formatter: (val) => {
                    let label = ''
                    if (val.overdueRate !== null && val.overdueRate !== undefined) {
                      label = val.overdueRate + '%'
                    } else {
                      label = '0%';
                    }
                    return label
                  }
                },
                {
                  prop: "averageOverdueDuration",
                  label: "平均超期时长",
                  align: "center",
                  tooltip: true,
                  minWidth: 150,
                  formatter: (val) => {
                    let label = ''
                    if (val.averageOverdueDuration !== null && val.averageOverdueDuration !== undefined) {
                      label = val.averageOverdueDuration + '天'
                    } else {
                      label = '0天';
                    }
                    return label
                  }
                },
            ],
            overdueRatelist: [],
            averageOverdueDurationList: [],
            cityList: [],
            isShow: true,
            searchForm: {}
        }
    },
    methods: {
      openFlow (row, a) {
        var item = {
          limit: 10,
          page: 1,
          city: row.city,
          implementationManager: row.implementationManager,
          orderDeliveryDateFrom: row.timePeriod.split("—")[0],
          orderDeliveryDateTo: row.timePeriod.split("—")[1]
        }
        if (a === '超期订单数') {
          item.notificationSent = '是'
        }
        console.log(item)
        this.$refs.clickDetail.open(item)
      },
      returnChart () {
        this.$refs.searchForm.searchForm = {
          city: "",
          orderDeliveryDateFrom: ""
        }
        this.searchForm = {}
        this.loadParam = {}
        this.isShow = true
        this.getAreaList('-2', 1)
      },
      drawChart () {
        var chartDom1 = document.getElementById('main1')
        var myChart1 = echarts.init(chartDom1)
        var label = ['总订单数', '超期率']
        let domData1 = this.averageOverdueDurationList
        let domData2 = this.overdueRatelist
        const maxNumber = Math.max(...this.averageOverdueDurationList) + 50
        var option
        option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: label //标题
          },
          xAxis: {
            type: 'category',
            data: this.cityList, //横轴项目集
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              interval: 0,
              rotate: -20
            }
          },
          yAxis: [
            {
              type: 'value',
              min: 0,
              max: maxNumber,
              interval: 100,
              axisLabel: {
                formatter: '{value}'
              }
            }, {
              type: 'value',
              min: 0,
              max: 100,
              interval: 10,
              axisLabel: {
                formatter: '{value} %'
              }
            }],
          series: [{
            name: '总订单数',
            type: 'bar',
            tiled: '总订单数',
            data: domData1,
            barWidth: '50px',
            label: {
                show: true, // 显示标签
                position: 'top', // 标签位置
                // 可以通过formatter自定义显示格式
                formatter: '{c}' // {c}表示数据值
            },
            tooltip: {
              valueFormatter: function (value) {
                return value
              }
            }
          },{
            yAxisIndex: 1,
            name: '超期率',
            type: 'line',
            tiled: '超期率',
            data: domData2,
            barWidth: '50px',
            label: {
                show: true, // 显示标签
                position: 'top', // 标签位置
                // 可以通过formatter自定义显示格式
                formatter: '{c}'+'%' // {c}表示数据值
            },
            tooltip: {
              valueFormatter: function (value) {
                return value + ' %'
              }
            }
          }]
        }
        myChart1.on('click', (params) => {
            this.isShow = false
            this.$refs.searchForm.searchForm.city = params.name
            this.searchForm = params.name
            this.search()
        })
        myChart1.setOption(option, true) // 重新渲染
        option && myChart1.setOption(option)
      },
      /**
       * 地市
       * @param parentId
       * @param index
       */
       getAreaList(parentId, index) {
        queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
          if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
              list.push({ label: item.name, value: item.name })
            })
            this.$set(this.searchConfig[index], 'options', list)
          }
        })
        listCity({}).then((res) => {
          this.overdueRatelist = []
          this.averageOverdueDurationList = []
          this.cityList = []
          res.data.forEach(item => {
            if (item.city !== '全省') {
              this.cityList.push(item.city)
              this.overdueRatelist.push(item.overdueRate)
              this.averageOverdueDurationList.push(item.totalOrderNumber)
            }
          });
          this.drawChart()
        })
      },
      //导出
      exportHandle() {
          commonDown({ ...this.loadParam, limit: -1}, downloadCityService)
      },
      //重置
      reset(form) {
        this.search(form)
      },
      //搜索
      search(val) {
        if (val == 1) {
          this.$refs.searchForm.searchForm = {
            city: "",
            orderDeliveryDateFrom: ""
          }
          this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        } else {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        }

      },
    },
    created() {
      this.getAreaList('-2', 1)
    },
}
</script>

<style scoped  lang="scss" >
.province-wide{
  position: absolute;
  top: 0.1%;
  left: 8.3%;
  z-index: 1000;
}
.table_cell_click {
  cursor: pointer;
  color: #02a7f0;
  .el-icon-data-analysis {
    font-size: 14px;
  }
}
/* Your styles here */
</style>
