<!-- 预警模板 -->
<template>
  <div class="design_warning">
    <mssCard title="预警模板">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
        <el-button @click="del">删除</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
        </mssTable>
        </el-form>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { warningTemplateList, updateTimeConfig, deleteTimeConfig, insertTimeConfig} from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
import { commonMultDel, commonOneDel } from "@/utils/btn";
export default {
  name: "receivingPersonnel",
  data() {
    return {
      tableData: [],
      form: {},
      rules: {}
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "day",
            label: "预警时间",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`day${index}`}>
                  <el-input  v-model={row.day} 
                  onChange={(value) => {
                    const regex = /^[0-9]*$/; // 正则表达式，匹配最多四位小数的数字
                      if (!regex.test(value)) {
                        // 如果输入值不符合要求，清空输入框
                        row.day = '';
                        this.$message.error('请输入数字');
                      }
                    this.form[`day${index}`] = value;
                  }}
                  >
                    <template slot="append">天</template>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    getTable() {
      warningTemplateList({}).then((res) => {
        if (res.code == "0000") {
          this.tableData  = res.data.data || []
        }
      });
    },
    addTable() {
      let data = {
        day: ""
      };
      insertTimeConfig(data).then((res) => {
        if (res.code == "0000") {
          this.tableData.push({ ...data, id: res.data });
        }
      });
    },
    deleteTable(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteTimeConfig({ids: row.id}).then(res => {
            if (res.code == '0000') {
              this.$message.success("删除成功");
              this.getTable();
            } else {
              this.$message.error(`${res.msg || '删除失败'}`)
            }
          })
        }).catch(() => {})
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            let obj = JSON.parse(JSON.stringify(row));
            updateTimeConfig(obj).then((res) => {
              if (res.code == "0000") {
                this.$message.success("保存成功");
              }
            });
          } else {
            return;
          }
        });
      });
    },
    // 删除
    del() {
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: deleteTimeConfig,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      if (arr.day === '' || arr.day == null || arr.day == undefined) {
        this.rules[`day${index}`] = {
          required: true,
          message: "该字段不能为空",
          trigger: ["blur", "change"],
        };
      } else {
        this.rules[`day${index}`] = {}
      }
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


