<!-- 设计时长 -->
<template>
  <div class="design_warning">
    <mssCard title="设计时长">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
        <!-- <el-button type="primary" @click="save">保存</el-button> -->
        <el-button @click="del">删除</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="true"
            :api="tableApi"
          >
        </mssTable>
        </el-form>
      </div>
    </mssCard>
    <magnifyingGlass ref="magnifyingGlass" @showCheckList="showCheckList" :mult-select="multSelect"></magnifyingGlass>
  </div>
</template>

<script>
import {
  getDurationConfigList,
  updateTimeConfig,
  deleteUserConfig,
  insertDurationConfig,
  updateDurationConfig,
  getListGroupByUnit
} from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
import { commonMultDel, commonOneDel } from "@/utils/btn";
import magnifyingGlass from "@/views/pccw/components/magnifyingGlass/index.vue";
export default {
  name: "receivingPersonnel",
  components: {
    magnifyingGlass
  },
  data() {
    return {
      tableData: [],
      tableApi: getDurationConfigList,
      form: {},
      rules: {},
      urationList: [],
      multSelect: false
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "month",
            label: "一阶段设计批复要求时长",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`month${index}`}>
                  <el-input
                    v-model={row.month}
                    onChange={(value) => {
                      const regex = /^\d+(\.\d)?$/; // 正则表达式，匹配整数或最多一位小数的数字
                      if (!regex.test(value)) {
                        // 如果输入值不符合要求，清空输入框
                        row.month = '';
                      } else {
                        this.form[`month${index}`] = value;
                      }
                    }}
                  >
                    <template slot="append">月</template>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "name",
            label: "标准工期专业分类",
            minWidth: 300,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`name${index}`}>
                  <el-input v-model={row.name}
                            readonly onFocus={() => {
                    this.openChooseUserDailog(row, index)
                    }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    showCheckList (val) {
      this.$set(this.tableData[val.index], 'name' , val.checkList[0].realName)
    },
    openChooseUserDailog (row, index) {
      const item = {
        excuterNames: row.name,
        excuterIds: row.name
      }
      this.$refs.magnifyingGlass.init(item, '设计时长', index)
    },

    getTable() {
      getDurationConfigList({}).then((res) => {
        if (res.code == "0000") {
          this.tableData  = res.data.data || []
        }
      });
    },
    addTable() {
      let data = {
        day: ""
      };
      insertDurationConfig(data).then((res) => {
        if (res.code == "0000") {
          this.tableData.push({ ...data, id: res.data });
        }
      });
    },
    deleteTable(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteUserConfig({ids: row.id}).then(res => {
            if (res.code == '0000') {
              this.$message.success("删除成功");
              this.getTable();
            } else {
              this.$message.error(`${res.msg || '删除失败'}`)
            }
          })
        }).catch(() => {})
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            let obj = JSON.parse(JSON.stringify(row));
            updateDurationConfig(obj).then((res) => {
              if (res.code == "0000") {
                this.$message.success("保存成功");
              } else {
                this.$message.error(res.msg);
              }
            });
          } else {
            return;
          }
        });
      });
    },
    save() {
      // let arr = []
      // this.tableData.forEach(item => {
      //   arr.push({
      //     departmentLeader: item.departmentLeader.toString(),
      //     cityConstructionManager: item.cityConstructionManager.toString(),
      //     provincialDepartmentManager: item.provincialDepartmentManager.toString(),
      //     specialStaffing: item.specialStaffing.toString()
      //   })
      // })
      // this.commonsetRule(arr);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            if (this.validateAll()) {
              let arr = JSON.parse(JSON.stringify(this.tableData));
              // arr.forEach((item) => {
              //   this.commondealName(item)
              // });
              updateTimeConfig(arr).then((res) => {
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              });
            } else {
              this.$message.warning(`相同预警级别、相同预警方式只能有一条数据，请修改数据`);
            }
          } else {
            return;
          }
        });
      });
    },
    // 删除
    del() {
      console.log(this.$refs.table.multipleSelection)
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: deleteUserConfig,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "month",
        "name"
      ];
      a.forEach((item) => {
        if (index) {
          this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          for (let index in arr) {
            this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
            this.rules[`${item}${index}`] = {
              required: true,
              message: "该字段不能为空",
              trigger: ["blur", "change"],
            };
          }
        }
      });
    },
    // 校验所有的数据
    validateAll() {
      const obj = {}
      for (let i = 0; i < this.tableData.length; i++) {
        const current = this.tableData[i]
        if (obj[`${current.levelNo}_${current.warnType}`]) {
          return false
        }
        obj[`${current.levelNo}_${current.warnType}`] = true
      }
      return true
    },
    // 处理其中文字
    commondealName(row){
      let a = [
        "day"
      ]
      a.forEach(item=>{
        row[`${item}Name`]=this[`${item}List`].filter((p)=>{return p.value==row[item]})[0].label
      })
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


