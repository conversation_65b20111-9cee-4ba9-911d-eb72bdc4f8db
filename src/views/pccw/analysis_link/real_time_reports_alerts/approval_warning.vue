<template>
  <div>
    <mssCard title="第一批设计批复预警">
      <div slot="content">
          <mssTable
            ref="table"
            :columns="tableHeader"
            :staticSearchParam="loadParam"
            :api="tableApi"
            border
            selection
          >
          </mssTable>
      </div>
    </mssCard>
  </div>
</template>
<script>
import { getWarningDetail } from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
export default {
  data () {
    return {
      tableApi: getWarningDetail,
      dialogVisible: false,
      tableData: [],
      tableHeader: [
        {
          prop: "projectCode",
          label: "项目编码",
          minWidth: 150,
        },
        {
          prop: "projectName",
          label: "项目名称",
          minWidth: 150,
        },
        {
          prop: "approveTime",
          label: "立项批复时间",
          minWidth: 150,
        },
        {
          prop: "constructionUnitProjectManager",
          label: "建设单位项目经理",
          minWidth: 150,
        },
        {
          prop: "standardDurationProfessionalClassification",
          label: "标准工期专业分类",
          minWidth: 150,
        },
        {
          prop: "engineeringConstructionCenterManager",
          label: "工建中心项目经理",
          minWidth: 200,
        },
        {
          prop: "deadlineTime",
          label: "第一批设计截止时间",
          minWidth: 150,
        },
        {
          prop: "remainingTime",
          label: "距离第一批设计批复截止时间(天)",
          minWidth: 200,
        }
      ],
      loadParam: {}
    }
  },
  created () {
    let flow = decodeURIComponent(this.$route.query.flow)
    this.flow = JSON.parse(flow); // 获取流程对象
    console.log(this.flow)
    this.loadParam = {
      limit: 10,
      page: 1,
      totalType: '1',
      calulateTime: this.$moment(this.flow.createDate).format('YYYY-MM-DD'),
      receivePerson: this.flow.acceptorName,
    }
  },
  methods: {
  }
} 
</script>
