<template>
    <div>  
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <!-- <el-tab-pane label="按地市" name="city" lazy>
            <byCity></byCity>
          </el-tab-pane> -->
          <el-tab-pane label="建设单位" name="pm" lazy>
            <byProjectManagementList></byProjectManagementList>
          </el-tab-pane>
          <el-tab-pane label="工程实施经理" name="effect" lazy>
            <projectLmplementation></projectLmplementation>
          </el-tab-pane>
          <el-tab-pane label="工程管理经理" name="manage" lazy> 
            <engineeringManagement></engineeringManagement>
          </el-tab-pane>
          <el-tab-pane label="详情" name="detail" lazy>
            <byDetail></byDetail>
          </el-tab-pane>
          <el-tab-pane label="接收人员配置界面" name="receiving" lazy>
            <receivingPersonnel></receivingPersonnel>
          </el-tab-pane>
          <el-tab-pane label="预警模板" name="warningTemplate" lazy>
            <warningTemplate></warningTemplate>
          </el-tab-pane>
          <el-tab-pane label="设计时长" name="designDuration" lazy>
            <designDuration></designDuration>
          </el-tab-pane>
          <!-- <el-tab-pane label="一阶段设计批复预警" name="approvalWarning">
            <approvalWarning></approvalWarning>
          </el-tab-pane>
          <el-tab-pane label="整体设计批复预警" name="overallApprovalWarning">
            <overallApprovalWarning></overallApprovalWarning>
          </el-tab-pane> -->
        </el-tabs>
      </div>
    </div>
</template>

<script>
import byDetail from "@/views/pccw/analysis_link/real_time_reports_alerts/detail.vue";
// import byCity from "@/views/pccw/analysis_link/real_time_reports_alerts/city_list.vue";
import byProjectManagementList from "@/views/pccw/analysis_link/real_time_reports_alerts/project_mangement_list.vue";
import projectLmplementation from "@/views/pccw/analysis_link/real_time_reports_alerts/project_lmplementation.vue";
import engineeringManagement from "@/views/pccw/analysis_link/real_time_reports_alerts/engineering_management.vue";
import approvalWarning from "@/views/pccw/analysis_link/real_time_reports_alerts/approval_warning.vue";
import overallApprovalWarning from "@/views/pccw/analysis_link/real_time_reports_alerts/overall_approval_warning.vue";
import receivingPersonnel from "@/views/pccw/analysis_link/real_time_reports_alerts/receivingPersonnel.vue";
import warningTemplate from "@/views/pccw/analysis_link/real_time_reports_alerts/warningTemplate.vue";
import designDuration from "@/views/pccw/analysis_link/real_time_reports_alerts/designDuration.vue";
export default {
    name: 'RealTimeReportsAlerts',
    components: {
      byDetail,
      byProjectManagementList,
      projectLmplementation,
      engineeringManagement,
      approvalWarning,
      overallApprovalWarning,
      receivingPersonnel,
      warningTemplate,
      designDuration
    },
    data(){
        return {
          activeName: 'pm',
        }
    },
    methods: {     
      handleClick () {

      },  
    },
}
</script>

<style scoped>
/* Your component's CSS code goes here */
</style>
