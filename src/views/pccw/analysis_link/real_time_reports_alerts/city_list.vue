<template>
    <div>
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <!--结果展示框-->
      <mssCard title="查询结果">
          <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              :staticSearchParam="loadParam"
              border
              selection
            >
            </mssTable>
          </div>
        </mssCard>
    </div>
</template>

<script>

import {listCity, downloadService} from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
// 获取地市:queryAreaListService
import {queryAreaListService} from '@/api/common_api.js'
import { commonDown } from '@/utils/btn'


export default {
    name: 'Sum',
    data() {
        return {
            tableApi: listCity,
            searchConfig:[
              {
                  label: '地市',
                  type: 'select',
                  fieldName: 'unit'
              },
            ],
            // 表头
            tableHeader: [
                {
                  prop: "city",
                  label: "地市",
                  align: "center",
                  tooltip: true,
                  minWidth: 90
                },
                {
                  prop: "projectNumber",
                  label: "项目数",
                  align: "center",
                  tooltip: true,
                },
                {
                  prop: "firstDesignApproval",
                  label: "第一批设计批复",
                  multilevelColumn: [
                    {
                      prop: "alertNumberOfFirstDesignApproval",
                      label: "预警数（30天以内）",
                      minWidth: 60,
                    },
                    {
                      prop: "overdueNumberOfFirstDesignApproval",
                      label: "超期项目数",
                      minWidth: 100,
                    },
                    {
                      prop: "rateOfFirstDesignApproval",
                      label: "及时率",
                    },
                    {
                      prop: "averageDurationOfFirstDesignApproval",
                      label: "平均时长",
                    },
                  ]
                },
                {
                  prop: "overallDesignApproval",
                  label: "整体设计批复",
                  multilevelColumn: [
                    {
                      prop: "numberOfOverallDesignApproval",
                      label: "预警数（30天以内）",
                      minWidth: 60,
                    },
                    {
                      prop: "overdueNumberOfOverallDesignApproval",
                      label: "超期项目数",
                      minWidth: 100,
                    },
                    {
                      prop: "rateOfOverallDesignApproval",
                      label: "及时率",
                    },
                    {
                      prop: "averageDurationOfOverallDesignApproval",
                      label: "平均时长",
                    },
                  ]
                },
                {
                  prop: "5",
                  label: "站点级设计批复",
                  multilevelColumn: [
                    {
                      prop: "numberOfSiteDesignApproval",
                      label: "预警数（30天以内）",
                      minWidth: 60,
                    },
                    {
                      prop: "overdueNumberOfSiteDesignApproval",
                      label: "超期项目数",
                      minWidth: 100,
                    },
                    {
                      prop: "rateOfSiteDesignApproval",
                      label: "及时率",
                    },
                    {
                      prop: "averageDurationOfSiteDesignApproval",
                      label: "平均时长",
                    },
                  ]
                },
            ],
        };
    },
    methods: {
      /**
       * 地市
       * @param parentId
       * @param index
       */
      getAreaList(parentId, index) {
        console.log('parentId', parentId)
        console.log('index', index)
        queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
          if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
              list.push({ label: item.name, value: item.id })
            })
            this.$set(this.searchConfig[index], 'options', list)
          }
        })
      },
      //导出
      exportHandle() {
            commonDown({ ...this.loadParam, limit: -1}, downloadService);
        },
        //重置
        reset(form) {
            this.search(form)
        },
        //搜索
        search() {
            this.$refs.table.page.current = 1
            this.loadParam = JSON.parse(
              JSON.stringify(this.$refs.searchForm.searchForm)
            )
            this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        },
    },
    created() {
      this.getAreaList('-2', 0)
    },
    mounted() {
        // Code to run when the component is mounted
    },
};
</script>

<style scoped>
/* Your component styles here */
</style>
