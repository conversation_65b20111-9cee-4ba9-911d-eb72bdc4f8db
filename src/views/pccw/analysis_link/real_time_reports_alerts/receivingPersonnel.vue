<!-- 接收人员配置界面 -->
<template>
  <div class="design_warning">
    <mssCard title="接收人员配置界面">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
        <!-- <el-button type="primary" @click="save">保存</el-button> -->
        <el-button @click="del">删除</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
        </mssTable>
        </el-form>
      </div>
    </mssCard>
    <magnifyingGlass ref="magnifyingGlass" @showCheckList="showCheckList" :mult-select="multSelect"></magnifyingGlass>
  </div>
</template>

<script>
import { getUserConfigList, userList, updateConfig, delService, insertConfig} from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
import { commonMultDel, commonOneDel } from "@/utils/btn";
import magnifyingGlass from "@/views/pccw/components/magnifyingGlass/index.vue";
export default {
  name: "receivingPersonnel",
  components: {
    magnifyingGlass
  },
  data() {
    return {
      tableData: [],
      form: {
      },
      rules: {},
      multSelect: true
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          // {
          //   prop: "tempDesc",
          //   label: "通报模板文字说明",
          //   minWidth:200,
          //   formatter: (row, column, cellValue, index) => {
          //     return (
          //       <el-form-item>
          //         <el-input  v-model={row.tempDesc}>
          //         </el-input>
          //       </el-form-item>
          //     );
          //   },
          // },
          {
            prop: "departmentLeader",
            label: "部门领导(副)",
            minWidth: 300,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`departmentLeader${index}`}>
                  <el-input  v-model={row.departmentLeader}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '部门领导(副)')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "cityConstructionManager",
            label: "地市工建经理",
            minWidth: 300,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`cityConstructionManager${index}`}>
                 <el-input  v-model={row.cityConstructionManager}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '地市工建经理')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "provincialDepartmentManager",
            label: "省公司科室经理",
            minWidth: 300,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`provincialDepartmentManager${index}`}>
                  <el-input  v-model={row.provincialDepartmentManager}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '省公司科室经理')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "provincialProjectManager",
            label: "省管项目经理",
            minWidth: 300,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`provincialProjectManager${index}`}>
                  <el-input  v-model={row.provincialProjectManager}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '省管项目经理')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "provincialIndustrialLeader",
            label: "省工建领导",
            minWidth: 300,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`provincialIndustrialLeader${index}`}>
                   <el-input  v-model={row.provincialIndustrialLeader}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '省工建领导')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "specialStaffing",
            label: "设计统筹人员",
            minWidth: 300,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`specialStaffing${index}`}>
                   <el-input  v-model={row.specialStaffing}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '设计统筹人员')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    showCheckList(val) {
        let realName = []
        let userId = []
        if (val.checkList.length > 0) {
          val.checkList.forEach(item => {
            realName.push(item.realName)
            userId.push(item.userId)
          })
        }
      if (val.deptParams == '部门领导(副)') {
        this.$set(this.tableData[val.index], 'departmentLeader' , realName.toString())
        this.$set(this.tableData[val.index], 'departmentLeaderId' , userId.toString())
      } else if (val.deptParams == '地市工建经理') {
        this.$set(this.tableData[val.index], 'cityConstructionManager' , realName.toString())
        this.$set(this.tableData[val.index], 'cityConstructionManagerId' , userId.toString())
      } else if (val.deptParams == '省公司科室经理') {
        this.$set(this.tableData[val.index], 'provincialDepartmentManager' , realName.toString())
        this.$set(this.tableData[val.index], 'provincialDepartmentManagerId' , userId.toString())
      } else if (val.deptParams == '省管项目经理') {
        this.$set(this.tableData[val.index], 'provincialProjectManager' , realName.toString())
        this.$set(this.tableData[val.index], 'provincialProjectManagerId' , userId.toString())
      } else if (val.deptParams == '省工建领导') {
        this.$set(this.tableData[val.index], 'provincialIndustrialLeader' , realName.toString())
        this.$set(this.tableData[val.index], 'provincialIndustrialLeaderId' , userId.toString())
      } else if (val.deptParams == '设计统筹人员') {
        this.$set(this.tableData[val.index], 'specialStaffing' , realName.toString())
        this.$set(this.tableData[val.index], 'specialStaffingId' , userId.toString())
      }
    },
    openChooseUserDailog (row, index, deptParams) {
      let item = {}
      if (deptParams == '部门领导(副)') {
        item = {
          excuterNames: row.departmentLeader ? row.departmentLeader : '',
          excuterIds: row.departmentLeaderId ? row.departmentLeaderId : ''
        }
      } else if (deptParams == '地市工建经理') {
        item = {
          excuterNames: row.cityConstructionManager ? row.cityConstructionManager : '',
          excuterIds: row.cityConstructionManagerId ? row.cityConstructionManagerId : ''
        }
      } else if (deptParams == '省公司科室经理') {
        item = {
          excuterNames: row.provincialDepartmentManager ? row.provincialDepartmentManager : '',
          excuterIds: row.provincialDepartmentManagerId ? row.provincialDepartmentManagerId : ''
        }
      } else if (deptParams == '省管项目经理') {
        item = {
          excuterNames: row.provincialProjectManager ? row.provincialProjectManager : '',
          excuterIds: row.provincialProjectManagerId ? row.provincialProjectManagerId : ''
        }
      } else if (deptParams == '省工建领导') {
        item = {
          excuterNames: row.provincialIndustrialLeader ? row.provincialIndustrialLeader : '',
          excuterIds: row.provincialIndustrialLeaderId ? row.provincialIndustrialLeaderId : ''
        }
      } else if (deptParams == '设计统筹人员') {
        item = {
          excuterNames: row.specialStaffing ? row.specialStaffing : '',
          excuterIds: row.specialStaffingId ? row.specialStaffingId : ''
        }
      }
      this.$refs.magnifyingGlass.init(item, deptParams, index)
    },
    getTable() {
      getUserConfigList({}).then((res) => {
        if (res.code == "0000") {
          this.tableData = res.data.data
        }
      });
    },
    addTable() {
      // this.tableData
      if (this.tableData.length == 1) {
       return this.$message.warning(`只能有一条数据，请修改数据`);
      }
      let data = {
        tempDesc: "",
        departmentLeader: "",
        cityConstructionManager: "",
        provincialDepartmentManager: "",
        provincialProjectManager: "",
        provincialIndustrialLeader: "",
        departmentLeaderId: "",
        cityConstructionManagerId: "",
        provincialDepartmentManagerId: "",
        provincialProjectManagerId: "",
        provincialIndustrialLeaderId: "",
        specialStaffing: "",
        specialStaffingId: ""
      };
      insertConfig(data).then((res) => {
        if (res.code == "0000") {
          this.tableData.push({ ...data, id: res.data });
        }
      });
    },
    deleteTable(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delService({ids: row.id}).then(res => {
            if (res.code == '0000') {
              this.$message.success("删除成功");
              this.getTable();
            } else {
              this.$message.error(`${res.msg || '删除失败'}`)
            }
          })
        }).catch(() => {})
    },
    saveONe(row, index) {
      if (row.departmentLeader === '' || row.departmentLeader === null) {
        return this.$message.error('请选择部门领导(副)')
      }
      if (row.cityConstructionManager === '' || row.cityConstructionManager === null) {
        return this.$message.error('请选择地市工建经理')
      }
      if (row.provincialDepartmentManager === '' || row.provincialDepartmentManager === null) {
        return this.$message.error('请选择省公司科室经理')
      }
      if (row.provincialProjectManager === '' || row.provincialProjectManager === null) {
        return this.$message.error('请选择省管项目经理')
      }
      if (row.provincialIndustrialLeader === '' || row.provincialIndustrialLeader === null) {
        return this.$message.error('请选择省工建领导')
      }
      if (row.specialStaffing === '' || row.specialStaffing === null) {
        return this.$message.error('请选择设计统筹人员')
      }
      updateConfig(row).then((res) => {
        if (res.code == "0000") {
          this.$message.success("保存成功");
        }
      });
    },
    // 删除
    del() {
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: delService,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },

    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "departmentLeaderId",
        "cityConstructionManagerId",
        "provincialDepartmentManagerId",
        "provincialProjectManagerId",
        "provincialIndustrialLeaderId"
      ];
      a.forEach((item) => {
        if (index) {
          this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          for (let index in arr) {
            this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
            this.rules[`${item}${index}`] = {
              required: true,
              message: "该字段不能为空",
              trigger: ["blur", "change"],
            };
          }
        }
      });
    },
    // 校验所有的数据
    validateAll() {
      const obj = {}
      for (let i = 0; i < this.tableData.length; i++) {
        const current = this.tableData[i]
        if (obj[`${current.levelNo}_${current.warnType}`]) {
          return false
        }
        obj[`${current.levelNo}_${current.warnType}`] = true
      }
      return true
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


