<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
          <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              :staticSearchParam="loadParam"
              border
            >
            </mssTable>
          </div>
        </mssCard>
        <div>
          <p>规则说明：</p>
          <p>1、统计范围：2023年1月1日至今立项项目；不含机房购置、资源池类项目；预警期：超期前30天内。</p>
          <p>2、超期规则：立项后超30天未完成第一批设计批复（集客、省内干线项目为105天）；超设计期限未完成整体设计批复，各专业设计期限详见附表“指标说明”。</p>
          <p>3、根据项目特点调整，业务支撑网工程不纳入第一批设计批复超期的通报范围。</p>
          <p>4、站点级批复不及时项目：明细表“BY”列低于95%；站点级批复及时率：计算规则详见“指标说明”。</p>
        </div>
    </div>
</template>

<script>

import { commonDown } from '@/utils/btn'
import { listDetail, downloadService } from '@/api/pccw/analysis_link/real_time_reports_alerts/api'

export default {
  name: 'Detail',
  data() {
    return {
        loadParam:{},
        tableApi: listDetail,
        searchConfig:[
          {
              label: 'MIS编号',
              type: 'input',
              fieldName: 'misNumber'
          },
          {
              label: '批复时间',
              type: 'date1',
              fieldName: 'approveTime'
          },
          {
              label: '标准工期专业',
              type: 'input',
              fieldName: 'standardDurationProfessionalClassification'
          },
          {
              label: '项目名称',
              type: 'input',
              fieldName: 'projectName'
          },
          {
              label: '工建中心项目经理',
              type: 'input',
              fieldName: 'engineeringConstructionCenterManager'
          },
        ],
        // 表头
        tableHeader: [
          {
            prop: "",
            label: "项目综合信息表",
            align: "center",
            tooltip: true,
            multilevelColumn: [
              {
                prop: "",
                label: "项目信息",
                align: "center",
                tooltip: true,
                multilevelColumn: [
                  {
                      prop: "constructionUnit",
                      label: "建设单位",
                      align: "center",
                      tooltip: true,
                  },
                  {
                      prop: "misNumber",
                      label: "MIS编号",
                      align: "center",
                      tooltip: true,
                  },
                  {
                      prop: "projectName",
                      label: "项目名称",
                      align: "center",
                      tooltip: true,
                  },
                ]
              },
              {
                  prop: "",
                  label: "立项阶段",
                  align: "center",
                  tooltip: true,
                  multilevelColumn: [
                    {
                        prop: "approveTime",
                        label: "批复时间",
                        align: "center",
                        tooltip: true,
                    },    
                  ]
              },
              {
                  prop: "overdueNumberOfFirstDesignApproval",
                  label: "设计阶段",
                  align: "center",
                  tooltip: true,
                  multilevelColumn: [
                    {
                        prop: "designApproveTime",
                        label: "设计批复时间",
                        align: "center",
                        tooltip: true,
                    },
                    {
                        prop: "firstDesignApproveTime",
                        label: "第一批设计批复时间",
                        align: "center",
                        tooltip: true,
                    },    
                  ]
              },
              {
                  prop: "other",
                  label: "其他",
                  align: "center",
                  tooltip: true,
                  multilevelColumn: [
                    {
                        prop: "constructionUnitProjectManager",
                        label: "建设单位项目经理",
                        align: "center",
                        tooltip: true,
                        minWidth: 100,
                    },
                    {
                        prop: "engineeringConstructionCenterManager",
                        label: "工建中心项目经理",
                        align: "center",
                        tooltip: true,
                        minWidth: 100,
                    },
                  ]
              },
            ]
          },
          {
            prop: "standardDurationProfessionalClassification",
            label: "标准工期专业分类",
            align: "center",
            tooltip: true,
          },
          {
            prop: "totalStageRequiresDuration",
            label: "一阶段设计批复要求时长",
            align: "center",
            tooltip: true,
          },
          {
            prop: "",
            label: "设计批复状态",
            align: "center",
            tooltip: true,
            multilevelColumn: [
              {
                  prop: "",
                  label: "第一批设计批复",
                  align: "center",
                  tooltip: true,
                  multilevelColumn: [
                      {
                          prop: "firstBatchOfApproval",
                          label: "第一批设计批复情况",
                          align: "center",
                          tooltip: true,
                      },
                      {
                          prop: "firstIsExceedTime",
                          label: "是否超期",
                          align: "center",
                          tooltip: true,
                      },
                      {
                          prop: "firstRemainingTime",
                          label: "距离第一批设计批复截止时间（天）",
                          align: "center",
                          tooltip: true,
                      },  
                      {
                          prop: "firstStageRequiresDuration",
                          label: "第一批设计批复时长",
                          align: "center",
                          tooltip: true,
                      },
                      {
                          prop: "firstDeadline",
                          label: "第一批设计截至时间",
                          align: "center",
                          tooltip: true,
                      },  
                  ]
              },
              {
                  prop: "",
                  label: "一阶段设计批复",
                  align: "center",
                  tooltip: true,
                  multilevelColumn: [
                      {
                          prop: "lastBatchOfApproval",
                          label: "一阶段设计批复情况",
                          align: "center",
                          tooltip: true,
                      },
                      {
                          prop: "lastIsExceedTime",
                          label: "是否超期",
                          align: "center",
                          tooltip: true,
                      },
                      {
                          prop: "lastRemainingTime",
                          label: "距离一阶段设计批复截至时间（天）",
                          align: "center",
                          tooltip: true,
                      },  
                      {
                          prop: "lastStageRequiresDuration",
                          label: "一阶段设计批复时长",
                          align: "center",
                          tooltip: true,
                      },
                      {
                          prop: "lastDeadline",
                          label: "一阶段设计截至时间",
                          align: "center",
                          tooltip: true,
                      },  
                  ]
              },
            ]
          },
          {
            prop: "designApprovalTime",
            label: "设计批复及时性",
            align: "center",
            tooltip: true,
            multilevelColumn: [
              {
                  prop: "numberOfProjectSites",
                  label: "立项批复站点数",
                  align: "center",
                  tooltip: true,
              },
              {
                  prop: "numberOfApprovedSites",
                  label: "已批复站点数",
                  align: "center",
                  tooltip: true,
              },
              {
                  prop: "numberOfShouldBeApproved",
                  label: "应完成批复站点数",
                  align: "center",
                  tooltip: true,
              },  
              {
                  prop: "designTimeliness",
                  label: "设计及时率",
                  align: "center",
                  tooltip: true,
              },
            ]
          },
        ],    
    };
  },
  methods: {
      //导出
      exportHandle() {
          commonDown({ ...this.$refs.searchForm.searchForm, limit: -1, exportType: 1}, downloadService);
      },
      //重置
      reset(form) {
          this.search(form)
      },
      //搜索
      search() {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
      },
  },
  mounted() {
      // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your component-specific styles go here */
</style>
