<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
           <div slot="headerBtn">
            <el-button type="primary" @click="exportHandle">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              :serial="false"
              :customSize="20"
              :pagination="false"
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              :staticSearchParam="loadParam"
              border
            >
            </mssTable>
          </div>
        </mssCard>
        <div>
          <p>规则说明：</p>
          <p>1、统计范围：2023年1月1日至今立项项目；不含机房购置、资源池类项目；预警期：超期前30天内。</p>
          <p>2、超期规则：立项后超30天未完成第一批设计批复（集客、省内干线项目为105天）；超设计期限未完成整体设计批复，各专业设计期限详见附表“指标说明”。</p>
          <p>3、根据项目特点调整，业务支撑网工程不纳入第一批设计批复超期的通报范围。</p>
          <p>4、站点级批复不及时项目：明细表“BY”列低于95%；站点级批复及时率：计算规则详见“指标说明”。</p>
        </div>
    </div>
</template>

<script>

// 获取地市:queryAreaListService
import {queryAreaListService} from '@/api/common_api.js'
import { commonDown } from '@/utils/btn'
import {getListGroupByManManager, downloadService} from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
export default {
  name: 'engineeringManagement',
  data() {
    return {
      loadParam:{},
      tableApi: getListGroupByManManager,
      searchConfig:[
        {
            label: '工程管理经理',
            type: 'input',
            fieldName: 'engineeringConstructionCenterManager'
        },
        // {
        //   label: '建设单位',
        //   type: 'input',
        //   fieldName: 'constructionUnit'
        // },
      ],
      // 表头
      tableHeader: [
        {
          prop: "engineeringConstructionCenterManager",
          label: "工程管理经理",
          align: "center",
          tooltip: true,
          minWidth: 90
        },
        // {
        //   prop: "constructionUnit",
        //   label: "建设单位",
        //   align: "center",
        //   tooltip: true,
        //   minWidth: 90
        // },
        {
          prop: "total",
          label: "项目数",
          align: "center",
          tooltip: true,
        },
        {
        prop: "",
        label: "第一批设计批复",
        multilevelColumn: [
          {
            prop: "firstDurationCount",
            label: "预警数（30天以内）",
            minWidth: 60,
          },
          {
            prop: "firstOverDueCount",
            label: "超期项目数",
            minWidth: 100,
          },
          {
            prop: "firstOnTimeRate",
            label: "及时率",
          },
          {
            prop: "firstAverageDuration",
            label: "平均时长(天)",
          },
        ]
        },
        {
          prop: "",
          label: "整体设计批复",
          multilevelColumn: [
            {
              prop: "lastDurationCount",
              label: "预警数（30天以内）",
              minWidth: 60,
            },
            {
              prop: "lastOverdueCount",
              label: "超期项目数",
              minWidth: 100,
            },
            {
              prop: "lastOnTimeRate",
              label: "及时率",
            },
            {
              prop: "lastAverageDuration",
              label: "平均时长(天)",
            },
          ]
        },
        {
          prop: "5",
          label: "站点级设计批复",
          multilevelColumn: [
            {
              prop: "siteLevelOverdueCount",
              label: "超期项目数",
              minWidth: 100,
            },
            {
              prop: "siteLevelOnTimeRate",
              label: "及时率",
            },
            {
              prop: "siteLevelAverageDuration",
              label: "平均时长(天)",
            },
          ]
        },
      ],
    };
  },
  methods: {
      /**
       * 地市
       * @param parentId
       * @param index
       */
      getAreaList(parentId, index) {
        console.log('parentId', parentId)
        console.log('index', index)
        queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
          if (res.code === '0000') {
            const list = []
            res.data.forEach(item => {
              list.push({ label: item.name, value: item.id })
            })
            this.$set(this.searchConfig[index], 'options', list)
          }
        })
      },
      //导出
      exportHandle() {
          commonDown({ ...this.$refs.searchForm.searchForm, limit: -1, exportType: 3}, downloadService);
      },
      //重置
      reset(form) {
          this.search(form)
      },
      //搜索
      search() {
          this.$refs.table.page.current = 1
          this.loadParam = JSON.parse(
            JSON.stringify(this.$refs.searchForm.searchForm)
          )
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
      },
  },
  created() {
    this.getAreaList('-2', 0)
  },
  mounted() {
      // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
