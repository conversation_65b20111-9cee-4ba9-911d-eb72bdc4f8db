<template>
  <div>
    <mssCard :title="title">
      <div slot="headerBtn">
        <el-button type="primary" v-if="whether ==='已提交'" disabled>已提交</el-button>
        <el-button type="primary" @click="getSubmit" v-if="whether ==='未提交'">提交</el-button>
      </div>
      <div slot="content">
          <mssTable
            :serial="false"
            :pagination="false"
            ref="table"
            :selection='false'
            :stationary="tableData"
            :columns="tableHeader"
            :staticSearchParam="loadParam"
          >
          </mssTable>
      </div>
    </mssCard>
  </div>
</template>
<script>
import { getWeekReportDetail, sendWeekReport, personnelCheck } from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
export default {
  data () {
    return {
      dialogVisible: false,
      tableHeader: [
        {
          prop: "constructionUnit",
          label: "地市",
          minWidth: 150,
        },
        {
          prop: "total",
          label: "项目数",
          minWidth: 150,
        },
        {
          prop: "",
          label: "第一批设计批复",
          multilevelColumn: [
            {
              prop: "firstDurationCount",
              label: "预警数（30天以内）",
              minWidth: 60,
            },
            {
              prop: "firstOnTimeRate",
              label: "及时率",
            },
          ]
        },
        {
          prop: "",
          label: "整体设计批复",
          multilevelColumn: [
            {
              prop: "lastDurationCount",
              label: "预警数（30天以内）",
              minWidth: 60,
            },
            {
              prop: "lastOnTimeRate",
              label: "及时率",
            },
          ]
        },
        {
          prop: "",
          label: "站点级设计批复",
          multilevelColumn: [
            {
              prop: "siteLevelOverdueCount",
              label: "不及时率项目数",
              minWidth: 60,
            },
            {
              prop: "siteLevelOnTimeRate",
              label: "批复及时率",
            },
          ]
        },
      ],
      loadParam: {},
      tableData: [],
      title: '',
      whether: ''
    }
  },
  created () {
    let flow = decodeURIComponent(this.$route.query.flow)
    console.log(this.$route.query.curWorkOrder)
    this.flow = JSON.parse(flow); // 获取流程对象
    console.log(this.flow)
    let matches =this.flow.title.match(/\(([^)]+)\)/)
    this.loadParam = {
      totalType: '1',
      calulateTime: matches ? matches[1]: '',
    }
    this.weeklyCityLsit()
    this.getPersonnelCheck()
  },
  methods: {
    getPersonnelCheck () {
      personnelCheck(this.loadParam).then((res)=>{
        this.whether = res.data
      })
    },
    weeklyCityLsit(){
      getWeekReportDetail(this.loadParam).then((res)=>{
        this.tableData = res.data.list
        this.title = res.data.title
      })
    },
    getSubmit () {
      let params = {
        calulateTime: this.loadParam.calulateTime,
        totalType: 1
      }
      sendWeekReport(params).then((res)=>{
        if (res.code === '0000') {
          this.$message.success('提交成功')
          this.$router.push({
            path: `/home`,
          })
        }
        console.log(res)
      })
    }
  }
} 
</script>
