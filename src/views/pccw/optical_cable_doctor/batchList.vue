<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="分析结果查询">
      <div slot="headerBtn">
        <el-button type="primary" @click="getDownload">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :staticSearchParam="loadParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>
<script>
  import { getList, onExport } from "@/api/pccw/optical_cable_doctor/api"
  import { commonDown } from "@/utils/btn"
  export default {
    name: "batchList",
    data() {
      return {
        loadParam: {
          // analysisTaskFlag:"1"
        },
        tableApi: getList,
        //搜索字段配置
        searchConfig: [
          {
            label: "工单编号",
            type: "input",
            fieldName: "orderNumber"
          },
          {
            label: "建设单位",
            type: "input",
            fieldName: "constructUnit"
          },
          {
            label: "所属区县",
            type: "input",
            fieldName: "district"
          },
          {
            label: "项目编码",
            type: "input",
            fieldName: "projectCode"
          },
          {
            label: "项目名称",
            type: "input",
            fieldName: "projectName"
          },
          {
            label: "任务编码",
            type: "input",
            fieldName: "taskCode"
          },
          {
            label: "任务名称",
            type: "input",
            fieldName: "taskName"
          },
          {
            label: "施工单位",
            type: "input",
            fieldName: "constructionUnit"
          },
          {
            type: "cycleDate",
            dateType: "month",
            label: "光缆医生工单创建时间",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            fieldName: "opticalCableDoctorCreateTime",
            clearable: false
          },
          {
            label: "分析任务是否完成",
            type: "select",
            fieldName: "analysisTaskFlag",
            options: [
              {
                label: "未完成",
                value: 0
              },
              {
                label: "完成",
                value: 1
              }
            ]
          },
          {
            label: "工单是否取消",
            type: "select",
            fieldName: "cancelOrderFlag",
            options: [
              {
                label: "否",
                value: 0
              },
              {
                label: "是",
                value: 1
              }
            ]
          },
          {
            label: "测试是否合格",
            type: "select",
            fieldName: "qualified",
            options: [
              {
                label: "合格",
                value: 1
              },
              {
                label: "不合格",
                value: 0
              }
            ]
          }
        ],
        //表格头部配置
        tableHeader: [
          {
            prop: "orderNumber",
            label: "工单编号",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "orderSystemName",
            label: "工单系统名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "orderTopic",
            label: "工单主题",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "constructUnit",
            label: "建设单位",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "district",
            label: "所属区县",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "taskCode",
            label: "任务编码",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "auxiliarySystemNameA",
            label: "辅助系统A端站点名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "auxiliarySystemNameZ",
            label: "辅助系统Z端站点名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "constructionUnit",
            label: "施工单位",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "year",
            label: "所属年份",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "constructionCommencementReportCompletionTime",
            label: "开工完成（填报）时间",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "stationAName",
            label: "光缆医生A端站点名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "stationBName",
            label: "光缆医生Z端站点名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "opticalCableLevel",
            label: "光缆级别",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "estimatedOpticCableLength",
            label: "预估光缆长度",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "testlength",
            label: "测试的光缆长度",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "fiberCount",
            label: "光缆芯数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "opticalCableDoctorCreateTime",
            label: "光缆医生工单创建时间",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "analysisTaskFlag",
            label: "分析任务是否完成",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return row.analysisTaskFlag == 1
                ? "完成"
                : row.analysisTaskFlag == "1"
                ? "完成"
                : "未完成"
            }
          },
          {
            prop: "cancelOrderFlag",
            label: "工单是否取消",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return row.cancelOrderFlag == 1
                ? "是"
                : row.cancelOrderFlag == "1"
                ? "是"
                : "否"
            }
          },
          {
            prop: "longitude",
            label: "测试地点经度",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "latitude",
            label: "测试地点纬度",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "testFiberCount",
            label: "测试纤芯数",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return row.analysisTaskFlag === "1" ? cellValue : ""
            }
          },
          // {
          //   prop: "testScoreFlag",
          //   label: "测试数据评分是否合格",
          //   align: "center",
          //   tooltip: true,
          //   minWidth: 150,
          //   formatter: (row, column, cellValue, index) => {
          //     return cellValue == "0"
          //       ? "不合格"
          //       : cellValue == "1"
          //       ? "合格"
          //       : ""
          //   }
          // },
          {
            prop: "qualified",
            label: "测试质量是否合格",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return cellValue === "0"
                ? "不合格"
                : cellValue === "1"
                ? "合格"
                : ""
            }
          },
          {
            prop: "qualifiedFiberCount",
            label: "达标纤芯数",
            align: "center",
            tooltip: true,
            minWidth: 150
            // formatter: (row, column, cellValue, index) => {
            //   return (
            //   row.analysisTaskFlag === 0 ? cellValue :
            //                 ''
            //   );
            // }
          },
          {
            prop: "disQualifiedFiberCount",
            label: "不达标纤芯数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "disqualifiedFiber",
            label: "不达标纤芯号",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "averageLoss",
            label: "平均衰耗",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "lastUploadTime",
            label: "测试上传时间",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "missionReportUrl",
            label: "报告URL地址",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              const flag = row.missionReportUrl;
              return (
                <span>
                  {flag ? (

                <span class="table_operate">
                  <span class="mr10"></span>{" "}
                  <span
                    class="table_btn mr10"
                    onClick={() => {
                      this.gotoUrl(cellValue)
                    }}
                  >
                    点击跳转
                  </span>
                </span>

                  ) : (
                    <span>无</span>
                  )}
                </span>
              );
            }


          },
          {
            prop: "stationNameFlag",
            label: "任务点一致性验证",
            align: "center",
            tooltip: true,
            minWidth: 150
          }
        ]
      }
    },
    methods: {
      gotoUrl(url) {
        window.open(url, "_blank") // 在新标签页中打开 URL
      },
      getDownload() {
        commonDown({ ...this.$refs.searchForm.searchForm }, onExport)
      },
      //删除
      getDelete() {
        if (this.$refs.table.multipleSelection.length > 0) {
          console.log(this.$refs.table.multipleSelection)
          let ids = []
          this.$refs.table.multipleSelection.forEach(item => {
            ids.push(item.id)
          })
          dispatchDelete({ ids: ids.toString() }).then(res => {
            if (res.code == "0000") {
              this.$message.success("删除成功")
              this.$refs.table.$refs.table.clearSelection()
              this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
            } else {
              this.$message.error(`${res.msg || "删除失败"}`)
            }
          })
        } else {
          this.$message({
            showClose: true,
            message: "请至少勾选一条数据",
            type: "warning"
          })
        }
      },
      //新增
      getAddition() {
        console.log(this.$refs, "==")
        this.$refs["exemption"].open("合作单位监督发现问题")
      },
      //子调父方法
      onClose() {
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      //重置
      reset(form) {
        this.search(form)
      },
      //搜索
      search() {
        this.$refs.table.page.current = 1
        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      }
    },
    mounted() {
      // this.$set(this.searchConfig[6], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
    }
  }
</script>

<style scoped>
  /* Your component's CSS styles go here */
</style>
