<template>
  <div>
    <CreateForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    >
      <el-button slot="afterBtn" type="primary" @click="getWorkOrder(2)"
        >取消工单</el-button
      >
    </CreateForm>
    <mssCard title="创建工单页面">
      <div slot="headerBtn">
        <el-upload
          style="display: inline-block; margin-left: 10px"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button
          type="primary"
          @click="exportHandleTemp"
          style="margin-left: 10px"
          >下载导入模板</el-button
        >
        <el-button type="primary" @click="getDownload">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          selection
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :staticSearchParam="loadParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>
<script>
  import {
    getList,
    deleteOrder,
    createOrder,
    onExport,
    createExport,
    downloadCableDoctorCreateTemplatep,
    importCableDoctorCreate
  } from "@/api/pccw/optical_cable_doctor/api"
  import { commonDown } from "@/utils/btn"
  import CreateForm from "./createForm"
  import {
    downloadInspectionSetTemp,
    importQualityLnspection
  } from "@/api/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare"
  export default {
    name: "createWorkOrder",
    components: {
      CreateForm
    },
    data() {
      return {
        loadParam: {},
        tableApi: getList,
        //搜索字段配置
        searchConfig: [
          {
            label: "工单编号",
            type: "input",
            fieldName: "orderNumber"
          },
          {
            label: "建设单位",
            type: "input",
            fieldName: "constructUnit"
          },
          {
            label: "所属区县",
            type: "input",
            fieldName: "district"
          },
          {
            label: "项目编码",
            type: "input",
            fieldName: "projectCode"
          },
          {
            label: "项目名称",
            type: "input",
            fieldName: "projectName"
          },
          {
            label: "任务编码",
            type: "input",
            fieldName: "taskCode"
          },
          {
            label: "任务名称",
            type: "input",
            fieldName: "taskName"
          }
        ],
        //表格头部配置
        tableHeader: [
          {
            prop: "orderNumber",
            label: "工单编号",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "orderSystemName",
            label: "工单系统名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "orderTopic",
            label: "工单主题",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "constructUnit",
            label: "建设单位",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "district",
            label: "所属区县",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "taskCode",
            label: "任务编码",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "auxiliarySystemNameA",
            label: "辅助系统A端站点名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "auxiliarySystemNameZ",
            label: "辅助系统Z端站点名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "estimatedOpticCableLength",
            label: "预估光缆长度",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "constructionUnit",
            label: "施工单位",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "year",
            label: "所属年份",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "constructionCommencementReportCompletionTime",
            label: "开工完成（填报）时间",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "pushTimes",
            label: "推送状态",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return cellValue >= 1 ? "已推送" : "未推送"
            }
          },
          {
            prop: "analysisTaskFlag",
            label: "任务是否已完成上传",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return cellValue === 1 || cellValue === "1" ? "已完成" : "未完成"
              // 如果确定cellValue是数字，则应该写成：
              // return cellValue === 1 ? '是' : '否';
            }
          },
          // {
          //     prop: "analysisTaskFlag",
          //     label: "分析任务是否完成",
          //     align: "center",
          //     tooltip: true,
          //     minWidth: 150,
          //     formatter: (row, column, cellValue, index) => {
          //       return (
          //         row.analysisTaskFlag == 0 ? '合格': row.analysisTaskFlag == '0' ? '合格' :'不合格'
          //       );
          //     }
          // },
          {
            prop: "cancelOrderFlag",
            label: "该工单是否取消",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return row.cancelOrderFlag == 1
                ? "是"
                : row.cancelOrderFlag == "1"
                ? "是"
                : "否"
            }
          }
        ]
      }
    },
    methods: {
      getWorkOrder(val) {
        console.log(this.$refs.table.multipleSelection)
        if (this.$refs.table.multipleSelection.length > 0) {
          if (val === 2) {
            this.$confirm("是否确认批量取消工单?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                let ids = []
                this.$refs.table.multipleSelection.forEach(res => {
                  ids.push(res.id)
                })
                deleteOrder({ ids: ids }).then(res => {
                  if (res.code == "0000") {
                    this.$message.success(res.data)
                    this.$refs.table.getTableData(
                      this.$refs.searchForm.searchForm
                    )
                  } else {
                    this.$message.error(`${res.msg || "取消工单失败"}`)
                  }
                })
              })
              .catch(() => {})
          }
        } else {
          this.$message({
            message: "至少勾选一条数据",
            type: "warning"
          })
        }
      },
      getDownload() {
        commonDown({ ...this.$refs.searchForm.searchForm }, createExport)
      },
      importFile(params) {
        const param = new FormData()
        param.append("files", params.file)
        importCableDoctorCreate(param).then(res => {
          if (res.code === "5000") {
            this.$message.warning(res.data.msg)
          } else if (res.code === "0000"&&res.data) {
            // if (res.data) {
            //   this.dialogTableVisible = true
            //   this.gridData = res.data
            // } else {

            // }
            // 使用 indexOf 方法判断是否存在逗号
            let commaExists = res.data.indexOf(",") !== -1;
            if(commaExists){
              // let inputString = res.data;
              // // 使用逗号分割字符串
              // let parts = inputString.split(",", 1);
              // let firstPart = parts[0];
              // let secondPart = parts.slice(1).join(",");
              // let [firstPartSimple, secondPartSimple] = inputString.split(",", 2);
              // console.log("First Part:", firstPartSimple); // 输出: Hello
              // console.log("Second Part:", secondPartSimple); // 输出: World
              // this.$message.success(firstPartSimple)

              this.$message({
                          type: "error",
                          message: res.data,
                          showClose: true,
                          duration:0,
                        });

              // this.$message.error({
              //   message: res.data,
              //   // duration: 0,  // 或者设置一个非常大的数，但推荐使用 manualClose
              //   manualClose: true,  // 明确设置为手动关闭
              //   closable: true  // 确保用户可以关闭这个提示（如果需要的话）
              // });
            }else{
              this.$message.success(res.data)
            }
            this.search()
          }
        })
      },
      exportHandleTemp() {
        commonDown(
          { ...this.$refs.searchForm.searchForm },
          downloadCableDoctorCreateTemplatep
        )
      },
      //删除
      getDelete() {
        if (this.$refs.table.multipleSelection.length > 0) {
          console.log(this.$refs.table.multipleSelection)
          let ids = []
          this.$refs.table.multipleSelection.forEach(item => {
            ids.push(item.id)
          })
          dispatchDelete({ ids: ids.toString() }).then(res => {
            if (res.code == "0000") {
              this.$message.success("删除成功")
              this.$refs.table.$refs.table.clearSelection()
              this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
            } else {
              this.$message.error(`${res.msg || "删除失败"}`)
            }
          })
        } else {
          this.$message({
            showClose: true,
            message: "请至少勾选一条数据",
            type: "warning"
          })
        }
      },
      //重置
      reset(form) {
        this.search(form)
      },
      //搜索
      search() {
        this.$refs.table.page.current = 1
        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      }
    },
    mounted() {
      // this.$set(this.searchConfig[6], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
    }
  }
</script>

<style scoped>
  /* Your component's CSS styles go here */
</style>
