<template>
  <div>
    <!-- :form 设置默认值 -->
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
      @changeSelect="changeSelect"
      :form="loadParam"
    ></mssSearchForm>
    <mssCard title="光缆测试情况统计表">
      <div slot="headerBtn">
        <el-button type="primary" @click="getDownload">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          selection
          :staticSearchParam="loadParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>
<script>
  import {
    getReportList,
    reportExport
  } from "@/api/pccw/optical_cable_doctor/api"
  import { commonDown } from "@/utils/btn"
  export default {
    name: "opticalCableDetailsList",
    data() {
      return {
        loadParam: {
          startTime: "",
          endTime: "",
          dimension: "1"
        },
        tableApi: getReportList,
        //搜索字段配置
        searchConfig: [
          {
            label: "统计维度",
            type: "select",
            fieldName: "dimension",
            options: [
              { label: "建设单位维度", value: "1" },
              { label: "施工单位维度", value: "2" },
              { label: "项目、建设单位维度", value: "3" }
            ]
          },
          {
            type: "cycleDate",
            dateType: "month",
            label: "月份（起)",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            fieldName: "startTime",
            clearable: false
          },
          {
            type: "cycleDate",
            dateType: "month",
            label: "月份（止)",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            fieldName: "endTime",
            clearable: false
          }
        ],
        //表格头部配置
        tableHeader: [],
        // 默认头部
        tableHeaderCommon: [
          // {
          //     prop: "",
          //     label: "光缆测试情况统计表",
          //     align: "center",
          //     multilevelColumn: [
          {
            prop: "startedTaskCount",
            label: "已开工任务数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "testedTaskCount",
            label: "已测试任务数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "testUploadRate",
            label: "测试上传率",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "qualifiedTaskCount",
            label: "测试合格任务数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "testQualificationRate",
            label: "测试合格率",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "oneTimeQualifiedTaskCount",
            label: "一次性测试合格任务数",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "oneTimeTestQualificationRate",
            label: "一次性测试合格率",
            align: "center",
            tooltip: true,
            minWidth: 150
          }
          // ]
          // },
        ]
      }
    },
    created() {
      this.$set(this.searchConfig[0], "value", "1")
       this.generateMonthRanges()
    },
    mounted() {
      // 添加默认条件
      this.addHeaderDefault()

      // this.reset()
      // this.initData()
      // 初始化统计维度
      // this.$refs.searchForm.searchForm.dimension = '1'
    },
    methods: {
      // 根据当前日期生成当前月和上个月的月份数据
      generateMonthRanges() {
        const now = new Date()
        const currentYear = now.getFullYear()
        const currentMonth = now.getMonth() + 1 // JavaScript中月份是从0开始的，所以需要加1

        // 上个月的年份和月份
        let previousMonth = currentMonth - 1
        let previousYear = currentYear
        if (previousMonth === 0) {
          previousMonth = 12
          previousYear -= 1
        }

        // 赋值给loadParam对象
        this.loadParam.startTime = `${previousYear}-${String(
          previousMonth
        ).padStart(2, "0")}`
        this.loadParam.endTime = `${currentYear}-${String(
          currentMonth
        ).padStart(2, "0")}`
        console.log("日期",this.loadParam)
      },
      addHeaderDefault() {
        this.tableHeader = JSON.parse(JSON.stringify(this.tableHeaderCommon))
        const newTableHeader = {
          prop: "constructUnit",
          label: "建设单位",
          align: "center",
          tooltip: true
        }
        this.tableHeader.unshift(newTableHeader) // 开头插入
      },
      changeSelect(name, val) {
        console.log("切换", name, val)
        // 选择维度列
        if (name === "dimension") {
          // this.staticSearchParam.dimension = val.value //赋值下拉框值给搜索条件
          if (val === "1") {
            // 1、
            // 更换表格列表
            this.addHeaderDefault()
          } else if (val === "2") {
            // 2、
            this.tableHeader = JSON.parse(
              JSON.stringify(this.tableHeaderCommon)
            )
            const newTableHeader = {
              prop: "constructionUnit",
              label: "施工单位",
              align: "center",
              tooltip: true
            }
            this.tableHeader.unshift(newTableHeader) // 开头插入
          } else if (val === "3") {
            // 3、
            this.tableHeader = JSON.parse(
              JSON.stringify(this.tableHeaderCommon)
            )
            const newTableHeaders = [
              {
                prop: "constructUnit",
                label: "建设单位",
                align: "center",
                tooltip: true
              },
              {
                prop: "projectName",
                label: "项目名称",
                align: "center",
                tooltip: true
              }
            ]
            // 一次性添加多个新对象到数组开头
            this.tableHeader.unshift(...newTableHeaders)
          } else {
            // 没有
            this.addHeaderDefault()
          }
        }
      },
      getDownload() {
        commonDown({ ...this.$refs.searchForm.searchForm }, reportExport)
      },
      //删除
      getDelete() {
        if (this.$refs.table.multipleSelection.length > 0) {
          console.log(this.$refs.table.multipleSelection)
          let ids = []
          this.$refs.table.multipleSelection.forEach(item => {
            ids.push(item.id)
          })
          dispatchDelete({ ids: ids.toString() }).then(res => {
            if (res.code == "0000") {
              this.$message.success("删除成功")
              this.$refs.table.$refs.table.clearSelection()
              this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
            } else {
              this.$message.error(`${res.msg || "删除失败"}`)
            }
          })
        } else {
          this.$message({
            showClose: true,
            message: "请至少勾选一条数据",
            type: "warning"
          })
        }
      },

      //新增
      getAddition() {
        console.log(this.$refs, "==")
        this.$refs["exemption"].open("合作单位监督发现问题")
      },
      //子调父方法
      onClose() {
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      //重置
      reset() {
        this.initData()
        this.search()
      },
      initData() {
        this.$refs.searchForm.searchForm.dimension = "1"
        // this.$refs.searchForm.searchForm.startTime = "2024-09"
        // this.$refs.searchForm.searchForm.endTime = "2024-10"

        // loadParam: {
        //   startTime: "2024-09",
        //   endTime: "2024-10",
        //   dimension:"1",
        // },
      },
      //搜索
      search() {
        this.$refs.table.page.current = 1
        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      }
    }
  }
</script>

<style scoped>
  /* Your component's CSS styles go here */
</style>
