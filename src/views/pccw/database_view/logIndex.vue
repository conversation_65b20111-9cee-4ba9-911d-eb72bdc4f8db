/**
* @author: ye<PERSON><PERSON>
* @date: 2024-07-05
* @description: 日志下载
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="日志文件列表">
      <div slot="headerBtn">

      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
          border
          :pagination ="false"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {selectLogFileList, uploadLogFile} from "@/api/pccw/databaseView/databaseView.js";
import {
  editCountyTimeoutConfig,
  selectCountyFeedbackConfigList
} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";


export default {
  name: 'countyTimeoutConfig',
  components: {},
  data() {
    return {
      loadParam: {},
      staticSearchParam:{},
      //搜索字段配置
      searchConfig: [
        {
          type: 'input',
          fieldName: 'filePathFiltration',
          label: '文件路径过滤'
        },
      ],
      tableApi: selectLogFileList,
      //表格头部配置
      tableHeader: [
        {
          label: '文件名称',
          prop: 'fileName',
          tooltip: true,
          minWidth: 180,
        },
        {
          label: '文件路径',
          prop: 'filePath',
          tooltip: true,
          minWidth: 800,
        },
        {
          label: '文件大小',
          prop: 'fileSize',
          tooltip: true,
          minWidth: 140,
        },
        {
          label: '最后一次同步 MiniIO 时间',
          prop: 'lastUpdateTime',
          tooltip: true,
          minWidth: 140,
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '60px',
          formatter: (row) => {
            return (
              <span>
               {(row.syncStatus !== '上传中' || !row.syncStatus) ? (
                 <el-button type='text' onClick={() => this.actionHandle(row)}>上传</el-button>
               ) : ('上传中')}
          </span>)
          }
        }
      ],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    reset() {
      this.search();
    },
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    actionHandle(row){
      uploadLogFile(row).then(res => {
        if (res.code === '0000') {
          this.$message.success("开始上传");
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        }
      })
    }
  }
}
</script>
