/**
* @author: ye<PERSON><PERSON>
* @date: 2024-05-08
* @description: 数据库可视化界面
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="库表信息">
      <div slot="headerBtn">

      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          selection
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {dbList} from "@/api/pccw/databaseView/databaseView.js";


export default {
  name: 'countyTimeoutConfig',
  components: {},
  data() {
    return {
      //搜索字段配置
      searchConfig: [
        {
          type: 'input',
          fieldName: 'tableName',
          label: '表名称'
        },
        {
          type: 'input',
          fieldName: 'tableComment',
          label: '表描述'
        },
      ],
      tableApi: dbList,
      //表格头部配置
      tableHeader: [
        {
          label: '表名称',
          prop: 'tableName',
          tooltip: true,
          minWidth: 60,
        },
        {
          label: '表描述',
          prop: 'tableComment',
          tooltip: true,
          minWidth: 80,
        },
      ],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    search() {
    },
    reset() {
    }
  }
}
</script>
