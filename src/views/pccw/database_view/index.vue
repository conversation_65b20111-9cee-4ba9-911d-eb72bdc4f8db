/**
* @author: yewenbin
* @date: 2024-05-08
* @description: 数据库可视化界面
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="库表信息">
      <div slot="headerBtn">

      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
          :customSize="20"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <el-dialog
      ref="tableColumnDailog"
      :visible.sync="showDialog"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="80%"
      :modal="true"
      append-to-body
      @close="close"
    >
      <mssSearchForm
        ref="dataSearchForm"
        :form="dataSearchForm"
        :search-config="searchDataConfig"
        @reset="resetData"
        @search="searchData"
      ></mssSearchForm>
      <mssCard title="数据列表">
        <div slot="content">
          <mssTable
            ref="tableColumnTable"
            :serial="false"
            :customSize="10"
            :api="selectDataListApi"
            :autoCall="false"
            :columns="columns"
            :staticSearchParam="staticSearchTableColumnParam"
            :pagination="true"
            border
          >
          </mssTable>
        </div>
      </mssCard>
    </el-dialog>
  </div>

</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {dbList, columnList, selectDataList} from "@/api/pccw/databaseView/databaseView.js";
import moment from "moment/moment";
import {getCityList} from "@/api/pccw/report_select/quarter_transfer_task";


export default {
  name: 'countyTimeoutConfig',
  components: {},
  data() {
    return {
      //搜索字段配置
      searchConfig: [
        {
          type: 'input',
          fieldName: 'tableName',
          label: '表名称',

        },
        // {
        //   type: 'input',
        //   fieldName: 'tableComment',
        //   label: '表描述'
        // },
      ],
      searchDataConfig: [
        {
          type: 'input',
          fieldName: 'tableName',
          label: '表名称',
          disabled: 'false'
        },
        {
          type: 'input',
          fieldName: 'whereConditions',
          label: 'WHERE条件',
        },
      ],
      tableApi: dbList,
      selectColumnsListApi: columnList,
      selectDataListApi: selectDataList,
      //表格头部配置
      tableHeader: [
        {
          label: '表名称',
          prop: 'tableName',
          tooltip: true
        },
        {
          label: '表描述',
          prop: 'tableComment',
          tooltip: true
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '20px',
          formatter: (row) => {
            return (
              <span>
                {row.tableName ? (
                  <span class='table_btn mr10' onClick={() => {
                    this.openColumnListDailog(row)
                  }}>
                  查看
                </span>) : ('')}
              </span>
            )
          }
        }
      ],
      dataSearchForm: {
        tableName: '',
      },
      loadParam: {
        tableName: '',
      },
      loadDataParam: {
        tableName: '',
      },
      //静态搜索参数
      staticSearchParam: {},
      staticSearchTableColumnParam: {
        tableName: '',
      },
      showDialog: false,
      title: '',
      columns: [],
      tableName: '',
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    reset(form) {
      this.search(form)
    },

    //打开项目选择弹窗
    openColumnListDailog(row) {
      console.log(row)
      this.showDialog = true
      let tableName = row.tableName
      this.tableName = tableName
      this.title = tableName + ' ' + row.tableComment
      this.loadDataParam.tableName = this.tableName
      this.dataSearchForm.tableName = this.tableName
      this.staticSearchTableColumnParam.tableName = this.tableName
      this.tableColumns(tableName)
    },
    tableColumns(tableName) {
      columnList(tableName).then(res => {
        this.columns = []
        if (res.code === '0000') {
          res.data.forEach(item => {
            var size = item.columnName.length;
            let number1 = Number(size * 7);
            if (number1 < 100) {
              number1 = 100
            }
            this.columns.push(
              {
                label: item.columnName + '/' + item.columnComment,
                prop: item.columnName,
                minWidth: number1 + 'px',
                tooltip: true
              }
            )
          })
          this.selectDataList()
        }
      })
    },
    selectDataList() {
      this.$refs.tableColumnTable.page.current = 1
      this.loadDataParam = JSON.parse(
        JSON.stringify(this.$refs.dataSearchForm.searchForm)
      )
      this.loadDataParam.tableName = this.tableName
      this.dataSearchForm.tableName = this.tableName
      this.$refs.tableColumnTable.getTableData(this.loadDataParam);
    },
    close() {
      this.$refs.dataSearchForm.searchForm.whereConditions = ''
      this.showDialog = false
    },
    searchData() {
      this.staticSearchTableColumnParam = JSON.parse(
        JSON.stringify(this.$refs.dataSearchForm.searchForm)
      );
      this.selectDataList(this.$refs.dataSearchForm.searchForm)
    },
    resetData(form) {
      this.$refs.dataSearchForm.searchForm.tableName = this.tableName
      this.$refs.dataSearchForm.searchForm.whereConditions = ''
      this.searchData()
    },
  }
}
</script>
