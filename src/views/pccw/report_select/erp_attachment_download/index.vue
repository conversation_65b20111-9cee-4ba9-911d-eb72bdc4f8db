<template>
  <div class="app-container">
    <mssSearchForm title="ERP系统附件下载参数配置" v-show="showSearch" ref="searchForm" :form="queryCondition" :search-config="queryParams"
      @reset="resetQuery" @search="handleQuery"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam"
         border selection>
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import {
    getERPList,updateERP
  } from "@/api/pccw/report_select/audit_statement_extraction.js";

  export default {
    components: {

    },
    data() {
      return {
        // 默认表格搜索条件
        staticSearchParam: {},
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [
          {
            label: '公司段',
            type: 'input',
            fieldName: 'companyName'
          },
          {
            label: '项目经理岗',
            type: 'input',
            fieldName: 'projectManagerPosition'
          },
        ],
        // 查询条件
        queryCondition: {

        },
        procInstId: '',
        // 表头
        tableHeader: [
          {
            prop: "companyName",
            label: "公司段",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectManagerPosition",
            label: "项目经理岗",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectNumber",
            label: "项目编号",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "isEnabled",
            label: "是否启用",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '100px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.updateRow(row)
                    }
                  } >
                  修改 <
                  /span>
                } <
                /span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: getERPList,
      };
    },
    created() {

    },
    methods: {
      updateFinace(row) {
// 根据id查询
// 弹框
// 发送修改请求
// let res=await updateFinace(row)
      },
      async downloadData() {
        console.log("t",this.staticSearchParam);
        let res =await exportDetail(qs.stringify(this.staticSearchParam))
        console.log("res",res);
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', '未完成接收费用订单列表.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
      /** 搜索按钮操作 */
      handleQuery(form) {
        // console.log("form", form);
        // this.queryOwn = {
        //   ...form
        // }
        this.$refs.table.page.current = 1
        this.queryOwn = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      },
    }
  };
</script>


<style lang="scss" scoped>

</style>
