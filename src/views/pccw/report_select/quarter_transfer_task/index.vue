/**
* @author: yewen<PERSON>
* @date: 2023-01-13
* @description: 季度转资任务完成报表-列表查询
*/
<template>
  <div>
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <mssSearchForm
        ref="searchForm"
        :form="searchForm"
        :searchConfig="searchConfig"
        @search="search"
        @reset="reset"
        @changeSelect="changeSelect"
      ></mssSearchForm>
      <mssCard title="查询结果">
        <div slot="headerBtn">
          <el-button @click="exportList(activeName)">导出</el-button>
        </div>
        <div slot="content">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane label="转资任务完成情况" name="mssMonthSummaryPanel" lazy>
              <div v-show="loadParam.granularity == 'month'">
                <mssTable
                  ref="monthTable"
                  :serial="false"
                  :customSize="20"
                  :api="summaryListApi"
                  :columns="monthColumns"
                  :staticSearchParam="staticSearchParam"
                  :pagination="false"
                  border
                >
                </mssTable>
              </div>
              <div v-show="loadParam.granularity =='quarter' ">
                <mssTable
                  ref="quarterTable"
                  :serial="false"
                  :customSize="20"
                  :api="summaryListApi"
                  :columns="quarterColumns"
                  :staticSearchParam="staticSearchParam"
                  :pagination="false"
                  :autoCall="false"
                  border
                >
                </mssTable>
              </div>
              <div v-show="loadParam.granularity =='year' ">
                <mssTable
                  ref="yearTable"
                  :serial="false"
                  :customSize="20"
                  :api="summaryListApi"
                  :columns="yearColumns"
                  :staticSearchParam="staticSearchParam"
                  :pagination="false"
                  :autoCall="false"
                  border
                >
                </mssTable>
              </div>
              <div v-show="isShowDesc ">
                <div style="margin-top: 20px">
                  <span v-html="description"> {{ description }} </span>
                </div>

              </div>


            </el-tab-pane>
            <el-tab-pane label="转资任务完成明细" name="mssMonthDetailsPanel" lazy>
              <mssTable
                ref="detailsTable"
                :serial="true"
                :customSize="20"
                :api="detailListApi"
                :columns="detailsColumns"
                :staticSearchParam="staticSearchParam"
                :autoCall="false"
                border
              >
              </mssTable>
              <projectDialog ref="projectDialog" @showCheckList="showCheckList"></projectDialog>
            </el-tab-pane>
          </el-tabs>
        </div>
      </mssCard>


    </div>
  </div>
</template>

<script>
// api
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {
  checkPermission,
  summaryList,
  detailList,
  updateProjectTransfer,
  summaryDescription,
  getCityList,
  downloadService
} from '@/api/pccw/report_select/quarter_transfer_task.js'
import moment from "moment/moment";
import {queryAreaListService} from "@/api/common_api";
import projectDialog from "@/views/pccw/report_select/quarter_transfer_task/project_dialog.vue";
import {exportAuditAlertInfo} from "@/api/pccw/report_select/settlement_reduction_rate";

export default {
  name: 'quarter_transfer_task',
  components: {projectDialog},
  data() {
    let titleName = '季度转资任务完成报表'
    //当前时间
    let date = new Date();
    const year = date.getFullYear(); // 获取年份
    const month = date.getMonth(); // 获取月份
    // 创建当前月份的第一天
    const startDay = new Date(year, month, 1);
    // 创建当前月份的最后一天
    const endDay = new Date(year, month + 1, 0);

    let yearPickerOptions = {
      disabledDate(time) {
        //限制用户只能选择当前年份和之前的年份。
        // return time.getTime() > Date.now()

        // 限制用户只能选择之前的年份
        // 获取当前日期和去年同一天的日期对象
        const today = new Date();
        const lastYearToday = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
        // 将传入日期对象与去年同一天的日期对象比较
        return time > lastYearToday;
      }
    };
    let monthPickerOptions = {
      disabledDate(time) {
        //限制用户只能选择当前月份和之前的月份。
        // return time.getTime() > Date.now()

        // 获取当前日期和上个月同一天的日期对象
        const today = new Date();
        const lastMonthToday = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        // 将传入日期对象与上个月同一天的日期对象比较
        return time > lastMonthToday;
      }
    };

    return {
      startDay: startDay,
      endDay: endDay,
      //默认选中 tab
      activeName: 'mssMonthSummaryPanel',
      //判断用户是否有权限配置
      isShow: false,
      //是否显示
      isShowDesc: false,
      //点评性描述
      description: '',

      monthPickerOptions: monthPickerOptions,
      yearPickerOptions: yearPickerOptions,
      searchForm: {
        granularity: "month",

        city: '',
        businessEntity: '',
        // startDate: moment(startDay).format('YYYY-MM-DD 00:00:00'),
        // endDate: moment(endDay).format('YYYY-MM-DD 23:59:59'),
        startDate: moment(startDay).format('YYYY-MM'),
        endDate: moment(endDay).format('YYYY-MM'),
      },
      searchConfig: [
        //颗粒选择
        {
          type: 'select',
          label: '日期颗粒',
          fieldName: 'granularity',
          options: [
            {label: '年颗粒', value: 'year'},
            {label: '季度颗粒', value: 'quarter'},
            {label: '月颗粒', value: 'month'},

          ],

        },
        //月选择框
        {
          type: 'cycleDate',
          dateType: 'month',
          label: '月度',
          format: 'yyyy-MM',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
          fieldName: 'startDate',
          clearable: false,
        },
        //地市选择框
        {
          label: '地市',
          type: 'select',
          fieldName: 'city',

          itemAs: true,
          options: []
        },
      ],
      //静态搜索参数
      staticSearchParam: {
        granularity: "month",
        city: '',
        businessEntity: '',
        startDate: moment(startDay).format('YYYY-MM'),
        endDate: moment(endDay).format('YYYY-MM'),
      },
      // 导出参数
      loadParam: {
        granularity: 'month',
        city: '',
        businessEntity: '',
        startDate: moment(startDay).format('YYYY-MM'),
        endDate: moment(endDay).format('YYYY-MM'),
      },
      summaryListApi: summaryList,
      detailListApi: detailList,

      monthColumns: [
        {
          label: '地市',
          prop: 'city',
          fixed: true,
          tooltip: true,
          minWidth: 100,
        },
        {
          prop: "titleName",
          label: titleName,
          multilevelColumn: [
            {
              label: 'TD',
              prop: 'TD',
              multilevelColumn: [
                {
                  label: '当月数',
                  prop: '当月数',
                  multilevelColumn: [
                    {
                      label: '当月任务(万元)',
                      prop: 'tdTaskMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '当月完成(万元)',
                      prop: 'tdFinishMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '当月完成进度(%)',
                      prop: 'tdProgressMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '季度数',
                  prop: '季度数',
                  multilevelColumn: [
                    {
                      label: '季度任务(万元)',
                      prop: 'tdTaskQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成(万元)',
                      prop: 'tdFinishQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成进度(%)',
                      prop: 'tdProgressQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'tdTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'tdFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'tdProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
            {
              label: '上市',
              prop: '上市',
              multilevelColumn: [
                {
                  label: '当月数',
                  prop: '当月数',
                  multilevelColumn: [
                    {
                      label: '当月任务(万元)',
                      prop: 'listedTaskMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '当月完成(万元)',
                      prop: 'listedFinishMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '当月完成进度(%)',
                      prop: 'listedProgressMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '季度数',
                  prop: '季度数',
                  multilevelColumn: [
                    {
                      label: '季度任务(万元)',
                      prop: 'listedTaskQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成(万元)',
                      prop: 'listedFinishQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成进度(%)',
                      prop: 'listedProgressQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'listedTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'listedFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'listedProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
            {
              label: '全口径',
              prop: '全口径',
              multilevelColumn: [
                {
                  label: '当月数',
                  prop: '当月数',
                  multilevelColumn: [
                    {
                      label: '当月任务(万元)',
                      prop: 'allCalibersTaskMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '当月完成(万元)',
                      prop: 'allCalibersFinishMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '当月完成进度(%)',
                      prop: 'allCalibersProgressMonth',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '季度数',
                  prop: '季度数',
                  multilevelColumn: [
                    {
                      label: '季度任务(万元)',
                      prop: 'allCalibersTaskQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成(万元)',
                      prop: 'allCalibersFinishQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成进度(%)',
                      prop: 'allCalibersProgressQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'allCalibersTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'allCalibersFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'allCalibersProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
          ]
        },
      ],
      quarterColumns: [
        {
          label: '地市',
          prop: 'city',
          fixed: true,
          tooltip: true,
          minWidth: 100,
        },
        {
          prop: "titleName",
          label: titleName,
          multilevelColumn: [
            {
              label: 'TD',
              prop: 'TD',
              multilevelColumn: [
                {
                  label: '季度数',
                  prop: '季度数',
                  multilevelColumn: [
                    {
                      label: '季度任务(万元)',
                      prop: 'tdTaskQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成(万元)',
                      prop: 'tdFinishQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成进度(%)',
                      prop: 'tdProgressQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'tdTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'tdFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'tdProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
            {
              label: '上市',
              prop: '上市',
              multilevelColumn: [
                {
                  label: '季度数',
                  prop: '季度数',
                  multilevelColumn: [
                    {
                      label: '季度任务(万元)',
                      prop: 'listedTaskQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成(万元)',
                      prop: 'listedFinishQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成进度(%)',
                      prop: 'listedProgressQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'listedTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'listedFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'listedProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
            {
              label: '全口径',
              prop: '全口径',
              multilevelColumn: [
                {
                  label: '季度数',
                  prop: '季度数',
                  multilevelColumn: [
                    {
                      label: '季度任务(万元)',
                      prop: 'allCalibersTaskQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成(万元)',
                      prop: 'allCalibersFinishQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '季度完成进度(%)',
                      prop: 'allCalibersProgressQuarter',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                },
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'allCalibersTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'allCalibersFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'allCalibersProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
          ]
        },
      ],
      yearColumns: [
        {
          label: '地市',
          prop: 'city',
          fixed: true,
          tooltip: true,
          minWidth: 100,
        },
        {
          prop: "titleName",
          label: titleName,
          multilevelColumn: [
            {
              label: 'TD',
              prop: 'TD',
              multilevelColumn: [
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'tdTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'tdFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'tdProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
            {
              label: '上市',
              prop: '上市',
              multilevelColumn: [
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'listedTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'listedFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'listedProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
            {
              label: '全口径',
              prop: '全口径',
              multilevelColumn: [
                {
                  label: '截止当月累计数',
                  prop: '截止当月累计数',
                  multilevelColumn: [
                    {
                      label: '累计任务(万元)',
                      prop: 'allCalibersTaskYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成(万元)',
                      prop: 'allCalibersFinishYear',
                      tooltip: true,
                      minWidth: 120
                    },
                    {
                      label: '累计完成进度(%)',
                      prop: 'allCalibersProgressYear',
                      tooltip: true,
                      minWidth: 120
                    },
                  ]
                }
              ]
            },
          ]
        },
      ],
      detailsColumns: [
        {
          prop: "titleName",
          label: titleName,
          multilevelColumn: [
            {
              label: '类型',
              prop: 'type',
              tooltip: true,
              minWidth: 60,
            },
            {
              label: '业务实体',
              prop: 'businessEntity',
              tooltip: true,
              minWidth: 80,
            },
            {
              label: '建设单位',
              prop: 'constructionUnit',
              tooltip: true,
              minWidth: 80,
            },
            {
              label: '项目编码',
              prop: 'projectCode',
              tooltip: true,
              minWidth: 120,
            },
            {
              label: '项目名称',
              prop: 'projectName',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '项目类型',
              prop: 'projectType',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '转固定资产金额',
              prop: 'fixedAssetAmount',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '转无形资产金额',
              prop: 'intangibleAssetAmount',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '转长期待摊金额',
              prop: 'deferredAssetAmount',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '转资金额合计',
              prop: 'totalAssetAmount',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '转固日期',
              prop: 'fixedDate',
              tooltip: true,
              minWidth: 80,
            },
            {
              label: '工程管理经理（主）',
              prop: 'engineeringManagementManagerMain',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '工程实施经理（主）',
              prop: 'engineeringImplementationManagerMain',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '工程实施经理（辅）',
              prop: 'engineeringImplementationManagerAuxiliary',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '转资管理员',
              prop: 'transferAdmin',
              tooltip: true,
              minWidth: 100,
            },
            {
              label: '操作',
              prop: 'caozuo',
              formatter: (row) => {
                const rest = row.businessEntity == '省本部'
                return (
                  <span>
                    {rest ? (
                      <span class='table_btn' onClick={() => {
                        this.openProjectDailog('senior', 'detail', row)
                      }}>
                  修改项目
                </span>
                    ) : (
                      <span> </span>
                    )}

              </span>
                )
              }
            },
          ],
        }
      ],
      //项目选择
      projectRow: {},
    }
  },
  created() {
    //判断是否用户是否有权限查看
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      checkPermission().then((res) => {
        this.isShow = res.data
      })
    } else {
      this.isShow = true
    }
    // 查询地市字典，parentId为地市父类 ID，index为地市选择框在 searchConfig 中的索引
    this.getAreaList('-2', 2)
    this.getNowDate(this.startDay, this.endDay, this.staticSearchParam.granularity)
  },
  mounted() {
    this.$nextTick(() => {
      // 监听 tableData[0] 的变化
      this.$watch('$refs.yearTable.tableData', this.handleTableDataChange);
      this.$watch('$refs.quarterTable.tableData', this.handleTableDataChange);
    });
  },
  methods: {
    // tab切换
    handleClick(tab, event) {
      if (tab.name === 'mssMonthSummaryPanel') {
        this.activeName = 'mssMonthSummaryPanel'
        // 使用 filter 方法过滤出不符合条件的项
        const filteredArr = this.searchConfig.filter(item => {
          // 如果服务要求和状态都符合要求，则返回 false
          if (item.fieldName === 'projectCode' || item.fieldName === 'projectName'
            || item.fieldName === 'engineeringManagementManagerMain' || item.fieldName === 'engineeringImplementationManagerMain'
            || item.fieldName === 'transferAdmin') {
            return false;
          }
          // 其他情况返回 true
          return true;
        });
        this.searchConfig = filteredArr

      } else if (tab.name === 'mssMonthDetailsPanel') {
        this.activeName = 'mssMonthDetailsPanel'
        this.searchConfig.push(
          {
            label: '项目编码',
            type: 'input',
            fieldName: 'projectCode'
          },
          {
            label: '项目名称',
            type: 'input',
            fieldName: 'projectName'
          }, {
            label: '工程管理经理-主',
            type: 'input',
            fieldName: 'engineeringManagementManagerMain'
          }, {
            label: '工程实施经理-主',
            type: 'input',
            fieldName: 'engineeringImplementationManagerMain'
          }, {
            label: '转资管理员',
            type: 'input',
            fieldName: 'transferAdmin'
          },
        )
      }
      this.search()
    },

    changeSelect(name, val) {
      if (name === 'granularity') {
        //当前时间
        let date = new Date();
        const yearToday = date.getFullYear(); // 获取年份
        const monthToday = date.getMonth(); // 获取月份
        // 创建当前月份的第一天
        const startToday = new Date(yearToday, monthToday, 1);

        // 创建当前月份的最后一天
        const endToday = new Date(yearToday, monthToday + 1, 0);
        let startDay
        let endDay
        console.log('点击改变：', val)
        if (val === 'year') {
          this.$set(this.searchConfig[1], 'type', 'cycleDate')
          this.$set(this.searchConfig[1], 'dateType', 'year')
          this.$set(this.searchConfig[1], 'label', '年度')
          this.$set(this.searchConfig[1], 'format', 'yyyy')
          this.$set(this.searchConfig[1], 'valueFormat', 'yyyy')
          this.$set(this.searchConfig[1], 'fieldName', 'startDate')
          //获取nowDate的所在的去年年份
          let nowYear = startToday.getFullYear() - 1
          // 创建当前年份的第一天
          startDay = new Date(nowYear, 0, 1);
          // 创建当前年份的最后一天
          endDay = new Date(nowYear, 11, 31);
          this.searchForm.granularity = 'year'
          this.$refs.searchForm.searchForm.granularity = 'year'
        } else if (val === 'month') {
          this.$set(this.searchConfig[1], 'type', 'cycleDate')
          this.$set(this.searchConfig[1], 'dateType', 'month')
          this.$set(this.searchConfig[1], 'label', '月度')
          this.$set(this.searchConfig[1], 'format', 'yyyy-MM')
          this.$set(this.searchConfig[1], 'valueFormat', 'yyyy-MM-dd')
          this.$set(this.searchConfig[1], 'fieldName', 'startDate')
          //获取nowDate的上个月的第一天和最后一天
          const year = startToday.getFullYear(); // 获取年份
          const month = startToday.getMonth(); // 获取月份
          // 创建当前月份的第一天
          startDay = new Date(year, month, 1);
          // 创建当前月份的最后一天
          endDay = new Date(year, month + 1, 0);
          this.searchForm.granularity = 'month'
          this.$refs.searchForm.searchForm.granularity = 'month'
        } else if (val === 'quarter') {
          this.$set(this.searchConfig[1], 'type', 'quarter')
          this.$set(this.searchConfig[1], 'dateType', 'quarter')
          this.$set(this.searchConfig[1], 'label', '季度')
          this.$set(this.searchConfig[1], 'format', 'yyyy年第Q季度')
          this.$set(this.searchConfig[1], 'valueFormat', 'yyyy-qq')
          this.$set(this.searchConfig[1], 'fieldName', 'startDateQuarter')
          const year = startToday.getFullYear(); // 获取年份
          // 获取当前月份（注意月份是从0开始的，所以要加1）
          var month = startToday.getMonth() + 1;
          // 根据月份计算季度
          var quarter = Math.ceil(month / 3);
          switch (quarter) {
            case 1:
              startDay = new Date(year, 0, 1);
              endDay = new Date(year, 2, 31);
              this.$refs.searchForm.searchForm.startDateQuarter = moment(startDay).format('YYYY-01-DD 00:00:00')
              break;
            case 2:
              startDay = new Date(year, 3, 1);
              endDay = new Date(year, 5, 30);
              this.$refs.searchForm.searchForm.startDateQuarter = moment(startDay).format('YYYY-02-DD 00:00:00')
              break;
            case 3:
              startDay = new Date(year, 6, 1);
              endDay = new Date(year, 8, 30);
              this.$refs.searchForm.searchForm.startDateQuarter = moment(startDay).format('YYYY-03-DD 00:00:00')
              break;
            case 4:
              startDay = new Date(year, 9, 1);
              endDay = new Date(year, 11, 31);
              this.$refs.searchForm.searchForm.startDateQuarter = moment(startDay).format('YYYY-04-DD 00:00:00')
              break;
          }

          this.searchForm.granularity = 'quarter'
          this.$refs.searchForm.searchForm.granularity = 'quarter'
        }
        this.$refs.searchForm.searchForm.city = this.searchForm.city

        this.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
          this.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
          this.$refs.searchForm.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
          this.$refs.searchForm.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
          console.log("切换后的时间", startDay, endDay)


      }
    },
    getAreaList(parentId, index) {
      getCityList().then(res => {
        console.log("城市列表2", res.data)
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({label: item.name, value: item.name})
          })
          this.$set(this.searchConfig[index], 'options', list)
        }
      })
    },
    //打开项目选择弹窗
    openProjectDailog(type, operation, row) {
      this.projectRow = row
      this.$refs.projectDialog.init(row)
    },
    async showCheckList(projectInfo) {
      let constructionProjectId = projectInfo.constructionProjectId
      let transferId = projectInfo.transferId
      let relationId = projectInfo.relationId
      let param = {
        constructionProjectId: constructionProjectId,
        transferId: transferId,
        relationId: relationId,
      }
      await updateProjectTransfer(param).then(res => {
        if (res.code === '0000') {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.$refs.detailsTable.getTableData(this.loadParam);
        }
      })
    },
    //解析时间，构建表头
    getNowDate(startDate, endDate, granularity) {

      console.log(granularity)
      let nowYear = startDate.getFullYear()
      let nowMonth = startDate.getMonth() + 1
      var startDayStr = moment(startDate).format('YYYY年MM月DD日');
      var endDayStr = moment().format('YYYY年MM月DD日');
      var newTitleName = '';
      switch (granularity) {
        case "month":
          newTitleName = nowYear + "年" + nowMonth + "月转资任务完成情况";
          this.titleName = newTitleName + "（截止" + endDayStr + "）"
          this.monthColumns[1].label = this.titleName
          // this.columns = this.monthColumns
          break;
        case "quarter":
          //判断是第几季度
          var quarter = Math.floor((nowMonth / 3)) + 1;
          newTitleName = nowYear + "年" + quarter + "季度转资任务完成情况";
          this.titleName = newTitleName + "（截止" + endDayStr + "）"
          this.quarterColumns[1].label = this.titleName
          // this.columns  = this.quarterColumns
          break;
        case "year":
          newTitleName = nowYear + "年转资任务完成情况";
          this.titleName = newTitleName + "（截止" + endDayStr + "）"
          this.yearColumns[1].label = this.titleName
          // this.columns  = this.yearColumns
          break;
      }
      this.detailsColumns[0].label = this.titleName
      // this.$refs.table.$forceUpdate()
    },
    async search() {
      console.log('点击搜索')
      let granularity = this.searchForm.granularity
      let city = this.searchForm.city
      if (this.$refs.searchForm.searchForm.city.label) {
        city = this.$refs.searchForm.searchForm.city.label
      }
      let startDay = new Date();
      let endDay = new Date();
      let userDate = new Date(this.$refs.searchForm.searchForm.startDate);
      console.log('用户选择的时间', userDate)
      switch (granularity) {
        case "year":
          //获取nowDate的所在年份中的第一天和最后一天
          let nowYear = userDate.getFullYear()
          // 创建当前年份的第一天
          startDay = new Date(nowYear, 0, 1);
          // 创建当前年份的最后一天
          endDay = new Date(nowYear, 11, 31);
          break;
        case "month":
          //获取nowDate的所在月份中的第一天和最后一天
          const year = userDate.getFullYear(); // 获取年份
          const month = userDate.getMonth(); // 获取月份
          // 创建当前月份的第一天
          startDay = new Date(year, month, 1);
          // 创建当前月份的最后一天
          endDay = new Date(year, month + 1, 0);
          break;
        case "quarter":
          userDate = new Date(this.$refs.searchForm.searchForm.startDateQuarter);
          // 获取月份
          console.log("用户选择的季度", userDate.getMonth())
          const yearToday = userDate.getFullYear(); // 获取年份
          switch (userDate.getMonth()) {
            case 0:
              startDay = new Date(yearToday, 0, 1);
              endDay = new Date(yearToday, 2, 31);
              break;
            case 1:
              startDay = new Date(yearToday, 3, 1);
              endDay = new Date(yearToday, 5, 30);
              break;
            case 2:
              startDay = new Date(yearToday, 6, 1);
              endDay = new Date(yearToday, 8, 30);
              break;
            case 3:
              startDay = new Date(yearToday, 9, 1);
              endDay = new Date(yearToday, 11, 31);
              break;
          }
          break;
      }
      this.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00')
      this.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59')
      this.$refs.searchForm.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00')
      this.$refs.searchForm.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59')
      console.log('打印第一天的日期', startDay); // 打印第一天的日期
      console.log('打印最后一天的日期', endDay); // 打印最后一天的日期
      await this.getNowDate(startDay, endDay, granularity)

      this.searchForm.city = city
      this.searchForm.granularity = granularity
      // 构建请求参数
      this.loadParam.startDate = moment(startDay).format('YYYY-MM')
      this.loadParam.endDate = moment(endDay).format('YYYY-MM')
      this.loadParam.businessEntity = city;
      this.loadParam.granularity = granularity;

      this.loadParam.projectCode = this.$refs.searchForm.searchForm.projectCode
      this.loadParam.projectName = this.$refs.searchForm.searchForm.projectName
      this.loadParam.engineeringManagementManagerMain = this.$refs.searchForm.searchForm.engineeringManagementManagerMain
      this.loadParam.engineeringImplementationManagerMain = this.$refs.searchForm.searchForm.engineeringImplementationManagerMain
      this.loadParam.transferAdmin = this.$refs.searchForm.searchForm.transferAdmin
      this.staticSearchParam = this.loadParam
      console.log('请求参数', this.loadParam)
      if (this.activeName === 'mssMonthSummaryPanel') {
        switch (granularity) {
          case "year":
            // 设置当前页
            this.$refs.yearTable.page.current = 1
            this.loadParam.page = this.$refs.yearTable.page.current;
            this.loadParam.limit = this.$refs.yearTable.page.size;
            await this.$refs.yearTable.getTableData(this.loadParam);
            this.isShowDesc = true
            break;
          case "quarter":
            this.$refs.quarterTable.page.current = 1
            this.loadParam.page = this.$refs.quarterTable.page.current;
            this.loadParam.limit = this.$refs.quarterTable.page.size;
            await this.$refs.quarterTable.getTableData(this.loadParam);
            this.isShowDesc = true
            break;
          case "month":
            this.$refs.monthTable.page.current = 1
            this.loadParam.page = this.$refs.monthTable.page.current;
            this.loadParam.limit = this.$refs.monthTable.page.size;
            await this.$refs.monthTable.getTableData(this.loadParam);
            this.isShowDesc = false
            break;
        }
      } else if (this.activeName === 'mssMonthDetailsPanel') {
        await this.$refs.detailsTable.getTableData(this.loadParam);
      }
    },
    reset() {
      this.$refs.searchForm.searchForm.city = ""
      this.$refs.searchForm.searchForm.projectCode = ""
      this.$refs.searchForm.searchForm.projectName = ""
      this.$refs.searchForm.searchForm.engineeringManagementManagerMain = ""
      this.$refs.searchForm.searchForm.engineeringImplementationManagerMain = ""
      this.$refs.searchForm.searchForm.transferAdmin = ""
      this.$refs.searchForm.searchForm.granularity = 'month'
      this.loadParam = {granularity: 'month'}
      this.searchForm.city = ""
      this.searchForm.granularity = "month"
      this.changeSelect('granularity', 'month')
      this.$refs.searchForm.searchForm.startDate = this.startDay
      this.search()
    },
    handleTableDataChange(newValue) {
      // 在 tableData[0] 变化时触发的方法
      console.log('tableData[0] 变化了，新值为：', newValue);
      // 调用其他方法或执行其他逻辑
      this.showDesc(this.loadParam)
    },
    //显示点评性描述
    async showDesc(params) {
      await summaryDescription(params).then(res => {
        if (res.code === '0000') {
          this.description = res.data
        }
      })
    },
    exportList(activeName) {
      let param = {...this.loadParam}
      param.titleName = this.titleName
      param.activeName = activeName
      console.log("导出参数：", param)
      commonDown(param, downloadService);
    }
  }
}
</script>
