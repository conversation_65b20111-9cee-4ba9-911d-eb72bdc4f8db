/**
* @author: ye<PERSON><PERSON>
* @date: 2023-01-13
* @description: 季度转资任务完成报表-列表查询-月度面板
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :form="searchForm"
      :searchConfig="searchConfig"
      @changeSelect="changeSelect"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportList">导出</el-button>
      </div>
      <div slot="content">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="转资任务完成情况" name="mssMonthSummaryPanel" lazy>
            <mssTable
              ref="table"
              :serial="false"
              :customSize="20"
              :api="summaryListApi"
              :columns="columns"
              :staticSearchParam="staticSearchParam"
              :show-summary="true"
              :get-summaries="getSummaries"
              border
            >
            </mssTable>
          </el-tab-pane>
          <el-tab-pane label="转资任务完成明细" name="mssMonthDetailsPanel" lazy>
            <mssTable
              ref="detailsTable"
              :serial="true"
              :customSize="20"
              :api="detailListApi"
              :columns="detailsColumns"
              :staticSearchParam="staticSearchParam"
              border
            >
            </mssTable>
          </el-tab-pane>
        </el-tabs>


      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import {summaryList,detailList} from "@/api/pccw/report_select/quarter_transfer_task"; //转资任务完成情况
import { commonOneDel, commonMultDel } from '@/utils/btn'
import { queryAreaListService } from "@/api/common_api"

export default {
  name: 'mssMonthPanel',

  data() {
    //当前时间
    let nowDate = new Date();
    let titleName='转资任务完成情况';

    return {
      //默认选中 tab
      activeName: 'mssMonthSummaryPanel',
      // tab切换
      handleClick(tab, event) {
        console.log(tab, event);
      },
      searchConfig: [
        //年度时间选择框
        // {
        //   type: 'date1',
        //   fieldName: 'evalYear',
        //   dateType: 'year',
        //   label: '年度',
        //   format: 'yyyy',
        //   valueFormat: 'yyyy'
        // },
        //季度与月度联动选择框
        // {
        //   label: '季度',
        //   type: 'select',
        //   options: [
        //     {
        //       label: '第一季度',
        //       value: '1'
        //     },
        //     {
        //       label: '第二季度',
        //       value: '2'
        //     },
        //     {
        //       label: '第三季度',
        //       value: '3'
        //     },
        //     {
        //       label: '第四季度',
        //       value: '4'
        //     }
        //   ],
        //   fieldName: 'quarter'
        // },
        {
          type: 'date1',
          dateType: 'month',
          label: '月度',
          format: 'yyyy-MM',
          valueFormat: 'yyyy-MM-dd',
          fieldName: 'startDate'
        },
        //地市选择框
        {
          label: '地市',
          type: 'select',
          fieldName: 'city',
          itemAs:true,
          options: []
        },
      ],
      searchForm:{startDate:nowDate},
      titleName:titleName,
      //表头前缀
      tablePrefix:"",

      //api
      summaryListApi:summaryList,
      detailListApi:detailList,

      staticSearchParam:{startDate:nowDate},
      columns: [
        {
          prop: "titleName",
          label: titleName,
          multilevelColumn: [
              {
                label: '地市',
                prop: 'city',
                tooltip: true,
                minWidth: 100
              },
              {
                label: 'TD任务(万元)',
                prop: 'tdTask',
                tooltip: true,
                minWidth: 120
              },
              {
                label: '上市任务(万元)',
                prop: 'listedTask',
                tooltip: true,
                minWidth: 120
              },
              {
                label: '全口径任务(万元)',
                prop: 'allCalibersTask',
                tooltip: true,
                minWidth: 120
              },
              {
                label: 'TD完成(万元)',
                prop: 'tdFinish',
                tooltip: true,
                minWidth: 120
              },
              {
                label: '上市完成(万元)',
                prop: 'listedFinish',
                tooltip: true,
                minWidth: 120
              },
              {
                label: '全口径完成(万元)',
                prop: 'allCalibersFinish',
                tooltip: true,
                minWidth: 120
              },
              {
                label: 'TD进度(%)',
                prop: 'tdProgress',
                tooltip: true,
                minWidth: 120
              },{
                label: '上市进度(%)',
                prop: 'listedProgress',
                tooltip: true,
                minWidth: 120
              },
              {
                label: '全口径进度(%)',
                prop: 'allCalibersProgress',
                tooltip: true,
                minWidth: 120
              },
          ]
        },
      ],
      detailsColumns: [
        {
          label: '类型',
          prop: 'type',
          tooltip: true,
          minWidth: 100
        },
        {
          label: '业务实体',
          prop: 'businessEntity',
          tooltip: true,
          minWidth: 100},
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
          minWidth: 100},
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
          minWidth: 100},
        {
          label: '转固定资产金额',
          prop: 'fixedAssetAmount',
          tooltip: true,
          minWidth: 100
        },
        {
          label: '转无形资产金额',
          prop: 'intangibleAssetAmount',
          tooltip: true,
          minWidth: 100
        },
        {
          label: '转长期待摊金额',
          prop: 'deferredAssetAmount',
          tooltip: true,
          minWidth: 100
        },
        {
          label: '转资金额合计',
          prop: 'totalAssetAmount',
          tooltip: true,
          minWidth: 100
        },
        {
          label: '转固日期',
          prop: 'fixedDate',
          tooltip: true,
          minWidth: 100
        },
        {
          label: '项目类型',
          prop: 'projectType',
          tooltip: true,
          minWidth: 100
        },
        {
          label: '工程管理经理/工程实施经理(主)',
          prop: 'engineManager',
          tooltip: true,
          minWidth: 100
        },
      ],
    }
  },
  created() {
    // 查询地市字典，parentId为地市父类 ID，index为地市选择框在 searchConfig 中的索引
    this.getAreaList('-2', 1)

    this.getNowDate(this.searchForm.startDate)

    // this.getStatusList()
  },
  methods: {
    //搜索条件变更
    changeSelect(name, val){

    },
    getAreaList(parentId, index) {
      queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.searchConfig[index], 'options', list)
        }
      })
    },
    //解析时间，获取年份，月份，当前月份最后一天
    getNowDate(nowDate) {
      let nowYear = nowDate.getFullYear()
      let nowMonth = nowDate.getMonth() + 1
      let nowDay = nowDate.getDate()
      let nowMonthLastDay = new Date(nowYear, nowMonth, 0).getDate()
      console.log(nowYear, nowMonth, nowMonthLastDay)
      this.titleName = nowYear+"年"+nowMonth+"月份"+"转资任务完成情况（截止"+nowMonth+"月"+nowMonthLastDay+ "日）"
      this.columns[0].label=this.titleName
      //遍历 this.columns 数组，将 label 属性值替换为 titleName
      for (let i = 0; i < this.columns.length; i++) {
        if (this.columns[i].multilevelColumn) {
          for (let j = 0; j < this.columns[i].multilevelColumn.length; j++) {
            switch (this.columns[i].multilevelColumn[j].prop){
              case "tdTask":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"TD任务(万元)"
                break;
              case "listedTask":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"上市任务(万元)"
                break;
              case "allCalibersTask":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"全口径任务(万元)"
                break;
              case "tdFinish":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"TD完成(万元)"
                break;
              case "listedFinish":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"上市完成(万元)"
                break;
              case "allCalibersFinish":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"全口径完成(万元)"
                break;
              case "tdProgress":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"TD进度(%)"
                break;
              case "listedProgress":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"上市进度(%)"
                break;
              case "allCalibersProgress":
                this.columns[i].multilevelColumn[j].label = nowMonth+"月份"+"全口径进度(%)"
                break;
            }
          }
        }
      }

    },
    //合计
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      const noSumColumn = [1, 2, 3, 4, 8]
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        } else if (noSumColumn.includes(index)) {
          // 不需要合计的列
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = (values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)).toFixed(2)
        }
      })
      return sums
    },

    search() {
      console.log('search')
      console.log(this.$refs.searchForm.searchForm.startDate);
      this.searchForm.startDate = new Date(this.$refs.searchForm.searchForm.startDate)
      this.getNowDate(this.searchForm.startDate)

      this.$refs.table.page.current = 1

      this.staticSearchParam=JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    reset() {
      let nowDate = new Date();
      let year = nowDate.getFullYear();
      let month = String(nowDate.getMonth() + 1).padStart(2, '0');
      let day = String(nowDate.getDate()).padStart(2, '0');

      let dateString = `${year}-${month}-${day}`;
      this.searchForm.startDate = nowDate;
      this.$refs.searchForm.searchForm.startDate = dateString
      this.search()
    },

  }
}
</script>
