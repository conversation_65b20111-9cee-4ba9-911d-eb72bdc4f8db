/**
* @author: ye<PERSON><PERSON>
* @date: 2023-01-27
* @description:季度转资预警明细列表
*/
<template>
  <div>
    <el-dialog
      :visible.sync="showDialog"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="80%"
      :modal="true"
      append-to-body
      @close="close"
    >
      <mssTable
        ref="table"
        :serial="false"
        :customSize="10"
        :api="alertDetailsList"
        :columns="detailsColumns"
        :staticSearchParam="staticSearchParam"
        :pagination="true"
        border
      >
      </mssTable>
    </el-dialog>
  </div>
</template>
<script>
import {alertDetailsList} from "@/api/pccw/report_select/quarter_transfer_task";

export default {
  data() {
    return {
      showDialog: false,
      title: '季度转资明细信息',
      alertDetailsList: alertDetailsList,
      //静态搜索参数
      staticSearchParam: {},
      detailsColumns: [
        {
          label: '类型',
          prop: 'type',
          tooltip: true,
          minWidth: 60,
        },
        {
          label: '业务实体',
          prop: 'businessEntity',
          tooltip: true,
          minWidth: 80,
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
          minWidth: 120,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '项目类型',
          prop: 'projectType',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '转固定资产金额',
          prop: 'fixedAssetAmount',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '转无形资产金额',
          prop: 'intangibleAssetAmount',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '转长期待摊金额',
          prop: 'deferredAssetAmount',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '转资金额合计',
          prop: 'totalAssetAmount',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '转固日期',
          prop: 'fixedDate',
          tooltip: true,
          minWidth: 80,
        },
        {
          label: '工程管理经理（主）',
          prop: 'engineeringManagementManagerMain',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '工程实施经理（主）',
          prop: 'engineeringImplementationManagerMain',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '工程实施经理（辅）',
          prop: 'engineeringImplementationManagerAuxiliary',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '转资管理员',
          prop: 'transferAdmin',
          tooltip: true,
          minWidth: 100,
        },
      ],
    }
  },
  methods: {
    /**
     * 弹框初始化
     * @param city 地市
     * @param auditSummaryKey 汇总 key
     * @param val
     */
    init(city, auditSummaryKey,userId) {
      this.staticSearchParam = {
        auditCityKey: city,
        auditSummaryKey: auditSummaryKey,
        userId: userId
      },
        console.log('弹框初始化', city, auditSummaryKey,userId)
      this.showDialog = true
      this.$refs.table.getTableData(this.staticSearchParam);
    },

    close() {
      this.showDialog = false
    }
  },
}
</script>
