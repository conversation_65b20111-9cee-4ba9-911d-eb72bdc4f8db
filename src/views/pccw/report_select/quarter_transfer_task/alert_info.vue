/**
* @author: yewen<PERSON>
* @date: 2023-01-27
* @description:季度转资任务-预警
*/
<template>
  <div>
    <mssCard title="消息详情">
      <div slot="headerBtn">
      </div>
      <div slot="content">
        截止系统当前日期，根据系统统计，您名下本季度转资任务完成情况如下，请您及时根据业务进展情况进行相应处理。
      </div>
    </mssCard>
    <mssCard :title="mssCardTitleName">
      <div slot="headerBtn">
        <el-button @click="exportList">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="sumTable"
          :serial="false"
          :columns="monthColumns"
          :staticSearchParam="staticSearchParam"
          :pagination="false"
          border
        >
        </mssTable>
        <alertDetailDialog ref="alertDetailDialog"></alertDetailDialog>
      </div>
    </mssCard>
  </div>
</template>
<script>
import {exportAuditAlertInfo, getAuditAlertInfo} from "@/api/pccw/report_select/settlement_reduction_rate";
import {commonDown} from "@/utils/btn";
import {getQuarterAlertInfo,exportAlert} from "@/api/pccw/report_select/quarter_transfer_task";
import {listReportSelectConfig} from "@/api/pccw/report_select/reportSelectConfig";
import moment from "moment";
import alertDetailDialog from "@/views/pccw/report_select/quarter_transfer_task/alert_detail_dialog.vue";

export default {
  name: "index",
  components: {alertDetailDialog},
  data() {
    return {
      getQuarterAlertInfo: getQuarterAlertInfo,
      auditSummaryKey: this.$route.query.auditSummaryKey,
      userId: this.$route.query.userId,
      staticSearchParam: {
        auditCityKey: this.$route.query.city,
        auditSummaryKey: this.$route.query.auditSummaryKey,
        userId: this.$route.query.userId
      },
      titleName: '季度转资任务',
      mssCardTitleName: '季度转资情况待阅列表',
      monthColumns: [
        {
          label: '地市',
          prop: 'city',
          fixed: true,
          tooltip: true,
          minWidth: 100,
        },
        {
          label: 'TD',
          prop: 'TD',
          multilevelColumn: [
            {
              label: '当月数',
              prop: '当月数',
              multilevelColumn: [
                {
                  label: '当月任务/(万元)',
                  prop: 'tdTaskMonth',
                  tooltip: true,
                },
                {
                  label: '当月完成/(万元)',
                  prop: 'tdFinishMonth',
                  tooltip: true,
                },
                {
                  label: '当月完成进度(%)',
                  prop: 'tdProgressMonth',
                  tooltip: true,
                },
              ]
            },
            {
              label: '季度数',
              prop: '季度数',
              multilevelColumn: [
                {
                  label: '季度任务/(万元)',
                  prop: 'tdTaskQuarter',
                  tooltip: true,
                },
                {
                  label: '季度完成/(万元)',
                  prop: 'tdFinishQuarter',
                  tooltip: true,
                },
                {
                  label: '季度完成进度(%)',
                  prop: 'tdProgressQuarter',
                  tooltip: true,
                },
              ]
            },
            {
              label: '累计数',
              prop: '累计数',
              multilevelColumn: [
                {
                  label: '累计任务/(万元)',
                  prop: 'tdTaskYear',
                  tooltip: true,
                },
                {
                  label: '累计完成/(万元)',
                  prop: 'tdFinishYear',
                  tooltip: true,
                },
                {
                  label: '累计完成进度(%)',
                  prop: 'tdProgressYear',
                  tooltip: true,
                },
              ]
            }
          ]
        },
        {
          label: '上市',
          prop: '上市',
          multilevelColumn: [
            {
              label: '当月数',
              prop: '当月数',
              multilevelColumn: [
                {
                  label: '当月任务/(万元)',
                  prop: 'listedTaskMonth',
                  tooltip: true,
                },
                {
                  label: '当月完成/(万元)',
                  prop: 'listedFinishMonth',
                  tooltip: true,
                },
                {
                  label: '当月完成进度(%)',
                  prop: 'listedProgressMonth',
                  tooltip: true,
                },
              ]
            },
            {
              label: '季度数',
              prop: '季度数',
              multilevelColumn: [
                {
                  label: '季度任务/(万元)',
                  prop: 'listedTaskQuarter',
                  tooltip: true,
                },
                {
                  label: '季度完成/(万元)',
                  prop: 'listedFinishQuarter',
                  tooltip: true,
                },
                {
                  label: '季度完成进度(%)',
                  prop: 'listedProgressQuarter',
                  tooltip: true,
                },
              ]
            },
            {
              label: '累计数',
              prop: '累计数',
              multilevelColumn: [
                {
                  label: '累计任务/(万元)',
                  prop: 'listedTaskYear',
                  tooltip: true,
                },
                {
                  label: '累计完成/(万元)',
                  prop: 'listedFinishYear',
                  tooltip: true,
                },
                {
                  label: '累计完成进度(%)',
                  prop: 'listedProgressYear',
                  tooltip: true,
                },
              ]
            }
          ]
        },
        {
          label: '全口径',
          prop: '全口径',
          multilevelColumn: [
            {
              label: '当月数',
              prop: '当月数',
              multilevelColumn: [
                {
                  label: '当月任务/(万元)',
                  prop: 'allCalibersTaskMonth',
                  tooltip: true,
                },
                {
                  label: '当月完成/(万元)',
                  prop: 'allCalibersFinishMonth',
                  tooltip: true,
                },
                {
                  label: '当月完成进度(%)',
                  prop: 'allCalibersProgressMonth',
                  tooltip: true,
                },
              ]
            },
            {
              label: '季度数',
              prop: '季度数',
              multilevelColumn: [
                {
                  label: '季度任务/(万元)',
                  prop: 'allCalibersTaskQuarter',
                  tooltip: true,
                },
                {
                  label: '季度完成/(万元)',
                  prop: 'allCalibersFinishQuarter',
                  tooltip: true,
                },
                {
                  label: '季度完成进度(%)',
                  prop: 'allCalibersProgressQuarter',
                  tooltip: true,
                },
              ]
            },
            {
              label: '累计数',
              prop: '累计数',
              multilevelColumn: [
                {
                  label: '累计任务/(万元)',
                  prop: 'allCalibersTaskYear',
                  tooltip: true,
                },
                {
                  label: '累计完成/(万元)',
                  prop: 'allCalibersFinishYear',
                  tooltip: true,
                },
                {
                  label: '累计完成进度(%)',
                  prop: 'allCalibersProgressYear',
                  tooltip: true,
                },
              ]
            }
          ]
        },
        {
          label: '操作',
          prop: 'caozuo',
          fixed: 'right',
          formatter: (row) => {
            const rest = row.city !== '全省合计'
            return (
              <span>
                    {rest ? (
                      <span class='table_btn' onClick={() => {
                        this.openDetailDailog(row)
                      }}>
                  查看
                </span>
                    ) : (
                      <span> </span>
                    )}

              </span>
            )
          }
        },
      ],
      alertDate: "",
      tableData: [],
    }
  },
  async created() {
    console.log(this.$route.query.city); // 输出：row.id 对应的值
    console.log(this.$route.query.auditSummaryKey); // 输出：row.id 对应的值
    console.log(this.$route.query.userId); // 输出：row.id 对应的值
    await this.searchSummaryTable();
  },
  methods: {
    exportList() {
      console.log("导出参数：", this.staticSearchParam)
      commonDown({...this.staticSearchParam}, exportAlert);
    },
    async searchSummaryTable() {
      await getQuarterAlertInfo({...this.staticSearchParam}).then(res => {
        console.log("res：", res)
        if (res.code === '0000') {
          this.alertDate = res.data.alertDate;
          this.mssCardTitleName = this.mssCardTitleName + "（" + moment(res.data.alertDate).format('YYYY-MM-DD') + "）";

          const month = Number(moment(res.data.alertDate).format('MM')) // 获取月份
          let monthStr = month + "月份";
          //判断季度
          let quarter = "";
          if (month <= 3) {
            quarter = "一季度";
          } else if (month <= 6) {
            quarter = "二季度";
          } else if (month <= 9) {
            quarter = "三季度";
          } else {
            quarter = "四季度";
          }

          for (let item of this.monthColumns) {
            if (item.label === 'TD' || item.label === '上市' || item.label === '全口径') {
              for (let item1 of item.multilevelColumn) {
                if (item1.label === '当月数') {
                  item1.label = monthStr;
                }
                if (item1.label === '季度数') {
                  item1.label = quarter + '数';
                }
                if (item1.label === '累计数') {
                  item1.label = quarter + '累计数';
                }
              }
            }
          }
          this.tableData = res.data.tableData;
          this.$refs.sumTable.tableData = this.tableData;
        }
      })
    },
    //打开项目选择弹窗
    openDetailDailog(row) {
      this.$refs.alertDetailDialog.init(row.city, this.auditSummaryKey,this.userId)
    },
  }
}

</script>
