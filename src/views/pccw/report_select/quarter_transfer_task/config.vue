/**
*  @author: yewen<PERSON>
* @date: 2023-01-13
* @description:季度转资报表配置管理
*
*/
<template>
  <div>
    <mssCard title="季度转资与转资不及时-人员权限配置">
      <div slot="headerBtn">
        <el-button v-if="powerData.personnel_allocation_save" type="primary" @click="saveTableInfo">保存</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :selection="false"
          :api="api"
          :columns="columns"
          :stationary="stationary"
          :staticSearchParam="staticSearchParam"
          border
          :pagination="false"
        ></mssTable>
      </div>
    </mssCard>

    <mssChooseUser ref="chooseUser" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseUser>
  </div>
</template>

<script>
import {listReportSelectConfig, updateReportSelectConfig} from "@/api/pccw/report_select/reportSelectConfig";
import {
  downloadTemplate,
  quarterTransferTaskImportList,
  uploadFile
} from "@/api/pccw/report_select/quarter_transfer_task";
import {commonDown} from "@/utils/btn";
import {exportAuditAlertInfo} from "@/api/pccw/report_select/settlement_reduction_rate";
import moment from "moment";

export default {
  name: 'PersonnelAllocation',
  data() {
    return {
      powerData: [],
      api: listReportSelectConfig,
      quarterTransferTaskImportListApi: quarterTransferTaskImportList,
      type: '',
      multSelect: false,
      stationary: [],
      appTypeStationary: [],
      alertStationary: [],
      roleRow: {}, // 某行数据
      staticSearchParam: {
        code: 'quarter_transfer_task_user_config'
      },

    }
  },
  computed: {
    columns() {
      return [
        {
          label: '角色名称',
          prop: 'roleName'
        },
        {
          label: '处理人账号',
          prop: 'userNames',
          formatter: (row) => {
            return <el-input v-model={row.userNames} readonly onFocus={() => {
              this.openChooseUserDailog(row)
            }}></el-input>
          }
        },
      ]
    },

  },
  created() {
    this.getPower()
  },
  methods: {
    /*    uploadFileMonth(params){
          let param = new FormData()
          param.append('file', params.file)
          param.append('boId', this.boId)
          uploadFile(param,'month').then((res) => {
            if (res.code == '0000' && !res.data) {
              this.$message.success('导入成功')
              // this.$refs.usageRptModTable.page.current = 1
              // this.$refs.usageRptModTable.getTableData()
            } else if (res.data) {
              this.$message({
                showClose: true,
                message: res.data,
                type: 'warning'
              })
            }
          })
        },
        uploadFileQuarter(params){

        },
        //下载模版
        downloadTemplate(fileType) {
          downloadTemplate(fileType).then(res => {
            let fileName = "";
            if ("month" == fileType) {
              fileName = "月度任务预算金额导入模板.xlsx";
            } else if ("quarter" == fileType) {
              fileName = "季度任务预算金额导入模板.xlsx";
            }
            const url = window.URL.createObjectURL(new Blob([res.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', fileName);
            document.body.appendChild(link);
            link.click();
          })
        },
        */

    getPower() {
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item) => {
        this.powerData[item.authority] = true
      })
    },
    // 修改配置人员
    async action(row) {
      if (row.userIds) {
        // 保存
        const obj = {
          roleId: row.roleId,
          userId: row.userIds
        }
        const res = await this.saveServiceFn(obj)
        if (res) {
          this.$message.success("保存成功！")
          this.$refs.table.getTableData()
        }
      } else {
        this.$message.warning('请选择人员')
      }
    },
    openChooseUserDailog(row) {
      this.roleRow = row
      const item = {
        excuterNames: row.userNames,
        excuterIds: row.userIds
      }
      if (row.roleName == '市公司工程建设安全管理员') {
        this.multSelect = false
      } else {
        this.multSelect = true
      }
      console.log(item)
      // const deptParams = {deptIds: sessionStorage.getItem('firstDeptId')}
      const deptParams = {rootId: '-2', orgChildId: '-2'}
      this.$refs.chooseUser.init(item, deptParams)
    },
    showCheckList({checkList}) {
      const list = checkList
      this.specailty = list
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.realName)
        ids.push(item.userId)
      })
      const data = this.$refs.table.tableData
      this.stationary = data.map((item, index) => {
        if (item.roleId === this.roleRow.roleId) {
          return {...item, userNames: names.join(','), userIds: ids.join(',')}
        } else {
          return item
        }
      })
    },
    // 需求文档上是批量保存
    async saveTableInfo() {
      const data = this.$refs.table.tableData
      // 请求队列
      let flag = true
      console.log('保存数据', data)
      try {
        if (data[0].userIds) {
          //data 转 json
          let dataStr = JSON.stringify(data)
          let params = {
            id: "5",
            code: 'quarter_transfer_task_user_config',
            configContent: dataStr
          }
          console.log('params', JSON.stringify(params))
          await this.saveServiceFn(params)
        } else {
          flag = false
          throw new Error('请选择人员')
        }
      } catch (error) {
        this.$message.warning(error.message)
      }
      if (flag) {
        // 所有请求都已完成，直接返回或处理 res
        // await Promise.all(list)
        this.$message.success("保存成功！")
        this.$refs.table.getTableData()
      }
    },
    async saveServiceFn(obj) {
      let flag = false
      await updateReportSelectConfig(obj).then(res => {
        if (res.code === '0000') {
          flag = true
        } else {
          flag = false
        }
      })
      return flag
    },
  }
}
</script>
