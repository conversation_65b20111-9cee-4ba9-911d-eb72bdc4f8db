/**
*  @author: yewen<PERSON>
* @date: 2023-01-13
* @description:季度转资导入
*
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :form="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
      @changeSelect="changeSelect"
    ></mssSearchForm>
    <mssCard title="季度转资任务导入">
      <div slot="headerBtn">
        <el-button type="primary" @click="saveTableInfo">保存</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :selection="false"
          :api="quarterTransferTaskImportListApi"
          :columns="taskColumns"
          :stationary="stationary"
          :staticSearchParam="taskStaticSearchParam"
          border
          :pagination="false"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {listReportSelectConfig, updateReportSelectConfig} from "@/api/pccw/report_select/reportSelectConfig";
import {
  downloadTemplate,
  quarterTransferTaskImportList, updateTransferTaskImportList,
  uploadFile
} from "@/api/pccw/report_select/quarter_transfer_task";
import {commonDown} from "@/utils/btn";
import {exportAuditAlertInfo} from "@/api/pccw/report_select/settlement_reduction_rate";
import moment from "moment";

export default {
  name: 'task_import',
  data() {
    //当前时间
    const date = new Date();
    const year = date.getFullYear(); // 获取年份
    const month = date.getMonth(); // 获取月份
    // 创建当前月份的第一天
    const startDay = new Date(year, month, 1);
    // 创建当前月份的最后一天
    const endDay = new Date(year, month + 1, 0);
    //
    // let monthPickerOptions = {
    //   disabledDate(time) {
    //     //限制用户只能选择当前月份和之前的月份。
    //     // return time.getTime() > Date.now()
    //
    //     // 获取当前日期和上个月同一天的日期对象
    //     const today = new Date();
    //    // const lastMonthToday = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    //     // 获取今年 12 月的最后一天
    //     const lastMonthToday = new Date(today.getFullYear(), 11, 31);
    //     // 将传入日期对象与上个月同一天的日期对象比较
    //     return time > lastMonthToday;
    //   }
    // };
    let quarterPickerOptions = {
      disabledDate(time) {
        const currentYear = new Date().getFullYear();
        const selectedYear = new Date(time).getFullYear();
        // 只允许选择当前年份及之前的季度
        return selectedYear > currentYear;
      }
    };
    let monthPickerOptions = {
      disabledDate(time) {
        //限制用户只能选择当前年的月份和之前年的月份。
        const today = new Date();
        const lastMonthToday = new Date(today.getFullYear(), 11, today.getDate());
        // 将传入日期对象与上个月同一天的日期对象比较
        return time > lastMonthToday;
      }
    };

    return {

      stationary: [],
      quarterTransferTaskImportListApi: quarterTransferTaskImportList,
      searchForm: {
        granularity: "month",
        startDate: moment(startDay).format('YYYY-MM-DD 00:00:00'),
        endDate: moment(endDay).format('YYYY-MM-DD 23:59:59'),
      },
      loadParam: {
        granularity: 'month',
        startDate: moment(startDay).format('YYYY-MM'),
        endDate: moment(endDay).format('YYYY-MM'),
      },

      monthPickerOptions: monthPickerOptions,
      quarterPickerOptions: quarterPickerOptions,
      searchConfig: [
        //颗粒选择
        {
          type: 'select',
          label: '日期颗粒',
          fieldName: 'granularity',
          options: [
            {label: '季度颗粒', value: 'quarter'},
            {label: '月颗粒', value: 'month'},
          ],
        },
        //月选择框
        {
          type: 'cycleDate',
          dateType: 'month',
          label: '月度',
          format: 'yyyy-MM',
          valueFormat: 'yyyy-MM-dd',
          fieldName: 'startDate',
          clearable: false,
          pickerOptions: monthPickerOptions
        },
      ],
      //静态搜索参数
      taskStaticSearchParam: {
        granularity: "month",
        startDate: moment(startDay).format('YYYY-MM'),
        endDate: moment(endDay).format('YYYY-MM'),
      },
      titleName: '',
    }
  },
  computed: {
    taskColumns() {
      return [
        {
          prop: "titleName",
          label: this.titleName,
          multilevelColumn: [
            {
              label: '公司',
              prop: 'companyName'
            },
            {
              label: 'TD（元）',
              prop: 'taskAmountTD',
              formatter: (row) => {
                return <el-input v-model={row.taskAmountTD} onChange={(value) => {
                  // 只能是数字，并且小数不能超过四位
                  const regex = /^\d+(\.\d{0,4})?$/; // 正则表达式，匹配最多四位小数的数字
                  if (!regex.test(value)) {
                    // 如果输入值不符合要求，清空输入框
                    row.taskAmountTD = '';
                    this.$message.error('请输入数字，小数位数不能超过四位！');
                  }
                }}></el-input>
              }
            },
            {
              label: '上市（元）',
              prop: 'taskAmountListed',
              formatter: (row) => {
                return <el-input v-model={row.taskAmountListed}></el-input>
              }
            },
            {
              label: '全口径（元）',
              prop: 'taskAmountAllCalibers',
              formatter: (row) => {
                row.taskAmountAllCalibers =  (Number(row.taskAmountTD) + Number(row.taskAmountListed)).toFixed(4);
                return  row.taskAmountAllCalibers
              }
            },
          ],
        },

      ]
    },

  },
  created() {
    this.getNowDate(this.taskStaticSearchParam.startDate,this.taskStaticSearchParam.endDate, this.taskStaticSearchParam.granularity)
  },
  methods: {
    getPower() {
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item) => {
        this.powerData[item.authority] = true
      })
    },
    //解析时间，构建表头
    getNowDate(startDate, endDate, granularity) {
      console.log(granularity)

      let nowYear = new Date(startDate).getFullYear()
      let nowMonth = new Date(startDate).getMonth() + 1
      var startDayStr = moment(startDate).format('YYYY年MM月DD日');
      var endDayStr = moment(endDate).format('YYYY年MM月DD日');
      var newTitleName = '';
      switch (granularity) {
        case "month":
          newTitleName = nowYear + "年" + nowMonth + "月转资任务金额导入";
          this.titleName = newTitleName

          break;
        case "quarter":
          //判断是第几季度
          var quarter = Math.floor((nowMonth / 3)) + 1;
          newTitleName = nowYear + "年" + quarter + "季度转资任务金额导入";
          this.titleName = newTitleName

          break;
      }
    },
    async search() {
      console.log('点击搜索')
      let granularity = this.searchForm.granularity
      if (this.$refs.searchForm.searchForm.granularity.value) {
        granularity = this.$refs.searchForm.searchForm.granularity.value
      }

      let startDay = new Date();
      let endDay = new Date();
      let userDate = new Date(this.$refs.searchForm.searchForm.startDate);
      console.log('用户选择的时间', userDate)
      switch (granularity) {
        case "month":
          //获取nowDate的所在月份中的第一天和最后一天
          const year = userDate.getFullYear(); // 获取年份
          const month = userDate.getMonth(); // 获取月份
          // 创建当前月份的第一天
          startDay = new Date(year, month, 1);
          // 创建当前月份的最后一天
          endDay = new Date(year, month + 1, 0);
          break;
        case "quarter":
          // 获取月份
          userDate = new Date(this.$refs.searchForm.searchForm.startDateQuarter);
          console.log("用户选择的季度", userDate.getMonth())
          const yearToday = userDate.getFullYear(); // 获取年份
          switch (userDate.getMonth()) {
            case 0:
              startDay = new Date(yearToday, 0, 1);
              endDay = new Date(yearToday, 2, 31);
              break;
            case 1:
              startDay = new Date(yearToday, 3, 1);
              endDay = new Date(yearToday, 5, 30);
              break;
            case 2:
              startDay = new Date(yearToday, 6, 1);
              endDay = new Date(yearToday, 8, 30);
              break;
            case 3:
              startDay = new Date(yearToday, 9, 1);
              endDay = new Date(yearToday, 11, 31);
              break;
          }
          break;
      }
      this.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
        this.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
        this.$refs.searchForm.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
        this.$refs.searchForm.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
        console.log('打印第一天的日期', startDay); // 打印第一天的日期
      console.log('打印最后一天的日期', endDay); // 打印最后一天的日期
      await this.getNowDate(startDay, endDay, granularity)
      this.searchForm.granularity = granularity
      // 构建请求参数
      this.loadParam.startDate = moment(startDay).format('YYYY-MM'),
        this.loadParam.endDate = moment(endDay).format('YYYY-MM'),
        this.loadParam.granularity = granularity;

      console.log('请求参数', this.loadParam)
      await this.$refs.table.getTableData(this.loadParam);
    },
    reset() {
      this.$refs.searchForm.searchForm.granularity = this.searchForm.granularity
      this.$refs.searchForm.searchForm.startDate = this.startDay
      this.search()
    },
    changeSelect(name, val) {
      if (name === 'granularity') {
        console.log("切换颗粒度")
        //当前时间
        let date = new Date();
        const yearToday = date.getFullYear(); // 获取年份
        const monthToday = date.getMonth(); // 获取月份
        // 创建当前月份的第一天
        const startToday = new Date(yearToday, monthToday, 1);

        // 创建当前月份的最后一天
        const endToday = new Date(yearToday, monthToday + 1, 0);
        let startDay
        let endDay
        console.log('点击改变：', val)
        if (val === 'month') {
          this.$set(this.searchConfig[1], 'type', 'cycleDate')
          this.$set(this.searchConfig[1], 'dateType', 'month')
          this.$set(this.searchConfig[1], 'label', '月度')
          this.$set(this.searchConfig[1], 'format', 'yyyy-MM')
          this.$set(this.searchConfig[1], 'valueFormat', 'yyyy-MM-dd')
          this.$set(this.searchConfig[1], 'fieldName', 'startDate')
          this.$set(this.searchConfig[1], 'pickerOptions', this.monthPickerOptions)

          //获取nowDate的上个月的第一天和最后一天
          const year = startToday.getFullYear(); // 获取年份
          const month = startToday.getMonth(); // 获取月份
          // 创建当前月份的第一天
          startDay = new Date(year, month, 1);
          // 创建当前月份的最后一天
          endDay = new Date(year, month + 1, 0);
          this.searchForm.granularity = 'month'
          this.$refs.searchForm.searchForm.granularity = 'month'
        } else if (val === 'quarter') {
          this.$set(this.searchConfig[1], 'type', 'quarter')
          this.$set(this.searchConfig[1], 'dateType', 'quarter')
          this.$set(this.searchConfig[1], 'label', '季度')
          this.$set(this.searchConfig[1], 'format', 'yyyy年第Q季度')
          this.$set(this.searchConfig[1], 'valueFormat', 'yyyy-qq')
          this.$set(this.searchConfig[1], 'fieldName', 'startDateQuarter')
          this.$set(this.searchConfig[1], 'pickerOptions', this.quarterPickerOptions)
          const year = startToday.getFullYear(); // 获取年份
          // 获取当前月份（注意月份是从0开始的，所以要加1）
          var month = startToday.getMonth() + 1;
          // 根据月份计算季度
          var quarter = Math.ceil(month / 3);
          switch (quarter) {
            case 1:
              startDay = new Date(year, 0, 1);
              endDay = new Date(year, 2, 31);
              this.$refs.searchForm.searchForm.startDateQuarter = moment(startDay).format('YYYY-01-DD 00:00:00')
              break;
            case 2:
              startDay = new Date(year, 3, 1);
              endDay = new Date(year, 5, 30);
              this.$refs.searchForm.searchForm.startDateQuarter = moment(startDay).format('YYYY-02-DD 00:00:00')
              break;
            case 3:
              startDay = new Date(year, 6, 1);
              endDay = new Date(year, 8, 30);
              this.$refs.searchForm.searchForm.startDateQuarter = moment(startDay).format('YYYY-03-DD 00:00:00')
              break;
            case 4:
              startDay = new Date(year, 9, 1);
              endDay = new Date(year, 11, 31);
              this.$refs.searchForm.searchForm.startDateQuarter = moment(startDay).format('YYYY-04-DD 00:00:00')
              break;
          }

          this.searchForm.granularity = 'quarter'
          this.$refs.searchForm.searchForm.granularity = 'quarter'
        }
        this.$refs.searchForm.searchForm.city = this.searchForm.city
        this.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
          this.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
          this.$refs.searchForm.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
          this.$refs.searchForm.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
          console.log("切换后的时间", startDay, endDay)


      }
    },
    async saveTableInfo() {
      const data = this.$refs.table.tableData
      // 请求队列
      let flag = false
      console.log('保存数据', data)
      console.log('保存数据', this.loadParam.startDate)
      console.log('保存数据', this.loadParam.endDate)
      console.log('保存数据', this.loadParam.granularity)
      try {
        data.forEach(item => {
          item.startDate = this.loadParam.startDate
          item.endDate = this.loadParam.endDate
          item.taskDate = this.loadParam.startDate
          item.granularity = this.loadParam.granularity
          console.log(item.taskAmountTD)
          //任务金额taskAmountTD只能是数值，最多4位小数
          if (item.taskAmountTD !== null && item.taskAmountTD !== undefined && item.taskAmountTD !== '') {
            if (!/^\d+(\.\d{1,4})?$/.test(item.taskAmountTD)) {
              flag = false
              throw new Error('TD-任务金额只能是数字，最多四位小数')
            }
          } else {
            flag = false
            throw new Error('请输入任务金额')
          }

          if (item.taskAmountListed !== null && item.taskAmountListed !== undefined && item.taskAmountListed !== '') {
            if (!/^\d+(\.\d{1,4})?$/.test(item.taskAmountListed)) {
              flag = false
              throw new Error('上市-任务金额只能是数字')
            }
          } else {
            flag = false
            throw new Error('请输入任务金额')
          }

          if (item.taskAmountAllCalibers !== null && item.taskAmountAllCalibers !== undefined && item.taskAmountAllCalibers !== '') {
            if (!/^\d+(\.\d{1,4})?$/.test(item.taskAmountAllCalibers)) {
              flag = false
              throw new Error('全口径-任务金额只能是数字')
            }
          } else {
            flag = false
            throw new Error('请输入任务金额')
          }
        })
        flag = await this.saveServiceFn(data)
      } catch (error) {
        this.$message.warning(error.message)
      }
      if (flag) {
        // 所有请求都已完成，直接返回或处理 res
        // await Promise.all(list)
        this.$message.success("保存成功！")
        // this.$refs.table.getTableData()

      }
    },
    async saveServiceFn(obj) {
      let flag = false
      await updateTransferTaskImportList(obj).then(res => {
        if (res.code === '0000') {
          flag = true
        } else {
          flag = false
        }
      })
      return flag
    }
    ,
  }
}
</script>
