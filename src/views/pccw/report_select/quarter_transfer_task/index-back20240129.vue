/**
* @author: yewen<PERSON>
* @date: 2023-01-13
* @description: 季度转资任务完成报表-列表查询
*/
<template>
  <div>
    <!--    年度面板，季度面板，月度面板-->
    <div class="QuarterTransferTask">
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="月度报表" name="monthPanel" lazy>
          <mssMonthPanel></mssMonthPanel>
        </el-tab-pane>
        <el-tab-pane label="季度报表" name="mssQuarterPanel" lazy>
          <mssQuarterPanel></mssQuarterPanel>
        </el-tab-pane>
        <el-tab-pane label="年度报表" name="mssYearPanel" lazy>
          <mssYearPanel></mssYearPanel>
        </el-tab-pane>
      </el-tabs>
    </div>

  </div>
</template>

<script>
// api
import {commonOneDel, commonMultDel} from '@/utils/btn'
import mssMonthPanel from "@/views/pccw/report_select/quarter_transfer_task/mssMonthPanel.vue";

export default {
  name: 'quarter_transfer_task',
  components: {mssMonthPanel},
  data() {
    return {
      //默认选中 tab
      activeName: 'monthPanel',

    }
  },
  created() {

  },
  methods: {
    // tab切换
    handleClick(tab, event) {
      console.log(tab, event);
    },

  }
}
</script>
