<template>
  <div >
    <el-dialog
      :visible.sync="showDialog"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      :modal="true"
      append-to-body
      @close="close"
    >
      <mssTable
        ref="table"
        :serial="false"
        :customSize="20"
        :api="selectProjectListApi"
        :columns="columns"
        :staticSearchParam="staticSearchParam"
        :pagination="true"
        border
      >
      </mssTable>

    </el-dialog>
  </div>
</template>
<script>
import {selectProjectList} from "@/api/pccw/report_select/quarter_transfer_task";

export default {
  data() {
    return {
      showDialog: false,
      title: '选择项目',
      projectCode: '',
      transferId: '',
      relationId: '',
      selectProjectListApi: selectProjectList,
      //静态搜索参数
      staticSearchParam: {},
      columns: [
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '建设单位',
          prop: 'businessEntity',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '工程管理经理（主）',
          prop: 'engineeringManagementManagerMain',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '工程实施经理（主）',
          prop: 'engineeringImplementationManagerMain',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '工程实施经理（辅）',
          prop: 'engineeringImplementationManagerAuxiliary',
          tooltip: true,
          minWidth: 100,
        },
        {
          label: '是否启用',
          prop: 'isEnable',
          formatter: (row) => {
            const rest = row.relationId !== null && row.relationId !== ''&& row.relationId !== undefined
            if (rest){
              this.relationId = row.relationId
            }

            return (
              <span>
                    {rest ? (
                      <span>是</span>
                    ) : (
                      <span>否</span>
                    )}

              </span>
            )
          }
        },
        {
          label: '操作',
          prop: 'caozuo',
          formatter: (row) => {
            const rest = row.relationId === null || row.relationId == '' || row.relationId === undefined
            return (
              <span>
                    {rest ? (
                      <span class='table_btn' onClick={() => {
                        this.submitOrder(row)
                      }}>
                  启用
                </span>
                    ) : (
                      <span> </span>
                    )}

              </span>
            )
          }
        },
      ],
    }
  },
  methods: {
    /**
     * 弹框初始化
     * @param row
     * @param val
     */
    init(row) {
      this.projectCode = row.projectCode
      this.transferId = row.transferId
      this.relationId = row.relationId
      this.staticSearchParam = {
        projectCode: this.projectCode,
        transferId: this.transferId
      },
        console.log('弹框初始化', row)
      this.showDialog = true
      this.$refs.table.getTableData(this.staticSearchParam);
    },
    /**
     * 提交表单
     */
    submitOrder(row) {
      console.log('提交表单', row)
      this.$emit('showCheckList', {
        constructionProjectId: row.constructionProjectId,
        transferId: this.transferId,
        relationId: this.relationId,
      })
      this.close()
    },
    close() {
      this.showDialog = false
    }
  },
}
</script>
