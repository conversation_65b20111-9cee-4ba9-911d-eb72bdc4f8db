/**
* @author: ye<PERSON><PERSON>
* @date: 2023-01-27
* @description:结算审减率报表-预警
*/
<template>
  <div>
    <mssCard title="审减率超标项目信息">
      <div slot="headerBtn">
        <el-button @click="exportList">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :serial="false"
          :api="getAuditAlertInfo"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>
<script>
import {exportAuditAlertInfo, getAuditAlertInfo} from "@/api/pccw/report_select/settlement_reduction_rate";
import {commonDown} from "@/utils/btn";
export default {
  name: "index",

  data() {
    return {
      getAuditAlertInfo: getAuditAlertInfo,
      staticSearchParam: {
        alertId: this.$route.query.alertId,
        messageId: this.$route.query.messageId,
        messageType: this.$route.query.messageType,
      },
      columns: [
        {
          label: '地市',
          prop: 'city',
          tooltip: true,
          minWidth: 60
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
          minWidth: 60
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
          minWidth: 60
        },
        // {
        //   label: '项目性质',
        //   prop: 'projectNature',
        //   tooltip: true,
        //   minWidth: 60
        // },
        // {
        //   label: '费用类型',
        //   prop: 'costType',
        //   tooltip: true,
        //   minWidth: 60
        // },
        // {
        //   label: '申请类型',
        //   prop: 'applicationType',
        //   tooltip: true,
        //   minWidth: 60
        // },
        {
          label: '工程管理经理（主）',
          prop: 'provincialManager',
          tooltip: true,
          minWidth: 60
        },
        {
          label: '工程实施经理（主）',
          prop: 'constructionManager',
          tooltip: true,
          minWidth: 60
        },
        // {
        //   label: '工程实施经理（辅）',
        //   prop: 'subConstructionManager',
        //   tooltip: true,
        //   minWidth: 60
        // },
        {
          label: '审减率（%）',
          prop: 'reductionRate',
          tooltip: true,
          minWidth: 60
        },
        {
          label: '审减金额',
          prop: 'reductionAmount',
          tooltip: true,
          minWidth: 60
        },
      ],

    }
  },
  created() {
    console.log(this.$route.query.alertId); // 输出：row.id 对应的值
    console.log(this.$route.query.messageId); // 输出：row.id 对应的值
    console.log(this.$route.query.messageType); // 输出：row.messageType 对应的值
  },
  methods: {
    exportList() {
      console.log("导出参数：",this.staticSearchParam)
      commonDown({ ...this.staticSearchParam}, exportAuditAlertInfo);
    }
  }
}

</script>
