/**
* @author: yewenbin
* @date: 2023-01-13
* @description:结算审减率报表
*/
<template>
  <div>
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <mssSearchForm
        ref="searchForm"
        :form="searchForm"
        :searchConfig="searchConfig"
        @search="search"
        @reset="reset"
        @changeSelect="changeSelect"
      ></mssSearchForm>
      <mssCard title="各地市审计情况统计及展示">
        <div slot="headerBtn">
          <!--        <el-button @click="exportList">导出</el-button>-->
        </div>
        <div slot="content">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <!--          <el-tab-pane label="审计情况统计" name="mssMonthSummaryPanel" lazy>-->
            <mssTable
              ref="table"
              :serial="false"
              :api="summaryListApi"

              :columns="columns"
              :staticSearchParam="staticSearchParam"
              :pagination="false"
              border
            >
            </mssTable>
            <div style="display: flex;justify-content: flex-end;margin-top: 10px;background: #f4f4f5; height: 30px;"
                 v-if="searchForm.applicationType === '全部'">
                <span style="margin-top: 5px; margin-left: 5px;text-align: right;">
<!--                  settlementConfig遍历创建 span-->
                  <span v-for="(item,index) in settlementConfig" :key="index"
                        style="font-size: 14px;font-weight: 700;margin-right: 18px;">{{
                      item.appTypeName
                    }}{{ item.logicalJudge }}{{ item.reductionRate }}%的个数：
                    <span style="color: rgba(0,0,0,0.75);"
                          v-if="item.appTypeName =='通信工程'">{{ sumParameter.communicationCount }}</span>
                    <span style="color: rgba(0,0,0,0.75);"
                          v-else-if="item.appTypeName =='土建工程'">{{ sumParameter.constructionCount }}</span>
                  </span>
                <span style="font-size: 14px;font-weight: 700;margin-right: 18px;"> 送审合计项目数：
                  <span style="color: #000000BF;">{{ sumParameter.submittalsCount }} </span></span>
                <span style="font-size: 14px;font-weight: 700;margin-right: 18px;"> 高审减占比：
                  <span style="color: #000000BF;">{{ sumParameter.highAuditReductionRate }}% </span>
                </span>
              </span>
            </div>
            <!-- 为 ECharts 准备一个定义了宽高的 DOM -->
            <div class="container">
              <div id="main" style="width: 600px;height:400px;"></div>
              <div id="main1" style="width: 600px;height:400px;"></div>
              <div id="main2" style="width: 600px;height:400px;"></div>
            </div>

            <div v-show="yearChart">
              <el-divider></el-divider>
              <div class="container">
                <div id="main3" style="width: 490px;height:400px;"></div>
                <div id="main4" style="width: 490px;height:400px;"></div>
                <div id="main5" style="width: 490px;height:400px;">
                  <mssTable
                    ref="yearTable"
                    :serial="false"
                    :columns="yearTableColumns"
                    :pagination="false"
                    border
                  >
                  </mssTable>
                  <h3>{{ sumTitle }}</h3>
                </div>
              </div>
            </div>

            <!--          </el-tab-pane>-->
            <!--          <el-tab-pane label="审计情况明细" name="mssMonthDetailsPanel" lazy>-->
            <!--            <mssTable-->
            <!--              ref="detailsTable"-->
            <!--              :serial="true"-->
            <!--              :customSize="20"-->
            <!--              :api="detailListApi"-->
            <!--              :columns="detailsColumns"-->
            <!--              :staticSearchParam="staticSearchParam"-->
            <!--              border-->
            <!--            >-->
            <!--            </mssTable>-->
            <!--          </el-tab-pane>-->
          </el-tabs>


        </div>
      </mssCard>
    </div>

  </div>

</template>

<script>
// api
import {summaryList, detailList} from "@/api/pccw/report_select/quarter_transfer_task"; //转资任务完成情况
import {commonOneDel, commonMultDel} from '@/utils/btn'
import {queryAreaListService} from "@/api/common_api"
import moment from "moment";
import * as echarts from 'echarts'
import {settlementReductionRateSummaryList,checkPermission} from "@/api/pccw/report_select/settlement_reduction_rate";
import search from "bpmn-js/lib/features/search";
import {listReportSelectConfig} from "@/api/pccw/report_select/reportSelectConfig";

export default {
  name: 'mssMonthPanel',

  data() {
    //当前时间
    let date = new Date();
    const day = date.getDay(); // 获取给定日期是星期几（0 表示星期日，1 表示星期一，依此类推）
    const diff = day === 0 ? 7 : day; // 计算给定日期距离上一个周日的日期差
    const lastSunday = new Date(date.setDate(date.getDate() - diff)); // 创建一个新的日期对象，其日期值为上一个周日的日期值

    var date2 = new Date(lastSunday);
    const day2 = date2.getDay(); // 获取给定日期是星期几（0 表示星期日，1 表示星期一，依此类推）
    const diff2 = day2 === 0 ? 6 : day2 - 1; // 计算给定日期距离上一个周一的日期差
    const lastMonday = new Date(date2.setDate(date2.getDate() - diff2)); // 创建一个新的日期对象，其日期值为上一个周一的日期值


    let nowDate = moment(lastMonday).format('YYYY-MM-DD');
    let titleName = '资本类通信工程（含汇聚机房土建工程）结算审计情况汇总';

    let weekPickerOptions = {
      'firstDayOfWeek': 1,
      disabledDate(time) {
        //限制用户只能选择当前周期和之前的周期
        // const now = new Date()
        // const nowWeek = now.getDay()
        // const weekEnd = now.getTime() + (7 - nowWeek) * 24 * 60 * 60 * 1000
        // return time.getTime() > weekEnd
        //限制用户只能选择上周期
        var date1 = new Date();
        const day = date1.getDay(); // 获取给定日期是星期几（0 表示星期日，1 表示星期一，依此类推）
        const diff = day === 0 ? 7 : day; // 计算给定日期距离上一个周日的日期差
        const lastSunday = new Date(date1.setDate(date1.getDate() - diff)); // 创建一个新的日期对象，其日期值为上一个周日的日期值
        return time.getTime() > lastSunday
      }
    }
    let yearPickerOptions = {
      disabledDate(time) {
        //限制用户只能选择当前年份和之前的年份。
        // return time.getTime() > Date.now()

        // 限制用户只能选择之前的年份
        // 获取当前日期和去年同一天的日期对象
        const today = new Date();
        const lastYearToday = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
        // 将传入日期对象与去年同一天的日期对象比较
        return time > lastYearToday;
      }
    };
    let monthPickerOptions = {
      disabledDate(time) {
        //限制用户只能选择当前月份和之前的月份。
        // return time.getTime() > Date.now()

        // 获取当前日期和上个月同一天的日期对象
        const today = new Date();
        const lastMonthToday = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        // 将传入日期对象与上个月同一天的日期对象比较
        return time > lastMonthToday;
      }
    };
    return {
      //默认选中 tab
      activeName: 'mssMonthSummaryPanel',
      // tab切换
      handleClick(tab, event) {
        console.log('tab切换', tab, event);
      },
      weekPickerOptions: weekPickerOptions,
      monthPickerOptions: monthPickerOptions,
      yearPickerOptions: yearPickerOptions,
      lastMonday: lastMonday,
      lastSunday: lastSunday,

      searchConfig: [
        //颗粒选择
        {
          type: 'select',
          label: '日期颗粒',
          fieldName: 'granularity',
          options: [
            {label: '年颗粒', value: 'year'},
            {label: '月颗粒', value: 'month'},
            {label: '周颗粒', value: 'week'},
          ],

        },
        //周选择框
        {
          type: 'cycleDate',
          dateType: 'week',
          label: '周期',
          format: 'yyyy 第 WW 周',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
          fieldName: 'startDate',
          clearable: false,
          pickerOptions: weekPickerOptions
        },
        //类型
        {
          label: '申报类型',
          type: 'select',
          fieldName: 'applicationType',
          itemAs: true,
          options: [
            {label: '全部', value: '全部'},
            {label: '通信工程', value: '通信工程'},
            {label: '土建工程', value: '土建工程'},
          ],
        },
      ],
      searchForm: {
        granularity: "week",
        startDate: moment(lastMonday).format('YYYY-MM-DD 00:00:00'),
        applicationType: '全部',
        endDate: moment(lastSunday).format('YYYY-MM-DD 23:59:59'),
      },
      //静态搜索参数
      staticSearchParam: {
        granularity: "week",
        applicationType: '全部',
        startDate: moment(lastMonday).format('YYYY-MM-DD 00:00:00'),
        endDate: moment(lastSunday).format('YYYY-MM-DD 23:59:59'),
      },
      // 导出参数
      loadParam: {},

      titleName: titleName,
      sumTitle: "2023年的平均审减率较2022年同比下降8%",
      //新送审批次 标题
      submittalsTitle :'',
      //审结批次 标题
      triedTitle :'',
      //表头前缀
      tablePrefix: "",
      //年专属图表
      yearChart: false,
      // 汇总参数
      sumParameter: {
        //通信工程
        communicationCount: 0,
        //土建工程
        constructionCount: 0,
        //送审合计项目
        submittalsCount: 0,
        //高审减占比
        highAuditReductionRate: 0
      },
      // 汇总参数
      sumParameterLastYear: {
        //通信工程
        communicationCount: 0,
        //土建工程
        constructionCount: 0,
        //送审合计项目
        submittalsCount: 0,
        //高审减占比
        highAuditReductionRate: 0
      },

      //api settlementReductionRateSummaryList
      summaryListApi: settlementReductionRateSummaryList,
      detailListApi: detailList,


      columns: [
        {
          prop: "titleName",
          label: titleName,
          multilevelColumn: [
            {
              label: '名称',
              prop: 'name',
              tooltip: true,
              minWidth: 100
            },
            {
              label: '省公司',
              prop: 'shenggongshi',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '南昌',
              prop: 'nanchang',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '九江',
              prop: 'jiujiang',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '上饶',
              prop: 'shangrao',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '抚州',
              prop: 'fuzhou',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '宜春',
              prop: 'yichun',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '吉安',
              prop: 'jian',
              tooltip: true,
              minWidth: 60
            }, {
              label: '赣州',
              prop: 'ganzhou',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '景德镇',
              prop: 'jindezheng',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '萍乡',
              prop: 'pingxiang',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '新余',
              prop: 'xinyu',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '鹰潭',
              prop: 'yintan',
              tooltip: true,
              minWidth: 60
            },
            {
              label: '地市平均',
              prop: 'cityAverage',
              tooltip: true,
              minWidth: 60
            },
          ]
        },
      ],
      yearTableColumns: [
        {
          label: '',
          prop: 'appTypeName',
          tooltip: true,
          maxWidth: 80
        },
        {
          label: '2000',
          prop: 'lastYear',
          tooltip: true,
          maxWidth: 60
        },
        {
          label: '2000',
          prop: 'year',
          tooltip: true,
          maxWidth: 60
        },
      ],
      // detailsColumns: [
      //   {
      //     label: '类型',
      //     prop: 'type',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '业务实体',
      //     prop: 'businessEntity',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '项目编码',
      //     prop: 'projectCode',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '项目名称',
      //     prop: 'projectName',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '转固定资产金额',
      //     prop: 'fixedAssetAmount',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '转无形资产金额',
      //     prop: 'intangibleAssetAmount',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '转长期待摊金额',
      //     prop: 'deferredAssetAmount',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '转资金额合计',
      //     prop: 'totalAssetAmount',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '转固日期',
      //     prop: 'fixedDate',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '项目类型',
      //     prop: 'projectType',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      //   {
      //     label: '工程管理经理/工程实施经理(主)',
      //     prop: 'engineManager',
      //     tooltip: true,
      //     minWidth: 100
      //   },
      // ],
      //配置类静态条件
      configStaticSearchParam: {
        code: 'settlement_reduction_rate_applicationType_config'
      },
      userConfigStaticSearchParam: {
        code: 'settlement_reduction_rate_config',
      },
      //专业类型预警配置
      settlementConfig: [],
      //当前用户ID
      currentUserId: '',
      //用户配置
      isShow: false,
    }

  },
  created() {
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      // this.searchSettlementConfig(this.userConfigStaticSearchParam)
      checkPermission().then((res) => {
        this.isShow = res.data
      })
    } else {
      this.isShow = true
    }


    this.getNowDate(this.searchForm.startDate, this.searchForm.endDate, this.searchForm.applicationType)
    let userDate = new Date(this.searchForm.startDate);
    switch (this.searchForm.granularity) {
      case "year":
        //获取nowDate的所在年份中的第一天和最后一天
        let nowYear = userDate.getFullYear()
        //处理图表标题
        //新送审批次
        this.submittalsTitle = "第" + nowYear + "年新送审批次"
        // 审结批次
        this.triedTitle = "第" + nowYear + "年审结批次"
        break;
      case "month":
        //获取nowDate的所在月份中的第一天和最后一天
        const year = userDate.getFullYear(); // 获取年份
        const month = userDate.getMonth()+1; // 获取月份
        //处理图表标题
        //新送审批次
        this.submittalsTitle = "第" + month + "月新送审批次"
        // 审结批次
        this.triedTitle = "第" + month + "月审结批次"
        break;
      case "week":
        let weekNumber = this.getWeek(userDate)
        //处理图表标题
        //新送审批次
        this.submittalsTitle = "第" + weekNumber + "周新送审批次"
        // 审结批次
        this.triedTitle = "第" + weekNumber + "周审结批次"
        console.log(userDate)
        break;
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 监听 tableData[0] 的变化
      this.$watch('$refs.table.tableData', this.handleTableDataChange);
    });

  },
  methods: {
    //根据日期获取第几周
    getWeek(dateTime) {
      var time,
        week,
        checkDate = new Date(dateTime);
      checkDate.setDate(checkDate.getDate() + 4 - (checkDate.getDay() || 7));
      time = checkDate.getTime();
      checkDate.setMonth(0);
      checkDate.setDate(1);
      week = Math.floor(Math.round((time - checkDate) / 86400000) / 7) + 1;
      return week;
    },

    changeSelect(name, val) {
      if (name === 'granularity') {
        //当前时间
        let date = new Date();
        const day = date.getDay(); // 获取给定日期是星期几（0 表示星期日，1 表示星期一，依此类推）
        const diff = day === 0 ? 7 : day; // 计算给定日期距离上一个周日的日期差
        const lastSunday = new Date(date.setDate(date.getDate() - diff)); // 创建一个新的日期对象，其日期值为上一个周日的日期值

        var date2 = new Date(lastSunday);
        const day2 = date2.getDay(); // 获取给定日期是星期几（0 表示星期日，1 表示星期一，依此类推）
        const diff2 = day2 === 0 ? 6 : day2 - 1; // 计算给定日期距离上一个周一的日期差
        const lastMonday = new Date(date2.setDate(date2.getDate() - diff2)); // 创建一个新的日期对象，其日期值为上一个周一的日期值
        let startDay
        let endDay
        console.log('点击改变：', val)
        if (val === 'year') {
          this.$set(this.searchConfig[1], 'dateType', 'year')
          this.$set(this.searchConfig[1], 'label', '年度')
          this.$set(this.searchConfig[1], 'format', 'yyyy')
          this.$set(this.searchConfig[1], 'valueFormat', 'yyyy')
          this.$set(this.searchConfig[1], 'pickerOptions', this.yearPickerOptions)
          //获取nowDate的所在的去年年份
          let nowYear = lastMonday.getFullYear() - 1
          // 创建当前年份的第一天
          startDay = new Date(nowYear, 0, 1);
          // 创建当前年份的最后一天
          endDay = new Date(nowYear, 11, 31);
          this.searchForm.granularity = 'year'
          this.$refs.searchForm.searchForm.granularity = 'year'
        } else if (val === 'month') {
          this.$set(this.searchConfig[1], 'dateType', 'month')
          this.$set(this.searchConfig[1], 'label', '月度')
          this.$set(this.searchConfig[1], 'format', 'yyyy-MM')
          this.$set(this.searchConfig[1], 'valueFormat', 'yyyy-MM-dd')
          this.$set(this.searchConfig[1], 'pickerOptions', this.monthPickerOptions)
          //获取nowDate的上个月的第一天和最后一天
          const year = lastMonday.getFullYear(); // 获取年份
          const month = lastMonday.getMonth(); // 获取月份
          // 创建当前月份的第一天
          startDay = new Date(year, month - 1, 1);
          // 创建当前月份的最后一天
          endDay = new Date(year, month, 0);
          this.searchForm.granularity = 'month'
          this.$refs.searchForm.searchForm.granularity = 'month'
        } else if (val === 'week') {
          this.$set(this.searchConfig[1], 'dateType', 'week')
          this.$set(this.searchConfig[1], 'label', '周度')
          this.$set(this.searchConfig[1], 'format', 'yyyy 第 WW 周')
          this.$set(this.searchConfig[1], 'valueFormat', 'yyyy-MM-dd')
          this.$set(this.searchConfig[1], 'pickerOptions', this.weekPickerOptions)
          startDay = lastMonday
          // 创建当前周的最后一天，周天作为最后一天
          endDay = lastMonday
          this.searchForm.granularity = 'week'
          this.$refs.searchForm.searchForm.granularity = 'week'
        }
        this.$refs.searchForm.searchForm.applicationType = this.searchForm.applicationType
        this.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
          this.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
          this.$refs.searchForm.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
          this.$refs.searchForm.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
          console.log("切换后的时间", startDay, endDay)

      }
    },
    //解析时间，获取年份，月份，当前月份最后一天
    getNowDate(startDate, endDate, applicationType) {
      var startDayStr = moment(startDate).format('YYYY年MM月DD日');
      var endDayStr = moment(endDate).format('YYYY年MM月DD日');
      var newTitleName = '';
      switch (applicationType) {
        case "全部":
          newTitleName = "资本类通信工程（含汇聚机房土建工程）结算审计情况汇总";
          break;
        case "通信工程":
          newTitleName = "资本类通信工程结算审计情况汇总";
          break;
        case "土建工程":
          newTitleName = "资本类土建工程结算审计情况汇总";
          break;
      }
      this.titleName = startDayStr + "至" + endDayStr + newTitleName
      this.columns[0].label = this.titleName

    },

    async searchSettlementConfig(param) {
      await listReportSelectConfig(param).then(res => {
        if (res.code === '0000') {
          if (param.code === 'settlement_reduction_rate_applicationType_config') {
            this.settlementConfig = res.data
          } else if (param.code === 'settlement_reduction_rate_config') {
            console.log("用户信息：", this.currentUserId)
            for (let i = 0; i < res.data.length; i++) {
              res.data[i].userIds = res.data[i].userIds.split(',')
              console.log("用户信息：", res.data[i].userIds)
              //判断
              if (res.data[i].userIds.includes(this.currentUserId)) {
                this.isShow = true
                console.log("用户信息：", this.settlementUserConfig)
                break
              }
            }
          }
        }
      })
    },
    async search() {
      console.log('点击搜索')
      let applicationType = this.searchForm.applicationType
      if (this.$refs.searchForm.searchForm.applicationType.value) {
        applicationType = this.$refs.searchForm.searchForm.applicationType.value
      }
      let granularity = this.searchForm.granularity
      if (this.$refs.searchForm.searchForm.granularity.value) {
        granularity = this.$refs.searchForm.searchForm.granularity.value
      }
      let startDay = new Date();
      let endDay = new Date();
      var userDate = new Date(this.$refs.searchForm.searchForm.startDate);
      console.log('用户选择的时间', userDate)
      switch (granularity) {
        case "year":
          //获取nowDate的所在年份中的第一天和最后一天
          let nowYear = userDate.getFullYear()
          // 创建当前年份的第一天
          startDay = new Date(nowYear, 0, 1);
          // 创建当前年份的最后一天
          endDay = new Date(nowYear, 11, 31);
          //处理图表标题
          //新送审批次
          this.submittalsTitle = "第" + nowYear + "年新送审批次"
          // 审结批次
          this.triedTitle = "第" + nowYear + "年审结批次"
          break;
        case "month":
          //获取nowDate的所在月份中的第一天和最后一天
          const year = userDate.getFullYear(); // 获取年份
          const month = userDate.getMonth(); // 获取月份
          // 创建当前月份的第一天
          startDay = new Date(year, month, 1);
          // 创建当前月份的最后一天
          endDay = new Date(year, month + 1, 0);
          //处理图表标题
          //新送审批次
          this.submittalsTitle = "第" + (month + 1) +"月新送审批次"
          // 审结批次
          this.triedTitle = "第" + (month + 1) +"月审结批次"
          break;
        case "week":
          var day = userDate.getDay() || 7
          // 创建当前周的第一天，周一作为第一天
          var lastMonday = new Date(userDate.getFullYear(), userDate.getMonth(), userDate.getDate() + 1 - day);
          // 创建当前周的最后一天，周天作为最后一天
          var lastSunday = new Date(userDate.getFullYear(), userDate.getMonth(), userDate.getDate() + 7 - day);
          startDay = lastMonday
          endDay = lastSunday
          //处理图表标题
          // 计算一年中的第几周
          let weekNumber = this.getWeek(userDate)
          //新送审批次
          this.submittalsTitle = "第" + weekNumber + "周新送审批次"
          // 审结批次
          this.triedTitle = "第" + weekNumber + "周审结批次"
          break;
      }
      this.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
        this.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
        this.$refs.searchForm.searchForm.startDate = moment(startDay).format('YYYY-MM-DD 00:00:00'),
        this.$refs.searchForm.searchForm.endDate = moment(endDay).format('YYYY-MM-DD 23:59:59'),
        console.log('打印第一天的日期', startDay); // 打印第一天的日期
      console.log('打印最后一天的日期', endDay); // 打印最后一天的日期
      this.getNowDate(startDay, endDay, applicationType)
      // 设置当前页
      this.$refs.table.page.current = 1

      // this.staticSearchParam = JSON.parse(
      //   JSON.stringify(this.$refs.searchForm.searchForm)
      // )
      // this.loadParam = JSON.parse(
      //   JSON.stringify(this.$refs.searchForm.searchForm)
      // );
      this.searchForm.applicationType = applicationType
      this.searchForm.granularity = granularity

      // 构建请求参数
      this.loadParam.startDate = this.$refs.searchForm.searchForm.startDate;
      this.loadParam.endDate = this.$refs.searchForm.searchForm.endDate;
      this.loadParam.applicationType = applicationType;
      this.loadParam.granularity = granularity;
      this.loadParam.page = this.$refs.table.page.current;
      this.loadParam.limit = this.$refs.table.page.size;

      console.log('请求参数', this.loadParam)
      await this.$refs.table.getTableData(this.loadParam);
      if (this.loadParam.applicationType === '全部' && this.loadParam.granularity === 'year') {
        await this.drawChart3();
        await this.drawChart4();
        this.yearChart = true
      } else {
        this.yearChart = false
      }
      await this.drawChart();
      await this.drawChart1();
      await this.drawChart2();
    },
    reset() {
      this.$refs.searchForm.searchForm.applicationType = "全部"
      this.$refs.searchForm.searchForm.granularity = this.searchForm.granularity
      this.$refs.searchForm.searchForm.startDate = this.lastSunday
      this.search()
    },
    handleTableDataChange(newValue) {
      // 在 tableData[0] 变化时触发的方法
      console.log('tableData[0] 变化了，新值为：', newValue);
      // 调用其他方法或执行其他逻辑
      this.someMethod();
    },
    someMethod() {
      this.drawChart()
      this.drawChart1()
      this.drawChart2()
      //查询配置
      this.searchSettlementConfig(this.configStaticSearchParam);
      //给配置赋业务值
      var tableData = this.$refs.table.tableData[2];
      console.log('tableData', tableData)
      this.sumParameter = tableData
      this.sumParameterLastYear = tableData
      if (this.loadParam.applicationType === '全部' && this.loadParam.granularity === 'year') {
        this.drawChart3();
        this.drawChart4();
        this.yearChart = true
        var startDate = new Date(this.searchForm.startDate)
        //获取时间年
        var year = startDate.getFullYear();
        //year 减一年
        var lastYear = year - 1
        this.yearTableColumns[1].label = lastYear + '年'
        this.yearTableColumns[2].label = year + '年'
        console.log("需要赋值的yearTable", this.$refs.yearTable.tableData)
        var yearTableData = [];
        for (let i = 0; i < this.settlementConfig.length; i++) {
          var item = this.settlementConfig[i];
          var data = {
            appTypeName: item.appTypeName + item.logicalJudge + item.reductionRate + '%的个数：'
          }
          if (item.appTypeName == '通信工程') {
            data.year = this.sumParameter.communicationCount
            data.lastYear = this.sumParameterLastYear.communicationCountLastYear
          }
          if (item.appTypeName == '土建工程') {
            data.year = this.sumParameter.constructionCount
            data.lastYear = this.sumParameterLastYear.constructionCountLastYear
          }
          yearTableData.push(data)
        }
        yearTableData.push({
          appTypeName: '送审合计项目数：',
          year: this.sumParameter.submittalsCount,
          lastYear: this.sumParameterLastYear.submittalsCountLastYear
        })
        yearTableData.push({
          appTypeName: '高审减占比：',
          year: this.sumParameter.highAuditReductionRate,
          lastYear: this.sumParameterLastYear.highAuditReductionRateLastYear
        })
        this.$refs.yearTable.tableData = yearTableData
        if (this.sumParameter.cityAverage > this.sumParameter.cityAverageLastYear) {
          this.sumTitle = year + '年的平均审核率较' + lastYear + '年同比上涨' + Number((this.sumParameter.cityAverage - this.sumParameter.cityAverageLastYear).toFixed(2)) + '%'
        } else if (this.sumParameter.cityAverage < this.sumParameter.cityAverageLastYear) {
          this.sumTitle = year + '年的平均审核率较' + lastYear + '年同比下降' + Number((this.sumParameter.cityAverageLastYear - this.sumParameter.cityAverage).toFixed(2)) + '%'
        } else if (this.sumParameter.cityAverage = this.sumParameter.cityAverageLastYear) {
          this.sumTitle = year + '年的平均审核率较' + lastYear + '年同比持平'
        }

      } else {
        this.yearChart = false
      }
    },
    async drawChart() {
      var multilevelColumn = this.columns[0].multilevelColumn;
      var tableData = this.$refs.table.tableData[0];
      var xAxisData = []
      var seriesData = []
      for (let i = 0; i < multilevelColumn.length; i++) {
        if (multilevelColumn[i].prop !== 'name' && multilevelColumn[i].prop !== 'cityAverage' && multilevelColumn[i].prop !== 'shenggongshi') {
          xAxisData.push(multilevelColumn[i].label)
          seriesData.push(tableData[multilevelColumn[i].prop])
        }
      }
      //初始化echarts
      var chartDom = document.getElementById('main')
      let myEchart = echarts.init(chartDom)
      let submittalsTitle = this.submittalsTitle
      let option = {
        title: {
          text: ""
        },
        tooltip: {},
        legend: {
          x: 'center', //水平居中
          y: 'bottom', //垂直居下
          data: [submittalsTitle]
        },
        // grid: {
        //   left: '3%',
        //   right: '4%',
        //   bottom: '3%',
        //   containLabel: true
        // },
        xAxis: {
          type: 'category',
          //xAxisData转数组
          data: xAxisData,
          // axisTick: {
          //   alignWithLabel: true
          // },
          axisLabel: {
            interval: 0,//代表显示所有x轴标签显示
          }

        },
        yAxis: {},
        series: [
          {
            name: submittalsTitle,
            type: "bar",
            data: seriesData,
            label: {
              show: true,
              position: 'top',
              formatter: '{c}'
            },
            markLine: {
              data: [
                {type: 'average', name: '平均新送审批次'},
              ]
            },
          },
        ]
      };
      myEchart.setOption(option);
    },
    async drawChart1() {
      var multilevelColumn = this.columns[0].multilevelColumn;
      var tableData = this.$refs.table.tableData[1];
      var xAxisData = []
      var seriesData = []
      for (let i = 0; i < multilevelColumn.length; i++) {
        if (multilevelColumn[i].prop !== 'name' && multilevelColumn[i].prop !== 'cityAverage'&& multilevelColumn[i].prop !== 'shenggongshi') {
          xAxisData.push(multilevelColumn[i].label)
          seriesData.push(tableData[multilevelColumn[i].prop])
        }
      }
      //初始化echarts
      var chartDom = document.getElementById('main1')
      let myEchart = echarts.init(chartDom)
     let triedTitle =this.triedTitle
      let option = {
        title: {
          text: ""
        },
        tooltip: {},
        legend: {
          x: 'center', //水平居中
          y: 'bottom', //垂直居下
          data: [triedTitle]
        },
        // grid: {
        //   left: '3%',
        //   right: '4%',
        //   bottom: '3%',
        //   containLabel: true
        // },
        xAxis: {
          type: 'category',
          //xAxisData转数组
          data: xAxisData,
          // axisTick: {
          //   alignWithLabel: true
          // },
          axisLabel: {
            interval: 0,//代表显示所有x轴标签显示
          }

        },
        yAxis: {},
        series: [
          {
            name: triedTitle,
            type: "bar",
            data: seriesData,
            label: {
              show: true,
              position: 'top',
              formatter: '{c}'
            },
            markLine: {
              data: [
                {type: 'average', name: '平均审结批次'},
              ]
            },
          },
        ]
      };
      myEchart.setOption(option);
    },
    async drawChart2() {
      var multilevelColumn = this.columns[0].multilevelColumn;
      var tableData = this.$refs.table.tableData[2];
      var xAxisData = []
      var seriesData = []
      for (let i = 0; i < multilevelColumn.length; i++) {
        if (multilevelColumn[i].prop !== 'name' && multilevelColumn[i].prop !== 'cityAverage' && multilevelColumn[i].prop !== 'shenggongshi') {
          xAxisData.push(multilevelColumn[i].label)
          seriesData.push(tableData[multilevelColumn[i].prop])
        }
      }
      //初始化echarts
      var chartDom = document.getElementById('main2')
      let myEchart = echarts.init(chartDom)
      let option = {
        title: {
          text: ""
        },
        tooltip: {},
        legend: {
          x: 'center', //水平居中
          y: 'bottom', //垂直居下
          data: ["平均审减率（%）"]
        },
        // grid: {
        //   left: '3%',
        //   right: '4%',
        //   bottom: '3%',
        //   containLabel: true
        // },
        xAxis: {
          type: 'category',
          //xAxisData转数组
          data: xAxisData,
          // axisTick: {
          //   alignWithLabel: true
          // },
          axisLabel: {
            interval: 0,//代表显示所有x轴标签显示
          }

        },
        yAxis: {},
        series: [
          {
            name: '平均审减率（%）',
            type: "bar",
            data: seriesData,
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%'
            },
            markLine: {
              data: [
                {type: 'average', name: '平均审减率'},
              ]
            },
          }
        ]
      };
      myEchart.setOption(option);
    },
    async drawChart3() {
      var startDate = new Date(this.searchForm.startDate)
      //获取时间年
      var year = startDate.getFullYear();
      //year 减一年
      var lastYear = year - 1
      var tableData = this.$refs.table.tableData[2];
      var xAxisData = [lastYear, year]
      var seriesData = []

      seriesData.push(tableData.highAuditReductionRateLastYear)
      seriesData.push(tableData.highAuditReductionRate)

      //初始化echarts
      var chartDom = document.getElementById('main3')
      let myEchart = echarts.init(chartDom)
      let option = {
        title: {
          text: "与上一年的高审减占比",
          textAlign: 'auto',//整体（包括 text 和 subtext）的水平对齐
          textVerticalAlign: 'auto',//整体（包括 text 和 subtext）的垂直对齐
        },
        tooltip: {},
        legend: {
          x: 'center', //水平居中
          y: 'bottom', //垂直居下
          data: ["高审减占比"]
        },
        // grid: {
        //   left: '3%',
        //   right: '4%',
        //   bottom: '3%',
        //   containLabel: true
        // },
        xAxis: {
          type: 'category',
          //xAxisData转数组
          data: xAxisData,
          // axisTick: {
          //   alignWithLabel: true
          // },
          axisLabel: {
            interval: 0,//代表显示所有x轴标签显示
          }

        },
        yAxis: {},
        series: [
          {
            name: '高审减占比',
            type: "bar",
            data: seriesData,
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%'
            },
          },

        ]
      };
      myEchart.setOption(option);
    },
    async drawChart4() {

      var startDate = new Date(this.searchForm.startDate)
      //获取时间年
      var year = startDate.getFullYear();
      //year 减一年
      var lastYear = year - 1
      var tableData = this.$refs.table.tableData[2];
      var xAxisData = [lastYear, year]
      var seriesData = []
      console.log('与上一年的平均审减率',tableData)
      seriesData.push(tableData.cityAverageLastYear)
      seriesData.push(tableData.cityAverage)
      //初始化echarts
      var chartDom = document.getElementById('main4')
      let myEchart = echarts.init(chartDom)
      let option = {
        title: {
          text: "与上一年的平均审减率",
          textAlign: 'auto',//整体（包括 text 和 subtext）的水平对齐
          textVerticalAlign: 'auto',//整体（包括 text 和 subtext）的垂直对齐
        },
        tooltip: {},
        legend: {
          x: 'center', //水平居中
          y: 'bottom', //垂直居下
          data: ["平均审减率"]
        },
        // grid: {
        //   left: '3%',
        //   right: '4%',
        //   bottom: '3%',
        //   containLabel: true
        // },
        xAxis: {
          type: 'category',
          //xAxisData转数组
          data: xAxisData,
          // axisTick: {
          //   alignWithLabel: true
          // },
          axisLabel: {
            interval: 0,//代表显示所有x轴标签显示
          }

        },
        yAxis: {},
        series: [
          {
            name: '平均审减率',
            type: "bar",
            data: seriesData,
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%'
            },
          },

        ]
      };
      myEchart.setOption(option);
    },
  },
  // watch: {
  //   //监听表格数据变化，重新绘制图表
  //   '$refs.table.tableData': function (newVal, oldVal) {
  //     console.log('表格数据变化', newVal, oldVal)
  //     this.drawChart()
  //   }
  // }
}
</script>
<style scoped>
.container {
  display: flex;
  margin-top: 10px;
}
</style>
