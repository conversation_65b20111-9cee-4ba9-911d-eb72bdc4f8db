/**
*  @author: ye<PERSON><PERSON>
* @date: 2023-01-13
* @description:结算审减率报表配置管理
* TODO: 1. 获取配置访问了三次接口，可以修改为一次获取三个配置
*/
<template>
  <div>
    <mssCard title="统计审减率配置">
      <div slot="headerBtn">
        <el-button v-if="powerData.personnel_allocation_save" type="primary" @click="saveAppTypeTableInfo">保存
        </el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="appTypeTable"
          :selection="false"
          :api="api"
          :columns="appTypeColumns"
          :stationary="appTypeStationary"
          :staticSearchParam="appTypeStaticSearchParam"
          border
          :pagination="false"
        ></mssTable>
      </div>
    </mssCard>


    <mssCard title="统计结算审减率数据人员配置">
      <div slot="headerBtn">
        <el-button v-if="powerData.personnel_allocation_save" type="primary" @click="saveTableInfo">保存</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :selection="false"
          :api="api"
          :columns="columns"
          :stationary="stationary"
          :staticSearchParam="staticSearchParam"
          border
          :pagination="false"
        ></mssTable>
      </div>
    </mssCard>

    <mssCard title="结算审减率预警配置">
      <div slot="headerBtn">
        <el-button v-if="powerData.personnel_allocation_save" type="primary" @click="saveAlertTableInfo">保存</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="alertTable"
          :selection="false"
          :api="api"
          :columns="alertColumns"
          :stationary="alertStationary"
          :staticSearchParam="alertStaticSearchParam"
          border
          :pagination="false"
        ></mssTable>
      </div>
    </mssCard>


    <mssChooseUser ref="chooseUser" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseUser>
  </div>
</template>

<script>
import {listReportSelectConfig, updateReportSelectConfig} from "@/api/pccw/report_select/reportSelectConfig";

export default {
  name: 'PersonnelAllocation',
  data() {
    return {
      powerData: [],
      api: listReportSelectConfig,
      type: '',
      multSelect: false,
      stationary: [],
      appTypeStationary: [],
      alertStationary: [],
      roleRow: {}, // 某行数据
      staticSearchParam: {
        code: 'settlement_reduction_rate_config'
      },
      appTypeStaticSearchParam: {
        code: 'settlement_reduction_rate_applicationType_config'
      },
      alertStaticSearchParam: {
        code: 'settlement_reduction_rate_alert_config'
      }
    }
  },
  computed: {
    appTypeColumns() {
      return [
        {
          label: '专业名称',
          prop: 'appTypeName'
        },
        {
          label: '运算符',
          prop: 'logicalJudge'
        },
        {
          label: '审减率配置（%）',
          prop: 'reductionRate',
          formatter: (row) => {
            return <el-input v-model={row.reductionRate} ></el-input>
          }
        },
      ]
    },
    columns() {
      return [
        {
          label: '角色名称',
          prop: 'roleName'
        },
        {
          label: '处理人账号',
          prop: 'userNames',
          formatter: (row) => {
            return <el-input v-model={row.userNames} readonly onFocus={() => {
              this.openChooseUserDailog(row)
            }}></el-input>
          }
        },
      ]
    },
    alertColumns() {
      return [
        {
          label: '专业名称',
          prop: 'appTypeName'
        },
        {
          label: '预警级别',
          prop: 'alertType'
        },
        {
          label: '运算符',
          prop: 'logicalJudge'
        },
        {
          label: '审减率配置（%）',
          prop: 'reductionRate',
          formatter: (row) => {
            return <el-input v-model={row.reductionRate} ></el-input>
          }
        },
      ]
    },
  },
  created() {
    this.getPower()
  },
  methods: {
    getPower() {
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item) => {
        this.powerData[item.authority] = true
      })
    },
    // 修改配置人员
    async action(row) {
      if (row.userIds) {
        // 保存
        const obj = {
          roleId: row.roleId,
          userId: row.userIds
        }
        const res = await this.saveServiceFn(obj)
        if (res) {
          this.$message.success("保存成功！")
          this.$refs.table.getTableData()
        }
      } else {
        this.$message.warning('请选择人员')
      }
    },
    openChooseUserDailog(row) {
      this.roleRow = row
      const item = {
        excuterNames: row.userNames,
        excuterIds: row.userIds
      }
      if (row.roleName == '市公司工程建设安全管理员') {
        this.multSelect = false
      } else {
        this.multSelect = true
      }
      const deptParams = {deptIds: sessionStorage.getItem('firstDeptId')}
      this.$refs.chooseUser.init(item, deptParams)
    },
    showCheckList({checkList}) {
      const list = checkList
      this.specailty = list
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.realName)
        ids.push(item.userId)
      })
      const data = this.$refs.table.tableData
      this.stationary = data.map((item, index) => {
        if (item.roleId === this.roleRow.roleId) {
          return {...item, userNames: names.join(','), userIds: ids.join(',')}
        } else {
          return item
        }
      })
    },
    // 需求文档上是批量保存
    async saveTableInfo() {
      const data = this.$refs.table.tableData
      // 请求队列
      let flag = true
      console.log('保存数据', data)
      try {
        if (data[0].userIds) {
          //data 转 json
          let dataStr = JSON.stringify(data)
          let params = {
            id: "1",
            code: 'settlement_reduction_rate_config',
            configContent: dataStr
          }
          console.log('params', JSON.stringify(params))
          await this.saveServiceFn(params)
        } else {
          flag = false
          throw new Error('请选择人员')
        }
      } catch (error) {
        this.$message.warning(error.message)
      }
      if (flag) {
        // 所有请求都已完成，直接返回或处理 res
        // await Promise.all(list)
        this.$message.success("保存成功！")
        this.$refs.table.getTableData()
      }
    },
    async saveAppTypeTableInfo() {
      const data = this.$refs.appTypeTable.tableData
      // 请求队列
      let flag = true
      console.log('保存数据', data)
      try {
        //reductionRate只能是数字，
        data.forEach(item => {
          if (item.reductionRate) {
            if (!/^\d+(\.\d+)?$/.test(item.reductionRate)) {
              flag = false
              throw new Error('审减率只能是数字')
            }
          } else {
            flag = false
            throw new Error('请输入审减率')
          }
        })
        if (flag) {
          //data 转 json
          let dataStr = JSON.stringify(data)
          let params = {
            id: "3",
            code: 'settlement_reduction_rate_applicationType_config',
            configContent: dataStr
          }
          console.log('params', JSON.stringify(params))
          await this.saveServiceFn(params)
        }
      } catch (error) {
        this.$message.warning(error.message)
      }
      if (flag) {
        // 所有请求都已完成，直接返回或处理 res
        // await Promise.all(list)
        this.$message.success("保存成功！")
        this.$refs.table.getTableData()
        this.$refs.appTypeTable.getTableData()
      }
    },
    async saveAlertTableInfo() {
      const data = this.$refs.alertTable.tableData
      // 请求队列
      let flag = true
      console.log('保存数据', data)
      try {
        //reductionRate只能是数字，
        data.forEach(item => {
          if (item.reductionRate) {
            if (!/^\d+(\.\d+)?$/.test(item.reductionRate)) {
              flag = false
              throw new Error('审减率只能是数字')
            }
          } else {
            flag = false
            throw new Error('请输入审减率')
          }
        })
        if (flag) {
          //data 转 json
          let dataStr = JSON.stringify(data)
          let params = {
            id: "4",
            code: 'settlement_reduction_rate_alert_config',
            configContent: dataStr
          }
          console.log('params', JSON.stringify(params))
          await this.saveServiceFn(params)
        }
      } catch (error) {
        this.$message.warning(error.message)
      }
      if (flag) {
        // 所有请求都已完成，直接返回或处理 res
        // await Promise.all(list)
        this.$message.success("保存成功！")
        this.$refs.table.getTableData()
        this.$refs.appTypeTable.getTableData()
      }
    },
    async saveServiceFn(obj) {
      let flag = false
      await updateReportSelectConfig(obj).then(res => {
        if (res.code === '0000') {
          flag = true
        } else {
          flag = false
        }
      })
      return flag
    }
  }
}
</script>
