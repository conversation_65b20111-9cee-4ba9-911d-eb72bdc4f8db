<template>
  <div class="app-container">
    <mssCard title="订单接收入账角色管理配置">
      <div slot="headerBtn">
       <el-button @click="addHandle" type="primary">新增</el-button>
      </div>
      <div slot="content">
        <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam"
         border selection v-if="isReload">
        </mssTable>
      </div>
    </mssCard>

  </div>
</template>

<script>
  import {
    getUserConfigList,deleteConfig
  } from "@/api/pccw/report_select/purchase_order.js";

  export default {
    components: {

    },
    data() {
      return {
        isReload: true,
        // 默认表格搜索条件
        staticSearchParam: {

        },
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [
          {
            label: '公司段',
            type: 'input',
            fieldName: 'companyName'
          },
        ],
        // 查询条件
        queryCondition: {

        },
        procInstId: '',
        // 表头
        tableHeader: [
          {
            prop: "type",
            label: "角色名称",
            align: "center",
            tooltip: true,
formatter: (row) => {
            if (row.type == 1) {
              return <span>工程管理经理</span>;
            } else if (row.type == 2) {
              return <span>建设单位</span>;
            } else if (row.type == 3) {
              return <span>超级管理员</span>;
            } else {
              return <span></span>;
            }
          },
          },
          {
            prop: "userAccount",
            label: "用户账号",
            align: "center",
            tooltip: true,
          },
          {
            prop: "context",
            label: "用户名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "unit",
            label: "建设单位",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "createBy",
            label: "操作人",
            align: "center",
            tooltip: true,
          },
          {
            prop: "createDate",
            label: "添加时间",
            align: "center",
            tooltip: true,
            width: 150
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '60px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.delRow(row)
                    }
                  } >
                  删除 <
                  /span>
                } <
                /span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: getUserConfigList,
      };
    },
    created() {

    },
    methods: {
      async delRow(row){
        const res=await deleteConfig(row)
        if(res.code==="0000"){
          console.log("删除成功333")
          // 刷新页面
                this.isReload = false;
                await this.$nextTick();
                this.isReload = true;
        }
      },
    //新增
    addHandle() {
      this.$router.push({
        path: '/pccw_menu/report_select/purchase_order/add',
        // query: {
        //   storageTempProcType: 'ADD',
        //   operateType: 'edit',
        //   pathlabel: encodeURIComponent(`暂存点工单处理页`)
        // }
      })
    },

    }
  };
</script>


<style lang="scss" scoped>

</style>
