<template>
  <div>
    <div class="operate-btn">
      <el-button type="primary" :disabled="isSaving" @click="toSave">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssCard title="新增">
      <div slot="content" class="common-form">
        <el-form :model="form" label-width="120px" :label-position="labelPosition">
          <el-form-item label="建设单位">
            <el-input v-model="form.context"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </mssCard>
    <select-user ref="selectUserDialog" @row-selected="handleRowSelected" />
  </div>

</template>

<script>
  import SelectUser from './SelectUser.vue';
  import {
    insertConfig
  } from "@/api/pccw/report_select/purchase_order.js";
  export default {
    components: {
      SelectUser
    },
    data() {
      return {
        isSaving: false, // 标记保存操作是否进行中
        labelPosition: 'right',
        dialogVisible: false,
        form: {
          type: '2', // 角色名称对应的类型
          id: '',
          context: null,
          unit: '',
          userAccount: '',
          userId: '',
        },
      };
    },
    methods: {
      toSave() {
        if (!this.form.context) {
          this.$message.error('还未填写建设单位')
          return
        }
        if (this.isSaving) {
          console.log("还在保存中");
          return; // 如果保存操作正在进行中，则不执行保存操作
        }
        this.submitData()
      },
      async submitData() {
        const res = await insertConfig(this.form)
        if (res.code === "0000") {
          this.$message.success('新增成功')
          this.$router.push({
            path: '/pccw_menu/report_select/purchase_order/unit',
          })
        }
        this.isSaving=true
      },
      goBack() {
        this.$router.go(-1); // 返回上一个路由
      },
      toSelectUser() {
        this.$refs.selectUserDialog.selectUserDialog = true;
      },
      // 处理子组件传值
      handleRowSelected(row) {
        this.form.context = row.context
        this.form.unit = row.unit
        this.form.userAccount = row.userAccount
        this.form.userId = row.userId
        // console.log("子组件数据", row);
      },
      openDialog() {
        this.dialogVisible = true;
      },
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-form-item__label {
    padding-right: 12px;
  }
</style>
