<template>
    <el-dialog title="选择用户" :visible.sync="selectUserDialog" class="dialog" :modal="false">
    <mssSearchForm v-show="showSearch" ref="searchForm"  :search-config="queryParams" labelWidth='90px'
      @reset="resetQuery" @search="handleQuery"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="queryUser"
         border :serial="false">
        </mssTable>
      </div>
    </mssCard>
  </div>
  </el-dialog>
</template>

<script>
  import {
    getUserList
  } from "@/api/pccw/report_select/purchase_order.js";

  export default {
    data() {
      return {
        selectUserDialog: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [
          {
            label: '用户账号',
            type: 'input',
            fieldName: 'userAccount'
          },
          {
            label: '用户名称',
            type: 'input',
            fieldName: 'context'
          }
        ],
        // 查询条件
        queryUser: {
          nickName:'',
        },
        // 表头
        tableHeader: [
          {
            prop: "userAccount",
            label: "用户账号",
            align: "center",
            tooltip: true
          },
          {
            prop: "context",
            label: "姓名",
            align: "center",
            tooltip: true
          },
          {
            prop: "unit",
            label: "建设单位",
            align: "center",
            tooltip: true
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '50px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.selectRow(row)
                    }
                  } >
                  选择 <
                  /span>
                } <
                /span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: getUserList,
      };
    },
    created() {},
    methods: {
      // 选择当行
      selectRow(row) {
        this.selectUserDialog=false
        this.$emit('row-selected', row);// 传值父组件
      },
      /** 搜索按钮操作 */
      handleQuery(form) {
        // console.log("form", form);
        this.queryUser.nickName = form.nickName
        this.$refs.table.page.current = 1
        this.queryUser = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      },
    }
  };
</script>


<style lang="scss" scoped>
  ::v-deep .content{
    .el-row{
      width:120% !important;
    }
  }

  .table_cell_click {
    cursor: pointer;
    color: #02a7f0;

    .el-icon-data-analysis {
      font-size: 14px;
    }
  }
</style>
