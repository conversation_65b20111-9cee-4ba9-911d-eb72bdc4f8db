<template>
  <div class="app-container">
    <mssSearchForm labelWidth='190px' title="未完成接收的费用采购订单 - 查询条件" v-show="showSearch" ref="searchForm" :form="staticSearchParam"
      :search-config="queryParams" @reset="resetQuery" @search="handleQuery"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="downloadData">导出</el-button>
      </div>
      <div slot="content">
        <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam"
          :customSize="queryOwn.size" border selection :autoCall="true">
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import qs from 'qs';
  import {
    getAll,
    getLastTime,
    exportTotal,
    exportDetail
  } from "@/api/pccw/comprehensive/purchase_order/index.js";

  export default {
    components: {

    },
    data() {
      return {
        // 默认表格搜索条件
        staticSearchParam: {
          // warningCalculationDate: '',
        },
        showTable: false,
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [{
            label: '订单编号',
            type: 'input',
            fieldName: 'purchaseOrder'
          },
          {
            label: '订单说明',
            type: 'input',
            fieldName: 'orderDescription'
          },
          {
            label: '项目编码',
            type: 'input',
            fieldName: 'projectNumber'
          },
          {
            label: '项目名称',
            type: 'input',
            fieldName: 'projectName'
          },
          {
            label: '公司/组织',
            type: 'input',
            fieldName: 'businessEntity'
          },
          {
            label: '所在部门',
            type: 'input',
            fieldName: 'department'
          },
          {
            label: '供应商名称',
            type: 'input',
            fieldName: 'supplierName'
          },
          {
            label: '工程管理经理或实施经理（主)',
            type: 'input',
            fieldName: 'manager'
          }
        ],
        procInstId: '',
        // 查询条件
        queryOwn: {
          // total: '',
          // workType: '',
          current: 1,
          size: 5,
        },
        // 表头
        tableHeader: [{
            prop: "businessEntity",
            label: "公司∕组织",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "department",
            label: "所在部门",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "purchaseOrder",
            label: "订单编号",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "orderDescription",
            label: "订单说明",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "supplierNumber",
            label: "供应商编号",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "supplierName",
            label: "供应商名称",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "projectNumber",
            label: "项目编号",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "taskNumber",
            label: "任务编号",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "allotmentAmount",
            label: "订单分配总额",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "receivedAmount",
            label: "订单接收总额",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "percentageOfReception",
            label: "订单接收百分比",
            align: "center",
            tooltip: true,
            width: 120,
            formatter: (val) => {
              let label = ''
              if (val.percentageOfReception !== null && val.percentageOfReception !== undefined) {
                label = val.percentageOfReception + '%'
              } else {
                label = '0%';
              }
              return label
            }
          },
          {
            prop: "approveStatus",
            label: "订单审批状态",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "warningCalculationDate",
            label: "结算审计完成时间",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "managementManager",
            label: "工程管理经理",
            align: "center",
            tooltip: true,
            width: 120
          },
          {
            prop: "implementationManager",
            label: "工程实施经理(主)",
            align: "center",
            tooltip: true,
            width: 120
          }
        ],
        // 表格数据API
        tableApi: getAll,
        warningCalculationDate: ''
      };
    },
    created() {
      // this.getLastTime()
    },
    methods: {
      async getLastTime() {
        let res = await getLastTime()
        if (res.code === "0000") {
          this.staticSearchParam.dispatchTime = res.data.warningCalculationDate
          this.$refs.table.getTableData()
        }
      },
      async downloadData() {
        // let res = await exportTotal(qs.stringify(this.staticSearchParam))
        let res = await exportTotal("limit=-1")
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', '未完成接收费用订单列表.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
      /** 搜索按钮操作 */
      handleQuery(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
      },
      /** 重置按钮操作 */
      resetQuery(form) {
        this.handleQuery(form)
      },
    }
  };
</script>


<style lang="scss" scoped>

</style>
