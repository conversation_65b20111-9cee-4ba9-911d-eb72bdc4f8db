<template>
  <div class="app-container">
    <mssCard title="订单接收入账省本部建设单位配置">
      <div slot="headerBtn">
        <el-button @click="addHandle" type="primary">新增</el-button>
      </div>
      <div slot="content">
        <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam" border
          selection v-if="isReload">
        </mssTable>
      </div>
    </mssCard>

  </div>
</template>

<script>
  import {
    getUnitConfigList,
    deleteConfig
  } from "@/api/pccw/report_select/purchase_order.js";

  export default {
    components: {

    },
    data() {
      return {
        isReload: true,
        // 默认表格搜索条件
        staticSearchParam: {

        },
        dialogXML: false,
        // 显示搜索条件
        showSearch: true,
        // 查询参数
        queryParams: [{
          label: '公司段',
          type: 'input',
          fieldName: 'companyName'
        }, ],
        // 查询条件
        queryCondition: {

        },
        procInstId: '',
        // 表头
        tableHeader: [{
            prop: "context",
            label: "建设单位",
            align: "center",
            tooltip: true,
          },
          {
            prop: "createDate",
            label: "添加时间",
            align: "center",
            tooltip: true,
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '60px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.delRow(row)
                    }
                  } >
                  删除 <
                  /span>
                } <
                /span>
              )
            }
          }
        ],
        // 表格数据API
        tableApi: getUnitConfigList,
      };
    },
    created() {

    },
    methods: {
      async delRow(row) {
        const res = await deleteConfig(row)
        if (res.code === "0000") {
          // 刷新页面
          this.isReload = false;
          await this.$nextTick();
          this.isReload = true;
        }
      },
      //新增
      addHandle() {
        this.$router.push({
          path: '/pccw_menu/report_select/purchase_order/unit/add',
        })
      },

    }
  };
</script>


<style lang="scss" scoped>

</style>
