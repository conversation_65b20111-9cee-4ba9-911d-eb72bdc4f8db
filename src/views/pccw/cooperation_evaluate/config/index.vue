/**
* @author: yewen<PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理
*/
<template>
  <div>
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="建设单位合作单位考核接口人配置" name="constructionNucleusConfig" lazy>
        <constructionNucleusConfig ref="constructionNucleusConfig"></constructionNucleusConfig>
      </el-tab-pane>
      <el-tab-pane label="区县超时待办失效时长设置" name="countyTimeoutConfig" lazy>
        <countyTimeoutConfig ref="countyTimeoutConfig"></countyTimeoutConfig>
      </el-tab-pane>
      <el-tab-pane label="建设单位合作单位管理员、维护部门评分人配置" name="constructionAdminAndMaintenanceScoreConfig"
                   lazy>
        <constructionAdminAndMaintenanceScoreConfig
          ref="constructionAdminAndMaintenanceScoreConfig"></constructionAdminAndMaintenanceScoreConfig>
      </el-tab-pane>
      <el-tab-pane label="省公司合作单位管理员配置" name="provinceAdminConfig" lazy>
        <provinceAdminConfig ref="provinceAdminConfig"></provinceAdminConfig>
      </el-tab-pane>
      <el-tab-pane label="区县反馈人配置页面" name="countyFeedbackConfig" lazy>
        <countyFeedbackConfig ref="countyFeedbackConfig"></countyFeedbackConfig>
      </el-tab-pane>
      <el-tab-pane label="项目任务所在阶段考核表模板配置" name="projectTaskStageAssessment" lazy>
        <projectTaskStageAssessment ref="projectTaskStageAssessment"></projectTaskStageAssessment>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import constructionNucleusConfig from "@/views/pccw/cooperation_evaluate/config/constructionNucleusConfig.vue";
import countyTimeoutConfig from "@/views/pccw/cooperation_evaluate/config/countyTimeoutConfig.vue";
import constructionAdminAndMaintenanceScoreConfig
  from "@/views/pccw/cooperation_evaluate/config/constructionAdminAndMaintenanceScoreConfig.vue";
import provinceAdminConfig from "@/views/pccw/cooperation_evaluate/config/provinceAdminConfig.vue";
import countyFeedbackConfig from "@/views/pccw/cooperation_evaluate/config/countyFeedbackConfig.vue";
import projectTaskStageAssessment from "@/views/pccw/cooperation_evaluate/config/projectTaskStageAssessment.vue";


export default {
  name: 'CooperationEvaluateConfig',
  components: {
    constructionNucleusConfig,
    countyTimeoutConfig,
    constructionAdminAndMaintenanceScoreConfig,
    provinceAdminConfig,
    countyFeedbackConfig,
    projectTaskStageAssessment
  },
  data() {
    return {
      //默认选中 tab
      activeName: 'constructionNucleusConfig',
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleClick() {
    },
  }
}
</script>
