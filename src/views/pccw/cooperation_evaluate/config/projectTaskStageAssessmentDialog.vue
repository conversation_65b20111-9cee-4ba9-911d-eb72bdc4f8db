/**
* @author: ye<PERSON><PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理-项目任务所在阶段考核表模板配置
*/
<template>
  <div>
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <mssCard :title="titleName">
        <div slot="headerBtn">
          <el-button type="primary" v-show="!isEditVisible" @click="handleUpdate">编辑</el-button>
          <el-button type="primary" v-show="isEditVisible" @click="handleAdd">新增考核内容</el-button>
          <el-button type="primary" v-show="isEditVisible" @click="handleSave">保存</el-button>
        </div>
        <div slot="content" class="common-form">
          <div v-show="!isEditVisible">
            <!--查看表格 -->
            <el-table
              :key="Math.random()"
              :data="tableList"
              class="table"
              row-key="id"
              :header-cell-style="rowClass"
              :span-method="handleSpanMethod"
              show-summary
              :summary-method="getSummaries"
              default-expand-all
              border>
              <el-table-column prop="assessmentProject" label="考核项目" width="80" align="center"></el-table-column>
              <!--            <el-table-column prop="serialNumber" label="序号" width="60" align="center">-->
              <!--              <template scope="scope">-->
              <!--                <div>{{ scope.row.serialNumber }}</div>-->
              <!--              </template>-->
              <!--            </el-table-column>-->
              <el-table-column label="考核内容" align="center">
                <el-table-column label="考核内容" align="center" width="120px">
                  <template scope="scope">
                    <div>{{ scope.row.assessmentContext }}</div>
                    <span>({{ scope.row.assessmentContextScore }}分)</span>
                  </template>
                </el-table-column>
                <el-table-column label="考核内容类型" prop="assessmentContextDetails" align="center" width="120px">
                  <template scope="scope">
                    <div>{{ scope.row.assessmentContextDetails }}</div>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column prop="standard" align="center" label="标准及要求"></el-table-column>
              <el-table-column prop="demerit" label="扣分规则"></el-table-column>
              <el-table-column label="扣分（分/次）" align="center" width="100px">
                <template scope="scope">
                  <div>{{ scope.row.deductPointsScore }}分/{{ scope.row.deductPointsNumber }}次</div>
                </template>
              </el-table-column>
              <el-table-column label="分值" prop="standardScore" align="center" width="80px">
                <template scope="scope">
                  <div v-show="scope.row.rowNumber !== 0">
                    {{ scope.row.standardScore }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-show="isEditVisible">
            <!--编辑表格 -->
            <el-table
              :data="tableList"
              class="table"
              row-key="id"
              :span-method="handleSpanMethod"
              show-summary
              :summary-method="getSummaries"
              default-expand-all
              border>
              <el-table-column prop="assessmentProject" label="考核项目" width="80px" align="center"></el-table-column>
              <!--            <el-table-column prop="serialNumber" label="序号" width="60" align="center">-->
              <!--              <template scope="scope">-->
              <!--                <div>{{ scope.row.serialNumber }}</div>-->
              <!--              </template>-->
              <!--            </el-table-column>-->
              <el-table-column label="考核内容" align="center" width="120px">
                <el-table-column label="考核内容" align="center" width="120px">
                  <template scope="scope">
                    <div>{{ scope.row.assessmentContext }}</div>
                    <span>({{ scope.row.assessmentContextScore }}分)</span>
                  </template>
                </el-table-column>
                <el-table-column label="考核内容类型" prop="assessmentContextDetails" align="center" width="120px">
                  <template scope="scope">
                    <div>{{ scope.row.assessmentContextDetails }}</div>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column prop="standard" align="center" label="标准及要求"></el-table-column>
              <el-table-column prop="demerit" label="扣分规则"></el-table-column>
              <el-table-column label="扣分（分/次）" align="center" width="80px">
                <template scope="scope">
                  <div>{{ scope.row.deductPointsScore }}分/{{ scope.row.deductPointsNumber }}次</div>
                </template>
              </el-table-column>
              <el-table-column label="分值" prop="standardScore" align="center" width="200px">
                <template scope="scope">
                  <div v-show="scope.row.rowNumber !== 0">
                    <el-input v-model.number="scope.row.standardScore"
                              placeholder="请输入分值"></el-input>
                    <span v-if=" scope.$index !== tableList.length - 1  && (scope.$index + scope.row.rowNumber < tableList.length)
                  && tableList[scope.$index + scope.row.rowNumber].rowNumber === 1">
                  <el-button type="text" @click="mergeDownCells(scope.$index)">向下合并</el-button>
                </span>
                    <span v-if="scope.row.rowNumber && scope.row.rowNumber > 1">
                  <el-button type="text" @click="splitCells(scope.$index)">拆分</el-button>
                </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="sort" align="center" width="60" label="排序">
              </el-table-column>
              <el-table-column label="操作" align="center" fixed="right">
                <template scope="scope">
                <span className="mr10">
                  <el-button type="text" @click="handleUpdateForm(scope.$index)">编辑</el-button>
                </span>
                  <span v-if="scope.$index !==0">
                    <el-button type="text" @click="rowDrop('up', scope.row, scope.$index)">上移</el-button>
                </span>
                  <span v-if="tableList.length - 1 !== scope.$index ">
                    <el-button type="text" @click="rowDrop('down', scope.row, scope.$index)">下移</el-button>
                </span>
                  <span className="mr10">
                  <el-button type="text" @click="handleDelete(scope.$index)">删除</el-button>
                </span>
                </template>
              </el-table-column>
            </el-table>
            <!--新增考核内容-->
            <el-dialog
              title="新增考核内容"
              :visible.sync="dialogVisible"
              :close-on-click-modal="false"
              width="35%"
              :before-close="closeDialog">
              <el-form :model="assessmentForm" ref="assessmentForm" v-if="dialogVisible" label-position="right"
                       label-width="160px">
                <el-form-item label="考核内容：" label-width="160px" prop="assessmentContext"
                              :rules="[ { required: true, message: '考核内容不能为空',trigger: ['blur', 'change']}]">
                  <el-select @change="handleSelectChange"
                             v-model="assessmentForm.assessmentContext"
                             filterable
                             allow-create
                             default-first-option
                             style="margin-left: 20px;width: 80%"
                             placeholder="请选择考核内容">
                    <el-option
                      v-for="item in assessmentContextOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>

                </el-form-item>
                <el-form-item label="考核内容总分值：" style="margin-top: 30px"
                              prop="assessmentContextScore" label-width="160px"
                              :rules="[
                { required: true, message: '总分值不能为空',trigger: 'blur'},
                { type: 'number', message: '总分值必须为数字值', trigger: ['blur', 'change']}
               ]">
                  <el-input v-model.number="assessmentForm.assessmentContextScore" autocomplete="off"
                            placeholder="请输入考核内容总分值" style="margin-left: 20px;width: 80%"></el-input>
                </el-form-item>
                <el-form-item label="考核内容类型：" style="margin-top: 30px" prop="assessmentContextDetails"
                              label-width="160px">
                  <el-select v-model="assessmentForm.assessmentContextDetails"
                             filterable
                             allow-create
                             default-first-option
                             style="margin-left: 20px;width: 80%"
                             placeholder="请选择考核内容类型">
                    <el-option
                      v-for="item in assessmentContextDetailsOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="标准及要求：" style="margin-top: 30px"
                              prop="standard" label-width="160px"
                              :rules="[ { required: true, message: '标准及要求不能为空',trigger: ['blur', 'change']}]">
                  <el-input v-model="assessmentForm.standard" autocomplete="off" placeholder="请输入标准及要求"
                            style="margin-left: 20px;width: 80%"></el-input>
                </el-form-item>
                <el-form-item label="扣分规则（分/次）：" required style="margin-top: 30px" label-width="160px">
                  <div style="display: flex; align-items: center;">
                    <el-form-item prop="deductPointsScore"
                                  style="margin-left: 20px; margin-right: 5px; width: 38%"
                                  :rules="[
                { required: true, message: '扣分分数不能为空', trigger: 'blur' },
                { pattern: /^(\d+)?(\.\d{1,2})?$/, message: '扣分分数必须为数字值且最多保留两位小数', trigger: ['blur', 'change'] }
              ]">
                      <el-input placeholder="请输入扣分分数" v-model.number="assessmentForm.deductPointsScore"
                                type="number">分
                      </el-input>
                    </el-form-item>
                    <span>/</span>
                    <el-form-item prop="deductPointsNumber" style="margin-left: 5px; width: 38%" :rules="[
                { required: true, message: '扣分次数不能为空',trigger: 'blur'},
                { type: 'number', message: '扣分次数必须为数字值', trigger: ['blur', 'change']}
               ]">
                      <el-input placeholder="请输入扣分次数"
                                v-model.number="assessmentForm.deductPointsNumber"></el-input>
                    </el-form-item>
                  </div>
                </el-form-item>
                <el-form-item label="扣分规则说明：" style="margin-top: 30px"
                              prop="demerit" label-width="160px"
                              :rules="[ { required: true, message: '扣分规则不能为空',trigger: ['blur', 'change']}]">
                  <el-input type="textarea" v-model="assessmentForm.demerit" autocomplete="off"
                            style="margin-left: 20px;width: 80%"></el-input>
                </el-form-item>

              </el-form>
              <span slot="footer" class="dialog-footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="dialogAddData">确 定</el-button>
            </span>

            </el-dialog>
          </div>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {
  checkPermissionCooperationConfig,
  editProjectTaskAssessmentConfig,
  editSuperviseIssueTypeConfig, selectProjectTaskAssessmentConfigList
} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";
import item from "@/layout/components/Sidebar/Item.vue";
import it from "element-ui/src/locale/lang/it";


export default {
  name: 'projectTaskStageAssessmentDialog',
  components: {},
  props: {
    titleName: {
      type: String,
      default: () => ''
    },
    //考核指标配置类型
    assessmentType: {
      type: String,
      default: () => ''
    },

  },
  data() {
    return {
      //判断用户是否有权限配置
      isShow: false,
      //是否编辑
      isEditVisible: false,

      //表格数据
      tableList: [],
      //分值单元格合并行数数组
      standardScoreRowspanArr: [],
      //考核内容单元格合并行数数组
      assessmentContextRowspanArr: [],
      //考核内容类型单元格合并行数数组
      assessmentContextDetailsRowspanArr: [],
      //标准及要求合并行数数组
      standardRowspanArr: [],
      //新增考核内容弹窗
      dialogVisible: false,
      //考核项目对象
      assessmentForm: {
        assessmentContext: '',
        assessmentContextScore: null,
      },
      assessmentProject: '考评项目',
      //考核内容下拉框
      assessmentContextOptions: [],
      //考核内容对应的分数
      assessmentContextKey: [],
      //考核内容类型下拉框
      assessmentContextDetailsOptions: [],
      //是否显示考核内容类型，在查看页面全部为空时不显示
      isShowAssessmentContextDetails: true,
    }
  },
  created() {
    //判断是否用户是否有权限查看
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      checkPermissionCooperationConfig().then((res) => {
        this.isShow = res.data
        if (true === this.isShow) {
          this.selectTableList("select")
        }
      })
    } else {
      this.isShow = true
      if (true === this.isShow) {
        this.selectTableList("select")
      }
    }
  },
  mounted() {
  },
  computed: {},
  methods: {
    //查询表格数据
    selectTableList(stats) {
      let params = {
        assessmentType: this.assessmentType
      }
      selectProjectTaskAssessmentConfigList(params).then(res => {
        if (res.code === '0000') {
          this.tableList = res.data.data
          if (stats === "select") {
            this.isShowAssessmentContextDetails = false
            this.tableList.forEach(item => {
              if (item.assessmentContextDetails !== undefined && item.assessmentContextDetails) {
                this.isShowAssessmentContextDetails = true
              }
            })
          } else {
            this.isShowAssessmentContextDetails = true
          }
          this.formatRowspanAndColspan()
        }
      })
    },
    //合并表头
    rowClass({row, column, rowIndex, columnIndex}) {
      if (rowIndex === 1) {
        return {
          display: 'none'
        }
      }
    },
    //合并单元格
    handleSpanMethod({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0) {
        if (rowIndex > 0) {
          return {
            rowspan: 0,
            colspan: 0
          }
        } else {
          return {
            rowspan: this.tableList.length,
            colspan: 1
          }
        }
        // } else if (columnIndex === 2) {
      } else if (columnIndex === 1) {
        //考核内容
        if (this.isShowAssessmentContextDetails) {
          const _row = this.assessmentContextRowspanArr[rowIndex]
          const _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        } else {
          //不显示考核内容类型
          const _row = this.assessmentContextRowspanArr[rowIndex]
          const _col = _row > 0 ? 2 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        }
        // } else if (columnIndex === 3) {
      } else if (columnIndex === 2) {
        //考核内容类型
        if (this.isShowAssessmentContextDetails) {
          const _row = this.assessmentContextDetailsRowspanArr[rowIndex]
          const _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        } else {
          //不显示考核内容类型
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      } else if (columnIndex === 3) {
        //标准及要求
        const _row = this.standardRowspanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
        // } else if (columnIndex === 1 || columnIndex === 7) {
      } else if (columnIndex === 6) {
        //序列、分值
        const _row = this.standardScoreRowspanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }

    },
    /**
     * 合并单元格辅助
     */
    formatRowspanAndColspan() {
      this.assessmentContextRowspanArr = []
      this.assessmentContextDetailsRowspanArr = []
      this.standardRowspanArr = []
      this.standardScoreRowspanArr = []
      let assessmentContextIndexPos = null
      let assessmentContextDetailsIndexPos = null
      let standardIndexPos = null
      if (this.tableList && this.tableList.length > 0) {
        for (let i = 0; i < this.tableList.length; i++) {
          //处理 分值、序号所占行数
          this.standardScoreRowspanArr.push(this.tableList[i].rowNumber)
          if (i === 0) {
            this.assessmentContextRowspanArr.push(1)
            assessmentContextIndexPos = 0
            this.assessmentContextDetailsRowspanArr.push(1)
            assessmentContextDetailsIndexPos = 0
            this.standardRowspanArr.push(1)
            standardIndexPos = 0
          } else {
            // 判断当前元素与上一个元素是否相同(第1行和第2行)
            //考核内容
            if (this.tableList[i].assessmentContext && this.tableList[i - 1].assessmentContext &&
              this.tableList[i].assessmentContext === this.tableList[i - 1].assessmentContext) {
              this.assessmentContextRowspanArr.push(0)
              this.assessmentContextRowspanArr[assessmentContextIndexPos] += 1
            } else {
              this.assessmentContextRowspanArr.push(1)
              assessmentContextIndexPos = i
            }
            //考核内容类型
            if (this.tableList[i].assessmentContextDetails !== undefined && this.tableList[i].assessmentContextDetails &&
              this.tableList[i - 1].assessmentContextDetails !== undefined && this.tableList[i - 1].assessmentContextDetails &&
              this.tableList[i].assessmentContextDetails === this.tableList[i - 1].assessmentContextDetails) {
              this.assessmentContextDetailsRowspanArr.push(0)
              this.assessmentContextDetailsRowspanArr[assessmentContextDetailsIndexPos] += 1
            } else {
              this.assessmentContextDetailsRowspanArr.push(1)
              assessmentContextDetailsIndexPos = i
            }
            //标准及要求
            if (this.tableList[i].standard && this.tableList[i - 1].standard &&
              this.tableList[i].standard === this.tableList[i - 1].standard) {
              this.standardRowspanArr.push(0)
              this.standardRowspanArr[standardIndexPos] += 1
            } else {
              this.standardRowspanArr.push(1)
              standardIndexPos = i
            }
          }
        }
      }
    },
    //单元格向下合并
    mergeDownCells(index) {
      let mergeData = index + this.tableList[index].rowNumber
      if (this.tableList[mergeData].rowNumber > 1) {
        this.$message.error("无法合并单元格");
        return;
      }
      this.tableList[index].rowNumber += 1
      this.tableList[mergeData].rowNumber -= 1
      this.tableList[mergeData].serialNumber = this.tableList[index].serialNumber
      this.tableList[mergeData].standardScore = 0
      this.setSerialNumber()
      this.formatRowspanAndColspan()
    },
    //单元格拆分
    splitCells(index) {
      let indexNext = index + this.tableList[index].rowNumber - 1
      this.tableList[indexNext].rowNumber = 1
      this.tableList[indexNext].standardScore = null
      this.tableList[indexNext].serialNumber = this.tableList[index].serialNumber + 1
      this.tableList[index].rowNumber -= 1
      this.setSerialNumber()
      this.formatRowspanAndColspan()
    },
    //序号处理
    setSerialNumber() {
      let serialNumber = 0
      for (let i = 0; i < this.tableList.length; i++) {
        let rowNumber = this.tableList[i].rowNumber;
        if (rowNumber > 0) {
          serialNumber += 1
          this.tableList[i].serialNumber = serialNumber
        } else {
          this.tableList[i].serialNumber = serialNumber
        }
      }
    },
    //表格排序移动
    rowDrop(type, row, index) {
      //判断是否存在合并单元格
      if (row.rowNumber > 1 || row.rowNumber === 0) {
        this.$message.error("合并单元格无法移动");
        return;
      }
      if (type === 'up') {
        if (row.sort == 1) {
          return
        }
        if (this.tableList[index - 1].rowNumber > 1 || this.tableList[index - 1].rowNumber === 0) {
          this.$message.error("合并单元格无法移动");
          return;
        }
        this.tableList[index - 1].sort = this.tableList[index - 1].sort + 1
        this.tableList[index].sort = this.tableList[index].sort - 1
        var elem = this.tableList.splice(index, 1);
        this.tableList.splice(index - 1, 0, elem[0]);
      } else if (type === 'down') {
        if (row.sort == this.tableList.length) {
          return
        }
        if (this.tableList[index + 1].rowNumber > 1 || this.tableList[index + 1].rowNumber === 0) {
          this.$message.error("合并单元格无法移动");
          return;
        }
        this.tableList[index + 1].sort = this.tableList[index + 1].sort - 1
        this.tableList[index].sort = this.tableList[index].sort + 1
        var elem = this.tableList.splice(index, 1);
        this.tableList.splice(index + 1, 0, elem[0]);
      }
      this.setSerialNumber()
    },
    //合计
    getSummaries(param) {
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        } else if (index === 6) {
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);

          } else {
            sums[index] = 0;
          }
        }
      });
      return sums;
    },
    //删除按钮
    handleDelete(index) {
      if (this.tableList[index].rowNumber === 0) {
        for (var i = index - 1; i >= 0; i--) {
          if (this.tableList[i].rowNumber > 1) {
            this.tableList[i].rowNumber -= 1
            break
          }
        }
      } else if (this.tableList[index].rowNumber > 1) {
        this.tableList[index + 1].rowNumber = this.tableList[index].rowNumber - 1;
        this.tableList[index + 1].standardScore = this.tableList[index].standardScore;
      }
      this.tableList.splice(index, 1);
      for (var i = 0; i < this.tableList.length; i++) {
        this.tableList[i].sort = i + 1
      }
      this.setSerialNumber()
      this.formatRowspanAndColspan()
    },

    //设置考核内容下拉框
    setAssessmentContextOptions() {
      this.assessmentContextOptions = []
      this.assessmentContextDetailsOptions = []
      let tempOptions = []
      let tempAssessmentContextDetailsOptions = []
      this.tableList.forEach(item => {
        let assessmentContext = item.assessmentContext;
        let assessmentContextDetails = item.assessmentContextDetails;
        //判断数组是否存在，不存在就创建
        if (!tempOptions.includes(assessmentContext)) {
          tempOptions.push(assessmentContext)
          this.assessmentContextOptions.push({
            value: assessmentContext,
            label: assessmentContext
          })
          this.assessmentContextKey.push({
            key: assessmentContext,
            value: item.assessmentContextScore
          })
        }
        console.log("是否存在", assessmentContextDetails, (assessmentContextDetails !== null && assessmentContextDetails !== undefined))
        if (assessmentContextDetails !== null && assessmentContextDetails !== undefined) {
          console.log("是否在数组里", (!tempAssessmentContextDetailsOptions.includes(assessmentContextDetails)))
          if (!tempAssessmentContextDetailsOptions.includes(assessmentContextDetails)) {
            tempAssessmentContextDetailsOptions.push(assessmentContextDetails)
            this.assessmentContextDetailsOptions.push({
              value: assessmentContextDetails,
              label: assessmentContextDetails
            })
          }
        }
      })

    },

    //编辑按钮
    handleUpdate() {
      this.selectTableList("edit")
      this.isEditVisible = true
    },
    //保存按钮
    handleSave() {
      console.log('table', this.tableList)
      let assessmentContextScore = 0;
      let tempSum = 0;
      for (var i = 0; i < this.tableList.length; i++) {
        if (this.tableList[i].standardScore == null || this.tableList[i].standardScore === undefined) {
          this.$message.error("第" + (i + 1) + "行，分值 不能为空");
          return
        }
        if (i === 0) {
          assessmentContextScore = this.tableList[i].assessmentContextScore;
          tempSum = this.tableList[i].standardScore;
        } else {
          // 判断当前元素与上一个元素是否相同(第1行和第2行)
          //考核内容
          if (this.tableList[i].assessmentContext && this.tableList[i - 1].assessmentContext &&
            this.tableList[i].assessmentContext === this.tableList[i - 1].assessmentContext) {
            tempSum += this.tableList[i].standardScore;
          } else {
            assessmentContextScore = this.tableList[i].assessmentContextScore;
            tempSum = this.tableList[i].standardScore;
          }
        }
        if (!this.isScope(tempSum, assessmentContextScore)) {
          this.$message.error("第" + (i + 1) + "行，分值不能大于考核内容总分值");
          return;
        }
      }
      editProjectTaskAssessmentConfig(this.tableList).then(res => {
        if (res.code === '0000') {
          this.$message.success("保存成功");
          this.selectTableList("select")
          this.isEditVisible = false
        }
      })
    },
    //判断分值是否超过考核内容分值
    isScope(standardScore, assessmentContextScore) {
      if (standardScore > assessmentContextScore) {
        return false
      }
      return true
    },
    //新增考核内容按钮
    handleAdd() {
      this.setAssessmentContextOptions()
      this.dialogVisible = true
      // this.tableList = this.$refs.editTable.tableData
      // let sortNum = this.tableList ? this.tableList.length + 1 : 1
      // this.tableList.push({issueTache: '', issueBigType: '', issueSmallType: '', sort: sortNum})
      // console.log('tableList', this.tableList)
      // this.$refs.editTable.tableData = this.tableList
    },
    //编辑考核内容按钮
    handleUpdateForm(index) {
      this.assessmentForm = this.tableList[index]
      this.dialogVisible = true
    },
    //薪资考核内容-考核内容总分值设置
    handleSelectChange() {
      let assessmentContextScore
      if (this.assessmentContextOptions && this.assessmentContextOptions.length > 0) {
        // 检查选中的值是否存在于选项中
        const selectedItemExists = this.assessmentContextOptions.some(
          (item) => item.value === this.assessmentForm.assessmentContext
        );
        console.log('selectedItemExists：', selectedItemExists)
        // 根据选中的值来决定是否改变 el-input 的状态和赋值
        if (selectedItemExists) {
          for (let i = 0; i < this.assessmentContextKey.length; i++) {
            let item = this.assessmentContextKey[i]
            if (item.key === this.assessmentForm.assessmentContext) {
              assessmentContextScore = item.value
              this.$set(this.assessmentForm, 'assessmentContextScore', assessmentContextScore);
              return
            }
          }
        }
      }
    },
    //新增考核内容数据
    dialogAddData() {
      this.$refs['assessmentForm'].validate((valid) => {
        if (valid) {
          if (!this.assessmentForm.id) {
            let sortNum = this.tableList ? this.tableList.length + 1 : 1
            let serialNumber = 1
            if (this.tableList && this.tableList.length > 0) {
              serialNumber = this.tableList[this.tableList.length - 1].serialNumber + 1
            }
            let data = {
              id: this.tableList.length + 1 + Date.now(),
              rowNumber: 1,
              serialNumber: serialNumber,
              assessmentProject: this.assessmentProject,
              assessmentContext: this.assessmentForm.assessmentContext,
              assessmentContextScore: this.assessmentForm.assessmentContextScore,
              assessmentContextDetails: this.assessmentForm.assessmentContextDetails,
              standard: this.assessmentForm.standard,
              deductPointsScore: this.assessmentForm.deductPointsScore,
              deductPointsNumber: this.assessmentForm.deductPointsNumber,
              demerit: this.assessmentForm.demerit,
              assessmentType: this.assessmentType,
              standardScore: null,
              sort: sortNum,
            }
            this.tableList.push(data)
          }
          this.formatRowspanAndColspan()
          this.closeDialog()
        } else {
          return false;
        }
      });

    },
    //关闭新增考核内容弹窗
    closeDialog() {
      this.assessmentForm = {}
      this.dialogVisible = false
    },


  }
}
</script>
