/**
* @author: ye<PERSON><PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理-合作单位监督发现问题分类配置
*/
<template>
  <div class="app-container">
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <mssCard title="合作单位监督发现问题分类配置">
        <div slot="headerBtn">
          <el-button type="primary" v-show="!isEditVisible" @click="handleUpdate">编辑</el-button>
          <el-button type="primary" v-show="isEditVisible" @click="handleAdd">新增</el-button>
          <el-button type="primary" v-show="isEditVisible" @click="handleSave">保存</el-button>
        </div>
        <div slot="content" class="common-form">
          <div v-show="!isEditVisible">
            <el-table
              :data="tableList"
              :span-method="objectSpanMethod"
              class="table"
              border>
              <el-table-column
                prop="issueTache"
                label="问题发生环节"
                width="180"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="issueBigType"
                label="问题大类"
                width="300"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="issueSmallType"
                label="问题小类">
              </el-table-column>
            </el-table>
          </div>
          <div v-show="isEditVisible" class="editTableDiv">
            <mssTable
              row-key="id"
              ref="editTable"
              :api="tableApi"
              :columns="editTableHeader"
              :static-search-param="staticSearchParam"
              :serial="false"
              :autoCall="false"
              :pagination="false"
              border
            >
            </mssTable>
          </div>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {
  checkPermissionCooperationConfig,
  editSuperviseIssueTypeConfig,
  selectSuperviseIssueTypeConfigList
} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";

export default {
  name: 'superviseIssueTypeConfig',
  components: {},
  data() {
    return {
      //判断用户是否有权限配置
      isShow: false,
      //是否编辑
      isEditVisible: false,
      // 表格数据API
      tableApi: selectSuperviseIssueTypeConfigList,
      //编辑表头
      editTableHeader: [
        {
          prop: "issueTache",
          label: "问题发生环节",
          align: "center",
          tooltip: true,
          width: 200,
          formatter: (row) => {
            return (
              <el-select v-model={row.issueTache} placeholder="请选择问题发生环节">
                <el-option label="设计环节" value="设计环节"></el-option>
                <el-option label="实施环节" value="实施环节"></el-option>
                <el-option label="验收环节" value="验收环节"></el-option>
              </el-select>
            );
          }
        },
        {
          prop: "issueBigType",
          label: "问题大类",
          align: "center",
          tooltip: true,
          width: 400,
          formatter: (row) => {
            return (
              <el-input v-model={row.issueBigType} type="string"></el-input>
            );
          }
        },
        {
          prop: "issueSmallType",
          label: "问题小类",
          align: "center",
          tooltip: true,
          formatter: (row) => {
            return (
              <el-input v-model={row.issueSmallType} type="string"></el-input>
            );
          }
        },
        {
          prop: "sort",
          label: "排序",
          align: "center",
          tooltip: true,
          width: 100
        },
        {
          prop: "tuodong",
          label: "移动排序",
          align: "center",
          tooltip: true,
          width: 120,
          formatter: (row, column, cellValue, index) => {
            return (
              <span>
                {index === 0 ? ('') : (
                  <span className="mr10">
                    <el-button type="text" onClick={() => {
                      this.rowDrop('up', row, index)
                    }}>上移</el-button>
                </span>
                )}
                {this.tableList.length - 1 == index ? ('') : (
                  <span className="mr10">
                    <el-button type="text" onClick={() => {
                      this.rowDrop('down', row, index)
                    }}>下移</el-button>
                </span>
                )}
                <span className="mr10">
                    <el-button type="text" onClick={() => {
                      this.handleDelete(index)
                    }}>删除</el-button>
                </span>
              </span>
            )
          }
        },
      ],
      // 表格静态参数
      staticSearchParam: {
        issueTache: '',
        issueBigType: '',
        issueSmallType: '',
      },
      //表格数据
      tableList: [],
      firstLevelIndexArr: [], // 一个空的数组，用于存放第一列每一行记录的合并数  控制第一列的合并
      firstLevelIndexPos: 0, // firstLevelIndexArr 的索引
      secondLevelIndexArr: [],
      indexDescArr: [],
      firstLine1: [],
      firstLine2: 0
    }
  },
  created() {
    //判断是否用户是否有权限查看
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      checkPermissionCooperationConfig().then((res) => {
        this.isShow = res.data
        if (true === this.isShow) {
          this.selectTableList()
        }
      })
    } else {
      this.isShow = true
      if (true === this.isShow) {
        this.selectTableList()
      }
    }

  },
  mounted() {
    this.rowDrop()
  },
  methods: {
    //获取列表数据
    selectTableList() {
      this.issueTacheSpanArr = []
      this.issueTachePos = 0
      this.issueBigTypeSpanArr = []
      this.issueBigTypePos = 0
      selectSuperviseIssueTypeConfigList(this.staticSearchParam).then(res => {
        if (res.code === '0000') {
          this.tableList = res.data.data
          // var tableListTemp = this.tableList
          this.getSpanArr(res.data.data)

        }
      })
    },
    getSpanArr(data) {
      // firstLevelIndexArr/secondLevelIndexArr来存放要合并的格数，同时还要设定一个变量firstLevelIndexPos/secondLevelIndexPos来记录
      this.firstLevelIndexArr = []
      this.secondLevelIndexArr = []
      this.indexDescArr = []
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.firstLevelIndexArr.push(1)
          this.firstLevelIndexPos = 0
        } else {
          // 判断当前元素与上一个元素是否相同(第1和第2列)
          if (data[i].issueBigType === data[i - 1].issueBigType) {
            this.firstLevelIndexArr[this.firstLevelIndexPos] += 1
            this.firstLevelIndexArr.push(0)
          } else {
            this.firstLevelIndexArr.push(1)
            this.firstLevelIndexPos = i
          }
        }
      }
      this.firstLine1 = []
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.firstLine1.push(1)
          this.firstLine2 = 0
        } else {
          // 判断当前元素与上一个元素是否相同(第1和第2列)
          if (data[i].issueTache === data[i - 1].issueTache) {
            this.firstLine1[this.firstLine2] += 1
            this.firstLine1.push(0)
          } else {
            this.firstLine1.push(1)
            this.firstLine2 = i
          }
        }
      }
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0) {
        const _row = this.firstLine1[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else if (columnIndex === 1) {
        const _row = this.firstLevelIndexArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    //编辑按钮
    handleUpdate() {
      this.isEditVisible = true
      this.$refs.editTable.getTableData(this.staticSearchParam)
      this.tableList = this.$refs.editTable.tableData
    },
    //保存按钮
    handleSave() {
      this.tableList = this.$refs.editTable.tableData
      console.log('保存', this.tableList)
      for (var i = 0; i < this.tableList.length; i++) {
        if (!this.tableList[i].issueTache) {
          this.$message.error("第" + (i + 1) + "行，问题发生环节 不能为空");
          return
        }
        if (!this.tableList[i].issueBigType) {
          this.$message.error("第" + (i + 1) + "行，问题大类 不能为空");
          return
        }
        if (!this.tableList[i].issueSmallType) {
          this.$message.error("第" + (i + 1) + "行，问题小类 不能为空");
          return
        }
      }
      editSuperviseIssueTypeConfig(this.tableList).then(res => {
        if (res.code === '0000') {
          this.$message.success("保存成功");
          this.isEditVisible = false
          this.$refs.table.getTableData(this.staticSearchParam)
        }
      })
    },
    //新增按钮
    handleAdd() {
      this.tableList = this.$refs.editTable.tableData
      let sortNum = this.tableList ? this.tableList.length + 1 : 1
      this.tableList.push({issueTache: '', issueBigType: '', issueSmallType: '', sort: sortNum})
      console.log('tableList', this.tableList)
      this.$refs.editTable.tableData = this.tableList
    },
    //删除按钮
    handleDelete(index) {
      console.log(index);
      this.tableList.splice(index, 1);
      for (var i = 0; i < this.tableList.length; i++) {
        this.tableList[i].sort = i + 1
      }
      this.$refs.editTable.tableData = this.tableList
    },
    //表格排序移动
    rowDrop(type, row, index) {
      if (type === 'up') {
        if (row.sort == 1) {
          return
        }
        this.tableList[index - 1].sort = this.tableList[index - 1].sort + 1
        this.tableList[index].sort = this.tableList[index].sort - 1
        var elem = this.tableList.splice(index, 1);
        this.tableList.splice(index - 1, 0, elem[0]);
      } else if (type === 'down') {
        if (row.sort == this.tableList.length) {
          return
        }
        this.tableList[index + 1].sort = this.tableList[index + 1].sort - 1
        this.tableList[index].sort = this.tableList[index].sort + 1
        var elem = this.tableList.splice(index, 1);
        this.tableList.splice(index + 1, 0, elem[0]);
      }
      this.$refs.editTable.tableData = this.tableList
    },
  }
}
</script>
