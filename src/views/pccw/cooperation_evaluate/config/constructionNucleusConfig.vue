/**
* @author: <PERSON><PERSON><PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理-建设单位合作单位考核接口人配置
*/
<template>
  <div>
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <mssCard title="配置内容">
        <div slot="headerBtn">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </div>
        <div slot="content">
          <mssTable
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            border
            :staticSearchParam="loadParam"
          >
          </mssTable>
          <!--新增考核内容-->
          <el-dialog
            title="新增建设单位合作单位考核接口人"
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            width="35%"
            :before-close="closeDialog">
            <el-form :model="constructionNucleusForm" ref="constructionNucleusForm" v-if="dialogVisible"
                     label-position="right"
                     label-width="160px">
              <el-form-item label="建设单位：" label-width="160px" prop="constructionUnit"
                            :rules="[ { required: true, message: '建设单位不能为空',trigger: ['blur', 'change']}]">
                <el-select
                  v-model="constructionNucleusForm.constructionUnit"
                  allow-create
                  default-first-option
                  style="margin-left: 20px;width: 80%"
                  placeholder="请选择建设单位">
                  <el-option
                    v-for="item in constructionUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="合作单位类型：" label-width="160px" prop="cooperationCompanyType"
                            :rules="[ { required: true, message: '合作单位类型不能为空',trigger: ['blur', 'change']}]">
                <el-select
                  v-model="constructionNucleusForm.cooperationCompanyType"
                  allow-create
                  default-first-option
                  style="margin-left: 20px;width: 80%"
                  placeholder="请选择合作单位类型">
                  <el-option
                    v-for="item in cooperationCompanyTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="合作单位名称：" label-width="160px" prop="cooperationCompanyName"
                            :rules="[ { required: true, message: '合作单位名称不能为空',trigger: ['blur', 'change']}]">
                <el-input v-model="constructionNucleusForm.cooperationCompanyName" style="margin-left: 20px;width: 80%"
                          readonly @focus="openChooseUserDailog(constructionNucleusForm)"></el-input>
              </el-form-item>

              <el-form-item label="合作单位编码：" style="margin-top: 30px"
                            prop="cooperationCompanyCode" label-width="160px"
                            :rules="[ { required: true, message: '合作单位编码不能为空',trigger: ['blur', 'change']}]">
                <el-input disabled v-model="constructionNucleusForm.cooperationCompanyCode"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>
              <el-form-item label="账号：" label-width="160px" prop="cooperationPersonLogin"
                            :rules="[ { required: true, message: '账号不能为空',trigger: ['blur', 'change']}]">
                <el-input disabled v-model="constructionNucleusForm.cooperationPersonLogin"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>
              <el-form-item label="姓名：" style="margin-top: 30px"
                            prop="cooperationPersonName" label-width="160px"
                            :rules="[ { required: true, message: '姓名不能为空',trigger: ['blur', 'change']}]">
                <el-input disabled v-model="constructionNucleusForm.cooperationPersonName"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>

            </el-form>
            <span slot="footer" class="dialog-footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="dialogAddData">确 定</el-button>
            </span>

          </el-dialog>

          <mssChooseUser ref="chooseUser" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseUser>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {
  checkPermissionCooperationConfig,
  deleteConstructionNucleusConfig,
  editConstructionNucleusConfig, selectConstructionNucleusConfigById,
  selectConstructionNucleusConfigList,
  selectCooperationCompanyCode
} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";


export default {
  name: 'constructionNucleusConfig',
  components: {},
  data() {
    return {
      //判断用户是否有权限配置
      isShow: false,
      loadParam: {},
      staticSearchParam: {},
      tableApi: selectConstructionNucleusConfigList,
      //搜索字段配置
      searchConfig: [
        {
          type: 'input',
          fieldName: 'cooperationCompanyName',
          label: '合作单位名称'
        },
        {
          type: 'input',
          fieldName: 'cooperationPersonLogin',
          label: '姓名'
        },
      ],
      //表格头部配置
      tableHeader: [
        {
          prop: "constructionUnit",
          label: "建设单位",
          align: "center",
          tooltip: true,
        },
        {
          prop: "cooperationCompanyName",
          label: "合作单位名称",
          align: "center",
          tooltip: true,
          minWidth: 150
        },
        {
          prop: 'cooperationCompanyCode',
          label: '合作单位编码',
          align: "center",
          tooltip: true,
          minWidth: 150
        },
        {
          prop: "cooperationPersonLogin",
          label: "账号",
          align: "center",
          tooltip: true,
        },
        {
          prop: "cooperationPersonName",
          label: "姓名",
          align: "center",
          tooltip: true,
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '60px',
          formatter: (row) => {
            return (
              <span>
                <span class='table_btn mr10' onClick={() => {
                  this.handleDelete(row)
                }}>删除 </span>
              </span>
            )
          }
        }
      ],
      //新增内容弹窗
      dialogVisible: false,
      //新增考核项目对象
      constructionNucleusForm: {
        constructionUnit: '',
        cooperationCompanyType: '',
        cooperationCompanyName: '',
        cooperationCompanyCode: '',
        cooperationPersonLogin: '',
        cooperationPersonName: '',
      },
      //建设单位下拉框
      constructionUnitOptions: [],
      //合作单位类型下拉框
      cooperationCompanyTypeOptions: [
        {value: '1001', label: '设计单位'},
        {value: '2001', label: '监理单位'},
        {value: '3001', label: '施工单位'},
      ],
      multSelect: false,
    }
  },
  created() {
    //判断是否用户是否有权限查看
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      checkPermissionCooperationConfig().then((res) => {
        this.isShow = res.data
      })
    } else {
      this.isShow = true
    }
  },
  mounted() {
    this.constructionUnitOptions = JSON.parse(window.sessionStorage.getItem('constructionUnit'));
  },
  methods: {
    reset() {
      this.search();
    },
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    //删除按钮
    handleDelete(row) {
      deleteConstructionNucleusConfig(row.id).then((res) => {
        if (res.code === '0000') {
          this.$message.success("删除成功");
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        } else {
          this.$message.error("删除失败");
        }
      })
    },
    //编辑按钮
    handleEdit(row) {
      selectConstructionNucleusConfigById(row.id).then((res) => {
        if (res.code === '0000') {
          this.constructionNucleusForm = res.data
          this.dialogVisible = true
        } else {
          this.$message.error("获取详细信息失败");
        }
      })
    },
    //新增考核内容按钮
    handleAdd() {
      this.constructionNucleusForm = {
        constructionUnit: '',
        cooperationCompanyType: '',
        cooperationCompanyName: '',
        cooperationCompanyCode: '',
        cooperationPersonLogin: '',
        cooperationPersonName: '',
      }
      this.dialogVisible = true
    },
    //取消新增按钮
    closeDialog() {
      this.constructionNucleusForm = {
        constructionUnit: '',
        cooperationCompanyType: '',
        cooperationCompanyName: '',
        cooperationCompanyCode: '',
        cooperationPersonLogin: '',
        cooperationPersonName: '',
      }
      this.dialogVisible = false
    },
    //新增考核内容数据
    dialogAddData() {
      this.$refs['constructionNucleusForm'].validate((valid) => {
        if (valid) {
          let param = this.constructionNucleusForm
          editConstructionNucleusConfig(param).then((res) => {
            if (res.code === '0000') {
              this.$message.success("保存成功");
              this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
              this.closeDialog()
            } else {
              // this.$message.error("保存失败");
            }
          })
        } else {
          return false;
        }
      });
    },
    //打开选择合作单位
    openChooseUserDailog(row) {
      if (!row.cooperationCompanyType) {
        this.$message.error("请先选择合作单位类型");
        return
      }
      console.log("选择合作单位类型：", row.cooperationCompanyType)
      this.roleRow = row
      const item = {
        excuterNames: row.cooperationPersonName,
        excuterIds: row.userId
      }
      const deptParams = {deptIds: row.cooperationCompanyType, orgChildId: row.cooperationCompanyType}
      this.$refs.chooseUser.init(item, deptParams)
    },
    //确认选择合作单位、合作单位账号
    showCheckList({checkList}) {
      const list = checkList[0]
      console.log("确认选择合作单位、合作单位账号", list)

      let param = {
        deptId: list.deptId,
        cooperationCompanyName: list.deptName,
      }
      selectCooperationCompanyCode(param).then((res) => {
        if (res.code === '0000') {
          console.log("获取合作单位编码：", res)
          this.constructionNucleusForm.cooperationCompanyName = list.deptName
          this.constructionNucleusForm.cooperationCompanyCode = res.data.cooperationCompanyCode
          this.constructionNucleusForm.cooperationPersonName = list.realName
          this.constructionNucleusForm.cooperationPersonLogin = list.account
          this.constructionNucleusForm.userId = list.userId
        } else {
          this.$message.error("获取合作单位编码失败");
        }
      })
    },
  }
}
</script>
