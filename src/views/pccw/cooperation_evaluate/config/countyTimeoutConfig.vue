/**
* @author: ye<PERSON><PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理-区县超时待办失效时长设置
*/
<template>
  <div>
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <mssCard title="区县超时待办失效时长">
        <div slot="headerBtn">

        </div>
        <div slot="content">
          <el-form :model="cooperationCountyTimeoutConfigForm" ref="cooperationCountyTimeoutConfigForm"
                   label-position="right"
                   label-width="200px">
            <el-form-item label="区县超时待办失效时长（天）：" prop="timeoutDays"
                          style="margin-left: 5px; width: 38%" :rules="[
                { required: true, message: '失效时长不能为空',trigger: 'blur'},
                { type: 'number', message: '失效时长必须为数字值', trigger: ['blur', 'change']}
               ]">
              <el-input placeholder="请输入失效时长"
                        v-model.number="cooperationCountyTimeoutConfigForm.timeoutDays"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="saveData">保存</el-button>
            </span>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {
  checkPermissionCooperationConfig, editCountyTimeoutConfig,
  selectCountyTimeoutConfigConfigList
} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";


export default {
  name: 'countyTimeoutConfig',
  components: {},
  data() {
    return {
      //判断用户是否有权限配置
      isShow: false,
      //区县超时待办失效时长配置
      cooperationCountyTimeoutConfigForm: {}
    }
  },
  created() {
    //判断是否用户是否有权限查看
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      checkPermissionCooperationConfig().then((res) => {
        this.isShow = res.data
        if (true === this.isShow) {
          this.selectCountyTimeoutConfigConfigList()
        }
      })
    } else {
      this.isShow = true
      this.selectCountyTimeoutConfigConfigList()
    }
  },
  mounted() {
  },
  methods: {
    selectCountyTimeoutConfigConfigList() {
      selectCountyTimeoutConfigConfigList().then((res) => {
        this.cooperationCountyTimeoutConfigForm = res.data.data[0]
      })
    },
    saveData(){
      let addParam = this.cooperationCountyTimeoutConfigForm
      editCountyTimeoutConfig(addParam).then(res => {
        if (res.code === '0000') {
          this.$message.success("保存成功");
          this.selectCountyTimeoutConfigConfigList();
        }
      })
    }
  }
}
</script>
