/**
* @author: yewen<PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理-省公司合作单位管理员配置
*/
<template>
  <div>
    <mssCard title="省公司合作单位管理员配置">
      <div slot="headerBtn">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          :staticSearchParam="loadParam"
        >
        </mssTable>
        <mssChooseUser ref="chooseUser" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseUser>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {
  deleteConstructionNucleusConfig, deleteProvinceAdminConfig,
  editProvinceAdminConfig,
  selectProvinceAdminConfigList
} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";


export default {
  name: 'provinceAdminConfig',
  components: {},
  data() {
    return {
      loadParam: {},
      staticSearchParam: {},
      tableApi: selectProvinceAdminConfigList,
      //表格头部配置
      tableHeader: [
        {
          prop: "constructionUnit",
          label: "建设单位",
          align: "center",
          tooltip: true,
        },
        {
          prop: "account",
          label: "账号",
          align: "center",
          tooltip: true,
        },
        {
          prop: "realName",
          label: "姓名",
          align: "center",
          tooltip: true,
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '60px',
          formatter: (row) => {
            return (
              <span>
                <span class='table_btn mr10' onClick={() => {
                  this.handleDelete(row)
                }}>删除 </span>
              </span>
            )
          }
        }
      ],
      multSelect: false,
    }

  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleAdd() {
      this.openChooseUserDailog({})
    },
    //删除按钮
    handleDelete(row) {
      deleteProvinceAdminConfig(row.id).then((res) => {
        if (res.code === '0000') {
          this.$message.success("删除成功");
          this.$refs.table.getTableData();
        } else {
          this.$message.error("删除失败");
        }
      })
    },
    //打开选择合作单位
    openChooseUserDailog(row) {
      this.roleRow = row
      if (row) {
      }
      const item = {
        excuterNames: row.cooperationPersonName ? row.cooperationPersonName : '',
        excuterIds: row.userId ? row.userId : ''
      }
      const deptParams = {
        deptIds: sessionStorage.getItem('firstDeptId'),
        orgChildId: sessionStorage.getItem('firstDeptId')
      }
      this.$refs.chooseUser.init(item, deptParams)
    },
    //确认选择合作单位、合作单位账号
    showCheckList({checkList}) {
      const list = checkList[0]
      let addParam = {
        account: list.account,
        realName: list.realName,
        userId: list.userId,
        constructionUnit: list.deptName,
      }
      editProvinceAdminConfig(addParam).then(res => {
        if (res.code === '0000') {
          this.$message.success("保存成功");
          this.$refs.table.getTableData();
        }
      })
    },
  }
}
</script>
