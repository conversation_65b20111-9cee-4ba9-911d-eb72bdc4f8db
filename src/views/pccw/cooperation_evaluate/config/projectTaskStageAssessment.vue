/**
* @author: yewen<PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理-项目任务所在阶段考核表模板配置
*/
<template>
  <div>
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <el-tabs v-model="tabActiveName" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="勘察设计阶段" name="surveyDesignStageTabPane" lazy>
          <projectTaskStageAssessmentDialog ref="surveyDesignStageDialog"
                                            :titleName="configArr.surveyDesignStageDialog.titleName"
                                            :assessmentType="configArr.surveyDesignStageDialog.assessmentType"
          ></projectTaskStageAssessmentDialog>
        </el-tab-pane>
        <el-tab-pane label="实施验收阶段" name="implementAcceptanceStageTabPane" lazy>
          <projectTaskStageAssessmentDialog ref="implementAcceptanceStageDialog"
                                            :titleName="configArr.implementAcceptanceStageDialog.titleName"
                                            :assessmentType="configArr.implementAcceptanceStageDialog.assessmentType"
          ></projectTaskStageAssessmentDialog>
        </el-tab-pane>
        <el-tab-pane label="工程准备阶段" name="engineeringPreparationStageTabPane" lazy>
          <el-tabs v-model="engineeringPreparationStageTabActiveName" type="border-card"
                   @tab-click="prepareHandleClick">
            <el-tab-pane label="监理单位" name="prepareSupervisionUnitTabPane" lazy>
              <projectTaskStageAssessmentDialog ref="prepareSupervisionUnitDialog"
                                                :titleName="configArr.prepareSupervisionUnitDialog.titleName"
                                                :assessmentType="configArr.prepareSupervisionUnitDialog.assessmentType"
              ></projectTaskStageAssessmentDialog>
            </el-tab-pane>
            <el-tab-pane label="施工单位" name="prepareConstructionUnitTabPane" lazy>
              <projectTaskStageAssessmentDialog ref="prepareConstructionUnitDialog"
                                                :titleName="configArr.prepareConstructionUnitDialog.titleName"
                                                :assessmentType="configArr.prepareConstructionUnitDialog.assessmentType"
              ></projectTaskStageAssessmentDialog>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="工程实施阶段" name="engineeringImplementationStageTabPane" lazy>
          <el-tabs v-model="engineeringImplementationStageTabActiveName" type="border-card"
                   @tab-click="implementationHandleClick">
            <el-tab-pane label="监理单位" name="implementationSupervisionUnitTabPane" lazy>
              <projectTaskStageAssessmentDialog ref="implementationSupervisionUnitDialog"
                                                :titleName="configArr.implementationSupervisionUnitDialog.titleName"
                                                :assessmentType="configArr.implementationSupervisionUnitDialog.assessmentType"
              ></projectTaskStageAssessmentDialog>
            </el-tab-pane>
            <el-tab-pane label="施工单位" name="implementationConstructionUnitTabPane" lazy>
              <projectTaskStageAssessmentDialog ref="implementationConstructionUnitDialog"
                                                :titleName="configArr.implementationConstructionUnitDialog.titleName"
                                                :assessmentType="configArr.implementationConstructionUnitDialog.assessmentType"
              ></projectTaskStageAssessmentDialog>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="工程验收阶段" name="engineeringAcceptanceStageTabPane" lazy>
          <el-tabs v-model="engineeringAcceptanceStageTabActiveName" type="border-card"
                   @tab-click="acceptanceHandleClick">
            <el-tab-pane label="监理单位" name="acceptanceSupervisionUnitTabPane" lazy>
              <projectTaskStageAssessmentDialog ref="acceptanceSupervisionUnitDialog"
                                                :titleName="configArr.acceptanceSupervisionUnitDialog.titleName"
                                                :assessmentType="configArr.acceptanceSupervisionUnitDialog.assessmentType"
              ></projectTaskStageAssessmentDialog>
            </el-tab-pane>
            <el-tab-pane label="施工单位" name="acceptanceConstructionUnitTabPane" lazy>
              <projectTaskStageAssessmentDialog ref="acceptanceConstructionUnitDialog"
                                                :titleName="configArr.acceptanceConstructionUnitDialog.titleName"
                                                :assessmentType="configArr.acceptanceConstructionUnitDialog.assessmentType"
              ></projectTaskStageAssessmentDialog>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import projectTaskStageAssessmentDialog
  from "@/views/pccw/cooperation_evaluate/config/projectTaskStageAssessmentDialog.vue";
import {checkPermissionCooperationConfig} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";


export default {
  name: 'projectTaskStageAssessment',
  components: {projectTaskStageAssessmentDialog},
  data() {
    return {
      //判断用户是否有权限配置
      isShow: false,
      //默认选中 tab
      tabActiveName: 'surveyDesignStageTabPane',
      //工程准备阶段-tabs
      engineeringPreparationStageTabActiveName: 'prepareSupervisionUnitTabPane',
      //工程实施阶段-tabs
      engineeringImplementationStageTabActiveName: 'implementationSupervisionUnitTabPane',
      //工程验收阶段-tabs
      engineeringAcceptanceStageTabActiveName: 'acceptanceSupervisionUnitTabPane',
      configArr: {
        surveyDesignStageDialog: {
          titleName: '通信建设工程勘察设计服务项目考评表（勘察设计阶段-权重70%）',
          assessmentType: 'surveyDesignStage'
        },
        implementAcceptanceStageDialog: {
          titleName: '通信建设工程勘察设计服务项目考评表（实施验收阶段-权重30%）',
          assessmentType: 'implementAcceptanceStage'
        },
        prepareSupervisionUnitDialog: {
          titleName: '通信建设工程监理服务项目考评表（工程准备阶段-权重10%）',
          assessmentType: 'prepareSupervisionUnit'
        },
        prepareConstructionUnitDialog: {
          titleName: '通信建设工程施工服务项目考评表（工程准备阶段-权重10%）',
          assessmentType: 'prepareConstructionUnit'
        },
        implementationSupervisionUnitDialog: {
          titleName: '通信建设工程监理服务项目考评表（工程实施阶段-权重70%）',
          assessmentType: 'implementationSupervisionUnit'
        },
        implementationConstructionUnitDialog: {
          titleName: '通信建设工程施工服务项目考评表（工程实施阶段-权重70%）',
          assessmentType: 'implementationConstructionUnit'
        },
        acceptanceSupervisionUnitDialog: {
          titleName: '通信建设工程监理服务项目考评表（工程验收阶段-权重20%）',
          assessmentType: 'acceptanceSupervisionUnit'
        },
        acceptanceConstructionUnitDialog: {
          titleName: '通信建设工程施工服务项目考评表（工程验收阶段-权重20%）',
          assessmentType: 'acceptanceConstructionUnit'
        }
      }

    }
  },
  created() {
    //判断是否用户是否有权限查看
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      checkPermissionCooperationConfig().then((res) => {
        this.isShow = res.data
      })
    } else {
      this.isShow = true
    }
  },
  mounted() {
  },
  methods: {
    handleClick(tab, event) {
    },
    //工程准备阶段-tabs
    prepareHandleClick() {
    },
    //工程实施阶段-tabs
    implementationHandleClick() {
    },
    //工程验收阶段-tabs
    acceptanceHandleClick() {
    },

  }
}
</script>
