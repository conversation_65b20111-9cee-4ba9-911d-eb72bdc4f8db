/**
* @author: <PERSON><PERSON><PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理-建设单位合作单位管理员、维护部门评分人配置"
*/
<template>
  <div>
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <mssCard title="建设单位合作单位管理员、维护部门评分人配置列表">
        <div slot="headerBtn">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </div>
        <div slot="content">
          <mssTable
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            border
            :staticSearchParam="loadParam"
          >
          </mssTable>
          <!--新增考核内容-->
          <el-dialog
            title="新增配置"
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            width="35%"
            :before-close="closeDialog">
            <el-form :model="maintenanceScoreForm" ref="maintenanceScoreForm" v-if="dialogVisible"
                     label-position="right"
                     label-width="160px">
              <el-form-item label="建设单位：" label-width="160px" prop="constructionUnitId"
                            :rules="[ { required: true, message: '建设单位不能为空',trigger: ['blur', 'change']}]">
                <el-select
                  v-model="maintenanceScoreForm.constructionUnitId"
                  allow-create
                  default-first-option
                  :disabled="isEditShow"
                  style="margin-left: 20px;width: 80%"
                  @change="handleSelectChange"
                  placeholder="请选择建设单位">
                  <el-option
                    v-for="item in constructionUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="评分人账号：" label-width="160px" prop="graderAccount"
                            :rules="[ { required: true, message: '账号不能为空',trigger: ['blur', 'change']}]">
                <el-input v-model="maintenanceScoreForm.graderAccount"
                          readonly @focus="openChooseUserDailog('grader',maintenanceScoreForm)"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>
              <el-form-item label="评分人姓名：" style="margin-top: 30px"
                            prop="graderUser" label-width="160px"
                            :rules="[ { required: true, message: '姓名不能为空',trigger: ['blur', 'change']}]">
                <el-input disabled v-model="maintenanceScoreForm.graderUser"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>
              <el-form-item label="合作单位管理员账号：" label-width="160px" prop="account"
                            :rules="[ { required: true, message: '账号不能为空',trigger: ['blur', 'change']}]">
                <el-input v-model="maintenanceScoreForm.account"
                          readonly @focus="openChooseUserDailog('cooperation',maintenanceScoreForm)"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>
              <el-form-item label="合作单位管理员姓名：" style="margin-top: 30px"
                            prop="realName" label-width="160px"
                            :rules="[ { required: true, message: '姓名不能为空',trigger: ['blur', 'change']}]">
                <el-input disabled v-model="maintenanceScoreForm.realName"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>
              <el-form-item label="待办下发时间：" label-width="160px" prop="toDoIssuedTime"
                            :rules="[ { required: true, message: '待办下发时间不能为空',trigger: ['blur', 'change']}]">
                <el-select
                  v-model="maintenanceScoreForm.toDoIssuedTime"
                  allow-create
                  default-first-option
                  style="margin-left: 20px;width: 80%"
                  placeholder="请选择待办下发时间">
                  <el-option
                    v-for="item in toDoIssuedTimeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

            </el-form>
            <span slot="footer" class="dialog-footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="dialogAddData">确 定</el-button>
            </span>

          </el-dialog>

          <mssChooseUser ref="chooseUser" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseUser>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {
  checkPermissionCooperationConfig,
  deleteMaintenanceScoreConfig,
  editMaintenanceScoreConfig,
  selectMaintenanceScoreConfigById,
  selectMaintenanceScoreConfigList
} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";
import {getDeptService} from "@/api/choose_dept";


export default {
  name: 'constructionAdminAndMaintenanceScoreConfig',
  components: {},
  data() {
    return {
      //判断用户是否有权限配置
      isShow: false,
      loadParam: {},
      staticSearchParam: {},
      tableApi: selectMaintenanceScoreConfigList,
      //搜索字段配置
      searchConfig: [
        {
          type: 'select',
          fieldName: 'constructionUnitId',
          label: '建设单位',
          options: this.constructionUnitOptions,
        },
        {
          type: 'input',
          fieldName: 'graderUser',
          label: '评分人姓名'
        },
        {
          type: 'input',
          fieldName: 'realName',
          label: '合作单位管理员姓名'
        },
      ],
      //表格头部配置
      tableHeader: [
        {
          prop: "constructionUnit",
          label: "建设单位",
          align: "center",
          tooltip: true,
        },
        {
          prop: "graderAccount",
          label: "评分人账号",
          align: "center",
          tooltip: true,

        },
        {
          prop: 'graderUser',
          label: '评分人姓名',
          align: "center",
          tooltip: true,

        },
        {
          prop: "account",
          label: "合作单位管理员账号",
          align: "center",
          tooltip: true,
        },
        {
          prop: "realName",
          label: "合作单位管理员姓名",
          align: "center",
          tooltip: true,
        },
        {
          prop: "toDoIssuedTime",
          label: "待办下发时间",
          align: "center",
          tooltip: true,
          formatter: (row) => {
            return (
              <span>
                <span>
                {row.toDoIssuedTime !== '32' ? '每月' + row.toDoIssuedTime + '日' : '每月最后一天'}
                </span>
              </span>
            )
          }
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '60px',
          formatter: (row) => {
            return (
              <span>
              <span class='table_btn mr10' onClick={() => {
                this.handleEdit(row)
              }}>编辑</span>
              <span class='table_btn' onClick={() => {
                this.handleDelete(row)
              }}>删除</span>
          </span>)
          }
        }
      ],
      //建设单位下拉框
      constructionUnitOptions: [],
      //建设单位下拉框
      constructionUnitFromOptionsValue: {},
      //新增内容弹窗
      dialogVisible: false,
      //新增评分人对象
      maintenanceScoreForm: {
        constructionUnit: '',
        constructionUnitId: '',
        graderAccount: '',
        graderUser: '',
        account: '',
        realName: '',
        toDoIssuedTime: '',
        userId: '',
        graderUserId: '',
      },
      //是否多选
      multSelect: false,
      //显示类型
      openChooseUserType: '',
      //待办下发时间选择框
      toDoIssuedTimeOptions: [],
      //是否允许编辑
      isEditShow: false,
    }
  },
  created() {
    //获取建设单位下拉框
    // let param = {
    //   "isReturnUser": "0",
    //   "parentId": "-2"
    // }
    //判断是否用户是否有权限查看
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      checkPermissionCooperationConfig().then((res) => {
        this.isShow = res.data
        // this.getDeptService(param)
      })
    } else {
      this.isShow = true
      // this.getDeptService(param)
    }
  },
  mounted() {
    this.constructionUnitOptions = JSON.parse(window.sessionStorage.getItem('constructionUnit'));
    this.$set(this.searchConfig[0], 'options', this.constructionUnitOptions)
    for (let i = 1; i < 32; i++) {
      this.toDoIssuedTimeOptions.push({
        label: i + "日",
        value: i
      })
    }
    this.toDoIssuedTimeOptions.push({
      label: "每月最后一天",
      value: 32
    })
  },
  methods: {
    //获取建设单位下拉框值
    getDeptService(param) {
      getDeptService(param).then((res) => {
        if (res.code === '0000') {
          let data = res.data
          data.forEach((item) => {
            if (item.text !== "省公司") {
              this.constructionUnitOptions.push({
                label: item.text,
                value: item.id
              })
              this.constructionUnitFromOptionsValue[item.id] = item.text
            }
          })
          console.log("value，", this.constructionUnitFromOptionsValue)
        }
      })
    },
    reset() {
      this.search();
    },
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    //新增评分人按钮
    handleAdd() {
      this.maintenanceScoreForm = {
        constructionUnit: '',
        constructionUnitId: '',
        graderAccount: '',
        graderUser: '',
        account: '',
        realName: '',
        toDoIssuedTime: '',
        userId: '',
        graderUserId: '',
      }
      this.dialogVisible = true
      this.isEditShow = false
    },
    //编辑按钮
    handleEdit(row) {
      selectMaintenanceScoreConfigById(row.id).then((res) => {
        if (res.code === '0000') {
          this.maintenanceScoreForm = res.data
          this.dialogVisible = true
          this.isEditShow = true
        } else {
          this.$message.error("获取详细信息失败");
        }
      })
    },
    //删除按钮
    handleDelete(row) {
      deleteMaintenanceScoreConfig(row.id).then((res) => {
        if (res.code === '0000') {
          this.$message.success("删除成功");
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        } else {
          this.$message.error("删除失败");
        }
      })
    },
    //取消新增按钮
    closeDialog() {
      this.maintenanceScoreForm = {
        constructionUnit: '',
        constructionUnitId: '',
        graderAccount: '',
        graderUser: '',
        account: '',
        realName: '',
        toDoIssuedTime: '',
        userId: '',
        graderUserId: '',
      }
      this.dialogVisible = false
    },
    handleSelectChange() {
      this.$set(this.maintenanceScoreForm, 'graderAccount', '')
      this.$set(this.maintenanceScoreForm, 'graderUser', '')
      this.$set(this.maintenanceScoreForm, 'account', '')
      this.$set(this.maintenanceScoreForm, 'realName', '')
      this.$set(this.maintenanceScoreForm, 'userId', '')
      this.$set(this.maintenanceScoreForm, 'graderUserId', '')
      // var constructionUnitId = this.maintenanceScoreForm.constructionUnitId;
      // this.$set(this.maintenanceScoreForm, 'constructionUnit', this.constructionUnitFromOptionsValue[constructionUnitId])
      this.$set(this.maintenanceScoreForm, 'constructionUnit', this.maintenanceScoreForm.constructionUnitId)
    },
    //打开选择合作单位
    openChooseUserDailog(type, row) {
      console.log("打开选择合作单位", type, row)
      this.openChooseUserType = type
      if (!row.constructionUnit) {
        this.$message.error("请先选择建设单位");
        return
      }
      this.roleRow = row
      let item = {}
      if (type === 'grader') {
        item = {
          excuterNames: row.graderUser,
          excuterIds: row.graderUserId
        }
      } else if (type === 'cooperation') {
        item = {
          excuterNames: row.realName,
          excuterIds: row.userId
        }
      }
      let deptParams = {}
      switch (row.constructionUnit) {
        case "鹰潭分公司":
          deptParams = {deptIds: 12, orgChildId: 12}
          break;
        case "新余分公司":
          deptParams = {deptIds: 10, orgChildId: 10}
          break;
        case "萍乡分公司":
          deptParams = {deptIds: 7, orgChildId: 7}
          break;
        case "景德镇分公司":
          deptParams = {deptIds: 4, orgChildId: 4}
          break;
        case "赣州分公司":
          deptParams = {deptIds: 2, orgChildId: 2}
          break;
        case "吉安分公司":
          deptParams = {deptIds: 3, orgChildId: 3}
          break;
        case "宜春分公司":
          deptParams = {deptIds: 11, orgChildId: 11}
          break;
        case "抚州分公司":
          deptParams = {deptIds: 9, orgChildId: 9}
          break;
        case "上饶分公司":
          deptParams = {deptIds: 8, orgChildId: 8}
          break;
        case "九江分公司":
          deptParams = {deptIds: 5, orgChildId: 5}
          break;
        case "南昌分公司":
          deptParams = {deptIds: 6, orgChildId: 6}
          break;
        default:
          //省公司
          deptParams = {deptIds: 1, orgChildId: 1}
          break;
      }
      this.$refs.chooseUser.init(item, deptParams)
    },
    //确认选择合作单位、合作单位账号
    showCheckList({checkList}) {
      const list = checkList[0]
      console.log("确认选择合作单位、合作单位账号", list)
      // this.maintenanceScoreForm.constructionUnit = list.secondDeptName
      // this.maintenanceScoreForm.constructionUnitId = list.secondDeptName
      if (this.openChooseUserType === 'grader') {
        this.maintenanceScoreForm.graderUser = list.realName
        this.maintenanceScoreForm.graderAccount = list.account
        this.maintenanceScoreForm.graderUserId = list.userId
      } else if (this.openChooseUserType === 'cooperation') {
        this.maintenanceScoreForm.realName = list.realName
        this.maintenanceScoreForm.account = list.account
        this.maintenanceScoreForm.userId = list.userId
      }
    },
    //新增考核内容数据
    dialogAddData() {
      this.$refs['maintenanceScoreForm'].validate((valid) => {
        if (valid) {
          let param = this.maintenanceScoreForm
          editMaintenanceScoreConfig(param).then((res) => {
            if (res.code === '0000') {
              this.$message.success("保存成功");
              this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
              this.closeDialog()
            } else {
              this.$message.error("保存失败");
            }
          })
        } else {
          return false;
        }
      });

    },
  }
}
</script>
