/**
* @author: <PERSON><PERSON><PERSON>
* @date: 2024-05-08
* @description: 合作单位后评估流程-配置管理-区县反馈人配置页面
*/
<template>
  <div>
    <div v-show="!isShow">
      <el-empty description="无权限查看数据，请联系管理员添加权限"></el-empty>
    </div>
    <div v-show="isShow">
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
        @changeSelect="handleSelectConstructionUnitChange"
      ></mssSearchForm>
      <mssCard title="区县反馈人配置列表">
        <div slot="headerBtn">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </div>
        <div slot="content">
          <mssTable
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            border
            :staticSearchParam="loadParam"
          >
          </mssTable>
          <!--新增考核内容-->
          <el-dialog
            title="新增区县反馈人配置"
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            width="35%"
            :before-close="closeDialog">
            <el-form :model="countyFeedbackForm" ref="countyFeedbackForm" v-if="dialogVisible"
                     label-position="right"
                     label-width="160px">
              <el-form-item label="建设单位：" label-width="160px" prop="constructionUnitId"
                            :rules="[ { required: true, message: '建设单位不能为空',trigger: ['blur', 'change']}]">
                <el-select
                  v-model="countyFeedbackForm.constructionUnitId"
                  allow-create
                  default-first-option
                  style="margin-left: 20px;width: 80%"
                  @change="handleSelectChange"
                  placeholder="请选择建设单位">
                  <el-option
                    v-for="item in constructionUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="区县：" label-width="160px" prop="countyId"
                            :rules="[ { required: true, message: '区县不能为空',trigger: ['blur', 'change']}]">
                <el-select
                  v-model="countyFeedbackForm.countyId"
                  allow-create
                  default-first-option
                  style="margin-left: 20px;width: 80%"
                  @change="handleSelectCountyChange"
                  placeholder="请选择区县">
                  <el-option
                    v-for="item in countyFromOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="反馈人账号：" label-width="160px" prop="feedbackAccount"
                            :rules="[ { required: true, message: '账号不能为空',trigger: ['blur', 'change']}]">
                <el-input v-model="countyFeedbackForm.feedbackAccount"
                          readonly @focus="openChooseUserDailog(countyFeedbackForm)"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>
              <el-form-item label="反馈人姓名：" style="margin-top: 30px"
                            prop="feedbackUser" label-width="160px"
                            :rules="[ { required: true, message: '姓名不能为空',trigger: ['blur', 'change']}]">
                <el-input disabled v-model="countyFeedbackForm.feedbackUser"
                          style="margin-left: 20px;width: 80%"></el-input>
              </el-form-item>

            </el-form>
            <span slot="footer" class="dialog-footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="dialogAddData">确 定</el-button>
            </span>

          </el-dialog>

          <mssChooseUser ref="chooseUser" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseUser>
        </div>
      </mssCard>
    </div>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {
  checkPermissionCooperationConfig,
  constructionCheckPermissionCooperationConfig,
  deleteConstructionNucleusConfig,
  deleteCountyFeedbackConfig,
  editConstructionNucleusConfig,
  editCountyFeedbackConfig,
  selectCountyFeedbackConfigList
} from "@/api/pccw/cooperation_evaluate/cooperationEvaluateConfig";
import {getDeptService} from "@/api/choose_dept";
import it from "element-ui/src/locale/lang/it";


export default {
  name: 'countyFeedbackConfig',
  components: {},
  data() {
    return {
      //判断用户是否有权限配置
      isShow: false,
      loadParam: {},
      staticSearchParam: {},
      tableApi: selectCountyFeedbackConfigList,
      //搜索字段配置
      searchConfig: [
        {
          type: 'select',
          fieldName: 'constructionUnitId',
          label: '建设单位',
          options: this.constructionUnitOptions,
        },
        {
          type: 'select',
          fieldName: 'countyId',
          label: '区县',
          options: []
        },
        {
          type: 'input',
          fieldName: 'feedbackUser',
          label: '反馈人姓名'
        },
      ],
      //表格头部配置
      tableHeader: [
        {
          prop: "constructionUnit",
          label: "建设单位",
          align: "center",
          tooltip: true,
        },
        {
          prop: "county",
          label: "区县",
          align: "center",
          tooltip: true,

        },
        {
          prop: 'feedbackAccount',
          label: '反馈人账号',
          align: "center",
          tooltip: true,

        },
        {
          prop: "feedbackUser",
          label: "反馈人姓名",
          align: "center",
          tooltip: true,
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '60px',
          formatter: (row) => {
            return (
              <span>
                <span class='table_btn mr10' onClick={() => {
                  this.handleDelete(row)
                }}>删除 </span>
              </span>
            )
          }
        }
      ],
      //新增内容弹窗
      dialogVisible: false,
      //新增考核项目对象
      countyFeedbackForm: {
        constructionUnit: '',
        constructionUnitId: '',
        county: '',
        countyId: '',
        feedbackAccount: '',
        feedbackUser: '',
        userId: '',
      },
      //建设单位下拉框
      constructionUnitOptions: [],
      //县下拉框
      countyOptions: [],
      //建设单位下拉框
      constructionUnitFromOptionsValue: {},
      //县下拉框
      countyFromOptions: [],
      countyFromOptionsValue: {},
      multSelect: false,
    }
  },
  created() {
    //获取建设单位下拉框
    let param = {
      "isReturnUser": "0",
      "parentId": "-2"
    }
    //判断是否用户是否有权限查看
    this.currentUserId = sessionStorage.getItem('userId');
    if (this.currentUserId !== '1') {
      constructionCheckPermissionCooperationConfig().then((res) => {
        this.isShow = res.data
        this.getDeptService(param)
      })
    } else {
      this.isShow = true
      this.getDeptService(param)
    }
  },
  mounted() {
    this.$set(this.searchConfig[0], 'options', this.constructionUnitOptions)
    this.$set(this.searchConfig[1], 'options', this.countyOptions)
  },
  methods: {
    //获取建设单位下拉框值
    getDeptService(param) {
      getDeptService(param).then((res) => {
        if (res.code === '0000') {
          let data = res.data
          data.forEach((item) => {
            if (item.text !== "省公司") {
              this.constructionUnitOptions.push({
                label: item.text,
                value: item.id
              })
              this.constructionUnitFromOptionsValue[item.id] = item.text
            }
          })
          console.log("value，", this.constructionUnitFromOptionsValue)
        }
      })
    },
    //搜索框 建设单位下拉框值改变
    handleSelectConstructionUnitChange(name, val) {
      if (name === 'constructionUnitId') {
        this.$set(this.$refs.searchForm.searchForm, 'countyId', '')
        var constructionUnitId = this.$refs.searchForm.searchForm.constructionUnitId;
        console.log("选择建设单位下拉框值：", constructionUnitId)
        let param = {
          "isReturnUser": "0",
          "parentId": constructionUnitId
        }
        getDeptService(param).then((res) => {
          if (res.code === '0000') {
            let data = res.data
            data.forEach((item) => {
              if (item.type === "3") {
                this.countyOptions.push({
                  label: item.text,
                  value: item.id
                })
              }
            })
          }
        })
      }

    },
    //添加 from 中建设单位下拉框
    handleSelectChange() {
      this.$set(this.countyFeedbackForm, 'countyId', '')
      this.$set(this.countyFeedbackForm, 'county', '')
      this.$set(this.countyFeedbackForm, 'feedbackAccount', '')
      this.$set(this.countyFeedbackForm, 'feedbackUser', '')
      this.$set(this.countyFeedbackForm, 'userId', '')
      var constructionUnitId = this.countyFeedbackForm.constructionUnitId;
      this.$set(this.countyFeedbackForm, 'constructionUnit', this.constructionUnitFromOptionsValue[constructionUnitId])
      let param = {
        "isReturnUser": "0",
        "parentId": constructionUnitId
      }
      getDeptService(param).then((res) => {
        if (res.code === '0000') {
          this.countyFromOptionsValue = {}
          let data = res.data
          data.forEach((item) => {
            if (item.type === "3") {
              this.countyFromOptions.push({
                label: item.text,
                value: item.id
              })
              this.countyFromOptionsValue[item.id] = item.text
            }
          })
        }
      })
    },
    handleSelectCountyChange() {
      var countyId = this.countyFeedbackForm.countyId;
      this.$set(this.countyFeedbackForm, 'county', this.countyFromOptionsValue[countyId])
    },
    reset() {
      this.search();
    },
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    //删除按钮
    handleDelete(row) {
      deleteCountyFeedbackConfig(row.id).then((res) => {
        if (res.code === '0000') {
          this.$message.success("删除成功");
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
        } else {
          this.$message.error("删除失败");
        }
      })
    },
    //新增考核内容按钮
    handleAdd() {
      this.countyFeedbackForm = {
        constructionUnit: '',
        constructionUnitId: '',
        county: '',
        countyId: '',
        feedbackAccount: '',
        feedbackUser: '',
        userId: '',
      }
      this.dialogVisible = true
    },
    //取消新增按钮
    closeDialog() {
      this.countyFromOptions = []
      this.countyFeedbackForm = {
        constructionUnit: '',
        constructionUnitId: '',
        county: '',
        countyId: '',
        feedbackAccount: '',
        feedbackUser: '',
        userId: '',
      }
      this.dialogVisible = false
    },
    //新增考核内容数据
    dialogAddData() {
      this.$refs['countyFeedbackForm'].validate((valid) => {
        if (valid) {
          let param = this.countyFeedbackForm
          editCountyFeedbackConfig(param).then((res) => {
            if (res.code === '0000') {
              this.$message.success("保存成功");
              this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
              this.closeDialog()
            } else {
              this.$message.error("保存失败");
            }
          })
        } else {
          return false;
        }
      });

    },
    //打开选择合作单位
    openChooseUserDailog(row) {
      if (!row.constructionUnit) {
        this.$message.error("请先选择建设单位");
        return
      }
      if (!row.county) {
        this.$message.error("请先选择区县");
        return
      }
      this.roleRow = row
      const item = {
        excuterNames: row.feedbackUser,
        excuterIds: row.userId
      }
      const deptParams = {deptIds: row.countyId, orgChildId: row.countyId}
      this.$refs.chooseUser.init(item, deptParams)
    },
    //确认选择合作单位、合作单位账号
    showCheckList({checkList}) {
      const list = checkList[0]
      console.log("确认选择合作单位、合作单位账号", list)
      this.countyFeedbackForm.constructionUnit = list.firstDeptName
      this.countyFeedbackForm.constructionUnitId = list.firstDeptId
      this.countyFeedbackForm.county = list.deptName
      this.countyFeedbackForm.countyId = list.deptId
      this.countyFeedbackForm.feedbackUser = list.realName
      this.countyFeedbackForm.feedbackAccount = list.account
      this.countyFeedbackForm.userId = list.userId


    },
  }
}
</script>
