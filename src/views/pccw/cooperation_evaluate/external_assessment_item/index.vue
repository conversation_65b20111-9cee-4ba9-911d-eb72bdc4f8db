<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="外部录入考核事项">
      <div slot="headerBtn">
        <el-button type="primary" @click="getAddition">新增</el-button>
        <el-button type="primary" @click="getDownload(1)">下载模板</el-button>
        <el-upload
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile2"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="getDownload(2)">导出</el-button>
        <!-- <el-button  type="primary" @click="getDelete">删除</el-button> -->
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          border
          selection
          :staticSearchParam="loadParam"
        >
        </mssTable>
      </div>
    </mssCard>
    <exemptionAdded @onClose="onClose" ref="exemption"></exemptionAdded>
    <el-dialog
      title="附件列表"
      :visible.sync="pendingShow"
      :close-on-click-modal="false"
      @close="pendingShow = false"
      width="50%"
    >
      <div>
        <mssTable
          ref="attachment"
          :columns="tableHeader2"
          :stationary="tableData"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="pendingShow = false"
          >确 定</el-button
        >
        <el-button
          size="mini"
          style="margin-left: 10px"
          @click="pendingShow = false"
          >取 消</el-button
        >
      </span>
    </el-dialog>
    <el-dialog title="错误信息" :visible.sync="dialogTableVisible">
      <el-table :data="gridData" border>
        <el-table-column
          property="lineNum"
          label="行数"
          width="100"
        ></el-table-column>
        <el-table-column property="message" label="信息"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogTableVisible = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {
    getEntryCheckList,
    uploadFile,
    selectFileList,
    downloadFile,
    downloadTemplate,
    deleteFile,
    exportTotal,
    importEntryCheckData,
    deleteEntryCheckData
  } from "@/api/pccw/cooperation_evaluate/external"
  import exemptionAdded from "@/views/pccw/cooperation_evaluate/exemptionAdded.vue"
  import { commonDown } from "@/utils/btn"
  export default {
    name: "problem_feedback_query",
    components: {
      exemptionAdded
    },
    data() {
      return {
        loadParam: {},
        tableApi: getEntryCheckList,
        //搜索字段配置
        searchConfig: [
          {
            label: "建设单位",
            type: "input",
            fieldName: "constructionUnit"
          },
          {
            label: "项目名称",
            type: "input",
            fieldName: "projectName"
          },
          {
            label: "任务名称",
            type: "input",
            fieldName: "taskName"
          },
          {
            label: "合作单位名称",
            type: "input",
            fieldName: "partnerName"
          },
          {
            type: "cycleDate",
            dateType: "month",
            label: "问题录入月份",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            fieldName: "entryMonth",
            clearable: false
          }
          // {
          //   label: '考核问题说明',
          //   type: 'select',
          //   fieldName: 'problemDesc'
          // },
        ],
        //表格头部配置
        tableHeader: [
          {
            prop: "creator",
            label: "发起人",
            align: "center",
            tooltip: true,
            minWidth: 130
          },
          {
            prop: "creatorCompany",
            label: "发起人单位名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "constructionUnit",
            label: "建设单位",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "projectNumber",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "partnerName",
            label: "合作单位名称",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "entryMonth",
            label: "问题录入月份",
            align: "center",
            tooltip: true,
            minWidth: 130
          },
          {
            prop: "problemDesc",
            label: "考核问题说明",
            align: "center",
            tooltip: true,
            minWidth: 150
          },
          {
            prop: "",
            label: "操作",
            align: "center",
            tooltip: true,
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-upload
                      ref="newFile"
                      class="upload-btn"
                      action="string"
                      show-file-list={false}
                      auto-upload={true}
                      http-request={params => {
                        this.importFile1(params, row)
                      }}
                    >
                      <el-button type="text">上传附件</el-button>
                    </el-upload>
                    <el-button
                      onClick={() => {
                        this.checkFile(row)
                      }}
                      type="text"
                    >
                      查看附件
                    </el-button>
                    <el-button
                      onClick={() => {
                        this.getDelete(row)
                      }}
                      type="text"
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              )
            }
          }
        ],
        pendingShow: false,
        tableHeader2: [
          {
            prop: "fileName",
            label: "附件名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "",
            label: "操作",
            align: "center",
            tooltip: true,
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.getDownload(3, row)
                      }}
                    >
                      下载附件
                    </el-button>
                  </span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.getDeleteFile(row)
                      }}
                    >
                      删除附件
                    </el-button>
                  </span>
                </span>
              )
            }
          }
        ],
        tableData: [],
        fileData: {},
        gridData: [],
        dialogTableVisible: false
      }
    },
    methods: {
      getDeleteFile(row) {
        deleteFile({ id: row.id }).then(res => {
          if (res.code == "0000") {
            this.$message.success("删除成功")
            selectFileList({ id: this.fileData.id, limit: 1000 }).then(res => {
              this.tableData = res.data.data
            })
          } else {
            this.$message.error(`${res.msg || "删除失败"}`)
          }
        })
      },
      getDownload(val, row) {
        if (val === 1) {
          commonDown({ limit: -1 }, downloadTemplate)
        } else if (val === 2) {
          commonDown(
            { ...this.$refs.searchForm.searchForm, limit: -1 },
            exportTotal
          )
        } else if (val === 3) {
          commonDown({ ...row, limit: -1 }, downloadFile)
        }
      },
      importFile1(params, row) {
        const param = new FormData()
        param.append("file", params.file)
        param.append("fileType", "外部录入考核事项")
        param.append("formId", row.id)
        uploadFile(param).then(res => {
          this.gridData = []
          if (res.code === "5000") {
            this.$message.warning(res.msg)
          } else if (res.code === "0000") {
            this.$message.success("导入成功")
            this.$refs.table.getTableData()
          }
        })
      },
      checkFile(row) {
        this.fileData = row
        selectFileList({ id: row.id, limit: 1000 }).then(res => {
          this.tableData = res.data.data
          this.pendingShow = true
        })
      },
      importFile2(params) {
        const param = new FormData()
        param.append("file", params.file)
        importEntryCheckData(param).then(res => {
          this.gridData = []
          if (res.code === "5000") {
            this.$message.warning(res.msg)
          } else if (res.code === "0000") {
            if (res.data.length > 0) {
              this.dialogTableVisible = true
              this.gridData = res.data
            } else {
              this.$message.success("导入成功")
            }
            this.$refs.table.getTableData()
          }
        })
      },
      //删除
      getDelete(row) {
        deleteEntryCheckData({ id: row.id }).then(res => {
          if (res.code == "0000") {
            this.$message.success("删除成功")
            this.$refs.table.$refs.table.clearSelection()
            this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
          } else {
            this.$message.error(`${res.msg || "删除失败"}`)
          }
        })
        // if (this.$refs.table.multipleSelection.length > 0) {
        //   console.log(this.$refs.table.multipleSelection)
        //   let ids = []
        //   this.$refs.table.multipleSelection.forEach(item => {
        //       ids.push(item.id)
        //   });
        //   deleteEntryCheckData({ids:ids.toString()}).then(res => {
        //     if (res.code == '0000') {
        //       this.$message.success("删除成功");
        //       this.$refs.table.$refs.table.clearSelection();
        //       this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        //     } else {
        //       this.$message.error(`${res.msg || '删除失败'}`)
        //     }
        //   })
        // } else {
        //   this.$message({
        //     showClose: true,
        //     message: '请至少勾选一条数据',
        //     type: 'warning'
        //   });
        // }
      },
      //新增
      getAddition() {
        console.log(this.$refs, "==")
        this.$refs["exemption"].open("外部录入考核事项")
      },
      //子调父方法
      onClose() {
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      //重置
      reset(form) {
        this.search(form)
      },
      //搜索
      search() {
        this.$refs.table.page.current = 1
        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        // this.loadParam.clockinDate = moment(this.loadParam.clockinDate).format('yyyy-MM-dd HH:mm:ss')
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      }
    },
    mounted() {
      let data = "success"
      console.log(data.length)
      // this.$set(this.searchConfig[6], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
    }
  }
</script>

<style scoped>
  .upload-btn {
    display: inline-block;
    margin: 0 10px;
  }
  /* Your component's CSS styles go here */
</style>
