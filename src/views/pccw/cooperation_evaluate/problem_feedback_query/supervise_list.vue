<template>
    <div>
      <SearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></SearchForm>
      <mssCard title="合作单位监督发现问题报表">
        <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              border
              selection
              :staticSearchParam="loadParam"
            >
            </mssTable>
        </div>
      </mssCard>
    </div>
</template>
<script>
import { dispatchQuery,  dispatchDelete,} from '@/api/pccw/analysis_link/key_processes/api'
import SearchForm from '@/views/pccw/cooperation_evaluate/searchForm.vue'
export default {
name: 'supervise_list',
components: {
  SearchForm
},
data() {
  return {
    loadParam: {},
    tableApi: dispatchQuery,
    //搜索字段配置
    searchConfig: [
      {
        label: '地市',
        type: 'select',
        fieldName: ''
      },
      {
        type: 'cycleDate',
        dateType: 'month',
        label: '问题发生月份',
        format: 'yyyy-MM',
        valueFormat: 'yyyy-MM',
        fieldName: '',
        clearable: false,
      },
      {
          label: '项目编码',
          type: 'input',
          fieldName: ''
      },
      {
          label: '任务编码',
          type: 'input',
          fieldName: ''
      },
      {
          label: '任务所属区县',
          type: 'input',
          fieldName: ''
      },
      {
          label: '问题单位',
          type: 'select',
          fieldName: ''
      }
    ],
    //表格头部配置
    tableHeader: [
        {
            prop: "",
            label: "地市",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题发生月份",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
         {
            prop: "",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
         {
            prop: "",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "任务编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "任务名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "任务所属区县",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "责任合作单位类型",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "责任合作单位名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题发生环节",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题单位",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题详情描述",
            align: "center",
            tooltip: true,
            minWidth: 150
        }
    ],
  };
},
methods: {
  //重置
  reset(form) {
      this.search(form)
  },
  //搜索
  search() {
      this.$refs.table.page.current = 1
      this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
},
mounted() {
  // this.$set(this.searchConfig[6], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
},
};
</script>

<style scoped>
/* Your component's CSS styles go here */
</style>
