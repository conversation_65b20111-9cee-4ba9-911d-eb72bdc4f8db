<template>
    <div>
      <SearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      >
      <el-button slot="customBtn" type="primary">提交审批</el-button>
      </SearchForm>
      <mssCard title="合作单位监督发现问题">
        <div slot="headerBtn" >
          <el-button  type="primary" @click="getAddition">新增</el-button>
          <el-button  type="primary" @click="getDelete">删除</el-button>
        </div>
        <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              border
              selection
              :staticSearchParam="loadParam">
            </mssTable>
        </div>
      </mssCard>
      <exemptionAdded @onClose='onClose' ref="exemption"></exemptionAdded>
    </div>
</template>
<script>
import {
  getOrderList,
} from "@/api/pccw/cooperation_evaluate/supervise";
import SearchForm from '@/views/pccw/cooperation_evaluate/searchForm.vue'
import exemptionAdded from "@/views/pccw/cooperation_evaluate/exemptionAdded.vue";
export default {
name: 'supervise_list',
components: {
  SearchForm,
  exemptionAdded
},
data() {
  return {
    loadParam: {},
    tableApi: getOrderList,
    //搜索字段配置
    searchConfig: [
      {
        label: '地市',
        type: 'select',
        fieldName: ''
      },
      {
        type: 'cycleDate',
        dateType: 'month',
        label: '问题发生月份',
        format: 'yyyy-MM',
        valueFormat: 'yyyy-MM',
        fieldName: '',
        clearable: false,
      },
      {
          label: '项目编码',
          type: 'input',
          fieldName: ''
      },
      {
          label: '任务编码',
          type: 'input',
          fieldName: ''
      },
      {
          label: '任务所属区县',
          type: 'input',
          fieldName: ''
      },
      {
          label: '问题单位',
          type: 'select',
          fieldName: ''
      }
    ],
    //表格头部配置
    tableHeader: [
        {
            prop: "",
            label: "建设单位",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题发生月份",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
         {
            prop: "",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
         {
            prop: "",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "任务编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "任务名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "任务所属区县",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "责任合作单位类型",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "责任合作单位名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题发生环节",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题大类",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题小类",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "问题描述",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "对责任合作单位考核问责情况",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "考核问责相关支撑材料",
            align: "center",
            tooltip: true,
            minWidth: 150
        }
    ],
  };
},
methods: {
  //删除
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      console.log(this.$refs.table.multipleSelection)
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      dispatchDelete({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  //新增
  getAddition () {
    console.log(this.$refs,'==')
    this.$refs['exemption'].open('合作单位监督发现问题')
  },
  //子调父方法
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
  },
  //重置
  reset(form) {
      this.search(form)
  },
  //搜索
  search() {
      this.$refs.table.page.current = 1
      this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
},
mounted() {
  // this.$set(this.searchConfig[6], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
},
};
</script>

<style scoped>
/* Your component's CSS styles go here */
</style>
