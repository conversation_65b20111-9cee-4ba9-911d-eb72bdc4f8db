<template>
    <div>
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <mssCard title="外部录入考核事项">
        <div slot="headerBtn" >
        <el-button  type="primary" @click="getDownload(2)">导出</el-button>
      </div>
      <div slot="content">
          <mssTable
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            border
            selection
            :staticSearchParam="loadParam">
          </mssTable>
      </div>
      </mssCard>
      <el-dialog
        title="附件列表"
        :visible.sync="pendingShow"
        :close-on-click-modal='false' 
        @close='pendingShow = false'
        width="50%">
        <div>
          <mssTable
            ref="attachment"
            :columns="tableHeader2"
            :stationary="tableData"
            :pagination="false"
            border>
          </mssTable>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="pendingShow = false">确 定</el-button>
          <el-button size="mini" style="margin-left: 10px" @click="pendingShow = false">取 消</el-button>
        </span>
      </el-dialog>
    </div>
</template>
<script>
import {
  selectFileList,
  downloadFile,
  getAllList
} from "@/api/pccw/cooperation_evaluate/external";
import { commonDown } from '@/utils/btn'
export default {
name: 'check_list',
data() {
  return {
    loadParam: {},
    tableApi: getAllList,
    //搜索字段配置
    searchConfig: [
        {
          label: '发起人',
          type: 'input',
          fieldName: 'creator'
        },
        {
          label: '发起人单位名称',
          type: 'input',
          fieldName: 'creatorCompany'
        },
        {
          label: '建设单位',
          type: 'input',
          fieldName: 'constructionUnit'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '合作单位名称',
          type: 'input',
          fieldName: 'partnerName'
        },
        {
          type: 'cycleDate',
          dateType: 'month',
          label: '问题录入月份',
          format: 'yyyy-MM',
          valueFormat: 'yyyy-MM',
          fieldName: 'entryMonth',
          clearable: false,
        }
    ],
    //表格头部配置
    tableHeader: [
        {
            prop: "creator",
            label: "发起人",
            align: "center",
            tooltip: true,
            minWidth: 130
        },
        {
            prop: "creatorCompany",
            label: "发起人单位名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "constructionUnit",
            label: "建设单位",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectNumber",
            label: "项目编码",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "partnerName",
            label: "合作单位名称",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "entryMonth",
            label: "问题录入月份",
            align: "center",
            tooltip: true,
            minWidth: 130
        },
        {
            prop: "problemDesc",
            label: "考核问题说明",
            align: "center",
            tooltip: true,
            minWidth: 150
        },
        {
            prop: "",
            label: "操作",
            align: "center",
            tooltip: true,
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
            return (
              <span>
                <span class="mr10">
                  <el-button
                    onClick= {() => {this.checkFile(row)}}
                    type="text">
                    查看附件
                  </el-button>
                </span>
              </span>
            );
            },
        }
    ],
    pendingShow: false,
    tableHeader2: [
      {
        prop: "fileName",
        label: '附件名称',
        align: "center",
        tooltip: true,
      },
      {
          prop: "",
          label: "操作",
          align: "center",
          tooltip: true,
          minWidth: 150,
          formatter: (row, column, cellValue, index) => {
          return (
            <span>
              <span class="mr10">
                <el-button type="text"  onClick={() => { this.getDownload(3, row) }}>下载附件</el-button>
              </span>
            </span>
          );
        },
      }
    ],
    tableData: [],
    fileData: {},
  };
},
methods: {
  getDownload (val, row) {
    if (val === 2) {
      commonDown({...this.$refs.searchForm.searchForm, limit: -1}, exportList);
    } else if (val === 3) {
      commonDown({ ...row, limit: -1}, downloadFile);
    }
  },
  checkFile (row) {
    this.fileData = row
    selectFileList({ id: row.id,limit: 1000 }).then((res)=>{
      this.tableData = res.data.data
      console.log(this.tableData, '===')
      this.pendingShow = true
    })
  },
  //重置
  reset(form) {
      this.search(form)
  },
  //搜索
  search() {
      this.$refs.table.page.current = 1
      this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
},
mounted() {
  // this.$set(this.searchConfig[6], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
},
};
</script>

<style scoped>
.upload-btn {
  display: inline-block;
  margin: 0 10px;
}
/* Your component's CSS styles go here */
</style>
