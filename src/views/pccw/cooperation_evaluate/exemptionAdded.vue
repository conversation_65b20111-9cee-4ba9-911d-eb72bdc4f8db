<template>
  <div>
    <el-dialog
      :title="title+'新增'"
      :visible.sync="dialogVisible"
      :close-on-click-modal='false' 
      @close='onClose'
      width="85%">
      <mssCard :title="title">
        <div slot="headerBtn">
          <el-button type="primary" @click="addTable">新增</el-button>
        </div>
        <div slot="content">
          <el-form ref="exemptionList" :model="form" :rules="rules">
            <mssTable
              ref="table"
              selection
              :serial="false"
              :stationary="tableData"
              :columns="tableHeader"
              :pagination="false"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
    </el-dialog>
  </div>
</template>
<script>
import {
  saveEntryCheckData
} from "@/api/pccw/cooperation_evaluate/external";
import { lpsCriticalProcessExemptAdd, lpsConstructionDispatchAdd, getProjectNameByCode} from '@/api/pccw/analysis_link/key_processes/api'
export default {
  name: 'exemptionAdded',
  data () {
    return {
      dialogVisible: false,
      tableData: [],
      form: {},
      rules: {},
      unitList:[],
      title: '',
    }
  },
  methods: {
    open (name) {
      this.unitList = JSON.parse(window.sessionStorage.getItem('constructionUnit'))
      this.title = name
      this.tableData = []
      this.dialogVisible = true
    },
    onClose () {
      this.$emit('onClose')
      this.dialogVisible = false
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.exemptionList.validate((validate) => {
          if (validate) {
              let data = {
                ...row,
                taskCreateTime: this.$moment(row.taskCreateTime).format('YYYY-MM-DD')
              }
              if (this.title === '合作单位监督发现问题') {
                lpsConstructionDispatchAdd(data).then((res) => {
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                });
              } else if (this.title === '外部录入考核事项') {
                let data = {
                  ...row,
                  entryMonth: this.$moment(row.entryMonth).format('YYYY-MM')
                }
                saveEntryCheckData(data).then((res) => {
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                });
              }
          } else {
            return;
          }
        });
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      var a = [];
      if (this.title === '合作单位监督发现问题') {
        
      } else if (this.title === '外部录入考核事项') {
        a = [
          'constructionUnit',
          'projectNumber',
          'projectName',
          'partnerName',
          'entryMonth',
          'problemDesc',
          'taskCode',
        ];
      }
      a.forEach((item) => {
        if (arr[item] === "" || arr[item] === null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          }
        } else {
          this.rules[`${item}${index}`] = {}
        }
      });
    },
    addTable() {
      if (this.title === '合作单位监督发现问题') {
        this.tableData.push({
          projectNo: '',
          taskCode: '',
          taskCreateTime: '',
          taskStatus: '',
          constructUnit: ''
        })
      } else if (this.title === '外部录入考核事项') {
        this.tableData.push({
          creator: '',
          creatorCompany: '',
          constructionUnit: '',
          projectNumber: '',
          projectName: '',
          partnerName: '',
          entryMonth: '',
          problemDesc: ''
        })
      }
    },
    getproject (row, index) {
      let param = {
        projectCode: row
      }
      getProjectNameByCode(param).then((res)=>{
        if (res.code === '0000') {
          console.log(res.data)
          this.$set(this.tableData[index], 'projectName', res.data)
        }
      })
    },
  },
  computed: {
    tableHeader: {
      get() {
        if (this.title === '合作单位监督发现问题') {
          return [
            {
              prop: "constructionUnit",
              label: "建设单位",
              minWidth: 200,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`constructionUnit${index}`}>
                    <el-select
                      v-model={row.constructionUnit}
                    >
                      {this.unitList.length &&
                        this.unitList.map((item) => {
                          return (
                            <el-option label={item.label} value={item.value} />
                          );
                        })}
                    </el-select>
                  </el-form-item>
                );
              },
            },
            {
              prop: "entryMonth",
              label: "发生问题月份",
              minWidth: 200,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`entryMonth${index}`} >
                    <el-date-picker 
                      style="width: 90%"
                      v-model={row.entryMonth}
                      type="date"
                    >
                    </el-date-picker>
                  </el-form-item>
                );
              },
            },
            {
              prop: "projectNumber",
              label: "项目编码",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`projectNumber${index}`}>
                    <el-input 
                      v-model={row.projectNumber}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "projectName",
              label: "项目名称",
              minWidth: 130,
            },
            {
              prop: "taskCode",
              label: "任务编码",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskCode${index}`}>
                    <el-input 
                      v-model={row.taskCode}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "taskName",
              label: "任务名称",
              minWidth: 130,
            },
            {
              prop: "taskCode",
              label: "任务所属区县",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskCode${index}`}>
                    <el-input 
                      v-model={row.taskCode}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "taskCode",
              label: "责任合作单位类型",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskCode${index}`}>
                    <el-input 
                      v-model={row.taskCode}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "taskCode",
              label: "责任合作单位名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskCode${index}`}>
                    <el-input 
                      v-model={row.taskCode}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "taskCode",
              label: "问题发生环节",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskCode${index}`}>
                    <el-input 
                      v-model={row.taskCode}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "taskCode",
              label: "问题大类",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskCode${index}`}>
                    <el-input 
                      v-model={row.taskCode}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "taskCode",
              label: "问题小类",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskCode${index}`}>
                    <el-input 
                      v-model={row.taskCode}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "taskCode",
              label: "问题详情描述",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskCode${index}`}>
                    <el-input 
                      v-model={row.taskCode}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ]; 
        } else if (this.title === '外部录入考核事项') {
          return [
            {
              prop: "constructionUnit",
              label: "建设单位",
              minWidth: 200,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`constructionUnit${index}`}>
                    <el-select
                      v-model={row.constructionUnit}
                      onChange={(value) => {
                        this.form[`constructionUnit${index}`] = value;
                      }}
                      >
                      {this.unitList.length &&
                        this.unitList.map((item) => {
                          return (
                            <el-option label={item.label} value={item.value} />
                          );
                        })}
                    </el-select>
                  </el-form-item>
                );
              },
            },
            {
              prop: "projectNumber",
              label: "项目编码",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`projectNumber${index}`}>
                    <el-input 
                      v-model={row.projectNumber}
                      onBlur={(val)=>{
                        this.getproject(row.projectNumber, index)
                      }}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "projectName",
              label: "项目名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`projectName${index}`}>
                    <el-input 
                      disabled
                      v-model={row.projectName}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "taskName",
              label: "任务名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`taskName${index}`}>
                    <el-input 
                      v-model={row.taskName}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "partnerName",
              label: "合作单位名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`partnerName${index}`}>
                    <el-input 
                      v-model={row.partnerName}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "entryMonth",
              label: "问题录入月份",
              minWidth: 200,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`entryMonth${index}`} >
                    <el-date-picker 
                      style="width: 90%"
                      v-model={row.entryMonth}
                      type="month"
                      format='yyyy-MM'
                      valueFormat='yyyy-MM'
                    >
                    </el-date-picker>
                  </el-form-item>
                );
              },
            },
            {
              prop: "problemDesc",
              label: "考核问题说明",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`problemDesc${index}`}>
                    <el-input 
                      v-model={row.problemDesc}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ];
        }
      },
    },
  },
} 
</script>
