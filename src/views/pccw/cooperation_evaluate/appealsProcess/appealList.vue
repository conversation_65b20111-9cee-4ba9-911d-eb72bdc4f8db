<template>
  <div>
    <el-dialog
      title="附件列表"
      :visible.sync="dialogVisible1"
      :close-on-click-modal='false' 
      @close='dialogVisible1 = false'
      width="95%">
      <div>
        <mssCard title="">
          <div slot="headerBtn">
            <el-button  type="primary" @click="dialogVisible2 = true">保 存</el-button>
            <el-button  >取 消</el-button>
          </div>
          <div slot="content">
            <TableList
              :serial='false'
              ref="attachment"
              :columns="tableHeader1"
              :stationary="tableData1"
              :pagination="false"
              border>
            </TableList>
          </div>  
        </mssCard>
      </div>
    </el-dialog>
    <el-dialog
      title="填写申诉信息"
      :visible.sync="dialogVisible2"
      :close-on-click-modal='false' 
      @close='dialogVisible2 = false'
      width="70%">
      <div>
        <el-card class="card">
          <el-descriptions title=""  :column="2" border>
            <el-descriptions-item label="考核项目">{{searchData.businessId}}</el-descriptions-item>
            <el-descriptions-item label="考核内容">{{searchData.businessName}}</el-descriptions-item>
            <el-descriptions-item label="标准以及要求" >{{searchData.createdTime}}</el-descriptions-item>
            <el-descriptions-item label="扣分说明">{{searchData.endTime}}</el-descriptions-item>
            <el-descriptions-item label="分值">{{searchData.projectCode}}</el-descriptions-item>
            <el-descriptions-item label="扣分事项说明">{{searchData.projectName}}</el-descriptions-item>
            <el-descriptions-item label="出现次数">{{searchData.projectName}}</el-descriptions-item>
            <el-descriptions-item label="扣分值">{{searchData.projectName}}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions title=""  :column="1" border>
            <el-descriptions-item label="申诉说明">
              <el-input
                style="width: 90%;"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                resize='none'
                v-model="searchData.textarea">
              </el-input>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions title=""  :column="1" border>
            <el-descriptions-item label="附件上传">
              <el-upload
                style="display: inline-block;margin-left: 10px;"
                ref="newFile"
                class="upload-btn"
                action="string"
                :show-file-list="false"
                :auto-upload="true"
                :http-request="importFile"
              >
                <el-button type="primary">点击上传</el-button>
              </el-upload>
          </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary">确 定</el-button>
        <el-button size="mini" style="margin-left: 10px" >取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import TableList from "./table.vue";
export default {
  components: {
    TableList
  },
  data () {
    return {
      dialogVisible1: true,
      dialogVisible2: false,
      tableData1: [],
      tableHeader1: [{
        prop: "",
        label: '通信建设工程勘察设计服务项目考评表(实施验收阶段-权重30%)',
        multilevelColumn: [
          {
            label: '考核项目',
            prop: '',
            minWidth: 200,
          },
          {
            label: '序号',
            prop: '',
            minWidth: 100,
          },
          {
            label: '考核内容',
            prop: '',
            minWidth: 200,
          },
          {
            label: '标准以及要求',
            prop: '',
            minWidth: 200,
          },
          {
            label: '扣分说明',
            prop: '',
            minWidth: 200,
          },
          {
            label: '分值',
            prop: '',
            minWidth: 100,
          },
          {
            label: '扣分事项说明',
            prop: '',
            minWidth: 200,
          },
          {
            label: '出现次数',
            prop: '',
            minWidth: 100,
          },
          {
            label: '扣分值',
            prop: '',
            minWidth: 100,
          },
          {
            label: '申诉说明',
            prop: '',
            minWidth: 200,
          },
          {
            label: '申诉附件',
            prop: '',
            minWidth: 200,
            formatter: (row) => {
              return (
                  <el-button
                      type="text"
                      onClick={() => {
                      this.saveONe(row)}}>
                      查看
                  </el-button>
              )
            }
          },
          {
            label: '操作',
            prop: '',
            minWidth: 200,
            formatter: (row) => {
              return (
                  <el-button
                      type="text"
                      onClick={() => {
                      this.saveONe(row)}}>
                      申诉
                  </el-button>
              )
            }
          }
        ]
      }],
      searchData: {},
    }
  },
  methods: {
    saveONe () {
      
    },
    importFile() {
      
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .is-bordered-label{
  width: 40% !important;
}
</style>