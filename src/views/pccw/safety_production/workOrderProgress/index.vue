<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
          <div slot="headerBtn" >
            <el-button  type="primary" @click="exportMethod">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader"
              :staticSearchParam="loadParam"
              border>
            </mssTable>
          </div>
        </mssCard>
        <el-dialog
          title="操作"
          :visible.sync="pendingShow"
          :close-on-click-modal='false' 
          @close='pendingShow = false'
          width="30%">
          <div>
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              disabled
              resize='none'
              v-model="textarea">
            </el-input>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary" @click="submitOrder">确 定</el-button>
            <el-button size="mini" style="margin-left: 10px" @click="pendingShow = false">取 消</el-button>
          </span>
        </el-dialog>
    </div>
</template>

<script>

import { getProductionFormList, sendWaitForReading, exportFormList } from '@/api/pccw/safety_production/safetyWork'
import { commonDown } from '@/utils/btn';
export default {
  name: 'workOrderProgress',
  data() {
    return {
        loadParam:{},
        tableApi: getProductionFormList,
        searchConfig:[
          {
              label: '工单发起月份',
              type: "monthrange",
              format:'yyyy-MM',
              valueFormat:'yyyy-MM',
              fieldName: "date",
          },
        ],
        // 表头
        tableHeader: [
          {
            prop: "fillingMonth",
            label: "工单发起月份",
            align: "center",
            tooltip: true,
          },
          {
            prop: "unitName",
            label: "合作单位名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "status",
            label: "状态",
            align: "center",
            tooltip: true,
          },
          {
            prop: "unitInterfacePerson",
            label: "合作单位接口人",
            align: "center",
            tooltip: true,
          },
          {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '120px',
          formatter: (row) => {
            return (
                <span>
                    <span class="mr10">
                    {
                      row.status == '未反馈' ? <el-button
                        type="text"
                        onClick={() => {
                        this.saveONe(row)}}>
                        催办
                    </el-button> : ''
                    }
                    </span>
                </span>
            )
          }
        }
        ],
        pendingShow: false,
        textarea: '任务即将超时，请尽快处理!',
        form: {} 
    };
  },
  methods: {
    exportMethod () {
      const params = {
      ...this.$refs.searchForm.searchForm,
      }
      commonDown(params, exportFormList)
    },
    submitOrder () {
      console.log(this.form)
      sendWaitForReading(this.form).then((res)=>{
        if (res.code == '0000' || res.code == '0000') {
          this.$message({
            message: '催办成功',
            type: 'success'
          });
          this.pendingShow = false
        } else {
          this.$message.error(res.data)
        }
      })
    },
    saveONe (row) {
      this.form = {
        person: row.person,
        personId: row.attr3,
        unitInterfacePerson: row.unitInterfacePerson,
        reportTime: row.fillingMonth
      }
      this.pendingShow = true
    },
    //重置
    reset(form) {
        this.search(form)
    },
    //搜索
    search() {
        let data = {
          ...this.$refs.searchForm.searchForm,
          startTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[0] :  "",
          endTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[1] :  ""
        }
        this.$refs.table.page.current = 1
        this.$refs.table.getTableData(data);
    },
  },
  mounted() {
      // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your component-specific styles go here */
</style>
