<template>
  <div>
    <el-dialog
        title="附件列表"
        :visible.sync="dialogVisible"
        :close-on-click-modal='false' 
        @close='onClose'
        width="30%">
        <mssTable
          ref="table"
          :columns="attachment"
          border
          :stationary="fileData"
        >
        </mssTable>
      </el-dialog>
  </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
export default {
  name: 'attachmentList',
  data () {
    return {
      dialogVisible: false,
      attachment: [
        {
          prop: "fileName",
          label: "附件名称",
          align: "center",
          tooltip: true,
          width: 150
        },
        {
          label: "操作",
          prop: "_caozuo",
          fixed: "right",
          minWidth: 100,
          formatter: (row, column, cellValue, index) => {
            return (
              <span>
                <span class="mr10">
                  <el-button
                    type="text"
                    onClick={() => {
                      this.downloadFile(row, 1);
                    }}
                  >
                    下载文件
                  </el-button>
                </span>
              </span>
            );
          },
      },
      ],
      fileData: [],
    }
  },
  methods: {
    open (row) {
      this.fileData = row
      this.dialogVisible = true
    }
  }
}
</script>

