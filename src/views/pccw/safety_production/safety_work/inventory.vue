<template>
    <div>
        <mssSearchForm
          ref="searchForm"
          :search-config="searchConfig"
          @reset="reset"
          @search="search"
        ></mssSearchForm>
        <!--结果展示框-->
        <mssCard title="查询结果">
          <div slot="headerBtn">
            <el-button  type="primary" @click="exportMethod">批量导出附件</el-button>
            <el-button type="primary" @click="exportHandle">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              ref="table"
              :api="tableApi"
              :columns="tableHeader1"
              :staticSearchParam="loadParam"
              border
            >
            </mssTable>
          </div>
        </mssCard>
        <el-dialog
          title="附件列表"
          :visible.sync="pendingShow"
          :close-on-click-modal='false' 
          @close='pendingShow = false'
          width="60%">
          <div>
            <mssTable
              ref="attachment"
              :columns="tableHeader2"
              :stationary="tableData"
              :pagination="false"
              border>
            </mssTable>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary" @click="pendingShow = false">确 定</el-button>
            <el-button size="mini" style="margin-left: 10px" @click="pendingShow = false">取 消</el-button>
          </span>
        </el-dialog>
    </div>
</template>

<script>

import { commonDown } from '@/utils/btn'
import { getDetailByPostResponsibility, exportDetailByPostResponsibility, exportMaterialByPostResponsibility, exportAllMaterial } from '@/api/pccw/safety_production/safetyWork'
import { queryAreaListService } from "@/api/common_api"
export default {
  name: 'inventory',
  data() {
    return {
        loadParam:{},
        tableApi: getDetailByPostResponsibility,
        searchConfig:[
          {
              label: '合作单位名称',
              type: 'input',
              fieldName: 'unitName'
          },
          {
              label: '安全工作填报月份',
              type: "monthrange",
              format:'yyyy-MM',
              valueFormat:'yyyy-MM',
              fieldName: "date",
          },
          {
              label: '地市',
              type: 'select',
              fieldName: 'city'
          }
        ],
        // 表头
        tableHeader1: [
          {
            prop: "city",
            label: "地市",
            align: "center",
            tooltip: true,
          },
          {
            prop: "unitName",
            label: "合作单位名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "fillingMonth",
            label: "填报月份",
            align: "center",
            tooltip: true,
          },
          {
            prop: "jobTitle",
            label: "岗位名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "safetyDuty",
            label: "安全职责",
            align: "center",
            tooltip: true,
          },
          {
            prop: "dutyContent",
            label: "履职内容",
            align: "center",
            tooltip: true,
          },
          {
            prop: "dutyTimes",
            label: "履职频次",
            align: "center",
            tooltip: true,
          },
          {
            prop: "remark",
            label: "备注",
            align: "center",
            tooltip: true,
          },
          {
            prop: "monthFlag",
            label: "本月是否涉及",
            align: "center",
            tooltip: true,
          },
          {
            prop: "finishCondition",
            label: "完成情况",
            align: "center",
            tooltip: true,
          },
          {
            prop: "fileName",
            label: "支撑材料",
            align: "center",
            tooltip: true,
          },
          {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '120px',
          formatter: (row) => {
            return (
                <span>
                  {row.fileName ? <el-button
                      type="text"
                      onClick={() => {
                      this.saveONe(row)}}>
                      查看支撑材料
                  </el-button> : ''}
                </span>
            )
          }
          }
        ],
        pendingShow: false,
        tableHeader2: [
          {
            prop: "fileName",
            label: "支撑材料",
            align: "center",
            tooltip: true,
          },
          {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '120px',
          formatter: (row) => {
            return (
                <span>
                  <el-button
                      type="text"
                      onClick={() => {
                      this.deleteTable(row)}}>
                      导出材料
                  </el-button>
                </span>
            )
          }
          }
        ],
        tableData: []     
    };
  },
  methods: {
    saveONe (row) {
      this.tableData = []
      let data = row.fileName.split(",")
      let fileId = row.fileId.split(",")
      data.forEach((item,index) => {
        this.tableData.push({
          fileName: item,
          city: row.city,
          unitName: row.unitName,
          reportTime: row.fillingMonth,
          fileId: fileId[index]
        })
      });
      this.pendingShow = true
    },
    deleteTable (row) {
      commonDown({ ...row, limit: -1, fileId: row.fileId}, exportMaterialByPostResponsibility);
    },
    getAreaList(parentId, index) {
      queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
          if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
              list.push({ label: item.name, value: item.name })
          })
          this.$set(this.searchConfig[index], 'options', list)
          }
      })
    },
    exportMethod () {
    commonDown({ ...this.loadParam, limit: -1, templateType: 1, unitType: '合作单位岗位责任清单履职'}, exportAllMaterial);
  },
    //导出
    exportHandle() {
        commonDown({ ...this.loadParam, limit: -1, exportType: 1}, exportDetailByPostResponsibility);
    },
    //重置
    reset(form) {
        this.search(form)
    },
    //搜索
    search() {
        this.$refs.table.page.current = 1
        let data = {
        ...this.$refs.searchForm.searchForm,
        startTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[0] :  "",
        endTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[1] :  ""
      }
        this.loadParam = data
        this.$refs.table.getTableData(data);
    },
  },
  mounted() {
    this.getAreaList('-2', 2)
  },
};
</script>

<style scoped>
/* Your component-specific styles go here */
</style>
