<template>
    <div v-loading="loading"> 
      <div class="operate-btn">
        <el-button style="text-align: right" type="primary" slot="custom-buttons" @click="savePage" >下发</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
      <div class="safety_work">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane :label="item.label" :name="item.name" v-for="(item, index) in list" :key="index">
              <mssCard title="查询结果">
                <div slot="content">
                  <mssTable
                    :ref="item.ref"
                    :columns="item.tableHeader"
                    :stationary="tableData"
                    border>
                  </mssTable>
                </div>
              </mssCard>
            </el-tab-pane>
          </el-tabs>
        </div> 
    </div>
</template>
<script>
import { getSafetyWorkTableName, getSafetyWorkPending, beginSafetyWorkFlow } from '@/api/pccw/safety_production/safetyWork'
export default {
    name: 'taskList',
    data(){
        return {
          activeName: '1',
          list: [],
          tableData: [],
          showApproval: false, //提交
          showReturn: false, //退回
          loading: false
        }
    },
    methods: {
      tableName () {
        let data = {
          unitType: this.$route.query.unitType,
          templateType: this.$route.query.templateType
        }
        getSafetyWorkTableName(data).then((res)=> {
          const val = res.data 
          this.list = []
          this.activeName = val[0].sheetName
          val.forEach((item, index) => {
            var a = []
            this.list.push({
              label: item.sheetRealName,
              name: item.sheetName,
              ref: `table${index}`,
              tableHeader: []
            })
            item.labelList.forEach((arr, i) => {
              a.push({
                prop: item.propList[i],
                label: arr,
                align: "center",
                minWidth: 150,
                tooltip:true
              })
            });
            this.list[index].tableHeader = a
          });
        })
      },     
      handleClick () {
        this.resultList()
        console.log(this.activeName);
      },
      resultList () {
        let data = {
          unitType: this.$route.query.unitType,
          templateType: this.$route.query.templateType,
          sheetName: this.activeName
        }
        getSafetyWorkPending(data).then((res)=>{
          this.tableData = res.data
          console.log(this.tableData, '=')
        })
      },
      savePage () {
        this.$confirm('请核查《合作单位及安全接口人》菜单中配置的合作单位是否准确无误，如需修改，请完成修改后再下发收集任务', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let data ={
            unitType: this.$route.query.unitType,
            templateType: this.$route.query.templateType
          }
           this.loading = true
           beginSafetyWorkFlow(data).then((res) => {
             this.$message.success('下发成功')
             this.$router.push({
                path: '/home'
             }).then(() => {
                this.$router.go(0); // 刷新当前页面
             });
           })
        }).catch(() => {
          this.loading = false
          this.$message({
            type: 'info',
            message: '已取消'
          });          
        });
      },  
      goBack() {
        this.$router.go(-1); // 返回上一页
      },
    },
    mounted () {
      this.tableName ()
      this.resultList ()
    }
}
</script>

<style scoped>
/* Your component's CSS code goes here */
</style>
