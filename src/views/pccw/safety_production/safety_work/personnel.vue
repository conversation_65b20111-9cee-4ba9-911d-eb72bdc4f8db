<template>
  <div class="design_warning">
    <mssCard title="合作单位填报安全生产工作人员配置界面">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable" v-if="jurisdiction">新增</el-button>
        <!-- <el-button @click="del">删除</el-button> -->
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
        </mssTable>
        </el-form>
      </div>
    </mssCard>
    <magnifyingGlass ref="magnifyingGlass" @showCheckList="showCheckList" :mult-select="multSelect"  :key="componentKey"></magnifyingGlass>
  </div>
</template>

<script>
import { queryConfig, addConfig, deleteConfig, getConfig, checkIsSystemAdmin } from '@/api/pccw/safety_production/safetyWork'
import { commonMultDel, commonOneDel } from "@/utils/btn";
import { queryAreaListService } from "@/api/common_api"
import magnifyingGlass from "@/views/pccw/components/magnifyingGlass/index.vue";
export default {
  name: "receivingPersonnel",
  components: {
    magnifyingGlass
  },
  data() {
    return {
      tableData: [],
      form: {},
      rules: {},
      urationList: [],
      multSelect: false,
      cityList: [],
      jurisdiction: false,
       componentKey: 0,  // 初始化 key
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "city",
            label: "地市",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`city${index}`}>
                  <el-select
                    disabled= {!this.jurisdiction}
                    v-model={row.city}
                    onChange={(value) => {
                      this.form[`city${index}`] = value;
                    }}
                  >
                    {this.cityList.length &&
                      this.cityList.map((item) => {
                        return (
                          <el-option label={item.label} value={item.label} />
                        );
                      })}
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            prop: "citySafetyAdmin",
            label: "地市安全管理员",
            minWidth:150,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`citySafetyAdmin${index}`}>
                  <el-input disabled= {!this.jurisdiction}  v-model={row.citySafetyAdmin} readonly onFocus={() => {
                    if (row.city == '' || row.city == null) {
                      this.$message({
                        showClose: true,
                        message: '请先选地市',
                        type: 'warning'
                      })
                    } else {
                      this.openChooseUserDailog(row, index, '地市安全管理员')
                    }
                  }}>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "unitType",
            label: "合作单位类型",
            minWidth: 150,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`unitType${index}`}>
                  <el-select
                    v-model={row.unitType}
                  >
                    <el-option label='设计' value='设计' />
                    <el-option label='施工' value='施工' />
                    <el-option label='监理' value='监理' />
                  </el-select>
                </el-form-item>
              );
            },
          },
          {
            prop: "unitName",
            label: "合作单位名称",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`unitName${index}`}>
                  <el-input  v-model={row.unitName}
                  readonly onFocus={() => {
                    if (row.unitType == '' || row.unitType == null) {
                      this.$message({
                        showClose: true,
                        message: '请先选合作单位类型',
                        type: 'warning'
                      })
                    } else {
                      this.openChooseUserDailog(row, index, '合作单位名称')
                    }
                  }}>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "supervisor",
            label: "监理员",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`supervisor${index}`}>
                  <el-input  v-model={row.supervisor}
                  readonly onFocus={() => {
                    if (row.unitType == '' || row.unitType == null) {
                      this.$message({
                        showClose: true,
                        message: '请先选择合作单位名称',
                        type: 'warning'
                      })
                    } else {
                      this.openChooseUserDailog(row, index, '监理员姓名')
                    }
                  }}>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "unitInterfacePerson",
            label: "合作单位安全接口人",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`unitInterfacePerson${index}`}>
                  <el-input  v-model={row.unitInterfacePerson}
                  readonly onFocus={() => {
                    if (row.unitType == '' || row.unitType == null) {
                      this.$message({
                        showClose: true,
                        message: '请先选择合作单位名称',
                        type: 'warning'
                      })
                    } else {
                      this.openChooseUserDailog(row, index, '合作单位安全接口人')
                    }
                  }}>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "feedbackTime",
            label: "反馈时间",
            minWidth: 200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`feedbackTime${index}`}>
                  <el-date-picker
                    v-model={row.feedbackTime}
                    type="date"
                    placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  {
                    this.jurisdiction === true ? <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row)}}>
                      删除
                    </el-button>
                  </span> : ''
                  }
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.gteCheckIsSystemAdmin()
    this.getAreaList('-2', 2)
    this.getTable();
  },
  methods: {
    gteCheckIsSystemAdmin(){
      checkIsSystemAdmin().then((res)=>{
        this.jurisdiction = res.data
      })
    },
    getAreaList(parentId, index) {
      queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
          if (res.code === '0000') {
            res.data.forEach(item => {
                this.cityList.push({ label: item.name, value: item.name })
            })
          }
      })
    },
    showCheckList (val) {
       this.componentKey += 1;
      console.log("val",val)
      if (val.deptParams == '合作单位名称') {
        this.$set(this.tableData[val.index], 'unitName' , val.checkList[0].realName)
        this.$set(this.tableData[val.index], 'unitId' , val.checkList[0].userId)
        this.$set(this.tableData[val.index], 'unitInterfacePerson' , '')
      } else if (val.deptParams == '合作单位安全接口人') {
        this.$set(this.tableData[val.index], 'unitInterfacePerson' , val.checkList[0].realName)
        this.$set(this.tableData[val.index], 'attr2' , val.checkList[0].attr2)
      } else if (val.deptParams == '监理员姓名') {
        this.$set(this.tableData[val.index], 'supervisor' , val.checkList[0].realName)
        this.$set(this.tableData[val.index], 'supervisorId' , val.checkList[0].attr2)
      } else {
        this.$set(this.tableData[val.index], 'citySafetyAdmin' , val.checkList[0].realName)
        this.$set(this.tableData[val.index], 'attr1' , val.checkList[0].attr1)
      }
      this.$set(this.tableData[val.index], 'name' , val.checkList[0].realName)
    },
    openChooseUserDailog (row, index, deptParams) {
      console.log("row",row)
      console.log("index",index) // 标识哪一行
      console.log("deptParams",deptParams)
      let item = {}
      if (deptParams == '合作单位名称') {
        console.log(row.unitType)
        item = {
          excuterNames: row.unitName,
          excuterIds: row.unitName,
          units: row.unitType
        }
      } else if (deptParams == '合作单位安全接口人') {
        item = {
          excuterNames: row.unitInterfacePerson,
          excuterIds: row.unitInterfacePerson,
          units: row.unitName,
        }
      } else if (deptParams == '监理员姓名') {
        item = {
          excuterNames: row.supervisor,
          excuterIds: row.supervisorId,
          units: row.unitName,
        }
      } else {
        item = {
          excuterNames: row.citySafetyAdmin,
          excuterIds: row.citySafetyAdmin,
          units: row.city
        }
      }
      this.$refs.magnifyingGlass.init(item, deptParams, index)
    },
    getTable() {
      queryConfig({}).then((res) => {
        if (res.code == "0000") {
          this.tableData  = res.data || []
        }
      });
    },
    addTable() {
      let data = {
        city: '',
        citySafetyAdmin: '',
        unitType: '',
        unitName: '',
        unitInterfacePerson: '',
        feedbackTime: '',
        userId: '',
        attr1: ''
      }
      addConfig(data).then((res)=>{
        if (res.code == "0000") {
          this.tableData.push({ ...data, id: res.data});
        }
      })
    },
    deleteTable(row, index) {
      this.$confirm('是否确认删除这条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // if (!row.id) {
          //   this.tableData.splice(index, 1)
          //   this.$message.success("删除成功");
          // } else {
            deleteConfig({ids:row.id}).then(res => {
              if (res.code == '0000') {
                  this.$message.success("删除成功");
                  this.getTable();
                } else {
                  this.$message.error(`${res.msg || '删除失败'}`)
                }
            })
          // }
        }).catch(() => {})
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            let obj = JSON.parse(JSON.stringify(row));
            addConfig(obj).then((res) => {
              if (res.code == "0000") {
                this.$message.success("保存成功");
              } else {
                this.$message.error(res.msg);
              }
            });
          } else {
            return;
          }
        });
      });
    },
    // 删除
    del() {
      console.log(this.$refs.table.multipleSelection)
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: deleteUserConfig,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "city",
        "citySafetyAdmin",
        "unitType",
        "unitName",
        "unitInterfacePerson",
        "feedbackTime",
        "supervisor"
      ];
      a.forEach(item => {
        if (arr[item] === '' || arr[item] == null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          this.rules[`${item}${index}`] = {}
        }
      });
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


