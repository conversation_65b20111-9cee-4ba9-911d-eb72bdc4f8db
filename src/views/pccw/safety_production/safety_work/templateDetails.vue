<template>
    <div> 
      <div class="operate-btn">
        <!-- <el-button style="text-align: right" type="primary" slot="custom-buttons" @click="savePage" >修改</el-button> -->
        <el-button style="text-align: right" type="primary" slot="custom-buttons" @click="exportHandle" >导出</el-button>
        <el-button @click="goBack">返回</el-button>
    </div>
      <div class="safety_work">
        <mssCard :title="title">
          <div slot="content">
             <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane :label="item.label" :name="item.name" v-for="(item, index) in list" :key="index">
              <div class="commonTable base-table">
                <el-table
                :data="item.tableData"
                class="table">
                  <template v-for="(column) in item.tableHeader">
                    <el-table-column 
                      :align="column.align || 'center'"
                      :prop="column.prop"
                      :label="column.label || ''"
                      min-width="150"
                      :key="column.prop"
                      :show-overflow-tooltip="true"
                      >
                        <template slot-scope="scope">
                          <el-input v-model="scope.row[column.prop]" type="textarea" :rows="4" resize='none'></el-input>
                        </template>
                    </el-table-column>
                  </template>
                  <el-table-column prop="" label="操作" align="center">
                      <template slot-scope="scope">
                          <el-button type="text" @click="savePage(scope.row)">
                              修改
                          </el-button>
                      </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
          </div>
        </mssCard>
        </div> 
    </div>
</template>
<script>
import { getSafetyWorkTableName, queryTemplate, exportTemplate, updateTemplate } from '@/api/pccw/safety_production/safetyWork'
export default {
    name: 'templateDetails',
    data(){
        return {
          activeName: '',
          list: [],
          tableData: [],
          showApproval: false, //提交
          showReturn: false, //退回
          form: {},
          rules: {},
          testData: [],
          title: ''
        }
    },
    methods: {
      exportHandle () {
        let form = {
          unitType: this.$route.query.unitType,
          templateType: this.$route.query.templateType
        }
        exportTemplate(form).then(res => {
          let fileName = "";
          fileName = `${this.$route.query.detail}.xlsx`;
          const url = window.URL.createObjectURL(new Blob([res.data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', fileName);
          document.body.appendChild(link);
          link.click();
        })
      },
      tableName () {
        let data = {
          unitType: this.$route.query.unitType,
          templateType: this.$route.query.templateType
        }
        getSafetyWorkTableName(data).then((res)=> {
          const val = res.data 
          this.list = []
          this.activeName = val[0].sheetName
          val.forEach((item, index) => {
            var a = []
            this.list.push({
              label: item.sheetRealName,
              name: item.sheetName,
              ref: `table${index}`,
              tableHeader: [],
              tableData: []
            })
            item.labelList.forEach((arr, i) => {
              a.push({
                prop: item.propList[i],
                label: arr,
                align: "center",
                minWidth: 150,
                tooltip: true,
              })
            });
            this.$set(this.list[index],'tableHeader', a)
          });
        })
      },     
      handleClick () {
      },
      resultList () {
        let data = {
          unitType: this.$route.query.unitType,
          templateType: this.$route.query.templateType,
          sheetName: this.activeName
        }
        queryTemplate(data).then((res) => {
          console.log(res.data)
          console.log(Object.keys(res.data))
          Object.keys(res.data).forEach(item => {
              this.list.forEach((val,i) => {
                if (this.list[i].label == item) {
                this.list[i].tableData = res.data[item]
                }
            })
          })
        })
      },
      savePage (row) {
        console.log(this.list);
        // this.$confirm('请核查《合作单位及安全接口人》菜单中配置的合作单位是否准确无误，如需修改，请完成修改后再下发收集任务?', '提示', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {
          let data ={
            ...row,
            unitType: this.$route.query.unitType,
            templateType: this.$route.query.templateType
          }
           updateTemplate(data).then((res) => {
             if (res.code == '0000') {
                this.$message.success('修改成功');
             } else {
                this.$message.error(res.msg);
             }
           })
        // }).catch(() => {
        //   this.$message({
        //     type: 'info',
        //     message: '已取消'
        //   });          
        // });
      },  
      goBack() {
        this.$router.go(-1); // 返回上一页
      },
    },
    mounted () {
      this.title = this.$route.query.detail 
      this.tableName ()
      this.resultList ()
    },
}
</script>

<style lang="scss" scoped>
.templateDetails {
  .pagination {
    margin-top: 16px;
    text-align: right;
  }
  ::v-deep .no-tooltip {
    .cell {
      white-space: nowrap !important;
    }
  }
}

</style>
