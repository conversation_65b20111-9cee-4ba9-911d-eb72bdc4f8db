<template>
    <div>  
      <div class="safety_work">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="月度安全管理模板" name="monthlySafety" lazy>
            <monthlySafety></monthlySafety>
          </el-tab-pane>
          <el-tab-pane label="监理单位" name="supervision" lazy>
            <supervision></supervision>
          </el-tab-pane>
          <el-tab-pane label="设计单位" name="design" lazy>
            <design></design>
          </el-tab-pane>
          <el-tab-pane label="施工单位" name="construction" lazy>
            <construction></construction>
          </el-tab-pane>
          <el-tab-pane label="合作单位填报安全生产工作人员配置界面" name="personnel" lazy>
            <personnel></personnel>
          </el-tab-pane>
          <el-tab-pane label="合作单位岗位责任清单履职" name="inventory" lazy>
            <inventory></inventory>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
</template>
<script>
import byDetail from "@/views/pccw/safety_production/safety_work/detail.vue";
import inventory from "@/views/pccw/safety_production/safety_work/inventory.vue";
import supervision from "@/views/pccw/safety_production/safety_work/supervision.vue";
import design from "@/views/pccw/safety_production/safety_work/design.vue";
import personnel from "@/views/pccw/safety_production/safety_work/personnel.vue";
import monthlySafety from "@/views/pccw/safety_production/safety_work/monthlySafety.vue";
import construction from "@/views/pccw/safety_production/safety_work/construction.vue";
export default {
    name: 'safety_work',
    components: {
      byDetail,
      inventory,
      supervision,
      design,
      personnel,
      monthlySafety,
      construction
    },
    data(){
        return {
          activeName: 'monthlySafety',
        }
    },
    methods: {     
      handleClick () {

      },  
    },
}
</script>

<style scoped>
/* Your component's CSS code goes here */
</style>
