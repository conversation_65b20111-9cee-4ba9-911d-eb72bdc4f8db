<template>
  <div>
      <mssCard title="查询结果">
        <div slot="content">
          <mssTable
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            :staticSearchParam="loadParam"
            border
            :pagination='false'
          >
          </mssTable>
        </div>
      </mssCard>
  </div>
</template>

<script>

import { commonDown } from '@/utils/btn'
import { queryALLTemplateConfig, sendWaitForReading} from '@/api/pccw/safety_production/safetyWork'
export default {
name: 'Detail',
data() {
  return {
      loadParam:{},
      tableApi: queryALLTemplateConfig,
      // 表头
      tableHeader: [
        {
          prop: "attr3",
          label: "模板类型",
          align: "center",
          tooltip: true,
          // formatter: (row) =>{
          //   return(
          //     <span>
          //     { row.templateType == 1 ? '岗位责任清单': '管理制度任务清单'}
          //     </span>
          //   )
          // }
        },
        {
          prop: "detail",
          label: "模板名称",
          align: "center",
          tooltip: true,
        },
        {
          prop: "createTime",
          label: "创建日期",
          align: "center",
          tooltip: true,
        },
        {
        label: '操作',
        align: 'center',
        fixed: 'right',
        minWidth: '120px',
        formatter: (row) => {
          return (
              <span>
                  <span class="mr10">
                  <el-button
                      type="text"
                      onClick={() => {
                      this.saveONe(row)}}>
                      查看
                  </el-button>
                  </span>
              </span>
          )
        }
      }
      ],    
  };
},
methods: {
  saveONe (row) {
    this.$router.push({
      path: `/pccw_menu/safety_production/safety_work/templateDetails`,
      query: {
        templateType :row.attr1,
        unitType :row.attr2,
        detail: row.detail
      }
    })
    // this.$$rout
  },
},
mounted() {
    // Code to run when the component is mounted
},
};
</script>

<style scoped>
/* Your component-specific styles go here */
</style>
