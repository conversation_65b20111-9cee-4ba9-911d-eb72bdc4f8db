<template>
  <div>
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
      <!--结果展示框-->
      <mssCard title="查询结果">
        <div slot="headerBtn">
          <el-button  type="primary" @click="exportMethod">批量导出附件</el-button>
          <el-button type="primary" @click="exportHandle">导出</el-button>
        </div>
        <div slot="content">
          <mssTable
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            :staticSearchParam="loadParam"
            border
          >
          </mssTable>
        </div>
      </mssCard>
      <el-dialog
        title="附件列表"
        :visible.sync="dialogVisible"
        :close-on-click-modal='false' 
        @close='onClose'
        width="30%">
        <mssTable
          ref="file"
          :columns="attachment"
          border
          :stationary="fileData"
        >
        </mssTable>
      </el-dialog>
  </div>
</template>

<script>

import { commonDown } from '@/utils/btn'
import { getDetailByConstructionUnit, exportDetailByConstructionUnit, exportMaterialByConstructionUnit, exportAllMaterial } from '@/api/pccw/safety_production/safetyWork'
import { queryAreaListService } from "@/api/common_api"
export default {
name: 'construction',
data() {
  return {
      loadParam:{},
      tableApi: getDetailByConstructionUnit,
      searchConfig:[
        {
            label: '合作单位名称',
            type: 'input',
            fieldName: 'unitName'
        },
        {
            label: '安全工作填报月份',
            type: "monthrange",
            format:'yyyy-MM',
            valueFormat:'yyyy-MM',
            fieldName: "date",
        },
        {
            label: '地市',
            type: 'select',
            fieldName: 'city'
        }
      ],
      // 表头
      tableHeader: [
        {
          prop: "city",
          label: "地市",
          align: "center",
          tooltip: true,
        },
        {
          prop: "unitName",
          label: "合作单位名称",
          align: "center",
          tooltip: true,
        },
        {
          prop: "fillingMonth",
          label: "填报月份",
          align: "center",
          tooltip: true,
        },
        {
          prop: "projectName",
          label: "项目",
          align: "center",
          tooltip: true,
        },
        {
          prop: "times",
          label: "频次或时间",
          align: "center",
          tooltip: true,
        },
        {
          prop: "jobDesc",
          label: "工作内容",
          align: "center",
          tooltip: true,
        },
        {
          prop: "checkType",
          label: "检查类型",
          align: "center",
          tooltip: true,
        },
        {
          prop: "checkPerson",
          label: "检查人员",
          align: "center",
          tooltip: true,
        },
        {
          prop: "checkTimes",
          label: "检查频次",
          align: "center",
          tooltip: true,
        },
        {
          prop: "checkPlace",
          label: "检查部位",
          align: "center",
          tooltip: true,
        },
        {
          prop: "checkContent",
          label: "检查内容和标准",
          align: "center",
          tooltip: true,
        },
        {
          prop: "trainOrEducationObject",
          label: "培训或教育对象",
          align: "center",
          tooltip: true,
        },
        {
          prop: "trainAndEducationContent",
          label: "培训和宣传教育内容",
          align: "center",
          tooltip: true,
        },
        {
          prop: "trainingPurpose",
          label: "培训目的",
          align: "center",
          tooltip: true,
        },
        {
          prop: "examIndex",
          label: "考核指标",
          align: "center",
          tooltip: true,
        },
        {
          prop: "examIndexDetail",
          label: "考核指标说明",
          align: "center",
          tooltip: true,
        },
        {
          prop: "examObject",
          label: "考核对象",
          align: "center",
          tooltip: true,
        },
        {
          prop: "examStandard",
          label: "考核标准",
          align: "center",
          tooltip: true,
        },
        {
          prop: "ReferTo",
          label: "部分单位的标准参考",
          align: "center",
          tooltip: true,
        },
        {
          prop: "remark",
          label: "备注",
          align: "center",
          tooltip: true,
        },
        {
          prop: "jobRequest",
          label: "工作要求",
          align: "center",
          tooltip: true,
        },
        {
          prop: "instiStandard",
          label: "制度标准",
          align: "center",
          tooltip: true,
        },
        {
          prop: "monthFlag",
          label: "本月是否涉及",
          align: "center",
          tooltip: true,
        },
        {
          prop: "finishCondition",
          label: "完成情况",
          align: "center",
          tooltip: true,
        },
        // {
        //   prop: "fileName",
        //   label: "支撑材料",
        //   align: "center",
        //   tooltip: true,
        // },
        {
        label: '操作',
        align: 'center',
        fixed: 'right',
        minWidth: '120px',
        formatter: (row) => {
          return (
              <span>
                  <span>
                  <el-button
                      type="text"
                      onClick={() => {
                      this.deleteTable(row)}}>
                      导出材料
                  </el-button>
                  </span>
              </span>
          )
        }}
      ],
      dialogVisible: false,
      attachment: [
        {
          prop: "fileName",
          label: "附件名称",
          align: "center",
          tooltip: true,
          width: 150
        },
        {
          label: "操作",
          prop: "_caozuo",
          fixed: "right",
          minWidth: 100,
          formatter: (row, column, cellValue, index) => {
            return (
              <span>
                <span class="mr10">
                  <el-button
                    type="text"
                    onClick={() => {
                      this.downloadFile(row, 1);
                    }}
                  >
                    下载文件
                  </el-button>
                </span>
              </span>
            );
          },
      },
      ],
      fileData: [],       
  };
},
methods: {
  exportMethod () {
    commonDown({ ...this.loadParam, limit: -1, templateType: 2, unitType: '施工'}, exportAllMaterial);
  },
  downloadFile (row) {
    commonDown({...row}, exportMaterialBySupervisionUnit);
  },
  onClose () {
    this.dialogVisible = false
  },
  deleteTable (row) {
    this.fileData = []
    let fileName = row.fileName.split(',')
    let fileId = row.fileId.split(',')
    fileName.forEach(item => {
      this.fileData.push({
        city: row.city,
        fileId: fileId[0],
        reportTime: row.fillingMonth,
        fileName: item,
        unitName: row.unitName
      })
    });
    this.dialogVisible = true
  },
  getAreaList(parentId, index) {
    queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
        const list = []
        res.data.forEach(item => {
            list.push({ label: item.name, value: item.name })
        })
        this.$set(this.searchConfig[index], 'options', list)
        }
    })
  },
  saveONe (row) {
    commonDown({ ...this.loadParam, limit: -1, fileId: row.fileId}, exportMaterialByConstructionUnit);
  },
  deleteTable () {

  },
  //导出
  exportHandle() {
      commonDown({ ...this.loadParam, limit: -1, exportType: 1}, exportDetailByConstructionUnit);
  },
  //重置
  reset(form) {
      this.search(form)
  },
  //搜索
  search() {
      this.$refs.table.page.current = 1
      let data = {
        ...this.$refs.searchForm.searchForm,
        startTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[0] :  "",
        endTime: this.$refs.searchForm.searchForm.date ? this.$refs.searchForm.searchForm.date[1] :  ""
      }
      // this.loadParam = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      this.$refs.table.getTableData(data);
  },
},
mounted() {
  this.getAreaList('-2', 2)
},
};
</script>

<style scoped>
</style>
