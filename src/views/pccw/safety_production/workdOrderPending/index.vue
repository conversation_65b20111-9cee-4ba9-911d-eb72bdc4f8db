<template>
    <div>
      <flow-detail :showReturn='showReturn' :showApproval='showApproval'  :flow="flow" :showTransact="false" :type="approveType" :msgApproval="msgApproval" ref="flowDetail"> 
        <el-button style="text-align: right" type="primary" slot="custom-buttons" @click="savePage" v-if="$route.query.type ==='todo' && saveStatus">保存提交</el-button>
        <div slot="content">
          <div style="background:rgb(255, 255, 255);padding: 10px 5px;color: #333333;font-weight: bold;">{{title}}</div>
          <div class="safety_work">
            <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
              <el-tab-pane :label="item.label" :name="item.name" v-for="(item, index) in list" :key="index">
                <mssCard title="查询结果">
                  <div slot="content">
                    <div class="commonTable base-table">
                      <el-table
                        :data="item.dataList"
                        class="table">
                        <template v-for="(column) in item.tableHeader">
                          <el-table-column 
                            v-if="column.label == '本月是否涉及'"
                            :align="column.align || 'center'"
                            :prop="column.prop"
                            :label="column.label || ''"
                            min-width="150"
                            :key="column.prop"
                            :show-overflow-tooltip="true"
                            >
                              <template slot-scope="scope">
                                <el-select v-model="scope.row[column.prop]" placeholder="请选择" v-if="approveType ==='todo' && saveStatus">
                                  <el-option label="是" value="是"></el-option>
                                  <el-option label="否" value="否"></el-option>
                                </el-select>
                                <div v-else>{{scope.row.monthFlag}}</div>
                              </template>
                          </el-table-column>
                          <el-table-column 
                            v-else-if="column.label == '完成情况'"
                            :align="column.align || 'center'"
                            :prop="column.prop"
                            :label="column.label || ''"
                            min-width="150"
                            :key="column.prop"
                            :show-overflow-tooltip="true"
                            >
                              <template slot-scope="scope">
                                <!-- {{scope.row.monthFlag}} -->
                                <el-input v-model="scope.row[column.prop]" type="textarea" :rows="4" resize='none' v-if="approveType ==='todo' && saveStatus"></el-input>
                                <div v-else>{{scope.row.finishCondition}}</div>
                              </template>
                          </el-table-column>
                          <el-table-column 
                            v-else-if="column.label == '支撑材料'"
                            :align="column.align || 'center'"
                            prop="''"
                            :label="column.label || ''"
                            min-width="150"
                            :key="column.prop"
                            :show-overflow-tooltip="true"
                            >
                            <template slot-scope="scope">
                                <div v-if="scope.row.monthFlag== '是'">
                                   <el-button
                                    v-if="approveType ==='todo' && saveStatus "
                                    type="text"
                                    @click='operation(scope.row, 1)'>
                                    上传附件
                                  </el-button>
                                  <el-button
                                    v-if="scope.row.monthFlag== '是'"
                                    type="text"
                                    @click='operation(scope.row, 2)'>
                                    查看附件
                                  </el-button>
                                </div>
                            </template>
                          </el-table-column>
                          <el-table-column 
                            v-else
                            :align="column.align || 'center'"
                            :prop="column.prop"
                            :label="column.label || ''"
                            min-width="150"
                            :key="column.prop"
                            :show-overflow-tooltip="true"
                            >
                          </el-table-column>
                        </template>
                      </el-table>
                    </div>
                  </div>
                </mssCard>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div> 
      </flow-detail>
      <el-dialog
        title="附件上传"
        :visible.sync="dialogVisible1"
        :close-on-click-modal='false' 
        @close='onClose'
        width="30%">
        <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm" style="width: 70%">
          <el-form-item label="上传附件">
            <el-upload
              style="display: inline-block;margin-left: 10px;"
              ref="newFile"
              class="upload-btn"
              action="string"
              :show-file-list="false"
              :auto-upload="true"
              :http-request="importFile"
              :limit="9999"
            >
              <el-button type="primary">上传附件</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog
        title="附件列表"
        :visible.sync="dialogVisible2"
        :close-on-click-modal='false' 
        @close='onClose1'
        width="30%">
        <mssTable
          ref="table"
          :columns="attachment"
          border
          :stationary="fileData"
        >
        </mssTable>
      </el-dialog>
    </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
import { getSafetyWorkTableAllMsg, uploadFile, getFileList, download, deleteFile, saveSafetyWork } from '@/api/pccw/safety_production/safetyWork'
import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue";
export default {
    components: {
      FlowDetail
    },
    name: 'workdOrderPending',
    data(){
        return {
          activeName: '',
          list: [],
          showApproval: false, //提交
          showReturn: false, //退回
          saveStatus: true, //提交审批
          flow: {},
          approveType: '',
          businessId: '',
          ruleForm: {},
          dialogVisible1: false,
          dialogVisible2: false,
          attachment: [
            {
              prop: "fileName",
              label: "附件名称",
              align: "center",
              tooltip: true,
              width: 150
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.downloadFile(row, 1);
                        }}
                      >
                        下载文件
                      </el-button>
                    </span>
                    <span>
                    {
                      this.approveType ==='todo' && this.saveStatus ? <el-button
                        type="text"
                        onClick={() => {
                          this.downloadFile(row, 2);
                        }}
                      >
                        删除
                      </el-button>: ''
                    }
                      
                    </span>
                  </span>
                );
              },
          },
          ],
          fileData: [],
          title: '',
          id: ''
        }
    },
    created() {
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow); // 获取流程对象
      console.log(this.flow)
      this.approveType = decodeURIComponent(this.$route.query.type); // 获取类型信息
      if (this.approveType == 'todo') {
        this.saveStatus = true //提交审批
        this.showApproval =  false //提交
        this.showReturn =  false //退回
      }
      if (this.approveType ==='todo' && !this.saveStatus) {
         this.msgApproval = ''
      } else {
         this.msgApproval = '请先填写信息'
      }
      
      this.businessId = this.flow.businessId; // 根据这个去后端查数据
      this.loadParam = {businessId: this.flow.businessId}
      this.tableName ()
    },
    methods: {
      onClose1 () {
        this.dialogVisible2()
      },
      downloadFile (row, index) {
         console.log(row);
         if (index === 1) {
          commonDown({...row, kbId: row.fileId}, download);
         } else {
          this.$confirm('此操作将删除附件', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let data ={
              kbId: row.fileId,
            }
            deleteFile(data).then((res) => {
               if (res.code == '0000') {
                  let row = {
                    id: this.ruleForm.id
                  }
                  this.operation(row, 2)
                  this.$message.success('删除成功');
               } else {
                  this.$message.error(res.msg);
               }
             })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });          
          });
         }
      },
      onClose () {
        if (this.$refs.newFile.uploadFiles.length > 0) {
          this.$refs.newFile.clearFiles()
        }
        this.dialogVisible1 = false
      },
      importFile(params) {
        const param = new FormData()
        param.append('file', params.file)
        param.append('detailId', this.ruleForm.id)
        param.append('businessId', this.flow.businessId)
        uploadFile(param).then((res) => {
          if (res.code === '0000') {
            this.list.forEach((a, i) => {
              a.dataList.forEach((b, index) => {
                if (b.id === this.ruleForm.id) {
                  this.$set(this.list[i].dataList[index], 'kbId', res.data.kbId)
                }
              });
            });
            console.log(this.list)
            this.$message.success('导入成功')
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      operation (row, index) {
        // console.log(this.list)
        if (index === 1) {
          this.ruleForm.id = row.id
          this.dialogVisible1 = true
        } else {
           this.ruleForm.id = row.id
           getFileList({id:row.id}).then((res)=>{
            this.fileData = []
            let fileId = res.data.fileId.split(",")
            let fileName = res.data.fileName.split(",")
              fileId.forEach((a, b) => {
                if (a === '' || a === null) {
                  this.fileData = []
                } else {
                  this.fileData.push({
                    fileId: a,
                    fileName: fileName[b]
                  })
                }
              });
            this.dialogVisible2 = true
            console.log(res)
           })
          // 
        }
      },
      tableName () {
        let data = {
          businessId: this.businessId
        }
        getSafetyWorkTableAllMsg(data).then((res)=> {
          const val = res.data 
          this.title = res.data.title
          this.id = res.data.id
          this.list = []
          this.activeName = val.detailList[0].sheetName
          if (res.data.status === '流程中') {
            this.saveStatus = false
            this.showApproval = true
            this.showReturn = true
            this.msgApproval = ''
          }
          val.detailList.forEach((item, index) => {
            var a = []
            this.list.push({
              label: item.sheetRealName,
              name: item.sheetName,
              ref: `table${index}`,
              tableHeader: [],
              dataList: item.dataList
            })
            item.labelList.forEach((arr, i) => {
              a.push({
                prop: item.propList[i],
                label: arr,
                align: "center",
                minWidth: 150,
                tooltip:true
              })
            });
            a.push({
                prop: 'monthFlag',
                label: '本月是否涉及',
                align: "center",
                minWidth: 150,
                tooltip: true
              },{
                prop: 'finishCondition',
                label: '完成情况',
                align: "center",
                minWidth: 150,
                tooltip: true
              },{
                prop: '',
                label: '支撑材料',
                align: "center",
                minWidth: 150,
                tooltip: true
              })
            this.list[index].tableHeader = a
          });
        })
      },     
      handleClick () {
        console.log(this.activeName);
      },
      savePage () {
        let data = []
        for (let index = 0; index < this.list.length; index++) {
          for (let a = 0; a < this.list[index].dataList.length; a++) {
            if (this.list[index].dataList[a].monthFlag === null || this.list[index].dataList[a].monthFlag === '' || !this.list[index].dataList[a].monthFlag) {
              let text = `${this.list[index].label}第${a+1}行本月是否涉及为空`
              this.$message.error(text)
              return
            }
            if (this.list[index].dataList[a].finishCondition === null || this.list[index].dataList[a].finishCondition === '' || !this.list[index].dataList[a].finishCondition) {
              let text = `${this.list[index].label}第${a +1}行完成情况为空`
              this.$message.error(text)
              return
            }
          }
          data.push({
            label: "专业负责人（审查人员）",
            name: "23",
            ref: "table0",
            dataList: this.list[index].dataList
          })
        }
        saveSafetyWork({detailList:data,id:this.id, taskId: this.flow.taskId}).then((res)=>{
          if (res.code == "0000") {
            this.$refs.flowDetail.msgApproval = ''
            this.$message.success('信息保存成功');
            this.$refs.flowDetail.openApprovalDialog()
            // this.$router.push('/home')
          }
        })
        
      },  
      stringToBoolean(str) {
        return (str === "true") ? true : false;
      },
    },
    mounted () {
      if (this.flow.nodeName == '安全管理员审核') {
        this.showReturn = false
        this.$refs.flowDetail.flagReturn = false
      }
      // this.tableName ()
    },
}
</script>

<style scoped>
/* Your component's CSS code goes here */
</style>
