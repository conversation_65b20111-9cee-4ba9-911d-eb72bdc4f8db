<template>
  <div>
    <el-card class="card">
        <div class="title">
          <h1 class="heading">安全生产费报账催办提醒</h1>
        </div>
    </el-card>
    <mssCard title="任务详细信息">
      <div slot="content">
        <mssTable
          ref="table"
          :columns="tableHeader"
          showSummary
          :stationary="tableData"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {getPendingSafetyCost} from '@/api/pccw/implementation_link/safety_payment_alert/alert_list'
export default {
    name: 'SafetyPaymentAlert',
    data() {
        return {
          tableData: [],
          showSummary: true,
          //总计
          total: 0,
          current: {
            page: 1,
            size: 10
          },
          //表头
          tableHeader: [
            {
                prop: "unit",
                label: "建设单位",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "projectManagerName",
                label: "项目经理",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "projectCode",
                label: "项目编码",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "projectName",
                label: "项目名称",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "constructionCommencementReportSubmissionDate",
                label: "开工日期",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "endTime",
                label: "应支付安全生产费用截止日期",
                align: "center",
                tooltip: true,
                minWidth: 150
            },
            {
                prop: "pendingTime",
                label: "支付剩余天数",
                align: "center",
                tooltip: true,
                minWidth: 150
            }
          ],
        }
    },

    created() {
      this.getPower()
    },

    computed: {
    },
    methods: {
      getPower() {
        getPendingSafetyCost({businessId: this.$route.query.businessId}).then((res)=>{
          this.tableData = []
          this.tableData.push({
            ...res.data
          })
          console.log(this.tableData)
        })
      },
    }
}
</script>

<style>
.card {
  margin-bottom: 20px;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  border-radius: 4px;
}
.title {
  text-align: center;
}

.heading {
  margin-top: 0;
  font-size: 24px;
  font-weight: bold;
}




</style>