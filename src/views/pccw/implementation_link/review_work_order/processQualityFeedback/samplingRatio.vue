<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    >
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="getTemplate">下载模板</el-button>
        <el-upload
          style="display: inline;margin: 0 10px;"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
          >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="UNITDIM"
          selection
          :staticSearchParam="loadParam"
          :api="tableApi"
        >
        </mssTable>
      </div>
    </mssCard>
    <administratorAdded @onClose='onClose' ref="administratorAdded"></administratorAdded>
    <ImportErrorMessage ref="importErrorMessage"></ImportErrorMessage>
  </div>
</template>

<script>
//获取接口地址
import { getKeyProcessesRatio, deleteKeyProcessesRatio, downloadKeyProcessesRatio, samplingRatioImportProcessNameData } from '@/api/pccw/review_work_order/processQualityFeedback/api';
import administratorAdded from "./administratorAdded.vue";
import { commonDown } from '@/utils/btn'
import ImportErrorMessage from '@/views/pccw/components/importErrorMessage/index'
export default {
name: 'samplingRatio',
components: {
  administratorAdded,
  ImportErrorMessage
},
data() {
  return{
    // 表格数据API
    tableApi: getKeyProcessesRatio,
    //搜索字段配置
    searchConfig: [
      {
          label: '管理类型',
          type: 'select',
          fieldName: 'managementType',
          options: [
            {
              label:'工序质量',value:'工序质量'
            },
            {
              label:'视频验收',value:'视频验收'
            }
          ]
      },
      {
          label: '工序模版名称',
          type: 'input',
          fieldName: 'processTemplateName'
      },
    ],
    // 建设单位
    UNITDIM: [
      {
        prop: "managementType",
        label: '管理类型',
      },
      {
        prop: "processTemplateName",
        label: '工序模版名称',
      },
      {
        prop: "auditPersonnelSamplingRatio",
        label: '稽核人员抽查比例',
      },
      {
        prop: "managerSamplingRatio",
        label: '工程实施经理（主）抽查比例',
      },
    ],
    labelWidth: {
      default: '130px'
    },
    loadParam: {},
  }
},

created() {
},
methods: {
  importFile(params) {
    const param = new FormData()
    param.append('file', params.file)
    samplingRatioImportProcessNameData(param).then((res) => {
      if (res.code === '5000') {
          this.$message.warning(res.msg)
      } else if (res.code === '0000') {
        if (res.data.length > 0){
          this.$refs.importErrorMessage.open(res.data)
        } else {
          this.$message.success('导入成功')
        }
        this.$refs.table.getTableData()
      }
    })
  },
  getTemplate () {
    commonDown({}, downloadKeyProcessesRatio)
  },
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      console.log(this.$refs.table.multipleSelection)
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      deleteKeyProcessesRatio({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
  },
  getAddition () {
    this.$refs.administratorAdded.open('关键工序、视频验收工单抽查比例配置')
  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  reset() {
    this.search()
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>