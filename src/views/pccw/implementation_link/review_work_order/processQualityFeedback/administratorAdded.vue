<template>
  <div>
    <el-dialog
      :title=" title+'新增' "
      :visible.sync="dialogVisible"
      :close-on-click-modal='false' 
      @close='onClose'
      width="85%">
      <mssCard :title="title">
        <div slot="headerBtn">
          <el-button type="primary" @click="addTable">新增</el-button>
        </div>
        <div slot="content">
          <el-form ref="exemptionList" :model="form" :rules="rules">
            <mssTable
              ref="table"
              selection
              :serial="false"
              :stationary="tableData"
              :columns="tableHeader"
              :pagination="false"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
    </el-dialog>
    <magnifyingGlass ref="magnifyingGlass" @showCheckList="showCheckList" :mult-select="multSelect"></magnifyingGlass>
  </div>
</template>
<script>
import { insertConfig, insertKeyProcessesRatio, insertVideoAcceptanceRatio,insertProblemSeverity, insertProcessName, insertProblemClassification, insertQualityAdministrator} from '@/api/pccw/review_work_order/processQualityFeedback/api';
import { templateList } from '@/api/pccw/analysis_link/key_processes/api'
import magnifyingGlass from "@/views/pccw/components/magnifyingGlass/index.vue";
import { queryAreaListService } from '@/api/common_api.js'
export default {
  name: 'exemptionAdded',
  components: {
    magnifyingGlass
  },
  data () {
    return {
      dialogVisible: false,
      tableData: [],
      levelNoList: [
        { label: "已终止", value: "已终止" },
        { label: "未终止", value: "未终止" }
      ],
      form: {},
      rules: {},
      unitList:[],
      title: '',
      multSelect: false
    }
  },
  methods: {
    //弹出层
    open (name) {
      this.title = name
      this.cityList = JSON.parse(window.sessionStorage.getItem('constructionUnit'))
      this.tableData = []
      this.dialogVisible = true
    },
    //关闭
    onClose () {
      this.$emit('onClose')
      this.dialogVisible = false
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.exemptionList.validate((validate) => {
          if (validate) {
              let data = {
                ...row
              }
              if (this.title === '管理员配置') {
                insertConfig(data).then((res)=>{
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                })
              } else if (this.title === '关键工序、视频验收工单抽查比例配置') {
                insertKeyProcessesRatio(data).then((res)=>{
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                })
              } else if (this.title === '关键工序、视频验收工单抽查比例配置(工程管理)') {
                insertVideoAcceptanceRatio(data).then((res)=>{
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                })
              } else if (this.title === '问题严重程度') {
                insertProblemSeverity(data).then((res)=>{
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                })
              } else if (this.title === '工序名称配置') {
                insertProcessName(data).then((res)=>{
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                })
              } else if (this.title === '省公司抽查问题分类配置') {
                insertProblemClassification(data).then((res)=>{
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                })
              } else if (this.title === '省公司质量管理员配置') {
                insertQualityAdministrator(data).then((res)=>{
                  if (res.code == "0000") {
                    this.$message.success("保存成功");
                  }
                })
              }
          } else {
            return;
          }
        });
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      var a = [];
      if (this.title === '管理员配置') {
        a = [
          'roleName',
          'userName',
          'userAccount',
          'project'
        ]
      } else if (this.title === '关键工序、视频验收工单抽查比例配置') {
        a = [
          'managementType',
          'processTemplateName',
          'auditPersonnelSamplingRatio',
          'managerSamplingRatio'
        ]
      } else if (this.title === '关键工序、视频验收工单抽查比例配置(工程管理)') {
        a = [
          'managementType',
          'processTemplateName',
          'weeklySamplingNumber',
          'managerSamplingRatio'
        ]
      } else if (this.title === '问题严重程度') {
        a = [
          'problemSeverity'
        ]
      } else if (this.title === '工序名称配置') {
        a = [
          'processTemplateName',
          'processName',
          'processDescription'
        ]
      } else if (this.title === '省公司抽查问题分类配置') {
        a = [
          'problemClassification'
        ]
      }
      a.forEach(item => {
        if (arr[item] === '' || arr[item] == null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          this.rules[`${item}${index}`] = {}
        }
      });
    },
    addTable() {
      if (this.title === '管理员配置') {
        this.tableData.push({
          roleName: '',
          userName: '',
          userAccount: '',
          project: '',
          userId: ''
        })
      } else if (this.title === '关键工序、视频验收工单抽查比例配置') {
        this.tableData.push({
          managementType: '',
          processTemplateName: '',
          auditPersonnelSamplingRatio: '',
          managerSamplingRatio: ''
        })
      } else if (this.title === '关键工序、视频验收工单抽查比例配置(工程管理)') {
        this.tableData.push({
          managementType: '',
          processTemplateName: '',
          weeklySamplingNumber: '',
          managerSamplingRatio: ''
        })
      } else if (this.title === '问题严重程度') {
        this.tableData.push({
          problemSeverity: ''
        })
      } else if (this.title === '工序名称配置') {
        this.tableData.push({
          processTemplateName: '',
          processName: '',
          processDescription: ''
        })
      } else if (this.title === '省公司抽查问题分类配置') {
        this.tableData.push({
          problemClassification: ''
        })
      } else if (this.title === '省公司质量管理员配置') {
        this.tableData.push({
          userAccount: '',
          userName: '',
          userId: ''
        })
      } 
    },
    openChooseUserDailog (row, index, deptParams) {
      let item = {}
      if (deptParams == '管理员配置') {
        item = {
          excuterNames: row.userName ? row.userName : '',
          excuterIds: row.userAccount ? row.userAccount : ''
        }
      }
      this.$refs.magnifyingGlass.init(item, deptParams, index)
    },
    showCheckList(val) {
      // console.log(val)
        let realName = []
        let account = []
        if (val.checkList.length > 0) {
          val.checkList.forEach(item => {
            realName.push(item.realName)
            account.push(item.account)
          })
        }
      if (val.deptParams == '管理员配置') {
        this.$set(this.tableData[val.index], 'userAccount' , account.toString())
        this.$set(this.tableData[val.index], 'userName' , realName.toString())
        this.$set(this.tableData[val.index], 'userId' , val.checkList[0].userId.toString())
      }
      console.log(this.tableData)
    },
  },
  computed: {
    tableHeader: {
      get() {
        if (this.title === '管理员配置') {
          return [
            {
              prop: "roleName",
              label: "角色",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`roleName${index}`}>
                    <el-select
                      v-model={row.roleName}
                      onChange={(value) => {
                        this.form[`roleName${index}`] = value;
                      }}
                    >
                      <el-option label={'省公司稽核人员'} value={'省公司稽核人员'} />
                      <el-option label={'建设单位质量管理员'} value={'建设单位质量管理员'} />
                    </el-select>
                  </el-form-item>
                );
              },
            },
            {
              prop: "userAccount",
              label: "用户名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`userAccount${index}`}>
                    <el-input 
                      v-model={row.userAccount}
                      readonly onFocus={() => {
                      this.openChooseUserDailog(row, index, '管理员配置')
                     }}>
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "userName",
              label: "用户账号",
              minWidth: 130
            },
            {
              prop: "project",
              label: "建设单位",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`project${index}`}>
                    <el-select
                      v-model={row.project}
                      onChange={(value) => {
                        this.form[`project${index}`] = value;
                      }}
                    >
                      {this.cityList.length &&
                        this.cityList.map((item) => {
                          return (
                            <el-option label={item.label} value={item.value} />
                          );
                        })}
                    </el-select>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ];
        } else if (this.title === '关键工序、视频验收工单抽查比例配置') {
          return [
            {
              prop: "managementType",
              label: "管理类型",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`managementType${index}`}>
                    <el-select
                      v-model={row.managementType}
                      onChange={(value) => {
                        this.form[`managementType${index}`] = value;
                      }}>
                      <el-option label={'工序质量'} value={'工序质量'} />
                      <el-option label={'视频验收'} value={'视频验收'} />
                    </el-select>
                  </el-form-item>
                );
              },
            },
            {
              prop: "processTemplateName",
              label: "工序模版名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`processTemplateName${index}`}>
                    <el-select
                      v-model={row.processTemplateName}
                      onChange={(value) => {
                        this.form[`processTemplateName${index}`] = value;
                      }}
                    >
                      {this.nameList.length &&
                        this.nameList.map((item) => {
                          return (
                            <el-option label={item.templateName} value={item.templateName} />
                          );
                        })}
                    </el-select>
                  </el-form-item>
                );
              },
            },
            {
              prop: "auditPersonnelSamplingRatio",
              label: "稽核人员抽查比例",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`auditPersonnelSamplingRatio${index}`}>
                    <el-input 
                      onChange={(value) => {
                        const regex = /^\d*(\.\d{1,20})?$/; // 正则表达式，匹配最多四位小数的数字
                          if (!regex.test(value)) {
                            // 如果输入值不符合要求，清空输入框
                            row.auditPersonnelSamplingRatio = '';
                            this.$message.error('请输入数字');
                          }
                        this.form[`auditPersonnelSamplingRatio${index}`] = value;
                      }}
                      v-model={row.auditPersonnelSamplingRatio}
                    >
                    <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "managerSamplingRatio",
              label: "工程实施经理（主）抽查比例",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`managerSamplingRatio${index}`}>
                    <el-input 
                      onChange={(value) => {
                        const regex = /^\d*(\.\d{1,20})?$/; // 正则表达式，匹配最多四位小数的数字
                          if (!regex.test(value)) {
                            // 如果输入值不符合要求，清空输入框
                            row.managerSamplingRatio = '';
                            this.$message.error('请输入数字');
                          }
                        this.form[`managerSamplingRatio${index}`] = value;
                      }}
                      v-model={row.managerSamplingRatio}
                    >
                    <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ];      
        } else if (this.title === '关键工序、视频验收工单抽查比例配置(工程管理)') {
          return [
             {
              prop: "managementType",
              label: "管理类型",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`managementType${index}`}>
                    <el-select
                      v-model={row.managementType}
                      onChange={(value) => {
                        this.form[`managementType${index}`] = value;
                      }}>
                      <el-option label={'工序质量'} value={'工序质量'} />
                      <el-option label={'视频验收'} value={'视频验收'} />
                    </el-select>
                  </el-form-item>
                );
              },
            },
            {
              prop: "processTemplateName",
              label: "工序模版名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`processTemplateName${index}`}>
                    <el-select
                      v-model={row.processTemplateName}
                      onChange={(value) => {
                        this.form[`processTemplateName${index}`] = value;
                      }}
                    >
                      {this.nameList.length &&
                        this.nameList.map((item) => {
                          return (
                            <el-option label={item.templateName} value={item.templateName} />
                          );
                        })}
                    </el-select>
                  </el-form-item>
                );
              },
            },
            {
              prop: "managerSamplingRatio",
              label: "工程管理经理抽查比例",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`managerSamplingRatio${index}`}>
                    <el-input 
                      onChange={(value) => {
                        const regex = /^\d*(\.\d{1,20})?$/; // 正则表达式，匹配最多四位小数的数字
                          if (!regex.test(value)) {
                            // 如果输入值不符合要求，清空输入框
                            row.managerSamplingRatio = '';
                            this.$message.error('请输入数字');
                          }
                        this.form[`managerSamplingRatio${index}`] = value;
                      }}
                      v-model={row.managerSamplingRatio}
                    >
                    <template slot="append">%</template>
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "weeklySamplingNumber",
              label: "每周抽查上限数量",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`weeklySamplingNumber${index}`}>
                    <el-input 
                      onChange={(value) => {
                        const regex = /^\d*(\.\d{1,20})?$/; // 正则表达式，匹配最多四位小数的数字
                          if (!regex.test(value)) {
                            // 如果输入值不符合要求，清空输入框
                            row.weeklySamplingNumber = '';
                            this.$message.error('请输入数字');
                          }
                        this.form[`weeklySamplingNumber${index}`] = value;
                      }}
                      v-model={row.weeklySamplingNumber}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ];      
        } else if (this.title === '问题严重程度') {
          return [
            {
              prop: "problemSeverity",
              label: "问题严重程度",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`problemSeverity${index}`}>
                    <el-input 
                      v-model={row.problemSeverity}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ];      
        } else if (this.title === '工序名称配置') {
          return [
            {
              prop: "processTemplateName",
              label: "工序模板名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`processTemplateName${index}`}>
                    <el-input 
                      v-model={row.processTemplateName}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "processName",
              label: "工序名称",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`processName${index}`}>
                    <el-input 
                      v-model={row.processName}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              prop: "processDescription",
              label: "工序描述",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`processDescription${index}`}>
                    <el-input 
                      v-model={row.processDescription}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ];      
        } else if (this.title === '省公司抽查问题分类配置') {
          return [
            {
              prop: "problemClassification",
              label: "问题分类",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`problemClassification${index}`}>
                    <el-input 
                      v-model={row.problemClassification}
                    >
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ];      
        } else if (this.title === '省公司质量管理员配置') {
          return [
            {
              prop: "userName",
              label: "用户账号",
              minWidth: 130
            },
            {
              prop: "userAccount",
              label: "用户",
              minWidth: 130,
              formatter: (row, column, cellValue, index) => {
                return (
                  <el-form-item prop={`userAccount${index}`}>
                    <el-input 
                      v-model={row.userAccount}
                      readonly onFocus={() => {
                      this.openChooseUserDailog(row, index, '管理员配置')
                     }}>
                    </el-input>
                  </el-form-item>
                );
              },
            },
            {
              label: "操作",
              prop: "_caozuo",
              fixed: "right",
              minWidth: 100,
              formatter: (row, column, cellValue, index) => {
                return (
                  <span>
                    <span class="mr10">
                      <el-button
                        type="text"
                        onClick={() => {
                          this.saveONe(row, index);
                        }}
                      >
                        保存
                      </el-button>
                    </span>
                  </span>
                );
              },
            },
          ];      
        }
      },
    },
  },
  mounted() {
    templateList({ limit: 10000,page: 1 }).then((res)=>{
       this.nameList = res.data.data
    })
  },
} 
</script>
