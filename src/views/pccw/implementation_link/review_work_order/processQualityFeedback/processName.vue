<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    >
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-upload
              style="display: inline-block;margin-left: 10px;"
              ref="newFile"
              class="upload-btn"
              action="string"
              :show-file-list="false"
              :auto-upload="true"
              :http-request="importFile"
            >
              <el-button type="primary">导入</el-button>
            </el-upload>
            <el-button type="primary" @click="exportHandle" style="margin-left: 10px;">下载模板</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="UNITDIM"
          selection
          :staticSearchParam="loadParam"
          :api="tableApi"
        >
        </mssTable>
      </div>
    </mssCard>
    <administratorAdded @onClose='onClose' ref="administratorAdded"></administratorAdded>
  </div>
</template>

<script>
//获取接口地址
import { getProcessName, deleteProcessName, downloadProcessName, importProcessNameData } from '@/api/pccw/review_work_order/processQualityFeedback/api';
import administratorAdded from "./administratorAdded.vue";
import { commonDown } from '@/utils/btn'
export default {
name: 'processName',
components: {
  administratorAdded
},
data() {
  return{
    // 表格数据API
    tableApi:getProcessName,
    //搜索字段配置
    searchConfig: [
      {
          label: '工序模板名称',
          type: 'input',
          fieldName: 'processTemplateName'
      },
      {
          label: '工序名称',
          type: 'input',
          fieldName: 'processName'
      },
      {
          label: '工序描述',
          type: 'input',
          fieldName: 'processDescription'
      },
    ],
    // 建设单位
    UNITDIM: [
      {
        prop: "processTemplateName",
        label: '工序模板名称',
      },
      {
        prop: "processName",
        label: '工序名称',
      },
      {
        prop: "processDescription",
        label: '工序描述',
      },
    ],
    labelWidth: {
      default: '130px'
    },
    loadParam: {},
  }
},

created() {
},
methods: {
  importFile(params) {
    const param = new FormData()
    param.append('file', params.file)
    importProcessNameData(param).then((res) => {
      if (res.code === '5000') {
          this.$message.warning(res.msg)
      } else if (res.code === '0000') {
        this.$message.success('导入成功')
        // if (res.data.length > 0){
        //   this.dialogTableVisible = true
        //   this.gridData = res.data
        // } else {
        //   this.$message.success('导入成功')
        // }
        this.$refs.table.getTableData()
      }
    })
  },
  exportHandle() {
      commonDown({}, downloadProcessName);
  },
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      deleteProcessName({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  getAddition () {
    this.$refs.administratorAdded.open('工序名称配置')
  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  reset() {
    this.search()
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>