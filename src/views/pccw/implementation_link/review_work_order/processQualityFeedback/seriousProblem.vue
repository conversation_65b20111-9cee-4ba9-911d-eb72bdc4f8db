<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    >
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="UNITDIM"
          selection
          :staticSearchParam="loadParam"
          :api="tableApi"
        >
        </mssTable>
      </div>
    </mssCard>
    <administratorAdded @onClose='onClose' ref="administratorAdded"></administratorAdded>
  </div>
</template>

<script>
//获取接口地址
import { getProblemSeverity, deleteProblemSeverity } from '@/api/pccw/review_work_order/processQualityFeedback/api';
import administratorAdded from "./administratorAdded.vue";
export default {
name: 'seriousProblem',
components: {
  administratorAdded
},
data() {
  return{
    // 表格数据API
    tableApi:getProblemSeverity,
    //搜索字段配置
    searchConfig: [
      {
          label: '问题严重程度',
          type: 'input',
          fieldName: 'problemSeverity'
      },
    ],
    // 建设单位
    UNITDIM: [
      {
        prop: "problemSeverity",
        label: '问题严重程度',
      }
    ],
    labelWidth: {
      default: '130px'
    },
    cityList: [],
    loadParam: {},
  }
},

created() {
},
methods: {
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      console.log(this.$refs.table.multipleSelection)
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      deleteProblemSeverity({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
  },
  getAddition () {
    this.$refs.administratorAdded.open('问题严重程度')
  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  reset() {
    this.search()
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>