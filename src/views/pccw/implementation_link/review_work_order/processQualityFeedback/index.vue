<template>
         <!-- 抽查配置页面 -->
    <div>  
      <div class="StagingPointWorkOrder">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="省公司稽核人员、建设单位质量管理员配置" name="index" lazy>
            <qualityManager ref="qualityManager"></qualityManager>
          </el-tab-pane>
          <el-tab-pane label="关键工序、视频验收工单抽查比例配置" name="samplingRatio" lazy>
            <samplingRatio ref="samplingRatio"></samplingRatio>
          </el-tab-pane>
          <el-tab-pane label="关键工序、视频验收工单抽查比例配置(工程管理)" name="samplingRatioEngineering" lazy>
            <samplingRatioEngineering ref="samplingRatioEngineering"></samplingRatioEngineering>
          </el-tab-pane>
          <el-tab-pane label="抽查问题分类模板配置" name="spotCheckQuestions" lazy>
            <spotCheckQuestions ref="spotCheckQuestions"></spotCheckQuestions>
          </el-tab-pane>
          <el-tab-pane label="问题严重程度配置" name="seriousProblem" lazy>
            <seriousProblem ref="seriousProblem"></seriousProblem>
          </el-tab-pane>
          <el-tab-pane label="工序名称配置" name="processName" lazy>
            <processName ref="processName"></processName>
          </el-tab-pane>
          <el-tab-pane label="超期未处理待办的时长（自然日）可支持配置" name="unprocessedOverdue" lazy>
            <unprocessedOverdue ref="unprocessedOverdue"></unprocessedOverdue>
          </el-tab-pane>
          <el-tab-pane label="省公司抽查问题分类配置" name="provinceSpotCheck" lazy>
            <provinceSpotCheck ref="provinceSpotCheck"></provinceSpotCheck>
          </el-tab-pane>
          <el-tab-pane label="省公司质量管理员配置列表" name="provincialAdministrator" lazy>
            <provincialAdministrator ref="provincialAdministrator"></provincialAdministrator>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
</template>

<script>
import qualityManager from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/qualityManager.vue";
import samplingRatio from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/samplingRatio.vue";
import samplingRatioEngineering from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/samplingRatioEngineering.vue";
import spotCheckQuestions from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/spotCheckQuestions.vue";
import seriousProblem from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/seriousProblem.vue";
import processName from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/processName.vue";
import unprocessedOverdue from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/unprocessedOverdue.vue";
import provinceSpotCheck from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/provinceSpotCheck.vue";
import provincialAdministrator from "@/views/pccw/implementation_link/review_work_order/processQualityFeedback/provincialAdministrator.vue";
export default {
    name: 'processQualityFeedback',
    components: {
      qualityManager,
      samplingRatio,
      samplingRatioEngineering,
      spotCheckQuestions,
      seriousProblem,
      processName,
      unprocessedOverdue,
      provinceSpotCheck,
      provincialAdministrator
    },
    data(){
        return {
          activeName: 'index',
        }
    },
    methods: {     
      handleClick () {
        
      },  
    },
}
</script>

<style scoped>
/* Your component's CSS code goes here */
</style>
