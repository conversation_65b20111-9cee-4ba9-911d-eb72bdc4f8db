<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    >
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="UNITDIM"
          selection
          :staticSearchParam="loadParam"
          :api="tableApi"
        >
        </mssTable>
      </div>
    </mssCard>
    <administratorAdded @onClose='onClose' ref="administratorAdded"></administratorAdded>
  </div>
</template>

<script>
//获取接口地址
import { getConfig, deleteConfig } from '@/api/pccw/review_work_order/processQualityFeedback/api';
import administratorAdded from "./administratorAdded.vue";
// 获取地市:
import {queryAreaListService} from '@/api/common_api.js'
export default {
name: 'qualityManager',
components: {
  administratorAdded
},
data() {
  return{
    // 表格数据API
    tableApi:getConfig,
    //搜索字段配置
    searchConfig: [
      {
          label: '角色',
          type: 'select',
          fieldName: 'roleName',
          options: [
            {
              label:'省公司稽核人员',value:'省公司稽核人员'
            },
            {
              label:'建设单位质量管理员',value:'建设单位质量管理员'
            }
          ]
      },
      {
          label: '用户账号',
          type: 'input',
          fieldName: 'userName'
      },
      {
          label: '建设单位',
          type: 'select',
          fieldName: 'project'
      },
    ],
    UNITDIM: [
      {
        prop: "roleName",
        label: '角色',
      },
      {
        prop: "userName",
        label: '用户账号',
      },
      {
        prop: "userAccount",
        label: '用户名称',
      },
      {
        prop: "project",
        label: '建设单位',
      },
    ],
    labelWidth: {
      default: '130px'
    },
    loadParam: {}
  }
},

created() {
  this.$set(this.searchConfig[2], 'options', JSON.parse(window.sessionStorage.getItem('constructionUnit')))
},
methods: {
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      console.log(this.$refs.table.multipleSelection)
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      deleteConfig({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
  },
  getAddition () {
    this.$refs.administratorAdded.open('管理员配置')
  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  reset() {
    this.search()
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>