<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    >
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="getTemplate">下载模板</el-button>
        <el-upload
          style="display: inline;margin: 0 10px;"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
          >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="UNITDIM"
          selection
          :staticSearchParam="loadParam"
          :api="tableApi"
        >
        </mssTable>
      </div>
    </mssCard>
     <el-dialog
      title="抽查问题分类模板新增"
      :visible.sync="dialogVisible"
      :close-on-click-modal='false' 
      @close='onClose'
      width="85%">
      <mssCard title="抽查问题分类模板">
        <div slot="headerBtn">
          
          <el-button type="primary" @click="addTable" v-if="problemType === '问题大类新增'">新增</el-button>
        </div>
        <div slot="content">
          <el-form ref="exemptionList" :model="form" :rules="rules">
            <mssTable
              ref="table2"
              selection
              :serial="false"
              :stationary="tableData"
              :columns="tableHeader"
              :pagination="false"
            >
            </mssTable>
          </el-form>
        </div>
      </mssCard>
    </el-dialog>
    <ImportErrorMessage ref="importErrorMessage"></ImportErrorMessage>
  </div>
</template>

<script>
//获取接口地址
import { getProblemTemplate, deleteProblemTemplate, addProblemTemplate, getRupload, downloadTemplate } from '@/api/pccw/review_work_order/processQualityFeedback/api';
import { commonDown } from '@/utils/btn'
import ImportErrorMessage from '@/views/pccw/components/importErrorMessage/index'
export default {
name: 'spotCheckQuestions',
components: {
  ImportErrorMessage
},
data() {
  return{
    // 表格数据API
    tableApi: getProblemTemplate,
    //搜索字段配置
    searchConfig: [
      {
          label: '问题详细分类',
          type: 'input',
          fieldName: 'name'
      },
    ],
    // 建设单位
    UNITDIM: [
      {
        prop: "name",
        label: '问题大类',
      },
      {
        prop: "",
        label: '操作',
        formatter: (row, column, cellValue, index) => {
          return (
            <span>
              <span class="mr10">
              {
                row.level == 3 ? '' :<el-button
                  type="text"
                  onClick={() => {
                    this.newAddition(row, index);
                  }}>
                  新增
                </el-button>
              }
              </span>
            </span>
          );
        },
      }
    ],
    labelWidth: {
      default: '130px'
    },
    cityList: [],
    loadParam: {},
    dialogVisible: false,
    form: {},
    rules: {},
    tableData: [],
    problemType: ''
  }
},

created() {
},
methods: {
  getTemplate () {
    commonDown({}, downloadTemplate)
  },
  importFile(params) {
    const param = new FormData()
    param.append('file', params.file)
    getRupload(param).then((res) => {
      if (res.code === '5000') {
          this.$message.warning(res.msg)
      } else if (res.code === '0000') {
        if (res.data.length > 0){
          this.$refs.importErrorMessage.open(res.data)
        } else {
          this.$message.success('导入成功')
        }
        this.$refs.table.getTableData()
      }
    })
  },
  //修改
  getModify (row) {
    console.log(row);
   if (row.level == 1) {
      this.dialogVisible = true
      this.problemType = '问题大类新增'
      this.tableData = [{name:row.name,id: row.id}]
    } else if (row.level == 2){
      this.dialogVisible = true
      this.problemType = '问题细分新增'
      this.tableData = [{large:row.name, id: row.id}]
    }
  },
  newAddition (row) {
    console.log(row)
    if (row.level == 1) {
      this.dialogVisible = true
      this.problemType = '问题小类新增'
      this.tableData = [{large:row.name,id: row.id,level: 2}]
    } else if (row.level == 2){
      this.dialogVisible = true
      this.problemType = '问题细分新增'
      this.tableData = [{large:row.name, id: row.id,level: 3}]
    }
  },
  addTable () {
    this.tableData.push({
      name: '',
      level: 1
    })
  },
  saveONe(row, index) {
    this.commonsetRule(row, index);
    this.$nextTick(() => {
      this.$refs.exemptionList.validate((validate) => {
        if (validate) {
            let data = {
              ...row
            }
            if (this.problemType === '问题大类新增') {
              addProblemTemplate(data).then((res)=>{
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              })
            } else if (this.problemType === '问题小类新增') {
              data = {
                name:row.name,
                parentId: row.id,
                level: row.level
              }
              addProblemTemplate(data).then((res)=>{
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              })
            } else if (this.problemType === '问题细分新增') {
              data = {
                name:row.name,
                parentId: row.id,
                level: row.level
              }
              addProblemTemplate(data).then((res)=>{
                if (res.code == "0000") {
                  this.$message.success("保存成功");
                }
              })
            }
        } else {
          return;
        }
      });
    });
  },
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      deleteProblemTemplate({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    this.dialogVisible = false
  },
  getAddition () {
    this.problemType = '问题大类新增'
    this.tableData = []
    this.dialogVisible = true
  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  reset() {
    this.search()
  },
  commonsetRule(arr, index) {
    this.form = {};
    this.rules = {};
    var a = ['name'];
    a.forEach(item => {
      if (arr[item] === '' || arr[item] == null || arr[item] == undefined) {
        this.rules[`${item}${index}`] = {
          required: true,
          message: "该字段不能为空",
          trigger: ["blur", "change"],
        };
      } else {
        this.rules[`${item}${index}`] = {}
      }
    });
  },
},
computed: {
  tableHeader: {
    get() {
      if (this.problemType === '问题大类新增') {
        return [
          {
            prop: "name",
            label: "问题大类",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`name${index}`}>
                  <el-input 
                    v-model={row.name}>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      } else if (this.problemType = '问题小类新增') {
        return [
          {
            prop: "large",
            label: "问题大类",
            minWidth: 130,
          },
          {
            prop: "name",
            label: "问题小类",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`name${index}`}>
                  <el-input 
                    v-model={row.name}>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];      
      } else if (this.problemType = '问题细分新增') {
        return [
          {
            prop: "large",
            label: "问题小类",
            minWidth: 130,
          },
          {
            prop: "name",
            label: "问题细分",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`name${index}`}>
                  <el-input 
                    v-model={row.name}>
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];       
      }
    },
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>