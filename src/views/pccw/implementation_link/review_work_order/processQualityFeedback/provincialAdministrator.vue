<template>
  <div>
    <mssSearchForm
      v-show="false"
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    >
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="getAddition">新增</el-button>
        <el-button  type="primary" @click="getDelete">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :columns="UNITDIM"
          selection
          :staticSearchParam="loadParam"
          :api="tableApi"
        >
        </mssTable>
      </div>
    </mssCard>
    <administratorAdded @onClose='onClose' ref="administratorAdded"></administratorAdded>
  </div>
</template>

<script>
//获取接口地址
import { getQualityAdministrator, deleteQualityAdministrator } from '@/api/pccw/review_work_order/processQualityFeedback/api';
import administratorAdded from "./administratorAdded.vue";
// 获取地市:
export default {
name: 'qualityManager',
components: {
  administratorAdded
},
data() {
  return{
    // 表格数据API
    tableApi: getQualityAdministrator,
    //搜索字段配置
    searchConfig: [
      // {
      //     label: '工序模板名称',
      //     type: 'input',
      //     fieldName: 'misNumber'
      // },
      // {
      //     label: '工序名称',
      //     type: 'input',
      //     fieldName: 'misNumber'
      // },
      // {
      //     label: '工序描述',
      //     type: 'select',
      //     fieldName: 'misNumber'
      // },
    ],
    // 建设单位
    UNITDIM: [
      {
        prop: "userAccount",
        label: '账号',
      },
      {
        prop: "userName",
        label: '姓名',
      },
    ],
    labelWidth: {
      default: '130px'
    },
    cityList: [],
    loadParam: {},
  }
},

created() {
},
methods: {
  getDelete () {
    if (this.$refs.table.multipleSelection.length > 0) {
      console.log(this.$refs.table.multipleSelection)
      let ids = []
      this.$refs.table.multipleSelection.forEach(item => {
          ids.push(item.id)
      });
      deleteQualityAdministrator({ids:ids.toString()}).then(res => {
        if (res.code == '0000') {
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection();
          this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    } else {
      this.$message({
        showClose: true,
        message: '请至少勾选一条数据',
        type: 'warning'
      });
    }
  },
  onClose () {
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
  },
  getAddition () {
    this.$refs.administratorAdded.open('省公司质量管理员配置')
  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },
  reset() {
    this.search()
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>