<template>
  <div class="design_warning">
    <mssCard title="超期未处理待办的时长（自然日）可支持配置">
      <div slot="headerBtn">
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
        </mssTable>
        </el-form>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { getDurationConfiguration, updateDurationConfiguration } from '@/api/pccw/review_work_order/processQualityFeedback/api';
export default {
  name: "unprocessedOverdue",
  data() {
    return {
      tableData: [],
      form: {},
      rules: {}
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "unprocessedAgent",
            label: "超期未处理代办时长(自然日)",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`unprocessedAgent${index}`}>
                  <el-input  v-model={row.unprocessedAgent}
                    onChange={(value) => {
                      const regex = /^[0-9]*$/; // 正则表达式，匹配最多四位小数的数字
                        if (!regex.test(value)) {
                          // 如果输入值不符合要求，清空输入框
                          row.unprocessedAgent = '';
                          this.$message.error('请输入数字');
                        }
                      this.form[`unprocessedAgent${index}`] = value;
                    }}
                  >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "unprocessedSupervise",
            label: "超期未处理的督办时长(自然日)",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`unprocessedSupervise${index}`}>
                  <el-input  v-model={row.unprocessedSupervise}
                    onChange={(value) => {
                      const regex = /^[0-9]*$/; // 正则表达式，匹配最多四位小数的数字
                        if (!regex.test(value)) {
                          // 如果输入值不符合要求，清空输入框
                          row.unprocessedSupervise = '';
                          this.$message.error('请输入数字');
                        }
                      this.form[`unprocessedSupervise${index}`] = value;
                    }}
                  >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "remainingDuration",
            label: "整改期限剩余时长(自然日)",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`remainingDuration${index}`}>
                  <el-input  v-model={row.remainingDuration}
                    onChange={(value) => {
                      const regex = /^[0-9]*$/; // 正则表达式，匹配最多四位小数的数字
                        if (!regex.test(value)) {
                          // 如果输入值不符合要求，清空输入框
                          row.remainingDuration = '';
                          this.$message.error('请输入数字');
                        }
                      this.form[`remainingDuration${index}`] = value;
                    }}
                  >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "notRectifiedSupervise",
            label: "超期未整改督办时长(自然日)",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`notRectifiedSupervise${index}`}>
                  <el-input  v-model={row.notRectifiedSupervise}
                    onChange={(value) => {
                      const regex = /^[0-9]*$/; // 正则表达式，匹配最多四位小数的数字
                        if (!regex.test(value)) {
                          // 如果输入值不符合要求，清空输入框
                          row.notRectifiedSupervise = '';
                          this.$message.error('请输入数字');
                        }
                      this.form[`notRectifiedSupervise${index}`] = value;
                    }}
                  >
                  </el-input>
                </el-form-item>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    getTable() {
      getDurationConfiguration({}).then((res) => {
        if (res.code == "0000") {
          this.tableData = []
          this.tableData.push(res.data)
        }
      });
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            let obj = JSON.parse(JSON.stringify(row));
            updateDurationConfiguration(obj).then((res) => {
              if (res.code == "0000") {
                this.$message.success("修改成功");
              }
            });
          } else {
            return;
          }
        });
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      var a = [
          'unprocessedAgent',
          'unprocessedSupervise',
          'remainingDuration',
          'notRectifiedSupervise'
        ];
      a.forEach(item => {
        if (arr[item] === '' || arr[item] == null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          this.rules[`${item}${index}`] = {}
        }
      });
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


