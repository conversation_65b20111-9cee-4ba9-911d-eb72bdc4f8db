<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
      @changeSelect="changeSelect"
      :form="searchForm"
    >
    <div slot='customForm' class="content">
      <el-form :inline="true" ref="formInline" :model="formInline" class="search-form" :label-width="labelWidth.default">
        <el-row style="width: 100%;">
          <el-col :span="8" :offset="0">
            <el-form-item label="建设单位" style="width: 100%;" class="elSelect">
              <el-select v-model="formInline.constructUnit" placeholder="--请选择--" style="width: 100%;">
                <el-option v-for="(item,index) in cityList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === '2'">
            <el-form-item label="省公司稽核人员" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.auditor"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === '3'">
            <el-form-item label="工程实施经理-主"  style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.implementationManager"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === '4'">
            <el-form-item label="区县"  style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.affiliatedRegion"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" v-if="whether === '4' || whether === '5'">
            <el-form-item label="施工单位" style="width: 100%;" class="elSelect">
              <el-input v-model="formInline.constructionUnit"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    </mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn" >
        <el-button  type="primary" @click="exportMethod(1)">一键导出</el-button>
        <el-button  type="primary" @click="exportMethod(2)">导出</el-button>
      </div>
      <div slot="content">
        <div class="exemptionList">
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === '1'">
          <el-tab-pane label="工序质量" name="2">
            <mssTable
              ref="table1"
              :api="tableApi"
              :columns="table1"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
          <el-tab-pane label="视频验收" name="1">
            <mssTable
              ref="table11"
              :api="tableApi"
              :columns="table11"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === '2'">
          <el-tab-pane label="工序质量" name="2">
            <mssTable
              ref="table2"
              :api="tableApi"
              :columns="table2"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
          <el-tab-pane label="视频验收" name="1">
            <mssTable
              ref="table22"
              :api="tableApi"
              :columns="table22"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === '3'">
          <el-tab-pane label="工序质量" name="2">
            <mssTable
              ref="table3"
              :api="tableApi"
              :columns="table3"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
          <el-tab-pane label="视频验收" name="1">
            <mssTable
              ref="table33"
              :api="tableApi"
              :columns="table33"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === '4'">
          <el-tab-pane label="工序质量" name="2">
            <mssTable
              ref="table4"
              :api="tableApi"
              :columns="table4"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
          <el-tab-pane label="视频验收" name="1">
            <mssTable
              ref="table44"
              :api="tableApi"
              :columns="table44"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
        </el-tabs>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" v-show="whether === '5'">
          <el-tab-pane label="工序质量" name="2">
            <mssTable
              ref="table5"
              :api="tableApi"
              :columns="table5"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
          <el-tab-pane label="视频验收" name="1">
            <mssTable
              ref="table55"
              :api="tableApi"
              :columns="table55"
              :staticSearchParam="loadParam"
            >
            </mssTable>
          </el-tab-pane>
        </el-tabs>
      </div>
      </div>
    </mssCard>
  </div>
</template>

<script>
//获取接口地址
import { commonDown } from '@/utils/btn';
import moment from 'moment'
import { exportByDimension, exportAll, getTotalList } from '@/api/pccw/implementation_link/review_work_order/quality_summary';

export default {
name: 'qualitySummary',
data() {
  return {
    // 表格数据API
    tableApi: getTotalList,
    //搜索字段配置
    searchConfig: [
      {
        label: '统计维度',
        type: 'select',
        fieldName: 'dimension',
        options: [
          {label: '建设单位', value: '1'},
          {label: '省公司稽核人员、建设单位', value: '2'},
          {label: '建设单位、工程实施经理', value: '3'},
          {label: '建设单位、区县、施工单位', value: '4'},
          {label: '建设单位、施工单位', value: '5'},
        ]
      },
      {
        type: 'cycleDate',
        dateType: 'month',
        label: '月份',
        format: 'yyyy-MM',
        valueFormat: 'yyyy-MM',
        fieldName: 'month',
        clearable: false,
      },
    ],
    // 建设单位
    table1: [
     {
        prop: "titleName",
        label: `工序质量抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table11: [
     {
        prop: "titleName",
        label: `视频验收抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table2: [
     {
        prop: "titleName",
        label: `工序质量抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '省公司稽核人员',
            prop: 'auditor',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table22: [
     {
        prop: "titleName",
        label: `视频验收抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '省公司稽核人员',
            prop: 'auditor',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table3: [
     {
        prop: "titleName",
        label: `工序质量抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '工程实施经理主',
            prop: 'implementationManager',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table33: [
     {
        prop: "titleName",
        label: `视频验收抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '工程实施经理主',
            prop: 'implementationManager',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table4: [
     {
        prop: "titleName",
        label: `工序质量抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '区县',
            prop: 'affiliatedRegion',
            minWidth: 150,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table44: [
     {
        prop: "titleName",
        label: `视频验收抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '区县',
            prop: 'affiliatedRegion',
            minWidth: 150,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table5: [
     {
        prop: "titleName",
        label: `工序质量抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    table55: [
     {
        prop: "titleName",
        label: `视频验收抽查审核情况统计`,
        multilevelColumn: [
          {
            label: '建设单位',
            prop: 'constructUnit',
            minWidth: 150,
          },
          {
            label: '施工单位',
            prop: 'constructionUnit',
            minWidth: 150,
          },
          {
            label: '抽查工单数',
            prop: 'spotCheckOrderCount',
            minWidth: 150,
          },
          {
            label: '抽查通过工单数',
            prop: 'spotCheckOrderPassCount',
            minWidth: 150,
          },
          {
            label: '抽查合格率',
            prop: 'spotCheckRate',
            minWidth: 150,
          },
          {
            label: '应整改待办数',
            prop: 'needRectifyCount',
            minWidth: 150,
          },
          {
            label: '整改完成数',
            prop: 'rectifyPassCount',
            minWidth: 150,
          },
          {
            label: '整改完成率',
            prop: 'rectifyRate',
            minWidth: 150,
          },
        ]
     },
    ],
    formInline: {
      dimension: "1",
      constructUnit: [],
      auditor: '',
      implementationManager: '',
      affiliatedRegion: '',
      constructionUnit: '',
      type: '2'
    },
    labelWidth: {
      default: '130px'
    },
    whether: '1',
    cityList: [],
    searchForm: {
      dimension: "1",
      type: '2',
    },
    loadParam: {
      dimension: "1",
      type: '2',
    },
    activeName: '2',
  }
},
created() {
  this.cityList = JSON.parse(window.sessionStorage.getItem('constructionUnit'))
},
methods: {
  handleClick () {
    let table = ''
    if (this.activeName == '2') {
      table = 'table'+ this.whether
    } else {
      table = 'table'+ this.whether + this.whether
    }
    this.$refs[table].page.current = 1
    let form = {
      ...this.formInline,
      ...this.$refs.searchForm.searchForm,
      type: this.activeName,
      constructUnit: this.formInline.constructUnit.toString()
    }
    this.$refs[table].getTableData(form);
  }, 
  
  onClose () {
  },
  getEditNotes () {
    this.$refs.editNotes.open()
  },
  changeSelect (item) {
    if (item === 'dimension') {
      this.formInline = {
        constructUnit: [],
        auditor: '',
        implementationManager: '',
        affiliatedRegion: '',
        constructionUnit: '',
        type: '2'
      }
      this.activeName = '2'
      this.whether = this.$refs.searchForm.searchForm.dimension
      this.search()
    }
  },
  search() {
    let table = ''
    if (this.activeName == '2') {
      table = 'table'+ this.whether
    } else {
      table = 'table'+ this.whether + this.whether
    }
    this.$refs[table].page.current = 1
    let form = {
      ...this.formInline,
      ...this.$refs.searchForm.searchForm,
      constructUnit: this.formInline.constructUnit.toString()
    }
    if (this.activeName == '2') {
      table = 'table'+ this.whether
    } else {
      table = 'table'+ this.whether + this.whether
    }
    this.$refs[table].getTableData(form);
  },
  reset () {
    console.log('3');
    this.formInline = {
      constructUnit: [],
      auditor: '',
      implementationManager: '',
      affiliatedRegion: '',
      constructionUnit: '',
      type: this.activeName,
    }
    this.$refs.searchForm.searchForm.dimension = '1'
    this.activeName = '2'
    this.whether = '1',
    this.search()
  },
  exportMethod (val) { 
    const params = {
      ...this.formInline,
      ...this.$refs.searchForm.searchForm,
      constructUnit: this.formInline.constructUnit.toString(),
      type: this.activeName
    }
    console.log(params)
    if (val == 1) {
      commonDown({limit: -1}, exportAll)
    } else {
      commonDown(params, exportByDimension)
    }
    
  },
},

}
</script>

<style lang='scss' >
.elSelect{
  .el-form-item__content{
    width: 73%;
  }
}

</style>