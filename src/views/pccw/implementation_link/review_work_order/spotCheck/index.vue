<template>
<div>  
<div class="StagingPointWorkOrder">
 <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
   <el-tab-pane label="省公司稽核人员" name="progressStatus" lazy>
     <progressStatus ref="progressStatus"></progressStatus>
   </el-tab-pane>
   <el-tab-pane label="工程实施经理" name="engineeringImplementation" lazy>
     <engineeringImplementation ></engineeringImplementation>
   </el-tab-pane>
   <el-tab-pane label="工程管理经理" name="engineeringManagement" lazy>
     <engineeringManagement ></engineeringManagement>
   </el-tab-pane>
 </el-tabs>
</div>
</div>
</template>

<script>
import progressStatus from "@/views/pccw/implementation_link/review_work_order/spotCheck/progressStatus.vue";
import engineeringImplementation from "@/views/pccw/implementation_link/review_work_order/spotCheck/engineeringImplementation.vue";
import engineeringManagement from "@/views/pccw/implementation_link/review_work_order/spotCheck/engineeringManagement.vue";
export default {
name: 'spotCheck',
components: {
  progressStatus,
  engineeringImplementation,
  engineeringManagement
},
data(){
 return {
   activeName: 'progressStatus',
 }
},
methods: {     
handleClick () {

},  
},
}
</script>

<style scoped>
/* Your component's CSS code goes here */
</style>
