<template>
  <div>
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
        :form="searchForm"
      ></mssSearchForm>
      <mssCard title="查询结果">
         <div slot="content">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane label="视频验收" name="quality">
              <mssTable
                  ref="table1"
                  :api="tableApi1"
                  :columns="tableHeader1"
                  :staticSearchParam="loadParam1"
                  border>
                </mssTable>
            </el-tab-pane>
            <el-tab-pane label="工序质量" name="personnel">
              <mssTable
                  ref="table2"
                  :api="tableApi2"
                  :columns="tableHeader2"
                  :staticSearchParam="loadParam2"
                  border
                >
                </mssTable>
            </el-tab-pane>    
          </el-tabs>  
            <div style='font-size:14px'>
              <p>备注：</p>
              <p>1、超期未处理待办数量，收到待办后X个自然日未处理；</p>
              <p>2、超期未整改的工单数量，超过整改期限，仍未整改完成的工单数量</p>
            </div>
         </div>
      </mssCard>
  </div>
</template>

<script>

// 获取地市:queryAreaListService
import { getAuditorProgress, getKeyProcessAuditorProgress } from '@/api/pccw/review_work_order/processQualityFeedback/api';
export default {
name: 'progressStatus',
data() {
  return {
      loadParam1: {workOrderType: '视频验收'},
      loadParam2: {workOrderType: '工序质量'},
      tableApi1: getAuditorProgress,
      tableApi2: getKeyProcessAuditorProgress,
      searchConfig: [
        {
					type: 'date1',
					fieldName: 'month',
					label: '月份',
          valueFormat: 'yyyy-MM',
          format: 'yyyy-MM',
          dateType: 'month'
				},
        {
          label: '省公司稽核人员',
          type: 'input',
          fieldName: 'auditor',
        },
      ],
      // 表头
      tableHeader1: [
        {
          prop: "auditor",
          label: "省公司稽核人员",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "receivedPendingNum",
          label: "已收到待办数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "processedPendingNum",
          label: "已处理待办数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "unprocessedPendingNum",
          label: "未处理待办数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "unprocessedOverdueNum",
          label: "超期未处理待办数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "rectificationNum",
          label: "已下发整改通知的工单数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "completedNum",
          label: "整改完成的工单数量",
          align: "center",
          tooltip: true,
          minWidth: 90
        },
        {
          prop: "overdueNum",
          label: "超期未整改的工单数量",
          align: "center",
          tooltip: true,
          minWidth: 90
        },
      ],
      tableHeader2: [
        {
          prop: "auditor",
          label: "省公司稽核人员",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "checkWorkOrderNum",
          label: "已收到待办数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "haveHandleWorkOrderNum",
          label: "已处理待办数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "NotHandleWorkOrderNum",
          label: "未处理待办数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "outTimeNotHandleWorkOrderNum",
          label: "超期未处理待办数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "shouldRectifiedCompletionNo",
          label: "已下发整改通知的工单数量",
          align: "center",
          tooltip: true,
          minWidth: 120
        },
        {
          prop: "rectifiedCompletionNo",
          label: "整改完成的工单数量",
          align: "center",
          tooltip: true,
          minWidth: 90
        },
        {
          prop: "outTimeNotRectifiedCompletionNo",
          label: "超期未整改的工单数量",
          align: "center",
          tooltip: true,
          minWidth: 90
        },
      ],
      activeName: 'quality',
      searchForm: {}
  };
},
methods: {
    handleClick () {
      this.search()
    },
    //重置
    reset(form) {
      if (this.activeName === 'personnel') {
        this.$refs.searchForm.searchForm.workOrderType = '工序质量'
      } else {
        this.$refs.searchForm.searchForm.workOrderType = '视频验收'
      }
      this.search(form)
    },
    //搜索
    search() {
      if (this.activeName === 'quality') {
        this.$refs.table1.page.current = 1
        this.loadParam1 = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
        this.$refs.table1.getTableData(this.$refs.searchForm.searchForm);
      } else {
        this.$refs.table2.page.current = 1
        this.loadParam2 = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
        this.$refs.table2.getTableData(this.$refs.searchForm.searchForm)
      }
    },
},
created() {
},
mounted() {
    // Code to run when the component is mounted
},
};
</script>

<style scoped>
/* Your CSS styles here */
</style>
