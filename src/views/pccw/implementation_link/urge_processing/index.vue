<template>
  <div>
    <!-- <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm> -->
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="loadParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>

import { workFlowList, workFlowListExport} from '@/api/pccw/implementation_link/safety_payment_query/query_list';
import { commonDown } from '@/utils/btn';
export default {
name: 'SafetyPaymentQuery',
data() {
  return{
    //权限
    powerData: [],
    // 表格数据API
    tableApi: workFlowList,
    //搜索字段配置
    searchConfig: [
      {
        label: '标题',
        type: 'select',
        fieldName: 'province',
      },
      {
        label: '市',
        type: 'select',
        fieldName: 'city'
      },
      {
        label: '项目编号',
        type: 'input',
        fieldName: 'projectCode',
      },
      {
        label: '项目名称',
        type: 'input',
        fieldName: 'projectName',
      },
    ],
    //表头
    tableHeader: [
      {
          prop: "taskName",
          label: "标题",
          align: "center",
          tooltip: true,
          minWidth: 100
      },
      {
          prop: "time",
          label: "接收时间",
          align: "center",
          tooltip: true,
          minWidth: 100
      },
      {
          prop: "lastLinkPerson",
          label: "上一环节处理人",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "person",
          label: "当前处理人",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "link",
          label: "当前环节",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      // {
      //   label: "操作",
      //   prop: "_caozuo",
      //   fixed: "right",
      //   minWidth: 100,
      //   formatter: (row, column, cellValue, index) => {
      //     return (
      //       <span>
      //       {
      //         <span class="mr10">
      //           <el-button
      //             type="text"
      //             onClick={() => {
      //               this.viewAttachments(row)
      //             }}
      //           >
      //             处理
      //           </el-button>
      //         </span>
      //       }
      //       </span>
      //     );
      //   },
      // },
    ],
    loadParam: {},
  }
},
computed: {

},

created() {
},

methods: {
  exportHandle() {
    commonDown({limit: -1}, workFlowListExport);
  },
  viewAttachments(row) {
    // this.$router.push({
    //   path: `/pccw_menu/implementation_link/secure_produce_pendingSave`,
    //   query: {
    //     flow: row,
    //     type: "finished",
    //     title: "安全生产费待办工单"
    //   }
    // })
  },
  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },

  reset() {
    this.search()
  },
},

}
</script>

<style>

</style>