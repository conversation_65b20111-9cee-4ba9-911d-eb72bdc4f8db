<!-- 预警时间配置 -->
<template>
  <div class="design_warning">
    <mssCard title="豁免清单">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
        <el-button @click="del">删除</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
        </mssTable>
        </el-form>
      </div>
    </mssCard>
    <magnifyingGlass ref="magnifyingGlass" @showCheckList="showCheckList" :mult-select="multSelect"></magnifyingGlass>
  </div>
</template>

<script>
import { getRemitList, updateRemitList, deleteRemitLis, insertRemitList} from '@/api/pccw/implementation_link/safety_payment_query/query_list';
import { commonMultDel, commonOneDel } from "@/utils/btn";
import magnifyingGlass from "@/views/pccw/components/magnifyingGlass/index.vue";
export default {
  name: "exemptionList",
  components: {
    magnifyingGlass
  },
  data() {
    return {
      tableData: [],
      form: {},
      rules: {},
      multSelect: false
    };
  },
  computed: {
    tableHeader: {
      get() {
        return [
          {
            prop: "projectCode",
            label: "项目编码",
            minWidth:200,
            formatter: (row, column, cellValue, index) => {
              return (
                <el-form-item prop={`projectCode${index}`}>
                  <el-input  v-model={row.projectCode}
                    readonly onFocus={() => {
                    this.openChooseUserDailog(row, index, '豁免清单')
                  }}></el-input>
                </el-form-item>
              );
            },
          },
          {
            prop: "constructionUnit",
            label: "建设单位",
            minWidth: 130,
            formatter: (row, column, cellValue, index) => {
              return (
               <el-input v-model={row.constructionUnit} readonly></el-input>
              );
            },
          },
          {
            label: "操作",
            prop: "_caozuo",
            fixed: "right",
            minWidth: 100,
            formatter: (row, column, cellValue, index) => {
              return (
                <span>
                  <span class="mr10">
                    <el-button
                      type="text"
                      onClick={() => {
                        this.saveONe(row, index);
                      }}
                    >
                      保存
                    </el-button>
                  </span>
                  <span>
                    <el-button
                      type="text"
                      onClick={() => {
                        this.deleteTable(row);
                      }}
                    >
                      删除
                    </el-button>
                  </span>
                </span>
              );
            },
          },
        ];
      },
    },
  },
  mounted() {
    this.getTable();
  },
  methods: {
    showCheckList(val) {
        let realName = []
        let userId = []
        if (val.checkList.length > 0) {
          val.checkList.forEach(item => {
            realName.push(item.constructionUnit)
            userId.push(item.userId)
          })
        }
      if (val.deptParams == '豁免清单') {
        this.$set(this.tableData[val.index], 'constructionUnit' , realName.toString())
        this.$set(this.tableData[val.index], 'projectCode' , userId.toString())
      }
    },
    openChooseUserDailog (row, index, deptParams) { 
      let item = {}
      if (deptParams == '豁免清单') {
        item = {
          excuterNames: row.constructionUnit ? row.constructionUnit : '',
          excuterIds: row.projectCode ? row.projectCode : ''
        }
      }
      this.$refs.magnifyingGlass.init(item, deptParams, index)
    },
    getTable() {
      getRemitList({}).then((res) => {
        if (res.code == "0000") {
          this.tableData  = res.data.data || []
        }
      });
    },
    addTable() {
      let data = {
        projectCode: "",
        constructionUnit: ''
      };
      insertRemitList(data).then((res) => {
        if (res.code == "0000") {
          this.tableData.push({ ...data, id: res.data });
        }
      });
    },
    deleteTable(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteRemitLis({ids: row.id}).then(res => {
            if (res.code == '0000') {
              this.$message.success("删除成功");
              this.getTable();
            } else {
              this.$message.error(`${res.msg || '删除失败'}`)
            }
          })
        }).catch(() => {})
    },
    saveONe(row, index) {
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        this.$refs.editform.validate((validate) => {
          if (validate) {
            let obj = JSON.parse(JSON.stringify(row));
            updateRemitList(obj).then((res) => {
              if (res.code == "0000") {
                this.$message.success("保存成功");
              }
            });
          } else {
            return;
          }
        });
      });
    },
    // 删除
    del() {
      commonMultDel.call(this, {
        data: this.$refs.table.multipleSelection,
        delApi: deleteTimeConfig,
        sucCb: (res) => {
          //成功后的一些操作}}）
          this.$message.success("删除成功");
          this.$refs.table.$refs.table.clearSelection(); // 清空表格勾选
          this.getTable();
        },
      });
    },
    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "day",
        "warner"
      ];
      a.forEach(item => {
        if (arr[item] === '' || arr[item] == null || arr[item] == undefined) {
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          this.rules[`${item}${index}`] = {}
        }
      });
    },
  },
};
</script>
<style lang="scss" >
.design_warning {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
  .is-error {
    margin-bottom: 20px;
  }
  .el-select {
    width: 100%;
  }
}
</style>


