<template>
    <div>
        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
          <el-tab-pane label="详情" name="detail" ref="detail" lazy>
              <detail ref="detail"></detail>
          </el-tab-pane>
          <el-tab-pane label="预警时间配置" name="warningTime" ref="warningTime" lazy>
              <warningTime ref="warningTime"></warningTime>
          </el-tab-pane>
          <el-tab-pane label="豁免清单" name="exemptionList" lazy>
              <exemptionList ref="exemptionList"></exemptionList>
          </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import detail from "@/views/pccw/implementation_link/safety_payment_query/detail.vue";
import warningTime from "@/views/pccw/implementation_link/safety_payment_query/warningTime.vue";
import exemptionList from "@/views/pccw/implementation_link/safety_payment_query/exemptionList.vue";
export default {
  name: 'safety_payment_query',
  components: {
      detail,
      warningTime,
      exemptionList
  },
  data() {
      return {
        activeName: 'detail',
      };
  },
  methods: {
    handleClick () {

    },
      // Your methods here
  },
  mounted() {
      // Code to run when the component is mounted
  },
};
</script>

<style scoped>
/* Your component styles here */
</style>
