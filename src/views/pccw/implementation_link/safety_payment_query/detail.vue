<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
      :form="searchForm"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportHandle1">导出</el-button>
        <el-button type="primary" @click="exportHandle2">下载附件压缩包</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          selection
          :staticSearchParam="loadParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { listSafetyPaymentQuery, download, getExport, downloadZip} from '@/api/pccw/implementation_link/safety_payment_query/query_list';
import { commonDown } from '@/utils/btn';
// 获取地市:queryAreaListService
import {queryAreaListService} from '@/api/common_api.js'
export default {
name: 'SafetyPaymentQuery',
data() {
  return{
    //权限
    powerData: [],
    // 表格数据API
    tableApi: listSafetyPaymentQuery,
    //搜索字段配置
    searchConfig: [
      {
        label: '省',
        type: 'select',
        fieldName: 'province',
        options: [{label: '江西', value: '江西'}]
      },
      {
        label: '市',
        type: 'select',
        fieldName: 'city'
      },
      {
        label: '项目编号',
        type: 'input',
        fieldName: 'projectCode',
      },
      {
        label: '项目名称',
        type: 'input',
        fieldName: 'projectName',
      },
    ],
    //表头
    tableHeader: [
      {
          prop: "province",
          label: "省",
          align: "center",
          tooltip: true,
          minWidth: 100
      },
      {
          prop: "city",
          label: "市",
          align: "center",
          tooltip: true,
          minWidth: 100
      },
      {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "safetyCostBillNum",
          label: "安全生产费报账单号",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "startWorkInitiationCompletionTime",
          label: "开工日期",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "safetyCost",
          label: "设计安全生产费金额",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "isPaid",
          label: "是否支付安全生产费",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "paymentDate",
          label: "应支付日期",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "safetyCostBillAmount",
          label: "安全生产费报账金额",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "safetyCostSum",
          label: "累计支付安全生产费金额",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "createdDate",
          label: "已支付日期",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "paymentProportion",
          label: "支付比例",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "pendingTime",
          label: "支付剩余天数",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "isPaidInTime",
          label: "是否支付及时",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
          prop: "isPaidFully",
          label: "是否足额支付",
          align: "center",
          tooltip: true,
          minWidth: 150
      },
      {
        label: "附件信息",
        prop: "_caozuo",
        fixed: "right",
        minWidth: 100,
        formatter: (row, column, cellValue, index) => {
          return (
            <span>
            { row.billPictureId ?
              <span class="mr10">
                <el-button
                  type="text"
                  onClick={() => {
                    this.viewAttachments(row.billPictureId, '报账单截图');
                  }}
                >
                  报账单截图
                </el-button>
              </span>: ''
            }
            {
              row.payOrderId ? 
                <span class="mr10">
                  <el-button
                    type="text"
                    onClick={() => {
                      this.viewAttachments(row.payOrderId, '支付凭证');
                    }}
                  >
                    支付凭证
                  </el-button>
                </span> : ''
            }  
            </span>
          );
        },
      },
    ],
    loadParam: {
      province: '江西'
    },
    searchForm: {
      province: '江西'
    }
  }
},
computed: {

},

created() {
  this.getAreaList('-2', 1)
},

methods: {
  //导出
  exportHandle1() {
      commonDown({ ...this.$refs.searchForm.searchForm, limit: -1}, getExport);
  },
  exportHandle2() {
      commonDown({ ...this.$refs.searchForm.searchForm, limit: -1}, downloadZip);
  },
  //查看附件
  viewAttachments(kbId, name) {
    const params = {
      kbId: kbId,
      fileType: name
    }
    commonDown(params, download)
  },
  /**
   * 地市
   * @param parentId
   * @param index
   */
  getAreaList(parentId, index) {
    queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
      if (res.code === '0000') {
        const list = []
        res.data.forEach(item => {
          list.push({ label: item.name, value: item.name })
        })
        this.$set(this.searchConfig[index], 'options', list)
      }
    })
  },

  search() {
    this.$refs.table.page.current = 1
    this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
  },

  reset() {
    this.search()
  },
},

}
</script>

<style>

</style>