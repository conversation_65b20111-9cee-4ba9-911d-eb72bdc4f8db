<template>
  <div>

   <flow-detail :showReturn='showReturn' :showApproval='showApproval'  :flow="flow" :showTransact="false" :type="approveType" :msgApproval="msgApproval" ref="flowDetail">
      <el-button style="text-align: right" type="primary" slot="custom-buttons" @click="savePage" v-if="$route.query.type ==='todo' && saveStatus">保存提交</el-button>
      <el-button style="text-align: right" type="primary" slot="custom-buttons" @click="getPending" v-if="$route.query.type ==='todo' && saveStatus">暂挂</el-button>
      <div slot="content">
        <el-card class="card">
            <div class="title">
              <h1 class="heading">安全生产费报账待办工单</h1>
            </div>
        </el-card>
        <el-card class="card">
          <el-descriptions title=""  :column="2" border>
            <el-descriptions-item label="安全生产费报账提醒工单编号">{{searchData.businessId}}</el-descriptions-item>
            <el-descriptions-item label="安全生产费报账提醒工单名称">{{searchData.businessName}}</el-descriptions-item>
            <el-descriptions-item label="工单派发时间" >{{searchData.createdTime}}</el-descriptions-item>
            <el-descriptions-item label="工单要求完成时间">{{searchData.endTime}}</el-descriptions-item>
            <el-descriptions-item label="项目编码">{{searchData.projectCode}}</el-descriptions-item>
            <el-descriptions-item label="项目名称">{{searchData.projectName}}</el-descriptions-item>
            <!-- <el-descriptions-item label="工单状态">{{searchData}}</el-descriptions-item> -->
          </el-descriptions>
        </el-card>
        <mssCard :title="title">
        <div slot="content">
          <div class="operate-btn">
            <el-button type="primary" @click="getAttachment" v-if="$route.query.type ==='todo' && saveStatus">上传附件</el-button>
            <el-button type="primary" @click="saveInfo(1)" v-if="$route.query.type ==='todo' && saveStatus">删除报账单截图附件</el-button>
            <el-button type="primary" @click="saveInfo(2)" v-if="$route.query.type ==='todo' && saveStatus">删除支付凭证附件</el-button>
            <el-button type="primary" @click="getCheck(1)" >查看报账单截图附件</el-button>
            <el-button type="primary" @click="getCheck(2)" >查看支付凭证附件</el-button>
          </div>
          <el-form ref="editform" :model="form" :rules="rules">
            <mssTable
              ref="table"
              :columns="tableHeader"
              :staticSearchParam="loadParam"
              :stationary="tableData"
            >
            </mssTable>
          </el-form>
        </div>
        </mssCard>
      </div>
    </flow-detail>
    <el-dialog
      title="附件上传"
      :visible.sync="dialogVisible"
      :close-on-click-modal='false'
      @close='onClose'
      width="30%">
      <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm" style="width: 70%">
         <el-form-item label="上传类型">
          <el-select v-model="ruleForm.fileType" placeholder="请选择类型">
            <el-option label="报账单截图" value="报账单截图"></el-option>
            <el-option label="支付凭证" value="支付凭证"></el-option>
          </el-select>
         </el-form-item>
        <el-form-item label="上传附件">
          <el-upload
            style="display: inline-block;margin-left: 10px;"
            ref="newFile"
            class="upload-btn"
            action="string"
            :show-file-list="false"
            :auto-upload="true"
            :http-request="importFile"
          >
            <el-button type="primary">上传附件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      title="暂挂"
      :visible.sync="pendingShow"
      :close-on-click-modal='false'
      @close='onClose'
      width="30%">
      <div>
        <el-radio-group v-model="pendingDays">
          <el-radio :label="7">7天</el-radio>
          <el-radio :label="15">15天</el-radio>
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitOrder">暂 挂</el-button>
        <el-button size="mini" style="margin-left: 10px" @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import {getSafetyCost, getSafetyCostDetail, importExcelupload, saveSafetyCost, deleteFile, pendingSafetyCost, download} from '@/api/pccw/implementation_link/safety_payment_alert/alert_list'
import FlowDetail from "@/views/pccw/workflow/flowDetail/index.vue";
import moment from "moment";
import { commonDown } from '@/utils/btn';
export default {
    components: {
      FlowDetail
    },
    name: 'secureProducePending',
    data() {
      return {
        tableData: [],
        dialogEmpower: false,
        dialogTitle: "系统错误-来自工程建设辅助系统",
        form: {},
        rules: {},
        approveType: '',
        flow: {},
        saveStatus: true, //提交审批
        showApproval: false, //提交
        showReturn: false, //退回
        title: '',
        msgApproval: '',
        loadParam: {},
        searchData: {},
        dialogVisible: false,
        ruleForm: {},
        kbId1: '',
        kbId2: '',
        pendingShow: false,
        pendingDays: 7,
        fileData: {
          fileType1: '',
          fileType2: '',
          billPictureId: '',
          payOrderId: ''
        }
      }
    },
    created() {
      let flow = decodeURIComponent(this.$route.query.flow)
      this.flow = JSON.parse(flow); // 获取流程对象
      // this.title = decodeURIComponent(this.$route.query.title);
      this.approveType = decodeURIComponent(this.$route.query.type); // 获取类型信息
      if (this.approveType == 'todo') {
        if (flow.nodeName == '省管项目经理审核') {
          this.saveStatus = false //提交审批
          this.showApproval =  true //提交
          this.showReturn =  true //退回
          this.title = '安全生产费待办工单'
        } else if (flow.nodeName == '项目经理起草') {
          this.saveStatus = true //提交审批
          this.showApproval =  false //提交
          this.showReturn =  false //退回
          this.title = '安全生产费'
        }
      }
      if (this.approveType ==='todo' && !this.saveStatus) {
        this.msgApproval = ''
      } else {
        this.msgApproval = '请先填写信息'
      }
      this.businessId = this.flow.businessId; // 根据这个去后端查数据
      this.loadParam = {businessId: this.flow.businessId}
      this.getPower()
   },
    methods: {
      getCheck (val) {
        let data ={}
        if (val == 1) {
          if (this.fileData.billPictureId) {
            data = {
              kbId: this.fileData.billPictureId,
              fileType: '报账单截图'
            }
          } else {
            return this.$message({ message: '请先上传报账单截图附件',type: 'warning'});
          }

        } else {
          if (this.fileData.payOrderId) {
            data = {
              kbId: this.fileData.payOrderId,
              fileType: '支付凭证'
            }
          } else {
            return this.$message({ message: '请先上传支付凭证附件',type: 'warning'});
          }

        }
        commonDown(data, download)
      },
      submitOrder (){
        console.log(this.pendingDays);
        pendingSafetyCost({pendingDays: this.pendingDays, businessId:this.businessId}).then((res)=>{
          if (res.code == "0000") {
          this.$message({ // 提示
            type: 'success',
            message: '审批成功'
          });
          this.pendingShow = false
          // 跳转页面
          this.$router.push({
            path: `/pccw_menu/pccw_workflow/work_todo`
          })
        }
          // console.log(res, '====')
        })
      },
      closeDialog () {
        this.pendingDays = 7
        this.pendingShow = false
      },
      getPending() {
        this.pendingShow = true
      },
      getAttachment () {
        this.ruleForm = {
          fileType: '',
          businessId: this.flow.businessId
        }
        this.dialogVisible = true
      },
      onClose () {
        if (this.$refs.newFile.uploadFiles.length > 0) {
          this.$refs.newFile.clearFiles()
        }
        this.dialogVisible = false
        this.pendingShow = false
      },
      importFile(params) {
        if (this.ruleForm.fileType == '' || this.ruleForm.fileType == null) {
          return this.$message({
          message: '请先选择上传类型',
          type: 'warning'
          })
        }
        const param = new FormData()
        param.append('file', params.file)
        param.append('fileType', this.ruleForm.fileType)
        param.append('businessId', this.flow.businessId)
        importExcelupload(param).then((res) => {
          if (res.code === '0000') {
            if (this.ruleForm.fileType == '报账单截图') {
              this.fileData.fileType1 = '报账单截图'
              this.fileData.billPictureId = res.data.kbId
            } else {
              this.fileData.fileType2 = '支付凭证'
              this.fileData.payOrderId = res.data.kbId
            }
            this.$message.success('导入成功')
          }
        })
        console.log(this.fileData)
      },
      stringToBoolean(str) {
        return (str === "true") ? true : false;
      },
      getPower() {
        getSafetyCost({businessId: this.flow.businessId}).then((res)=>{
          this.searchData = res.data
          if (res.data.billPictureId) {
            this.fileData.fileType1 = '报账单截图'
            this.fileData.billPictureId = res.data.billPictureId
          }
          if (res.data.payOrderId) {
            this.fileData.fileType2 = '支付凭证'
            this.fileData.payOrderId = res.data.payOrderId
          }
        })
        getSafetyCostDetail({businessId: this.flow.businessId}).then((res)=>{
          this.tableData = []
          res.data.forEach(item => {
            let safetyCostSum =  Number(item.safetyCostSum)
            let safetyCost =  Number(item.safetyCost)
            let paymentProportion = Number(safetyCostSum/safetyCost)
            let isPaidFully = ''
            if (paymentProportion > 0.5) {
              isPaidFully = '是'
            } else {
              isPaidFully = '否'
            }
            this.tableData.push({
              ...item,
              isPaidFully: isPaidFully,
              paymentProportion: paymentProportion
            })
          })
        })
      },
      savePage () {
        this.commonsetRule(this.tableData);
        this.$nextTick(() => {
          this.$refs.editform.validate((validate) => {
            if (validate) {
              let val = []
              this.tableData.forEach(item => {
                val.push({
                  ...item,
                  createdDate: this.$moment(item.createdDate).format('YYYY-MM-DD HH:mm:ss')
                })
              });
              let params = {
                businessId: this.flow.businessId,
                detailList: val
              }
              saveSafetyCost(params).then((res) => {
                if (res.code == "0000") {
                  this.$refs.flowDetail.msgApproval = ''
                  this.$message.success('信息保存成功');
                  this.$refs.flowDetail.openApprovalDialog()
                  // this.$router.push('/home')
                }
              });
            } else {
              return;
            }
          });
        });
      },
      // 添加必填校验
      commonsetRule(arr, index) {
        this.form = {};
        this.rules = {};
        let a = [
          "safetyCostBillNum",
          "safetyCostBillAmount",
          "createdDate"
        ];
        a.forEach((item) => {
          if (index) {
            this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
            this.rules[`${item}${index}`] = {
              required: true,
              message: "该字段不能为空",
              trigger: ["blur", "change"],
            };
          } else {
            for (let index in arr) {
              this.$set(this.form, `${item}${index}`, arr[index][item]); // 必须用set，否则form校验识别不到
              this.rules[`${item}${index}`] = {
                required: true,
                message: "该字段不能为空",
                trigger: ["blur", "change"],
              };
            }
          }
        });
      },
      saveInfo(val) {
        let data ={}
        if (val == 1) {
          if (this.fileData.billPictureId) {
            data = {
              kbId: this.fileData.billPictureId,
              fileType: '报账单截图'
            }
          } else {
            return this.$message({ message: '请先上传报账单截图附件',type: 'warning'});
          }
        } else {
          if (this.fileData.payOrderId) {
            data = {
              kbId: this.fileData.payOrderId,
              fileType: '支付凭证'
            }
          } else {
            return this.$message({ message: '请先上传支付凭证附件',type: 'warning'});
          }
        }
        deleteFile(data).then((res)=>{
          if (res.code == '0000') {
            if (val == 1) {
              this.fileData.fileType1 = ''
              this.fileData.billPictureId = null
            } else {
              this.fileData.fileType2 = ''
              this.fileData.payOrderId = null
            }
            this.$message.success("删除成功");
          } else {
            this.$message.error(`${res.msg || '删除失败'}`)
          }
        })
        // this.dialogEmpower = true;
      },
    },
    computed: {
      tableHeader: {
        get() {
          return [
            {
                  prop: "province",
                  label: "省",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
              {
                  prop: "city",
                  label: "地市",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
            {
              prop: "constructUnit",
              label: "施工单位",
              align: "center",
              tooltip: true,
              minWidth: 150
            },
              {
                  prop: "startWorkInitiationCompletionTime",
                  label: "开工报告提交日期",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
              {
                  prop: "batchNum",
                  label: "批次",
                  align: "center",
                  tooltip: true,
                  minWidth: 150,
              },
              {
                  prop: "safetyCost",
                  label: "设计会审金额-安全生产费（不含税）",
                  align: "center",
                  tooltip: true,
                  minWidth: 150,

              },
              // 手填
              {
                  prop: "safetyCostBillNum",
                  label: "安全生产费报账单号",
                  align: "center",
                  tooltip: true,
                  minWidth: 200,
                  formatter: (row, column, cellValue, index) => {
                    if (this.approveType ==='todo' && this.saveStatus) {
                      return (
                        <div>
                          <el-form-item prop={`safetyCostBillNum${index}`}>
                            <el-input
                              onChange={(value) => {
                                const regex = /^[A-Za-z0-9]+$/; // 正则表达式
                                if (!regex.test(value)) {
                                  // 如果输入值不符合要求，清空输入框
                                  row.safetyCostBillNum = '';
                                  this.$message.error('请输入字母和数字类型');
                                }
                              }}
                              v-model={row.safetyCostBillNum}
                            >
                            </el-input>
                          </el-form-item>
                        </div>
                      );
                    } else {
                      return row.safetyCostBillNum
                    }
                  },
              },
              //手填
              {
                  prop: "safetyCostBillAmount",
                  label: "安全生产费报账金额",
                  align: "center",
                  minWidth: 200,
                  formatter: (row, column, cellValue, index) => {
                    if (this.approveType ==='todo' && this.saveStatus) {
                      return (
                        <div>
                          <el-form-item prop={`safetyCostBillAmount${index}`} >
                            <el-input
                              v-model={row.safetyCostBillAmount}
                              onChange={(value) => {
                              const regex = /^[0-9]+(\.[0-9]+)?$/; // 正则表达式
                              if (!regex.test(value)) {
                                // 如果输入值不符合要求，清空输入框
                                row.safetyCostBillAmount = '';
                                this.$message.error('请输入正数字')
                              }
                              // let safetyCostBillAmount =  Number(row.safetyCostBillAmount)
                              // let safetyCost =  Number(row.safetyCost)
                              // let paymentProportion = Number(safetyCostBillAmount/safetyCost)
                              // this.tableData[index].paymentProportion = paymentProportion
                              // if (this.tableData[index].paymentProportion > 0.5) {
                              //   this.$set(this.tableData[index],'isPaidFully', '是')
                              // } else {
                              //   this.$set(this.tableData[index],'isPaidFully', '否')
                              // }
                            }}
                            >
                            </el-input>
                          </el-form-item>
                        </div>
                      );
                    } else {
                      return row.safetyCostBillAmount
                    }
                  },
              },
              {
                  prop: "paymentDate",
                  label: "应支付日期",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
              {
                  prop: "safetyCostSum",
                  label: "累计支付安全生产费金额",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
              //手选
              {
                  prop: "createdDate",
                  label: "实际支付日期",
                  align: "center",
                  tooltip: true,
                  minWidth: 300,
                  formatter: (row, column, cellValue, index) => {
                    if (this.approveType ==='todo' && this.saveStatus) {
                      return (
                        <div>
                          <el-form-item prop={`createdDate${index}`}>
                            <el-date-picker
                              v-model={row.createdDate}
                              type="datetime"
                              onChange = {() => {
                                let createdDate = moment(row.createdDate).valueOf()
                                let paymentDate = moment(row.paymentDate).valueOf()
                                if (createdDate < paymentDate) {
                                  this.$set(this.tableData[index],'isPaidInTime', '是')
                                } else {
                                  this.$set(this.tableData[index],'isPaidInTime', '否')
                                }
                                }}
                              placeholder="选择日期时间">
                            </el-date-picker>
                          </el-form-item>
                        </div>
                      );
                    } else {
                      return row.createdDate
                    }
                  },
              },
              {
                  prop: "paymentProportion",
                  label: "支付比例",
                  align: "center",
                  tooltip: true,
                  minWidth: 150,
                  formatter: (row, column, cellValue, index) => {
                    if (row.paymentProportion) {
                      if (!/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(row.paymentProportion)) {
                        return  (Number(row.paymentProportion)* 100).toFixed(2) +'%'
                      } else {
                        return  Number(row.paymentProportion)* 100 +'%'
                      }
                    } else {
                      return row.paymentProportion
                    }
                  }
              },
              {
                  prop: "isPaidInTime",
                  label: "是否及时支付",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
              {
                  prop: "isPaidFully",
                  label: "是否足额支付",
                  align: "center",
                  tooltip: true,
                  minWidth: 150
              },
          ];
        },
      },
    },
}
</script>

<style>
.card {
  margin-bottom: 20px;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  box-shadow: 0px 0px 5px 0px rgba(0, 132, 207, 0.2);
  border-radius: 4px;
}
.title {
  text-align: center;
}

.heading {
  margin-top: 0;
  font-size: 24px;
  font-weight: bold;
}




</style>
