<!-- 普通的选择组件 -->
<template>
  <div class="ChooseUser">
    <el-dialog
      :visible.sync="showDialog"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      :modal="true"
      append-to-body
      @close="close"
    >
      <div class="choose" :style="{ width: '50%' }">
        <div class="chooseBody includeFoot">
          <el-input
            v-model="keyInput"
            class="key-search"
            placeholder="请输入关键字查询"
            @keyup.enter.native="keyWordsSearch"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              style="cursor: pointer"
              title="查询"
              @click="keyWordsSearch"
            ></i>
          </el-input>
          <mssTable
            ref="table"
            get-change
            :serial="false"
            :columns="columns"
            row-key="userId"
            selection
            hideAllSelection
            :stationary="tableData"
            :single-choice="!multSelect"
            table-size="mini"
            highlight-current-row
            :pagination="false"
            @rowChecked="selectHandle"
          />
        </div>
        <div class="chooseFoot" style="padding:0">
          <el-pagination
            small
            :current-page.sync="page.current"
            :page-size="page.size"
            background
            :pager-count="5"
            layout="prev,pager,next,total"
            :total="page.total"
            @current-change="currentChange"
          />
        </div>
      </div>
      <div class="choose" :style="{ width: '50%' }">
        <div class="chooseTitle">
          <span class="iconfont">选择结果</span>
          <span class="tips">(双击名字删除)</span>
        </div>
        <div class="chooseBody">
          <el-checkbox-group v-model="delList">
            <el-checkbox
              v-for="(item, index) in checkList"
              :key="index"
              class="showList"
              :label="item.realName"
            >
              <span @dblclick="delPeople(item, 'single')">
                {{
                  item.realName
                }}
              </span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="chooseFoot">
          <el-checkbox v-model="checkedAll" @change="checkAll">全选</el-checkbox>
          <span class="del" @click="delPeople(delList, 'multiple')">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitOrder">确 定</el-button>
        <el-button size="mini" style="margin-left: 10px" @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDurationList} from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
import { validEmail } from '@/utils/validate'
import { userList} from '@/api/pccw/analysis_link/real_time_reports_alerts/api'
import { queryList } from '@/api/pccw/analysis_link/videoAcceptance/exemptionList';
import { queryDesignUnitPerson, queryProjectManagementMajor } from '@/api/pccw/analysis_link/design_survey_specify_report_alert/api'
import { queryCooperationCompanys, queryPersonByCooperationCompany, queryPersonByCity} from '@/api/pccw/safety_production/safetyWork'
import { getUser } from '@/api/pccw/review_work_order/processQualityFeedback/api';
import { getProjectInformation} from '@/api/pccw/implementation_link/safety_payment_query/query_list';
export default {
  name: 'magnifyingGlass',
  props: {
    // 是否多选
    multSelect: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      showTree: false,
      title: '选择人员',
      checkList: [],
      delList: [],
      defKeys: [],
      checkedAll: false, // 是否全选
      resolveData: [],
      areaTreeData: '', // 组织结构数据
      defaultProps: {
        label: 'text',
        children: 'children',
        isLeaf: 'leaf'
      },
      columns: [{ prop: 'realName', label: '名称' }],
      tableData: [],
      tableParams: {},
      page: {
        total: 0,
        size: 10,
        current: 1
      },
      keyInput: '',
      deptParams: '',
      personParams: {},
      showDialog: false,
      index: null,
      firstDeptName: '',
      designUnit: '',
      primaryMajor: '',
      units: ''
    }
  },
  methods: {
    /**
     * 弹框初始化
     * @param item
     * @param val
     */
    init(item, deptParams, index) {
      // console.log(this.$refs.table.$refs.table.clearSelection())
      console.log("item",item);
      console.log("deptParams",deptParams)
      this.showTree = true
      this.checkList = []
      this.keyInput = ''
      this.index =  index
      if (item && item.excuterNames && item.excuterIds) {
        const excuterNames = item.excuterNames.split(',') //姓名
        const excuterIds = item.excuterIds.split(',') //id
        excuterNames.forEach((ele, index) => {
          this.checkList.push({
            realName: ele,
            userId: excuterIds[index]
          })
        })

      } else {
        this.checkedAll = false // 重置全选
        this.delList = []
        this.tableCheck()
      }
      if (deptParams === '设计单位人员') {
        this.designUnit = item.designUnit
      }
      if (deptParams === '偏差标准项目管理专业') {
        this.primaryMajor = item.primaryMajor
      }
      if (deptParams === '合作单位名称' || '合作单位安全接口人' || '地市安全管理员' || '监理员姓名') {
        this.units = item.units
      }
      console.log("checkList", this.checkList)
      this.treeCheck()
      this.firstDeptName = item.constractUnit ? item.constractUnit : ''
      this.showDialog = true
      this.deptParams = deptParams
      this.tableParams = {limit:10, page: 1}
      this.getUserList(this.tableParams)
    },

    /**
     * 提交表单
     */
    submitOrder() {
      this.showDialog = false
      this.$emit('showCheckList', {
        checkList: this.checkList,
        index: this.index,
        deptParams: this.deptParams
      })
      this.defKeys = []
    },
    /**
     * 获取人员列表数据
     * @param params
     */
    getUserList(params) {
      if (this.deptParams === '设计时长') {
        let val = {
          ...params,
          name: params.searchValue
        }
        getDurationList(val)
        .then((res) => {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                realName: item.name,
                userId: item.name,
              })
            });
          } else {
            this.tableData = []
          }
        })
        .catch(() => {
          this.tableLoading = false
          this.tableData = []
        })
      } else if (this.deptParams === '部门领导(副)' || this.deptParams === '地市工建经理' || this.deptParams === '省公司科室经理' || this.deptParams === '省管项目经理' || this.deptParams === '省工建领导' || this.deptParams === '设计统筹人员') {
        let val = {
          ...params,
          context: params.searchValue
        }
        userList(val)
        .then((res) => {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                realName: item.context,
                userId: item.userId,
              })
            });
          } else {
            this.tableData = []
          }
        })
        .catch(() => {
          this.tableLoading = false
          this.tableData = []
        })
      } else if (this.deptParams === '地市质量管理员配置' || this.deptParams === '省公司质量管理员') {
        let val = {
            ...params,
            name: params.searchValue,
            userId: '',
            roleName:'',
            constractUnit: this.firstDeptName,
            realName: params.searchValue
        }
        queryList(val).then((res)=> {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                ...item,
                realName: item.realName,
                userId: item.userId,
              })
            });
          } else {
            this.tableData = []
          }
        })
      } else if (this.deptParams === '设计单位人员') {
        let val = {
          ...params,
          designUnit: this.designUnit,
          designUnitPerson: params.searchValue
        }
        queryDesignUnitPerson(val).then((res)=> {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                ...item,
                realName: item.realName,
                userId: item.userId,
              })
            });
          } else {
            this.tableData = []
          }
        })
      } else if (this.deptParams === '偏差标准项目管理专业') {
        let val = {
          ...params,
          primaryMajor: this.primaryMajor,
          designUnitPerson: params.searchValue
        }
        queryProjectManagementMajor(val).then((res)=> {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                ...item,
                realName: item.projectManagementMajor,
                userId: item.projectManagementMajor,
              })
            });
          } else {
            this.tableData = []
          }
        })
      } else if (this.deptParams === '合作单位名称') {
        let val = {
          ...params,
          unitName: params.searchValue,
          unitType: this.units
        }
        queryCooperationCompanys(val).then((res)=> {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                ...item,
                realName: item.unitName,
                userId: item.unitId,
              })
            });
          } else {
            this.tableData = []
          }
        })
      } else if (this.deptParams === '合作单位安全接口人' || this.deptParams === '监理员姓名') {
        let val = {
          ...params,
          unitInterfacePerson: params.searchValue,
          unitName: this.units
        }
        queryPersonByCooperationCompany(val).then((res)=> {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                ...item,
                realName: item.unitInterfacePerson,
                userId: item.unitId,
              })
            });
          } else {
            this.tableData = []
          }
        })
      } else if (this.deptParams === '管理员配置') {
        let val = {
          ...params,
          realName: params.searchValue,
        }
        getUser(val).then((res)=> {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                ...item,
                realName: item.realName,
                account: item.account,
              })
            });
          } else {
            this.tableData = []
          }
        })
      } else if (this.deptParams === '地市安全管理员') {
        let val = {
          ...params,
          unitName: params.searchValue,
          city: this.units,
        }
        queryPersonByCity(val).then((res)=> {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                ...item,
                realName: item.citySafetyAdmin,
                userId: item.citySafetyAdmin,
              })
            });
          } else {
            this.tableData = []
          }
        })
      } else if (this.deptParams === '豁免清单') {
        let val = {
          ...params,
          projectCode: params.searchValue
        }
        getProjectInformation(val).then((res)=> {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = []
            data.forEach(item => {
              this.tableData.push({
                ...item,
                realName: item.projectCode + ',' + item.constructionUnit,
                userId: item.projectCode,
              })
            });
          } else {
            this.tableData = []
          }
        })
      }
      this.tableCheck()
    },

    keyWordsSearch() {
      this.getUserList({
        ...this.tableParams,
        searchValue: this.keyInput
      })
    },
    selectHandle(selection, row) {
      if (!this.multSelect) {
        this.checkList = [row]
      } else {
        let flag = false
        // 是否选中
        selection.forEach((item) => {
          if (item.userId === row.userId) {
            flag = true
          }
        })
        if (flag) {
          // 查询是否存在数组中，添加没有之前选择的数据
          let flag1 = false
          this.checkList.forEach((item) => {
            if (item.userId === row.userId) {
              flag1 = true
            }
          })
          !flag1 && this.checkList.push(row)
        } else {
          // 删除取消选择的数据
          for (let index = 0; index < this.checkList.length; index++) {
            const element = this.checkList[index]
            if (element.userId === row.userId) {
              this.checkList.splice(index, 1)
              break
            }
          }
        }
      }
    },
    currentChange(page) {
      this.getUserList({
        ...this.tableParams,
        limit: 10,
        page: page,
        searchValue: this.keyInput
      })
    },

    /**
     * 删除事件
     * @param val
     * @param type
     */
    delPeople(val, type) {
      this.defKeys = [] // 树回显数据
      // 单个删除
      if (type === 'single') {
        this.checkList.splice(this.checkList.indexOf(val), 1)
      } else if (type === 'multiple') {
        for (var j = 0; j < val.length; j++) {
          for (var i = 0; i < this.checkList.length; i++) {
            if (this.checkList[i].realName === val[j]) {
              this.checkList.splice(i, 1)
              i -= 1
            }
          }
        }
      }
      this.checkedAll = false // 重置全选
      this.delList = []
      this.treeCheck()
      this.tableCheck()
      // console.log(this.delList,'=====')
    },
    /**
     * 树回显勾选事件
     */
    treeCheck() {
      this.defKeys = []
      this.checkList.forEach((item) => {
        this.defKeys.push(item.userId)
      })
      this.defKeys.concat()
    },
    // 表格勾选
    tableCheck() {
      this.$nextTick(() => {
        this.$refs.table.$refs.table.clearSelection()
        this.tableData.forEach(item => {
          this.checkList.forEach(item2 => {
            if (item.userId === item2.userId) {
              this.$refs.table.$refs.table.toggleRowSelection(item, true)
            }
          })
        })
      })
    },
    /**
     * 全选事件
     * @param val
     */
    checkAll(val) {
      if (validEmail) {
        this.checkList.forEach((item) => {
          this.delList.push(item.realName)
        })
      } else {
        this.delList = []
      }
    },
    closeDialog() {
      this.showDialog = false
    },
    close(){
      this.showTree = false
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss">
.choose {
  display: inline-block;
  .iconfont {
    color: #5887ff;
    font-weight: 400;
    margin-right: 0.5rem;
  }
  .chooseTitle {
    height: 20px;
    line-height: 20px;
    color: #000;
    font-weight: bold;
    padding-left: 0.5rem;
    .iconfont {
      font-size: 13px;
    }
    .tips {
      font-size: 12px;
      font-weight: normal;
      color: #ff5a09;
      margin-left: 0.5rem;
    }
  }
  .chooseBody {
    width: 100%;
    height: 23rem;
    background: #ffffff;
    border: 1px solid #dddddd;
    margin-top: 0.7rem;
    padding: 0.2rem;
    overflow: auto;
    .showList {
      display: block;
      padding: 0.2rem 1rem;

      &:hover {
        background: #ebf1ff;
      }
    }
  }
  .chooseFoot {
    height: 2rem;
    line-height: 2rem;
    width: 100%;
    background: #f9fbff;
    border: 1px solid #dddddd;
    border-top: none;
    padding: 0 1rem;
    .el-pagination {
      padding: 0;
      .el-pager {
        height: 22px;
      }
    }
    .el-checkbox__label {
      font-size: 0.6rem;
    }
  }
  .del {
    float: right;
    cursor: pointer;
    font-size: 1.4rem;
    color: #5887ff;
  }
}
</style>
