<template>
  <div>
    <el-dialog title="错误信息" :visible.sync="dialogTableVisible">
          <el-table :data="gridData" border>
            <el-table-column property="lineNum" label="行数" width="100"></el-table-column>
            <el-table-column property="message" label="信息" ></el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogTableVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogTableVisible = false">确 定</el-button>
          </div>
      </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'ImportErrorMessage',
  data() {
    return {
      dialogTableVisible: false,
      gridData: []
    }
  },
  methods: {
    open(data) {
      this.dialogTableVisible = true
      this.gridData = data
    }
  }
} 
</script>

