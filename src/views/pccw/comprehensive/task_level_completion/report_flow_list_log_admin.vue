/** * @author: <PERSON><PERSON><PERSON> * @date: 2024-06-20 * @description:
查询完工任务竣工流程启动记录 */
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="任务竣工待办流程启动记录-管理1216">
      <div slot="headerBtn">
        <el-input v-model="tableName" placeholder="表名"></el-input>
        <el-button @click="exportData()">导出数据</el-button>
        <el-button @click="updatePerson()">修改施工负责人</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
          :serial="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <approval-dialog ref="approvalDialog" @DialogChange="handleDialogChange" />

    <!-- 添加或修改运行时流程变量对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="150px">
        <el-form-item label="流程是否发起成功" prop="name">
          <el-input v-model="form.isFlowSuccess" placeholder="请输入" />
        </el-form-item>
        <!--  <el-form-item label="施工专业负责人-姓名" prop="name">
          <el-input v-model="form.createName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="施工专业负责人-id" prop="name">
          <el-input v-model="form.createBy" placeholder="请输入" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>
    </el-dialog>
    <!-- 抽屉 -->
    <el-drawer
      title="检测信息"
      :visible="drawerVisible"
      direction="ltr"
      @close="closeDrawer"
      size="50%"
    >
    <div class="drawer-content">
      <div v-html="drawerData" />
      <el-divider></el-divider>
      <template>
        <div class="task-list">
          1. 任务清除：移除所有bid<br />
          2. 任务补全：设置所有符合条件的数据为当前bid<br />
          3. 转移文件：关联文件变更为目前的bid
          4. 手动转移文件：转移bid,被转移bid
        </div>
      </template>
      <el-input v-model="input" placeholder="请输入处理类型"></el-input>
      <el-input v-model="input2" placeholder="请输入错误描述"></el-input>
      <el-button type="primary" @click="maintain()">错误处理</el-button>
      处理结果：
      {{ processingResult }}
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import ApprovalDialog from "./ApprovalDialog.vue"
  import { commonOneDel, commonMultDel, commonDown } from "@/utils/btn"
  import {
    editReportFlow,
    delReportFlow,
    selectComReportFlowInfoList,
    selectComReportFlowInfoList2,
    toSendFlow,
    getReportById,
    updateTaskList,
    errorCheck,
    exportTable
  } from "@/api/pccw/comprehensive/completion"
  import { generateUUID } from "@/utils"
  export default {
    name: "ReportFlowListLog",
    components: {
      ApprovalDialog
    },
    data() {
      return {
        processingResult: "",
        tableName: "",
        input: null,
        input2: null,
        errorObject: null,
        drawerVisible: false, // 控制抽屉的显示与隐藏
        drawerData: "", // 存储抽屉显示的数据
        tableApi: selectComReportFlowInfoList2,
        //搜索字段配置
        searchConfig: [
          {
            type: "input",
            fieldName: "completionReportNo",
            label: "完工报告编号"
          },
          {
            type: "input",
            fieldName: "projectCode",
            label: "项目编号"
          },
          {
            type: "input",
            fieldName: "companyName",
            label: "施工单位"
          },
          // {
          //   type: 'input',
          //   fieldName: 'taskCodeList',
          //   label: '失败任务编码集合'
          // },
          {
            type: "input",
            fieldName: "businessId",
            label: "流程id"
          },
          {
            type: "input",
            fieldName: "title",
            label: "流程主题"
          },
          {
            type: "select",
            label: "流程是否发起成功",
            fieldName: "isFlowSuccess",
            options: [
              { label: "失败", value: "fail" },
              { label: "成功", value: "success" }
            ]
          },
          {
            type: "input",
            fieldName: "errMessage",
            label: "失败原因"
          },
          {
            type: "input",
            fieldName: "createName",
            label: "施工单位负责人-姓名"
          },
          {
            type: "input",
            fieldName: "createBy",
            label: "施工单位负责人id"
          },
          {
            type: "input",
            fieldName: "supervisionUserName",
            label: "监察经理/姓名"
          },
          {
            type: "input",
            fieldName: "supervisionUserId",
            label: "监察经理/id"
          },
          {
            type: "input",
            fieldName: "engineImplementationManager",
            label: "实施经理/姓名"
          },
          {
            type: "input",
            fieldName: "implementUserId",
            label: "实施经理/id"
          }
          // {
          //   type: "input",
          //   fieldName: "createByAccount",
          //   label: "施工单位负责人/系统账号"
          // }
        ],
        //表格头部配置
        tableHeader: [
          {
            prop: "businessId",
            label: "流程id",
            align: "center",
            tooltip: true,
            minWidth: 240
          },
          {
            prop: "title",
            label: "流程主题",
            align: "center",
            tooltip: true,
            minWidth: 180
          },
          {
            prop: "completionReportNo",
            label: "完工报告编号",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "projectCode",
            label: "项目编号",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "companyName",
            label: "施工单位",
            align: "center",
            tooltip: true,
            minWidth: 230
          },
          {
            prop: "createByAccount",
            label: "施工单位负责人-系统账号",
            align: "center",
            minWidth: 160,
            tooltip: true
          },
          {
            prop: "createName",
            label: "施工单位负责人-姓名",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "createBy",
            label: "施工单位负责人/(创建人)-id",
            align: "center",
            minWidth: 160,
            tooltip: true
          },
          {
            prop: "supervisionUserName",
            label: "监察经理-姓名",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "supervisionUserId",
            label: "监察经理-id",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "engineImplementationManager",
            label: "实施经理-姓名",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "implementUserId",
            label: "实施经理-id",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          // {
          //   prop: "updateBy",
          //   label: "更新人",
          //   align: "center",
          //   tooltip: true
          // },
          {
            prop: "isFlowSuccess",
            label: "流程是否发起成功",
            fixed: "right",
            align: "center",
            tooltip: true,
            formatter: row => {
              return (
                <span>
                  {row.isFlowSuccess == "success" ? (
                    <el-tag type="success">成功</el-tag>
                  ) : (
                    <el-tag type="danger">失败</el-tag>
                  )}
                </span>
              )
            }
          },
          {
            prop: "errMessage",
            label: "失败原因",
            align: "center",
            tooltip: true,
            minWidth: 200
          },
          {
            prop: "taskCodeList",
            label: "关联任务编码集合",
            align: "center",
            tooltip: true,
            minWidth: 280
          },
          {
            prop: "createTime",
            label: "创建时间",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "updateTime",
            label: "更新时间",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "status",
            label: "流程状态",
            align: "center",
            tooltip: true
          },
          {
            label: "操作",
            align: "center",
            fixed: "right",
            minWidth: "100px",
            formatter: row => {
              if (row.isFlowSuccess != "success") {
                return (
                  <span>
                    {
                      <span
                        class="table_btn mr10"
                        onClick={() => {
                          this.gotoSendFlow(row, 1)
                        }}
                      >
                        重新下发
                      </span>
                    }
                  </span>
                )
              } else {
                return ""
              }
            }
          },
          {
            label: "未下发",
            align: "center",
            fixed: "right",
            Width: "10px",
            formatter: row => {
              if (row.isFlowSuccess != "success") {
                return (
                  <span>
                    {
                      <span
                        class="table_btn mr10"
                        onClick={() => {
                          this.gotoDel(row)
                        }}
                      >
                        删除
                      </span>
                    }
                  </span>
                )
              } else {
                return ""
              }
            }
          },
          {
            label: "编辑",
            align: "center",
            fixed: "right",
            minWidth: "70px",
            formatter: row => {
              return (
                <span>
                  {
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.handleUpdate(row)
                      }}
                    >
                      编辑
                    </span>
                  }
                </span>
              )
            }
          },
          {
            label: "已下发",
            align: "center",
            fixed: "right",
            minWidth: "100px",
            formatter: row => {
              if (row.isFlowSuccess === "success") {
                // if(1=1){
                return (
                  <span class="table_btn mr10">
                    {
                      <div
                        onClick={() => {
                          this.toUpdateTaskList(row)
                        }}
                      >
                        补发任务
                      </div>
                    }
                    {
                      <div
                        onClick={() => {
                          this.toErrorCheck(row)
                        }}
                      >
                        错误检测
                      </div>
                    }
                  </span>
                )
              } else {
                return ""
              }
            }
          }
        ],
        tableLoading: false, // 表格loading
        isSubmitting: false, // 标识流程提交
        loadParam: {},
        staticSearchParam: {},
        // 表单参数
        form: {},
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false
      }
    },
    created() {},
    mounted() {},
    methods: {
      async exportData() {
        let res = await exportTable(this.tableName)
        console.log("res", res)
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", this.tableName + ".sql")
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      },
      // 修理
      async maintain() {
        let row = this.errorObject
        row.errorType = "修复"
        row.errorCode = this.input
        row.errorDescribe = this.input2

        let res = await errorCheck(row)
        this.processingResult = res.data
      },
      async toErrorCheck(row) {
        row.errorType = null // 避免出现错误，一般不会
        let res = await errorCheck(row)
        // res.data = "这是一个很长的文本内容，可能包含换行符。<br>这个地方应该换行。";
        // this.$message.success({
        //   message: res.data,
        //   duration: 3000, // 设置消息显示时长，单位是毫秒
        //   customClass: 'custom-message', // 添加一个自定义 class
        // });
        this.errorObject = row
        let baseStr =
          row.businessId +
          "<br>" +
          row.completionReportNo +
          "<br>" +
          row.projectCode +
          "<br>" +
          row.companyName +
          "<br>" +
          row.createName +
          "<hr>检测结果：<br>"
        this.drawerData = baseStr + res.data
        this.drawerVisible = true

        this.$message({
          message: `<div>${res.data}</div>`, // 使用div来包裹消息，以便更好地控制样式
          type: "warning",
          duration: 3000, // 毫秒
          customClass: "custom-message", // 添加一个自定义 class
          showClose: true,
          center: true,
          dangerouslyUseHTMLString: true // 允许使用HTML字符串（注意安全性）
        })
      },
      closeDrawer() {
        this.errorObject = null
        this.drawerVisible = false // 关闭抽屉
      },
      async toUpdateTaskList(row) {
        let res = await updateTaskList(row)
        if (res.code === "0000") {
          this.$message.success(res.data)
        }
      },
      gotoEdit(row) {
        let res = editReportFlow(row)
      },
      /** 修改按钮操作 */
      async handleUpdate(row) {
        console.log(row)
        // this.reset()
        // const id = row.id
        let res = await getReportById(row.id)

        this.form = res.data
        this.open = true
        this.title = "修改"
      },
      handleDialogChange() {
        this.$refs.table.getTableData()
      },
      /** 提交按钮 */
      submitForm() {
        if (this.form.id != null) {
          editReportFlow(this.form).then(res => {
            if (res.code === "0000" && res.data === 1) {
              this.$message.success("修改成功")
              this.$refs.table.getTableData()
            }
            this.open = false
          })
        } else {
          // addVariable(this.form).then(response => {
          //   this.$message.success("新增成功")
          //   this.open = false
          //   this.$refs.table.getTableData()
          // })
        }
      },
      // 审批（提交）对话框
      updatePerson() {
        this.$refs.approvalDialog.approvalDialog = true
      },
      validateFlow(flow) {
        const errors = []

        // 验证任务编码
        if (!flow.taskCodeList) {
          errors.push("任务编码为空！")
        }

        // 假设flow对象现在直接有一个completionReportNo属性
        if (!flow.completionReportNo) {
          errors.push("完工单号为空！")
        }

        // 验证项目名称
        if (!flow.projectName) {
          errors.push("项目名称为空！")
        }

        // 验证施工单位及其负责人信息
        if (!flow.companyName) {
          errors.push("施工单位为空！")
        }
        if (!flow.createName) {
          errors.push("施工单位负责人姓名为空！")
        }
        if (!flow.createBy) {
          errors.push(
            `施工单位负责人${flow.createName}的userId为空！请检查系统是否未添加此用户`
          )
        }

        // 验证实施经理及其id
        if (!flow.engineImplementationManager) {
          errors.push("实施经理为空！")
        }
        if (!flow.implementUserId) {
          errors.push(
            `实施经理${flow.engineImplementationManager}的userId为空！请检查系统是否未添加此用户`
          )
        }

        // 验证监察经理及其id
        if (flow.supervisionUserName && !flow.supervisionUserId) {
          errors.push(
            `监理单位${flow.supervisionUserName}的userId为空！请检查系统是否未添加此用户`
          )
        }

        // 显示所有错误并返回是否验证通过
        if (errors.length > 0) {
          // 合并所有错误消息为一个字符串
          const combinedMessage = errors.join("<br/>") // 使用HTML换行符来分隔消息

          // 显示合并后的消息
          this.$message({
            message: `<div>${combinedMessage}</div>`, // 使用div来包裹消息，以便更好地控制样式
            type: "error",
            duration: 3000 * errors.length, // 根据错误数量延长显示时间，或者设置一个固定的时间
            showClose: true,
            center: true,
            dangerouslyUseHTMLString: true // 允许使用HTML字符串（注意安全性）
          })
          return false
        }
        return true
      },

      async gotoDel(row) {
        this.$confirm("确定要继续删除吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(async () => {
            let res = await delReportFlow(row.id)
            if (res.code === "0000") {
              this.$message.success("删除成功")
              this.$refs.table.getTableData() // 刷新表格数据
            }
          })
          .catch(() => {
            // 处理用户取消确认对话框的情况
            this.$message({
              type: "info",
              message: "已取消操作"
            })
          })
      },
      async gotoSendFlow(flow, type) {
        this.$refs.table.tableLoading = true
        // this.tableLoading = true;
        // 数据矫正
        if (type === 2) {
          flow.problemType = "数据核对"
        }
        // 先进行验证
        // if (!this.validateFlow(flow)) {
        //   return // 验证失败，提前返回
        // }
        this.isSubmitting = true // 设置为执行中
        try {
          flow.requestId = generateUUID()
          const flowCopy = JSON.parse(JSON.stringify(flow))
          let res = await toSendFlow(flowCopy)
          if (res.code === "0000") {
            this.$message.success(res.data)
            this.$refs.table.getTableData() // 刷新表格数据
          }
        } finally {
          // 延迟一秒后设置为false
          setTimeout(() => {
            this.$refs.table.tableLoading = false
          }, 1000) // 1000毫秒等于1秒
        }
      },
      search() {
        this.$refs.table.page.current = 1

        this.staticSearchParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )

        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      reset() {
        this.search()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .drawer-content {
    margin: 30px;
  }

  .task-list {
    white-space: pre-line; /* 保留换行符，并允许自动换行 */
  }
  .custom-message {
    word-wrap: break-word; /* 允许单词换行 */
    white-space: normal; /* 默认是 nowrap, 使用 normal 允许文本换行 */
    position: fixed; /* 使消息悬浮 */
    top: 20px; /* 可以根据需要调整位置 */
    left: 50%;
    transform: translateX(-50%); /* 居中对齐 */
    max-width: 80%; /* 限制消息的最大宽度 */
    z-index: 9999; /* 保证消息显示在最上层 */
    padding: 10px 20px; /* 设置内边距 */
    background-color: rgba(0, 0, 0, 0.7); /* 背景色 */
    color: #fff; /* 文本颜色 */
    border-radius: 4px; /* 圆角 */
    line-height: 2.5;
  }
</style>
