<template>
  <div>
    <!-- 待阅明细 -->
    <mssCard title="任务级竣工资料收集不及时明细">
      <div slot="headerBtn">
      <el-button @click="$router.go(-1)">返回</el-button>
      </div>
      <div slot="content">
        <fileDialog ref="selectDownload" :fileList="fileList"></fileDialog>
        <mssTableTaskLevel
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :static-search-param="staticSearchParam"
          border
          :pagination="false"
        >
        </mssTableTaskLevel>
      </div>
    </mssCard>
  </div>
</template>

<script>
  // import { getDetailsToRead } from "@/api/pccw/comprehensive/completion/index.js"
  // 新
  import {getList} from "@/api/pccw/comprehensive/completion";
  import fileDialog from "./fileDialog"
  export default {
    components: {
      fileDialog
    },
    data() {
      return {
        fileList: [],
        // 默认表格搜索条件
        staticSearchParam: {
          warningSignId: "", // 预警标识id
          warningSignIdKey: "",// 预警标识id明细key，拼接来
        },
        // 表格数据API
        tableApi: getList,
        tableHeader: [
          {
            prop: "taskCompnayName",
            label: "建设单位",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "taskCode",
            label: "任务编码",
            align: "center",
            tooltip: true
          },
          {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "taskTypeName",
            label: "任务专业类型",
            align: "center",
            tooltip: true
          },
          {
            prop: "taskArea",
            label: "任务所属区域",
            align: "center",
            tooltip: true
          },
          {
            prop: "engineImplementationManager",
            label: "工程实施经理(主)",
            align: "center",
            tooltip: true
          },
          {
            prop: "engineManageManager",
            label: "工程管理经理",
            align: "center",
            tooltip: true
          },
          {
            prop: "actualEndDate",
            label: "完工报告完成时间",
            align: "center",
            tooltip: true
          },
          {
            prop: "completionReportNo",
            label: "完工报告单号",
            align: "center",
            tooltip: true
          },
          {
            prop: "constructionDept",
            label: "施工单位",
            align: "center",
            tooltip: true
            // width: 200
          },
          {
            prop: "constructionUserName",
            label: "施工单位负责人",
            align: "center",
            tooltip: true
          },
          {
            prop: "supervisionOrgName",
            label: "监理单位",
            align: "center",
            tooltip: true
          },
          {
            prop: "supervisionUserName",
            label: "监理单位负责人",
            align: "center",
            tooltip: true
          },
          {

            label: "是否上传",
            align: "",
            fixed: "right",
            minWidth: "120px",
            formatter: row => {
              if (row.selectAsBuiltDrawingsFileList && row.selectAsBuiltDrawingsFileList.length > 0&&row.selectMaterialsFileList && row.selectMaterialsFileList.length > 0&&row.selectStatementFileList && row.selectStatementFileList.length > 0) {
                return (
                  "是"
                )
              } else {
                return "否"
              }
            }
          }
        ]
      }
    },
    created() {
        const query =this.$route.query
        console.log("预警",query)
        // this.staticSearchParam.warningSignId=query.warningSignId
        // this.staticSearchParam.warningSignIdKey=query.warningSignIdKey
        this.staticSearchParam = {
          projectCode: this.$route.query.projectCode,
          completionReportNo: this.$route.query.completionReportNo,
          constructionDept: this.$route.query.constructionDept,
          taskCompnayName: this.$route.query.taskCompnayName
        }
      // this.procInstId = this.flow.procInstId
      // this.getDetails()
    },
    methods: {
      async getDetails(row, type) {
        if (type === "settlement") {
          this.fileList = row.selectStatementFileList
        } else if (type === "material") {
          this.fileList = row.selectMaterialsFileList
        } else if (type === "built") {
          this.fileList = row.selectAsBuiltDrawingsFileList
        }
        // console.log("this.fileList",this.fileList);
        this.$refs.selectDownload.downDialog = true
      }
    }
  }
</script>

<style lang="scss" scoped></style>
