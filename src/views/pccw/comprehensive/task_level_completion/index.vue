/** * @author: noah * @date: 2023-11-24 * @description: 任务级竣工资料收集 */
<template>
  <div>
    <!--查询框 -->
      <mssSearchForm
        ref="searchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
      ></mssSearchForm>
    <!--结果展示框-->
    <mssCard title="任务级竣工资料收集报表">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <div class="StagingPointWorkOrder">
          <el-tabs
            v-model="activeName"
            type="border-card"
            @tab-click="handleClick"
          >
            <el-tab-pane label="按项目" name="byProject">
              <byProject
                ref="project"
                :staticSearchParam2="staticSearchParam"
              ></byProject>
            </el-tab-pane>
            <el-tab-pane label="按完工报告" name="byCompletionReport" lazy>
              <byCompletionReport
                ref="report"
                :staticSearchParam2="staticSearchParam"
              ></byCompletionReport>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </mssCard>
  </div>
</template>

<script>
// 获取地市:queryAreaListService
import {queryAreaListService} from "@/api/common_api.js"
import {downloadCompletionExcel, getLastSummaryTime} from "@/api/pccw/comprehensive/completion/index.js"
import byProject from "@/views/pccw/comprehensive/task_level_completion/by_project.vue"
import byCompletionReport from "@/views/pccw/comprehensive/task_level_completion/by_completion_report.vue"

export default {
  name: "TaskLevelCompletion",
  components: {
    byProject,
    byCompletionReport
  },
  data() {
    return {
      // 搜索静态条件
      staticSearchParam: {
        lastSummaryTime: ""
      },
      //搜索字段配置
      searchConfig: [],
      searchConfigCommon: [
        // {
        //   label: "最后汇总时间",
        //   fieldName: "lastSummaryTime",
        //   type: "date1",
        //   format: 'yyyy-MM-dd HH:mm:ss',
        //   valueFormat: 'yyyy-MM-dd HH:mm:ss',
        //   disabled: true
        // },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode"
        },
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName"
        },
        {
          label: "建设单位",
          type: "input",
          fieldName: "taskCompnayName"
        }
      ],
      activeName: "byProject", // byCompletionReport
      downloadCompletionExcel: {
        downloadCompletionType: null
      },
      lastSummaryTime: '',
    }
  },
  async created() {
    await this.getCompletionReport()
    this.searchConfig = JSON.parse(JSON.stringify(this.searchConfigCommon))
  },
  watch: {
    "$refs.searchForm.searchForm": {
      handler(newVal, oldVal) {
        // 在这里对 searchForm 的变化做出相应的处理
        console.log("searchForm 发生变化", newVal)
      },
      deep: true // 如果 searchForm 是一个对象或数组，需要设置 deep 为 true
    },
    activeName(newValue, oldValue) {
      // 在 activeName 的值发生变化时执行的操作
      console.log("activeName 值发生了变化，新值为：", newValue)
      // 按完工报告
      if (newValue === "byCompletionReport") {
        const newConfig1 = {
          label: "施工单位",
          type: "input",
          fieldName: "constructionDept"
        }
        const newConfig = {
          label: "完工报告单号",
          type: "input",
          fieldName: "completionReportNo"
        }
        const newConfig2 = {
          label: "完工报告日期",
          fieldName: 'actualEndDateStart',
          fieldName2: 'actualEndDateEnd',
          type: "date2",
          format: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd"
        }
        this.searchConfig = JSON.parse(
          JSON.stringify(this.searchConfigCommon)
        )
        this.searchConfig.push(newConfig1)
        this.searchConfig.push(newConfig)
        this.searchConfig.push(newConfig2)
        this.$refs.report.$refs.table.getTableData(
          this.$refs.searchForm.searchForm
        )
      } else {
        this.searchConfig = JSON.parse(
          JSON.stringify(this.searchConfigCommon)
        )
        this.$refs.project.$refs.table.getTableData(
          this.$refs.searchForm.searchForm
        )
      }
    }
  },
  methods: {
    handleQueryParameters() {
      // 获取当前日期
      let today = new Date()

      // 获取年、月、日
      let year = today.getFullYear()
      let month = String(today.getMonth() + 1).padStart(2, "0") // 月份从0开始，所以+1
      let day = String(today.getDate()).padStart(2, "0")

      // 格式化为 YYYY-MM-DD
      let formattedToday = `${year}-${month}-${day}`

      this.staticSearchParam.actualEndDate = formattedToday
      console.log("格式化啊啊啊啊啊啊", formattedToday)
    },
    async exportHandle() {
      // 先添加其他条件
      this.downloadCompletionExcel = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      // 下载类型
      if (this.activeName === "byProject") {
        this.downloadCompletionExcel.downloadCompletionType = "项目"
      } else if (this.activeName === "byCompletionReport") {
        this.downloadCompletionExcel.downloadCompletionType = "完工报告"
      }
      let res = await downloadCompletionExcel(this.downloadCompletionExcel)
      console.log("res", res)
      const url = window.URL.createObjectURL(new Blob([res.data]))
      const link = document.createElement("a")
      link.style.display = "none"
      link.href = url
      link.setAttribute("download", "任务级竣工资料收集报表.xlsx")
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    },
    handleClick() {
    },
    // 表单重置
    reset() {
      this.$refs.searchForm.searchForm.lastSummaryTime = this.lastSummaryTime
      this.search()
    },
    /** 搜索按钮操作 */
    search() {
      console.log("搜索按钮")
      // this.$refs.table.page.current = 1
      // this.$refs.table.getTableData(form)
      this.staticSearchParam.lastSummaryTime = this.lastSummaryTime
  this.staticSearchParam=JSON.parse(
    JSON.stringify(this.$refs.searchForm.searchForm)
  )

      if (this.activeName === "byProject") {
        // console.log("this.$refs.project.staticSearchParam2",this.$refs.project.staticSearchParam2)
        // this.$refs.project.staticSearchParam=JSON.parse(
        //   JSON.stringify(this.$refs.searchForm.searchForm)
        // )

        this.$refs.project.$refs.table.getTableData(
          this.$refs.searchForm.searchForm
        )
      } else if (this.activeName === "byCompletionReport") {
        this.$refs.report.$refs.table.getTableData(
          this.$refs.searchForm.searchForm
        )
      }
    },
    //获取最后汇总时间
    async getCompletionReport() {
      let res = await getLastSummaryTime()
      if (res.code === "0000") {
        console.log("获取最后汇总时间", res.data)
        this.staticSearchParam.lastSummaryTime = res.data
        this.lastSummaryTime = res.data
      } else {
        this.$message.error(`${res.msg || '获取最后汇总时间'}`)
      }
    }
  }
}
</script>
