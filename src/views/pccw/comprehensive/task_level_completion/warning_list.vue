<template>
  <div>
    <mssCard title="消息详情">
      <div slot="content">
        <p class="message-title" v-if="dateTitle">
          截止{{
            dateTitle
          }}晨，您所管理的施工单位完工任务级竣工资料收集不及时，请您根据业务进展情况进行相应的督促。
        </p>
      </div>
    </mssCard>
    <mssCard title="竣工资料收集不及时列表">
      <div slot="headerBtn">
        <!-- <el-button type="primary" @click="exportHandle">导出</el-button> -->
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :static-search-param="staticSearchParam"
          border
          selection
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import { findByMsgIdService } from "@/api/message_manager/message_manager_api"

  import {
    getByReport,
    getDatils,
    downloadCompletionExcel
  } from "@/api/pccw/comprehensive/completion/index.js"
  import { param } from "@/utils"
  export default {
    data() {
      return {
        id: "",
        messageType: "",
        messageTitle: "",
        dateTitle: "",
        pageLoading: false,
        staticSearchParam: {
          warningSigns: "warning",
          warningSignId: null,
          warningSignIdKey: null
        },
        query: {
          projectCode: "",
          completionReportNo: "",
          constructionDept: ""
        },
        // 表格数据API
        tableApi: getByReport,
        tableHeader: [
          {
            prop: "taskCompnayName",
            label: "建设单位",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true
          },
          {
            prop: "completionReportNo",
            label: "完工报告单号",
            align: "center",
            tooltip: true
          },
          {
            prop: "actualEndDate",
            label: "完工报告完成时间",
            align: "center",
            tooltip: true
          },
          {
            prop: "constructionDept",
            label: "施工单位",
            align: "center",
            tooltip: true
          },
          {
            prop: "engineImplementationManager",
            label: "工程实施经理(主)",
            align: "center",
            tooltip: true
          },
          {
            prop: "totalCompletedTasks",
            label: "完工任务数",
            align: "center",
            tooltip: true
          },
          {
            prop: "uploadedTaskNum",
            label: "已上传任务数",
            align: "center",
            tooltip: true
            // width: 200
          },
          {
            prop: "uploadedRate",
            label: "上传率",
            align: "center",
            tooltip: true
            // width: 200
          },
          {
            prop: "uploadFailedOneMoonRate",
            label: "上传不及时率(超1个月)",
            align: "center",
            tooltip: true
          },
          {
            prop: "uploadFailedTwoMoonRate",
            label: "上传严重不及时率(超2个月)",
            align: "center",
            tooltip: true
          },
          {
            prop: "approvedTaskNum",
            label: "审核通过任务数",
            align: "center",
            tooltip: true
          },
          {
            prop: "approvalRate",
            label: "审核通过率",
            align: "center",
            tooltip: true
          },
          {
            label: "操作",
            align: "center",
            fixed: "right",
            minWidth: "100px",
            formatter: row => {
              return (
                <span>
                  {" "}
                  {
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.getDetails(row)
                      }}
                    >
                      查看{" "}
                    </span>
                  }{" "}
                </span>
              )
            }
          }
        ]
      }
    },
    created() {
      // 浏览器传参
      const urlQuery = this.$route.query
      console.log("this.$route.query", urlQuery)
      this.staticSearchParam.warningSignId = urlQuery.warningSignId
      this.staticSearchParam.warningSignIdKey = urlQuery.warningSignIdKey

      this.id = urlQuery.messageId
      this.messageType = urlQuery.type
      this.getData()
    },
    methods: {
      // 获取日期
      getData(){
        this.pageLoading = true
        findByMsgIdService(this.id)
        .then(res => {
          this.messageTitle = res?.data?.title || ''
          // 匹配
          const str = this.messageTitle;
          const regex = /\((.*?)\)/; // 匹配括号内的内容
          const match = str.match(regex);
          if (match) {
            this.dateTitle= match[1]; // 获取匹配到的括号内的内容
          }
        })
        .finally(_ => {
          this.pageLoading = false
        })
      },
      async exportHandle() {
        let comExcel = {
          downloadCompletionType: "完工报告"
        }
        let res = await downloadCompletionExcel(comExcel)
        console.log("res", res)
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute("download", "任务级竣工资料收集报表.xlsx")
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      },
      getDetails(row) {
        this.query = {
          projectCode: row.projectCode,
          completionReportNo: row.completionReportNo,
          constructionDept: row.constructionDept
        }
        console.log("row", row)
        let key = row.completionReportNo + '-' + row.projectCode + '-' + row.taskCompnayName + '-' + row.constructionDept + '-' + row.engineImplementationManagerId;

console.log("key", key)
        this.$router.push({
          path: "/pccw_menu/comprehensive/task_level_completion/details_warning",
          query:row
          // query: {
          //  warningSignId: this.staticSearchParam.warningSignId ,// 预警标识id
          //  warningSignIdKey:key,// 预警标识id明细key，拼接来
          // }
        })
      }
    }
  }
</script>

<style lang="scss" scoped></style>
