<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="任务竣工任务列表-已完工">
      <div slot="headerBtn">
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
          :serial = "false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <!-- 修改 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="150px">
        <el-form-item label="流程id" prop="name">
          <el-input v-model="form.businessId" placeholder="请输入" />
        </el-form-item>
         <el-form-item label="施工专业负责人-姓名" prop="name">
          <el-input v-model="form.constructionUserName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="施工单位" prop="name">
          <el-input v-model="form.constructionDept" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {commonOneDel, commonMultDel, commonDown} from '@/utils/btn'
import {selectComReportFlowInfoList,selectComReportFlowInfoList2,getList,editTask,getTaskById,tablelist} from "@/api/pccw/comprehensive/completion";


export default {
  name: 'ReportFlowListLog',
  components: {},
  data() {
    return {
      // 表单参数
      form: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      tableApi:tablelist,
      //搜索字段配置
      searchConfig: [
        {
          type: 'input',
          fieldName: 'completionReportNo',
          label: '完工报告编号'
        },
        {
          type: 'input',
          fieldName: 'projectCode',
          label: '项目编号'
        },
        {
          type: 'input',
          fieldName: 'taskCode',
          label: '任务编号'
        },
        {
          type: 'input',
          fieldName: 'constructionDept',
          label: '施工单位'
        },
        {
          type: 'input',
          fieldName: 'businessId',
          label: '流程id'
        },
        {
          type: 'input',
          fieldName: 'title',
          label: '流程主题'
        },
        {
          type: 'input',
          fieldName: 'taskCompnayName',
          label: '建设单位名称'
        },
        {
          type: 'input',
          fieldName: 'constructionUserName',
          label: '施工单位负责人/姓名'
        },
        {
          type: 'input',
          fieldName: 'constructionUserId',
          label: '施工单位负责人/id'
        },
        {
          type: 'input',
          fieldName: 'supervisionUserName',
          label: '监察经理/姓名'
        },
        {
          type: 'input',
          fieldName: 'supervisionUserId',
          label: '监察经理/id'
        },
        {
          type: 'input',
          fieldName: 'engineImplementationManager',
          label: '实施经理/姓名'
        },
        {
          type: 'input',
          fieldName: 'implementUserId',
          label: '实施经理/id'
        },
      ],
      //表格头部配置
      tableHeader: [
        {
          prop: "businessId",
          label: "流程id",
          align: "center",
          tooltip: true,
          minWidth: 240,
        },
        {
          prop: "title",
          label: "流程主题",
          align: "center",
          tooltip: true,
          minWidth: 180,
        },
        {
          prop: "completionReportNo",
          label: "完工报告编号",
          align: "center",
          tooltip: true,
          minWidth: 160,
        },
        {
          prop: "projectCode",
          label: "项目编号",
          align: "center",
          tooltip: true,
          minWidth: 160,
        },
        {
          prop: "taskCode",
          label: "任务编号",
          align: "center",
          tooltip: true,
          minWidth: 160,
        },
        {
          prop: "taskCompnayName",
          label: "建设单位名称",
          align: "center",
          tooltip: true,
          minWidth: 160,
        },
        {
          prop: "constructionDept",
          label: "施工单位",
          align: "center",
          tooltip: true,
          minWidth: 230,
        },
        {
          prop: "constructionUserName",
          label: "施工单位负责人-姓名",
          align: "center",
          minWidth: 160,
          tooltip: true,
        },
         {
           prop: "constructionUserId",
           label: "施工单位负责人-id",
           align: "center",
           minWidth: 160,
           tooltip: true
         },
          {
            prop: "supervisionUserName",
            label: "监察经理-姓名",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "supervisionUserId",
            label: "监察经理-id",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "engineImplementationManager",
            label: "实施经理-姓名",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "implementUserId",
            label: "实施经理-id",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "engineManageManager",
            label: "工程管理经理",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "fileNum",
            label: "文件数量",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "flowTime",
            label: "流程发起时间",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "createTime",
            label: "创建时间",
            align: "center",
            tooltip: true,
            minWidth: 160,
          },
          {
            prop: "updateTime",
            label: "更新时间",
            align: "center",
            tooltip: true,
            minWidth: 160,
          },
          {
            label: "编辑",
            align: "center",
            fixed: "right",
            minWidth: "80px",
            formatter: row => {
              return (
                <span>
                  {
                    <span
                      class="table_btn mr10"
                      onClick={() => {
                        this.handleUpdate(row)
                      }}
                    >
                      编辑
                    </span>
                  }
                </span>
              )
            }
          },
      ],
      staticSearchParam:{
        type:"list"
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    /** 修改按钮操作 */
    async handleUpdate(row) {
      console.log(row)
      // this.reset()
      // const id = row.id
      let res = await getTaskById(row.id)

      this.form = res.data
      this.open = true
      this.title = "修改"
    },
    /** 提交按钮 */
    submitForm() {
      if (this.form.id != null) {
        editTask(this.form).then(res => {
          if (res.code === "0000") {
            this.$message.success("修改成功")
            this.$refs.table.getTableData()
          }
          this.open = false
        })
      } else {
        // addVariable(this.form).then(response => {
        //   this.$message.success("新增成功")
        //   this.open = false
        //   this.$refs.table.getTableData()
        // })
      }
    },
    search() {
  // 保存原始查询条件
  this.staticSearchParam = JSON.parse(
    JSON.stringify(this.$refs.searchForm.searchForm)
  );

  // 合并 searchForm 和 staticSearchParam
  let loadParam = {
    ...this.staticSearchParam,
    ...this.$refs.searchForm.searchForm
  };
loadParam.type='list';
  // 获取表格数据
  this.$refs.table.getTableData(loadParam);
    },

    reset() {
      this.search();
    },
  }
}
</script>
