<template>
  <el-dialog title="选择下载" :visible.sync="downDialog" class="dialog" :modal="true">
    <mssTable ref="table" :columns="tableHeader" border :stationary="stationary" :pagination="false">
    </mssTable>
    </div>
  </el-dialog>
</template>

<script>
  import {
    downloadFile
  } from "@/api/pccw/comprehensive/completion/index.js";

  export default {
    watch: {
      fileList(newVal) {
        this.stationary = newVal;
      }
    },
    props: ["type", "fileList"],
    data() {
      return {
        // 表格静态数据
        stationary: [],
        downDialog: false,
        // 表头
        tableHeader: [{
            prop: "fileName",
            label: "文件名称",
            align: "left",
            tooltip: true,
            width: 500
          },
          {
            label: '操作',
            align: 'center',
            // fixed: 'right',
            minWidth: '80px',
            formatter: (row) => {
              return ( <
                span >
                <
                span class = "table_btn mr10"
                onClick = {
                  () => {
                    this.selectRow(row);
                  }
                } >
                选择 <
                /span> <
                /span>
              );
            }
          }
        ],
      };
    },
    created() {
      this.stationary = this.fileList
    },
    methods: {
      // 选择当行
      async selectRow(row) {
        let res = await downloadFile(row.kbId)
        console.log("res", res)
        console.log(row);
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', row.fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        // this.selectUserDialog=false
        // this.$emit('row-selected', row);// 传值父组件
      },
    }
  };
</script>


<style lang="scss" scoped>
  .dialog {
    width: 110%;
  }
</style>
