<template>
  <div>
    <mssTable
      ref="table"
      :api="tableApi"
      :columns="tableHeader"
      :static-search-param="staticSearchParam2"
      border
    >
    </mssTable>
  </div>
</template>

<script>
  import {
  getByReport,getDatils
    } from "@/api/pccw/comprehensive/completion/index.js";
import { param } from "@/utils";
  export default {
    props: {
      staticSearchParam2: {
        type: Object,
        default: () => ({})
      }
    },
    data(){
      return{
        query:{
          projectCode:'',
          completionReportNo:'',
          constructionDept:''
        },
        // 表格数据API
        tableApi: getByReport,
        tableHeader: [
          {
            prop: "completionReportNo",
            label: "完工报告单号",
            align: "center",
            tooltip: true,
          },
          {
            prop: "actualEndDate",
            label: "完工报告完成时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "taskCompnayName",
            label: "建设单位",
            align: "center",
            tooltip: true,
          },
          {
            prop: "constructionDept",
            label: "施工单位",
            align: "center",
            tooltip: true,
          },
          // {
          //   prop: "engineImplementationManager",
          //   label: "工程实施经理(主)",
          //   align: "center",
          //   tooltip: true,
          // },
          // {
          //   prop: "engineManageManager",
          //   label: "工程管理经理",
          //   align: "center",
          //   tooltip: true,
          // },
          {
            prop: "totalCompletedTasks",
            label: "完工任务数",
            align: "center",
            tooltip: true,
          },
          {
            prop: "uploadedTaskNum",
            label: "已上传任务数",
            align: "center",
            tooltip: true,
            // width: 200
          },
          {
            prop: "uploadedRate",
            label: "上传率",
            align: "center",
            tooltip: true,
            // width: 200
          },
          {
            prop: "uploadFailedOneMoonRate",
            label: "上传不及时率(超1个月)",
            align: "center",
            tooltip: true,
          },
          {
            prop: "uploadFailedTwoMoonRate",
            label: "上传严重不及时率(超2个月)",
            align: "center",
            tooltip: true,
          },
          {
            prop: "approvedTaskNum",
            label: "审核通过任务数",
            align: "center",
            tooltip: true,
          },
          {
            prop: "approvalRate",
            label: "审核通过率",
            align: "center",
            tooltip: true,
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '100px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.getDetails(row)
                    }
                  } >
                  查看 <
                  /span>
                } <
                /span>
              )
            }
          }
        ],
      }
    },
  methods: {
getDetails(row) {
  this.query = {
    projectCode: row.projectCode,
    completionReportNo: row.completionReportNo,
    constructionDept: row.constructionDept
  };
  console.log("row", row);
  this.$router.push({
    path: "/pccw_menu/comprehensive/task_level_completion/details",
    query: {
          projectCode: row.projectCode,
          completionReportNo: row.completionReportNo,
          constructionDept: row.constructionDept,
          taskCompnayName: row.taskCompnayName
    }
  });
}

  }
  }
</script>

<style lang="scss" scoped>
</style>
