<template>
  <div>
    <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam2" border>
    </mssTable>
  </div>

</template>

<script>
  import {
    auto
  } from "html-webpack-plugin/lib/chunksorter";
  import {
    getByProject,
    getDatils
  } from "@/api/pccw/comprehensive/completion/index.js";
  export default {
    props: {
      staticSearchParam2: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        // 表格数据API
        tableApi: getByProject,
        tableHeader: [
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "taskCompnayName",
            label: "建设单位",
            align: "center",
            tooltip: true,
          },
          {
            prop: "totalCompletedTasks",
            label: "完工任务数",
            align: "center",
            tooltip: true,
            // width: 200
          },
          {
            prop: "uploadedTaskNum",
            label: "已上传任务数",
            align: "center",
            tooltip: true,
            // width: 200
          },
          {
            prop: "uploadedRate",
            label: "上传率",
            align: "center",
            tooltip: true,
            // width: 200
          },
          {
            prop: "approvedTaskNum",
            label: "审核通过任务数",
            align: "center",
            tooltip: true,
            // width: 200
          },
          {
            prop: "approvalRate",
            label: "审核通过率",
            align: "center",
            tooltip: true,
            // width: 200
          },
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            minWidth: '100px',
            formatter: (row) => {
              return ( <
                span > {
                  <
                  span class = 'table_btn mr10'
                  onClick = {
                    () => {
                      this.getDetails(row)
                    }
                  } >
                  查看 <
                  /span>
                } <
                /span>
              )
            }
          }
        ],
      }
    },
    methods: {
      getDetails(row) {
        let query = {
          projectCode: row.projectCode,
          // completionReportNo: "",
          completionReportNo: row.completionReportNo,
          constructionDept: row.constructionDept
        };
        console.log("row", row);
        this.$router.push({
          path: "/pccw_menu/comprehensive/task_level_completion/details",
          query: {
          projectCode: row.projectCode,
          completionReportNo: row.completionReportNo,
            taskCompnayName: row.taskCompnayName
          }
        });
      }

    }
  }
</script>

<style lang="scss" scoped>
</style>
