<template>
  <div>
    <!-- 列表明细 -->
    <mssCard title="报表-查看任务级竣工资料收集明细">
       <div slot="headerBtn">
       <el-button @click="$router.go(-1)">返回</el-button>
       </div>
      <div slot="content">
        <fileDialog ref="selectDownload" :fileList="fileList"></fileDialog>
       <mssTable ref="table" :api="tableApi" :columns="tableHeader" :static-search-param="staticSearchParam"
          border >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import {
    getDatils
  } from "@/api/pccw/comprehensive/completion/index.js";
  // 新
  import {selectComReportFlowInfoList,selectComReportFlowInfoList2,getList} from "@/api/pccw/comprehensive/completion";

  import fileDialog from "./fileDialog"
  export default {
    components: {
      fileDialog,
    },
    data() {
      return {
        fileList:[],
        // 默认表格搜索条件
        staticSearchParam: {
          // projectCode: "",
          // completionReportNo: "",
          // constructionDept: ""
        },
        // 表格数据API
        tableApi: getList,
        tableHeader: [{
            prop: "taskCompnayName",
            label: "建设单位",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectCode",
            label: "项目编码",
            align: "center",
            tooltip: true,
          },
          {
            prop: "projectName",
            label: "项目名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "taskCode",
            label: "任务编码",
            align: "center",
            tooltip: true,
          },
          {
            prop: "taskName",
            label: "任务名称",
            align: "center",
            tooltip: true,
          },
          {
            prop: "taskTypeName",
            label: "任务专业类型",
            align: "center",
            tooltip: true,
          },
          {
            label: "任务所属区域",
            prop: "taskArea",
            minWidth: 200,
  formatter: row => {
    return `${row.province}-${row.city}-${row.county}`;
  }
          },
          {
            prop: "engineImplementationManager",
            label: "工程实施经理(主)",
            align: "center",
            tooltip: true,
          },
          {
            prop: "engineManageManager",
            label: "工程管理经理",
            align: "center",
            tooltip: true,
          },
          {
            prop: "actualEndDate",
            label: "完工报告完成时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "completionReportNo",
            label: "完工报告单号",
            align: "center",
            tooltip: true,
          },
          {
            prop: "constructionDept",
            label: "施工单位",
            align: "center",
            tooltip: true,
            // width: 200
          },
          {
            prop: "constructionUserName",
            label: "施工单位负责人",
            align: "center",
            tooltip: true,
          },
          {
            prop: "supervisionOrgName",
            label: "监理单位",
            align: "center",
            tooltip: true,
          },
          {
            prop: "supervisionUserName",
            label: "监理单位负责人",
            align: "center",
            tooltip: true,
          },
          {
            label: '是否上传',
            align: '',
            fixed: 'right',
            minWidth: '120px',
           formatter:this.formatUploadStatus
          },
          {
            prop: "uploadedDate",
            label: "上传时间",
            align: "center",
            tooltip: true,
          },
          {
            prop: "isApproved", // 原来的isPassedApprove
            label: "是否审批通过",
            align: "center",
            tooltip: true,
            formatter: this.formatter  // 将 formatter 方法应用到该列上
          },
          {
            // prop: "attribute2",// 按钮通过时间
            // isPassedApproveTime 原来的
            prop: "isPassedApproveTime",
            label: "审批通过时间",
            align: "center",
            tooltip: true,
            formatter:this.formatterIsPassedApproveTime
          },
        ],
      }
    },
    created() {
      let type = this.$route.query.type; // 获取类型
      if (type === 'warning') {
        console.log("预警")
        this.staticSearchParam = {
          messageId: this.$route.query.messageId,
          messageType: this.$route.query.messageType
        }
      } else {
        this.staticSearchParam = {
          projectCode: this.$route.query.projectCode,
          completionReportNo: this.$route.query.completionReportNo,
          constructionDept: this.$route.query.constructionDept,
          taskCompnayName: this.$route.query.taskCompnayName
        }
      }
      // this.procInstId = this.flow.procInstId
      // this.getDetails()
    },
    methods: {
          formatUploadStatus(row) {
              // 初始化按钮数组
              const buttons = [];

              // 添加竣工图纸下载按钮
              if (row.selectAsBuiltDrawingsFileList && row.selectAsBuiltDrawingsFileList.length > 0&&row.selectAsBuiltDrawingsFileList[0]) {
                buttons.push(
                  <span key="built" class="table_btn mr10" onClick={() => this.getDetails(row, 'built')}>
                    竣工图纸下载
                  </span>
                );
              }
              // 添加平衡表下载按钮
              if (row.selectMaterialsFileList && row.selectMaterialsFileList.length > 0&&row.selectMaterialsFileList[0]) {
                buttons.push(
                  <span key="material" class="table_btn mr10" onClick={() => this.getDetails(row, 'material')}>
                    平衡表下载
                  </span>
                );
              }
              // 添加结算表下载按钮
              if (row.selectStatementFileList && row.selectStatementFileList.length > 0&&row.selectStatementFileList[0]) {
                buttons.push(
                  <span key="settlement" class="table_btn mr10" onClick={() => this.getDetails(row, 'settlement')}>
                    结算表下载
                  </span>
                );
              }
              // 如果没有任何按钮，则显示"否"
              if (buttons.length === 0) {
                return <span class='mr10'>否</span>;
              }

              // 如果有按钮，则渲染它们
              return (
                <span style={{ display: 'flex', flexDirection: 'column' }}>
                  {buttons}
                </span>
              );
      },
      formatterIsPassedApproveTime(row, column, cellValue) {
        console.log("cellValue",cellValue)
        // 检查是否以 "1970-01-01" 开头的字符串
        // 审批通过时间
        if (!cellValue||cellValue.startsWith('1970-01-01')) {
          return '无';
        } else {
          return cellValue; // 其他情况下直接显示原本的值
        }
      },
      formatter(row, column, cellValue) {
        if (cellValue === '是') {
          return '是';
        } else {
          return '否';
        }
      },
      async getDetails(row, type) {
        if (type === 'settlement') {
          this.fileList = row.selectStatementFileList
        } else if (type === 'material') {
          this.fileList = row.selectMaterialsFileList
        } else if (type === 'built') {
          this.fileList = row.selectAsBuiltDrawingsFileList
        }
        // console.log("this.fileList",this.fileList);
        this.$refs.selectDownload.downDialog = true;
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
