/** * @author: ye<PERSON><PERSON> * @date: 2024-06-20 * @description:
查询完工任务竣工流程启动记录 */
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>
    <mssCard title="任务竣工待办流程启动记录3">
      <div slot="headerBtn"> </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
          :serial="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import { commonOneDel, commonMultDel, commonDown } from "@/utils/btn"
  import { selectComReportFlowInfoList,selectComReportFlowInfoList2,toSendFlow } from "@/api/pccw/comprehensive/completion"

  export default {
    name: "ReportFlowListLog",
    components: {},
    data() {
      return {
        tableApi: selectComReportFlowInfoList2,
        //搜索字段配置
        searchConfig: [
          {
            type: "input",
            fieldName: "completionReportNo",
            label: "完工报告编号"
          },
          {
            type: "input",
            fieldName: "projectCode",
            label: "项目编号"
          },
          {
            type: "input",
            fieldName: "companyName",
            label: "施工单位"
          },
          // {
          //   type: 'input',
          //   fieldName: 'taskCodeList',
          //   label: '失败任务编码集合'
          // },
          {
            type: "input",
            fieldName: "businessId",
            label: "流程id"
          },
          {
            type: "input",
            fieldName: "title",
            label: "流程主题"
          },
          {
            type: "select",
            label: "流程是否发起成功",
            fieldName: "isFlowSuccess",
            options: [
              { label: "失败", value: "fail" },
              { label: "成功", value: "success" }
            ]
          },
          {
            type: "input",
            fieldName: "errMessage",
            label: "失败原因"
          },
          {
            type: "input",
            fieldName: "createName",
            label: "施工单位负责人-姓名"
          },
        ],
        //表格头部配置
        tableHeader: [
          {
            prop: "businessId",
            label: "流程id",
            align: "center",
            tooltip: true,
            minWidth: 240
          },
          {
            prop: "title",
            label: "流程主题",
            align: "center",
            tooltip: true,
            minWidth: 180
          },
          {
            prop: "completionReportNo",
            label: "完工报告编号",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "projectCode",
            label: "项目编号",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "companyName",
            label: "施工单位",
            align: "center",
            tooltip: true,
            minWidth: 230
          },
          {
            prop: "createByAccount",
            label: "施工单位负责人-系统账号",
            align: "center",
            minWidth: 160,
            tooltip: true
          },
          {
            prop: "createName",
            label: "施工单位负责人-姓名",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "isFlowSuccess",
            label: "流程是否发起成功",
            fixed: "right",
            align: "center",
            tooltip: true,
            formatter: row => {
              return (
                <span>
                  {row.isFlowSuccess == "success" ? (
                    <el-tag type="success">成功</el-tag>
                  ) : (
                    <el-tag type="danger">失败</el-tag>
                  )}
                </span>
              )
            }
          },
          {
            prop: "errMessage",
            label: "失败原因",
            align: "center",
            tooltip: true,
            minWidth: 200
          },
          {
            prop: "taskCodeList",
            label: "失败任务编码集合",
            align: "center",
            tooltip: true,
            minWidth: 280
          },
          {
            prop: "createTime",
            label: "创建时间",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: "updateTime",
            label: "更新时间",
            align: "center",
            tooltip: true,
            minWidth: 160
          },
                    {
                      prop: "status",
                      label: "流程状态",
                      align: "center",
                      tooltip: true
                    },
        ],
        loadParam: {},
        staticSearchParam: {}
      }
    },
    created() {},
    mounted() {},
    methods: {
      validateFlow(flow) {
     const errors = [];

      // 验证任务编码
      if (!flow.taskCodeList) {
          errors.push("任务编码为空！");
      }

      // 假设flow对象现在直接有一个completionReportNo属性
      if (!flow.completionReportNo) {
          errors.push("完工单号为空！");
      }

      // 验证项目名称
      if (!flow.projectName) {
          errors.push("项目名称为空！");
      }

      // 验证施工单位及其负责人信息
      if (!flow.companyName) {
          errors.push("施工单位为空！");
      }
      if (!flow.createName) {
          errors.push("施工单位负责人姓名为空！");
      }
      if (!flow.createBy) {
          errors.push(`施工单位负责人${flow.createName}的userId为空！请检查系统是否未添加此用户`);
      }

      // 验证实施经理及其id
      if (!flow.engineImplementationManager) {
          errors.push("实施经理为空！");
      }
      if (!flow.implementUserId) {
          errors.push(`实施经理${flow.engineImplementationManager}的userId为空！请检查系统是否未添加此用户`);
      }

      // 验证监察经理及其id
      if (flow.supervisionUserName && !flow.supervisionUserId) {
          errors.push(`监理单位${flow.supervisionUserName}的userId为空！请检查系统是否未添加此用户`);
      }

      // 显示所有错误并返回是否验证通过
      if (errors.length > 0) {
    // 合并所有错误消息为一个字符串
    const combinedMessage = errors.join('<br/>'); // 使用HTML换行符来分隔消息

    // 显示合并后的消息
    this.$message({
      message: `<div>${combinedMessage}</div>`, // 使用div来包裹消息，以便更好地控制样式
      type: 'error',
      duration: 3000 * errors.length, // 根据错误数量延长显示时间，或者设置一个固定的时间
      showClose: true,
      center: true,
      dangerouslyUseHTMLString: true // 允许使用HTML字符串（注意安全性）
    });
          return false;
      }
      return true;
      },

      async gotoSendFlow(flow,type){
        // 数据矫正
        if(type===2){
          flow.problemType='数据核对'
        }
        // 先进行验证
      if (!this.validateFlow(flow)) {
    return; // 验证失败，提前返回
      }
        let res = await toSendFlow(flow)
        console.log(res.code ==='0000')
        if(res.code ==='0000'){
          this.$message.success(res.data)
          this.$refs.table.getTableData()// 刷新表格数据
        }
      },

      search() {
        this.$refs.table.page.current = 1

        this.staticSearchParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )

        this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
        )
        this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
      },
      reset() {
        this.search()
      }
    }
  }
</script>
