/**
* @author: ty
* @date: 2023-07-22
* @description: 消息详情展示页面
*/
<template>
  <div class="MessageDetails" v-loading="pageLoading">
    <div style="margin-top: 30px;"></div>
    <mssCard title="消息详情">
      <div slot="content">
        消息
        <p class="message-title">{{messageTitle}}</p>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {findByMsgIdService} from "@/api/message_manager/message_manager_api";
export default {
  name: 'MessageDetails',
  data(){
    return {
      id: '',
      messageType: '',
      messageTitle: '',
      pageLoading: false
    }
  },
  created(){
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.messageId
    this.messageType = urlQuery.type
    this.getData()

  },
  methods: {

    getData(){
      this.pageLoading = true
      findByMsgIdService(this.id)
      .then(res => {
        this.messageTitle = res?.data?.title || ''
      })
      .finally(_ => {
        this.pageLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .MessageDetails{
    .message-title{

    }
  }
</style>
