<template>
  <div class="custom-dialog">
    <el-dialog title="选择" :visible.sync="approvalDialog" class="dialog" :modal-append-to-body="false">
      <el-form :label-position="labelPosition" label-width="100px"        :model="form">
        <el-form-item label="原施工专业负责人" prop="name">
          <el-input v-model="form.createName" placeholder="姓名" />
        </el-form-item>
        <el-form-item label="施工单位" prop="name">
          <el-input v-model="form.companyName" placeholder="施工单位" />
        </el-form-item>
        <el-form-item label="更换的用户" class="sendMan">
          <el-button @click="toSelectUser">选择</el-button>
          <div v-if="selectUser">
            用户：{{selectUser.nickName}}
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="consent()">确定</el-button>
        <el-button @click="closed()">取消</el-button>
      </span>
      <select-user ref="selectUserDialog" @row-selected="handleRowSelected" />
    </el-dialog>
  </div>
</template>

<script>
  import {updateUser} from "@/api/pccw/comprehensive/completion";

  import SelectUser from './SelectUser.vue';
  export default {
    props: {

    },
    components: {
      SelectUser
    },
    data() {
      return {
        form:{

        },
        selectUser: {}, // 选择的用户信息
        approvalDialog: false,
        labelPosition: 'top',

      };
    },
    created() {

    },

    mounted() {

    },
    methods: {
      toSelectUser() {
        this.$refs.selectUserDialog.selectUserDialog = true;
      },
      // 处理子组件传值
      handleRowSelected(row) {
        this.selectUser = row

      },
      closed() {
        this.form.selectUser =null;
        this.approvalDialog=false
      },
      // 确认
      async consent() {

        this.form.userId =this.selectUser.userId
        this.form.nickName =this.selectUser.nickName
        const res = await updateUser(this.form);
        this.approvalDialog = false;
        if (res.code == "0000") {
          this.$message.success(res.data)
          // 通知父组件刷新数据
          this.$emit('DialogChange')
        }
      },
    }
  };
</script>

<style scoped>

</style>
