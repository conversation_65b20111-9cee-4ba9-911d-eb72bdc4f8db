/**
* @author: administrator
* @date: 2023-11-22
* @description: pccw-rpa-ERP采购订单信息
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :form="staticSearchForm"
      :search-config="searchConfig"
      @reset="reset"
      @search="search"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
<!--        <el-button v-if="powerData['comprehensive_acceptance_add']" type="primary" @click="addHandle">新增</el-button>-->
<!--        <el-button v-if="powerData['comprehensive_acceptance_edit']" type="primary" @click="editHandle">修改</el-button>-->
<!--        <el-button v-if="powerData['comprehensive_acceptance_del']" type="primary" @click="delBatchHandle">删除</el-button>-->
        <el-button v-if="powerData['comprehensive_acceptance_export']" type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="tableHeader"
          :static-search-param="staticSearchParam"
          border
          selection
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import {listAcceptance, downloadService} from "@/api/pccw/comprehensive/acceptance"
// 获取地市:queryAreaListService
import {queryAreaListService} from '@/api/common_api.js'
import {commonDown} from "@/utils/btn";


export default {
  name: "Acceptance",
  data() {
    return {
      //权限
      powerData: [],
      //搜索字段配置
      searchConfig: [
        {
          label: '业务实体',
          type: 'select',
          fieldName: 'businessEntity',
          itemAs: true,
          options: []
        },
        {
          label: '项目编号',
          type: 'input',
          fieldName: 'projectNumber'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        // {
        //   label: '费用类型',
        //   type: 'select',
        //   fieldName: 'expenseType',
        //   options: []
        // },
      ],
      // 表头
      tableHeader: [
        // {
        //   prop: "id",
        //   label: "ID",
        //   align: "center",
        //   tooltip: true,
        //   width: 120
        // },
        {
          prop: "businessEntity",
          label: "业务实体",
          align: "center",
          tooltip: true,
          width: 200
        },
        {
          prop: "inventoryOrganization",
          label: "库存组织",
          align: "center",
          tooltip: true,
          width: 200
        },
        {
          prop: "receivingOrganization",
          label: "接收组织",
          align: "center",
          tooltip: true,
          width: 200
        },
        {
          prop: "projectNumber",
          label: "项目编号",
          align: "center",
          tooltip: true,
          width: 160
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          tooltip: true,
          width: 240
        },
        {
          prop: "stationNumber",
          label: "站号",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "purchaseOrder",
          label: "采购订单",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "orderDescription",
          label: "订单说明",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "vendorNumber",
          label: "供应商编号",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "vendorName",
          label: "供应商名称",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "orderApprovalStatus",
          label: "订单审批状态",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "orderCloseStatus",
          label: "订单关闭状态",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "orderCancelStatus",
          label: "订单取消状态",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "buyer",
          label: "采购员",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "department",
          label: "所在部门",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "approvalDate",
          label: "审批日期",
          align: "center",
          tooltip: true,
          width: 180
        },
        {
          prop: "createTime",
          label: "创建时间",
          align: "center",
          tooltip: true,
        },
        {
          prop: "closeDate",
          label: "关闭日期",
          align: "center",
          tooltip: true,
          width: 180
        },
        {
          prop: "lastReceiveDate",
          label: "最后一次接收日期",
          align: "center",
          tooltip: true,
          width: 180
        },
        {
          prop: "receiver",
          label: "接收人",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "receiveAmount",
          label: "接收金额sum",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "allocationAmount",
          label: "分配金额sum",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "matchAmount",
          label: "匹配金额sum",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "externalPoNumber",
          label: "外围系统PO编号",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "contractOrFrameworkCode",
          label: "省公司合同或省框架协议编码",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "orderReceivePercentage",
          label: "订单接收百分比",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "taskNumber",
          label: "任务编号",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "expenseType",
          label: "费用类型",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '120px',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (<span
                  class='table_btn mr10'
                  onClick={() => {
                    this.acitonHandle('edit', row)
                  }}
                >
                  处理
                </span>) : ('')}
                {this.powerData['company_info_view'] && row.id ? (
                  <span class='table_btn mr10' onClick={() => {
                    this.acitonHandle('detail', row)
                  }}>
                  查看
                </span>) : ('')}
                {row.isAllowDelete ? (
                  <span
                    class='table_btn'
                    onClick={() => {
                      this.delHandle(row)
                    }}
                  >
                  删除
                  </span>
                ) : ('')}
              </span>
            )
          }
        }
      ],
      // 表格数据API
      tableApi: listAcceptance,
      // 搜索静态条件
      staticSearchForm: {
        expenseType: "费用",
        // 所在部门
        departments: ["工程建设中心", "有线业务部"],
      },
      // 表格静态参数
      staticSearchParam: {
        expenseType: "费用",
        // 所在部门
        departments: ["工程建设中心", "有线业务部"],
      },
      // 导出参数
      loadParam: {
        expenseType: "费用",
        // 所在部门
        departments: ["工程建设中心", "有线业务部"],
      },
    }
  },
  created() {
    this.getPower()
    this.getAreaList('-2', 0)
  },
  methods: {
    getPower() {
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item) => {
        this.powerData[item.authority] = true
      })
      console.log(this.powerData, "权限信息")
    },

    /**
     * 地市
     * @param parentId
     * @param index
     */
    getAreaList(parentId, index) {
      console.log('parentId', parentId)
      console.log('index', index)
      queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.searchConfig[index], 'options', list)
        }
      })
    },
    /** 搜索按钮操作 */
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.page.current = 1

      if (this.$refs.searchForm.searchForm.businessEntity) {
        this.$refs.searchForm.searchForm.businessEntity = this.$refs.searchForm.searchForm.businessEntity.label
      }

      this.staticSearchParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )

      this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
    },
    // 表单重置
    reset(form) {
      this.search(form)
    },

    /** 新增按钮操作 */
    addHandle() {

    },
    /** 修改按钮操作 */
    editHandle(row) {

    },
    /** 删除按钮操作 */
    delBatchHandle(row) {

    },
    /** 导出按钮操作 */
    exportHandle() {
      this.loadParam.limit = this.$refs.table.page.total
      console.log("导出参数：",this.loadParam)
      commonDown({ ...this.loadParam}, downloadService);
    }
  }
}
</script>
