/**
* @author: administrator
* @date: 2023-12-11
* @description: sysUser
*/
<template>
  <div>
    <mssSearchForm
        ref="searchForm"
        :form="staticSearchForm"
        :search-config="searchConfig"
        @reset="reset"
        @search="search"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button v-if="powerData['system_sysUser_add']" type="primary" @click="addHandle">新增</el-button>
        <el-button v-if="powerData['system_sysUser_edit']" type="primary" @click="editHandle">修改</el-button>
        <el-button v-if="powerData['system_sysUser_del']" type="primary" @click="delBatchHandle">删除</el-button>
        <el-button v-if="powerData['system_sysUser_export']" type="primary" @click="exportHandle">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
            ref="table"
            :api="tableApi"
            :columns="tableHeader"
            :selectable="selectable"
            :static-search-param="staticSearchParam"
            border
            selection
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import {listSysUser} from "@/api/pccw/system/sysUser"

export default {
  name: "SysUser",
  data() {
    return {
      // 权限
      powerData: [],
      // 搜索字段配置
      searchConfig: [
        {
          label: '',
          type: 'input',
          fieldName: 'account'
        },
        {
          label: '',
          type: 'input',
          fieldName: 'mobile'
        },
        {
          label: '',
          type: 'input',
          fieldName: 'email'
        },
        {
          label: '',
          type: 'input',
          fieldName: 'realName'
        },
        {
          label: '',
          type: 'input',
          fieldName: 'empNum'
        },
        {
          label: '',
          type: 'input',
          fieldName: 'isPrimary'
        },
        {
          label: '',
          type: 'radio',
          fieldName: 'status',
          options: []
        },
        {
          label: '',
          type: 'input',
          fieldName: 'deptId'
        },
        {
          label: '',
          type: 'input',
          fieldName: 'firstDeptId'
        },
        {
          label: '',
          type: 'input',
          fieldName: 'secondDeptId'
        },
        {
          label: '',
          type: 'input',
          fieldName: 'thirdDeptId'
        },
      ],
      //表头
      tableHeader: [
        {
          prop: "userId",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "account",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "mobile",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "email",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "realName",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "empNum",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "password",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "isPrimary",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "status",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "delFlag",
          label: "",
          align: "center",
          tooltip: true,
        },
        {
          prop: "deptId",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "firstDeptId",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "secondDeptId",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "thirdDeptId",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "openid",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "openidType",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "platformId",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "sort",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "position",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "lastModifyPwdTime",
          label: "",
          align: "center",
          tooltip: true,
          width: 180
        },
        {
          prop: "lastLoginTime",
          label: "",
          align: "center",
          tooltip: true,
          width: 180
        },
        {
          prop: "lastLoginIp",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "curLoginTime",
          label: "",
          align: "center",
          tooltip: true,
          width: 180
        },
        {
          prop: "curLoginIp",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "createTime",
          label: "",
          align: "center",
          tooltip: true,
        },
        {
          prop: "updateTime",
          label: "",
          align: "center",
          tooltip: true,
        },
        {
          prop: "attribute1",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute2",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute3",
          label: "emis通讯地址",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute4",
          label: "emis描述",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute5",
          label: "合作人员身份证号",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute6",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute7",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute8",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute9",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "attribute10",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "att1",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "att2",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "att3",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "att4",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "att5",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "pwdExpiredTime",
          label: "",
          align: "center",
          tooltip: true,
          width: 180
        },
        {
          prop: "appCode",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "appValue",
          label: "",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          prop: "ownerType",
          label: "0-普通数据 1-系统数据 2-接口同步数据",
          align: "center",
          tooltip: true,
          width: 120
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '120px',
          formatter: (row) => {
            return (
                <span>
                {row.isAllowOperate ? (<span
                    class='table_btn mr10'
                    onClick={() => {
                      this.acitonHandle('edit', row)
                    }}
                >
                  处理
                </span>) : ('')}
                  {this.powerData['company_info_view'] && row.id ? (
                      <span class='table_btn mr10' onClick={() => {
                        this.acitonHandle('detail', row)
                      }}>
                  查看
                </span>) : ('')}
                  {row.isAllowDelete ? (
                      <span
                          class='table_btn'
                          onClick={() => {
                            this.delHandle(row)
                          }}
                      >
                  删除
                  </span>
                  ) : ('')}
              </span>
            )
          }
        }
      ],
      //表格数据API
      tableApi: listSysUser,
      //搜索静态条件
      staticSearchForm: {},
      //表格静态参数
      staticSearchParam: {},
    }
  },
  created() {
    this.getPower()
  },
  methods: {
    getPower() {
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item) => {
        this.powerData[item.authority] = true
      })
    },
    /** 搜索按钮操作 */
    search(form) {
      this.staticSearchParam = {...form}
      this.$refs.commonTable.page.current = 1
      this.$refs.commonTable.getTableData(form)
    },
    //表单重置
    reset(form) {
      this.search(form)
    },

    /** 新增按钮操作 */
    addHandle() {

    },
    /** 修改按钮操作 */
    editHandle(row) {

    },
    /** 删除按钮操作 */
    delBatchHandle(row) {

    },
    /** 导出按钮操作 */
    exportHandle() {

    }
  }
}
</script>
