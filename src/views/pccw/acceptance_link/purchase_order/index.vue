/**
* @author: ty
* @date: 2023-07-07
* @description: 安全检查工单-列表查询 TODO: 新增删除等待调试完成后需要去掉
*/
<template>
  <div>
    <mssSearchForm
        ref="searchForm"
        :searchConfig="searchConfig"
        @search="search"
        @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
<!--        <el-button @click="addHandle" type="primary">新增</el-button>-->
<!--        <el-button @click="delBatchHandle" type="primary">删除</el-button>-->
      </div>
      <div slot="content">
        <mssTable
            ref="table"
            selection
            :api="api"
            :columns="columns"
            :selectable="selectable"
            :staticSearchParam="staticSearchParam"
            border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService, beforeSaveService, saveService, delBatchService, delService } from '@/api/monthly_report/safe_check.js'
import { commonOneDel, commonMultDel } from '@/utils/btn'
export default {
  name: 'safe_check',
  data() {
    const validateStartEndDate = (rule, value, callback) => {
      if(this.$refs.searchForm.searchForm.startDate && this.$refs.searchForm.searchForm.endDate){
        if(new Date(this.$refs.searchForm.searchForm.startDate).getTime() > new Date(this.$refs.searchForm.searchForm.endDate).getTime()){
          callback(new Error('开始日期需小于结束日期'))
        }else{
          callback()
        }
      }else{
        callback()
      }
    }
    return {
      searchConfig: [
        {
          label: '地市',
          type: 'input',
          fieldName: 'qualityCode'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编号',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '所在部门',
          type: 'select',
          options: [],
          fieldName: 'qualityCode'
        },
        {
          label: '费用类型',
          type: 'select',
          options: [],
          fieldName: 'status'
        },

      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '项目名称',
          prop: 'name',
          tooltip: true,
          minWidth: 200
        },
        {
          label: '项目编号',
          prop: 'code',
          tooltip: true,
          minWidth: 180
        },
        {
          label: '所在部门',
          prop: 'tempType',
          tooltip: true,
          minWidth: 120
        },
        {
          label: '创建时间',
          prop: 'tempType',
          tooltip: true,
          minWidth: 140
        },
        {
          label: '关闭日期',
          prop: 'tempType',
          tooltip: true,
          minWidth: 140
        },
        {
          label: '最后一次接收日期',
          prop: 'tempType',
          tooltip: true,
          minWidth: 140
        },
        {
          label: '接收金额sum',
          prop: 'tempName',
          tooltip: true,
          minWidth: 120
        },
        {
          label: '分配金额sum',
          prop: 'creatorName',
          tooltip: true,
          minWidth: 120
        },{
          label: '匹配金额sum',
          prop: 'createDate',
          tooltip: true,
          minWidth: 120
        },
        {
          label: '订单接收百分比',
          prop: 'status',
          tooltip: true,
          minWidth: 120
        },
        {
          label: '费用类型',
          prop: 'status',
          tooltip: true,
        },
        {
          label: '操作',
          prop: '_operationCol',
          formatter: (row, column, cellValue, index) => {
            if (row.isAllowOperate) {
              if (row.isAllowDelete) {
                return (
                    <div>
                      <a href='javascript:;' onClick={() => { this.operateHandle(row, 'view') }}>查看</a>&nbsp;&nbsp;
                      <a href='javascript:;' onClick={() => { this.operateHandle(row, 'edit') }}>处理</a>&nbsp;&nbsp;
                      <a href='javascript:;' onClick={() => { this.operateHandle(row, 'dele') }}>删除</a>
                    </div>
                )
              } else {
                return (
                    <div>
                      <a href='javascript:;' onClick={() => { this.operateHandle(row, 'view') }}>查看</a>&nbsp;&nbsp;
                      <a href='javascript:;' onClick={() => { this.operateHandle(row, 'edit') }}>处理</a>
                    </div>
                )
              }

            } else {
              return (
                  <div>
                    <a href='javascript:;' onClick={() => { this.operateHandle(row, 'view') }}>查看</a>
                  </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标3是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.searchConfig[3], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 新增
    addHandle() {
      // 点击新增，校验成功即创建表单
      beforeSaveService().then(res=>{
        if (res.data && res.data != '') {
          this.$message.warning(res.data)
        } else {
          saveService({id:''}).then(res1=>{
            this.$router.push({
              path: '/monthly_report/safe_check/safe_check/edit',
              query: { boId: res1.data.id }
            })
          })
        }
      })
    },
    //  删除 todo 接口未调试
    delHandle(id) {
      let that = this
      const req = {
        sucCb: (res) => {
          if (res.code === '0000') {
            this.$message.success('删除成功')
          } else {
            this.$message.warning(res.data || '请稍后再试')
          }
          that.search(this.$refs?.searchForm?.searchForm || {})
        }
      }
      commonOneDel.call(this, id, delService, req.sucCb, 'path')
    },
    //  批量删除 todo 接口未调试
    delBatchHandle() {
      let that = this
      const req = {
        data: this.$refs.table.multipleSelection,
        delApi: delBatchService,
        key: 'id',
        sucCb: (res) => {
          if (res.code === '0000') {
            that.$message.success('批量删除成功')
          } else {
            that.$message.warning(res.data || '请稍后再试')
          }
          that.$refs.table.$refs.table.clearSelection() // 清空表格勾选
          that.search(this.$refs?.searchForm?.searchForm || {})
        }
      }
      commonMultDel.call(this, req)
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle(row, type){
      if (type == 'view') {
        this.$router.push({
          path: '/monthly_report/safe_check/safe_check/view',
          query: {boId:row.id}
        })
      } else if (type == 'edit') {
        this.$router.push({
          path: '/monthly_report/safe_check/safe_check/edit',
          query: {boId:row.id}
        })
      } else if (type == 'dele') {
        this.delHandle(row.id)
      }
    },
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
          JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    reset() {
      this.search()
    },
    selectable(row, index) {
      if (row.isAllowDelete) {
        return true
      } else {
        return false
      }
    },
  }
}
</script>
