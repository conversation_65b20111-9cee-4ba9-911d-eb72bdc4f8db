<template>
  <div class="draftList">
    <mssSearchForm :searchConfig="searchConfig" ref="searchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="addHandle">新增</el-button>
        <el-button @click="delHandle('multiple')">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          border
          selection
          :selectable="selectable"
          :reserveSelection="false"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  findPageService,
  multipleDelService,
  singleDelService
} from '@/api/staging_point/work_order_api.js'
import { commonMultDel, commonOneDel } from '@/utils/btn'
export default {
  name: 'draftList',
  data() {
    return {
      searchConfig: [
        {
          label: '暂存点名称',
          type: 'input',
          fieldName: 'storagePointName'
        },
        {
          label: '暂存点编号',
          type: 'input',
          fieldName: 'storagePointCode'
        }
      ],
      api: findPageService,
      staticSearchParam: {
        entityStatus: 'draft'
      },
      columns: [
        {
          label: '任务类型',
          prop: 'storageTempProcType',
          width:80,
          formatter: (row) => {
            let storageTempProcType = {
              ADD: '新增',
              MODIFY: '修改',
              UNDO: '撤销'
            }
            return <span>{storageTempProcType[row.storageTempProcType]}</span>
          }
        },
        {
          label: '地市',
          prop: 'cityName',
          tooltip: true,
          width:80,
        },
        {
          label: '区县',
          prop: 'countyName',
          tooltip: true,
          width:160,
        },
        {
          label: '暂存点名称',
          prop: 'storagePointName',
          tooltip: true
        },
        {
          label: '暂存点编号',
          prop: 'storagePointCode',
          tooltip: true
        },
        {
          label: '暂存点地址',
          prop: 'storagePointAddress',
          tooltip: true
        },
        {
          prop: 'operate',
          label: '操作',
          width:120,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.action(row, 'edit')
                    }}
                  >
                    处理
                  </el-button>
                ) : (
                  ''
                )}
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row, 'details')
                  }}
                >
                  查看
                </el-button>
                {row.isAllowDelete ? (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.delHandle('single', row)
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ''
                )}
              </span>
            )
          }
        }
      ]
    }
  },
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'draft',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'draft',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //新增
    addHandle() {
      this.$router.push({
        path: '/staging_point/work_order_edit',
        query: {
          storageTempProcType: 'ADD',
          operateType: 'edit',
          pathlabel: encodeURIComponent(`暂存点工单处理页`)
        }
      })
    },
    delHandle(type, row) {
      if (type == 'single') {
        commonOneDel.call(
          this,
          row.id,
          singleDelService,
          (res) => {
            if (res.code === '0000') {
              this.$message.success('删除成功')
            }
            this.search(this.$refs?.searchForm?.searchForm || {})
          },
          'path'
        )
      } else {
        const req = {
          data: this.$refs.table.multipleSelection,
          delApi: multipleDelService,
          key: 'id',
          sucCb: (res) => {
            if (res.code === '0000') {
              this.$message.success('删除成功')
            }
            this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
            this.search(this.$refs?.searchForm?.searchForm || {})
          }
        }
        commonMultDel.call(this, req)
      }
    },
    action(row, type) {
      this.$router.push({
        path: '/staging_point/work_order_edit',
        query: {
          storageTempProcType: row.storageTempProcType,
          boId: row.id,
          operateType: type,
          storageTempId: row.storageTempId,
          pathlabel: encodeURIComponent(
            `暂存点工单${type == 'edit' ? '处理页' : '详情页'}`
          )
        }
      })
    },
    selectable(row) {
      return row.isAllowDelete
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
