<template>
  <div class="StagingPointWorkOrder">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="草稿" name="draftList">
        <draftList></draftList>
      </el-tab-pane>
      <el-tab-pane label="变更" name="modifyList" lazy>
        <modifyList></modifyList>
      </el-tab-pane>
      <el-tab-pane label="待处理" name="todoList" lazy>
        <todoList></todoList>
      </el-tab-pane>
      <el-tab-pane label="已完成" name="doneList" lazy>
        <doneList></doneList>
      </el-tab-pane>
      <el-tab-pane label="已关闭" name="closedList" lazy>
        <closedList></closedList>
      </el-tab-pane>
      <el-tab-pane label="全部" name="wholeList" lazy>
        <wholeList></wholeList>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import draftList from './draf_list.vue'
import modifyList from './modify_list.vue'
import todoList from './todo_list.vue'
import doneList from './done_list.vue'
import closedList from './closed_list.vue'
import wholeList from './whole_list.vue'
export default {
  name: 'StagingPointWorkOrder',
  components: {
    draftList,
    todoList,
    doneList,
    closedList,
    wholeList,
    modifyList
  },
  data() {
    return {
      activeName: 'draftList'
    }
  },
  methods: {
    handleClick() {}
  }
}
</script>

<style lang="scss" scoped>
</style>