<template>
  <div class="StagingPointWorkOrderEdit">
    <div class="operate-btn">
      <el-button v-if="boId && dealPage" type="primary" @click="submit">提交</el-button>
      <el-button v-if="!boId" @click="save">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="暂存点基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :label-position="labelPosition"
          :rules="basicRules"
          :form="basicForm"
          labelWidth="140px"
          :disable-form="disableBasicForm"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionModify" class="page-anchor-point"></div>
    <mssCard v-if="storageTempProcType=='MODIFY'" title="暂存点修改信息">
      <div slot="content">
        <mssForm
          ref="modifyForm"
          :config="modifyConfig"
          :rules="modifyRules"
          :label-position="labelPosition"
          :form="modifyForm"
          labelWidth="140px"
          :disable-form="disableModifyForm"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionMaterials" class="page-anchor-point"></div>
    <mssCard v-if="storageTempProcType=='MODIFY' || storageTempProcType =='UNDO'" title="暂存点物资信息">
      <div slot="content">
        <mssTable
          :api="api"
          :staticSearchParam="staticSearchParam"
          ref="materialsTable"
          class="materialsTable"
          :columns="columns"
          :stationary="stationary"
        ></mssTable>
      </div>
    </mssCard>
    <div id="sectionRevoke" class="page-anchor-point"></div>
    <mssCard v-if="storageTempProcType=='UNDO'" title="暂存点撤销信息">
      <div slot="content">
        <mssForm
          ref="revokeForm"
          :config="revokeConfig"
          :rules="revokeRules"
          :label-position="labelPosition"
          :form="revokeForm"
          labelWidth="140px"
          :disable-form="disableRevokeForm"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="boId"
      :dealPage="dealPage"
      :boId="boId"
      :businessType="workflowCode"
      :nodeName="nodeName"
    ></mssAttachment>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <mssChooseUser :key="userKey" ref="chooseUser" :mult-select="false" @showCheckList="showCheckList" @closeDialog="closeDialog"></mssChooseUser>
  </div>
</template>

<script>
import {
  getByStoragePointCode,
  savePageService,
  getByIdService,
  updateByIdPageService,
  getAutoCodeAndName
} from '@/api/staging_point/work_order_api.js'
import { checkPhone } from '@/utils/validate.js'
import { materialFindPageService } from '@/api/staging_point/inbound_outbound_api'
import { queryAreaListService } from '@/api/common_api'
export default {
  name: 'StagingPointWorkOrderEdit',
  data() {
    const validationMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入电话'))
      } else if (!/^1[34578]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }
    return {
      basicConfig: [
        {
          label: '任务编号',
          type: 'input',
          prop: 'taskListNum',
          disabled: true
        },
        {
          label: '任务名称',
          type: 'input',
          prop: 'taskListName',
          disabled: true
        },
        {
          label: '地市',
          type: 'select',
          prop: 'city',
          options: [],
          disabled: true
        },
        {
          label: '区县',
          type: 'select',
          prop: 'county',
          options: [],
          eventListeners: {
            change: this.getAutoCodeAndName
          }
        },
        {
          label: '暂存点名称',
          type: 'input',
          prop: 'storagePointName',
          eventListeners: {
            change: this.getAutoCodeAndName
          }
        },
        {
          label: '暂存点编号',
          type: 'input',
          prop: 'storagePointCode',
          disabled: true
        },
        {
          label: '地市物资管理员姓名',
          type: 'input',
          prop: 'citiesSuppliesManagerName',
          disabled: true
        },
        {
          label: '地市物资管理员电话',
          type: 'input',
          prop: 'citiesSuppliesManagerPhone',
          disabled: true
        },
        {
          label: '账务管理员姓名',
          type: 'input',
          prop: 'accountingManagerName',
          readonly: true,
          eventListeners: {
            focus: () => {
              this.openUserSelectDialog('accountingManager', 'basicForm')
            } // 打开选择部门弹窗
          }
        },
        {
          label: '账务管理员电话',
          type: 'input',
          prop: 'accountingManagerPhone'
        },
        {
          label: '实物管理员姓名',
          type: 'input',
          prop: 'physicalManagerName',
          readonly: true,
          eventListeners: {
            focus: () => {
              this.openUserSelectDialog('physicalManager', 'basicForm')
            } // 打开选择部门弹窗
          }
        },
        {
          label: '实物管理员电话',
          type: 'input',
          prop: 'physicalManagerPhone'
        },
        {
          label: '暂存点地址',
          type: 'input',
          prop: 'storagePointAddress'
        },
        {
          label: '业主姓名',
          type: 'input',
          prop: 'ownerName'
        },
        {
          label: '业主联系电话',
          type: 'input',
          prop: 'ownerPhone'
        },
        {
          label: '备注',
          type: 'input',
          prop: 'note'
        }
      ],
      basicRules: {
        county: [{ required: true, message: '请选择区县', trigger: 'blur' }],
        storagePointName: [
          { required: true, message: '请输入暂存点名称', trigger: 'blur' }
        ],
        accountingManagerName: [
          {
            required: true,
            message: '请输入账务管理员姓名',
            trigger: 'change'
          }
        ],
        accountingManagerPhone: [
          { required: true, validator: validationMobile, trigger: 'blur' }
        ],
        physicalManagerName: [
          {
            required: true,
            message: '请输入实物管理员姓名',
            trigger: 'change'
          }
        ],
        physicalManagerPhone: [
          { required: true, validator: validationMobile, trigger: 'blur' }
        ],
        storagePointAddress: [
          { required: true, message: '请输入暂存点地址', trigger: 'blur' }
        ],
        ownerPhone: [
          {
            required: false,
            validator: (rule, value, callback) => {
              checkPhone(rule, value, callback, false)
            },
            trigger: 'blur'
          }
        ]
      },
      modifyConfig: [
        {
          label: '任务编号',
          type: 'input',
          prop: 'taskListNum',
          disabled: true
        },
        {
          label: '任务名称',
          type: 'input',
          prop: 'taskListName',
          disabled: true
        },
        {
          label: '账务管理员姓名',
          type: 'input',
          prop: 'accountingManagerName',
          readonly: true,
          eventListeners: {
            focus: () => {
              this.openUserSelectDialog('accountingManager', 'modifyForm')
            } // 打开选择部门弹窗
          }
        },
        {
          label: '账务管理员电话',
          type: 'input',
          prop: 'accountingManagerPhone'
        },
        {
          label: '实物管理员姓名',
          type: 'input',
          prop: 'physicalManagerName',
          readonly: true,
          eventListeners: {
            focus: () => {
              this.openUserSelectDialog('physicalManager', 'modifyForm')
            } // 打开选择部门弹窗
          }
        },
        {
          label: '实物管理员电话',
          type: 'input',
          prop: 'physicalManagerPhone'
        },
        {
          label: '暂存点地址',
          type: 'input',
          prop: 'storagePointAddress'
        },
        {
          label: '业主姓名',
          type: 'input',
          prop: 'ownerName'
        },
        {
          label: '业主联系电话',
          type: 'input',
          prop: 'ownerPhone'
        }
      ],
      modifyRules: {
        accountingManagerName: [
          {
            required: true,
            message: '请输入账务管理员姓名',
            trigger: 'change'
          }
        ],
        accountingManagerPhone: [
          { required: true, validator: validationMobile, trigger: 'blur' }
        ],
        physicalManagerName: [
          {
            required: true,
            message: '请输入实物管理员姓名',
            trigger: 'change'
          }
        ],
        physicalManagerPhone: [
          { required: true, validator: validationMobile, trigger: 'blur' }
        ],
        storagePointAddress: [
          { required: true, message: '请输入暂存点地址', trigger: 'blur' }
        ],
        ownerPhone: [
          {
            required: false,
            validator: (rule, value, callback) => {
              checkPhone(rule, value, callback, false)
            },
            trigger: 'blur'
          }
        ]
      },
      revokeConfig: [
        {
          label: '任务编号',
          type: 'input',
          prop: 'taskListNum',
          disabled: true
        },
        {
          label: '任务名称',
          type: 'input',
          prop: 'taskListName',
          disabled: true
        },
        {
          label: '原因说明',
          type: 'input',
          prop: 'reason'
        }
      ],
      revokeRules: {
        reason: [{ required: true, message: '请输入撤销原因', trigger: 'blur' }]
      },
      labelPosition: 'top',
      basicForm: {
        physicalManagerName: '',
        accountingManagerName: ''
      },
      modifyForm: {
        physicalManagerName: '',
        accountingManagerName: ''
      },
      columns: [
        {
          prop: 'outputOrderCode',
          label: '供应链出库通知单',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'receiveApplyCode',
          label: '供应链领用申请单号',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectCode',
          label: '项目编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectName',
          label: '项目名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskCode',
          label: '任务编号',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskName',
          label: '任务名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialCode',
          label: '物料编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialName',
          label: '物料名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'unit',
          label: '单位',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'num',
          label: '物料数量',
          align: 'center',
          tooltip: true
        }
      ],
      api: materialFindPageService,
      staticSearchParam: {},
      storageTempProcType: 'ADD',
      revokeForm: {},
      boId: '',
      workflowCode: 'StorageTempProc',
      nodeName: '草稿',
      nodeCode: '',
      stationary: [],
      pageAnchorConfig: [],
      completeTaskUrl: '',
      returnAddress: '/staging_point/work_order_list',
      storageTempProcType: '',
      disableBasicForm: true,
      disableModifyForm: true,
      disableRevokeForm: true,
      operateType: '',
      dealPage: true,
      storagePointCode: '',
      storageTempId: '',
      chooseUserType: '',
      modeType: '',
      userKey:1,
      swglyRole:{},
      zwglyRole:{}
    }
  },
  watch: {
    'basicForm.city': {
      handler(n, o) {
        if (n) {
          this.getAreaList(this.basicForm.city)
        }
      },
      deep: true,
      immediate: true
    },
    storageTempProcType(n, o) {
      if (n) {
        if (this.operateType == 'edit') {
          this.$refs.workFlow.init()
        } else {
          this.labelPosition = 'left'
          this.dealPage = false
        }
      }
    }
  },
  computed:{
    addPageAnchorConfig(){
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment',
          show:!!this.boId
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow',
          show:!!this.boId
        }
      ]
    },
    editPageAnchorConfig(){
      return  [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '修改<br>信息',
          id: 'sectionModify'
        },
        {
          text: '物资<br>信息',
          id: 'sectionMaterials'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment',
          show:!!this.boId
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow',
          show:!!this.boId
        }
      ]
    },
    revokePageAnchorConfig(){
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '物资<br>信息',
          id: 'sectionMaterials'
        },
        {
          text: '撤销<br>信息',
          id: 'sectionRevoke'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment',
          show:!!this.boId
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow',
          show:!!this.boId
        }
      ]
    }
  },
  created() {
    this.storageTempProcType = this.$route.query.storageTempProcType
    this.operateType = this.$route.query.operateType
    if(this.operateType == 'details'){
      this.basicConfig.forEach(item=>{
        if(!item.label.includes('：')){
          item.label = item.label + "："
        }
      })
      this.modifyConfig.forEach(item=>{
        if(!item.label.includes('：')){
          item.label = item.label + "："
        }
      })
      this.revokeConfig.forEach(item=>{
        if(!item.label.includes('：')){
          item.label = item.label + "："
        }
      })
    }
    this.storagePointCode = this.$route.query.storagePointCode
    this.storageTempId = this.$route.query.storageTempId
    this.staticSearchParam.storageTempId = this.storageTempId
    this.boId = this.$route.query.boId || ''
    //获取地市
    this.getAreaList('-2')
    if (!this.boId) {
      this.$set(this.basicForm, 'city', sessionStorage.getItem('firstDeptId'))
    }
    if (this.boId) {
      getByIdService(this.boId).then((res) => {
        if (res && res.code == '0000') {
          this.storageTempId = res.data.storageTempId
          this.staticSearchParam.storageTempId = this.storageTempId
          this.storageTempProcType = res.data.storageTempProcType
          this.completeTaskUrl = res.data.completeTaskUrl
          this.zwglyRole = res.data.zwglyRole
          this.swglyRole = res.data.swglyRole
          if (this.storageTempProcType == 'ADD') {
            this.basicForm = res.data
            this.pageAnchorConfig = this.addPageAnchorConfig
          } else if (this.storageTempProcType == 'MODIFY') {
            this.modifyForm = res.data
            this.basicForm = res.data.storageTempDto
            this.pageAnchorConfig = this.editPageAnchorConfig
          } else if (this.storageTempProcType == 'UNDO') {
            this.revokeForm = res.data
            this.basicForm = res.data.storageTempDto
            this.pageAnchorConfig = this.revokePageAnchorConfig
          }
        }
      })
    } else {
      if (this.storageTempProcType == 'ADD') {
        this.pageAnchorConfig = this.addPageAnchorConfig
        getByStoragePointCode({
          storageTempProcType: this.storageTempProcType
        }).then((res) => {
          if (res && res.code == '0000') {
            this.basicForm = res.data
            this.completeTaskUrl = res.data.completeTaskUrl
            this.zwglyRole = res.data.zwglyRole
            this.swglyRole = res.data.swglyRole
          }
        })
      } else if (this.storageTempProcType == 'MODIFY') {
        this.pageAnchorConfig = this.editPageAnchorConfig
        getByStoragePointCode({
          storageTempProcType: this.storageTempProcType,
          storagePointCode: this.storagePointCode
        }).then((res) => {
          if (res && res.code == '0000') {
            this.modifyForm = res.data
            this.basicForm = res.data.storageTempDto
            this.completeTaskUrl = res.data.completeTaskUrl
            this.zwglyRole = res.data.zwglyRole
            this.swglyRole = res.data.swglyRole
          }
        })
      } else if (this.storageTempProcType == 'UNDO') {
        this.pageAnchorConfig = this.revokePageAnchorConfig
        getByStoragePointCode({
          storageTempProcType: this.storageTempProcType,
          storagePointCode: this.storagePointCode
        }).then((res) => {
          if (res && res.code == '0000') {
            this.revokeForm = res.data
            this.basicForm = res.data.storageTempDto
            this.completeTaskUrl = res.data.completeTaskUrl
            this.zwglyRole = res.data.zwglyRole
            this.swglyRole = res.data.swglyRole
          }
        })
      }
    }
  },
  methods: {
    // 获取地市区县
    getAreaList(parentId) {
      queryAreaListService({ parentId, typeCode: 'area' }).then((res) => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach((item) => {
            list.push({ label: item.name, value: item.id })
          })
          if (parentId == '-2') {
            this.$set(this.basicConfig[2], 'options', list)
          } else {
            this.$set(this.basicConfig[3], 'options', list)
          }
        }
      })
    },
    // 保存草稿
    save(cb) {
      if (this.storageTempProcType == 'ADD') {
        this.addSave(cb)
      } else if (this.storageTempProcType == 'MODIFY') {
        this.modifySave(cb)
      } else if (this.storageTempProcType == 'UNDO') {
        this.undoSave(cb)
      }
    },
    getAutoCodeAndName() {
      if (
        this.$refs.basicForm.modelForm.city &&
        this.$refs.basicForm.modelForm.county &&
        this.$refs.basicForm.modelForm.storagePointName
      ) {
        let req = {
          city: this.$refs.basicForm.modelForm.city,
          county: this.$refs.basicForm.modelForm.county,
          storagePointName: this.$refs.basicForm.modelForm.storagePointName,
          storageTempProcType: this.storageTempProcType
        }
        getAutoCodeAndName(req).then((res) => {
          if (res && res.code == '0000') {
            this.basicForm = res.data
          }
        })
      }
    },
    //新增保存
    addSave(cb) {
      this.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          let req = {
            id: this.boId,
            storageTempProcType: this.storageTempProcType,
            ...this.$refs.basicForm.modelForm
          }
          if (this.boId) {
            updateByIdPageService(req).then((res) => {
              if (res.code === '0000') {
                if (cb) {
                  cb()
                } else {
                  this.$message.success('保存成功')
                }
              }
            })
          } else {
            savePageService(req).then((res) => {
              if (res.code === '0000') {
                this.basicForm = res.data
                this.completeTaskUrl = res.data.completeTaskUrl
                this.boId = res.data.id
                this.$closeTagView({
                  close: this.$route.fullPath,
                  to: `${this.$route.fullPath}&boId=${this.boId}`
                })
                this.$message({
                  type: 'success',
                  message: '保存成功！'
                })
              }
            })
          }
        }
      })
    },
    //修改保存
    modifySave(cb) {
      this.$refs.modifyForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          let req = {
            id: this.boId,
            storageTempProcType: this.storageTempProcType,
            ...this.$refs.modifyForm.modelForm
          }
          if (this.boId) {
            updateByIdPageService(req).then((res) => {
              if (res.code === '0000') {
                if (cb) {
                  cb()
                } else {
                  this.$message.success('保存成功')
                }
              }
            })
          } else {
            savePageService(req).then((res) => {
              if (res.code === '0000') {
                this.modifyForm = res.data
                this.boId = res.data.id
                this.completeTaskUrl = res.data.completeTaskUrl
                this.$closeTagView({
                  close: this.$route.fullPath,
                  to: `${this.$route.fullPath}&boId=${this.boId}`
                })
                this.$message({
                  type: 'success',
                  message: '保存成功！'
                })
              }
            })
          }
        }
      })
    },
    //修改保存
    undoSave(cb) {
      this.$refs.revokeForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          let req = {
            id: this.boId,
            storageTempProcType: this.storageTempProcType,
            ...this.$refs.revokeForm.modelForm
          }
          if (this.boId) {
            updateByIdPageService(req).then((res) => {
              if (res.code === '0000') {
                if (cb) {
                  cb()
                } else {
                  this.$message.success('保存成功')
                }
              }
            })
          } else {
            savePageService(req).then((res) => {
              if (res.code === '0000') {
                this.revokeForm = res.data
                this.boId = res.data.id
                this.completeTaskUrl = res.data.completeTaskUrl
                this.$closeTagView({
                  close: this.$route.fullPath,
                  to: `${this.$route.fullPath}&boId=${this.boId}`
                })
                this.$message({
                  type: 'success',
                  message: '保存成功！'
                })
              }
            })
          }
        }
      })
    },
    // 提交
    submit() {
      // if (this.storageTempProcType == 'ADD') {
      //   this.$refs.basicForm.$refs.form.validateScroll((valid) => {
      //     if (valid) {
      //       this.$refs.workFlow.opendialogInitNextPath()
      //     }
      //   })
      // } else if (this.storageTempProcType == 'MODIFY') {
      //   this.$refs.modifyForm.$refs.form.validateScroll((valid) => {
      //     if (valid) {
      //       this.$refs.workFlow.opendialogInitNextPath()
      //     }
      //   })
      // } else if (this.storageTempProcType == 'UNDO') {
      //   this.$refs.revokeForm.$refs.form.validateScroll((valid) => {
      //     if (valid) {
      //       this.$refs.workFlow.opendialogInitNextPath()
      //     }
      //   })
      // }
      this.$refs.workFlow.opendialogInitNextPath()
    },
    async beforeSubmit() {
      let str=''
      if (this.storageTempProcType == 'ADD') {
        this.$refs.basicForm.$refs.form.validateScroll((valid) => {
          str=valid?'':'有必填信息未填写完整，请检查'
        })
      } else if (this.storageTempProcType == 'MODIFY') {
        this.$refs.modifyForm.$refs.form.validateScroll((valid) => {
          str=valid?'':'有必填信息未填写完整，请检查'
        })
      } else if (this.storageTempProcType == 'UNDO') {
        this.$refs.revokeForm.$refs.form.validateScroll((valid) => {
          str=valid?'':'有必填信息未填写完整，请检查'
        })
      }
      return str
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/staging_point/work_order_list'
      })
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      let taskListName
      if (this.storageTempProcType == 'ADD') {
        taskListName = this.$refs.basicForm.modelForm.taskListName
      } else if (this.storageTempProcType == 'MODIFY') {
        taskListName = this.$refs.modifyForm.modelForm.taskListName
      } else if (this.storageTempProcType == 'UNDO') {
        taskListName = this.$refs.revokeForm.modelForm.taskListName
      }
      const name = {
        code: 'name',
        value: taskListName
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode() {
      // 设置默认处理人
      const fileNameArr = {
        // swzglycl: { userName: 'admin', userId: '1' }
      }
      return fileNameArr
    },
    getNodeData(data) {
      this.nodeName = data.nodeName
      this.nodeCode = data.nodeCode
      if (this.storageTempProcType == 'ADD') {
        this.disableBasicForm = data.nodeCode != 'cg'
      } else if (this.storageTempProcType == 'MODIFY') {
        this.disableModifyForm = data.nodeCode != 'cg'
      } else if (this.storageTempProcType == 'UNDO') {
        this.disableRevokeForm = data.nodeCode != 'cg'
      }
    },
    // 打开选人弹框
    openUserSelectDialog(name, modeType) {
      if (!this.disableBasicForm || !this.disableModifyForm) {
        let roleIds
        if(name == 'accountingManager'){
          roleIds = this.zwglyRole.roleId
        }else if(name == 'physicalManager'){
          roleIds = this.swglyRole.roleId
        }
        this.modeType = modeType
        this.chooseUserType = name
        let personnal = {}
        const excuterIds = this.$refs[this.modeType].modelForm[name] || ''
        const excuterNames =
          this.$refs[this.modeType].modelForm[`${name}Name`] || ''
        if (excuterIds) {
          personnal = {
            excuterIds,
            excuterNames
          }
        }
        let deptParams = {
          rootId:name == 'physicalManager' ? '-1' : this.$refs.basicForm.modelForm.city,
          orgChildId: name == 'physicalManager' ? '-1' : this.$refs.basicForm.modelForm.city,
          roleIds:roleIds
        }
        let personParams = {
          roleIds:roleIds
        }
        this.$refs.chooseUser.init(personnal, deptParams ,personParams)
      }
    },
    showCheckList({ checkList }) {
      if (checkList && checkList.length) {
        if (
          this.$refs[this.modeType].modelForm[`${this.chooseUserType}Name`] !=
            checkList[0].realName &&
          this.$refs[this.modeType].modelForm[this.chooseUserType] !=
            checkList[0].userId
        ) {
          this.$set(
            this.$refs[this.modeType].modelForm,
            `${this.chooseUserType}Name`,
            checkList[0].realName
          )
          this.$set(
            this.$refs[this.modeType].modelForm,
            `${this.chooseUserType}Phone`,
            checkList[0].mobile
          )
          this.$set(
            this.$refs[this.modeType].modelForm,
            `${this.chooseUserType}`,
            checkList[0].userId
          )
        }
        this.chooseUserType = ''
        this.modeType = ''
      } else {
        this.$set(
          this.$refs[this.modeType].modelForm,
          `${this.chooseUserType}Name`,
          ''
        )
        this.$set(
          this.$refs[this.modeType].modelForm,
          `${this.chooseUserType}Phone`,
          ''
        )
        this.$set(
          this.$refs[this.modeType].modelForm,
          `${this.chooseUserType}`,
          ''
        )
        this.chooseUserType = ''
        this.modeType = ''
      }
    },
    closeDialog(){
      this.$nextTick(()=>{
        this.userKey ++
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.StagingPointWorkOrderEdit {
  .file-tips {
    font-size: 12px;
    margin-left: 5px;
  }
  .operate-btn {
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
