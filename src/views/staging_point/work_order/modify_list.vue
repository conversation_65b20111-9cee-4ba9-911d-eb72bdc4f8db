<template>
  <div class="doneList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable ref="table" :api="api" :columns="columns" border :staticSearchParam="staticSearchParam"></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  findTempPageService,
  undoCheckService
} from '@/api/staging_point/work_order_api.js'
export default {
  name: 'doneList',
  data() {
    return {
      searchConfig: [
        {
          label: '暂存点名称',
          type: 'input',
          fieldName: 'storagePointName'
        },
        {
          label: '暂存点编号',
          type: 'input',
          fieldName: 'storagePointCode'
        }
      ],
      api: findTempPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '地市',
          prop: 'cityName',
          tooltip:true,
          width:80,
        },
        {
          label: '区县',
          prop: 'countyName',
          tooltip:true,
          width:160,
        },
        {
          label: '暂存点名称',
          prop: 'storagePointName',
          tooltip:true,
        },
        {
          label: '暂存点编号',
          prop: 'storagePointCode',
          tooltip:true,
        },
        {
          label: '暂存点地址',
          prop: 'storagePointAddress',
          tooltip:true,
        },
        {
          prop: 'operate',
          label: '操作',
          width:100,
          formatter: (row) => {
            return (
              <span>
                <span>
                  <el-button
                    type="text"
                    onClick={() => {
                      this.action('MODIFY', row)
                    }}
                  >
                    修改
                  </el-button>
                </span>
                <span>
                  <el-button
                    style="margin-left:10px"
                    type="text"
                    onClick={() => {
                      this.action('UNDO', row)
                    }}
                  >
                    撤销
                  </el-button>
                </span>
              </span>
            )
          }
        }
      ]
    }
  },
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {}
      this.$refs.table.getTableData(searchForm)
    },
    action(type, row) {
      if (type == 'UNDO') {
        undoCheckService({ id: row.id }).then((res) => {
          if (res && res.code == '0000' && res.data === true) {
            this.$confirm(
              '该暂存点存在库存物资，请先完成物资出库或退库操作！',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton:false,
                closeOnClickModal:false,
                type: 'warning'
              }
            )
          } else if (res && res.code == '0000' && res.data === false) {
            this.$router.push({
              path: '/staging_point/work_order_edit',
              query: {
                storageTempProcType: type,
                operateType: 'edit',
                storageTempId: row.id,
                storagePointCode: row.storagePointCode,
                pathlabel: encodeURIComponent(`暂存点工单处理页`),
              }
            })
          }
        })
      } else {
        this.$router.push({
          path: '/staging_point/work_order_edit',
          query: {
            storageTempProcType: type,
            operateType: 'edit',
            storageTempId: row.id,
            storagePointCode: row.storagePointCode,
            pathlabel: encodeURIComponent(`暂存点工单处理页`),
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
