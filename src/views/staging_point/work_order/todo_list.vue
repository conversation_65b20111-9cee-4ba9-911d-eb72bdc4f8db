<template>
  <div class="todoList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          border
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { findPageService } from '@/api/staging_point/work_order_api.js'
export default {
  name: 'todoList',
  data() {
    return {
      searchConfig: [
        {
          label: '暂存点名称',
          type: 'input',
          fieldName: 'storagePointName'
        },
        {
          label: '暂存点编号',
          type: 'input',
          fieldName: 'storagePointCode'
        }
      ],
      api: findPageService,
      staticSearchParam: {
        entityStatus: 'in_process'
      },
      columns: [
        {
          label: '任务类型',
          prop: 'storageTempProcType',
          width:80,
          formatter: (row) => {
            let storageTempProcType = {
              ADD: '新增',
              MODIFY: '修改',
              UNDO: '撤销'
            }
            return <span>{storageTempProcType[row.storageTempProcType]}</span>
          }
        },
        {
          label: '地市',
          prop: 'cityName',
          tooltip: true,
          width:80,
        },
        {
          label: '区县',
          prop: 'countyName',
          tooltip: true,
          width:160,
        },
        {
          label: '暂存点名称',
          prop: 'storagePointName',
          tooltip: true
        },
        {
          label: '暂存点编号',
          prop: 'storagePointCode',
          tooltip: true
        },
        {
          label: '暂存点地址',
          prop: 'storagePointAddress',
          tooltip: true
        },
        {
          prop: 'operate',
          label: '操作',
          width:100,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.action(row, 'edit')
                    }}
                  >
                    处理
                  </el-button>
                ) : (
                  ''
                )}
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row, 'details')
                  }}
                >
                  查看
                </el-button>
              </span>
            )
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'in_process',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'in_process'
      }
      this.$refs.table.getTableData(searchForm)
    },
    action(row, type) {
      this.$router.push({
        path: '/staging_point/work_order_edit',
        query: {
          storageTempProcType: row.storageTempProcType,
          boId: row.id,
          operateType: type,
          storageTempId: row.storageTempId,
          pathlabel: encodeURIComponent(
            `暂存点工单${type == 'edit' ? '处理页' : '详情页'}`
          )
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
