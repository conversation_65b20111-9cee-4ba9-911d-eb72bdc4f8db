<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-06 14:24:34
 * @Description: 台账信息管理
-->
<template>
	<div class="ledger_information">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="searchForm"
			@search="search"
			@reset="reset"
			@changeSelect="changeSelect"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import { findPageService, downloadService} from "@/api/staging_point/ledger_management"
import { queryAreaListService } from "@/api/common_api"
import { commonDown } from "@/utils/btn"
export default {
	name: 'ledgerManagementList',
	data() {
		return {
			searchConfig: [
				{
					type: 'select',
					fieldName: 'city',
					label: '地市',
          options: []
				},
        {
					type: 'select',
					fieldName: 'county',
					label: '区县',
          options: []
				},
				{
					type: 'input',
					fieldName: 'storagePointName',
					label: '暂存点名称',
				},
        {
					type: 'input',
					fieldName: 'storagePointCode',
					label: '暂存点编号',
				},
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findPageService,
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "cityName",
						label: "地市",
						minWidth: 120,
						tooltip: true,
					},
					{
						prop: "countyName",
						label: "区县",
						minWidth: 120,
						tooltip: true
					},
					{
						prop: "storagePointCode",
						label: "暂存点编号",
						minWidth: 200,
						tooltip: true,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row)}}>{row.storagePointCode}</span>
							)
						}
					},
					{
						prop: "storagePointName",
						label: "暂存点名称",
						minWidth: 180,
						tooltip: true
					},
					{
						prop: "storagePointAddress",
						label: "暂定点详细地址",
						minWidth: 180,
						tooltip: true
					},
					{
						prop: "physicalManagerName",
						label: "实物管理员",
						minWidth: 120,
						tooltip: true
					},
					{
						prop: "accountingManagerName",
						label: "账务管理员",
						minWidth: 120,
						tooltip: true
					},
					{
						prop: "updatedName",
						label: "最近更新人",
						minWidth: 120,
						tooltip: true
					},
					{
						prop: "updateDate",
						label: "最新更新时间",
            minWidth: 140
					},
					{
						prop: "deletedFlag",
						label: "暂存点状态",
						minWidth: 100
					}
				]
			}
		}

	},
	created(){
		this.getAreaList('-2', 0)
	},
	methods: {
		getAreaList(parentId, index) {
			queryAreaListService({ parentId: parentId, typeCode: 'area', filter: 'city' }).then(res => {
				if (res.code === '0000') {
				const list = []
				res.data.forEach(item => {
					list.push({ label: item.name, value: item.id })
				})
				this.$set(this.searchConfig[index], 'options', list)
				}
			})
    },
		changeSelect(name, val){
			if(name == 'city'){
				this.$set(this.$refs.searchForm.searchForm,'county','')
				this.getAreaList(val, 1)
			}
		},
		search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
		exportMethod(){
			commonDown({ ...this.staticSearchParam, exportType: 'exportPage' }, downloadService);
		},
		toDetail(row){
			this.$router.push({
				path: '/staging_point/ledger_management_info',
				query: {
					boId: row.id,
					storagePointCode: row.storagePointCode,
					deleted: row.deleted
				}
			})
		}
	}
}
</script>


