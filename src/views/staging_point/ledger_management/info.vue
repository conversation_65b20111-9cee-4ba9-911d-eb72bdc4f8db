<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-06 14:41:40
 * @Description: 暂存点详情
-->
<template>
	<div class="stagingPointDetail">
		<div class="operate-btn">
			<el-button @click="goBack">返回</el-button>
		</div>

		<div id="sectionBasic" class="page-anchor-point"></div>
		<mssCard title="暂存点基本信息">
			<div slot="content">
				<mssForm labelWidth="200px" disableForm :config="basicConfig" :labelPosition="labelPosition" :form="basicForm">
				</mssForm>
			</div>
		</mssCard>

		<div id="sectionHistory" class="page-anchor-point"></div>
		<mssCard title="暂存点历史信息">
			<div slot="content">
				<mssForm v-if="historyInfoFlag" labelWidth="200px" disableForm :config="basicConfig" :labelPosition="labelPosition" :form="historyForm">
				</mssForm>
				<div v-else>无</div>
			</div>
		</mssCard>

		<div id="sectionDelete" class="page-anchor-point"></div>
		<mssCard title="暂存点删除信息">
			<div slot="content">
				<div v-if="deleteReason.length">
					<p v-for="(item, index) in deleteReason" :key="index">
						删除申请工单原因：{{ item.reason }}
					</p>
				</div>
				<div v-else>无</div>
			</div>
		</mssCard>

		<div id="sectionDetail" class="page-anchor-point"></div>
		<mssCard title="暂存点物资详情">

			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssSearchForm
					:searchConfig="searchConfig"
					ref="SearchForm"
					@search="search"
					@reset="reset"
				></mssSearchForm>
				<mssTable border ref="table" :columns="columns" :staticSearchParam="staticSearchParam" :api="tableApi">
				</mssTable>
			</div>

		</mssCard>
		<mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
	</div>
</template>

<script>
import {
	getBasicInfoService,
	getDeleteInfoService,
	getMaterialDerailService,
	getStorageHistoryService,
	exportPageMaterial
} from '@/api/staging_point/ledger_management'
import { commonDown } from "@/utils/btn";
export default {
	name: 'ledgerManagementInfo',
	data() {
		return {
			boId: this.$route.query.boId,
			deleted: this.$route.query.deleted,
			basicConfig: [
				{
					label: '地市：',
					type: 'input',
					prop: 'cityName',
					span: 12
				},
				{
					label: '区县：',
					type: 'input',
					prop: 'countyName',
					span: 12
				},
				{
					label: '暂存点名称：',
					type: 'input',
					prop: 'storagePointName',
					span: 12
				},
				{
					label: '暂存点编号：',
					type: 'input',
					prop: 'storagePointCode',
					span: 12
				},
				{
					label: '地市物资管理员：',
					type: 'input',
					prop: 'citiesSuppliesManagerName',
					span: 12
				},
				{
					label: '账务管理员：',
					type: 'input',
					prop: 'accountingManagerName',
					span: 12
				},
				{
					label: '实物管理员：',
					type: 'input',
					prop: 'physicalManagerName',
					span: 12
				},
				{
					label: '详细地址：',
					type: 'input',
					prop: 'storagePointAddress',
					span: 12
				},
				{
					label: '业主姓名：',
					type: 'input',
					prop: 'ownerName',
					span: 12
				},
				{
					label: '业主联系电话：',
					type: 'input',
					prop: 'ownerPhone',
					span: 12
				},
			],
			searchConfig: [{
					type: 'input',
					fieldName: 'materialName',
					label: '物料名称',
				},
        {
					type: 'input',
					fieldName: 'materialCode',
					label: '物料编码',
				}],
			basicForm: {},
			historyForm: {},
			labelPosition: 'left', // 查看页面放在左边
			pageAnchorConfig: [
				{
					text: '基本<br>信息',
					id: 'sectionBasic'
				},
				{
					text: '历史<br>信息',
					id: 'sectionHistory'
				},
				{
					text: '删除<br>信息',
					id: 'sectionDelete'
				},
				{
					text: '物资<br>详情',
					id: 'sectionDetail'
				}
			],
			staticSearchParam: {storageTempId: this.$route.query.boId},
			tableApi: getMaterialDerailService,
			loadParams: {},
			deleteReason: [],
			historyInfoFlag: false
		}
	},
	computed: {
		columns: {
			get() {
				return [
					{
						prop: "materialName",
						label: "物料名称",
						minWidth: 280,
						tooltip: true
					},
					{
						prop: "materialCode",
						label: "物料编码",
						minWidth: 160,
						tooltip: true
					},
					{
						prop: "unit",
						label: "单位",
						minWidth: 80
					},
					{
						prop: "unitPrice",
						label: "单价（元）",
						minWidth: 100
					},
					{
						prop: "intoNum",
						label: "最近一次入库登记量",
						minWidth: 140
					},
					{
						prop: "outNum",
						label: "最近一次出库登记量",
						minWidth: 140
					},
					{
						prop: "refNum",
						label: "最近一次退库量",
						minWidth: 140
					},
					{
						prop: "actualIntoNum",
						label: "最近一次实际入库量",
						minWidth: 140
					},
					{
						prop: "actualOutNum",
						label: "最近一次实际出库量",
						minWidth: 140
					},
					{
						prop: "actualRefNum",
						label: "最近一次实际退库量",
						minWidth: 140
					},
					{
						prop: "num",
						label: "当前库存",
						minWidth: 100,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row)}}>{row.num}</span>
							)
						}
					},
					{
						prop: "amount",
						label: "当前库存金额（元）",
						minWidth: 140
					}
				]
			}
		}
	},
	created() {
		this.initPage()
	},
	methods: {
		initPage(){
			// 暂存点基础信息
			getBasicInfoService({id: this.boId}).then(res=>{
				if(res && res.code === '0000' && res.data){
					this.basicForm = res.data
				}
			})
			// 暂存点历史信息
			getStorageHistoryService({storagePointCode: this.$route.query.storagePointCode}).then(res=>{
				if(res && res.code === '0000'){
					if(res.data.length){
						this.historyInfoFlag = true
						this.historyForm = res.data[0]
					}
				}
			})
			// 暂存点删除信息
			getDeleteInfoService({storagePointCode: this.$route.query.storagePointCode}).then(res=>{
				if(res && res.code === '0000'){
					res.data.length && (this.deleteReason = res.data)
				}
			})
		},
		goBack() {
			this.$closeTagView({
        close: this.$route.fullPath,
        to: '/staging_point/ledger_management_list'
      })
		},
		search(searchData) {
			this.$refs.table.page.current = 1
			this.loadParams = JSON.parse(JSON.stringify(searchData))
			this.$refs.table.getTableData(searchData)
		},
		reset(searchData) {
			this.$refs.table.page.current = 1
			this.loadParams = JSON.parse(JSON.stringify(searchData))
			this.$refs.table.getTableData(searchData)
		},
		exportMethod(){
			commonDown({ ...this.staticSearchParam, ...this.loadParams }, exportPageMaterial);
		},
		toDetail(row){
			this.$router.push({
				path: '/staging_point/merterial_history_deteail',
				query: {
					boId: row.id,
					materialName: row.materialName,
					materialCode: row.materialCode,
					storageTempId: row.storageTempId
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.stagingPointDetail {
	padding-right: 70px;

	.operate-btn {
		text-align: right;
		margin-bottom: 10px;
	}
}
</style>

