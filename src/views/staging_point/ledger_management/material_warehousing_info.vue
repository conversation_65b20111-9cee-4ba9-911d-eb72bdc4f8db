<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-06 17:49:06
 * @Description: 物资入库信息-物资信息“入库登记量”点击下钻
-->
<template>
	<div class="material_warehousing_info">
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable border :columns="columns" :staticSearchParam="staticSearchParam" :stationary="tableData" />
			</div>
		</mssCard>
	</div>
</template>

<script>
export default {
	name: 'material_warehousing_info',
	data() {
		return {
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableData: []
		}
	},
	computed: {
		columns: {
			get() {
				return [
					{ prop: "1", label: "入库物料名称", minWidth: 220 },
					{ prop: "3", label: "物料编码", minWidth: 140 },
					{ prop: "4", label: "单位", minWidth: 80 },
					{ prop: "5", label: "入库登记量", minWidth: 120 },
					{ prop: "2", label: "入库时间", minWidth: 120 },
					{ prop: "601", label: "操作人", minWidth: 160 },
					{ prop: "6", label: "操作人所属公司", minWidth: 160 },
					{ prop: "7", label: "当前累计入库量", minWidth: 140 }
				]
			}
		}

	},
	created() {

	},
	methods: {
		exportMethod() { }
	}
}
</script>
