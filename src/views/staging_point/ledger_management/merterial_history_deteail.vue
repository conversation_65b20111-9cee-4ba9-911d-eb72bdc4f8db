<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-22 15:53:51
 * @Description: 物资详情-当前库存-下钻  物资历史详情页
-->

<template>
	<div class="material_history_detail">
    <div class="operate-btn">
			<el-button @click="goBack">返回</el-button>
		</div>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
        <mssSearchForm 
					:searchConfig="searchConfig" 
					ref="SearchForm" 
					@search="search" 
					:resetFlag="false"
				></mssSearchForm>
				<mssTable border ref="table" :columns="columns" :staticSearchParam="staticSearchParam" :api="tableApi" />
			</div>
		</mssCard>
	</div>
</template>

<script>
import { 
	getMaterialHistoryService,
  exportPageMaterialHis
} from '@/api/staging_point/ledger_management'
import { commonDown } from "@/utils/btn";
export default {
	name: 'material_history_detail',
	data() {
		return {
			tableName: this.$route.meta.title,
			staticSearchParam: {
        storageTempId: this.$route.query.storageTempId,
        materialName: this.$route.query.materialName,
				materialCode: this.$route.query.materialCode
      },
			tableApi: getMaterialHistoryService,
      searchConfig: [{
        type: 'input',
        fieldName: 'materialName',
        label: '物料名称',
      },
      {
        type: 'input',
        fieldName: 'materialCode',
        label: '物料编码',
      }],
      loadParams: {},
			sheetTypeArr:[]
		}
	},
	computed: {
		columns: {
			get() {
				return  [
					{
						prop: "materialName",
						label: "物料名称",
						minWidth: 300,
						tooltip: true
					},{
						prop: "materialCode",
						label: "物料编码",
						minWidth: 160,
					},{
						prop: "unit",
						label: "单位",
						minWidth: 80
					},{
						prop: "unitPrice",
						label: "单价（元）"
					},{
						prop: "5",
						label: "历史操作信息",
						multilevelColumn: [
							{label:"类型",prop:"sheetType",minWidth:140,formatter: row=>{
								return (	
									this.sheetTypeArr.length && this.sheetTypeArr.map(item=>{
										if(item.value === row.sheetType){
											return (<span>{item.label}</span>)
										}
									})
								)
							}},
							{label:"数量",prop:"actualNum",minWidth:140},
							{label:"申请人",prop:"creatorName",minWidth:140},
							{label:"申请人所属公司",prop:"deptName",minWidth:140},
							{label:"完成时间",prop:"finishDate",minWidth:160}
						]
					},{
						prop: "historyNum",
						label: "当前库存量",
						minWidth:100
					},{
						prop: "totalAmount",
						label: "当前库存金额（元）",
						minWidth:140
					}
				]
			}
		}

	},
	async created(){
		this.sheetTypeArr =  await this.$dictOptions({ parentValue: '001001004', appCode: '001001' })
	},
	mounted(){
    this.$set(this.$refs.SearchForm.searchForm, 'materialName', this.$route.query.materialName)
		this.$set(this.$refs.SearchForm.searchForm, 'materialCode', this.$route.query.materialCode)
  },
	methods: {
		search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData(form)
    },
		exportMethod(){
			this.staticSearchParam.materialName = encodeURIComponent(this.staticSearchParam.materialName)
			commonDown({ ...this.staticSearchParam,  }, exportPageMaterialHis);
		},
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/staging_point/ledger_management_list'
      })
		},
	}
}
</script>
