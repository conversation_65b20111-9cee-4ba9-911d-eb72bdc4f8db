<template>
  <div class="wholeList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          border
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { findPageService } from '@/api/staging_point/inventory_management_api.js'
export default {
  name: 'wholeList',
  data() {
    return {
      searchConfig: [
        {
          label: '暂存点名称',
          type: 'input',
          fieldName: 'storagePointName'
        },
        {
          label: '暂存点编号',
          type: 'input',
          fieldName: 'storagePointCode'
        }
      ],
      api: findPageService,
      staticSearchParam: {
        entityStatus: ''
      },
      columns: [
        {
          label: '任务申请单编号',
          prop: 'taskListNum',
          tooltip:true,
        },
        {
          label: '任务申请单名称',
          prop: 'taskListName',
          tooltip:true,
        },
        {
          label: '地市',
          prop: 'cityName',
          tooltip:true,
          width:80,
        },
        {
          label: '区县',
          prop: 'countyName',
          tooltip:true,
          width:160,
        },
        {
          label: '暂存点名称',
          prop: 'storagePointName',
          tooltip:true,
        },
        {
          label: '暂存点编号',
          prop: 'storagePointCode',
          tooltip:true,
        },
        {
          label: '暂存点地址',
          prop: 'storagePointAddress',
          tooltip:true,
        },
        {
          prop: 'operate',
          label: '操作',
          width:80,
          formatter: (row) => {
            return (
              <span>
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row)
                  }}
                >
                查看
                </el-button>
              </span>
            )
          }
        }
      ]
    }
  },
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: '',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: '',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    action(row) {
      this.$router.push({
        path: '/staging_point/ledger_statistics_detail',
        query: {
          boId: row.id,
          operateType: 'details',
          fromUrl:'workOrder',
          pathlabel: encodeURIComponent(`暂存点盘点工单详情页`),
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>