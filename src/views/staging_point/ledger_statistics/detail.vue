<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-07 09:38:15
 * @Description: xxx暂存点任务详情
-->

<template>
  <div class="ledgerStatisticsDetail">
    <div class="operate-btn">
      <el-button v-if="boId && dealPage" type="primary" @click="submit">提交</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="暂存点基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="140px"
          disableForm
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionHistory" class="page-anchor-point"></div>
    <mssCard title="暂存点物资盘点信息">
      <div slot="headerBtn">
        <el-upload
          v-if="opreateFlag"
          class="upload-btn"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button v-if="opreateFlag" @click="exportTable">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          v-if="staticSearchParam.storageTempId"
          ref="inventoryTable"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>

    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment
      v-if="boId"
      :dealPage="dealPage"
      :boId="boId"
      :businessType="workflowCode"
      :nodeName="nodeName"
    >
      <div slot="filesBottom">
        <el-form
          :model="inventoryForm"
          ref="inventoryForm"
          label-width="100px"
          :rules="inventoryRules"
          @submit.native.prevent
        >
          <el-row>
            <el-col :span="10">
              <el-form-item class="conclusion" label="盘点结论：" prop="conclusion">
                <el-select :disabled="!opreateFlag" v-model="inventoryForm.conclusion">
                  <el-option
                    v-for="item in conclusionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </mssAttachment>

    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode" />
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :saveMainForm="opreateFlag"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <mssProgressDialog ref="progressDialog"></mssProgressDialog>
  </div>
</template>

<script>
import {
  getByIdService,
  exportPageService,
  importExcelService,
  savePageService,
  findInventoryMateriaPageService,
  beforeSubmitService
} from '@/api/staging_point/inventory_management_api.js'
import { getStorageDetailService } from '@/api/staging_point/ledger_statistics'
import { commonDown } from '@/utils/btn'
export default {
  name: 'ledgerStatisticsDetail',
  data() {
    return {
      boId: '',
      basicConfig: [],
      basicConfig_copy: [
        {
          label: '地市：',
          type: 'input',
          prop: 'cityName'
        },
        {
          label: '区县：',
          type: 'input',
          prop: 'countyName'
        },
        {
          label: '任务单名称：',
          type: 'input',
          prop: 'taskListName'
        },
        {
          label: '任务单编号：',
          type: 'input',
          prop: 'taskListNum'
        },
        {
          label: '暂存点名称：',
          type: 'input',
          prop: 'storagePointName'
        },
        {
          label: '暂存点编号：',
          type: 'input',
          prop: 'storagePointCode'
        },
        {
          label: '暂存点地址：',
          type: 'input',
          prop: 'storagePointAddress'
        },
        {
          label: '实物管理员姓名：',
          type: 'input',
          prop: 'physicalManagerName'
        },
        {
          label: '实物管理员电话：',
          type: 'input',
          prop: 'physicalManagerPhone'
        },
        {
          label: '账务管理员姓名：',
          type: 'input',
          prop: 'accountingManagerName'
        },
        {
          label: '账务管理员电话：',
          type: 'input',
          prop: 'accountingManagerPhone'
        },
        {
          label: '地市物资管理员姓名：',
          type: 'input',
          prop: 'citiesSuppliesManagerName'
        },
        {
          label: '地市物资管理员电话：',
          type: 'input',
          prop: 'citiesSuppliesManagerPhone'
        }
      ],
      inventoryForm: {
        conclusion: ''
      },
      inventoryRules: {
        conclusion: [
          { required: true, message: '请选择盘点结论', trigger: 'blur' }
        ]
      },
      basicForm: {},
      api: findInventoryMateriaPageService,
      labelPosition: 'left', // 查看页面放在左边
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '盘点<br>信息',
          id: 'sectionHistory'
        },
        {
          text: '附件<br>列表',
          id: 'sectionFile'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow'
        }
      ],
      staticSearchParam: {},
      workflowCode: 'Inventory',
      completeTaskUrl: '',
      returnAddress: '',
      operateType: '',
      nodeCode: '',
      nodeName: '',
      dealPage: true,
      storageId: '',
      sheetType: '',
      opreateFlag: true,
      fromUrl: '',
      submitFlag: true,
      conclusionOptions:[
        {
          value: '盘亏',
          label: '盘亏'
        },
        {
          value: '盘盈',
          label: '盘盈'
        },
        {
          value: '账实一致',
          label: '账实一致'
        }
      ]
    }
  },
  computed: {
    columns: {
      get() {
        return [
          { prop: 'materialName', label: '物料名称' },
          { prop: 'materialCode', label: '物料编码' },
          { prop: 'unit', label: '单位' },
          { prop: 'num', label: '当前库存量' },
          { prop: 'actStorageNumber', label: '实际盘点库存量' }
        ]
      }
    }
  },
  created() {
    this.operateType = this.$route.query.operateType
    this.boId = this.$route.query.boId
    this.staticSearchParam.boId = this.boId
    this.fromUrl = this.$route.query.fromUrl
    const _config = this.basicConfig_copy.slice()
    if (this.fromUrl != 'ledgerStatistics') {
      this.returnAddress = '/staging_point/inventory_management_list'
      this.basicConfig = _config
    } else {
      this.returnAddress = '/staging_point/ledger_statistics_list'
      const otherConfig = [
        {
          label: '业主姓名：',
          type: 'input',
          prop: 'ownerName'
        },
        {
          label: '业主联系电话：',
          type: 'input',
          prop: 'ownerPhone'
        }
      ]
      this.basicConfig = _config.concat(otherConfig)
    }
    this.getByIdService()
  },
  mounted() {
    if (this.operateType == 'edit') {
      this.$refs.workFlow.init()
    } else {
      this.dealPage = false
      this.opreateFlag = false
    }
  },
  methods: {
    save(cb) {
      this.$refs.inventoryForm.validateScroll((valid) => {
        if (valid) {
          let req = {
            ...this.$refs.basicForm.modelForm,
            conclusion: this.inventoryForm.conclusion
          }
          savePageService(req).then((res) => {
            if (res.code === '0000') {
              cb()
            }
          })
        }
      })
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.returnAddress
      })
    },
    getByIdService() {
      if (this.fromUrl != 'ledgerStatistics') {
        getByIdService(this.boId).then((res) => {
          if (res && res.code == '0000') {
            this.basicForm = res.data
            this.inventoryForm.conclusion = res.data.conclusion
            this.completeTaskUrl = res.data.completeTaskUrl
            this.storageId = res.data.storageId
            this.staticSearchParam.storageTempId = this.storageId
            this.sheetType = res.data.sheetType
          }
        })
      } else {
        getStorageDetailService(this.boId).then((res) => {
          if (res && res.code == '0000') {
            this.basicForm = res.data
            this.inventoryForm.conclusion = res.data.conclusion
            this.storageId = res.data.storageId
            this.staticSearchParam.storageTempId = this.storageId
            this.sheetType = res.data.sheetType
          }
        })
      }
    },
    importFile(params) {
      let param = new FormData()
      param.append('file', params.file)
      param.append('inventoryId', this.boId)
      param.append('storagePointId', this.storageId)
      this.$refs.progressDialog.open()
      importExcelService(param).then((res) => {
        if (res.code == '0000' && !res.data) {
          this.$message.success('导入成功')
          this.$refs.progressDialog.close()
          this.$refs.inventoryTable.page.current = 1
          this.$refs.inventoryTable.getTableData()
        } else if (res.data) {
          this.$refs.progressDialog.close()
          this.$message({
            showClose: true,
            message: res.data,
            type: 'warning'
          })
        }
      })
    },
    exportTable() {
      let req = {
        boId: this.boId,
        storageTempId: this.storageId,
        sheetType: this.sheetType
      }
      commonDown(req, exportPageService)
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.taskListName
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit() {
      let str = "";
      await this.$refs.inventoryForm.validateScroll((valid)=>{
        str += valid?'':'有必填信息未填写完整，请检查'
      })
      let res = await beforeSubmitService({ boId: this.boId });
      if (res.data && res.data.code != 0) {
        str += res.data.msg;
      }
      return str;
    },
    beforeNode() {
      // 设置默认处理人
      const fileNameArr = {
        Confirm: {
          userName: this.basicForm.citiesSuppliesManagerName,
          userId: this.basicForm.citiesSuppliesManager
        }
      }
      return fileNameArr
    },
    submit() {
      if (this.submitFlag) {
        // this.$refs.inventoryForm.validateScroll((valid) => {
        //   if (valid) {
            this.$refs.workFlow.opendialogInitNextPath()
        //   }
        // })
      } else {
        this.$message({
          showClose: true,
          message: '请先导入物资盘点信息',
          type: 'warning'
        })
      }
    },
    getNodeData(data) {
      this.nodeName = data.nodeName
      this.nodeCode = data.nodeCode
      this.opreateFlag = data.nodeCode == 'Handle'
    }
  }
}
</script>

<style lang="scss" scoped>
.ledgerStatisticsDetail {
  padding-right: 70px;

  .operate-btn {
    text-align: right;
    margin-bottom: 10px;
  }
  ::v-deep .el-form-item__label{
    height: 26px;
    line-height: 26px;
  }
  ::v-deep .conclusion{
    .el-form-item__content{
      line-height: normal;
    }
  }
  .upload-btn {
    display: inline-block;
    margin-right: 10px;
  }
}
</style>

