<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-06 15:50:21
 * @Description: 暂存点台账统计管理
-->
<template>
	<div class="monthlyscore">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="searchForm"
			@search="search"
			@reset="reset"
			@changeSelect="changeSelect"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					ref="table"
					border
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				/>
			</div>
		</mssCard>
	</div>
</template>

<script>
import { findPageService, downloadService } from "@/api/staging_point/ledger_statistics"
import { queryAreaListService } from "@/api/common_api"
import { commonDown } from "@/utils/btn";
export default {
	name: 'ledgerStatisticsList',
	data() {
		return {
			searchConfig: [
				{
					type: 'select',
					fieldName: 'city',
					label: '地市',
					options: []
				},
				{
					type: 'select',
					fieldName: 'county',
					label: '区县',
					options: []
				},
				{
					type: 'input',
					fieldName: 'storagePointName',
					label: '暂存点名称'
				},
				{
					type: 'input',
					fieldName: 'storagePointCode',
					label: '暂存点编号'
				},
				{
					type: 'date1',
					fieldName: 'entityCreateDate',
					dateType: 'month',
					label: '开始月份',
					format: 'yyyy-MM',
					valueFormat: 'yyyy-MM'
				},
				{
					type: 'date1',
					fieldName: 'finishDate',
					dateType: 'month',
					label: '结束月份',
					format: 'yyyy-MM',
					valueFormat: 'yyyy-MM'
				},
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: findPageService
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{prop: "years",label: "年",tooltip: true},
					{prop:"months",label: "月",tooltip: true},
					{prop: "cityName",label: "地市",tooltip: true},
					{prop: "countyName",label: "区县",tooltip: true},
					{prop: "storagePointName",label: "暂存点名称",minWidth: 140, tooltip: true,
						formatter: row => {
							return (
								<span class='table_btn' onClick={()=>{this.toDetail(row)}}>{row.storagePointName}</span>
							)
						}
					},
					{prop: "storagePointCode",label: "暂存点编号",minWidth: 140, tooltip: true},
					{prop: "storagePointAddress",label: "暂存点地址",minWidth: 160, tooltip: true},
					{prop: "status",label: "状态",tooltip: true},
				]
			}
		}

	},
	created(){
		this.getAreaList('-2', 0)
	},
	methods: {
		getAreaList(parentId, index) {
			queryAreaListService({ parentId: parentId, typeCode: 'area', filter: 'city' }).then(res => {
				if (res.code === '0000') {
				const list = []
				res.data.forEach(item => {
					list.push({ label: item.name, value: item.id })
				})
				this.$set(this.searchConfig[index], 'options', list)
				}
			})
    },
		changeSelect(name, val){
			if(name == 'city'){
				this.$set(this.$refs.searchForm.searchForm,'county','')
				this.getAreaList(val, 1)
			}
		},
		search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
		exportMethod(){
			commonDown({ ...this.staticSearchParam, exportType: 'exportPage' }, downloadService);
		},
		toDetail(row){
			this.$router.push({
          path: '/staging_point/ledger_statistics_detail',
          query: {
            boId: row.id,
						operateType: 'details',
						fromUrl:'ledgerStatistics'
          }
        })
		}
	}
}
</script>
