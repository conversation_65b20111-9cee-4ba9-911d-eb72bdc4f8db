<template>
  <div class="addList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable ref="table" :api="api" :columns="columns" border :staticSearchParam="staticSearchParam"></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { storageTempFindPage } from '@/api/staging_point/inbound_outbound_api.js'
export default {
  name: 'addList',
  data() {
    return {
      searchConfig: [
        {
          label: '暂存点名称',
          type: 'input',
          fieldName: 'storagePointName'
        },
        {
          label: '暂存点编号',
          type: 'input',
          fieldName: 'storagePointCode'
        }
      ],
      api: storageTempFindPage,
      staticSearchParam:{},
      columns: [
        {
          label: '地市',
          prop: 'cityName',
          tooltip:true,
          width:80,
        },
        {
          label: '区县',
          prop: 'countyName',
          tooltip:true,
          width:160,
        },
        {
          label: '暂存点名称',
          prop: 'storagePointName',
          tooltip:true,
        },
        {
          label: '暂存点编号',
          prop: 'storagePointCode',
          tooltip:true,
        },
        {
          label: '暂存点地址',
          prop: 'storagePointAddress',
          tooltip:true,
        },
        {
          prop: 'operate',
          label: '操作',
          width:120,
          formatter: (row) => {
            return (
              <span>
                <el-button type="text" onClick={() => {this.action('inbound',row)}}>入库</el-button>
                <el-button type="text" onClick={() => {this.action('outbound',row)}}>出库</el-button>
                <el-button type="text" onClick={() => {this.action('return',row)}}>退库</el-button>
              </span>
            )
          }
        }
      ],
      sheetTypeCn: {
        'outbound': '出库',
        'inbound': '入库',
        'return': '退库'
      },
    }
  },
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    action(type,row){
      this.$router.push({
        path:`/staging_point/material_${type}_edit`,
        query:{
          storageTempId:row.id,
          operateType:'edit',
          pathlabel: encodeURIComponent(`物资${this.sheetTypeCn[type]}处理页`),
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>