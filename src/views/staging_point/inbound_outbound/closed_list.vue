<template>
  <div class="closedList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          border
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { outputInputFindPageService } from '@/api/staging_point/inbound_outbound_api.js'
export default {
  name: 'closedList',
  data() {
    return {
      searchConfig: [
        {
          label: '暂存点名称',
          type: 'input',
          fieldName: 'storagePointName'
        },
        {
          label: '暂存点编号',
          type: 'input',
          fieldName: 'storagePointCode'
        }
      ],
      api: outputInputFindPageService,
      staticSearchParam: {
        entityStatus: 'terminated'
      },
      columns: [
        {
          label: '地市',
          prop: 'cityName',
          tooltip:true,
          width:80,
        },
        {
          label: '区县',
          prop: 'countyName',
          tooltip:true,
          width:160,
        },
        {
          label: '任务名称',
          prop: 'taskApplyName',
          tooltip:true,
        },
        {
          label: '任务编号',
          prop: 'taskApplyCode',
          tooltip:true,
        },
        {
          label: '暂存点名称',
          prop: 'storagePointName',
          tooltip:true,
        },
        {
          label: '暂存点编号',
          prop: 'storagePointCode',
          tooltip:true,
        },
        {
          prop: 'operate',
          label: '操作',
          width:80,
          formatter: (row) => {
            return (
              <span>
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row)
                  }}
                >
                查看
                </el-button>
              </span>
            )
          }
        }
      ],
      sheetType: {
        '001001004001': 'outbound',
        '001001004002': 'inbound',
        '001001004003': 'return'
      },
      sheetTypeCn: {
        '001001004001': '出库',
        '001001004002': '入库',
        '001001004003': '退库'
      },
    }
  },
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'terminated',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'terminated',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    action(row){
      this.$router.push({
        path:`/staging_point/material_${this.sheetType[row.sheetType]}_edit`,
        query:{
          boId:row.id,
          operateType:'details',
          storageTempId:row.storageId,
          pathlabel: encodeURIComponent(`物资${this.sheetTypeCn[row.sheetType]}详情页`),
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>