<template>
  <div class="inbound">
    <div class="operate-btn">
      <el-button v-if="boId && dealPage" type="primary" @click="submit">提交</el-button>
      <el-button v-if="!boId" @click="save">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="入库基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disable-form="disableBasicForm"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionInbound" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.outputInputId" title="入库申请物资信息">
      <div slot="headerBtn">
        <el-upload
          v-if="applyBtnFlag"
          class="upload-btn"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="(params)=>{importFile(params,'apply')}"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button v-if="(applyFlag || applyBtnFlag) && status !='draft'" @click="exportDetailTable">导出</el-button>
        <el-button v-if="applyBtnFlag" @click="exportInboundTable(false)">入库清单模板下载</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="applyInboundTable"
          class="inboundTable"
          :columns="inboundColumns"
          :api="inboundApi"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
    <div id="sectionInbound" class="page-anchor-point"></div>
    <mssCard v-if="applyFlag" title="入库操作物资信息">
      <div slot="headerBtn">
        <el-upload
          v-if="operateFlag"
          class="upload-btn"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="(params)=>{importFile(params,'operate')}"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button v-if="operateFlag && rejectStatuc =='1'" @click="exportPageDownFn">导出</el-button>
        <!-- <el-button @click="exportInboundTable(true)">导入模板下载</el-button> -->
      </div>
      <div slot="content">
        <mssTable
          ref="operateInboundTable"
          class="outboundTable"
          :columns="operateColumns"
          :api="inboundApi"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :boId="boId" :workflowCode="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      :saveMainForm="!disableBasicForm || saveBsasicForm"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <mssProgressDialog ref="progressDialog"></mssProgressDialog>
  </div>
</template>

<script>
import {
  findDetailPageService,
  addPageService,
  saveService,
  putService,
  detailExportPage,
  outputImportExcelService,
  getDetailsById,
  exportDetailService,
  detailActualExcelService,
  beforeSubmitService,
  exportPageDownService
} from '@/api/staging_point/inbound_outbound_api'
import { commonDown } from '@/utils/btn'
export default {
  name: 'materialInboundEdit',
  data() {
    return {
      labelPosition: 'top',
      basicForm: {},
      inboundColumns: [
        {
          prop: 'outputOrderCode',
          label: '供应链出库通知单号',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'receiveApplyCode',
          label: '供应链领用申请单号',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectCode',
          label: '项目编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectName',
          label: '项目名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskCode',
          label: '任务编号',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskName',
          label: '任务名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialCode',
          label: '物料编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialName',
          label: '物料名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'unit',
          label: '单位',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'unitPrice',
          label: '单价',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'num',
          label: '数量',
          align: 'center',
          tooltip: true
        }
      ],
      operateColumns: [
        {
          prop: 'outputOrderCode',
          label: '供应链出库通知单号',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'receiveApplyCode',
          label: '供应链领用申请单号',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectCode',
          label: '项目编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectName',
          label: '项目名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskCode',
          label: '任务编号',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskName',
          label: '任务名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialCode',
          label: '物料编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialName',
          label: '物料名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'unit',
          label: '单位',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'unitPrice',
          label: '单价',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'actualNum',
          label: '实际入库数量',
          align: 'center',
          tooltip: true
        }
      ],
      inboundApi: findDetailPageService,
      boId: '',
      storageTempId: '',
      staticSearchParam: {
        outputInputId: ''
      },
      workflowCode: 'OutputInput',
      nodeName: '草稿',
      nodeCode: '',
      operateType: '',
      disableBasicForm: true,
      saveBsasicForm: false,
      dealPage: true,
      status:"",
      rejectStatuc:'0',
      completeTaskUrl: '', //流程处理地址
      returnAddress: '/staging_point/inbound_outbound_list' //流程提交成功返回路由地址
    }
  },
  computed: {
    pageAnchorConfig() {
      let ary = []
      if (this.boId) {
        ary = [
          {
            text: '基本<br>信息',
            id: 'sectionBasic'
          },
          {
            text: '入库<br>信息',
            id: 'sectionInbound'
          },
          {
            text: '流程<br>记录',
            id: 'sectionWorkFlow'
          }
        ]
      } else {
        ary = [
          {
            text: '基本<br>信息',
            id: 'sectionBasic'
          }
        ]
      }
      return ary
    },
    basicConfig() {
      return [
        {
          label: '暂存点名称',
          type: 'input',
          prop: 'storagePointName',
          disabled: true
        },
        {
          label: '暂存点编号',
          type: 'input',
          prop: 'storagePointCode',
          disabled: true
        },

        {
          label: '任务申请单名称',
          type: 'input',
          prop: 'taskApplyName',
          disabled: true
        },
        {
          label: '任务申请单编号',
          type: 'input',
          prop: 'taskApplyCode',
          disabled: true
        },
        {
          label: '入库申请人',
          type: 'input',
          prop: 'creatorName',
          disabled: true
        },
        {
          label: '创建时间',
          type: 'input',
          prop: 'createDate',
          disabled: true
        },
        {
          label: '入库操作人',
          type: 'input',
          prop: 'operatorIdName',
          disabled: true,
          hide: !this.applyFlag
        },
        {
          label: '入库操作时间',
          type: 'input',
          prop: 'operateDate',
          disabled: true,
          hide: !this.applyFlag
        },
        {
          label: '原因说明',
          type: 'input',
          prop: 'reason'
        }
      ]
    },
    applyFlag() {
      return (
        this.nodeCode == 'Initiate' ||
        this.nodeCode == 'Reapproval' ||
        this.operateType == 'details'
      )
    },
    applyBtnFlag() {
      return this.nodeCode == 'Draft'
    },
    operateFlag() {
      return this.nodeCode == 'Initiate'
    }
  },
  created() {
    this.storageTempId = this.$route.query.storageTempId
    this.operateType = this.$route.query.operateType
    if(this.operateType == 'details'){
      this.basicConfig.forEach(item=>{
        if(!item.label.includes('：')){
          item.label = item.label + "："
        }
      })
    }
    this.boId = this.$route.query.boId
    this.staticSearchParam.outputInputId = this.boId
    if (!this.boId) {
      let req = {
        id: this.storageTempId,
        type: '************'
      }
      addPageService(req).then((res) => {
        if (res && res.code == '0000') {
          this.basicForm = res.data
        }
      })
    } else {
      getDetailsById(this.boId).then((res) => {
        if (res && res.code == '0000') {
          this.basicForm = res.data
          this.rejectStatuc = res.data.rejectStatuc
          this.completeTaskUrl = res.data.completeTaskUrl
        }
      })
    }
  },
  mounted() {
    if (this.operateType == 'edit') {
      this.$refs.workFlow.init()
    } else {
      this.labelPosition = 'left'
      this.dealPage = false
    }
  },
  methods: {
    save(cb) {
      let req = {
        id: this.boId,
        sheetType: '************',
        storageId: this.storageTempId,
        ...this.$refs.basicForm.modelForm
      }
      if (this.boId) {
        putService(req).then((res) => {
          if (res.code === '0000') {
            if (cb) {
              cb()
            } else {
              this.$message.success('保存成功')
            }
          }
        })
      } else {
        saveService(req).then((res) => {
          if (res.code === '0000') {
            this.basicForm = res.data
            this.completeTaskUrl = res.data.completeTaskUrl
            this.boId = res.data.id
            this.staticSearchParam.outputInputId = this.boId
            this.$closeTagView({
              close: this.$route.fullPath,
              to: `${this.$route.fullPath}&boId=${this.boId}`
            })
            this.$message({
              type: 'success',
              message: '保存成功！'
            })
          }
        })
      }
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/staging_point/inbound_outbound_list'
      })
    },
    //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      let name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.taskApplyName
      }
      workFlowPrams.push(name)
      const sheetType = {
        code: 'sheetType',
        value: '************'
      }
      workFlowPrams.push(sheetType)
      return workFlowPrams
    },
    beforeNode() {
      let fileNameArr = {
        Approve: {
          userName: this.basicForm.accountingManagerName,
          userId: this.basicForm.accountingManager
        },
        Reapproval: {
          userName: this.basicForm.accountingManagerName,
          userId: this.basicForm.accountingManager
        },
        Confirm: {
          userName: this.basicForm.physicalManagerName,
          userId: this.basicForm.physicalManager
        },
        Initiate: {
          userName: this.basicForm.physicalManagerName,
          userId: this.basicForm.physicalManager
        }
      }
      // 设置默认处理人
      return fileNameArr
    },
    async beforeSubmit() {
      let str = ''
      let res = await beforeSubmitService({
        outputInputId: this.boId,
        sheetType: '************',
        storageTempId: this.storageTempId,
        nodeCode:this.nodeCode
      })
      if (res.data && res.data.code != 0) {
        str = res.data.msg
      }
      return str
    },
    // 提交--先保存后提交
    submit() {
      this.$refs.workFlow.opendialogInitNextPath()
    },
    // 导出出库清单模版
    exportInboundTable(flag) {
      let req = {
        sheetType: '************'
      }
      if (flag) {
        req.nodeCode = this.nodeCode
      }
      commonDown(req, detailExportPage)
    },
    exportDetailTable() {
      let req = {
        outputInputId: this.boId
      }
      commonDown(req, exportDetailService)
    },
    exportPageDownFn() {
      let req = {
        outputInputId: this.boId
      }
      commonDown(req, exportPageDownService)
    },
    //导入
    importFile(params, type) {
      let api, table
      if (type == 'apply') {
        api = outputImportExcelService
        table = 'applyInboundTable'
      } else {
        api = detailActualExcelService
        table = 'operateInboundTable'
      }
      let param = new FormData()
      param.append('file', params.file)
      param.append('outputInputId', this.boId)
      param.append('storageTempId', this.storageTempId)
      param.append('sheetType', '************')
      this.$refs.progressDialog.open()
      api(param).then((res) => {
        if (res.code == '0000' && !res.data) {
          this.$message.success('导入成功')
          this.$refs.progressDialog.close()
          this.$refs[table].page.current = 1
          this.$refs[table].getTableData()
        } else if (res.data) {
          this.$refs.progressDialog.close()
          this.$message({
            showClose: true,
            message: res.data,
            type: 'warning'
          })
        }
      })
    },
    getNodeData(data) {
      this.nodeName = data.nodeName
      this.nodeCode = data.nodeCode
      this.status = data.status
      this.disableBasicForm = data.nodeCode != 'Draft'
      this.saveBsasicForm = data.nodeCode == 'Initiate'
    }
  }
}
</script>

<style lang="scss" scoped>
.inbound {
  .upload-btn {
    display: inline-block;
    margin-right: 10px;
  }
}
</style>
