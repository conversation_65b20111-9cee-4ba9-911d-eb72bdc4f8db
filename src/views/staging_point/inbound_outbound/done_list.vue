<template>
  <div class="doneList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportPageFn">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          border
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  outputInputFindPageService,
  exportPageService
} from '@/api/staging_point/inbound_outbound_api.js'
import { queryAreaListService } from '@/api/common_api.js'
import { commonDown } from '@/utils/btn'
export default {
  name: 'doneList',
  data() {
    return {
      searchConfig: [
        {
          label: '暂存点名称',
          type: 'input',
          fieldName: 'storagePointName'
        },
        {
          label: '暂存点编号',
          type: 'input',
          fieldName: 'storagePointCode'
        },
        {
          label: '地市',
          type: 'select',
          fieldName: 'city',
          options: []
        },
        {
          label: '完成时间',
          type: 'date2',
          fieldName: 'endDateStart',
          fieldName2: 'endDateEnd',
          span: 12
        }
      ],
      api: outputInputFindPageService,
      staticSearchParam: {
        entityStatus: 'completed'
      },
      columns: [
        {
          label: '地市',
          prop: 'cityName',
          tooltip: true,
          width: 80
        },
        {
          label: '区县',
          prop: 'countyName',
          tooltip: true,
          width: 160
        },
        {
          label: '任务名称',
          prop: 'taskApplyName',
          tooltip: true
        },
        {
          label: '任务编号',
          prop: 'taskApplyCode',
          tooltip: true
        },
        {
          label: '暂存点名称',
          prop: 'storagePointName',
          tooltip: true
        },
        {
          label: '暂存点编号',
          prop: 'storagePointCode',
          tooltip: true
        },
        {
          prop: 'operate',
          label: '操作',
          width: 80,
          formatter: (row) => {
            return (
              <span>
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row)
                  }}
                >
                  查看
                </el-button>
              </span>
            )
          }
        }
      ],
      sheetType: {
        '001001004001': 'outbound',
        '001001004002': 'inbound',
        '001001004003': 'return'
      },
      sheetTypeCn: {
        '001001004001': '出库',
        '001001004002': '入库',
        '001001004003': '退库'
      },
      firstDeptId: ''
    }
  },
  created() {
    this.firstDeptId = sessionStorage.getItem('firstDeptId')
    if (this.firstDeptId != '1') {
      this.$set(this.searchConfig[2], 'value', this.firstDeptId)
      this.$set(this.searchConfig[2], 'disabled', true)
      this.staticSearchParam.city = this.firstDeptId
    }
    this.getAreaList()
  },
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'completed',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'completed',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then((res) => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach((item) => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.searchConfig[2], 'options', list)
          if (this.firstDeptId != '1'){
            this.$refs.SearchForm.searchForm.city = this.firstDeptId
          }
        }
      })
    },
    exportPageFn() {
      commonDown(this.staticSearchParam, exportPageService)
    },
    action(row) {
      this.$router.push({
        path: `/staging_point/material_${this.sheetType[row.sheetType]}_edit`,
        query: {
          boId: row.id,
          operateType: 'details',
          storageTempId: row.storageId,
          pathlabel: encodeURIComponent(
            `物资${this.sheetTypeCn[row.sheetType]}详情页`
          )
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>