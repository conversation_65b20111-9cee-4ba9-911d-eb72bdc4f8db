/**
* @author: ty
* @date: 2023-07-11
* @description: 施工组织设计
*/
<template>
<div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
      :labelWidth="'150px'"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {findPageService} from "@/api/engineering_management/constructionOrgDesign"
import { exportExcelService } from '@/api/engineering_management/projectMessage'
import { commonDown } from "@/utils/btn";
export default {
  name: 'ConstructionOrgDesignFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '施工组织设计工单号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '施工组织设计工单名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '施工组织设计单号',
          prop: 'code',
          minWidth: 160,
          tooltip: true
        },
        {
          label: '施工组织设计名称',
          prop: 'name',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true
        },
        {
          label: '施工单位',
          prop: 'constructionOrgName',
          tooltip: true
        },
        {
          label: '建设单位',
          prop: 'buildOrgName',
          tooltip: true
        },
        {
          label: '复勘时间',
          prop: 'resurveyDate',
          tooltip: true
        },
        {
          label: '复勘结果',
          prop: 'resurveyResult',
          tooltip: true
        },
        {
          label: '安全生产费是否已落实',
          prop: 'safetyCostFlag',
          width: 150,
          tooltip: true,
          formatter:(row)=>{
            return row.safetyCostFlag ? '是' : '否'
          }
        },
        {
          label: '创建人',
          prop: 'creatorName',
          tooltip: true
        },
        {
          label: '创建日期',
          prop: 'createDate',
          tooltip: true,
          formatter: (row)=>{
            return this.$moment(row.createDate).format('yyyy-MM-DD')
          }
        },
        {
          label: '单据状态',
          prop: 'status',
          width: 80,
          tooltip: true
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          width: 80,
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      this.$set(this.searchConfig[4], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 导出
    exportHandle(){
      commonDown({type: '施工组织设计', ...this.staticSearchParam }, exportExcelService);
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({id}) {
      this.$router.push({
        path: '/construction_org_design/view_form',
        query: {
          id
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    }
  }
}
</script>

<style scoped>

</style>
