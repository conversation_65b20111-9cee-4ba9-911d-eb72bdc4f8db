/**
* @author: ty
* @date: 2023-07-11
* @description:
*/
<template>
  <div class="page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :deal-page="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import { detailPageService } from "@/api/engineering_management/constructionOrgDesign"
export default {
  name: 'ConstructionOrgDesignViewForm',
  data() {
    return {
      pageLoading: false,
      id: this.$route.query.id,
      boId: this.$route.query.boId,
      businessType: '',
      basicConfig: [
        {
          label: '施工组织设计单号：',
          prop: 'code',
          span: 12,
          type: 'input'
        },
        {
          label: '施工组织设计名称：',
          prop: 'name',
          span: 12,
          type: 'input'
        },
        {
          label: '项目编码：',
          prop: 'projectCode',
          span: 12,
          type: 'input'
        },
        {
          label: '项目名称：',
          prop: 'projectName',
          span: 12,
          type: 'input'
        },
        {
          label: '施工单位：',
          prop: 'constructionOrgName',
          span: 12,
          type: 'input'
        },
        {
          label: '建设单位：',
          prop: 'buildOrgName',
          span: 12,
          type: 'input'
        },
        {
          label: '复勘结果：',
          prop: 'resurveyResult',
          span: 12,
          type: 'input'
        },
        {
          label: '复勘时间：',
          prop: 'resurveyDate',
          span: 12,
          type: 'input'
        },
        {
          label: '安全生产费是否落实：',
          prop: 'safetyCostFlag',
          span: 24,
          type: 'input'
        },
        {
          label: '创建人：',
          prop: 'creatorName',
          span: 12,
          type: 'input'
        },
        {
          label: '创建日期：',
          prop: 'createDate',
          span: 12,
          type: 'input'
        },
        {
          label: '备注：',
          prop: 'description',
          span: 12,
          type: 'input'
        }
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  created() {
    this.getBasicInfo()
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/construction_org_design/find_page'
      })
    },
    getBasicInfo(){
      detailPageService(this.$route.query.id || '').then(res=>{
        this.basicForm = Object.assign({}, this.basicForm, res.data)
        if(typeof this.basicForm.safetyCostFlag === 'boolean'){
          this.basicForm.safetyCostFlag = this.basicForm.safetyCostFlag ? '是' : '否'
        }
        if(this.basicForm.createDate){
          this.basicForm.createDate = this.$moment(this.basicForm.createDate).format('yyyy-MM-DD')
        }
      })
    },
  }
}
</script>

<style scoped>

</style>
