/**
* @author: ty
* @date: 2023-07-06
* @description: 开工报告根据id查看工单
*/
<template>
  <div class="projectStartViewForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionProjctBasicInfo" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :dealPage="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
  getDetailService
} from '@/api/engineering_management/projectStart.js'
export default {
  name: 'ProjectStartViewForm',
  data() {
    return {
      pageLoading: false,
      boId: '',
      businessType: '',
      tableApi: getDetailService,
      columns: [
        {
          label: '任务编码',
          prop: 'taskCode',
          tooltip: true,
          minWidth: 200
        },
        {
          label: '任务名称',
          prop: 'taskName',
          tooltip: true,
          minWidth: 200
        },
        {
          label: '任务专业类型',
          prop: 'taskSpecType',
          tooltip: true,
          minWidth: 200
        },
        {
          label: '任务所属区域',
          prop: 'taskArea',
          tooltip: true,
          minWidth: 200
        },{
          label: '任务经理',
          prop: 'taskManager',
          tooltip: true,
          width: 100
        },{
          label: '计划开工日期',
          prop: 'startDate',
          width: 100,
          formatter: (row)=>{
            if(row.startDate){
              return this.$moment(row.startDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '计划完工日期',
          prop: 'endDate',
          width: 100,
          formatter: (row)=>{
            if(row.endDate){
              return this.$moment(row.endDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '安全技术交底日期',
          prop: 'safetyDisclosureDate',
          tooltip: true,
          width: 140,
          formatter: (row)=>{
            if(row.safetyDisclosureDate){
              return this.$moment(row.safetyDisclosureDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },{
          label: '施工单位',
          prop: 'constructionOrgName',
          minWidth: 200
        },{
          label: '施工单位负责人',
          prop: 'constructionManagerName',
          width: 110
        },
        {
          label: '监理单位',
          prop: 'supervisionOrgName',
          minWidth: 200
        },{
          label: '监理单位负责人',
          prop: 'supervisionManagerName',
          width: 110
        }
      ],
      tableQueryParams: {},
      tableVisible: false,
      tableParams: {},
      basicConfig: [
        {
          label: '开工报告单号：',
          type: 'input',
          prop: 'code',
          span: 12
        },
        {
          label: '开工报告单名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
        },
        {
          label: '创建日期：',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'createDate'
        },
        {
          label: '主要工作内容：',
          type: 'input',
          prop: 'content',
          span: 24
        },
        {
          label: '工程准备情况或存在的主要问题：',
          type: 'input',
          prop: 'question',
          span: 24
        },
        {
          label: '提前或推迟开工原因：',
          type: 'input',
          prop: 'reason',
          span: 24
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionProjctBasicInfo'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || ''
    this.boId = urlQuery.boId || ''
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getDataById()
    this.pageLoading = false
  },
  methods: {
    getDataById() {
      return new Promise(resolve => {
        getByIdService(this.id)
          .then(res => {
            if(res && res.code === '0000'){
              this.basicForm = Object.assign({}, this.basicForm, res.data)
            }
          }).finally(_=>{
          resolve()
        })
      })
    },
    // 返回
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/project_start/find_page'
      })
    }
  }
}
</script>
