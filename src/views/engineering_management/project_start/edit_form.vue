/**
* @author: ty
* @date: 2023-07-06
* @description: 施工派工根据id编辑或者是新增工单
*/
<template>
  <div class="QualityDeclarationEditForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button type="primary">提交审核</el-button>
      <el-button type="primary" @click="saveDraftHandle">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionProjctBasicInfo" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="columns"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && basicForm.businessType" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :deal-page="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  findPageService
} from '@/api/engineering_management/constructionDispatch.js'
export default {
  name: 'ProjectStartEditForm',
  data() {
    return {
      pageLoading: false,
      boId: '',
      businessType: '',
      tableApi: findPageService,
      columns: [
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 200
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 200
        },{
          label: '工程建设地点',
          prop: 'name',
          minWidth: 200
        },
      ],
      tableQueryParams: {},
      searchFieldList: [
        {
          label: '项目名称',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          fieldName: 'projectCode'
        },
        {
          label: '建设单位名称',
          fieldName: 'buildUnitName'
        }
      ],
      basicConfig: [
        {
          label: '申报方式',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '质监申报清单号',
          type: 'input',
          prop: 'code',
          span: 12
        },
        {
          label: '质监申报单名称',
          type: 'input',
          prop: 'projectName',
          span: 12,
        },
        {
          label: '质监申报单号',
          type: 'input',
          prop: 'projectName',
          span: 12,
        },
        {
          label: '质监编号',
          type: 'input',
          prop: 'projectCode',
          span: 12
        },
        {
          label: '申报开始时间',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'wbsTaskName',
          span: 12
        },
        {
          label: '申报结束时间',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'visaTypeName',
          span: 12
        },
        {
          label: '申报状态',
          type: 'select',
          options: [],
          prop: 'applicationMoney',
          span: 12
        },
        {
          label: '申报单位',
          type: 'creatorDept',
          prop: 'code',
          span: 12
        }, {
          label: '申报人',
          type: 'creatorName',
          prop: 'code',
          span: 12
        },
        {
          label: '申报时间',
          type: 'creatorDate',
          prop: 'code',
          span: 12
        },
        {
          label: '备注',
          type: 'input',
          prop: 'description',
          span: 12
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'top',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '项目<br>信息',
          id: 'sectionProjctBasicInfo'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId || null
  },
  methods: {
    saveDraftHandle(){
      // todo 提交前校验
      this.pageLoading = true
      saveService(this.$refs.basicForm.modelForm)
      .then(res => {
        this.goBack()
      })
      .finally(_=> {
        this.pageLoading = false
      })
    },
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/quality_declaration/find_page'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.QualityDeclarationEditForm{

}
</style>
