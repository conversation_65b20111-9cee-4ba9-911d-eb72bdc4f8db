/**
* @author: ty
* @date: 2023-07-07
* @description: 开工报告-列表查询
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          rowKey="$index"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService } from '@/api/engineering_management/projectStart.js'
import { commonDown } from '@/utils/btn'
import { exportExcelService } from '@/api/engineering_management/projectMessage'
export default {
  name: 'ProjectStartFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '开工报告单号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '开工报告名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '开工报告单号',
          prop: 'code',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '开工报告单名称',
          prop: 'name',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '创建人',
          prop: 'creatorName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '创建日期',
          prop: 'createDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.createDate){
              return this.$moment(row.createDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '项目管理颗粒度',
          prop: 'manageGranularity',
          minWidth: 120,
          tooltip: true
        },
        {
          label: '施工单位',
          prop: 'tdConstructionOrgName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '施工单位负责人',
          prop: 'tdConstructionManagerName',
          minWidth: 110,
          tooltip: true
        },
        {
          label: '监理单位',
          prop: 'tdSupervisionOrgName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '监理单位负责人',
          prop: 'tdSupervisionManagerName',
          minWidth: 110,
          tooltip: true
        },
        {
          label: '监理工程师',
          prop: 'tdSupervisionerName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '计划开工日期',
          prop: 'tdStartDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.tdStartDate){
              return this.$moment(row.tdStartDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '计划完工日期',
          prop: 'tdEndDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.tdEndDate){
              return this.$moment(row.tdEndDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '实际开工日期',
          prop: 'tdFactStartDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.tdFactStartDate){
              return this.$moment(row.tdFactStartDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '单据状态',
          prop: 'status',
          width: 80,
          tooltip: true
        },
        {
          label: '主要工作内容',
          prop: 'content',
          minWidth: 100,
          tooltip: true
        },{
          label: '工程准备情况或存在的主要问题',
          prop: 'question',
          minWidth: 220,
          tooltip: true
        },{
          label: '提前或推迟开工原因',
          prop: 'reason',
          minWidth: 160,
          tooltip: true
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 100,
          tooltip: true
        },{
          label: '任务专业类型',
          prop: 'taskSpecType',
          minWidth: 100,
          tooltip: true
        },{
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 100,
          tooltip: true
        },{
          label: '任务经理',
          prop: 'taskManager',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标6是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.searchConfig[6], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },

    // 导出
    exportHandle(){
      commonDown({type: '开工报告', ...this.staticSearchParam}, exportExcelService);
    },

    // 导出附件
    exportAttachmentHandle(){

    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({boId, id}) {
      this.$router.push({
        path: '/project_start/view_form',
        query: {
          boId,
          id
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    }
  }
}
</script>
