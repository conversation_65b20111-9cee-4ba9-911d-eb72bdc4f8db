/**
* @author: ty
* @date: 2023-07-06
* @description: 施工派工根据id查看工单
*/
<template>
  <div class="QualityDeclarationEditForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionBasic2" class="page-anchor-point"></div>
    <mssCard title="施工单位信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig2"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionProjctBasicInfo" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :dealPage="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
  getDetailService
} from '@/api/engineering_management/constructionDispatch.js'
export default {
  name: 'ConstructionDispatchViewForm',
  data() {
    return {
      pageLoading: false,
      boId: '',
      businessType: '',
      tableApi: getDetailService,
      columns: [
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 200
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 200
        },
        {
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 200
        },
        {
          label: '项目管理专业',
          prop: 'projectManageSpec',
          minWidth: 200
        },{
          label: '预估施工费（元）',
          prop: 'constructMoney',
          minWidth: 200
        },
        {
          label: '安全生产费（元）',
          prop: 'safetyMoney',
          minWidth: 200
        },
        {
          label: '安全生产费（元）（设计会审）',
          prop: 'safetyDesignMoney',
          minWidth: 200
        },
        {
          label: '要求受理时间',
          prop: 'requiredAcceptanceDate',
          minWidth: 200,
          formatter: (row)=>{
            if(row.requiredAcceptanceDate){
              return this.$moment(row.requiredAcceptanceDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '要求开工日期',
          prop: 'requiredStartDate',
          minWidth: 200
        },
        {
          label: '要求完工日期',
          prop: 'requiredCompletionDate',
          minWidth: 200,
          formatter: (row)=>{
            if(row.requiredCompletionDate){
              return this.$moment(row.requiredCompletionDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '施工单位属性',
          prop: 'constructionOrgType',
          minWidth: 200
        },
        {
          label: '是否最后一次施工派工',
          prop: 'lastFlag',
          minWidth: 200,
          formatter: (row)=>{
            return row.lastFlag? '是': '否'
          }
        },
      ],
      tableQueryParams: {},
      tableVisible: false,
      tableParams: {},
      basicConfig: [
        {
          label: '施工派工单号：',
          type: 'input',
          prop: 'code',
          span: 12,
        },
        {
          label: '施工派工单名称：',
          type: 'input',
          prop: 'name',
          span: 12,
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName'
        },
        {
          label: '立项批复金额(万元)：',
          type: 'input',
          prop: 'decisionMoney',
        },
        {
          label: '工程实施经理(主)：',
          type: 'input',
          prop: 'projectManagerName',
        },
        {
          label: '派工人员：',
          type: 'input',
          prop: 'dispatchUserName',
        },
        {
          label: '派工时间：',
          prop: 'dispatchDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          label: '派工描述：',
          type: 'input',
          prop: 'dispatchDesc',
        },
      ],
      // 施工单位信息
      basicConfig2: [
        {
          label: '施工单位：',
          type: 'input',
          prop: 'constructionOrgName',
        },
        {
          label: '份额产品名称：',
          type: 'input',
          prop: 'productName',
        },
        {
          label: '合同编码：',
          type: 'input',
          prop: 'contractCode',
        },
        {
          label: '合同名称：',
          type: 'input',
          prop: 'contractName',
        },
        {
          label: '合同行号：',
          type: 'input',
          prop: 'contractLineCode',
          minWidth: 200
        },
        {
          label: '不含税可用投资金额(元)：',
          type: 'input',
          prop: 'planInvest',
        },
        {
          label: '施工单位总负责人：',
          type: 'input',
          prop: 'constructionManagerName',
        },
        {
          label: '总预估施工费(元)：',
          type: 'input',
          prop: 'totalInvest',
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '单位<br>信息',
          id: 'sectionBasic2'
        },
        {
          text: '任务<br>列表',
          id: 'sectionProjctBasicInfo'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || ''
    this.boId = urlQuery.boId || ''
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getDataById()
    this.pageLoading = false
  },
  methods: {
    getDataById() {
      return new Promise(resolve => {
        getByIdService(this.id)
          .then(res => {
            if(res && res.code === '0000'){
              this.basicForm = Object.assign({}, this.basicForm, res.data)
            }
          }).finally(_=>{
          resolve()
        })
      })
    },
    // 返回
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/construction_dispatch/find_page'
      })
    }
  }
}
</script>
