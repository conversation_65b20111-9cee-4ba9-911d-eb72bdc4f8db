/**
* @author: ty
* @date: 2023-07-07
* @description: 施工派工-列表查询
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :selection="false"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService, delService } from '@/api/engineering_management/constructionDispatch.js'
import { commonDown, commonMultDel } from '@/utils/btn'
import { exportExcelService } from '@/api/engineering_management/projectMessage'
export default {
  name: 'ConstructionDispatchFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '施工派工单号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '施工派工名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '项目年份',
          prop: 'projectyear',
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '任务名称',
          prop: 'taskName',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '任务所属区域',
          tooltip: true,
          prop: 'taskArea',
          minWidth: 200,
        },
        {
          label: '项目管理专业',
          prop: 'projectManageSpec',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '任务经理',
          prop: 'taskManagerName',
          tooltip: true
        },
        {
          label: '派工单位类型',
          prop: 'deptType',
          width: 100,
          tooltip: true
        },
        {
          label: '派工人员',
          prop: 'dispatchUserName',
          tooltip: true
        },
        {
          label: '派工时间',
          prop: 'dispatchDate',
          tooltip: true,
          formatter: (row) => {
            if (row.dispatchDate) {
              return this.$moment(row.dispatchDate).format('yyyy-MM-DD')
            } else {
              return ''
            }
          }
        },
        {
          label: '派工单位名称',
          prop: 'constructionOrgName',
          width: 100,
          tooltip: true
        },
        {
          label: '派工单位名称(更新后)',
          prop: 'dispatchOrgNameNew',
          width: 150,
          tooltip: true
        },
        {
          label: '单据编号',
          prop: 'code',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '单据名称',
          prop: 'name',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '撤销状态',
          prop: 'revokeStatus',
          width: 80,
        },
        {
          label: '撤销时间',
          prop: 'revokeDate',
          tooltip: true,
          formatter: (row)=>{
            if(row.revokeDate){
              return this.$moment(row.revokeDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '审批环节',
          prop: 'nodeName',
          tooltip: true
        },
        {
          label: '单据状态',
          prop: 'status',
          width: 80,
          tooltip: true
        },
        {
          label: '要求受理时间',
          prop: 'requiredAcceptanceDate',
          width: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.requiredAcceptanceDate){
              return this.$moment(row.requiredAcceptanceDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '要求完工日期',
          prop: 'requiredCompletionDate',
          width: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.requiredCompletionDate){
              return this.$moment(row.requiredCompletionDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '实际受理时间',
          prop: 'actAcceptanceDate',
          width: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.actAcceptanceDate){
              return this.$moment(row.actAcceptanceDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '实际完工日期',
          prop: 'actualenddate',
          width: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.actualenddate){
              return this.$moment(row.actualenddate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '设计单位负责人',
          prop: 'desinLeaderName',
          minWidth: 110,
          tooltip: true
        },
        {
          label: '设计员',
          prop: 'desinUserName',
          width: 100,
          tooltip: true
        },
        {
          label: '施工单位负责人',
          prop: 'constructionManagerName',
          minWidth: 110,
          tooltip: true
        },
        {
          label: '施工专业负责人',
          prop: 'constructionCaptainName',
          minWidth: 110,
          tooltip: true
        },
        {
          label: '施工队长',
          prop: 'constructionWorkerName',
          width: 100,
          tooltip: true
        },
        {
          label: '监理单位负责人',
          prop: 'suplearder',
          minWidth: 110,
          tooltip: true
        },
        {
          label: '监理工程师',
          prop: 'supUserName',
          width: 100,
          tooltip: true
        },
        {
          label: '预估金额（元）',
          prop: 'totalInvest',
          minWidth: 110,
          tooltip: true
        },
        {
          label: '任务设计会审金额（元）',
          prop: 'designReplyMoney',
          minWidth: 160,
          tooltip: true
        },
        {
          label: '安全生产费（元）',
          prop: 'safetyMoney',
          minWidth: 120,
          tooltip: true
        },
        {
          label: '份额产品名称',
          prop: 'productName',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '不含税可用投资金额（元）',
          prop: 'planInvest',
          minWidth: 170,
          tooltip: true
        },
        {
          label: '合同编码',
          prop: 'contractCode',
          tooltip: true,
          minWidth: 200
        },
        {
          label: '合同名称',
          prop: 'contractName',
          tooltip: true,
          minWidth: 200
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          width: 80,
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          },
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标6是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.searchConfig[6], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 新增
    addHandle() {
      this.$router.push({
        path: '/construction_dispatch/edit_form'
      })
    },

    // 导出
    exportHandle(){
      commonDown({type: '施工派工', ...this.staticSearchParam}, exportExcelService);
    },

    // 导出附件
    exportAttachmentHandle(){

    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({boId, id}) {
      this.$router.push({
        path: '/construction_dispatch/view_form',
        query: {
          boId,
          id
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    }
  }
}
</script>
