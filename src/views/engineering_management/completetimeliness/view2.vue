/**
* @author: ty
* @date: 2023-07-26
* @description: 完工及时性-查看详情页面 对应/project-center-jx/completetimeliness/view/{id}
*/
<template>
  <div class="TransfertimelinessView page-anchor-parentpage" v-loading="pageLoading">
    <div style="margin-top: 30px;"></div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          label-width="240px"
          disable-form
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm" />
      </div>
    </mssCard>

    <div id="sectionTaskList" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

  </div>
</template>

<script>
import {
  viewByIdService,
  taskCutoverByIdService
} from "@/api/engineering_management/completetimeliness";

export default {
  name: "CompletetimelinessView2",
  data(){
    return {
      pageLoading: false,
      labelPosition: 'left', // 查看页面放在左边
      basicConfig: [
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          span: 24
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          span: 12
        },
        {
          label: '创建时间：',
          type: 'input',
          prop: 'modifyDate',
          span: 12
        },
        {
          label: '要求完工时间：',
          type: 'input',
          prop: 'expectedDate',
          span: 12
        },
        {
          label: '到期剩余天数：',
          type: 'input',
          prop: 'lastdays',
          span: 12
        }
      ],
      basicForm: {},
      tableApi: taskCutoverByIdService,
      tableVisible: false,
      tableParams: {},
      columns: [
        {
          label: '单据编号',
          prop: 'jobno',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '单据名称',
          prop: 'jobname',
          minWidth: 200,
          tooltip: true
        },
        // {因为接口没有传。而基本信息里又有项目名称和项目编码
        //   label: '项目编码',
        //   prop: 'projectcode',
        //   minWidth: 200,
        //   tooltip: true
        // },{
        //   label: '项目名称',
        //   prop: 'projectname',
        //   minWidth: 200,
        //   tooltip: true
        // },
        {
          label: '派工人员',
          prop: 'jobusername',
          minWidth: 200,
          tooltip: true
        },{
          label: '派工时间',
          prop: 'jobdate',
          minWidth: 200,
          tooltip: true
        },{
          label: '派工单位编码',
          prop: 'cooperationcompanycode',
          minWidth: 200,
          tooltip: true
        },{
          label: '派工单位名称',
          prop: 'cooperationcompanyname',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '任务名称',
          prop: 'taskname',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '任务编码',
          prop: 'taskcode',
          minWidth: 200,
          tooltip: true
        }, {
          label: '任务项目管理专业',
          prop: 'taskmajorname',
          minWidth: 200,
          tooltip: true
        },{
          label: '要求受理时间',
          prop: 'requesttime',
          minWidth: 200,
          tooltip: true
        },{
          label: '要求开工日期',
          prop: 'requeststartdate',
          minWidth: 200,
          tooltip: true
        },{
          label: '要求完工日期',
          prop: 'requestenddate',
          minWidth: 200,
          tooltip: true
        }
      ],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionTaskList'
        }
      ]
    }
  },
  async created(){
    this.id = this.$route.params.id
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getData()
    this.pageLoading = false
  },
  methods: {
    getData(){
      return new Promise((resolve, reject) => {
        viewByIdService(this.id)
          .then(res => {
            if (res.code === '0000') {
              this.basicForm = res.data
            }
          })
          .finally(_ => {
            resolve()
          })
      })
    }
  }
}
</script>

