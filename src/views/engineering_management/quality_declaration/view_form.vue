/**
* @author: ty
* @date: 2023-07-06
* @description: 质监申报根据id查看工单
*/
<template>
  <div class="QualityDeclarationEditForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionProjctBasicInfo" class="page-anchor-point"></div>
    <mssCard title="项目基本信息">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :dealPage="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
  getDetailService
} from '@/api/engineering_management/qualityDeclaration.js'
export default {
  name: 'QualityDeclarationViewForm',
  data() {
    return {
      pageLoading: false,
      id: '',
      businessType: '',
      tableApi: getDetailService,
      columns: [
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 200
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 200
        },
        {
          label: '工程建设地点',
          prop: 'constructionLocation',
          minWidth: 200
        }
      ],
      tableVisible: false,
      tableParams: {},
      basicConfig: [
        {
          label: '申报方式：',
          type: 'input',
          prop: 'applyType',
          span: 24
        },
        {
          label: '质监申报单号：',
          type: 'input',
          mode: 'textarea',
          prop: 'code',
          span: 12
        },
        {
          label: '质监申报单名称：',
          type: 'input',
          mode: 'textarea',
          prop: 'name',
          span: 12
        },
        {
          label: '申报人：',
          type: 'input',
          prop: 'creatorName',
          span: 12
        },
        {
          label: '申报开始时间：',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'startDate',
          span: 12
        },
        {
          label: '申报完成时间：',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'endDate',
          span: 12
        },
        {
          label: '申报状态：',
          type: 'input',
          prop: 'statusName',
          span: 12
        },
        {
          label: '质监编号：',
          type: 'input',
          prop: 'qualityCode',
          span: 12
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '项目<br>信息',
          id: 'sectionProjctBasicInfo'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || ''
    this.boId = urlQuery.boId || ''
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getDataById()
    this.pageLoading = false
  },
  methods: {
    getDataById() {
      return new Promise(resolve => {
        getByIdService(this.id)
          .then(res => {
            if (res && res.code === '0000'){
              this.basicForm = Object.assign({}, this.basicForm, res.data)
              if(this.basicForm.createDate){
                this.basicForm.createDate = this.$moment(this.basicForm.createDate).format('yyyy-MM-DD')
              }

              if(this.basicForm.startDate){
                this.basicForm.startDate = this.$moment(this.basicForm.startDate).format('yyyy-MM-DD')
              }

              if(this.basicForm.endDate){
                this.basicForm.endDate = this.$moment(this.basicForm.endDate).format('yyyy-MM-DD')
              }
            }
          }).finally(_=>{
            resolve()
        })
      })
    },
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/quality_declaration/find_page'
      })
    }
  }
}
</script>
