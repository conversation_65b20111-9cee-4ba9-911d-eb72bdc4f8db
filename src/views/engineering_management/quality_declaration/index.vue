/**
* @author: ty
* @date: 2023-07-07
* @description: 质监申报-列表查询
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :selection="false"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService, delService } from '@/api/engineering_management/qualityDeclaration.js'
import { commonDown, commonMultDel } from '@/utils/btn'
import { exportExcelService } from '@/api/engineering_management/projectMessage'
export default {
  name: 'QualityDeclarationFindPage',
  data() {
    const validateStartEndDate = (rule, value, callback) => {
      if(this.$refs.searchForm.searchForm.startDate && this.$refs.searchForm.searchForm.endDate){
        if(new Date(this.$refs.searchForm.searchForm.startDate).getTime() > new Date(this.$refs.searchForm.searchForm.endDate).getTime()){
          callback(new Error('开始日期需小于结束日期'))
        }else{
          callback()
        }
      }else{
        callback()
      }
    }
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '质监编号',
          type: 'input',
          fieldName: 'qualityCode'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        },
        {
          label: '申请开始-完成时间',
          type: 'date2',
          fieldName: 'startDate',
          fieldName2: 'endDate',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          span: 12,
          rules: [
            { validator: validateStartEndDate, trigger: ['blur', 'change'] },
          ]
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '质监申报申清单号',
          prop: 'pmsCode',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '质监申报单号',
          prop: 'code',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '质监申报名称',
          prop: 'name',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '质监编号',
          prop: 'qualityCode',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '项目管理专业',
          prop: 'projectManageSpec',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '建设单位',
          prop: 'buildOrgName',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '申报状态',
          prop: 'statusName',
          tooltip: true,
        },
        {
          label: '申报人',
          prop: 'creatorName',
          tooltip: true,
        },
        {
          label: '申报开始时间',
          prop: 'startDate',
          tooltip: true,
          minWidth: 200,
          formatter: (row)=>{
            if(row.startDate){
              return this.$moment(row.startDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '申报完成时间',
          prop: 'endDate',
          tooltip: true,
          minWidth: 200,
          formatter: (row)=>{
            if(row.endDate){
              return this.$moment(row.endDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '申报待办触发源',
          prop: 'declarationSource',
          width: 110,
          tooltip: true,
        },
        {
          label: '推送申报待办时间',
          prop: 'declarationPushTime',
          width: 120,
          tooltip: true,
          formatter: (row)=>{
            if(row.declarationPushTime){
              return this.$moment(row.declarationPushTime).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '推送申报待办账号',
          prop: 'declarationPushAccount',
          width: 120,
          tooltip: true,
        },
        {
          label: '申报待办处理时间',
          prop: 'declarationProcessTime',
          width: 120,
          tooltip: true,
          formatter: (row)=>{
            if(row.declarationProcessTime){
              return this.$moment(row.declarationProcessTime).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          width: 80,
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标3是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.searchConfig[3], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 新增
    addHandle() {
      this.$router.push({
        path: '/quality_declaration/edit_form'
      })
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({id, name = ''}){
      this.$router.push({
        path: '/quality_declaration/view_form',
        query: {
          id,
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    },
    // 导出
    exportHandle(){
      commonDown({type: '质监申报', ...this.staticSearchParam }, exportExcelService);
    },
  }
}
</script>
