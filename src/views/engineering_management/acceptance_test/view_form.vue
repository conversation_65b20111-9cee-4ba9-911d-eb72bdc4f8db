/**
* @author: ty
* @date: 2023-07-06
* @description: yansho根据id查看工单
*/
<template>
  <div class="AcceptanceTestViewForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionProjctBasicInfo" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :deal-page="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
  getDetailService
} from '@/api/engineering_management/acceptanceTest.js'
export default {
  name: 'AcceptanceTestViewForm',
  data() {
    return {
      pageLoading: false,
      id: '',
      boId: '',
      businessType: '',
      tableApi: getDetailService,
      columns: [
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 200
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 200
        },
        {
          label: '任务专业类型',
          prop: 'projectManageSpec',
          minWidth: 200
        },
        {
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 200
        },{
          label: '任务经理',
          prop: 'taskManager',
          minWidth: 200
        },
        {
          label: '施工单位',
          prop: 'constructionOrgName',
          minWidth: 200
        },{
          label: '监理单位',
          prop: 'supervisionOrgName',
          minWidth: 200
        },{
          label: '监理工程师',
          prop: 'supervisionUserName',
          minWidth: 200
        },
      ],
      tableQueryParams: {},
      tableVisible: false,
      tableParams: {},
      basicConfig: [
        {
          label: '验收测试单号：',
          type: 'input',
          prop: 'code',
          span: 12,
        },
        {
          label: '验收测试单名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          minWidth: 200
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          minWidth: 200
        },
        {
          label: '验收测试日期：',
          prop: 'testDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          label: '项目管理颗粒度：',
          type: 'input',
          prop: 'manageGranularity',
          minWidth: 200
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
          minWidth: 200
        },
        {
          label: '创建日期：',
          prop: 'createDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          label: '工程实施经理(主)：',
          type: 'input',
          prop: 'projectManagerName',
          minWidth: 200
        },
        {
          label: '验收测试说明：',
          type: 'input',
          prop: 'description',
          minWidth: 200,
          span: 24
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionProjctBasicInfo'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || ''
    this.boId = urlQuery.boId || ''
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getDataById()
    this.pageLoading = false
  },
  methods: {
    getDataById() {
      return new Promise(resolve => {
        getByIdService(this.id)
          .then(res => {
            if(res && res.code === '0000'){
              this.basicForm = Object.assign({}, this.basicForm, res.data)
            }
          }).finally(_=>{
          resolve()
        })
      })
    },
    // 返回
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/acceptance_test/find_page'
      })
    }
  }
}
</script>
