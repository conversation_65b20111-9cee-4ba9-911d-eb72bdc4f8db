/**
* @author: ty
* @date: 2023-07-07
* @description: 验收测试-列表查询
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService } from '@/api/engineering_management/acceptanceTest.js'
import { commonDown } from '@/utils/btn'
import { exportExcelService } from '@/api/engineering_management/projectMessage'
export default {
  name: 'AcceptanceTestFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '验收测试单号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '验收测试名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '验收测试单号',
          prop: 'code',
          minWidth: 200,
          tooltip: true,
        },
        {
          label: '验收测试单名称',
          prop: 'name',
          minWidth: 200,
          tooltip: true,
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
        },
        {
          label: '施工单位',
          prop: 'constructionOrgName',
          minWidth: 200,
          tooltip: true,
        },
        {
          label: '监理单位',
          prop: 'supervisionOrgName',
          minWidth: 200,
          tooltip: true,
        },
        {
          label: '验收测试日期',
          prop: 'testDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.testDate){
              return this.$moment(row.testDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '项目管理颗粒度',
          prop: 'manageGranularity',
          minWidth: 120,
          tooltip: true,
        },
        {
          label: '单据状态',
          prop: 'status',
          width: 80,
          tooltip: true
        },
        {
          label: '创建人',
          prop: 'creatorName',
          tooltip: true,
        },
        {
          label: '创建日期',
          prop: 'createDate',
          tooltip: true,
          formatter: (row)=>{
            if(row.createDate){
              return this.$moment(row.createDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '验收测试说明',
          prop: 'description',
          minWidth: 100,
          tooltip: true,
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标6是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.searchConfig[6], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },

    // 导出
    exportHandle(){
      commonDown({type: '验收测试', ...this.staticSearchParam }, exportExcelService);
    },

    // 导出附件
    exportAttachmentHandle(){

    },
    // 点击操作列的按钮，查看单子
    operateHandle({boId, id}) {
      this.$router.push({
        path: '/acceptance_test/view_form',
        query: {
          boId,
          id
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    }
  }
}
</script>
