/**
* @author: ty
* @date: 2023-07-04
* @description: 监理费确认根据boid编辑或者是新增工单
*/
<template>
  <div v-loading="pageLoading" class="ProjectTaskVisaEditForm page-anchor-parentpage">
    <div class="operate-btn">
      <el-button v-if="id" type="primary" @click="submit">提交审核</el-button>
      <el-button v-if="saveBtnVisible" type="primary" @click="save('byBtn')">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          :config="basicConfig"
          :label-position="labelPosition"
          :rules="basicRules"
          :form="basicForm"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionJieSuanShenJi" class="page-anchor-point"></div>
    <jieSuanShenJiTable
      v-if="jieSuanShenJiVisible"
      :id="id"
      ref="jieSuanShenJiTable"
      :bo-id="boId"
      :project-id="projectId"
      :disable-form="jieSuanShenJiDisabled">
    </jieSuanShenJiTable>

    <div id="sectionJiFeiBiaoZhun" class="page-anchor-point"></div>
    <jiFeiBiaoZhunTable
      v-if="jiFeiBiaoZhunVisible"
      :id="id"
      ref="jiFeiBiaoZhunTable"
      :bo-id="boId"
      :project-id="projectId"
      :disable-form="jiFeiBiaoZhunDisabled">
    </jiFeiBiaoZhunTable>

    <div id="sectionTongXinShenDing" class="page-anchor-point"></div>
    <tongXinShenDingJianLiFeiTable ref="tongXinShenDingJianLiFeiTable" v-if="tongXinShenDingJianLiFeiVisible" :id="id" :bo-id="boId" :project-id="projectId"></tongXinShenDingJianLiFeiTable>

    <div id="sectionTuJianShenDing" class="page-anchor-point"></div>
    <tuJianLeiShenDingJianLiFeiTable
      v-if="tujianVisible"
      :id="id"
      ref="tuJianLeiShenDingJianLiFeiTable"
      :bo-id="boId"
      :project-id="projectId"
      :disable-form="tuJianShenDingDisabled">
    </tuJianLeiShenDingJianLiFeiTable>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="boId"
      :deal-page="dealPage"
      :bo-id="boId"
      :business-type="workflowCode"
      :node-name="currentNodeName"
    ></mssAttachment>

    <div id="sectionWorkFlowHistory" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor ref="mssPageAnchor" :config="pageAnchorConfig"></mssPageAnchor>

    <!-- 选择项目的弹窗   -->
    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      dialog-width="1000px"
      row-key="projectId"
      :table-api="tableApi"
      :table-query-params="tableQueryParams"
      :search-fields="searchFieldList"
      :table-single-choice="true"
      :table-selected-data="tableSelectedData"
      :table-data-key="tableDataKey"
      :table-columns="tableColumns"
      @confirm="projectNameSearchConfirmHandle"
      @cancle="projectNameSearchCancleHandle"
    ></mssTableSearchDialog>

    <!--  流程组件  -->
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="saveMainForm"
      @getNodeData="getNodeData"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
      @getHideList="getHideList"
    ></mssWorkFlowHandel>
  </div>
</template>

<script>
import {
  initService,
  findProjectService,
  saveService,
  getSupervisorUserService,
  checkFeeService,
} from '@/api/engineering_management/supervisionFeeConfirm.js'
import jieSuanShenJiTable from './components/jie_suan_shen_ji_table'
import jiFeiBiaoZhunTable from './components/ji_fei_biao_zhun_table'
import tongXinShenDingJianLiFeiTable from './components/tong_xin_shen_ding_jian_li_fei_table'
import tuJianLeiShenDingJianLiFeiTable from './components/tu_jian_lei_shen_ding_jian_li_fei_table.vue'
export default {
  name: 'SupervisionFeeConfirmEditForm',
  components: {jieSuanShenJiTable, jiFeiBiaoZhunTable, tongXinShenDingJianLiFeiTable, tuJianLeiShenDingJianLiFeiTable},
  data() {
    return {
      pageLoading: false,
      currentNodeName: '', // 当前所属环节name
      currentNodeCode: '', // 当前所属环节code
      projectTypeName: '', // 项目类型名称 (不是土建就是通信)
      boId: '',
      projectId: '',
      jieSuanShenJiDisabled: true,// 结算审计表格是否禁用（禁用时不允许导入）
      jiFeiBiaoZhunDisabled: true,// 计费标准表格是否禁用（禁用时不允许导入）
      tuJianShenDingDisabled: true,
      dealPage: true,
      workflowCode: 'SupervisorFeeConfirm',
      completeTaskUrl: '',// todo
      returnAddress: '/supervision_fee_confirm/find_page', // 写死？还是说等接口传入？todo
      // 项目选择弹窗的相关配置信息
      tableApi: findProjectService,
      tableQueryParams: {},
      searchFieldList: [
        {
          label: '项目名称',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          fieldName: 'projectCode'
        },
        {
          label: '项目年份',
          fieldName: 'projectYear'
        },
        {
          label: '专业',
          fieldName: 'specName'
        },
      ],
      tableSelectedData: [], // 表格中已勾选的数据。数据格式与查询接口返回的数据格式一致
      tableDataKey: 'projectId', // 表格的查询接口返回的数据中表示唯一标识的字段
      tableColumns: [
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 200
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 100
        },
        {
          label: '专业',
          prop: 'specName',
          width: 100
        },
        {
          label: '年份',
          prop: 'projectYear',
          width: 60
        },
        {
          label: '建设部门',
          prop: 'orgDeptName',
          minWidth: 200
        },
        {
          label: '计划项目经理',
          prop: 'managerName',
          minWidth: 120
        },
        {
          label: '建设项目经理',
          prop: 'projectmanagerName',
          minWidth: 120
        }
      ],
      basicConfig: [
        {
          label: '监理费确认工单名称',
          type: 'input',
          prop: 'name',
          span: 24,
        },
        {
          label: '项目名称',
          type: 'input',
          prop: 'projectName',
          span: 12,
          eventListeners: {
            focus: this.openProjectSelectDialog// 打开选择项目的弹窗
          }
        },
        {
          label: '项目编码',
          type: 'input',
          prop: 'projectCode',
          span: 12,
        },
        {
          label: '所属专业',
          type: 'input',
          prop: 'specName',
        },
        {
          label: '建设单位',
          type: 'input',
          prop: 'buildOrgName',
          span: 12,
        },
        {
          label: '立项批复投资(元)',
          type: 'input',
          prop: 'projectReplyinvest',
          span: 12,
        },
        {
          label: '设计批复投资(元)',
          type: 'input',
          prop: 'designReplyInvest',
          span: 12,
        },
        // {
        //   label: '设计批复中设备费金额（万元）',
        //   type: 'input',
        //   prop: 'equipAmount',
        //   span: 12,
        // },
        {
          label: '拟稿单位',
          type: 'input',
          prop: 'applyOrgName',
          span: 12,
        },
        {
          label: '拟稿人',
          type: 'input',
          prop: 'creatorName',
          span: 12,
        },
        {
          label: '拟稿时间',
          type: 'input',
          prop: 'createDate',
          span: 12,
        },
        {
          label: '备注',
          type: 'input',
          mode: 'textarea',
          prop: 'description',
          span: 24
        }
      ],
      basicRules: {},
      basicForm: {
        name: '',
        projectName: ''
      },
      labelPosition: 'top', // 编辑时在上方。仅查看着在左边
      beforeNode2newObj: null,
      saveMainForm: true// todo 感觉没有生效，是因为我的页面有多个表单的问题嘛？
    }
  },
  watch: {
    // 项目只允许选择1次并保存
    projectId: {
      handler(n, o){
        if(n){
          this.$set(this.basicConfig[1], 'disabled', true)
        }else{
          this.$set(this.basicConfig[1], 'disabled', false)
        }
      },
      immediate:true
    }
  },
  computed: {
    // 是否展示【保存】按钮
    saveBtnVisible() {
      return (this.currentNodeCode === 'draft' || this.currentNodeCode === 'jiansheshenpi' || !this.currentNodeCode)
    },
    // 结算审计清单是否可见
    jieSuanShenJiVisible() {
      return !!this.projectId
    },
    // 计费额标准是否可见
    jiFeiBiaoZhunVisible() {
      return !!(this.projectId && this.projectTypeName !== '土建项目')
    },
    // 通信审计table是否可见
    tongXinShenDingJianLiFeiVisible(){
      return !!(this.projectId && this.projectTypeName !== '土建项目')
    },
    // 土建类审定监理费是否可见
    tujianVisible() {
      return !!(this.projectId)
    },
    pageAnchorConfig() {
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '结算<br>审计',
          id: 'sectionJieSuanShenJi',
          show: this.jieSuanShenJiVisible
        },
        {
          text: '计费<br>标准',
          id: 'sectionJiFeiBiaoZhun',
          show: this.jiFeiBiaoZhunVisible
        },
        {
          text: '通信<br>审定',
          id: 'sectionTongXinShenDing',
          show: this.tongXinShenDingJianLiFeiVisible
        },
        {
          text: '土建<br>审定',
          id: 'sectionTuJianShenDing',
          show: this.tujianVisible
        },
        {
          text: '附件',
          id: 'sectionAttachment',
          show: this.id
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlowHistory',
          show: this.id
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId || urlQuery.id || ''
    this.id = urlQuery.id || urlQuery.boId || ''
    // 如果没有boid,表示是新增。有boid是编辑修改
    this.pageLoading = true
    await this.init()
    this.pageLoading = false
    this.$refs.workFlow.init()
  },
  methods: {
    setValueToForms(resData = {}){
      let metaData = {}
      if(!this.id){
        metaData = {
          creatorId: sessionStorage.getItem('userId'),
          creatorName: sessionStorage.getItem('realName'),
          createDate: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
          applyDeptId: sessionStorage.getItem('deptId'),
          applyDeptName: sessionStorage.getItem('deptName')
        }
      }
      this.basicForm = Object.assign(
        metaData,
        this.basicForm,
        resData || {})
    },
    init(id = this.id) {
      return new Promise((resolve, reject) => {
        initService({ boId: id })
          .then(res => {
            if (res.code === '0000') {
              this.projectTypeName = res.data.projectTypeName
              this.projectId = res.data.projectId
              this.setValueToForms(res.data)
              this.completeTaskUrl = res.data.completeTaskUrl
            }
          })
          .finally(_ => {
            resolve()
          })
      })
    },
    // 打开选择项目的弹窗
    openProjectSelectDialog() {
      this.tableQueryParams = {}
      this.$nextTick(_ => {
        this.$refs.projectNameSearchDialog.openDialog()
      })
    },

    // 项目选择的弹窗点了确定
    projectNameSearchConfirmHandle(data) {
      const ids = []
      const names = []
      const codes = []
      const specIds = []
      const specNames = []
      data.forEach(it => {
        ids.push(it.projectId)
        names.push(it.projectName)
        codes.push(it.projectCode)
        specIds.push(it.specId)
        specNames.push(it.specName)
      })
      this.basicForm = JSON.parse(JSON.stringify(this.$refs.basicForm.modelForm))
      this.$set(this.basicForm, 'projectId', ids.join())
      this.$set(this.basicForm, 'projectName', names.join())
      this.$set(this.basicForm, 'projectCode', codes.join())
      this.$set(this.basicForm, 'specId', specIds.join())
      this.$set(this.basicForm, 'specName', specNames.join())

      // 根据所选项目，生成工单名称
      this.$set(this.basicForm, 'name', this.generateName(names.join()))

      this.tableSelectedData = JSON.parse(JSON.stringify(data || []))
    },
    generateName(projectNames){
      return `关于${projectNames}的监理费确认`
    },
    projectNameSearchCancleHandle() {},

    showDeptCheckList({ checkList }) {
      const ids = []
      const names = []
      checkList.forEach(it => {
        ids.push(it.id)
        names.push(it.name)
      })
      this.basicForm = JSON.parse(JSON.stringify(this.$refs.basicForm.modelForm))
      this.$set(this.basicForm, 'supUnitId', ids.join())
      this.$set(this.basicForm, 'supUnitName', names.join())

    },
    // 校验计费标准，或者是土建列表的数据量。必须至少有一个有数据。如果满足这个条件，返回空字符串，表示校验通过。不满足的会返回具体的错误信息文字
    checkJiFeiOrTuJianData(){
      if (this.$refs.jiFeiBiaoZhunTable && this.$refs.jiFeiBiaoZhunTable.tableData && this.$refs.jiFeiBiaoZhunTable.tableData.length > 0){
        return ''
      } else if (this.$refs.tuJianLeiShenDingJianLiFeiTable && this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData && this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData.length){
        // // 检查是否有值
        // let validateSuccess = true
        // const tableData = this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData;
        // for(let i = 0,len = tableData.length;i<len;i++){
        //   let child = tableData[i]
        //   if(!child.supProvider || !child.supFee){
        //     validateSuccess = false
        //     break;
        //   }
        // }
        // if(validateSuccess){
        //   return ''
        // }else{
        //
        // }

        return ''
      } else {
        // 新增时不存在projectId
        if(!this.projectId){
          return ''
        }
        const list = []
        if (this.$refs.jiFeiBiaoZhunTable){
          list.push('计费额标准')
        }
        if (this.$refs.tuJianLeiShenDingJianLiFeiTable){
          list.push('土建类审定监理费')
        }

        return `请填写${list.join('、')}`
      }
    },

    // 保存
    async save(cb) {
      // 首先校验基本信息表单的内容，成功返回空字符串，不成功则反馈具体的错误信息
      const validateBasicFormFlag = await this.validateBasicForm()
      if(validateBasicFormFlag){return validateBasicFormFlag}

      const param = {
        ...this.$refs.basicForm.modelForm,
      }
      // 检查计费标准，或者是土建列表的数据，必须至少有一个有数据。校验失败时，会返回具体的错误信息。校验通过时返回空字符串
      let checkJiFeiOrTuJianData = this.checkJiFeiOrTuJianData()
      if(checkJiFeiOrTuJianData){
        this.$message.warning(checkJiFeiOrTuJianData)
        return false
      }

      if(this.$refs.jiFeiBiaoZhunTable && this.$refs.jiFeiBiaoZhunTable.tableData && this.$refs.jiFeiBiaoZhunTable.tableData.length){// dtoList
        // 校验表格内容是否有效，有效为true，无效为false。有效才继续提交/保存工单
        const validateTableDataFlag = await this.$refs.jiFeiBiaoZhunTable.validateTableData()
        if (!validateTableDataFlag){
          this.$refs.mssPageAnchor.scrollToSection('sectionJiFeiBiaoZhun')
          return false
        }

        let tableData = JSON.parse(JSON.stringify(this.$refs.jiFeiBiaoZhunTable.tableData))
        tableData.forEach(it => {
          if(it.id && it.id.startsWith('_')){
            delete it.id
          }
          it.superfeeId = this.id
        })
        param.dtoList = tableData
      }

      // 土建审定（看文档，通信类的不存在新增保存）
      if(this.$refs.tuJianLeiShenDingJianLiFeiTable && this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData && this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData.length){// dtoList
        let tableData = JSON.parse(JSON.stringify(this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData))
        tableData.forEach(it => {
          if(it.id && it.id.startsWith('_')){
            delete it.id
          }
          it.superfeeId = this.id
        })
        param.dtoLists = tableData
      }
      this.pageLoading = true
      saveService(param)
        .then(async res => {
          if (res.code === '0000') {
            this.id = res.data
            if (cb === 'byBtn') {
              const req = {
                ...this.$route.query,
                id: res.data,
                _t: new Date().getTime()
              }

              let newToPath = this.$route.path + '?'
              for(let key in req){
                newToPath += `${key}=${req[key]}&`
              }
              // 移除最后一个&
              newToPath = newToPath.substr(0, newToPath.length - 1)
              this.$closeTagView({
                close: this.$route.fullPath,
                to: newToPath
              })
            }

            if (cb && cb.constructor === Function) {
              cb(param)
            } else {
              this.$message({
                type: 'success',
                message: '保存成功！'
              })
            }
          }
        })
        .finally(_ => {
          this.pageLoading = false
        })
    },
    getNodeData(data) {
      this.currentNodeName = data.nodeName
      this.currentNodeCode = data.nodeCode
    },
    beforeNode() {
      const fileNameArr = {
        // dishijiheyuan: {
        //   userName: this.basicForm.supervisorUserName,
        //   userId: this.basicForm.supervisorUserId
        // },
        gongjianshenhe: {
          userName: this.basicForm.constructManagerName,
          userId: this.basicForm.constructManagerId
        }
      }
      // 设置默认处理人
      return fileNameArr
    },
    // 特殊记录一下:结算审计清单表格是否可编辑\计费额标准列表是否可编辑
    initFormRules(rules) {
      if (rules) {
        this.basicRules = rules
        this.$nextTick(_ => {
          this.$refs.basicForm.$refs.form.clearValidate()
        })
      }
    },
    // 只读表单
    getReadonlyList(param) {
      if (param && param.length) {
        this.basicConfig.forEach(item => {
          if (param.includes(item.prop)) {
            item.disabled = true
          }
        })
      }
      // 检查结算审计表格和计费额标准列表是否可写
      this.jieSuanShenJiDisabled = param.includes('jieSuanShenJiList')
      this.jiFeiBiaoZhunDisabled = param.includes('jiFeiEBiaoZhun')
      this.tuJianShenDingDisabled = param.includes('tuJianShenDing')
    },
    // 隐藏表单
    getHideList() {},
    // 提交
    async submit() {
      // 首先校验基本信息表单的内容，成功返回空字符串，不成功则反馈具体的错误信息
      // const validateBasicFormFlag = await this.validateBasicForm()
      // if(validateBasicFormFlag){return validateBasicFormFlag}
      this.$refs.workFlow.opendialogInitNextPath()
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.name
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },

    valid() {
      return new Promise(resolve => {
        validService({ boId: this.boId,currentNodeName: this.currentNodeCode })
          .then(res => {
            if (res.data && res.data.success != 'true') {
              resolve(res.data.msg || '附件校验异常！')
            } else {
              resolve('')
            }
          })
          .catch(e => {
            resolve(e.msg || '附件校验异常！')
          })
      })
    },
    // checkMainEquipFee(){
    //   // 计费额标准（不含税）单位的设备费总数
    //   let sumMoney = 0
    //   if(this.$refs.jiFeiBiaoZhunTable){
    //     const tableData = this.$refs.jiFeiBiaoZhunTable.tableData
    //   }
    // },
    // 校验主表单，校验通过返回空字符串，未通过的话返回具体的错误信息
    validateBasicForm(){
      return new Promise(resolve => {
        this.$refs.basicForm.$refs.form.validateScroll((valid) => {
          if(valid){
            resolve('')
          }else{
            resolve('基本信息校验失败，请检查')
          }
        })
      })
    },
    validateJieSuanShenJiTable(){
      if(this.$refs.jieSuanShenJiTable && this.$refs.jieSuanShenJiTable.$refs.JieSuanShenJiTable && this.$refs.jieSuanShenJiTable.$refs.JieSuanShenJiTable.tableData.length){
        return ''
      }else{
        return '结算审计清单数据为空！'
      }
    },
    checkFee(){
      return new Promise(resolve => {
        checkFeeService({ boId: this.id })
        .then(res => {
          if (res.code === '0000') {
            if(res.data.code === 0 || res.data.code === '0'){
              resolve('')
            }else{
              resolve(res.data.msg)
            }
          }else{
            resolve('校验金额失败，请稍后再试')
          }
        })
        .finally(_ => {

        })
      })
    },
    async beforeSubmit() {
      // 首先校验基本信息表单的内容
      const validateBasicFormFlag = await this.validateBasicForm()
      if(validateBasicFormFlag){return validateBasicFormFlag}
      // 如果基本信息表单校验通过，则校验结算审计表格的内容(如果未通过，会返回具体的错误信息。)
      const jieSuanShenJiTableValidation = await this.validateJieSuanShenJiTable()
      if(jieSuanShenJiTableValidation.length > 0){ return jieSuanShenJiTableValidation}

      // 检查计费标准，或者是土建列表的数据，必须至少有一个有数据。校验失败时，会返回具体的错误信息。校验通过时返回空字符串
      let checkJiFeiOrTuJianData = this.checkJiFeiOrTuJianData()
      if(checkJiFeiOrTuJianData){
        this.$message.warning(checkJiFeiOrTuJianData)
        return false
      }

      // 如果基本信息表单校验通过、结算审计表格内容通过，则校验计费标准表格的内容
      if(this.$refs.jiFeiBiaoZhunTable && this.$refs.jiFeiBiaoZhunTable.tableData && this.$refs.jiFeiBiaoZhunTable.tableData.length) {// dtoList
        const validateTableDataFlag = await this.$refs.jiFeiBiaoZhunTable.validateTableData()
        if (!validateTableDataFlag){
          this.$refs.mssPageAnchor.scrollToSection('sectionJiFeiBiaoZhun')
          return '列表校验失败'
        }else{
          // 保存本工单，然后去查处理人 dishijiheyuan
          let flag = await this.saveAndGetUser()
          // 如果是字符串，则表示出错了
          if(typeof flag === 'string' && flag.length){
            return flag
          }else{
            // 请求后端的校验接口，查询数值是否有效
            let flag0 = await this.checkFee()
            if(flag0.length){return flag0}

            let names = []
            let ids = []
            flag.forEach(it => {
              names.push(it.name)
              ids.push(it.id)
            })
            let newObj = {
              // dishijiheyuan: {
              //   userName: names.join(','),
              //   userId: ids.join(',')
              // },
              jianlidanweifuhe: {
                userName: names.join(','),
                userId: ids.join(',')
              },
              // gongjianshenhe: {
              //   userName: names.join(','),
              //   userId: ids.join(',')
              // }
            }
            this.beforeNode2newObj = newObj || []
              // this.$refs.workFlow.nextPathListData.forEach(item => {
              //   if(newObj && newObj[item.nodeCode]){
              //     item.excuterNames = newObj[item.nodeCode].userName
              //     item.excuterIds = newObj[item.nodeCode].userId
              //   }
              // })
              // this.$refs.workFlow.nextPathListData = JSON.parse(JSON.stringify(this.$refs.workFlow.nextPathListData))
            return ''

          }
        }
      } else {
        return ''
      }
    },
    beforeNode2(data){
      const newObj = this.beforeNode2newObj
      data.forEach(item => {
        if(newObj && newObj[item.nodeCode]){
          item.excuterNames = newObj[item.nodeCode].userName
          item.excuterIds = newObj[item.nodeCode].userId
        }
      })
      return data;
    },
    saveAndGetUser(){
      return new Promise(resolve => {
        const param = {
          ...this.$refs.basicForm.modelForm,
        }
        let tableData = JSON.parse(JSON.stringify(this.$refs.jiFeiBiaoZhunTable.tableData))
        tableData.forEach(it => {
          if(it.id && it.id.startsWith('_')){
            delete it.id
          }
          it.superfeeId = this.id
        })
        param.dtoList = tableData

        // 土建审定（看文档，通信类的不存在新增保存）
        if(this.$refs.tuJianLeiShenDingJianLiFeiTable && this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData && this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData.length){// dtoList
          let tableData = JSON.parse(JSON.stringify(this.$refs.tuJianLeiShenDingJianLiFeiTable.tableData))
          tableData.forEach(it => {
            if(it.id && it.id.startsWith('_')){
              delete it.id
            }
            it.superfeeId = this.id
          })
          param.dtoLists = tableData
        }
        this.pageLoading = true
        saveService(param)
          .then(async res => {
            if (res.code === '0000') {
              let id = res.data

              await this.init(res.data)


              getSupervisorUserService({boId: id})
                .then(res => {
                  if (res.code === '0000') {
                    resolve(res.data || [])
                  }
                })
                .catch(e => {
                  resolve('获取默认处理人时出错')
                })
                .finally(_ => {
                  this.pageLoading = false
                })
            }
          })
          .catch(e=>{
            this.pageLoading = false
            resolve('保存工单时出错')
          })
          .finally(_ => {

          })
      })
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/supervision_fee_confirm/find_page'
      })
    }
  }
}
</script>
