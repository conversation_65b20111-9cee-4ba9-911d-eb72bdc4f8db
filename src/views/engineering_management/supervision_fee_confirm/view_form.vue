/**
* @author: ty
* @date: 2023-07-04
* @description: 监理费确认根据boid查看工单
*/
<template>
  <div v-loading="pageLoading" class="ProjectTaskVisaViewForm page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          disable-form
          label-width="200px"
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionJieSuanShenJi" class="page-anchor-point"></div>
    <jieSuanShenJiTable v-if="jieSuanShenJiVisible" disable-form :id="id" :bo-id="boId" :project-id="projectId"></jieSuanShenJiTable>

    <div id="sectionJiFeiBiaoZhun" class="page-anchor-point"></div>
    <jiFeiBiaoZhunTable
      disable-form
      v-if="jiFeiBiaoZhunVisible"
      ref="jiFeiBiaoZhunTable"
      :id="id"
      :bo-id="boId"
      :project-id="projectId">
    </jiFeiBiaoZhunTable>

    <div id="sectionTongXinShenDing" class="page-anchor-point"></div>
    <tongXinShenDingJianLiFeiTable v-if="tongXinShenDingJianLiFeiVisible"  disable-form :id="id" :bo-id="boId" :project-id="projectId"></tongXinShenDingJianLiFeiTable>

    <div id="sectionTuJianShenDing" class="page-anchor-point"></div>
    <tuJianLeiShenDingJianLiFeiTable v-if="tujianVisible" disable-form ref="tuJianLeiShenDingJianLiFeiTable" :id="id" :bo-id="boId" :project-id="projectId"></tuJianLeiShenDingJianLiFeiTable>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      :deal-page="dealPage"
      :bo-id="boId"
      :business-type="workflowCode"
    ></mssAttachment>

    <div id="sectionWorkFlowHistory" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  initService,
  findProjectService,
  saveService
} from '@/api/engineering_management/supervisionFeeConfirm.js'
import jieSuanShenJiTable from './components/jie_suan_shen_ji_table'
import jiFeiBiaoZhunTable from './components/ji_fei_biao_zhun_table'
import tongXinShenDingJianLiFeiTable from './components/tong_xin_shen_ding_jian_li_fei_table'
import tuJianLeiShenDingJianLiFeiTable from './components/tu_jian_lei_shen_ding_jian_li_fei_table.vue'
export default {
  name: 'SupervisionFeeConfirmViewForm',
  components: {jieSuanShenJiTable, jiFeiBiaoZhunTable, tongXinShenDingJianLiFeiTable, tuJianLeiShenDingJianLiFeiTable},
  data() {
    return {
      pageLoading: false,
      currentNodeName: '', // 当前所属环节name
      currentNodeCode: '', // 当前所属环节code
      projectTypeName: '',// 项目类型名称 (目前数据库只有“土建项目 ”“其他” 这2种数据)  判断土建还是非土建 TODO: 当前工单是通信类还是土建类，这个字段是暂时命名的，具体的等后端接口出来
      boId: '',
      projectId: '',
      routerType: '',
      dealPage: false,
      workflowCode: 'SupervisorFeeConfirm',
      completeTaskUrl: '',
      returnAddress: '/supervision_fee_confirm/find_page', // 写死？还是说等接口传入？
      // 项目选择弹窗的相关配置信息
      tableApi: findProjectService,
      tableQueryParams: {},
      searchFieldList: [
        {
          label: '项目名称',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          fieldName: 'projectCode'
        },
        {
          label: '项目年份',
          fieldName: 'projectYear'
        },
        {
          label: '专业',
          fieldName: 'specName'
        },
      ],
      tableSelectedData: [], // 表格中已勾选的数据。数据格式与查询接口返回的数据格式一致
      tableDataKey: 'projectId', // 表格的查询接口返回的数据中表示唯一标识的字段
      tableColumns: [
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 200
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 100
        },
        {
          label: '专业',
          prop: 'specName',
          width: 100
        },
        {
          label: '年份',
          prop: 'projectYear',
          width: 100
        },
        {
          label: '建设部门',
          prop: 'orgDeptName',
          minWidth: 200
        },{
          label: '计划项目经理',
          prop: 'managerName',
          minWidth: 200
        },{
          label: '建设项目经理',
          prop: 'projectmanagerName',
          minWidth: 200
        },
      ],
      basicConfig: [
        {
          label: '监理费确认工单名称：',
          type: 'input',
          prop: 'name',
          span: 24
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          span: 24
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          span: 12,
          disabled: true
        },
        {
          label: '所属专业：',
          type: 'input',
          prop: 'specName',
          disabled: true
        },
        {
          label: '建设单位：',
          type: 'input',
          prop: 'buildOrgName',
          span: 12,
          disabled: true
        },
        {
          label: '立项批复投资(元)：',
          type: 'input',
          prop: 'projectReplyinvest',
          span: 12,
          disabled: true
        },
        {
          label: '设计批复投资(元)：',
          type: 'input',
          prop: 'designReplyInvest',
          span: 12,
          disabled: true
        },
        // {
        //   label: '设计批复中设备费金额（万元）',
        //   type: 'input',
        //   prop: 'equipAmount',
        //   span: 12,
        //   disabled: true
        // },
        {
          label: '拟稿单位：',
          type: 'input',
          prop: 'applyOrgName',
          span: 12,
          disabled: true
        },
        {
          label: '拟稿人：',
          type: 'input',
          prop: 'creatorName',
          span: 12,
          disabled: true
        },
        {
          label: '拟稿时间：',
          type: 'input',
          prop: 'createDate',
          span: 12,
          disabled: true
        },
        {
          label: '备注：',
          type: 'input',
          mode: 'textarea',
          prop: 'description',
          span: 24
        }
      ],
      basicForm: {
        name: '',
        projectName: ''
      },
      labelPosition: 'left', // 编辑时在上方。仅查看着在左边
      saveMainForm: true// todo 感觉没有生效，是因为我的页面有多个表单的问题嘛？
    }
  },
  computed: {
    // 结算审计清单是否可见
    jieSuanShenJiVisible() {
      return !!this.projectId
    },
    // 计费额标准是否可见
    jiFeiBiaoZhunVisible() {
      return !!(this.projectId && this.projectTypeName !== '土建项目')
    },
    // 通信审计table是否可见
    tongXinShenDingJianLiFeiVisible(){
      return !!(this.projectId && this.projectTypeName !== '土建项目')
    },
    // 土建类审定监理费是否可见
    tujianVisible() {
      return !!(this.projectId)
    },
    pageAnchorConfig() {
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '结算<br>审计',
          id: 'sectionJieSuanShenJi',
          show: this.jieSuanShenJiVisible
        },
        {
          text: '计费<br>标准',
          id: 'sectionJiFeiBiaoZhun',
          show: this.jiFeiBiaoZhunVisible
        },
        {
          text: '通信<br>审定',
          id: 'sectionTongXinShenDing',
          show: this.tongXinShenDingJianLiFeiVisible
        },
        {
          text: '土建<br>审定',
          id: 'sectionTuJianShenDing',
          show: this.tujianVisible
        },
        {
          text: '附件',
          id: 'sectionAttachment',
          show: this.id
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlowHistory',
          show: this.id
        }
      ]
    }
  },
  watch: {
    'currentNodeCode': {
      handler(n, o) {
      },
      immediate: true
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId || urlQuery.id || ''
    this.id = urlQuery.id || urlQuery.boId || ''
    this.routerType = urlQuery.type || ''// 如果type为coop。表示是从list_cooperate.vue页面来的
    // 如果没有boid,表示是新增。有boid是编辑修改
    this.pageLoading = true
    await this.init()
    this.pageLoading = false
    // this.$refs.workFlow.init()
  },
  methods: {
    setValueToForms(resData = {}){
      this.basicForm = Object.assign({
        creatorId: sessionStorage.getItem('userId'),
        creatorName: sessionStorage.getItem('realName'),
        createDate: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
        applyDeptId: sessionStorage.getItem('deptId'),
        applyDeptName: sessionStorage.getItem('deptName')
      }, this.basicForm, resData || {})
    },
    init(id = this.id) {
      return new Promise((resolve, reject) => {
        initService({ boId: id })
          .then(res => {
            if (res.code === '0000') {
              this.projectTypeName = res.data.projectTypeName
              this.projectId = res.data.projectId
              this.setValueToForms(res.data)
              this.completeTaskUrl = res.data.completeTaskUrl
            }
          })
          .finally(_ => {
            resolve()
          })
      })
    },
    // 只读表单
    getReadonlyList(param) {
      if (param && param.length) {
        this.basicConfig.forEach(item => {
          if (param.includes(item.prop)) {
            item.disabled = true
          }
        })
      }
    },
    // 隐藏表单
    getHideList() {},
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/supervision_fee_confirm/find_page'
      })
    }
  }
}
</script>
