/**
* @author: ty
* @date: 2023-07-03
* @description: 监理费用确认
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="addHandle">新增</el-button>
        <el-button type="primary" @click="delHandle">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          :selectable="canSelect"
          selection
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService, delService } from '@/api/engineering_management/supervisionFeeConfirm.js'
import { commonMultDel } from '@/utils/btn'
export default {
  name: 'SupervisionFeeConfirmFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '监理费工单名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '建设单位',
          type: 'input',
          fieldName: 'orgNames'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'entityStatus'
        }
      ],
      api: findPageService,
      staticSearchParam: {},
      columns: [
        {
          label: '监理费用确认工单名称',
          prop: 'name',
          minWidth: 200,
          tooltip: true,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
        },
        {
          label: '建设单位',
          prop: 'buildOrgName',
          tooltip: true,
        },
        {
          label: '拟稿人',
          prop: 'creatorName',
          tooltip: true,
        }, {
          label: '拟稿时间',
          prop: 'createDate',
          tooltip: true,
          width: 100,
        },
        {
          label: '状态',
          prop: 'status',
          width: 80,
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          width: 80,
          formatter: row => {
            return (
              <span>
                {
                  row.isAllowOperate
                    ? <a href='javascript:;' class='mr10' onClick={() => {
                      this.operateHandle(row, 'deal')
                    }}
                    >处理</a>
                    : ''
                }
                <a href='javascript:;' onClick={() => {
                  this.operateHandle(row, 'view')
                }}
                >查看</a>
              </span>
            )
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      this.$set(this.searchConfig[4], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB' }))
      this.$set(this.$refs.searchForm.searchForm, 'entityStatus', 'all')
    },
    // 新增
    addHandle() {
      this.$router.push({
        path: '/supervision_fee_confirm/edit'
      })
    },
    //  删除
    delHandle() {
      const req = {
        data: this.$refs.table.multipleSelection,
        delApi: delService,
        key: 'id',
        sucCb: (res) => {
          if (res.code === '0000') {
            this.$message.success('删除成功')
          } else {
            this.$message.warning(res.data || '请稍后再试')
          }
          this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
          this.search(this.$refs?.searchForm?.searchForm || {})
        }
      }
      commonMultDel.call(this, req)
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({ isAllowOperate, boId = '', id = '' }, operateType) {
      if (operateType === 'deal') { // 处理
        this.$router.push({
          path: '/supervision_fee_confirm/edit',
          query: {
            id,
            boId
          }
        })
      } else { // 查看
        this.$router.push({
          path: '/supervision_fee_confirm/view',
          query: {
            id,
            boId
          }
        })
      }
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    },
    // 是否可选
    canSelect(row) {
      if (row.isAllowDelete) {
        return true
      } else {
        return false
      }
    },
  }
}
</script>
