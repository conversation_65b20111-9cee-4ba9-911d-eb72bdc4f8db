/**
* @author: ty
* @date: 2023-07-19
* @description:
*/
<template>
  <mssCard title="通信类审定监理费（不含税）（元）如：审定金额和实际不符，请调整计费标准">
    <div slot="content">
      <mssTable
        ref="tongXinShenDingTable"
        :selection="false"
        :columns="tongXinShenDingTableColumns"
        :static-search-param="tongXinShenDingTableParam"
        :api="tongXinShenDingTableApi"
      />
    </div>
  </mssCard>
</template>

<script>
import {getCivilService} from '@/api/engineering_management/supervisionFeeConfirm.js'
export default {
  name: 'TongXinShenDingJianLiFeiTable',
  props: {
    boId: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: ''
    },
  },
  data(){
    return {
      tongXinShenDingTableColumns: [
        {
          label: '监理单位名称',
          prop: 'supProviderName'
        },
        {
          label: '标段',
          prop: 'consTendersName'
        },
        {
          label: '中标折扣率(%)',
          prop: 'rebate'
        },
        {
          label: '审定监理费（不含税）',
          prop: 'supFee'
        }
      ],
      tongXinShenDingTableParam: {
        boId: this.id,
        // supProviderType: 1,
      },
      tongXinShenDingTableApi: getCivilService
    }
  },
  created(){
  },
}
</script>
