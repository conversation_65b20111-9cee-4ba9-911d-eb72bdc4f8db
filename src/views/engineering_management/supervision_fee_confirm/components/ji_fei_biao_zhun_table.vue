/**
* @author: ty
* @date: 2023-07-19
* @description: 计费额标准列表
*/
<template>
  <mssCard title="计费额标准（不含税）（元）">
    <div slot="headerBtn">
      <el-button v-if="!disableForm" @click="jiFeiBiaoZhunTableAddHandle">新增</el-button>
      <el-button v-if="!disableForm" type="primary" @click="jiFeiBiaoZhunTableDelHandle">删除</el-button>
    </div>
    <div slot="content">
      <el-form
        ref="table"
        :model="formModel"
        :disabled="disableForm">
        <mssTable
          ref="jiFeiBiaoZhunTable"
          selection
          :columns="jiFeiBiaoZhunTableColumns"
          :stationary="tableData"
          :pagination="false"
        />
      </el-form>
    </div>
  </mssCard>
</template>

<script>
import {
  getChargingStandardService, // 查询列表
  getCityService, // 选择区县监理单位
  getSupervisorDeptService,// 选监理单位
  getConsTendersByCountyIdService,
  getProviderByCountyIdService,
  saveChargingStandardService,
  deleteChargingStandardService
} from '@/api/engineering_management/supervisionFeeConfirm'

export default {
  name: 'JiFeiBiaoZhunTable',
  props: {
    boId: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: ''
    },
    disableForm: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const checkSamecountyCodesupervisionOrgIdconsTenders = (rowIndex, tableData) => {
      let selfTableData = tableData[rowIndex]
      let selfIds = `${selfTableData.countyCode}_${selfTableData.supervisionOrgId}_${selfTableData.consTenders}`
      let errMsg = ''
      for(let i = 0, len = this.tableData.length;i<len;i++){
        if(i !== rowIndex){
          let child = this.tableData[i]
          let ids = `${child.countyCode}_${child.supervisionOrgId}_${child.consTenders}`
          if(ids === selfIds){
            errMsg = '不可出现重复的区县、监理单位、标段'
            break
          }
        }
      }
      return errMsg
    }
    let validateCounty = (rule, value, callback) => {
      // 相同区县、监理单位、标段、只能唯一
      let rowIndex = Number(rule.field.replace('countyCode', ''))
      let errMsg = checkSamecountyCodesupervisionOrgIdconsTenders(rowIndex, this.tableData)
      if(errMsg){
        callback(new Error(errMsg))
      }else{
        callback()
      }
    }
    let validateconsTenders = (rule, value, callback) => {
      let rowIndex = Number(rule.field.replace('consTenders', ''))
      let errMsg = checkSamecountyCodesupervisionOrgIdconsTenders(rowIndex, this.tableData)
      if(errMsg){
        callback(new Error(errMsg))
      }else{
        callback()
      }
    }
    return {
      formModel: {},
      tableData: [],
      cityList: [],
      supervisorDeptList: [],// 监理单位下拉可选列表（后面新加的接口对应的功能）
      supervisionMap: {}, // 数据格式类似于：{'地市id1': [],'地市id2': [],}
      consTendersMap: {},
      countSum: {},// 调用getConsTendersMap接口的次数，这个是有必要的，防止页面出错时多次请求接口
      jiFeiBiaoZhunTableColumns: [
        {
          label: '区县',
          prop: 'countyCode',
          minWidth: 210,
          formatter: (row, column, cellValue, index) => {
            return (
              <el-form-item prop={`countyCode${index}`} rules={this.countyRules}>
                <el-select
                  v-model={row.countyCode}
                  onChange={(val) => { this.changeCountyHandle(val, row, column, cellValue, index) }}>

                  {this.cityList.map((item, index) => (
                    <el-option key={index} label={item.county} value={item.countycode}></el-option>
                  ))}
                </el-select>
              </el-form-item>
            )
          }
        },
        {
          label: '监理单位名称',
          prop: 'supervisionOrgId', // 'supervisionOrgName'
          minWidth: 210,
          formatter: (row, column, cellValue, index) => {
            return (
              <el-form-item prop={`supervisionOrgId${index}`} rules={this.supervisionOrgRules}>
                <el-select v-model={row.supervisionOrgId}
                  onChange={(val) => { this.changeSupervisionOrgHandle(val, row, column, cellValue, index) }}>
                  {this.supervisorDeptList.map((item, index) => (
                    <el-option key={index} label={item.supervisorDeptName} value={item.supervisorDeptCode}></el-option>
                  ))}
                </el-select>
              </el-form-item>
              )

            // if (this.supervisionMap[row.countyCode]) {
            //   return (
            //     <el-form-item prop={`supervisionOrgId${index}`} rules={this.supervisionOrgRules}>
            //       <el-select v-model={row.supervisionOrgId} onChange={(val) => { this.supervisionOrgIdHandle(val) }}>
            //       {this.supervisionMap[row.countyCode].map((item, index) => (
            //         <el-option key={index} label={item.text} value={item.id}></el-option>
            //       ))}
            //       </el-select>
            //     </el-form-item>
            //   )
            // } else {
            //   return (
            //     <el-form-item prop={`supervisionOrgId${index}`} rules={this.supervisionOrgRules}>
            //       <el-select v-model={row.supervisionOrgName}></el-select>
            //     </el-form-item>
            //   )
            // }
          }
        },
        {
          label: '标段',
          prop: 'consTenders',
          minWidth: 210,
          formatter: (row, column, cellValue, index) => {
            if(!this.consTendersMap[`${row.countyCode}_${row.supervisionOrgId}`] && (this.countSum[`${index}`] === undefined || this.countSum[`${index}`] < 3)){
              this.getConsTendersMap(row, index)
            }
            if (this.consTendersMap[`${row.countyCode}_${row.supervisionOrgId}`]) {
              return (
                <el-form-item prop={`consTenders${index}`} rules={this.consTendersRules}>
                  <el-select
                    class='consTendersSelect1'
                    v-model={row.consTenders}
                    onFocus={(evt) => { this.focusConsTendersHandle(evt, row, column, cellValue, index)}}
                    onChange={(val) => { this.changeConsTendersHandle(val, row, column, cellValue, index) }}>
                  {this.consTendersMap[`${row.countyCode}_${row.supervisionOrgId}`].map((item, index) => (
                    <el-option key={index} label={item.consTendersName} value={item.consTendersId}></el-option>
                  ))}
                  </el-select>
                </el-form-item>
              )
            }
          }
        },
        {
          label: '系统自动带出，不能修改',
          children: [
            {
              label: '折前审定施工费（不含税）',
              minWidth: 210,
              prop: 'constructionFee'
            },
            {
              label: '甲供材料费（不含税）',
              minWidth: 210,
              prop: 'materialFee'
            }
          ]
        },
        {
          label: '调整后金额，允许修改',
          children: [
            {
              label: '折前审定施工费（不含税）',
              prop: 'constructionFeeAfter',
              minWidth: 210,
              formatter: (row) => {
                const precision = 2
                return (<el-input type='number' v-model={row.constructionFeeAfter} ></el-input>)
              }
            },
            {
              label: '甲供材料费（不含税）',
              prop: 'materialFeeAfter',
              minWidth: 210,
              formatter: (row) => {
                const precision = 2
                return (<el-input type='number' v-model={row.materialFeeAfter}></el-input>)
              }
            }
          ]
        }, {
          label: '调整差额，不能修改',
          children: [
            {
              label: '折前审定施工费（不含税）',
              prop: 'constructionFeeDiff',
              minWidth: 210
            }, {
              label: '甲供材料费（不含税）',
              prop: 'materialFeeDiff',
              minWidth: 210
            }
          ]
        },
        {
          label: '设备费（不含税）',
          prop: 'mainEquipFee',
          minWidth: 210,
          formatter: (row) => {
            const precision = 2
            return (<el-input type='number' v-model={row.mainEquipFee}></el-input>)
          }
        }
      ],
      countyRules: [
        { required: true, message: '请输入区县', trigger: ['blur', 'change']},
        { validator: validateCounty, trigger: ['blur', 'change'] },
      ],
      supervisionOrgRules: [
        { required: true, message: '请输入监理单位', trigger: ['blur', 'change']},
      ],
      consTendersRules: [
        { required: true, message: '请输入标段', trigger: ['blur', 'change']},
        { validator: validateconsTenders, trigger: ['blur', 'change'] },
      ]
    }
  },
  async created() {
    await this.getCityList()// 获取区县数据
    await this.getSupervisorDeptList()// 获取监理单位数据

    this.getTableList({
      boId: this.id,
      limit: 99999,
      page: 1
    })
  },
  methods: {
    // 根据传入数据重新给form赋值，这是为了表单校验
    updateFormModel(list = []) {
      let listData = JSON.parse(JSON.stringify(list))
      let newFormData = {}
      listData.forEach((it, index) => {
        // 只设置以下三个数据，因为校验时校验的就是这3个数据，其他的数据不需要校验
        newFormData[`consTenders${index}`] = it.consTenders
        newFormData[`countyCode${index}`] = it.countyCode
        newFormData[`supervisionOrgId${index}`] = it.supervisionOrgId
      })
      this.formModel = newFormData
    },
    getTableList(queryParams) {
      getChargingStandardService(queryParams)
        .then(res => {
          this.tableData = res.data || []
          this.updateFormModel(this.tableData)
        })
        .catch(e => {
        })
    },
    // 获取区县数据
    getCityList() {
      return new Promise((resolve, reject) => {
        getCityService({ projectId: this.projectId })
          .then(res => {
            if (res && res.code === '0000') {
              this.cityList = res.data.map((it, index) => {
                return {
                  ...it,
                  index: index
                }
              })
            } else {
              this.cityList = []
            }
          })
        .finally(_=>{
          resolve()
        })
      })
    },
    // 获取监理单位数据
    getSupervisorDeptList() {
      return new Promise((resolve, reject) => {
        getSupervisorDeptService({ projectId: this.projectId })
          .then(res => {
            if (res && res.code === '0000') {
              this.supervisorDeptList = res.data.map((it, index) => {
                return {
                  ...it,
                  index: index
                }
              })
            } else {
              this.supervisorDeptList = []
            }
          })
        .finally(_=>{
          resolve()
        })
      })
    },
    // 新增一行空数据
    jiFeiBiaoZhunTableAddHandle() {
      this.tableData.push({
        id: '_' + new Date().getTime(), // todo 必须要有id,否则删除功能会失效。但是要注意，提交到后端的时候可能不需要id
        countyCode: '',
        countyName: '',
        supervisionOrgId: '',
        supervisionOrgName: '',
        consTenders: '',
        consTendersName: '',
        constructionFee: 0,
        materialFee: 0,
        constructionFeeAfter: 0,
        materialFeeAfter: 0,
        constructionFeeDiff: 0,
        materialFeeDiff: 0,
        mainEquipFee: 0
      })
    },
    // 调用后端的删除接口
    delServiceHandle(req){
      return new Promise(resolve => {
        deleteChargingStandardService(req)
          .then(res => {
            if(res && res.code === '0000'){
              resolve('')
            }else{
              resolve(res?.data?.msg || '删除失败')
            }
          }).catch(e=>{
            resolve(e.message || '删除失败')
        })
      })
    },
    // 删除勾选的数据
    jiFeiBiaoZhunTableDelHandle() {
      const checkedRows = this.$refs.jiFeiBiaoZhunTable.multipleSelection
      if (checkedRows.length) {
        this.$confirm('是否确认删除这些数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          const checkedIds = checkedRows.map(it => it.id)
          // 区分只需要在页面上删除的数据和需要提交接口的数据
          let deletingListOnlyOnPage = []
          let deletingListToBackend = []
          let othersList = []
          this.tableData.forEach(it => {
            if (checkedIds.includes(it.id)) {
              if (it.id.startsWith('_')) {
                deletingListOnlyOnPage.push(it)
              } else {
                deletingListToBackend.push(it)
              }
            } else {
              othersList.push(it)
            }
          })
          this.$refs.jiFeiBiaoZhunTable.tableLoading = true
          // 需要调用后端删除接口
          if (deletingListToBackend && deletingListToBackend.length) {
            await this.delServiceHandle({ids: deletingListToBackend.map(it => it.id).join()})
          }
          this.tableData = JSON.parse(JSON.stringify(othersList))
          this.updateFormModel(this.tableData)
          this.$refs.jiFeiBiaoZhunTable.tableLoading = false
          this.$nextTick(_ => {
            this.$refs.jiFeiBiaoZhunTable.$refs.table.clearSelection()
          })
        }).catch(() => {})
      } else {
        this.$message.warning('请勾选需要删除的数据')
      }
    },
    getCityDataByVal(val){
      return this.cityList.find(it => it.countycode === val)
    },
    getSupervisionDataByVal(val){
      return this.supervisorDeptList.find(it => it.supervisorDeptCode === val)
    },
    // 区县选择器的选中值切换之后
    changeCountyHandle(val, tableRow, column, cellValue, index) {
      const cityData = this.getCityDataByVal(val)
      this.$set(tableRow, 'countyName', cityData.county)
      this.$set(tableRow, 'consTenders', '')// 清空当前的标段信息

      this.formModel[`countyCode${index}`] = val
      this.formModel[`consTenders${index}`] = ''
      this.formModel = JSON.parse(JSON.stringify(this.formModel))// 必须要这一句，否则表单校验会出错

      if(tableRow.supervisionOrgId){
        getConsTendersByCountyIdService({
          countyId: cityData.countycode,
          providerId: tableRow.supervisionOrgId
        })
          .then(res => {
            if (res.code === '0000' && res.data && res.data.length){
              this.consTendersMap[`${cityData.countycode}_${tableRow.supervisionOrgId}`] = res.data
            } else {
              this.consTendersMap[`${cityData.countycode}_${tableRow.supervisionOrgId}`] = []
            }
            this.consTendersMap = JSON.parse(JSON.stringify(this.consTendersMap))
          })
      }
    },
    // 监理单位选择器的选中值切换之后
    changeSupervisionOrgHandle(val, tableRow, column, cellValue, index){
      const supervisionData = this.getSupervisionDataByVal(val)
      // this.$set(tableRow, 'supervisionOrgId', supervisionData.supervisorDeptCode)
      this.$set(tableRow, 'supervisionOrgName', supervisionData.supervisorDeptName)
      this.$set(tableRow, 'consTenders', '')// 清空当前的标段信息

      this.formModel[`supervisionOrgId${index}`] = supervisionData.supervisorDeptCode
      this.formModel[`consTenders${index}`] = ''
      this.formModel = JSON.parse(JSON.stringify(this.formModel))// 必须要这一句，否则表单校验会出错
      getConsTendersByCountyIdService({
        countyId: tableRow.countyCode,
        providerId: supervisionData.supervisorDeptCode
      })
        .then(res => {
          if (res.code === '0000' && res.data && res.data.length){
            this.consTendersMap[`${tableRow.countyCode}_${supervisionData.supervisorDeptCode}`] = res.data
          } else {
            this.consTendersMap[`${tableRow.countyCode}_${supervisionData.supervisorDeptCode}`] = []
          }
          this.consTendersMap = JSON.parse(JSON.stringify(this.consTendersMap))
        })
    },
    getConsTendersDataByVal(list, val) {
      return list.find(it => it.consTendersId === val)
    },
    // 标段选择器聚焦时事件
    focusConsTendersHandle(evt, row, column, cellValue, index){
    },
    // 标段选择器的选中值切换之后
    changeConsTendersHandle(val, row, column, cellValue, index){
      const consTendersData = this.getConsTendersDataByVal(this.consTendersMap[`${row.countyCode}_${row.supervisionOrgId}`], val)

      this.$set(row, 'consTendersName', consTendersData.consTendersName)

      this.formModel[`consTenders${index}`] = val
      this.formModel = JSON.parse(JSON.stringify(this.formModel))// 必须要这一句，否则表单校验会出错
    },
    getConsTendersMap(row, index){
      let countyCode = row.countyCode
      let supervisionOrgId = row.supervisionOrgId

      if(!countyCode || !supervisionOrgId){return false}
      if(this.countSum[`${index}`] === undefined){
        this.countSum[`${index}`] = 0
      }
      this.countSum[`${index}`] += 1
      getConsTendersByCountyIdService({
        countyId: row.countyCode,
        providerId: row.supervisionOrgId
      })
        .then(res => {
          if (res.code === '0000' && res.data && res.data.length){
            this.consTendersMap[`${row.countyCode}_${row.supervisionOrgId}`] = res.data
          } else {
            this.consTendersMap[`${row.countyCode}_${row.supervisionOrgId}`] = []
          }
          this.consTendersMap = JSON.parse(JSON.stringify(this.consTendersMap))
        })
    },
    // 校验表格内容是否有效（相同区县、监理单位、标段、只能唯一）
    validateTableData(){
      return new Promise(resolve => {
        this.$refs.table.validate(v=>{
          resolve(v)
        })
      })
    }
  }
}
</script>
