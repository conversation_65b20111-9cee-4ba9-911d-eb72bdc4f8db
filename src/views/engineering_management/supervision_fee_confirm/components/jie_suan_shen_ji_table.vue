/**
* @author: ty
* @date: 2023-07-19
* @description: 结算审计清单列表
*/
<template>
  <mssCard title="结算审计清单">
    <div slot="headerBtn">
      <el-upload
        ref="newFile"
        class="upload-btn"
        action="string"
        :show-file-list="false"
        :auto-upload="true"
        :http-request="JieSuanShenJiTableImportHandle"
      >
        <el-button v-if="!disableForm" type="primary" :loading="importLoading">导入</el-button>
      </el-upload>
      <el-button type="primary" @click="JieSuanShenJiTableExportHandle">导出</el-button>
    </div>
    <div slot="content">
      <mssTable
        ref="JieSuanShenJiTable"
        :columns="JieSuanShenJiTableColumns"
        :static-search-param="JieSuanShenJiTableParam"
        :api="JieSuanShenJiTableApi"
      />
    </div>
  </mssCard>
</template>

<script>
import {
  getConstructionAuditService,
  exportConstructionAuditService,
  importExcelService
} from '@/api/engineering_management/supervisionFeeConfirm';
import { commonDown } from '@/utils/btn'
export default {
  name: 'JieSuanShenJiTable',
  props: {
    boId: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: ''
    },
    disableForm: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      JieSuanShenJiTableColumns: [
        {
          label: '结算审计号',
          prop: 'auditCode'
        },{
          label: '施工送审金额（元）',
          prop: 'constructionFeeB'
        },{
          label: '折前审定施工费（不含税）(元)',
          prop: 'constructionFeeA'
        },{
          label: '甲材送审金额（元）',
          prop: 'materialFeeB'
        },{
          label: '甲供材料费（不含税）(元)',
          prop: 'materialFeeA'
        },{
          label: '施工单位',
          prop: 'consUnitNames'
        },
      ],
      JieSuanShenJiTableParam: {
        boId: this.id
      },
      JieSuanShenJiTableApi: getConstructionAuditService, // 结算审计清单的tableapi
      importLoading: false
    }
  },
  created(){
  },
  methods: {
    search() {
      this.$refs.JieSuanShenJiTable.page.current = 1
      this.$refs.JieSuanShenJiTable.getTableData({})
    },
    // 导入
    JieSuanShenJiTableImportHandle(params) {
      this.importLoading = true
      const param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.id)
      importExcelService(param).then((res) => {
        if (res.code === '0000') {
          this.$message.success('导入成功')
          this.search()
        }
      }).finally(_ => {
        this.importLoading = false
      })
    },
    // 导出
    JieSuanShenJiTableExportHandle(){
      commonDown({boId: this.id}, exportConstructionAuditService);
    },
  }
}
</script>

<style lang="scss" scoped>
.upload-btn {
  display: inline-block;
  margin: 0 10px;
}
</style>
