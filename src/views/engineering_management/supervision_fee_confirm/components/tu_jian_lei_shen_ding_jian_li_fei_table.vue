/**
* @author: ty
* @date: 2023-07-18
* @description: 土建类审定监理费的list
*/
<template>
  <mssCard title="土建类审定监理费（不含税）（元）如：审定金额和实际不符，请调整计费标准">
    <div slot="headerBtn">
      <el-button v-if="!disableForm" @click="addHandle">新增</el-button>
      <el-button v-if="!disableForm" type="primary" @click="delHandle">删除</el-button>
    </div>
    <div slot="content">
<!--      <el-form-->
<!--        ref="table"-->
<!--        :model="formModel"-->
<!--        :disabled="disableForm">-->
      <mssTable
        ref="tuJianShenDingTable"
        selection
        :columns="tuJianShenDingTableColumns"
        :stationary="tableData"
        :pagination="false"
      />

<!--      </el-form>-->
    </div>
  </mssCard>
</template>

<script>
import {
  delCivilItemService,
  getCivilProviderService,
  getCivilProviderListService,
  saveCivilService
} from '@/api/engineering_management/supervisionFeeConfirm.js'
export default {
  name: 'TuJianLeiShenDingJianLiFeiTable',
  props: {
    boId: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: ''
    },
    disableForm: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      supProviderList: [],
      tuJianShenDingTableColumns: [
        {
          label: '监理单位名称',
          prop: 'supProvider', //supervisorDeptCode??supervisorDeptName??
          formatter: (row, column, cellValue, index) => {
            if(this.disableForm){
              return row.supProviderName
            }else{
              return (<el-select v-model={row.supProvider} onChange={(val) => { this.changeSupProviderHandle(val, row, column, cellValue, index) }}>
                {this.supProviderList.map((item, index) => (
                  <el-option key={index} label={item.supervisorDeptName} value={item.supervisorDeptCode}></el-option>
                ))}
              </el-select>)
            }
          }
        },
        {
          label: '审定监理费（不含税）',
          prop: 'supFee',
          formatter: (row) => {
            if(this.disableForm){
              return row.supFee
            }else{
            return (<el-input type='number' v-model={row.supFee}></el-input>)}
          }
        },
      ],
      tuJianShenDingTableParam: {
        boId: this.id,
        supProviderType: 2
      }
    }
  },
  created() {
    this.getTableList({
      boId: this.id,
      supProviderType: 2,
      limit: 99999,
      page: 1
    })

    getCivilProviderService({
      projectId: this.projectId
    })
    .then(res => {
      this.supProviderList = res.data || []
    })
  },
  methods: {
    getTableList(queryParams) {
      getCivilProviderListService(queryParams)
        .then(res => {
          this.tableData = res?.data?.data || []
        })
        .catch(e => {
        })
    },
    // 新增一行空数据
    addHandle() {
      this.tableData.push({
        id: '_' + new Date().getTime(), // todo 必须要有id,否则删除功能会失效。但是要注意，提交到后端的时候可能不需要id
        supProviderName: '',
        supFee: ''
      })
    },
    // 调用后端的删除接口
    delServiceHandle(req){
      return new Promise(resolve => {
        delCivilItemService(req)
          .then(res => {
            if(res && res.code === '0000'){
              resolve('')
            }else{
              resolve(res?.data?.msg || '删除失败')
            }
          }).catch(e=>{
          resolve(e.message || '删除失败')
        })
      })
    },
    // 删除勾选的数据 todo 数据不全，没法调试
    delHandle() {
      const checkedRows = this.$refs.tuJianShenDingTable.multipleSelection
      if (checkedRows.length) {
        this.$confirm('是否确认删除这些数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          const checkedIds = checkedRows.map(it => it.id)
          // 区分只需要在页面上删除的数据和需要提交接口的数据
          let deletingListOnlyOnPage = []
          let deletingListToBackend = []
          let othersList = []
          this.tableData.forEach(it => {
            if (checkedIds.includes(it.id)) {
              if (it.id.startsWith('_')) {
                deletingListOnlyOnPage.push(it)
              } else {
                deletingListToBackend.push(it)
              }
            } else {
              othersList.push(it)
            }
          })
          this.$refs.tuJianShenDingTable.tableLoading = true
          // 需要调用后端删除接口
          if (deletingListToBackend && deletingListToBackend.length) {
            await this.delServiceHandle({ids: deletingListToBackend.map(it => it.id).join()})
          }
          this.tableData = JSON.parse(JSON.stringify(othersList))
          this.$refs.tuJianShenDingTable.tableLoading = false
          this.$nextTick(_ => {
            this.$refs.tuJianShenDingTable.$refs.table.clearSelection()
          })
        }).catch(() => {})
      } else {
        this.$message.warning('请勾选需要删除的数据')
      }
    },
    getSupProviderDataByVal(val){
      return this.supProviderList.find(it => it.supervisorDeptCode === val)
    },
    changeSupProviderHandle(val, tableRow, column, cellValue, index){
      const data = this.getSupProviderDataByVal(val)
      this.$set(tableRow, 'supProvider', data.supervisorDeptCode)
      this.$set(tableRow, 'supProviderName', data.supervisorDeptName)
      this.$set(tableRow, 'supProviderType', '2')
    },
    saveHandle(){
      return new Promise(resolve => {
        // 区分出是在页面上增加的数据，这些去掉 id
        const newList = this.tableData.map(it => {
          const newIt = { ...it, supProviderType: 2 }
          if (newIt.id.startsWith('_')) {
            delete newIt.id
          }
          return newIt
        })
        saveCivilService(newList)
          .then(res => {
            if (res && res.code === '0000'){
              // 保存成功
              resolve('')
            } else {
              // 保存失败
              resolve(res?.data?.msg || '保存失败')
            }
          }).catch(e => {
            resolve(e.msg || e.message)
          })
      })
    }
  }
}
</script>
