/**
* @author: ty
* @date: 2023-07-06
* @description: 开工启动会根据id查看工单
*/
<template>
  <div class="kickoffMeetingViewForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :deal-page="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
  getDetailService
} from '@/api/engineering_management/kickoffMeeting.js'
export default {
  name: 'KickoffMeetingViewForm',
  data() {
    return {
      pageLoading: false,
      boId: '',
      businessType: '',
      basicConfig: [
        {
          label: '开工启动会单号：',
          type: 'input',
          prop: 'code',
          span: 12,
        },
        {
          label: '开工启动会单名称：',
          type: 'input',
          prop: 'name',
          span: 12,
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          minWidth: 200
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          minWidth: 200
        },
        {
          label: '工程实施经理(主)：',
          type: 'input',
          prop: 'projectManagerName',
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
          minWidth: 200
        },
        {
          label: '创建时间：',
          prop: 'createDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd'
        },
        {
          label: '施工单位：',
          type: 'input',
          prop: 'constructionOrgName',
          minWidth: 200
        },
        {
          label: '计划开工日期：',
          prop: 'startDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd'
        },
        {
          label: '计划完工日期：',
          prop: 'endDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          label: '开工会启动完成时间：',
          prop: 'startCompletionDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
        },{
          label: '建设内容：',
          type: 'input',
          prop: 'constructContent',
          span: 24
        },
        {
          label: '建设方式：',
          type: 'input',
          prop: 'constructType',
          span: 24
        },
        {
          label: '工期安排：',
          type: 'input',
          prop: 'schedulePlan',
          span: 24
        },
        {
          label: '其他事宜：',
          type: 'input',
          prop: 'otherContent',
          span: 24
        }
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || ''
    this.boId = urlQuery.boId || ''
    this.pageLoading = true
    await this.getDataById()
    this.pageLoading = false
  },
  methods: {
    getDataById() {
      return new Promise(resolve => {
        getByIdService(this.id)
          .then(res => {
            if(res && res.code === '0000'){
              this.basicForm = Object.assign({}, this.basicForm, res.data)
            }
          }).finally(_=>{
          resolve()
        })
      })
    },
    // 返回
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/kickoff_meeting/find_page'
      })
    }
  }
}
</script>
