/**
* @author: ty
* @date: 2023-07-07
* @description: 开工启动会-列表查询
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService, delService } from '@/api/engineering_management/kickoffMeeting.js'
import { commonDown, commonMultDel } from '@/utils/btn'
import { exportExcelService } from '@/api/engineering_management/projectMessage'
export default {
  name: 'KickoffMeetingFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '开工启动会单号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '开工启动会名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '开工启动会单号',
          prop: 'code',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '开工启动会名称',
          prop: 'name',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '创建人',
          prop: 'creatorName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '创建日期',
          prop: 'createDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.createDate){
              return this.$moment(row.createDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '施工单位',
          prop: 'constructionOrgName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '计划开工日期',
          prop: 'startDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.startDate){
              return this.$moment(row.startDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '计划完工日期',
          prop: 'endDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.endDate){
              return this.$moment(row.endDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '单据状态',
          prop: 'status',
          width: 80,
          tooltip: true
        },
        {
          label: '建设内容',
          prop: 'constructContent',
          minWidth: 100,
          tooltip: true
        },{
          label: '建设方式',
          prop: 'constructType',
          minWidth: 100,
          tooltip: true
        },{
          label: '工期安排',
          prop: 'schedulePlan',
          minWidth: 100,
          tooltip: true
        },{
          label: '其他事宜',
          prop: 'otherContent',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          width: 80,
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标4是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.searchConfig[4], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },

    // 导出
    exportHandle(){
      commonDown({type: '开工启动会', ...this.staticSearchParam}, exportExcelService);
    },

    // 点击操作列的按钮，处理或者查看单子
    operateHandle({boId, id}) {
      this.$router.push({
        path: '/kickoff_meeting/view_form',
        query: {
          boId,
          id
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    }
  }
}
</script>
