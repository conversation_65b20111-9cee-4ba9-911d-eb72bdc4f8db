/**
* @author: ty
* @date: 2023-07-11
* @description:
*/
<template>
<div class="page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionTaskInfo" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :deal-page="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import { detailPageService, taskListService  } from '@/api/engineering_management/designDisclosure.js'
export default {
  name: 'DesignDisclosureViewForm',
  data() {
    return {
      pageLoading: false,
      id: this.$route.query.id,
      boId: this.$route.query.boId,
      businessType: '',
      tableApi: taskListService,
      staticSearchParam: {id: this.$route.query.id || ''},
      columns: [
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 160
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 160
        },{
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 120
        },{
          label: '任务专业类型',
          prop: 'taskSpecType'
        },{
          label: '任务经理',
          prop: 'managerName'
        },{
          label: '施工单位',
          prop: 'constructionOrgName'
        },{
          label: '施工队长',
          prop: 'constructionUserName'
        },{
          label: '监理单位',
          prop: 'supervisionOrgName'
        },{
          label: '监理工程师',
          prop: 'supervisionUserName'
        }
      ],
      basicConfig: [
        {
          label: '设计交底单号：',
          prop: 'code',
          span: 12,
          type: 'input'
        },
        {
          label: '设计交底单名称：',
          prop: 'name',
          span: 12,
          type: 'input'
        },
        {
          label: '项目编码：',
          prop: 'projectCode',
          span: 12,
          type: 'input'
        },
        {
          label: '项目名称：',
          prop: 'projectName',
          span: 12,
          type: 'input'
        },
        {
          label: '工程实施经理(主)：',
          prop: 'projectManagerName',
          span: 12,
          type: 'input'
        },
        {
          label: '交底时间：',
          prop: 'disclosureDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          span: 12
        },
        {
          label: '会议详细地点：',
          prop: 'address',
          span: 12,
          type: 'input'
        },
        {
          label: '组织单位：',
          prop: 'orgName',
          span: 12,
          type: 'input'
        },
        {
          label: '参会人员：',
          prop: 'participantUserNames',
          span: 24,
          type: 'input'
        },
        {
          label: '创建人：',
          prop: 'creatorName',
          span: 12,
          type: 'input'
        },
        {
          label: '创建日期：',
          prop: 'createDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          span: 12
        },
        {
          label: '备注：',
          prop: 'description',
          span: 12,
          type: 'input'
        }
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionTaskInfo'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  created() {
   this.getBasicInfo()
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/design_disclosure/find_page'
      })
    },
    getBasicInfo(){
      detailPageService(this.$route.query.id || '').then(res=>{
        this.basicForm = Object.assign({}, this.basicForm, res.data)
      })
    },
  }
}
</script>

<style scoped>

</style>
