/**
* @author: ty
* @date: 2023-07-11
* @description:
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {findPageService } from "@/api/engineering_management/designDisclosure"
import { exportExcelService } from '@/api/engineering_management/projectMessage'
import { commonDown } from "@/utils/btn";
export default {
  name: 'DesignDisclosureFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '设计交底工单号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '设计交底工单名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '设计交底单号',
          prop: 'code',
          minWidth: 160,
          tooltip: true
        },
        {
          label: '设计交底单名称',
          prop: 'name',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '项目年份',
          prop: 'projectYear',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '工程实施经理（主）',
          prop: 'projectManagerName',
          minWidth: 140,
          tooltip: true
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '组织单位',
          prop: 'orgName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '创建人',
          prop: 'creatorName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '创建日期',
          prop: 'createDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.createDate){
              return this.$moment(row.createDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '单据状态',
          prop: 'status',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '操作',
          prop: '_operationCol',
          width: 80,
          fixed: 'right',
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      this.$set(this.searchConfig[6], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 导出
    exportHandle(){
      commonDown({type: '设计交底', ...this.staticSearchParam }, exportExcelService);
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({id}) {
      this.$router.push({
        path: '/design_disclosure/view_form',
        query: {
          id
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    }
  }
}
</script>

<style scoped>

</style>
