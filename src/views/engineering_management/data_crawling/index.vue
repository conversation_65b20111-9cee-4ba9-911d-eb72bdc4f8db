<template>
  <div>
    <mssSearchForm ref="searchForm" :search-config="searchConfig" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button type="primary" @click="compensateBatch">补偿</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          :autoCall="false"
          selection
          :staticSearchParam="staticSearchParam"
          border
          @tableDataChange="tableDataChange"
        ></mssTable>
      </div>
    </mssCard>
    <el-dialog title="数据详情" :visible.sync="dialogVisible" width="70%">
      <div>{{ datas }}</div>
    </el-dialog>
    <el-dialog title="日志详情" custom-class="log-content" :visible.sync="logDialogVisible" width="40%">
      <mssForm
        v-for="(item,index) in basicForm"
        :key="index"
        :disable-form="true"
        :config="basicConfig"
        :label-position="labelPosition"
        :form="item"
      />
    </el-dialog>
  </div>
</template>

<script>
// api
import {
  exchangerQueryService,
  exchangerCompensateService,
  exchangerGetmodulecodesService,
  exchangerDetailService,
  compensateAllNoFileService
} from '@/api/engineering_management/data_crawling_api.js'
export default {
  name: 'DataCrawlingFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '业务模型编码',
          type: 'select',
          options: [],
          fieldName: 'moduleCode',
          rules: [
            { required: true, message: '请选择业务模型编码', trigger: 'change' }
          ]
        },
        {
          label: '数据状态',
          type: 'select',
          options: [],
          fieldName: 'status',
          clearable: true
        },
        {
          label: '数据详情',
          type: 'input',
          fieldName: 'datas'
        },
        {
          label: '业务编号',
          type: 'input',
          fieldName: 'boId'
        }
      ],
      api: exchangerQueryService,
      staticSearchParam: {},
      columns: [
        {
          label: '业务模型编码',
          prop: 'moduleCode',
          tooltip: true
        },
        {
          label: '业务编号',
          prop: 'boId',
          tooltip: true
        },
        {
          label: '数据详情',
          prop: 'datas',
          tooltip: true,
          formatter: (row) => {
            return (
              <el-button
                type="text"
                onclick={() => {
                  this.details(row)
                }}
              >
                详情
              </el-button>
            )
          }
        },
        {
          label: '数据创建时间',
          prop: 'createDate',
          tooltip: true
        },
        {
          label: '数据状态',
          prop: 'status',
          tooltip: true,
          formatter: (row) => {
            return this.statusOption.map((item) => {
              if (item.value == row.status) {
                return <span>{item.label}</span>
              }
            })
          }
        },
        {
          label: '投递成功时间',
          prop: 'sendTime',
          tooltip: true
        },
        {
          label: '已执行的任务次数',
          prop: 'jobCount',
          tooltip: true
        },
        {
          prop: 'operate',
          label: '操作',
          width: 120,
          formatter: (row) => {
            return (
              <span>
                <el-button
                  type="text"
                  onclick={() => {
                    this.compensate(row)
                  }}
                >
                  补偿
                </el-button>
                <el-button
                  type="text"
                  onclick={() => {
                    this.action(row)
                  }}
                >
                  查看
                </el-button>
              </span>
            )
          }
        }
      ],
      statusOption: [
        {
          value: 0,
          label: '无需投递'
        },
        {
          value: 1,
          label: '待投递'
        },
        {
          value: 2,
          label: '投递完成'
        },
        {
          value: 3,
          label: '投递未成功，但业务侧通知无需再次投递'
        },
        {
          value: 4,
          label: '投递失败'
        }
      ],
      basicConfig: [
        {
          label: '投递开始时间：',
          type: 'input',
          prop: 'sendBeginTime',
          span: 24
        },
        {
          label: '投递结束时间：',
          type: 'input',
          prop: 'sendEndTime',
          span: 24
        },
        {
          label: '投递状态：',
          type: 'input',
          prop: 'status',
          span: 24
        },
        {
          label: 'HTTP状态码：',
          type: 'input',
          prop: 'httpCode',
          span: 24
        },
        {
          label: '返回详情：',
          type: 'textarea',
          prop: 'message',
          mode: 'textarea',
          resize:'none',
          autosize:true,
          span: 24
        }
      ],
      dialogVisible: false,
      logDialogVisible: false,
      datas: '',
      labelPosition: 'left',
      basicForm: [],
      flag: false
    }
  },
  created() {
    this.searchConfig[1].options = this.statusOption
    exchangerGetmodulecodesService().then((res) => {
      if (res && res.code == '0000') {
        this.searchConfig[0].options = res.data.map((item) => {
          return {
            label: item.moduleName + item.moduleCode,
            value: item.moduleCode
          }
        })
      }
    })
  },
  methods: {
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.searchForm.$refs.searchForm.validate((valid) => {
        if (valid) {
          this.$refs.table.page.current = 1
          this.staticSearchParam = JSON.parse(JSON.stringify(searchData))
          this.$refs.table.getTableData(searchData)
        }
      })
    },
    reset(searchData) {
      this.search()
    },
    compensate(row) {
      let req = {
        exchangerId: row.id,
        moduleCode: row.moduleCode
      }
      exchangerCompensateService(req).then((res) => {
        if (res && res.code == '0000') {
          this.$message.success('补偿调用成功')
          this.search()
        }
      })
    },
    compensateBatch() {
      this.$refs.searchForm.$refs.searchForm.validate((valid) => {
        if (valid) {
          const selection = this.$refs.table.multipleSelection || []
          const ids = []
          selection.forEach((item) => {
            ids.push(item.id)
          })
          let req = {
            exchangerIds: ids.join(','),
            moduleCode: this.$refs.searchForm.searchForm.moduleCode
          }
          compensateAllNoFileService(req).then((res) => {
            if (res && res.code == '0000') {
              this.$message.success('补偿调用成功')
              this.flag = true
              this.search()
            }
          })
        }
      })
    },
    tableDataChange() {
      if (this.flag) {
        this.$nextTick(() => {
          this.$refs.table.$refs.table.clearSelection()
          this.flag = false
        })
      }
    },
    details(row) {
      this.datas = row.datas
      this.dialogVisible = true
    },
    action(row) {
      exchangerDetailService(row.id, row.moduleCode).then((res) => {
        if (res && res.code == '0000') {
          this.basicForm = res.data || []
          this.logDialogVisible = true
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .log-content {
  .el-dialog__body {
    max-height: 60vh;
    overflow: auto;
    .base-form {
      border-bottom: 1px solid #e6ebf5;
      margin-bottom: 10px;
    }
  }
}
</style>
