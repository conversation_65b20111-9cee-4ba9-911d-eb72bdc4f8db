/**
* @author: ty
* @date: 2023-07-07
* @description: 割接交维（割接上线交维）-列表查询
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService } from '@/api/engineering_management/intersectDimension.js'
import { commonDown } from '@/utils/btn'
import { exportExcelService } from '@/api/engineering_management/projectMessage'
export default {
  name: 'IntersectDimensionFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '割接交维单号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '割接交维名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '割接上线单号',
          prop: 'code',
          minWidth: 200,
          tooltip: true,
        },
        {
          label: '割接上线单名称',
          prop: 'name',
          minWidth: 200,
          tooltip: true,
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true,
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          tooltip: true,
        },
        {
          label: '任务名称',
          prop: 'taskName',
          tooltip: true,
        },
        {
          label: '交维日期',
          prop: 'dimensionDate',
          tooltip: true,
          formatter: (row)=>{
            if(row.dimensionDate){
              return this.$moment(row.dimensionDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '交维负责人',
          prop: 'dimensionManagerName',
          minWidth: 100,
          tooltip: true,
        },
        {
          label: '单据状态',
          prop: 'status',
          tooltip: true,
        },
        {
          label: '验收状态',
          prop: 'acceptanceStatus',
          tooltip: true,
        },
        {
          label: '操作',
          prop: '_operationCol',
          width: 80,
          fixed: 'right',
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标6是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.searchConfig[6], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },

    // 导出
    exportHandle(){
      commonDown({type: '割接交维', ...this.staticSearchParam}, exportExcelService);
    },

    // 导出附件
    exportAttachmentHandle(){

    },
    // 点击操作列的按钮，查看单子
    operateHandle({boId, id}) {
      this.$router.push({
        path: '/intersect_dimension/view_form',
        query: {
          boId,
          id
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    }
  }
}
</script>
