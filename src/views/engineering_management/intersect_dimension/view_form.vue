/**
* @author: ty
* @date: 2023-07-06
* @description: 割接交维根据id查看工单
*/
<template>
  <div class="IntersectDimensionViewForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionProjctBasicInfo" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :dealPage="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
  getDetailService
} from '@/api/engineering_management/intersectDimension.js'
export default {
  name: 'IntersectDimensionViewForm',
  data() {
    return {
      pageLoading: false,
      boId: '',
      businessType: '',
      tableApi: getDetailService,
      columns: [
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 200
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 200
        },
        {
          label: '任务专业类型',
          prop: 'projectManageSpec',
          minWidth: 200
        },{
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 200
        },{
          label: '任务经理',
          prop: 'taskManager',
          minWidth: 200
        },
        {
          label: '监理单位',
          prop: 'supervisionOrgName',
          minWidth: 200
        },{
          label: '监理单位负责人',
          prop: 'supervisionUserName',
          minWidth: 200
        },
        {
          label: '施工单位',
          prop: 'constructionOrgName',
          minWidth: 200
        },
        {
          label: '施工单位负责人',
          prop: 'constructionUserName',
          minWidth: 200
        },
        {
          label: '设备建设情况',
          prop: 'equipmentSituation',
          minWidth: 200
        },
        {
          label: '割接上线交维反馈结果',
          prop: 'responseResult',
          minWidth: 200
        },
        {
          label: '备注',
          prop: 'description',
          minWidth: 200
        },
      ],
      tableQueryParams: {},
      tableVisible: false,
      tableParams: {},
      basicConfig: [
        {
          label: '割接上线单号：',
          type: 'input',
          prop: 'code',
          span: 12,
        },
        {
          label: '割接上线单名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          minWidth: 200
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          minWidth: 200
        },
        {
          label: '项目管理颗粒度：',
          type: 'input',
          prop: 'manageGranularity',
          minWidth: 200,
          span: 24,
        },
        {
          label: '交维日期：',
          type: 'input',
          prop: 'dimensionDate',
        },
        {
          label: '维护部门负责人：',
          type: 'input',
          prop: 'dimensionManagerName',
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
        },
        {
          label: '创建日期：',
          prop: 'createDate',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          label: '备注：',
          type: 'input',
          prop: 'description',
          minWidth: 200,
          span: 24
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionProjctBasicInfo'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || ''
    this.boId = urlQuery.boId || ''
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getDataById()
    this.pageLoading = false
  },
  methods: {
    getDataById() {
      return new Promise(resolve => {
        getByIdService(this.id)
          .then(res => {
            if(res && res.code === '0000'){
              this.basicForm = Object.assign({}, this.basicForm, res.data)
            }
          }).finally(_=>{
          resolve()
        })
      })
    },
    // 返回
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/intersect_dimension/find_page'
      })
    }
  }
}
</script>
