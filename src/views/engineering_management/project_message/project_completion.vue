/**
* @author: ty
* @date: 2023-07-26
* @description: 完工报告完成时间待阅提醒-查看详情页面 对应/project-center-jx/project_completion/{id}
*/
<template>
  <div class="TransfertimelinessView page-anchor-parentpage" v-loading="pageLoading">
    <div style="margin-top: 30px;"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <p class="message-title">{{messageTitle}}</p>
      </div>
    </mssCard>

    <div id="sectionTaskList" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {findByMsgIdService} from "@/api/message_manager/message_manager_api";
import { taskCutoverByIdService } from "@/api/engineering_management/projectMessage.js";

export default {
  name: "ProjectCompletionViewMsg",
  data(){
    return {
      id: '',
      messageId: '',
      messageType: '',
      messageTitle: '',
      pageLoading: false,
      tableApi: taskCutoverByIdService,
      tableVisible: false,
      tableParams: {},
      columns: [
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '任务编码',
          prop: 'taskNumber',
          minWidth: 200,
          tooltip: true
        },{
          label: '完工报告完成时间',
          prop: 'actualEndDate',
          minWidth: 200,
          tooltip: true
        },
      ],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionTaskList'
        }
      ]
    }
  },
  async created(){
    const urlQuery = this.$route.query
    this.messageId = urlQuery.messageId
    this.messageType = urlQuery.type
    this.id = this.$route.params.id
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getData()
    this.pageLoading = false
  },
  methods: {
    getData(){
      return new Promise((resolve, reject) => {
        findByMsgIdService(this.messageId)
        .then(res => {
          this.messageTitle = res?.data?.title || ''
        })
        .finally(_ => {
          this.pageLoading = false
        })
      })
    }
  }
}
</script>
