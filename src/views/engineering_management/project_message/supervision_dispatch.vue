/**
* @author: ty
* @date: 2023-07-26
* @description: 施工派工待阅-查看详情页面 对应/project-center-jx/project_message/supervision_dispatch/message/{id}
*/
<template>
  <div class="SupervisionDispatchMessage page-anchor-parentpage" v-loading="pageLoading">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          label-width="240px"
          disable-form
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm" />
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :selection="false"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
  getSupTaskByIdService,
} from "@/api/engineering_management/projectMessage";

export default {
  name: "SupervisionDispatchMessage",
  data(){
    return {
      pageLoading: false,
      labelPosition: 'left', // 查看页面放在左边
      basicConfig: [
        {
          label: '项目名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'code',
          span: 12
        },
        {
          label: '建设单位：',
          type: 'input',
          prop: 'constructOrgName',
          span: 12
        },
        {
          label: '建设单位项目经理：',
          type: 'input',
          prop: 'projectManagerName',
          span: 12
        },
      ],
      api: getSupTaskByIdService,
      staticSearchParam:{id: this.id},
      columns: [
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 200
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 200
        },
        {
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 200
        },
        {
          label: '任务专业类型',
          prop: 'taskSpec',
          minWidth: 200,
        },
        {
          label: '任务经理',
          prop: 'taskManager',
          minWidth: 200,
        },
        {
          label: '监理单位',
          prop: 'supervisionOrg',
          minWidth: 200,
        },
        {
          label: '监理工程师',
          prop: 'superviser',
          minWidth: 200,
        },
      ],
      basicForm: {},
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionList'
        }
      ]
    }
  },
  async created(){
    this.id = this.$route.params.id
    this.pageLoading = true
    this.staticSearchParam.id = this.id,
    await this.getData()
    this.pageLoading = false
  },
  methods: {
    getData(){
      return new Promise((resolve, reject) => {
        getByIdService(this.id)
          .then(res => {
            if (res.code === '0000') {
              this.basicForm = res.data
            }
          })
          .finally(_ => {
            resolve()
          })
      })
    },
  }
}
</script>
