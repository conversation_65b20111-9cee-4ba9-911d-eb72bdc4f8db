/**
* @author: ty
* @date: 2023-07-11
* @description: 监理派工列表查询页（监理派工分页查询页）
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { findPageService } from '@/api/engineering_management/supervisionDispatch.js'
import { exportExcelService } from '@/api/engineering_management/projectMessage'
import { commonDown } from "@/utils/btn";
export default {
  name: 'SupervisionDispatchFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '监理派工单号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '监理派工单名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '监理派工单号',
          prop: 'code',
          minWidth: 160,
          tooltip: true
        },
        {
          label: '监理派工单名称',
          prop: 'name',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '立项批复金额（万元）',
          prop: 'decisionMoney',
          minWidth: 150,
          tooltip: true
        },
        {
          label: '项目经理',
          prop: 'projectManagerName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '派工人员',
          prop: 'dispatchUserName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '派工时间',
          prop: 'dispatchDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.dispatchDate){
              return this.$moment(row.dispatchDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '派工描述',
          prop: 'dispatchDesc',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '监理单位',
          prop: 'supervisionOrgName',
          minWidth: 100,
          tooltip: true
        }, {
          label: '份额产品名称',
          prop: 'productName',
          minWidth: 120,
          tooltip: true
        },
        {
          label: '合同编码',
          prop: 'contractCode',
          minWidth: 100,
          tooltip: true
        },{
          label: '合同名称',
          prop: 'contractName',
          minWidth: 100,
          tooltip: true
        },{
          label: '合同行号',
          prop: 'contractLineCode',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '不含税可用投资金额（元）',
          prop: 'planInvest',
          minWidth: 180,
          tooltip: true
        },
        {
          label: '总监理工程师',
          prop: 'supervisionManagerName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '总预估监理费（元）',
          prop: 'totalInvest',
          minWidth: 140,
          tooltip: true
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 160,
          tooltip: true
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 180,
          tooltip: true
        }, {
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 100,
          tooltip: true
        },{
          label: '项目管理专业',
          prop: 'projectManageSpec',
          minWidth: 120,
          tooltip: true
        },{
          label: '预估监理费（元）',
          prop: 'constructMoney',
          minWidth: 120,
          tooltip: true
        },
        {
          label: '单据状态',
          prop: 'status',
          width: 80,
          tooltip: true
        },
        {
          label: '要求受理时间',
          prop: 'requiredAcceptanceDate',
          minWidth: 100,
          tooltip: true,
          formatter: (row)=>{
            if(row.requiredAcceptanceDate){
              return this.$moment(row.requiredAcceptanceDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '要求监理总结上传日期',
          prop: 'requiredUploadDate',
          minWidth: 160,
          tooltip: true,
          formatter: (row)=>{
            if(row.requiredUploadDate){
              return this.$moment(row.requiredUploadDate).format('yyyy-MM-DD')
            }else{
              return ''
            }
          }
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  created(){
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      this.$set(this.searchConfig[6], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 导出
    exportHandle(){
      commonDown({type: '监理派工', ...this.staticSearchParam }, exportExcelService);
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({id}) {
      this.$router.push({
        path: '/supervision_dispatch/view_form',
        query: {
          id
        }
      })
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()

    }
  }
}
</script>

<style scoped>

</style>
