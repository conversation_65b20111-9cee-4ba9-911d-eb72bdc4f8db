/**
* @author: ty
* @date: 2023-07-11
* @description:
*/
<template>
<div class="page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionBasic2" class="page-anchor-point"></div>
    <mssCard title="单位信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig2"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionTaskInfo" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

  <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
  <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :deal-page="false" />

  <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import { detailPageService, taskListService  } from '@/api/engineering_management/supervisionDispatch.js'
export default {
  name: 'SupervisionDispatchViewForm',
  data() {
    return {
      pageLoading: false,
      id: this.$route.query.id,
      boId: this.$route.query.boId,
      businessType: '',
      tableApi: taskListService,
      staticSearchParam: {id: this.$route.query.id},
      columns: [
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 160
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 160
        },{
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 120
        },{
          label: '项目管理专业',
          prop: 'projectManageSpec',
          minWidth: 120
        },{
          label: '预估监理费（元）',
          prop: 'constructMoney',
          minWidth: 140
        },{
          label: '要求受理时间',
          prop: 'requiredAcceptanceDate',
          minWidth: 160
        },{
          label: '要求监理总结上传日期',
          prop: 'requiredUploadDate',
          minWidth: 160
        },{
          label: '监理单位属性',
          prop: 'supervisionOrgType',
          minWidth: 140
        }
      ],
      // 基本信息
      basicConfig: [
        {
          label: '监理派工单号：',
          prop: 'code',
          span: 12,
          type: 'input'
        },
        {
          label: '监理派工单名称：',
          prop: 'name',
          span: 12,
          type: 'input'
        },
        {
          label: '项目编码：',
          prop: 'projectCode',
          span: 12,
          type: 'input'
        },
        {
          label: '项目名称：',
          prop: 'projectName',
          span: 12,
          type: 'input'
        },
        {
          label: '立项批复金额(万元)：',
          prop: 'decisionMoney',
          span: 12,
          type: 'input'
        },
        {
          label: '工程实施经理(主)：',
          prop: 'projectManagerName',
          span: 12,
          type: 'input'
        },
        {
          label: '派工人员：',
          prop: 'dispatchUserName',
          span: 12,
          type: 'input'
        },
        {
          label: '派工时间：',
          prop: 'dispatchDate',
          span: 12,
          type: 'input'
        },
        {
          label: '派工描述：',
          prop: 'description',
          span: 12,
          type: 'input'
        }
      ],
      // 监理单位信息
      basicConfig2: [
        {
          label: '监理单位：',
          prop: 'supervisionOrgName',
          span: 12,
          type: 'input'
        },
        {
          label: '份额产品名称：',
          prop: 'productName',
          span: 12,
          type: 'input'
        },
        {
          label: '合同编码：',
          prop: 'contractCode',
          span: 12,
          type: 'input'
        },
        {
          label: '合同名称：',
          prop: 'contractName',
          span: 12,
          type: 'input'
        },
        {
          label: '合同行号：',
          prop: 'contractLineCode',
          span: 12,
          type: 'input'
        },
        {
          label: '不含税可用投资金额(元)：',
          prop: 'planInvest',
          span: 12,
          type: 'input'
        },
        {
          label: '监理单位负责人：',
          prop: 'supervisionManagerName',
          span: 12,
          type: 'input'
        },
        {
          label: '总预估监理费(元)：',
          prop: 'constructMoney',
          span: 12,
          type: 'input'
        }
      ],
      basicForm: {
        name: ''
      },
      basicForm2: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },{
          text: '单位<br>信息',
          id: 'sectionBasic2'
        },
        {
          text: '任务<br>列表',
          id: 'sectionTaskInfo'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  created() {
    this.getBasicInfo()
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/supervision_dispatch/find_page'
      })
    },
    getBasicInfo(){
      detailPageService(this.$route.query.id).then(res=>{
        this.basicForm = Object.assign({}, this.basicForm, res.data)
      })
    },
  }
}
</script>

<style scoped>

</style>
