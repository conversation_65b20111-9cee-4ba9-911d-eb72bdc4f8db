/**
* @author: ty
* @date: 2023-07-26
* @description: 转资及时性列表查看
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          selection
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
export default {
  name: "TransfertimelinessFindPage",
  data() {
    return {
      searchConfig: [
        {
          label: '待阅状态',
          type: 'select',
          options: [
            {
              label: '待阅',
              value: 1,
            }
          ],
          fieldName: 'status'
        },
      ]
    }
  }
}
</script>

<style scoped>

</style>
