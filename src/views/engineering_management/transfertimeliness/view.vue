/**
* @author: ty
* @date: 2023-07-26
* @description: 转资及时性-查看详情页面 对应/project-center-jx/transfertimeliness/{id}
*/
<template>
  <div class="TransfertimelinessView page-anchor-parentpage" v-loading="pageLoading">
    <div style="margin-top: 30px;"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          label-width="240px"
          disable-form
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm" />
      </div>
    </mssCard>

    <div id="sectionTaskList" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {getByIdService, taskCutoverByIdService} from "@/api/engineering_management/transfertimeliness";

export default {
  name: "TransfertimelinessView",
  data(){
    return {
      pageLoading: false,
      labelPosition: 'left', // 查看页面放在左边
      basicConfig: [
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          span: 24
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          span: 12
        },
        {
          label: '项目类型：',
          type: 'input',
          prop: 'projectType',
          span: 12
        },
        {
          label: '创建时间：',
          type: 'input',
          prop: 'modifyDate',
          span: 12
        },
        {
          label: '交维结束时间：',
          type: 'input',
          prop: 'deliveryMaintenanceEndDate',
          span: 12
        },
        {
          label: '物资未转资金额：',
          type: 'input',
          prop: 'untransferInvAmount',
          span: 12
        },
        {
          label: '费用类未转资金额：',
          type: 'input',
          prop: 'untransferExpAmount',
          span: 12
        },
      ],
      basicForm: {},
      tableApi: taskCutoverByIdService,
      tableVisible: false,
      tableParams: {},
      columns: [
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 200,
          tooltip: true
        },{
          label: '任务编号',
          prop: 'taskNumber',
          minWidth: 200,
          tooltip: true
        },{
          label: '公司名称',
          prop: 'companyName',
          minWidth: 200,
          tooltip: true
        },{
          label: '公司编码',
          prop: 'companyCode',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 200,
          tooltip: true
        },{
          label: '项目名称',
          prop: 'projectName',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '项目类型',
          prop: 'projectType',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '任务工程管理专业',
          prop: 'managerMajorName',
          minWidth: 200,
          tooltip: true
        },{
          label: 'EAM流程中资产条目数',
          prop: 'notApprovalCount',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '已定义已分摊金额',
          prop: 'untransferConfirmamountDif',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '转资次数',
          prop: 'transferCount',
          minWidth: 200,
          tooltip: true
        },{
          label: '已转资金额',
          prop: 'transferredAmount',
          minWidth: 200,
          tooltip: true
        },{
          label: '转资确认失败金额',
          prop: 'untransferConfirmamountFail',
          minWidth: 200,
          tooltip: true
        }, {
          label: '转资工单金额',
          prop: 'untransferConfirmamountProc',
          minWidth: 200,
          tooltip: true
        },{
          label: '未转资金额',
          prop: 'untransferredAmount',
          minWidth: 200,
          tooltip: true
        },{
          label: '已签收资产条目数',
          prop: 'approvalCount',
          minWidth: 200,
          tooltip: true
        },{
          label: '完工日期',
          prop: 'completionDate',
          minWidth: 200,
          tooltip: true
        },{
          label: '交维结束时间',
          prop: 'deliveryMaintenanceEndDate',
          minWidth: 200,
          tooltip: true
        },{
          label: '交维开始时间',
          prop: 'deliveryMaintenanceStartDate',
          minWidth: 200,
          tooltip: true
        },{
          label: 'ERP未转资金额',
          prop: 'erpUntransferAmount',
          minWidth: 200,
          tooltip: true
        },{
          label: '支出金额',
          prop: 'expenditureAmount',
          minWidth: 200,
          tooltip: true
        },{
          label: '第一次转资完成时间',
          prop: 'firstCompleteTime',
          minWidth: 200,
          tooltip: true
        },{
          label: '已形成固定资产',
          prop: 'formedFixedAssets',
          minWidth: 200,
          tooltip: true
        },
      ],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionTaskList'
        }
      ]
    }
  },
  async created(){
    this.id = this.$route.params.id
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getData()
    this.pageLoading = false
  },
  methods: {
    getData(){
      return new Promise((resolve, reject) => {
        getByIdService(this.id)
          .then(res => {
            if (res.code === '0000') {
              this.basicForm = res.data
            }
          })
          .finally(_ => {
            resolve()
          })
      })
    }
  }
}
</script>
