/**
* @author: ty
* @date: 2023-07-03
* @description: 工程签证（合作）
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="addHandle">新增</el-button>
        <el-button type="primary" @click="delHandle">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          row-key="boId"
          :autoCall="false"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          :selectable="canSelect"
          selection
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageCoopService, delService } from '@/api/engineering_management/projectTaskVisa.js'
import { commonMultDel } from '@/utils/btn'
export default {
  name: 'ProjectTaskVisaFindPageCoop',
  data() {
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '工程签证工单号',
          type: 'input',
          fieldName: 'visacode'
        },
        {
          label: '工程签证名称',
          type: 'input',
          fieldName: 'visaname'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'selectType'
        }
      ],
      api: findPageCoopService,
      staticSearchParam:{},
      columns: [
        {
          label: '工程签证工单号',
          prop: 'visacode',
          minWidth: 180,
          tooltip: true
        },
        {
          label: '工程签证工单名称',
          prop: 'visaname',
          minWidth: 180,
          tooltip: true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 180,
          tooltip: true
        }, {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true
        },
        {
          label: '合同名称',
          prop: 'contractName',
          minWidth: 180,
          tooltip: true
        },
        {
          label: '拟稿人',
          prop: 'creatorName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '状态',
          prop: 'chineseStatus',
          tooltip: true
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          formatter: row => {
            return (
              <span>
                {
                  row.isCurrentUserTask
                    ? <a href='javascript:;' class='mr10' onClick={() => {
                      this.operateHandle(row, 'deal')
                    }}
                    >处理</a>
                    : ''
                }
                <a href='javascript:;' onClick={() => {
                  this.operateHandle(row, 'view')
                }}
                >查看</a>
              </span>
            )
          }
        }
      ]
    }
  },
  async created() {
    await this.getStatusList()
    this.search()
  },
  methods: {
    async getStatusList() {
      this.$set(this.searchConfig[4], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB' }))
      this.$set(this.$refs.searchForm.searchForm, 'selectType', 'all')
    },
    // 新增
    addHandle() {
      this.$router.push({
        path: '/project_task_visa/edit',
        query: {
          type: 'coop'
        }
      })
    },
    //  删除
    delHandle() {
      const req = {
        data: this.$refs.table.multipleSelection,
        delApi: delService,
        key: 'boId',
        sucCb: (res) => {
          if (res.code === '0000') {
            this.$message.success('删除成功')
          } else {
            this.$message.warning(res.data || '请稍后再试')
          }
          this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
          this.search(this.$refs?.searchForm?.searchForm || {})
        }
      }
      commonMultDel.call(this, req)
    },
    //
    /**
     * 点击操作列的按钮，处理或者查看单子
     * @param boId
     * @param operateType {String} deal: 处理 view: 查看
     */
    operateHandle({ boId }, operateType) {
      if (operateType === 'deal') { // 处理
        this.$router.push({
          path: '/project_task_visa/edit',
          query: {
            boId,
            type: 'coop'
          }
        })
      } else { // 查看
        this.$router.push({
          path: '/project_task_visa/view',
          query: {
            boId,
            type: 'coop'
          }
        })
      }
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    },
    // 是否可选
    canSelect(row) {
      if (row.isCurrentUserTask && row.status === 'draft') {
        return true
      } else {
        return false
      }
    },
  }
}
</script>
