/**
* @author: ty
* @date: 2023-07-04
* @description: 工程签证根据boid编辑或者是新增工单
*/
<template>
  <div v-loading="pageLoading" class="ProjectTaskVisaEditForm page-anchor-parentpage">
    <div class="operate-btn">
      <el-button v-if="boId" type="primary" @click="submit">提交审核</el-button>
      <el-button type="primary" @click="save('byBtn')">保存</el-button>
      <el-button v-if="exportDocVisible" type="primary" @click="exportVisaReport('doc')">导出签证报告</el-button>
      <el-button v-if="exportPdfVisible" type="primary" @click="exportVisaReport('pdf')">导出签证报告</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          :config="basicConfig"
          :label-position="labelPosition"
          :rules="basicRules"
          :form="basicForm"
        ></mssForm>

        <!--    签证提示的内容    -->
        <div class="common-form infos-wrapper">
          <form class="el-form el-form--label-top edit">
            <div class="el-row">
              <div class="el-col el-col-24 el-col-offset-0">
                <div class="el-form-item">
                  <label class="el-form-item__label">签证提示</label>
                  <div class="el-form-item__content el-textarea__inner">
                    <p>
                      1、一次签证的金额在3000元以下，且工程费用不突破整个项目预算的，由工程实施部门科室经理审批。
                    </p>
                    <p>
                      2、一次签证的金额在3000元至10000以下，且工程费用不突破整个项目预算的，由工程实施部门分管领导审批。
                    </p>
                    <p>
                      3、一次签证的金额在10000元以上，且工程费用不突破整个项目预算的，由工程实施部门领导审批。
                    </p>
                    <p>
                      4、如已签署的签证累计金额达到相应单项工程设计批复金额的1％（含1％）时，应报工程实施部门分管领导审批。
                    </p>
                    <p>
                      5、已签署的签证累计金额达到相应单项工程设计批复金额的3％（含3％）时，应报工程实施部门领导审批。
                    </p>
                    <p>
                      6、结算送审自动带出线上电子签证，19年及以后立项项目无法上传线下纸质签证。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </mssCard>

    <div id="sectionContract" class="page-anchor-point"></div>
    <mssCard title="合同信息">
      <div slot="content">
        <mssForm
          ref="contractForm"
          label-width="200px"
          :config="contractConfig"
          :label-position="labelPosition"
          :form="contractForm"
          :rules="basicRules"
        >
        </mssForm>
      </div>
    </mssCard>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="处理意见">
      <div slot="content">
        <mssForm
          ref="opForm"
          label-width="200px"
          :config="operationConfig"
          :label-position="labelPosition"
          :form="operationForm"
          :rules="basicRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="boId"
      :deal-page="dealPage"
      :bo-id="boId"
      :business-type="workflowCode"
      :file-type-flag="true"
      :node-name="currentNodeName"
    ></mssAttachment>

    <div id="sectionWorkFlowHistory" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <!-- 选择项目的弹窗   -->
    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      dialog-width="800px"
      row-key="projectId"
      :table-api="tableApi"
      :table-query-params="tableQueryParams"
      :search-fields="searchFieldList"
      :table-single-choice="true"
      :table-selected-data="tableSelectedData"
      :table-data-key="tableDataKey"
      :table-columns="tableColumns"
      @confirm="projectNameSearchConfirmHandle"
      @cancle="projectNameSearchCancleHandle"
    ></mssTableSearchDialog>

    <!--  站点选择的弹窗  -->
    <mssTableSearchDialog
      ref="wbsTaskNameSearchDialog"
      dialog-width="800px"
      dialog-title="选择站点"
      row-key="wbstaskId"
      :table-api="wbsTaskTableApi"
      :table-query-params="wbsTaskTableQueryParams"
      :search-fields="wbsTaskSearchFieldList"
      :table-single-choice="true"
      :table-selected-data="wbsTaskTableSelectedData"
      :table-data-key="wbsTaskTableDataKey"
      :table-columns="wbsTaskTableColumns"
      @confirm="wbsTaskSearchConfirmHandle"
      @cancle="wbsTaskSearchCancleHandle"
    ></mssTableSearchDialog>

    <!--  合同选择的弹窗  -->
    <mssTableSearchDialog
      ref="contractSearchDialog"
      dialog-width="900px"
      row-key="contractId"
      :table-api="contractTableApi"
      :table-query-params="contractTableQueryParams"
      :search-fields="contractSearchFieldList"
      :table-single-choice="true"
      :table-selected-data="contractTableSelectedData"
      :table-data-key="contractTableDataKey"
      :table-columns="contractTableColumns"
      @confirm="contractSearchConfirmHandle"
      @cancle="contractSearchCancleHandle"
    ></mssTableSearchDialog>

    <!--  监理单位选择的弹窗  -->
    <mssTableSearchDialog
      ref="supUnitSearchDialog"
      dialog-width="600px"
      :table-api="supUnitTableApi"
      :table-query-params="supUnitTableQueryParams"
      :search-fields="supUnitSearchFieldList"
      :table-single-choice="true"
      :table-selected-data="supUnitTableSelectedData"
      :table-data-key="supUnitTableDataKey"
      :table-columns="supUnitTableColumns"
      @confirm="supUnitSearchConfirmHandle"
      @cancle="supUnitSearchCancleHandle"
    ></mssTableSearchDialog>

    <!--  流程组件  -->
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="saveMainForm"
      @getNodeData="getNodeData"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
      @getHideList="getHideList"
    ></mssWorkFlowHandel>

    <mssChooseUser ref="chooseUser"
                   :mult-select="false"
                   @showCheckList="showCheckList">
    </mssChooseUser>

    <!-- 部门弹框 -->
    <mssChooseDept ref="chooseDept"
                   @showCheckList="showDeptCheckList">
    </mssChooseDept>
  </div>
</template>

<script>
import {
  initService,
  getListService,
  findProject4visaService,
  findtask4visaService,
  findContractService,
  getSuporgService,
  expVisaReportService,
  generateVisaReportService,
  refreshGrandTotalMoneyService,
  validService,
  saveService } from '@/api/engineering_management/projectTaskVisa.js'
import { commonDown } from '@/utils/btn'
export default {
  name: 'ProjectTaskVisaEditForm',
  data() {
    return {
      pageLoading: false,
      currentNodeName: '', // 当前所属环节name
      currentNodeCode: '', // 当前所属环节code
      boId: '',
      routerType: '',
      dealPage: true,
      workflowCode: 'ProjectTaskVisa',
      completeTaskUrl: '',
      returnAddress: '/project_task_visa/find_page_coop', // 写死？还是说等接口传入？
      // 项目选择弹窗的相关配置信息
      tableApi: findProject4visaService,
      tableQueryParams: {},
      searchFieldList: [
        {
          label: '项目名称',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          fieldName: 'projectCode'
        },
        {
          label: '建设单位名称',
          fieldName: 'deptName'// 老系统是'buildUnitName'
        }
      ],
      tableSelectedData: [], // 表格中已勾选的数据。数据格式与查询接口返回的数据格式一致
      tableDataKey: 'projectId', // 表格的查询接口返回的数据中表示唯一标识的字段
      tableColumns: [
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 200
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 100
        },
        {
          label: '建设单位名称',
          prop: 'deptName',
          minWidth: 200
        }
      ],

      // 站点选择弹窗的相关配置信息
      wbsTaskTableApi: findtask4visaService,
      wbsTaskTableQueryParams: {},
      wbsTaskSearchFieldList: [
        {
          label: '名称',
          fieldName: 'name'
        },
        {
          label: '编码',
          fieldName: 'code'
        }
      ],
      wbsTaskTableSelectedData: [], // 表格中已勾选的数据。数据格式与查询接口返回的数据格式一致
      wbsTaskTableDataKey: 'wbstaskId', // 表格的查询接口返回的数据中表示唯一标识的字段
      wbsTaskTableColumns: [
        {
          label: '站点名称',
          prop: 'wbstaskName',
          minWidth: 200
        },
        {
          label: '监理单位',
          prop: 'supdeptName',
          minWidth: 200
        },
        {
          label: '设计单位',
          prop: 'depdeptName',
          minWidth: 200
        },
        {
          label: '建设单位',
          prop: 'constructOrgName',
          minWidth: 100
        }
        // {
        //   label: '施工单位编码',
        //   prop: 'conDeptCode', // 任务查询结果中的施工的单位编码
        //   minWidth: 200
        // }
      ],

      // 合同选择弹窗的相关配置信息
      contractTableApi: findContractService,
      contractTableQueryParams: {},
      contractSearchFieldList: [
        {
          label: '合同名称',
          fieldName: 'contractName'
        },
        {
          label: '合同编码',
          fieldName: 'contractCode'
        }
      ],
      contractTableSelectedData: [], // 表格中已勾选的数据。数据格式与查询接口返回的数据格式一致
      contractTableDataKey: 'contractId', // 表格的查询接口返回的数据中表示唯一标识的字段
      contractTableColumns: [
        {
          label: '合同名称',
          prop: 'contractName',
          minWidth: 300
        },
        {
          label: '合同编码',
          prop: 'contractCode',
          minWidth: 300
        }
      ],

      // 监理单位选择弹窗的相关配置信息
      supUnitTableApi: getSuporgService,
      supUnitTableQueryParams: {},
      supUnitSearchFieldList: [],
      supUnitTableSelectedData: [], // 表格中已勾选的数据。数据格式与查询接口返回的数据格式一致
      supUnitTableDataKey: 'id', // 表格的查询接口返回的数据中表示唯一标识的字段
      supUnitTableColumns: [
        {
          label: '监理单位名称',
          prop: 'supUnitName',
          minWidth: 200
        }
      ],

      basicConfig: [
        {
          label: '工程签证工单名称',
          type: 'input',
          prop: 'name',
          span: 12,
          disabled: true// 工单名称自动生成，新建时根据所选项目来自动生成
        },
        {
          label: '工程签证工单编号',
          type: 'input',
          prop: 'code',
          span: 12,
          disabled: true
        },
        {
          label: '项目名称',
          type: 'input',
          prop: 'projectName',
          span: 12,
          eventListeners: {
            focus: this.openProjectSelectDialog// 打开选择项目的弹窗
          }
        },
        {
          label: '项目编码',
          type: 'input',
          prop: 'projectCode',
          span: 12,
          disabled: true
        },
        {
          label: '签证类型',
          type: 'select',
          prop: 'visaTypeId',
          options: [],
          span: 12,
          eventListeners: {
            change: this.visaTypeChanged// 打开站点选择的弹窗
          }
        },
        {
          label: '站点名称',
          type: 'input',
          prop: 'wbsTaskName',
          span: 12,
          eventListeners: {
            focus: this.openWbsTaskNameSelectDialog// 打开站点选择的弹窗
          }
        },
        {
          label: '建设单位：',
          type: 'input',
          prop: 'buildUnitName',
          span: 12,
          disabled: true
        },
        {
          label: '施工单位：',
          type: 'input',
          prop: 'conUnitName',
          span: 12
        },
        {
          label: '监理单位：',
          type: 'input',
          prop: 'supUnitName',
          span: 12,
          eventListeners: {
            focus: this.openSupUnitSelectDialog// 打开监理单位的选择弹窗
          }
        },
        {
          label: '签证申请金额（元）',
          type: 'input',
          mode: 'number',
          prop: 'applicationMoney',
          span: 12
        },
        {
          label: '签证确认金额（元）',
          type: 'input',
          mode: 'number',
          prop: 'visaMoney',
          span: 12
        },
        {
          label: '预计施工完成时间',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'planConstCompleteDate',
          span: 12
        },
        {
          label: '当前累计金额（元）（不含本次）',
          type: 'input',
          mode: 'number',
          prop: 'grandTotalMoney',
          span: 12,
          disabled: true
        },
        {
          label: '事由',
          type: 'input',
          mode: 'textarea',
          prop: 'cause',
          span: 24
        },
        {
          label: '实际签证内容',
          type: 'input',
          mode: 'textarea',
          prop: 'visaContent',
          span: 24
        },
        {
          label: '拟稿单位',
          type: 'input',
          prop: 'applyOrgName',
          span: 12,
          disabled: true
        },
        {
          label: '拟稿人',
          type: 'input',
          prop: 'creatorName',
          span: 12,
          disabled: true
        },
        {
          label: '拟稿时间',
          type: 'input',
          prop: 'createDate',
          span: 12,
          disabled: true
        }, {
          label: '备注',
          type: 'input',
          mode: 'textarea',
          prop: 'description',
          span: 24
        }
      ],
      basicRules: {
      },
      basicForm: {
        name: ''
      },
      contractForm: {
        contractName: ''
      },
      contractConfig: [
        {
          label: '合同名称',
          type: 'input',
          mode: 'textarea',
          prop: 'contractName',
          span: 24,
          eventListeners: {
            focus: this.openContractSelectDialog// 打开合同项目的弹窗
          }
        },
        {
          label: '合同编码',
          type: 'input',
          prop: 'contractCode',
          span: 12,
          disabled: true
        },
        {
          label: '合同标的',
          type: 'input',
          prop: 'contractSubject',
          span: 12
        }
      ],
      // 处理意见的字段配置
      operationConfig: [
        {
          label: '施工单位项目经理',
          type: 'input',
          prop: 'conUserName',
          span: 12,
        },
        {
          label: '填写时间',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'conUserDate',
          span: 12
        },
        {
          label: '施工单位意见',
          type: 'input',
          mode: 'textarea',
          prop: 'conUserOpinion',
          span: 24
        },
        {
          label: '监理单位项目经理',
          type: 'input',
          prop: 'supUserName',
          span: 12,
          // eventListeners: {
          //   focus: this.openUserDialogSupUser// 打开选人弹窗
          // }
        },
        {
          label: '填写时间',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'supUserDate',
          span: 12
        },
        {
          label: '总监理',
          type: 'input',
          prop: 'ssupUserName',
          span: 12
        },
        {
          label: '监理单位意见',
          type: 'input',
          mode: 'textarea',
          prop: 'supUserOpinion',
          span: 24
        },
        {
          label: '设计单位项目经理',
          type: 'input',
          prop: 'desUserName',
          span: 12,
          // eventListeners: {
          //   focus: this.openUserDialogDesUser// 打开选人弹窗
          // }
        },
        {
          label: '填写时间',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'desUserDate',
          span: 12
        },
        {
          label: '设计单位意见',
          type: 'input',
          mode: 'textarea',
          prop: 'desUserOpinion',
          span: 24
        },
        {
          label: '区县人员',
          type: 'input',
          prop: 'countyUserName',
          span: 12,
          // eventListeners: {
          //   focus: this.openUserDialogCountyUser// 打开选人弹窗
          // }
        },
        {
          label: '填写时间',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'countyUserDate',
          span: 12
        },
        {
          label: '区县人员意见',
          type: 'input',
          mode: 'textarea',
          prop: 'countyUserOpinion',
          span: 24
        },
        {
          label: '建设单位项目经理',
          type: 'input',
          prop: 'projectUserName',
          span: 12,
          // eventListeners: {
          //   focus: this.openUserDialogProjectUser// 打开选人弹窗
          // }
        },
        {
          label: '填写时间',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'projectUserDate',
          span: 12
        },
        {
          label: '建设单位意见',
          type: 'input',
          mode: 'textarea',
          prop: 'projectUserOpinion',
          span: 24
        },
        {
          label: '相关资料存放位置',
          type: 'input',
          mode: 'textarea',
          prop: 'dataPosition',
          span: 24
        }
      ],
      operationForm: {},
      labelPosition: 'top', // 编辑时在上方。仅查看着在左边
      saveMainForm: true// todo 感觉没有生效，是因为我的页面有多个表单的问题嘛？
    }
  },
  computed: {
    exportDocVisible() {
      return this.currentNodeCode === 'shigongqueren' && ['工程量签证', '特殊费用签证', '工程材料签证'].includes(this.basicForm.visaTypeName)
    },
    exportPdfVisible() {
      return (this.currentNodeCode === 'jianshedanweixiangmujingliqueren' || this.currentNodeCode === 'jieshu') &&
        ['工程量签证', '特殊费用签证', '工程材料签证'].includes(this.basicForm.visaTypeName)
    },
    pageAnchorConfig() {
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '合同<br>信息',
          id: 'sectionContract'
        },
        {
          text: '处理<br>意见',
          id: 'sectionOperation'
        },
        {
          text: '附件',
          id: 'sectionAttachment',
          show: !!this.boId
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlowHistory',
          show: !!this.boId

        }
      ]
    },
  },
  watch: {
    'currentNodeCode': {
      handler(n, o) {
        if (this.$refs.basicForm) {
          const visaTypeId = this.$refs.basicForm.modelForm.visaTypeId
          if (visaTypeId !== '3' && n === 'shigongdanweiqueren') {
            this.$set(this.basicConfig[10], 'disabled', true)// 签证确认金额的下标是10
            this.$refs.basicForm.modelForm.visaMoney = ''
          }
        }
      },
      immediate: true
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId || null
    this.routerType = urlQuery.type || ''// 如果type为coop。表示是从list_cooperate.vue页面来的
    // 如果没有boid,表示是新增。有boid是编辑修改
    this.pageLoading = true
    await this.initTaskVisa()
    this.$set(this.basicConfig[4], 'options', await this.getVisaTypeList())
    this.pageLoading = false
    this.$refs.workFlow.init()
  },
  methods: {
    setValueToForms(resData = {}){
      this.basicForm = Object.assign({}, this.basicForm, resData || {})
      this.contractForm = {
        contractName: resData.contractName || '',
        contractCode: resData.contractCode || '',
        contractSubject: resData.contractSubject || ''
      }
      this.operationForm = {
        conUserName: resData.conUserName || '',
        conUserDate: resData.conUserDate || '',
        conUserOpinion: resData.conUserOpinion || '',

        supUserName: resData.supUserName || '',
        supUserDate: resData.supUserDate || '',
        ssupUserName: resData.ssupUserName || '',
        supUserOpinion: resData.supUserOpinion || '',

        desUserName: resData.desUserName || '',
        desUserDate: resData.desUserDate || '',
        desUserOpinion: resData.desUserOpinion || '',

        countyUserName: resData.countyUserName || '',
        countyUserDate: resData.countyUserDate || '',
        countyUserOpinion: resData.countyUserOpinion || '',

        projectUserName: resData.projectUserName || '',
        projectUserDate: resData.projectUserDate || '',
        projectUserOpinion: resData.projectUserOpinion || '',

        dataPosition: resData.dataPosition || ''
      }
    },
    initTaskVisa() {
      return new Promise((resolve, reject) => {
        initService({ boId: this.boId })
          .then(res => {
            if (res.code === '0000') {
              this.setValueToForms(res.data)
              this.completeTaskUrl = res.data.completeTaskUrl
            }
          })
          .finally(_ => {
            resolve()
          })
      })
    },
    getVisaTypeList() {
      return new Promise((resolve, reject) => {
        getListService()
          .then(res => {
            if (res.code === '0000') {
              const newList = res.data.visaTypes.map(it => {
                return {
                  value: it.id,
                  label: it.name
                }
              })
              resolve(newList)
            }
          })
          .finally(_ => {
            resolve()
          })
      })
    },
    // 打开选择项目的弹窗
    openProjectSelectDialog() {
      const list = ['开始', '拟稿', '施工单位负责人']
      // 空串代表开始环节
      if (this.currentNodeName && !list.includes(this.currentNodeName)) {
        return ''
      }
      this.tableQueryParams = {
        providerNum: this.basicForm.providerNum
      }
      this.$nextTick(_ => {
        this.$refs.projectNameSearchDialog.openDialog()
      })
    },
    // 项目选择的弹窗点了确定
    projectNameSearchConfirmHandle(data) {
      const projectNames = []
      const projectCodes = []
      const projectIds = []
      const deptIds = []
      const deptNames = []
      data.forEach(it => {
        projectNames.push(it.projectName)
        projectCodes.push(it.projectCode)
        projectIds.push(it.projectId)
        deptIds.push(it.deptId)
        deptNames.push(it.deptName)
      })
      this.basicForm = JSON.parse(JSON.stringify(this.$refs.basicForm.modelForm))
      this.$set(this.basicForm, 'projectName', projectNames.join())// 项目名称
      this.$set(this.basicForm, 'projectCode', projectCodes.join())// 项目编码
      this.$set(this.basicForm, 'projectId', projectIds.join())// 项目id
      this.$set(this.basicForm, 'buildUnitId', deptIds.join())// 建设单位id
      this.$set(this.basicForm, 'buildUnitName', deptNames.join())// 建设单位名称

      // 根据所选项目，生成工单名称
      this.$set(this.basicForm, 'name', this.generateName(projectNames.join()))

      // 检查新选择的项目是否和上次选的项目相同，如果不同，则需要清空站点、合同
      const currentSelectedProjectId = this.$refs.basicForm.modelForm.projectId
      if(currentSelectedProjectId !== projectIds.join()){// 因为项目选择是单选，所以简单判断即可
        this.$nextTick(_=>{
          this.wbsTaskSearchConfirmHandle([])
          this.contractSearchConfirmHandle([])
        })

      }

      this.tableSelectedData = JSON.parse(JSON.stringify(data || []))
    },
    generateName(projectNames){
      return `关于${projectNames}的工程签证`
    },

    projectNameSearchCancleHandle() {},
    // 打开站点选择的弹窗
    openWbsTaskNameSelectDialog() {
      const list = ['开始', '拟稿', '施工单位负责人']
      // 空串代表开始环节
      if (this.currentNodeName && !list.includes(this.currentNodeName)) {
        return ''
      }
      if (!this.basicForm.projectId) {
        this.$message.warning('请先选择项目!')
        return
      }
      this.wbsTaskTableQueryParams = {
        providerNum: this.basicForm.providerNum,
        projectCode: this.basicForm.projectCode,
        projectId: this.basicForm.projectId,
        buildUnitId: this.basicForm.buildUnitId//  建设单位id
      }
      this.$nextTick(_ => {
        this.$refs.wbsTaskNameSearchDialog.openDialog()
      })
    },
    // 站点选择弹窗点击了确定
    wbsTaskSearchConfirmHandle(data) {
      const wbstaskNames = []// 站点名称
      const wbstaskIds = []// 站点名称

      const supdeptIds = []// 监理单位名称
      const supdeptNames = []// 监理单位名称

      const depdeptNames = []// 设计单位名称
      const depdeptIds = []// 设计单位名称

      const contractIds = []
      const contractNames = []
      const contractCodes = []

      // conUnitName 施工单位名称
      // conUnitId 施工单位ID
      data.forEach(it => {
        wbstaskNames.push(it.wbstaskName)
        wbstaskIds.push(it.wbstaskId)
        supdeptIds.push(it.supdeptId)
        supdeptNames.push(it.supdeptName)

        depdeptNames.push(it.depdeptName)
        depdeptIds.push(it.dedeptId)

        if (it.contractId) {
          contractIds.push(it.contractId)
          contractNames.push(it.contractName)
          contractCodes.push(it.contractCode)
        }
      })

      this.basicForm = JSON.parse(JSON.stringify(this.$refs.basicForm.modelForm))
      this.$set(this.basicForm, 'wbsTaskName', wbstaskNames.join())// 站点名称
      this.$set(this.basicForm, 'wbsTaskId', wbstaskIds.join())// 站点id

      this.$set(this.basicForm, 'desUnitName', depdeptNames.join())// 设计单位名称
      this.$set(this.basicForm, 'desUnitId', depdeptIds.join())// 设计单位id

      this.$set(this.basicForm, 'supUnitId', supdeptIds.join())// 监理单位
      this.$set(this.basicForm, 'supUnitName', supdeptNames.join())// 监理单位

      // 合同id
      // 合同code
      // 合同名称
      this.contractForm = JSON.parse(JSON.stringify(this.$refs.contractForm.modelForm))
      this.$set(this.contractForm, 'contractId', contractIds.join())
      this.$set(this.contractForm, 'contractCode', contractCodes.join())
      this.$set(this.contractForm, 'contractName', contractNames.join())

      this.wbsTaskTableSelectedData = JSON.parse(JSON.stringify(data || []))
    },
    wbsTaskSearchCancleHandle() {},

    // 打开合同选择的弹窗
    openContractSelectDialog() {
      const list = ['开始', '拟稿', '施工单位负责人']
      // 空串代表开始环节
      if (this.currentNodeName && !list.includes(this.currentNodeName)) {
        return ''
      }
      if (!this.basicForm.projectId) {
        this.$message.warning('请先选择项目!')
        return
      }

      this.contractTableQueryParams = {
        providerNum: this.basicForm.providerNum,
        projectCode: this.basicForm.projectCode,
        projectId: this.basicForm.projectId
      }
      this.$nextTick(_ => {
        this.$refs.contractSearchDialog.openDialog()
      })
    },

    // 合同选择弹窗点击了确定
    contractSearchConfirmHandle(data) {
      const ids = []
      const names = []
      const codes = []
      data.forEach(it => {
        ids.push(it.contractId)
        names.push(it.contractName)
        codes.push(it.contractCode)
      })
      this.contractForm = JSON.parse(JSON.stringify(this.$refs.contractForm.modelForm))
      this.$set(this.contractForm, 'contractId', ids.join())
      this.$set(this.contractForm, 'contractCode', codes.join())
      this.$set(this.contractForm, 'contractName', names.join())
    },
    contractSearchCancleHandle() {},

    // 点击【监理单位】的输入框， 打开监理单位的选择弹窗
    // 查询参数 wbsTaskId
    openSupUnitSelectDialog() {
      // const list = ['开始', '拟稿', '施工单位负责人']
      // // 空串代表开始环节
      // if (this.currentNodeName && !list.includes(this.currentNodeName)) {
      //   return ''
      // }
      // if (!this.basicForm.wbsTaskId) {
      //   this.$message.warning('请先选择站点!')
      //   return
      // }
      // this.supUnitTableQueryParams = {
      //   wbsTaskId: this.basicForm.wbsTaskId// 站点id
      // }
      // this.$nextTick(_ => {
      //   this.$refs.supUnitSearchDialog.openDialog()
      // })
      this.$refs.chooseDept.init([], {conditionType: 'completionSupervisionDept' })
    },
    // 监理单位选择的弹窗点了确定
    supUnitSearchConfirmHandle(data) {
      const ids = []
      const names = []
      data.forEach(it => {
        ids.push(it.supUnitId)
        names.push(it.supUnitName)
      })
      this.basicForm = JSON.parse(JSON.stringify(this.$refs.basicForm.modelForm))
      this.$set(this.basicForm, 'supUnitId', ids.join())
      this.$set(this.basicForm, 'supUnitName', names.join())
    },

    showDeptCheckList({ checkList }) {
      const ids = []
      const names = []
      checkList.forEach(it => {
        ids.push(it.id)
        names.push(it.text)
      })
      this.basicForm = JSON.parse(JSON.stringify(this.$refs.basicForm.modelForm))
      this.$set(this.basicForm, 'supUnitId', ids.join())
      this.$set(this.basicForm, 'supUnitName', names.join())

    },

    supUnitSearchCancleHandle() {},

    // 但凡一个表单有校验失败，都算失败。
    validateFormRules() {
      let basicFormValidation = ''
      let contractFormValidation = ''
      let opFormValidation = ''

      this.$refs.opForm.$refs.form.validateScroll((valid) => {
        if (!valid) { opFormValidation = '处理意见表单' }
      })
      this.$refs.contractForm.$refs.form.validateScroll((valid) => {
        if (!valid) { contractFormValidation = '合同信息表单' }
      })
      this.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (!valid) { basicFormValidation = '基本信息表单' }
      })

      if (basicFormValidation || contractFormValidation || opFormValidation){
        return `${basicFormValidation} ${contractFormValidation} ${opFormValidation}: 校验失败，请检查表单`
      } else {
        return ''
      }
    },

    // 保存
    save(cb) {
      // 校验页面表单。如果校验失败，有具体的失败信息提示。没有的话表示校验成功
      const formsValidationErrorMsg = this.validateFormRules()
      if (formsValidationErrorMsg) { return false }

      if (this.$refs.basicForm.modelForm.visaTypeId !== '3' && this.currentNodeCode === 'shigongdanweiqueren') {
        this.$set(this.basicConfig[10], 'disabled', true)// 签证确认金额的下标是10
        this.$refs.basicForm.modelForm.visaMoney = ''
      }
      this.pageLoading = true
      const param = {
        ...this.$refs.basicForm.modelForm,
        ...this.$refs.contractForm.modelForm,
        ...this.$refs.opForm.modelForm
      }
      saveService(param)
        .then(res => {
          if (res.code === '0000') {
            this.boId = res.data.boId
            this.setValueToForms(res.data)
            if (cb === 'byBtn') {
              const req = {
                ...this.$route.query,
                boId: this.boId,
                _t: new Date().getTime()
              }
              // this.$router.replace({ query: req })
              let newToPath = this.$route.path + '?'
              for(let key in req){
                newToPath += `${key}=${req[key]}&`
              }
              // 移除最后一个&
              newToPath = newToPath.substr(0, newToPath.length - 1)
              this.$closeTagView({
                close: this.$route.fullPath,
                to: newToPath
              })
            }

            if (cb && cb.constructor === Function) {
              cb(param)
            } else {
              this.$message({
                type: 'success',
                message: '保存成功！'
              })
            }
          }
        })
        .finally(_ => {
          this.pageLoading = false
        })
    },
    // 导出签证报告
    exportVisaReport(type) {
      if (type === 'pdf') {
        commonDown({ boId: this.boId }, generateVisaReportService)
      } else if (type === 'doc') {
        commonDown({ boId: this.boId }, expVisaReportService)
      }
    },
    getNodeData(data) {
      this.currentNodeName = data.nodeName
      this.currentNodeCode = data.nodeCode
      this.$nextTick(_=>{
        this.setMoneyDisable(this.basicForm.visaTypeId)
      })
    },
    beforeNode() {
      const fileNameArr = {
        // 设计单位负责人
        shejidanweifuzeren: {
          userName: this.basicForm.desInterFaceUserName,
          userId: this.basicForm.desInterFaceUserId
        },
        // 建设单位项目经理
        jianshedanweixiangmujingli: {
          userName: this.basicForm.projectManagerName,
          userId: this.basicForm.projectManagerId
        },
        // 监理单位负责人
        jianlidanweifuzeren: {
          userName: this.basicForm.supInterFaceUserName,
          userId: this.basicForm.supInterFaceUserId
        }
      }
      // 设置默认处理人
      return fileNameArr
    },
    initFormRules(rules) {
      if (rules) {
        this.basicRules = rules
        this.$nextTick(_ => {
          this.$refs.basicForm.$refs.form.clearValidate()
          this.$refs.contractForm.$refs.form.clearValidate()
          this.$refs.opForm.$refs.form.clearValidate()
        })
      }
    },
    // 只读表单
    getReadonlyList(param) {
      if (param && param.length) {
        [].concat(this.basicConfig, this.contractConfig, this.operationConfig).forEach(item => {
          if (param.includes(item.prop)) {
            item.disabled = true
          }
        })
      }
    },
    // 隐藏表单
    getHideList() {},
    // 提交
    submit() {
      // 校验页面表单。如果校验失败，有具体的失败信息提示。没有的话表示校验成功
      // const formsValidationErrorMsg = this.validateFormRules()
      // if (formsValidationErrorMsg) { return false }

      this.$refs.workFlow.opendialogInitNextPath()
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.name
      }
      const isSpecialVisa = {
        code: 'isSpecialVisa',
        value: this.$refs.basicForm.modelForm.isSpecialVisa
      }

      const isHasSupervisor = {
        code: 'isHasSupervisor',
        value: this.$refs.basicForm.modelForm.isHasSupervisor
      }
      const bigger = {
        code: 'bigger',
        value: this.$refs.basicForm.modelForm.bigger
      }

      const greater = {
        code: 'greater',
        value: this.$refs.basicForm.modelForm.greater
      }

      workFlowPrams.push(name, isSpecialVisa, isHasSupervisor,bigger,greater)
      return workFlowPrams
    },

    // 更新累计金额(字段对应的页面中文：当前累计金额（元）（不含本次）)
    refreshGrandTotalMoney() {
      return new Promise(resolve => {
        refreshGrandTotalMoneyService({ boId: this.boId })
          .then(res => {
            if (res.code === '0000') {
              const grandTotalMoney = res.data.grandTotalMoney
              this.basicForm = JSON.parse(JSON.stringify(this.$refs.basicForm.modelForm))
              this.$set(this.basicForm, 'grandTotalMoney', grandTotalMoney)// 当前累计金额（元）（不含本次）
              resolve(res?.data?.msg || '')
            } else {
              resolve(res?.data?.msg || '更新失败')
            }
          })
          .catch(e => {
            resolve(e.msg || '更新失败')
          })
      })
    },
    valid() {
      return new Promise(resolve => {
        validService({ boId: this.boId,currentNodeName: this.currentNodeCode })
          .then(res => {
            if (res.data && res.data.success != 'true') {
              resolve(res.data.msg || '附件校验异常！')
            } else {
              resolve('')
            }
          })
          .catch(e => {
            resolve(e.msg || '附件校验异常！')
          })
      })
    },
    async beforeSubmit() {
      // todo
      // // 先进行页面上的规则校验（不涉及到后端接口的，纯页面上的校验）
      const validationFlagOnPage = await this.validateFormRules()
      // // 假如有报错文字，就直接显示报错，不要进行后续校验了
      if(validationFlagOnPage){
        return validationFlagOnPage
      }
      const moneyReg = /^(\d+)|(\d+.\d+)$/;
      const str = ''
      // 更新数据库中的累计金额，同时反映页面上
      const msg = await this.refreshGrandTotalMoney()
      // 假如有报错文字，就直接显示报错，不要进行后续校验了
      if(msg){
        return msg
      }
      // 假如更新金额成功，再进行后续校验
      const currentNodeCode = this.currentNodeCode
      const visaTypeId = this.$refs.basicForm.modelForm.visaTypeId
      const visaMoney = this.$refs.basicForm.modelForm.visaMoney // 签证确认金额
      const applicationMoney = this.$refs.basicForm.modelForm.applicationMoney // 签证申请金额
      const visaContent = this.$refs.basicForm.modelForm.visaContent // 实际签证内容
      if(!currentNodeCode || ['kaishi', 'shigongdanweifuzeren', 'shigongqueren'].includes(currentNodeCode)){

        const msg = await this.valid()
        if (msg) {
          return msg
        }
        // 签证类型为特殊签证时，要对签证申请金额和签证确认金额校验
        if(visaTypeId === '3'){
          if(!currentNodeCode || ['kaishi', 'shigongdanweifuzeren'].includes(currentNodeCode)){
            if(!applicationMoney){
              return '请填写签证申请金额(元)！'
            }else if(!moneyReg.test(applicationMoney)){
              return '请输入正确的签证申请金额！'
            }else if(applicationMoney <= 0){
              return '签证申请金额必须大于0！'
            }
          }else{
            if(!visaMoney){// 校验签证确认金额是否填写、是否超签证申请金额
              return '请填写签证确认金额(元)！'
            }else if(!moneyReg.test(visaMoney)){
              return '请输入正确的签证金额！'
            }else if(Number(visaMoney) <= 0){
              return '签证确认金额必须大于0！'
            }else if(applicationMoney && applicationMoney > 0 && (visaMoney - applicationMoney) > 0){
              return '签证确认金额不能大于签证申请金额！'
            }
          }
        }else{
          if(currentNodeCode === 'shejiqueren'){
            if(!visaMoney){// 校验签证确认金额是否填写、是否超签证申请金额
              return '请填写签证确认金额(元)！'
            }
          }
        }
        if(currentNodeCode == 'shigongqueren'){
          if(!visaContent){
            return '请填写实际签证内容！'
          }
        }
      }else if(['shejidanweifuzeren', 'shejiqueren'].includes(currentNodeCode)){
        // 当签证类型不为特殊费用签证时，设计单位负责人环节，要填写签证确认金额，设计单位确认环节要填写签证申请金额
        if(visaTypeId && visaTypeId != '3'){
          if(currentNodeCode == 'shejidanweifuzeren'){
            if(applicationMoney == ''){
              return '请填写签证申请金额(元)！'
            }else if(!moneyReg.test(applicationMoney)){
              return '请输入正确的签证申请金额！'
            }else if(Number(applicationMoney) <= 0){
              return '签证申请金额必须大于0！'
            }
          }else if(currentNodeCode == 'shejiqueren'){
            if(visaMoney == ''){// 校验签证确认金额是否填写、是否超签证申请金额
              return '请填写签证确认金额(元)！'
            }else if(!moneyReg.test(visaMoney)){
              return '请输入正确的签证金额！'
            }else if(Number(visaMoney) <= 0){
              return '签证确认金额必须大于0！'
            }else if(applicationMoney != '' && applicationMoney > 0 && (visaMoney - applicationMoney) > 0){
              return '签证确认金额不能大于签证申请金额！'
            }
          }
        }
      }
      return ''
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.routerType === 'coop' ? '/project_task_visa/find_page_coop' : '/project_task_visa/find_page'
      })
    },
    setMoneyDisable(visaTypeVal){
      const currentNodeCode = this.currentNodeCode
      // 特殊费用签证 是 3
      if(visaTypeVal === '3'){
        this.$set(this.basicConfig[9], 'disabled', true)// 签证申请金额(applicationMoney)的下标是9
        // this.$set(this.basicForm, 'applicationMoney', '')// 签证申请金额(applicationMoney)的下标是9

        this.$set(this.basicConfig[10], 'disabled', true)// 签证确认金额(visaMoney)的下标是10
        // this.$set(this.basicForm, 'visaMoney', '')// 签证确认金额(visaMoney)的下标是10

        if (currentNodeCode === 'shigongdanweifuzeren'){
          this.$set(this.basicConfig[9], 'disabled', false)// 签证申请金额的下标是9
        }else if(currentNodeCode === 'shigongqueren'){
          this.$set(this.basicConfig[10], 'disabled', false)// 签证确认金额的下标是10
        }
      }else{
        this.$set(this.basicConfig[9], 'disabled', true)// 签证申请金额(applicationMoney)的下标是9
        // this.$set(this.basicForm, 'applicationMoney', '')// 签证申请金额(applicationMoney)的下标是9

        this.$set(this.basicConfig[10], 'disabled', true)// 签证确认金额(visaMoney)的下标是10
        // this.$set(this.basicForm, 'visaMoney', '')// 签证确认金额(visaMoney)的下标是10

        if(currentNodeCode === 'shejidanweifuzeren'){
          this.$set(this.basicConfig[9], 'disabled', false)// 签证申请金额的下标是9
        }else if(currentNodeCode === 'shigongqueren'){
          this.$set(this.basicConfig[10], 'disabled', false)// 签证确认金额的下标是10
        }else if(currentNodeCode === 'shejiqueren'){
          this.$set(this.basicConfig[10], 'disabled', false)// 签证确认金额的下标是10
        }
      }
    },
    // 签证类型更改了
    visaTypeChanged(newVal) {
      this.setMoneyDisable(newVal)
    },
    openUserDialogConUser(){
      this.dialogUserType = 'conUser'
      this.openUserDialog()
    },
    openUserDialogSupUser(){
      this.dialogUserType = 'supUser'
      this.openUserDialog()
    },
    openUserDialogDesUser(){
      this.dialogUserType = 'desUser'
      this.openUserDialog()
    },
    openUserDialogCountyUser(){
      this.dialogUserType = 'countyUser'
      this.openUserDialog()
    },
    openUserDialogProjectUser(){
      this.dialogUserType = 'projectUser'
      this.openUserDialog()
    },
    openUserDialog(){
      const userId = this.basicForm.creatorId || ''
      const realName = this.basicForm.creatorName || ''
      let personnal = []
      if (userId) {
        personnal.push({ userId, realName })
      }
      this.$refs.chooseUser.init(personnal)
    },
    showCheckList({ checkList }) {
      let ids = []
      let names = []
      checkList.forEach(it => {
        ids.push(it.userId)
        names.push(it.realName)
      })

      this.operationForm = JSON.parse(JSON.stringify(this.$refs.opForm.modelForm))
      this.$set(this.operationForm, this.dialogUserType + 'Id', ids.join())
      this.$set(this.operationForm, this.dialogUserType + 'Name', names.join())
    },
  }
}
</script>

<style lang="scss" scoped>
.infos-wrapper {
  .el-textarea__inner{
    border: none;
  }
  p{
    margin: 0;
  }
}
</style>
