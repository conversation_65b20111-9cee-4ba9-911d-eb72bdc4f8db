/**
* @author: ty
* @date: 2023-07-03
* @description: 工程签证（移动）
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :autoCall="false"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import {findPageService, exportFindPageService} from '@/api/engineering_management/projectTaskVisa.js'
import {commonDown} from "@/utils/btn";
export default {
  name: 'ProjectTaskVisaFindPage',
  data() {
    const validateCreateStartEndDate = (rule, value, callback) => {
      if(this.$refs.searchForm.searchForm.createDateBegin && this.$refs.searchForm.searchForm.createDateEnd){
        if(new Date(this.$refs.searchForm.searchForm.createDateBegin).getTime() > new Date(this.$refs.searchForm.searchForm.createDateEnd).getTime()){
          callback(new Error('开始日期需小于结束日期'))
        }else{
          callback()
        }
      }else{
        callback()
      }
    }
    const validateFinishStartEndDate = (rule, value, callback) => {
      if(this.$refs.searchForm.searchForm.finishDateBegin && this.$refs.searchForm.searchForm.finishDateEnd){
        if(new Date(this.$refs.searchForm.searchForm.finishDateBegin).getTime() > new Date(this.$refs.searchForm.searchForm.finishDateEnd).getTime()){
          callback(new Error('开始日期需小于结束日期'))
        }else{
          callback()
        }
      }else{
        callback()
      }
    }
    return {
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName',
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode',
        },
        // {
        //   label: '建设单位',
        //   type: 'input',
        //   fieldName: 'constructOrgName',// 注意：prop是不对的哦
        // },
        {
          label: '站点',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '签证编号',
          type: 'input',
          fieldName: 'visacode'
        },
        {
          label: '当前状态',
          type: 'select',
          options: [],
          fieldName: 'selectType'
        },
        // { todo del?
        //   label: '工程签证名称',
        //   type: 'input',
        //   fieldName: 'visaname'
        // },
        {
          label: '签证发起时间',
          type: 'date2',
          fieldName: 'createDateBegin',
          fieldName2: 'createDateEnd',
          span: 12,
          rules: [
            { validator: validateCreateStartEndDate, trigger: ['blur', 'change'] },
          ]
        },
        {
          label: '签证归档时间',
          type: 'date2',
          fieldName: 'finishDateBegin',
          fieldName2: 'finishDateEnd',
          span: 12,
          rules: [
            { validator: validateFinishStartEndDate, trigger: ['blur', 'change'] },
          ]
        },
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '签证报告编码',
          prop: 'visacode',
          minWidth: 200,
          tooltip: true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true
        },{
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true
        },{
          label: '站点',
          prop: 'taskName',
          tooltip: true
        },
        {
          label: '建设单位',
          prop: 'constructOrgName',
          tooltip: true
        },{
          label: '承包单位',
          prop: 'conUnitName',
          tooltip: true
        },{
          label: '监理单位',
          prop: 'supUnitName',
          tooltip: true
        },
        {
          label: '签证发起时间',
          prop: 'createDate',
          minWidth: 100,
          tooltip: true
        },{
          label: '完成时间',
          prop: 'finishDate',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '签证申请金额',
          prop: 'applicationMoney',
          minWidth: 100,
          tooltip: true
        },{
          label: '签证确认金额',
          prop: 'visaMoney',
          minWidth: 100,
          tooltip: true
        },{
          label: '签证类型',
          prop: 'visaTypeName',
          tooltip: true
        },
        {
          label: '当前处理人',
          prop: 'userName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '当前状态',
          prop: 'chineseStatus',
          tooltip: true
        },
        {
          label: '操作',
          prop: '_operationCol',
          fixed: 'right',
          formatter: row => {
            return (
              <span>
                {
                  row.isCurrentUserTask
                     ? <a href='javascript:;' class='mr10' onClick={() => {
                       this.operateHandle(row, 'deal')
                     }}
                     >处理</a>
                     : ''
                 }
                <a href='javascript:;' onClick={() => {
                  this.operateHandle(row, 'view')
                }}
                >查看</a>
              </span>
            )
          }
        }
      ]
    }
  },
  async created() {
    await this.getStatusList()
    this.search()
  },
  methods: {
    async getStatusList() {
      // 下标4是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      const list = await this.$dictOptions({parentValue: "wrokStatusAll",appCode: "PUB"});
      this.$set(this.searchConfig[4], 'options', list)
      this.$set(this.$refs.searchForm.searchForm, 'selectType', 'all')
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({isCurrentUserTask, boId}, operateType){
      if (operateType === 'deal') {// 处理
        this.$router.push({
          path: '/project_task_visa/edit',
          query: {
            boId
          }
        })
      } else { // 查看
        this.$router.push({
          path: '/project_task_visa/view',
          query: {
            boId
          }
        })
      }
    },
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData);
    },
    reset(searchData) {
      this.search()
    },
    // 导出
    exportHandle() {
      commonDown(this.staticSearchParam, exportFindPageService)
    }
  }
}
</script>
