/**
* @author: ty
* @date: 2023-07-04
* @description: 工程签证根据boid查看工单详情的页面
*/
<template>
  <div class="ProjectTaskVisaViewForm page-anchor-parentpage">
    <div class="operate-btn">
      <el-button v-if="exportPdfVisible" type="primary" @click="generateVisaReport">导出签证报告</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point" />
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm label-width="240px" disable-form :config="basicConfig" :label-position="labelPosition" :form="basicForm" />
        <!--    签证提示的内容    -->
        <div class="common-form infos-wrapper">
          <form class="el-form el-form--label-left detail">
            <div class="el-row">
              <div class="el-col el-col-24 el-col-offset-0">
                <div class="el-form-item">
                  <label class="el-form-item__label">签证提示：</label>
                  <div class="el-form-item__content detail content-input">
                    <p>
                      1、一次签证的金额在3000元以下，且工程费用不突破整个项目预算的，由工程实施部门科室经理审批。
                    </p>
                    <p>
                      2、一次签证的金额在3000元至10000以下，且工程费用不突破整个项目预算的，由工程实施部门分管领导审批。
                    </p>
                    <p>
                      3、一次签证的金额在10000元以上，且工程费用不突破整个项目预算的，由工程实施部门领导审批。
                    </p>
                    <p>
                      4、如已签署的签证累计金额达到相应单项工程设计批复金额的1％（含1％）时，应报工程实施部门分管领导审批。
                    </p>
                    <p>
                      5、已签署的签证累计金额达到相应单项工程设计批复金额的3％（含3％）时，应报工程实施部门领导审批。
                    </p>
                    <p>
                      6、结算送审自动带出线上电子签证，19年及以后立项项目无法上传线下纸质签证。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </mssCard>

    <div id="sectionContract" class="page-anchor-point" />
    <mssCard title="合同信息">
      <div slot="content">
        <mssForm label-width="240px" disable-form :config="contractConfig" :label-position="labelPosition" :form="basicForm" />
      </div>
    </mssCard>

    <div id="sectionOperation" class="page-anchor-point" />
    <mssCard title="处理意见">
      <div slot="content">
        <mssForm label-width="240px" disable-form :config="operationConfig" :label-position="labelPosition" :form="basicForm" />
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point" />
    <mssAttachment
      :bo-id="boId"
      :deal-page="dealPage"
      :business-type="workflowCode" />

    <div id="sectionWorkFlowHistory" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig" />
  </div>
</template>

<script>
import {viewService, generateVisaReportService } from '@/api/engineering_management/projectTaskVisa.js'
import { commonDown } from '@/utils/btn'
export default {
  name: 'ProjectTaskVisaViewForm',
  data() {
    return {
      boId: '',
      routerType: '',
      dealPage: false,
      workflowCode: 'ProjectTaskVisa',
      basicConfig: [
        {
          label: '工程签证工单名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '工程签证工单编号：',
          type: 'input',
          prop: 'code',
          span: 12
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          span: 12
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          span: 12
        },
        {
          label: '签证类型：',
          type: 'input',
          prop: 'visaTypeName',
          span: 12
        },
        {
          label: '站点名称：',
          type: 'input',
          prop: 'wbsTaskName',
          span: 12
        },
        {
          label: '建设单位：',
          type: 'input',
          prop: 'buildUnitName',
          span: 12
        },
        {
          label: '施工单位：',
          type: 'input',
          prop: 'conUnitName',
          span: 12
        },
        {
          label: '监理单位：',
          type: 'input',
          prop: 'supUnitName',
          span: 12
        },
        {
          label: '签证申请金额（元）：',
          type: 'input',
          prop: 'applicationMoney',
          span: 12
        },
        {
          label: '签证确认金额（元）：',
          type: 'input',
          prop: 'visaMoney',
          span: 12
        },
        {
          label: '预计施工完成时间：',
          type: 'input',
          prop: 'planConstCompleteDate',
          span: 12
        },
        {
          label: '当前累计金额（元）（不含本次）：',
          type: 'input',
          prop: 'grandTotalMoney',
          span: 12
        },
        {
          label: '事由：',
          type: 'input',
          mode: 'textarea',
          prop: 'cause',
          span: 24
        },
        {
          label: '实际签证内容：',
          type: 'input',
          mode: 'textarea',
          prop: 'visaContent',
          span: 24
        },
        {
          label: '拟稿单位：',
          type: 'input',
          prop: 'applyOrgName',
          span: 12
        },
        {
          label: '拟稿人：',
          type: 'input',
          prop: 'creatorName',
          span: 12
        }, {
          label: '拟稿时间：',
          type: 'input',
          prop: 'createDate',
          span: 12
        }, {
          label: '备注：',
          type: 'input',
          mode: 'textarea',
          prop: 'description',
          span: 24
        }
      ],
      basicForm: {
        name: ''
      },
      contractConfig: [
        {
          label: '合同名称：',
          type: 'input',
          mode: 'textarea',
          prop: 'contractName',
          span: 24
        },
        {
          label: '合同编码：',
          type: 'input',
          prop: 'contractCode',
          span: 12,
          disabled: true
        },
        {
          label: '合同标的：',
          type: 'input',
          prop: 'contractSubject',
          span: 12
        }
      ],
      // 处理意见的字段配置
      operationConfig: [
        {
          label: '施工单位项目经理：',
          type: 'input',
          prop: 'conUserName',
          span: 12
        },
        {
          label: '填写时间：',
          type: 'input',
          prop: 'conUserDate',
          span: 12
        },
        {
          label: '施工单位意见：',
          type: 'input',
          mode: 'textarea',
          prop: 'conUserOpinion',
          span: 24
        },
        {
          label: '监理单位项目经理：',
          type: 'input',
          prop: 'supUserName',
          span: 12
        },
        {
          label: '填写时间：',
          type: 'input',
          prop: 'supUserDate',
          span: 12
        },
        {
          label: '监理单位意见：',
          type: 'input',
          mode: 'textarea',
          prop: 'supUserOpinion',
          span: 24
        },
        {
          label: '设计单位项目经理：',
          type: 'input',
          prop: 'desUserName',
          span: 12
        },
        {
          label: '设计单位意见：',
          type: 'input',
          mode: 'textarea',
          prop: 'desUserOpinion',
          span: 24
        },
        {
          label: '填写时间：',
          type: 'input',
          prop: 'desUserDate',
          span: 12
        },
        {
          label: '区县人员：',
          type: 'input',
          prop: 'countyUserName',
          span: 12
        },
        {
          label: '填写时间：',
          type: 'input',
          prop: 'countyUserDate',
          span: 12
        },
        {
          label: '区县人员意见：',
          type: 'input',
          mode: 'textarea',
          prop: 'countyUserOpinion',
          span: 24
        },
        {
          label: '建设单位项目经理：',
          type: 'input',
          prop: 'projectUserName',
          span: 12
        },
        {
          label: '填写时间：',
          type: 'input',
          prop: 'projectUserDate',
          span: 12
        },
        {
          label: '建设单位意见：',
          type: 'input',
          mode: 'textarea',
          prop: 'projectUserOpinion',
          span: 24
        },
        {
          label: '相关资料存放位置：',
          type: 'input',
          mode: 'textarea',
          prop: 'dataPosition',
          span: 24
        }
      ],
      labelPosition: 'left', // 查看页面放在左边
      columns: [
        {
          label: '工程签证工单号',
          prop: 'visacode',
          minWidth: 200
        },
        {
          label: '工程签证工单名称',
          prop: 'visaname',
          minWidth: 200
        }
      ],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '合同<br>信息',
          id: 'sectionContract'
        },
        {
          text: '处理<br>意见',
          id: 'sectionOperation'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlowHistory'
        }
      ]
    }
  },
  computed: {
    exportPdfVisible() {
      return ['工程量签证', '特殊费用签证', '工程材料签证'].includes(this.basicForm.visaTypeName)
    }
  },
  created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId
    this.routerType = urlQuery.type || ''// 如果type为coop。表示是从list_cooperate.vue页面来的
    this.getDetailsData()
  },
  methods: {
    getDetailsData() {
      viewService({ boId: this.boId })
        .then(res => {
          this.basicForm = JSON.parse(JSON.stringify(res.data))// 基本信息
        })
    },
    // 导出签证报告
    generateVisaReport(){
      commonDown({boId: this.boId}, generateVisaReportService);
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.routerType === 'coop' ? '/project_task_visa/find_page_coop': '/project_task_visa/find_page'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.infos-wrapper{
  .el-form-item{
    display: table;
  }
  .el-form-item__label,
  .el-form-item__content{
    display: table-cell;
    float: none;
  }
  p{
    margin: 0;
  }
}
</style>
