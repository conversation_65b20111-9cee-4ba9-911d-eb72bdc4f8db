/**
* @author: ty
* @date: 2023-07-26
* @description: 开工及时性-查看详情页面 对应/project-center-jx/starttimeliness/{id}
*/
<template>
  <div class="StarttimelinessView page-anchor-parentpage" v-loading="pageLoading">
    <div style="margin-top: 30px;"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          label-width="240px"
          disable-form
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm" />
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
} from "@/api/engineering_management/starttimeliness";

export default {
  name: "StarttimelinessView",
  data(){
    return {
      pageLoading: false,
      labelPosition: 'left', // 查看页面放在左边
      basicConfig: [
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          span: 24
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          span: 12
        },
        {
          label: '创建时间：',
          type: 'input',
          prop: 'modifyDate',
          span: 12
        },
        {
          label: '立项批复：',
          type: 'input',
          prop: 'projectReplyDate',
          span: 12
        },
        {
          label: '预计完工时间：',
          type: 'input',
          prop: 'expectedDate',
          span: 12
        },
        {
          label: '到期剩余天数：',
          type: 'input',
          prop: 'lastdays',
          span: 12
        }
      ],
      basicForm: {},
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        }
      ]
    }
  },
  async created(){
    this.id = this.$route.params.id
    this.pageLoading = true
    await this.getData()
    this.pageLoading = false
  },
  methods: {
    getData(){
      return new Promise((resolve, reject) => {
        getByIdService(this.id)
          .then(res => {
            if (res.code === '0000') {
              this.basicForm = res.data
            }
          })
          .finally(_ => {
            resolve()
          })
      })
    }
  }
}
</script>
