/**
* @author: ty
* @date: 2023-07-06
* @description: 项目完工根据id查看工单
*/
<template>
  <div class="ProjectCompletionViewForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionProjctBasicInfo" class="page-anchor-point"></div>
    <mssCard title="任务列表">
      <div slot="content">
        <mssTable
          v-if="tableVisible"
          ref="table"
          :api="tableApi"
          :columns="columns"
          :staticSearchParam="tableParams"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <div id="sectionAttachment" v-if="id && (basicForm.businessType || basicForm.workflowCode)" class="page-anchor-point"></div>
    <mssAttachment v-if="id && (basicForm.businessType || basicForm.workflowCode)" :bo-id="id" :business-type="(basicForm.businessType || basicForm.workflowCode)" :dealPage="false" />

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import {
  getByIdService,
  getDetailService
} from '@/api/engineering_management/projectCompletion.js'
export default {
  name: 'ProjectCompletionViewForm',
  data() {
    return {
      pageLoading: false,
      boId: '',
      businessType: '',
      tableApi: getDetailService,
      columns: [
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 200
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 200
        },
        {
          label: '任务专业类型',
          prop: 'projectManageSpec',//'taskSpecType',
          minWidth: 200
        },
        {
          label: '任务所属区域',
          prop: 'taskArea',
          minWidth: 200
        },
        {
          label: '任务经理',
          prop: 'taskManager',
          minWidth: 200
        },
        {
          label: '计划开工日期',
          type: 'input',
          prop: 'startDate',
          minWidth: 200
        },
        {
          label: '实际开工日期',
          type: 'input',
          prop: 'factStartDate',
          minWidth: 200
        },
        {
          label: '计划完工日期',
          type: 'input',
          prop: 'endDate',
          minWidth: 200
        },
        {
          label: '实际完工日期',
          type: 'input',
          prop: 'factEndDate',
          minWidth: 200
        },
        // {
        //   label: '项目管理颗粒度',
        //   prop: 'manageGranularity',
        //   minWidth: 200
        // },
        // {
        //   label: '项目管理专业',
        //   prop: 'projectManageSpec',
        //   minWidth: 200
        // },
        {
          label: '设计单位',
          prop: 'designOrgName',
          minWidth: 200
        },{
          label: '设计单位负责人',
          prop: 'designManagerName',
          minWidth: 200
        }, {
          label: '施工单位',
          prop: 'constructionOrgName',
          minWidth: 200
        },{
          label: '施工单位负责人',
          prop: 'constructionUserName',
          minWidth: 200
        },
        {
          label: '监理单位',
          prop: 'supervisionOrgName',
          minWidth: 200
        },
        {
          label: '监理单位负责人',
          prop: 'supervisionUserName',
          minWidth: 200
        },
        // {
        //   label: '审批部门',
        //   prop: 'approvalDept',
        //   minWidth: 200
        // },
        // {
        //   label: '审批时间',
        //   prop: 'approvalTime',
        //   minWidth: 200
        // }
      ],
      tableVisible: false,
      tableParams: {},
      basicConfig: [
        {
          label: '完工报告单号：',
          type: 'input',
          prop: 'code',
          span: 12
        },
        {
          label: '完工报告单名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          minWidth: 200
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
        },
        {
          label: '创建日期：',
          type: 'datePicker',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          prop: 'createDate',
        },
        {
          label: '实施情况及完成的主要内容：',
          type: 'input',
          prop: 'content',
          minWidth: 200,
          span: 24
        },
        {
          label: '提前或推迟完工原因：',
          type: 'input',
          prop: 'reason',
          minWidth: 200,
          span: 24
        },
        {
          label: '工程实施经理(主)：',
          type: 'input',
          prop: 'projectManagerName',
          minWidth: 200
        }
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>列表',
          id: 'sectionProjctBasicInfo'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || ''
    this.boId = urlQuery.boId || ''
    this.tableParams = {
      id: this.id
    }
    this.tableVisible = true
    this.pageLoading = true
    await this.getDataById()
    this.pageLoading = false
  },
  methods: {
    getDataById() {
      return new Promise(resolve => {
        getByIdService(this.id)
          .then(res => {
            if(res && res.code === '0000'){
              this.basicForm = Object.assign({}, this.basicForm, res.data)
              if(this.basicForm.createDate){
                this.basicForm.createDate = this.$moment(this.basicForm.createDate).format('yyyy-MM-DD')
              }
            }
          }).finally(_=>{
          resolve()
        })
      })
    },
    // 返回
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/project_completion/find_page'
      })
    }
  }
}
</script>
