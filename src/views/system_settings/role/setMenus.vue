<template>
  <mssSystemDialog
    :dialog-title="dialogTitle"
    :dialog-width="'900px'"
    :custom-class="'dialogClass'"
    :show-dialog.sync="dialogEmpower"
  >
    <div class="setMenus">
      <el-row>
        <el-col :span="10">
          <div class="company-box">
            <el-tree
              v-if="dialogEmpower"
              ref="companyTree"
              :props="props"
              :load="loadNode"
              :highlight-current="true"
              lazy
              @node-click="changeGroup"
            ></el-tree>
          </div>
        </el-col>
        <el-col :span="14">
          <div class="grid-content bg-purple listLeft">
            <div class="inputTop">
              <el-input
                v-model="outMgrName"
                size="mini"
                placeholder="姓名 "
                style="width: 50%"
              ></el-input>
              <el-button
                class="outTable"
                size="mini"
                round
                @click="searchTable()"
              >查询</el-button>

              <el-button
                class="outTable"
                size="mini"
                round
                @click="resetTable()"
              >重置</el-button>
            </div>
            <el-table
              ref="outTable"
              v-loading="outUserLoading"
              element-loading-spinner="el-icon-loading"
              :data="outUserData"
              height="274"
              size="small"
              border
              style="width: 100%"
              @selection-change="handleSelectionChangeOut"
              @select="handleChange"
              @row-click="roeClickOut"
            >
              <el-table-column
                type="selection"
                align="center"
                :selectable="checkSelect"
              ></el-table-column>
              <el-table-column
                prop="name"
                label="角色名"
                align="center"
                show-overflow-tooltip
              ></el-table-column>
            </el-table>
          </div>
          <el-pagination
            :current-page.sync="leftCurrentPage"
            :total="leftPageTotal"
            small
            layout="prev,jumper,next,total"
            @current-change="currentChangeLeft"
          ></el-pagination>
        </el-col>
      </el-row>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" size="mini" @click="submitSetMenu">确 定</el-button>
      <el-button
        size="mini"
        @click="dialogEmpower = false"
      >取 消</el-button>
    </div>
  </mssSystemDialog>
</template>

<script>
// 引入系统公共弹框
// import mssSystemDialog from '@develop2.2/components/common/mssSystemDialog.vue'
// 引入loading
// import { openLoading, closeLoading } from '@develop2.2/utils/common'
import { getRolePageService, queryTypeTree } from '@/api/system_settings/user'
import { copyMenusService, referenceMenusService } from '@/api/system_settings/role'
export default {
  name: 'SetMenus',
  components: {
    // mssSystemDialog
  },
  props: ['config'],
  data() {
    return {
      outUserLoading: false,
      dialogTitle: '设置菜单',
      dialogEmpower: false,
      companyData: [], // 角色组列表
      outUserData: [], // 左侧表格
      inUserData: [], // 右侧所有表格数据
      inUserShowData: [], // 右侧筛选后的展示数据
      outMgrName: '', // 左侧搜索框
      roleId: '', // 角色id
      multiSelect: '', // 是否支持多选
      type: '', // 是复制还是引用
      roleGroupId: '', // 角色组ID
      selectOutUserTableData: [], // 左侧表格选中值
      leftCurrentPage: 1,
      leftPageTotal: 0,
      props: {
        // 懒加载树状下拉框
        label: 'name',
        children: 'zones',
        isLeaf: 'leaf'
      },
      // 树状图默认选中
      expandDefault: []
    }
  },
  watch: {
    expandDefault(val) {
      if (val) {
        this.$nextTick(() => {
          document
            .querySelector('.setMenus .el-tree-node__content')
            .click()
        })
      }
    }
  },
  methods: {
    setMenusInitialize(roleId, type, multiSelect) {
      this.type = type // 记录数据
      this.multiSelect = multiSelect
      this.roleId = roleId
      this.outMgrName = ''
      this.InMgrName = ''
      type === 'share' ? this.dialogTitle = '复制资源列表' : this.dialogTitle = '引用资源列表'
      this.dialogEmpower = true
      this.flag = true
    },
    // 获取角色列表
    getCompanyData(data, callback) {
      const that = this
      queryTypeTree(data).then(function(response) {
        if (response) {
          that.companyData = response.data || []
          callback()
        }
      })
    },
    // 树状下拉框
    loadNode(node, resolve) {
      let params
      if (node.level === 0) {
        this.getCompanyData({ rootId: '' }, () => {
          this.expandDefault.push(this.companyData[0])
          return resolve(this.companyData)
        })
      } else {
        if (node.data && node.data.id) {
          params = { rootId: node.data.id }
        } else {
          params = { rootId: '' }
        }
        setTimeout(() => {
          this.getCompanyData(params, () => {
            resolve(this.companyData)
          })
        }, 200)
      }
    },
    // 左侧选择角色组名字
    changeGroup(item, it) {
      console.log(item)
      const data = []
      this.companyData.forEach((i) => {
        i.checked = !!(i.id === it.data.id)
        data.push(i)
      })
      this.companyData = data
      this.roleGroupId = item.id

      this.getOutUserData({
        groupId: this.roleGroupId,
        page: 1
      })
    },
    // 获取左侧用户列表
    getOutUserData(param) {
      const paramData = param || {}
      this.outUserLoading = true
      getRolePageService(paramData).then((res) => {
        this.outUserData = res.data.data
        this.leftCurrentPage = parseInt(res.data.page)
        this.leftPageTotal = parseInt(res.data.total)
        this.outUserLoading = false
      })
    },
    // 判断是否勾选
    checkSelect(row, index) {
      let isChecked = true
      if (row.selectable == true) {
        // 判断里面是否存在某个参数
        isChecked = false
      } else {
        isChecked = true
      }
      return isChecked
    },

    // 搜索框查询
    searchTable() {
      this.getOutUserData({
        groupId: this.roleGroupId,
        name: this.outMgrName,
        page: 1
      })
    },

    // 搜索框重置
    resetTable() {
      this.outMgrName = ''
      this.getOutUserData({
        groupId: this.roleGroupId,
        page: 1
      })
    },

    // 左侧表格分页
    currentChangeLeft(val) {
      this.leftCurrentPage = val
      this.getOutUserData({
        groupId: this.outMgrName ? '' : this.roleGroupId,
        name: this.outMgrName,
        page: val
      })
    },

    // 左侧表格状态
    handleSelectionChangeOut(val) {
      this.selectOutUserTableData = val
    },

    handleChange(selection, row) {
      if (!this.multiSelect && this.selectOutUserTableData.length) {
        this.$refs.outTable.clearSelection()
        this.$nextTick(() => {
          this.$refs.outTable.toggleRowSelection(row, true)
        })
      }
    },

    // 左侧表格点击行
    roeClickOut(row) {
      this.$refs.outTable.toggleRowSelection(row)
    },

    // 确定提交按钮
    submitSetMenu() {
      const that = this
      let targetRoleIds; const targetRoleIdsArray = []
      this.selectOutUserTableData.forEach((item) => {
        targetRoleIdsArray.push(item.roleId)
      })
      targetRoleIds = targetRoleIdsArray.toString()
      let params
      if (this.type === 'share') {
        params = {
          sourceRoleId: this.roleId,
          targetRoleIds: targetRoleIds
        }
      } else if (this.type === 'cite') {
        params = {
          sourceRoleId: targetRoleIds,
          targetRoleId: this.roleId
        }
      }
      // let params = {
      //   sourceRoleId: this.roleId,
      //   [this.type === 'share' ? 'targetRoleIds' : 'targetRoleId']: targetRoleIds
      // }
      if (this.selectOutUserTableData.length === 0) {
        this.$message({
          message: '请选择角色',
          type: 'warning',
          duration: 1000
        })
      } else {
        (this.type === 'share' ? copyMenusService : referenceMenusService)(params).then(
          function(res) {
            if (res.code === '0000') {
              that.$message({
                showClose: true,
                message: '设置菜单成功',
                type: 'success',
                duration: 1000,
                onClose: function() {
                  that.dialogEmpower = false // 成功后，不关闭窗口
                }
              })
            }
          }
        )
      }
    }
  }
}
</script>

<style lang="scss">

.setMenus {
  .el-pagination__jump {
    margin-left: 0px;
  }
  .company-box {
    width: 90%;
    height: 320px;
    overflow: auto;
    float: left;
    margin-top: -8px;
    .company {
      list-style: none;
      li {
        font-size: 12px;
        line-height: 14px;
        padding: 8px 0;
        color: #000;
        border-bottom: 1px dotted #c0c0c0;
        cursor: pointer;
        i {
          display: inline-block;
          line-height: 16px;
          vertical-align: top;
        }
        span {
          display: inline-block;
          width: 80%;
        }
        .el-icon-caret-right {
          color: #3699ff;
          margin-right: 10px;
        }
      }
    }
    .el-pagination__editor {
      .el-input__inner {
        max-height: 80px;
      }
    }
    .active {
      cursor: pointer;
      color: #21b4f9 !important;
      font-weight: bold;
      border-bottom: 1px solid #21b4f9 !important;
      .el-icon-caret-right {
        color: #fe9601 !important;
      }
    }
  }
  .grid-content {
    margin-top: -30px;
    .inputTop {
      margin-left: 10px;
      margin-bottom: 2px;
      margin-top: 24px;
    }
    .el-table {
      margin-bottom: 10px;
    }
    .el-table--small td,
    .el-table--small th {
      padding: 0;
    }
  }
  .searchBtn {
    display: inline-block;
    /* background: #ff000030; */
    font-size: 20px;
    color: #409eff;
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-weight: bold;
    &:hover {
      cursor: pointer;
    }
  }
  .outTable{
    color: #5181FF;
    border: 1px solid #5181FF;
  }
  .inTable{
    color: #fff;
    background: #5181FF;
    border: 1px solid #5181FF;
  }
  .centerBtns {
    text-align: center;
    &:first-child {
      padding-top: 174px;
      padding-bottom: 30px;
    }
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  .el-tree {
    min-width: 100%;
    display: inline-block;
    }
  //修改树状列表样式
  .el-tree-node__expand-icon {
    color: #3699ff;
  }
  .el-tree-node.is-current > .el-tree-node__content {
    cursor: pointer;
    color: #21b4f9 !important;
    font-weight: bold;
    border-bottom: 1px solid #21b4f9 !important;
    .el-tree-node__expand-icon {
      color: #fe9601;
    }
    .el-tree-node__expand-icon.is-leaf {
      color: transparent;
    }
  }
  .el-tree-node__expand-icon.is-leaf {
    color: transparent;
  }
}
</style>
