<!--
 * @Descripttion: 选择上级领导弹出框
 * @version:
 * @Author: xyh
 * @Date: 2021-09-03 14:07:57
 * @LastEditors: sueRimn
 * @LastEditTime: 2021-09-09 13:50:14
-->
<template>
  <div class="chooseLeader">
    <mssSystemDialog
      :show-dialog.sync="dialogEmpower"
      :dialog-width="'900px'"
      :dialog-title="'选择上级领导'"
      :custom-class="'empDialog'"
    >
      <div class="emp-content">
        <div class="emp-left">
          <div class="emp-left-scroll">
            <el-tree
              v-if="dialogEmpower"
              ref="companyTree"
              :props="props"
              :load="loadNode"
              :highlight-current="true"
              lazy
              @node-click="changeType"
            ></el-tree>
          </div>
        </div>
        <div class="emp-right">
          <el-table
            ref="multipleTable"
            :data="emptableData"
            height="100%"
            border
            @select="selectChange"
            @select-all="selectAll"
          >
            <el-table-column type="selection"> </el-table-column>
            <el-table-column label="角色名" prop="name"> </el-table-column>
            <!-- <el-table-column label="角色编码" prop="code"> </el-table-column> -->
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitUserRole()">确 定</el-button>
        <el-button
          size="mini"
          @click="
            () => {
              dialogEmpower = false;
            }
          "
        >取 消</el-button>
      </div>
    </mssSystemDialog>
  </div>
</template>

<script>
// import mssSystemDialog from "@develop2.2/components/common/mssSystemDialog.vue";
import {
  getRoleTreeService,
  tableEmpListService
} from '@/api/system_settings/user'
export default {
  // components: { mssSystemDialog },
  data() {
    return {
      dialogEmpower: false,
      props: {
        // 懒加载树状下拉框
        label: 'name',
        children: 'zones',
        isLeaf: 'isLeaf'
      },
      emptableData: [],
      wfCode: '',
      userRoleIds: []
    }
  },
  methods: {
    init(code) {
      this.dialogEmpower = true
      this.wfCode = code
    },
    // 树状选择
    loadNode(node, resolve) {
      let params
      if (node.level === 0) {
        return resolve([{ name: '角色组列表' }])
      } else {
        if (node.data && node.data.id) {
          params = {
            rootId: node.data.id,
            wfCode: this.wfCode,
            wfId: this.$route.query.wfId || ''
          }
        } else {
          params = {
            rootId: '',
            wfCode: this.wfCode,
            wfId: this.$route.query.wfId || ''
          }
        }
        setTimeout(() => {
          this.getRoleTreeList(params, () => {
            resolve(this.roleTreeData)
          })
        }, 200)
      }
    },
    // 获取角色列表
    getRoleTreeList(data, callback) {
      getRoleTreeService(data).then((res) => {
        if (res && res.data && res.code === '0000') {
          this.roleTreeData = res.data
          this.roleTreeData.forEach((item, index) => {
            item.isLeaf = !item.hasChlid
          })
          callback()
        }
      })
    },
    // 树状列表点击切换列表数据
    changeType(ev, item) {
      tableEmpListService(item.data.id).then((res) => {
        if (res && res.data) {
          this.emptableData = res.data
          // 循环勾选已绑定角色
          for (let i = 0; i < this.emptableData.length; i++) {
            // for (let j = 0; j < this.userRole.length; j++) {
            if (this.userRoleIds.includes(this.emptableData[i].roleId)) {
              setTimeout(() => {
                this.$refs.multipleTable.toggleRowSelection(
                  this.emptableData[i]
                )
              }, 100)
            }
            // }
          }
        }
      })
    },
    selectChange(selection, row) {
      // 选择项大于1时
      if (selection.length > 1) {
        const del_row = selection.shift()
        this.$refs.multipleTable.toggleRowSelection(del_row, false)
      }
    },
    selectAll(selection) {
      if (selection.length > 1) {
        selection.length = 1
      }
    },
    submitUserRole() {
      this.$emit('getLeader', this.$refs.multipleTable.selection)
      this.emptableData = []
      this.dialogEmpower = false
    }
  }
}
</script>

<style lang='scss'>
.empDialog .emp-content {
  height: 400px;
  .emp-left {
    width: 200px;
    height: 100%;
    float: left;
    .emp-left-scroll {
      height: 100%;
      width: 100%;
      overflow: auto;
    }
  }
  .emp-right {
    width: calc(100% - 220px);
    float: left;
    margin-left: 20px;
    height: 100%;
  }
  //修改树状列表样式
  .el-tree {
    margin-top: 10px;
    min-width: 100%;
    display: inline-block;
  }
}
</style>
