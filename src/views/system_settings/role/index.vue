<template>
  <div class="index-role settingCommonCLass" >
    <div>
      <div class="text item">
        <mssSearchForm
          ref="search"
          :form="searchForm"
          :search-config="configSearchFiled"
          @search="detailSearch"
          @reset="reset"
        ></mssSearchForm>
      </div>
      <mssCard :title="'角色列表'">
        <div slot="content">
          <div class="text item">
            <el-table
              ref="table"
              v-loading="tableLoading"
              :data="tableData"
              size="small"
              border
              stripe
              style="width: 100%"
              element-loading-spinner="el-icon-loading"
            >
              <template slot="empty">
                <div class="tableNodata">
                  <span>暂无数据</span>
                </div>
              </template>
              <el-table-column
                prop="name"
                label="角色名称"
                align="center"
                :show-overflow-tooltip="true"
              ></el-table-column>
              <el-table-column
                prop="description"
                label="角色描述"
                :show-overflow-tooltip="true"
                align="center"
              ></el-table-column>
              <el-table-column  label="操作"  align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button  type="text" size="small" @click="bindUser(scope.row)">绑定用户</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div>
              <mssPagination :config="configPage" @page-change="pageChange"></mssPagination>
            </div>
          </div>
        </div>

      </mssCard>
    </div>
    <!--绑定用户-->
    <roleBindUser ref="bindUserAlert" :config="configBindUser"></roleBindUser>
  </div>
</template>
<script>
import roleBindUser from './roleBindUser.vue'
import {
  tableListService,
} from '@/api/system_settings/role'
import { getSysDicList } from '@/api/system_settings/dictionary.js'
import {
  getDeptListService
} from '@/api/system_settings/user'
import { queryAreaListService } from '@/api/common_api.js'
export default {
  name: 'Role',
  components: {
    roleBindUser,
  },
  data() {
    return {
      tableLoading: false,
      selectRe: [],
      checkListRe: [], // 地域选择框需要的储存参数的变量
      configSearchFiled: [
        {
          // 搜索框属性
          label: '角色名称',
          type: 'input',
          fieldName: 'name',
          span: 8
        }
      ],
      searchForm: { name: '' },
      // 个性化配置的插槽参数，根据工单类型，可传入不同的配置参数
      roleDetailConfig: [], // 传入的新增/编辑 角色插槽配置
      roleDetailData: {
        attribute11: '',
        attribute12: '',
        attribute13: '',
        attribute2: []
      }, // 传入的新增/编辑 角色插槽参数
      roleDetailDataCopy: {
        attribute11: '',
        attribute12: '',
        attribute13: '',
        attribute2: []
      }, // 用于重置的角色插槽参数
      // 故障工单角色表单槽配置
      commonfaultroleDetailConfig: [
        {
          span: 12,
          label: '地市',
          placeholder: '请选择地市',
          type: 'outSelect',
          prop: 'attribute2',
          prefixIcon: 'iconfont icontubiao-dingwei',
          multiple: 'multiple',
          labelWidth: '120px',
          disabled: false
        },
        {
          span: 12,
          label: '一级主管',
          placeholder: '请输入主管',
          type: 'input',
          prop: 'attribute11',
          labelWidth: '120px'
        },
        {
          span: 12,
          label: '二级主管',
          placeholder: '请输入主管',
          type: 'input',
          prop: 'attribute12',
          labelWidth: '120px'
        },
        {
          span: 12,
          label: '三级主管',
          placeholder: '请输入主管',
          type: 'input',
          prop: 'attribute13',
          labelWidth: '120px'
        }
      ],
      workOrderParentCode: '', // 当前选择的工单所属的类型的code
      searchFlag: true, // 搜索框样式
      filters: {}, // 用于储存搜索组件的值，方便刷新表格时保存状态
      RoleType: '', // 用于储存当前所指的角色类型，是故障工单还是投诉工单   ？是否和变量workOrderCode有重复？
      tableData: [],
      selectionTableList: [],
      dialogDelVisible: false,
      dialogBindVisible: false,
      dbConfigId: '',
      bindData: [],
      // 新增编辑弹出框状态
      dialogAddEditVisible: false,
      dialogAddEditTitle: '新增子角色',
      // 角色分类弹出框状态
      dialogRoleShow: false,
      dialogRoleTittle: '新增',
      typeName: '', // 搜索条件类型名字
      // 角色分类表单数据
      roleFormData: {
        name: '',
        code: '',
        virtualGroup: '',
        sort: '',
        attribute9: ''
      },
      Node: [], // 用于保存节点数据
      theNode: '', // 用于保存节点
      nodedata: { groupName: window.g.groupName, groupId: window.g.groupId },
      // 上级地图数据
      getMapData: [],
      addEditFormData: {
        groupName: '', // 角色组名称
        roleId: '', // 角色id
        name: '', // 角色名称
        code: '', // 角色编码
        type: '', // 角色类型ID
        description: '', // 描述
        platformId: sessionStorage.getItem('platformId'), // 系统id
        platformName: '', // 系统名称
        // 以下为不同类型工单的动态字段
        attribute11: '', // 一级主管
        attribute11Name: '', // 一级主管CN
        attribute12: '', // 二级主管
        attribute12Name: '', // 二级主管CN
        attribute13: '', // 三级主管
        attribute13Name: '', // 三级主管CN
        attribute2: [], // 地市
        attribute1: '', // 部门
        attribute16: '', // 上一级领导
        attribute17: '' // 上一级领导CN
      },
      addEditFormDataCopy: {
        groupName: '',
        roleId: '',
        name: '',
        code: '', // 角色编码
        type: '', // 角色类型ID
        description: '',
        platformId: sessionStorage.getItem('platformId'),
        platformName: '',
        // 以下为不同类型工单的动态字段
        attribute11: '', // 一级主管
        attribute11Name: '', // 一级主管CN
        attribute12: '', // 二级主管
        attribute12Name: '', // 二级主管CN
        attribute13: '', // 三级主管
        attribute13Name: '', // 三级主管CN
        attribute2: [], // 地市
        attribute1: '', // 部门
        attribute16: '', // 上一级领导
        attribute17: '' // 上一级领导CN
      },

      // 是否虚拟组选项
      virtualSelect: [
        { name: '是', type: '1' },
        { name: '否', type: '0' }
      ],

      // 角色编码列表
      roleTypeOptions: [
        { name: '权限角色', type: '1' },
        { name: '数据角色', type: '2' },
        { name: '流程角色', type: '3' }
      ],
      netTypeOneOptions: [], // 网络一级分类选项
      netTypeTwoOptions: [], // 网络二级分类选项
      netTypeThreeOptions: [], // 网络三姐分类选项
      factoryOptions: [], // 厂商分类选项
      addEditFormRules: {
        name: {
          required: true,
          message: '请输入角色名称',
          trigger: 'blur'
        },
        type: {
          required: true,
          message: '请选择角色类型',
          trigger: 'blur'
        }
        // platformId: {
        //   required: true,
        //   message: "请选择系统",
        //   trigger: "change",
        // },
      },
      roleFormRules: {
        name: {
          required: true,
          message: '请输入名称',
          trigger: 'blur'
        },
        virtualGroup: {
          required: true,
          message: '请选择是否虚拟组',
          trigger: 'change'
        }
      },
      // 分页参数
      configPage: {
        pageTotal: 0,
        pageSize: 10,
        currentPage: 1
      },
      // 人员绑定配置项
      configBindUser: {
        roleId: ''
      },
      // 设置组长配置项
      configSetLeader: {
        roleId: '',
        aroleId: ''
      },
      // 分配菜单配置项
      configSpecialty: {
        roleId: ''
      },
      // codeMsg: "", //角色编码的消息
      // isCodeError: false,
      props: {
        // 懒加载树状下拉框
        label: 'name',
        children: 'zones',
        isLeaf: 'isLeaf'
      },
      reloadTree: true, // 控制树重加载
      roleTreeData: [],
      menu: false,
      menuTop: 0,
      menuLeft: 0,
      hasAdd: false, // 是否限定新增
      nowVirtualGroup: '0', // 是否
      formId: '', // 表单的id,而不是业务的id
      userId: sessionStorage.getItem('user_id'),
      formModel: {},
      lastFormData: {},
      fdFormItems: [],
      fdFormData: {},
      formRules: {}, // 表单的校验规则
      flatFdFormList: [], // 平级的表单字段集合
      attribute9: '',
      businessType: '',
      formVersion: '',
      view: false,
      powerData: {}, // 用于存储权限数据
      deptList: [], // 单位列表树状下拉数据
      deptTreeSelectKey: 0, // 树形结构刷新--新增编辑弹出框
      leaderList: [], // 存储上一级领导选的人
      showLeader: false, // 用于控制是否展示上一级领导
      cityList: [],
      groupId: window.g.groupId,
      cityType: sessionStorage.getItem('firstDeptId') // 省公司
    }
  },
  activated(){
    this.$refs.table.doLayout()
  },
  mounted() {
    this.getTableList({ groupId: this.groupId })
  },
  methods: {
    // 获取table列表
    getTableList(data) {
      this.tableLoading = true
      const that = this
      tableListService(data)
        .then((response) => {
          that.configPage = {
            pageTotal: parseInt(response.data.total) || 0,
            pageSize: parseInt(response.data.limit) || 10,
            currentPage: parseInt(response.data.page) || 1
          }
          if (response && response.data) {
            this.tableData = response.data.data || []
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },

    // 分页改变，子组件pagination会调用此方法并传值
    pageChange(currentPage, pagesize) {
      const that = this
      that.configPage.currentPage = currentPage
      that.configPage.pageSize = pagesize
      that.getTableList({
        limit: pagesize,
        page: currentPage,
        groupId: this.nodedata.groupId,
        ...that.filters
      })
    },
    // 搜索
    detailSearch(params) {
      this.filters = params
      this.configPage.pageSize = 10
      this.configPage.currentPage = 1
      this.getTableList({
        limit: this.configPage.pageSize,
        page: this.configPage.currentPage,
        groupId: this.nodedata.groupId,
        ...this.filters
      })
    },
    reset(params) {
      this.filters = params
      this.configPage.pageSize = 10
      this.configPage.currentPage = 1
      this.getTableList({
        limit: this.configPage.pageSize,
        page: this.configPage.currentPage,
        groupId: this.nodedata.groupId,
        ...this.filters
      })
    },


    // 绑定用户
    bindUser(item) {
      this.$refs.bindUserAlert.bindUserInitialize(item)
    },
  }
}
</script>
<style lang="scss">
.index-role {
  // padding: 20px !important;
  .list-area {
    width: 300px;
    height: 100%;
    float: left;
    .el-card__body {
      padding: 5px 5px !important;
      height: calc(100vh - 110px);
      overflow: auto;
      width: auto;
      .el-input__inner {
        height: 30px;
      }
    }
  }
  .table-area {
    width: calc(100% - 310px);
    float: right;
  }
  .out-card {
    margin-bottom: 0 !important;
  }
  .tree-area{
      width: 100%;
      height: calc(100% - 30px);
      overflow: auto;
    }
  //修改树状列表样式
  .el-tree {
    margin-top: 10px;
    min-width: 100%;
    display: inline-block;
  }

  .el-tree-node.is-current > .el-tree-node__content {
    cursor: pointer;
    // color: $themeSubColor !important;
    font-weight: bold;
    // border-bottom: 1px solid $themeSubColor !important;
    .el-tree-node__expand-icon {
      color: #fe9601;
    }
    .el-tree-node__expand-icon.is-leaf {
      color: transparent;
    }
  }
  .el-tree-node__expand-icon.is-leaf {
    color: transparent;
  }
  #menu {
    width: 70px;
    position: fixed;
    top: 300px;
    left: 200px;
    .el-menu-item {
      height: 30px;
      line-height: 30px;
      span {
        color: #606266;
      }
    }
  }
  .roleFormData {
    padding-left: 10px;
  }
}
.dialogClass {
  .add-form {
    .fd-form-item>.el-form-item {
      .el-form-item__label {
        line-height: 40px;
        color: #606266;
      }
      >.el-form-item__content {
        float: none;
        line-height: 40px;
        width: calc(100% - 120px);
        .el-textarea {
          width: 100%;
          margin-top: 10px;
          .el-textarea__inner {
            width: 100%;
          }
        }
        .el-form-item__label,
        .el-input,
        .el-input__icon,
        .el-input__inner {
          height: 26px !important;
          line-height: 26px;
        }
      }
      .outSelect {
        .el-select__tags {
          margin-left: 1.7rem;
          height: 27px;
          .el-tag {
            display: flex;
            height: 22px;
            padding: 0 8px;
            line-height: 20px;
            .el-select__tags-text {
              max-width: 5vw;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              display: block;
            }
            .el-tag__close {
              top: 5px;
            }
          }
          .el-select__input {
            margin-left: 0px;
          }
        }
      }
    }
  }
}
.roleDialog {
  .el-dialog__body {
    overflow: auto;
  }
}
.outSelectDown {
  display: none;
}
.popperClass1 {
  height: 260px;
  // overflow-x: auto;
  overflow-y: auto;
}
</style>
