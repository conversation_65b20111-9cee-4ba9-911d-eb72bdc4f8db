<template>
  <mssSystemDialog
    :dialog-title="'绑定用户'"
    :dialog-width="'60%'"
    :custom-class="'dialogClass'"
    :show-dialog.sync="dialogBindUserVisible"
  >
    <div class="roleBindUser">
      <el-row>
        <el-col :span="6">
          <div class="company-box">
            <el-tree
              v-if="dialogBindUserVisible"
              ref="companyTree"
              :props="props"
              :load="loadNode"
              :highlight-current="true"
              lazy
              @node-click="changeCompany"
            ></el-tree>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple listLeft">
            <div class="inputTop">
              <el-input
                v-model="outMgrName"
                size="mini"
                placeholder="姓名 "
                style="width: 50%"
              ></el-input>
              <el-button
                class="outTable"
                size="mini"
                round
                @click="searchTable('outTable')"
              >查询</el-button>

              <el-button
                class="outTable"
                size="mini"
                round
                @click="resetTable('outTable')"
              >重置</el-button>
            </div>
            <el-table
              ref="outTable"
              v-loading="outUserLoading"
              :data="outUserData"
              height="274"
              size="small"
              border
              style="width: 100%"
              element-loading-spinner="el-icon-loading"
              @selection-change="handleSelectionChangeOut"
              @row-click="roeClickOut"
              @cell-mouse-enter="centerCell"
              @cell-mouse-leave="leaveCell"
            >
              <el-table-column
                type="selection"
                align="center"
                :selectable="checkSelect"
              ></el-table-column>
              <el-table-column
                prop="realName"
                label="姓名"
                align="center"
                width="100px"
              ></el-table-column>
              <el-table-column
                prop="deptName"
                label="单位"
                align="center"
              ></el-table-column>
            </el-table>
          </div>
          <el-pagination
            :current-page.sync="leftCurrentPage"
            :total="leftPageTotal"
            small
            layout="prev,jumper,next,total"
            @current-change="currentChangeLeft"
          ></el-pagination>
        </el-col>
        <el-col :span="2">
          <div class="grid-content bg-purple-light">
            <div class="centerBtns">
              <el-button
                icon="el-icon-caret-right"
                size="mini"
                :disabled="rightMove"
                @click="outMoveIn()"
              ></el-button>
            </div>
            <div class="centerBtns">
              <el-button
                icon="el-icon-caret-left"
                size="mini"
                :disabled="leftMove"
                @click="inMoveOut()"
              ></el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple listRight">
            <div class="inputTop">
              <el-input
                v-model="InMgrName"
                placeholder="姓名 "
                size="mini"
                style="width: 50%"
              ></el-input>
              <el-button
                class="outTable"
                size="mini"
                round
                @click="searchTable('inTable')"
              >查询</el-button>
              <el-button
                class="outTable"
                size="mini"
                round
                @click="resetTable('inTable')"
              >重置</el-button>
            </div>
            <el-table
              ref="inTable"
              v-loading="inUserLoading"
              element-loading-spinner="el-icon-loading"
              :data="inUserShowData"
              height="274"
              size="small"
              border
              style="width: 100%"
              @selection-change="handleSelectionChangeIn"
              @row-click="roeClickIn"
              @cell-mouse-enter="centerCell"
              @cell-mouse-leave="leaveCell"
            >
              <el-table-column
                type="selection"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="realName"
                label="姓名"
                align="center"
                width="80px"
              ></el-table-column>
              <el-table-column
                prop="deptName"
                label="单位"
                align="center"
              ></el-table-column>
            </el-table>
          </div>
          <el-pagination
            :total="rightPageTotal"
            small
            layout="total"
          >
          </el-pagination>
        </el-col>
      </el-row>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button
      type="primary"
        size="mini"
        @click="dialogBindUserVisible = false"
      >关闭</el-button>
    </div>
    <div v-if="roleMsg" id="roleMsg" :style="{left:roleMsgLeft+'px',top:roleMsgTop+'px'}">
      <ul>
        <li>账号：{{ roleMsgData.account||'' }}</li>
        <li>姓名：{{ roleMsgData.realName||'' }}</li>
        <li>电话：{{ roleMsgData.mobile||'' }}</li>
        <li>部门：{{ roleMsgData.deptName||'' }}</li>
      </ul>
    </div>
  </mssSystemDialog>
</template>

<script>
import {
  getCompanyService, // 获取公司列表
  getOutUserService, // 获取用户列表
  bindUsersService, // 绑定用户
  getRootService,// 获取根
} from '@/api/system_settings/role'
export default {
  name: 'RoleBindUser',
  data() {
    return {
      outUserLoading: false,
      inUserLoading: false,
      dialogBindUserVisible: false,
      companyData: [], // 公司列表
      outUserData: [], // 左侧表格
      inUserData: [], // 右侧所有表格数据
      inUserShowData: [], // 右侧筛选后的展示数据
      outMgrName: '', // 左侧搜索框
      InMgrName: '', // 右侧搜索框
      // orgName: '',//公司名字
      orgId: '', // 公司ID
      rightMove: false, // 右移按钮是否可用
      leftMove: false, // 左移按钮是否可用
      selectOutUserTableData: [], // 左侧表格选中值
      selectInUserTableData: [], // 右侧表格选中值
      leftCurrentPage: 1,
      flag: true,
      leftPageTotal: 0,
      rightCurrentPage: 1,
      rightPageTotal: 0,
      props: {
        // 懒加载树状下拉框
        label: 'name',
        children: 'zones',
        isLeaf: 'leaf'
      },
      // 树状图默认选中
      expandDefault: [],
      roleMsg: false,
      roleMsgTop: 0,
      roleMsgLeft: 0,
      roleMsgData: {},
      rootId:'',
      config:{}
    }
  },
  watch: {
    expandDefault(val) {
      if (val) {
        this.$nextTick(() => {
          document
            .querySelector('.roleBindUser .el-tree-node__content')
            .click()
        })
      }
    }
  },
  methods: {
    bindUserInitialize(data) {
      this.outMgrName = ''
      this.InMgrName = ''
      this.dialogBindUserVisible = true
      this.outUserData=[]
      this.flag = true
      this.config=data
      //根据施工单位、施工队长，只能选施工单位相关的人
      if(this.config.code==window.g.sgdzRoleCode||this.config.code==window.g.sgfzrRoleCode){
        this.rootId=window.g.sgdwRootId
        this.rootId2=""
      }else{
        if(sessionStorage.getItem('firstDeptId')=='1'){
          this.rootId='-2'
          this.rootId2=""
        }else{
          this.rootId=sessionStorage.getItem('firstDeptId')
          this.rootId2=window.g.sgdwRootId
        }
      }
      this.getInUserData({
          roleId: this.config.roleId,
          onlyShowDeptId:this.rootId,
          limit: 99999,
          roleHaveUser: true
        })
    },
    // 获取公司数据
    getCompanyData(data, callback) {
      getCompanyService(data).then(response=>{
        if(response){
          this.companyData = response.data || []
          callback()
        }
      })
    },
    // 树状下拉框
    loadNode(node, resolve) {
      let params
      if (node.level === 0) {
        if(this.rootId2){
          Promise.all([getRootService(this.rootId), getRootService(this.rootId2)])
          .then(([res1,res2]) => {
            // 两个请求都完成后
            this.companyData=[]
            if(res1.code=='0000'&& res2.code=='0000'){
              let obj={
                "id": res1.data.deptId,
                "parentId": res1.data.parentDeptId,
                "children": [],
                "name": res1.data.deptName,
                "simpleName": res1.data.deptSimpleName,
                "code": res1.data.deptCode,
                "status": res1.data.status,
                "hasChlid": true
              }
              this.companyData.push(obj)
              this.companyData.push({
                "id": res2.data.deptId,
                "parentId": res2.data.parentDeptId,
                "children": [],
                "name": res2.data.deptName,
                "simpleName": res2.data.deptSimpleName,
                "code": res2.data.deptCode,
                "status": res2.data.status,
                "hasChlid": true
              })
              this.expandDefault.push(obj)
              return resolve(this.companyData)
            }
          })

        }else{
          getRootService(this.rootId).then(res=>{
            if(res.code=='0000'){
              this.companyData=[{
                "id": res.data.deptId,
                "parentId": res.data.parentDeptId,
                "children": [],
                "name": res.data.deptName,
                "simpleName": res.data.deptSimpleName,
                "code": res.data.deptCode,
                "status": res.data.status,
                "hasChlid": true
              }]
              this.expandDefault.push(this.companyData[0])
              return resolve(this.companyData)
            }
          })
        }
      } else {
        if (node.data && node.data.id) {
          params = { rootId: node.data.id }
        } else {
          params = { rootId: '' }
        }
        setTimeout(() => {
          this.getCompanyData(params, () => {
            resolve(this.companyData)
          })
        }, 200)
      }
    },
    // 左侧选择公司名字
    changeCompany(item, it) {
      const data = []
      this.companyData.forEach((i) => {
        i.checked = !!(i.id === it.data.id)
        data.push(i)
      })
      this.companyData = data
      this.orgId = it.data.id
      this.getOutUserData({
        realName: this.outMgrName,
        deptId: it.data.id,
        roleId: this.config.roleId,
        roleHaveUser: false
      })
      if (this.flag) {

        this.flag = false
      }
    },
    // 获取左侧用户列表
    getOutUserData(param) {
      const that = this
      const paramData = param || {}
      this.outUserLoading = true
      if(this.outMgrName){
        delete paramData.deptId
      }else{
        delete paramData.deptIdCaseChild
      }
      // INFO：业务逻辑是：默认只查询点击左侧树节点的人员，当有输入值时，查询当前节点及其子类的数据，当绑定成功或取消
      // 绑定后，也是根据当前是否有搜索来判断获取什么节点的用户
      getOutUserService(paramData)
        .then((res) => {
          this.outUserData = res.data.data
          this.leftCurrentPage = parseInt(res.data.page)
          this.leftPageTotal = parseInt(res.data.total)
          this.outUserLoading = false
        })
        .catch(e => {
          this.outUserLoading = false
        })
    },
    // 判断是否勾选
    checkSelect(row, index) {
      let isChecked = true
      if (row.selectable == true) {
        // 判断里面是否存在某个参数
        isChecked = false
      } else {
        isChecked = true
      }
      return isChecked
    },
    // 获取右侧绑定用户列表
    getInUserData(param) {
      const paramData = param || {}
      this.inUserLoading = true
      if(this.rootId2){
        Promise.all([getOutUserService(param), getOutUserService({...param,onlyShowDeptId:this.rootId2})])
          .then(([res1,res2]) => {
            // 两个请求都完成后
            if(res1.code=='0000'&& res2.code=='0000'){
              this.InMgrName = ''
              this.inUserData = res1.data.data.concat(res2.data.data)
              this.rightPageTotal = parseInt(res1.data.total)+parseInt(res2.data.total)
              this.refreshShowData()
              this.inUserLoading = false
            }
          }).catch(e => {
          this.inUserLoading = false
        })
      }else{
        getOutUserService(paramData)
        .then((res) => {
          this.InMgrName = ''
          this.inUserData = res.data.data
          this.rightPageTotal = parseInt(res.data.total)
          this.refreshShowData()
          this.inUserLoading = false
        })
        .catch(e => {
          this.inUserLoading = false
        })
      }

    },
    // 搜索框查询
    searchTable(data) {
      if (data === 'outTable') {
        this.getOutUserData({
          realName: this.outMgrName,
          roleId: this.config.roleId,
          deptIdCaseChild: this.orgId,
          roleHaveUser: false
        })
      } else {
        this.refreshShowData() // 刷新搜索框
      }
    },

    // 刷新右侧展示框
    refreshShowData() {
      const searchName = this.InMgrName.trim()
      this.inUserShowData = this.inUserData.filter(
        (data) => !searchName || data.realName.toLowerCase().includes(searchName.toLowerCase())
      )
    },

    // 搜索框重置
    resetTable(data) {
      if (data === 'outTable') {
        this.outMgrName = ''
        this.getOutUserData({
          deptId: this.orgId,
          roleId: this.config.roleId,
          roleHaveUser: false
        })
      } else {
        // 右侧的搜索为前端行为，重置向后端请求数据
        // this.InMgrName = ''
        this.getInUserData({
          roleId: this.config.roleId,
          limit: 99999,
          roleHaveUser: true
        })
      }
    },

    // 左侧表格分页
    currentChangeLeft(val) {
      this.leftCurrentPage = val
      this.getOutUserData({
        roleId: this.config.roleId,
        realName: this.outMgrName,
        deptIdCaseChild: this.orgId,
        deptId: this.orgId,
        page: val,
        roleHaveUser: false
      })
    },

    // 右侧表格状态
    handleSelectionChangeIn(val) {
      this.leftMove = false
      this.rightMove = true
      this.selectInUserTableData = val
    },
    // 左侧表格状态
    handleSelectionChangeOut(val) {
      this.leftMove = true
      this.rightMove = false
      this.selectOutUserTableData = val
    },
    // 左侧表格点击行
    roeClickOut(row) {
      this.$refs.outTable.toggleRowSelection(row)
    },
    // 右侧表格点击行
    roeClickIn(row) {
      this.$refs.inTable.toggleRowSelection(row)
    },
    centerCell(row, column, cell, event) {
      this.roleMsgData = row
      this.roleMsgTop = event.clientY + 10
      this.roleMsgLeft = event.clientX + 10
      this.roleMsg = true
    },
    leaveCell() {
      this.roleMsg = false
      this.roleMsgData = null
    },
    // 右移点击事件
    outMoveIn() {
      bindUsersService({
        op:"bind",
        userIds:this.selectOutUserTableData.map((item)=>{
          return item.userId
        }).join(','),
        roleId:this.config.roleId
      }).then(res=>{
        if(res.code=='0000'){
          this.$message.success('绑定成功')
          this.getOutUserData({
          realName: this.outMgrName,
          roleId: this.config.roleId,
          deptIdCaseChild: this.orgId,
          deptId: this.orgId,
          roleHaveUser: false
        })
        this.getInUserData({
          roleId: this.config.roleId,
          onlyShowDeptId:this.rootId,
          limit: 99999,
          roleHaveUser: true
        })
        }
      })
    },
    // 左移点击事件
    inMoveOut() {
      bindUsersService({
        op:"unbind",
        userIds:this.selectInUserTableData.map((item)=>{
          return item.userId
        }).join(','),
        roleId:this.config.roleId
      }).then(res=>{
        if(res.code=='0000'){
          this.$message.success('取消绑定成功')
          this.getOutUserData({
          realName: this.outMgrName,
          roleId: this.config.roleId,
          deptIdCaseChild: this.orgId,
          deptId: this.orgId,
          roleHaveUser: false
        })
        this.getInUserData({
          roleId: this.config.roleId,
          onlyShowDeptId:this.rootId,
          limit: 99999,
          roleHaveUser: true
        })
        }
      })
    },
  }
}
</script>

<style lang="scss">
// @import "@develop2.2/assets/scss/modules/setting_manage/system_settings.scss";
.roleBindUser {
  .el-pagination__jump {
    margin-left: 0px;
  }
  .company-box {
    width: 95%;
    height: 320px;
    overflow: auto;
    float: left;
    margin-top: -8px;
    .company {
      list-style: none;
      li {
        font-size: 12px;
        line-height: 14px;
        padding: 8px 0;
        color: #000;
        border-bottom: 1px dotted #c0c0c0;
        cursor: pointer;
        i {
          display: inline-block;
          line-height: 16px;
          vertical-align: top;
        }
        span {
          display: inline-block;
          width: 80%;
        }
        .el-icon-caret-right {
          color: #3699ff;
          margin-right: 10px;
        }
      }
    }
    .el-pagination__editor {
      .el-input__inner {
        max-height: 80px;
      }
    }
    .active {
      cursor: pointer;
      color: #21b4f9 !important;
      font-weight: bold;
      border-bottom: 1px solid #21b4f9 !important;
      .el-icon-caret-right {
        color: #fe9601 !important;
      }
    }
  }
  .grid-content {
    margin-top: -30px;
    .inputTop {
      margin-left: 10px;
      margin-bottom: 2px;
      margin-top: 24px;
    }
    .el-table {
      margin-bottom: 10px;
    }
    .el-table--small td,
    .el-table--small th {
      padding: 0;
    }
  }
  .searchBtn {
    display: inline-block;
    /* background: #ff000030; */
    font-size: 20px;
    color: #409eff;
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-weight: bold;
    &:hover {
      cursor: pointer;
    }
  }
  .outTable{
    color: #5181FF;
    border: 1px solid #5181FF;
  }
  .inTable{
    color: #fff;
    background: #5181FF;
    border: 1px solid #5181FF;
  }
  .centerBtns {
    text-align: center;
    &:first-child {
      padding-top: 174px;
      padding-bottom: 30px;
    }
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  .el-tree {
    min-width: 100%;
    display: inline-block;
    }
  //修改树状列表样式
  .el-tree-node__expand-icon {
    color: #3699ff;
  }
  .el-tree-node.is-current > .el-tree-node__content {
    cursor: pointer;
    color: #21b4f9 !important;
    font-weight: bold;
    border-bottom: 1px solid #21b4f9 !important;
    .el-tree-node__expand-icon {
      color: #fe9601;
    }
    .el-tree-node__expand-icon.is-leaf {
      color: transparent;
    }
  }
  .el-tree-node__expand-icon.is-leaf {
    color: transparent;
  }
}
    #roleMsg {
    position: fixed;
    z-index: 9999;
    .el-card__body{
      padding: 0;
    }
    ul{
      background-color: #e2e9f0;
      border: 1px solid #222222;
      padding: 5px 0;
      li{
        list-style-type: none;
      height: 18px;
      line-height: 18px;
      padding: 0 10px;
      font-size: 12px;
      }
    }
  }
</style>
