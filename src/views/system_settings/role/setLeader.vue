<!--
 * @Author: <PERSON><PERSON><PERSON>hang
 * @Date: 2021-01-06 15:21:37
 * @LastEditors: <PERSON><PERSON><PERSON> zhang
 * @LastEditTime: 2021-01-15 22:37:31
 * @Description: 设置组长弹框
-->
<template>
  <div class="setLeader">
    <mssSystemDialog
      title="设置组长"
      :show-dialog.sync="dialogTargetVisible"
      :dialog-width="'450px'"
      :custom-class="'dialogClass setLeaderDialog'"
    >
      <div class="members-box">
        <div class="members-header">
          <i aria-hidden="true" class="iconfont iconusergroup1"></i>
          <el-input v-model="filter" placeholder="请输入人员名称">
          </el-input>
          <el-button
            type="primary"
            class="header-right"
            @click="setLeader"
          >设置组长
          </el-button>
        </div>
        <div ref="members-list" class="members-list" :class="hasLeader()">
          <div
            v-for="(item, index) in filterMemberList"
            :key="index"
            :class="setClass(item.id)"
            @click="selectMember(item.id)"
          >
            <i aria-hidden="true" class="iconfont iconicon"></i>
            <span class="member-name">
              {{ item.name }}
            </span>
            <span
              v-if="leaderId == item.id"
              class="leader-title"
            >(组长)</span>
          </div>
        </div>
      </div>
      <!--弹框确定和取消-->
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="bindTargetServiceAjax"
        >确 定</el-button>
        <el-button @click="dialogTargetVisible = false">取 消</el-button>
      </div>
    </mssSystemDialog>
  </div>
</template>

<script>
import {
  getOutUserService, // 获取用户列表
  bindUserService // 绑定用户状态提交
} from '@/api/system_settings/role'

export default {
  name: 'SetLeader',
  props: ['config'],
  data() {
    return {
      dialogTargetVisible: false, // 弹出框默认隐藏
      selectId: null, // 选择的id
      leaderId: null, // 组长的id
      filter: '', // 筛选人员名称参数 （搜索框）
      // 成员列表
      filterMemberList: [], // 筛选后呈现的结果，如果不筛选，则返回的是所有的人员
      memberList: [] // 所有人员的数据
    }
  },
  watch: {
    // 检测filter，当发生变化时间进行筛选/模拟搜索的效果
    filter(n, o) {
      this.filterData()
    }
  },
  created() {
    this.filterData()
  },
  methods: {
    // 初始化，显示弹框，并请求下拉框和指标列表的数据
    setLeaderInitialize() {
      this.selectId = null
      this.leaderId = null
      this.filter = ''
      this.filterMemberList = []
      this.dialogTargetVisible = true
      this.getInUserData({
        roleId: this.config.roleId,
        page: '1',
        roleHaveUser: true
      })
    },
    // 选中组员
    selectMember(id) {
      this.selectId = id
    },
    // 设置组长
    setLeader(index) {
      if (this.selectId) {
        this.leaderId = this.selectId
        this.$refs['members-list'].scrollTop = 0
      } else {
        this.$message({
          showClose: true,
          message: '未选中人员！',
          type: 'warning',
          duration: 1000
        })
      }
    },

    // 确定按钮
    bindTargetServiceAjax() {
      bindUserService({ roleId: this.config.aroleId, userIds: this.leaderId }).then(
        (res) => {
          if (res.code === '0000') {
            this.$message({
              showClose: true,
              message: '组长设置成功！',
              type: 'success',
              duration: 500
            })

            this.dialogTargetVisible = false
          }
        }
      )
    },

    // 动态绑定样式 判断数组中是否有组长
    setClass(id) {
      const isSelected = this.selectId == id ? ' select' : ''
      const isLeader = this.leaderId == id ? ' isLeader' : ''
      const className = 'member-item' + isSelected + isLeader
      return className
    },
    // 动态绑定样式  判断数组中是否存在组长
    hasLeader() {
      let leaderHere = false
      this.filterMemberList.forEach((item) => {
        if (item.id == this.leaderId) {
          leaderHere = true
        }
      })
      return leaderHere == true ? 'hasLeader' : ''
    },

    // 筛选方法
    filterData() {
      const data = this.memberList
      // 对人员列表进行筛选，返回搜索框的内容或当filter为空字符串时，将所有人员列表全都返回
      this.filterMemberList = data.filter(
        (data) =>
          !this.filter ||
          data.name.toLowerCase().includes(this.filter.toLowerCase())
      )
    },

    // 获取绑定用户列表
    getInUserData(param) {
      const paramData = param || {}
      getOutUserService(paramData).then((res) => {
        if (res && res.code === '0000' && res.data && res.data.data) {
          res.data.data.forEach((item) => {
            item.name = item.realName
            item.id = item.userId

            // 获取人员列表，当有人员为组长时，讲人员的id复制给leaderId（标记当前组长长，展示组长tag）
            if (item.groupLeader == '1') {
              this.leaderId = item.id
            }
          })

          // 将用户列表赋值存起来
          this.memberList = res.data.data || []
          // 进行筛选
          this.filterData()
        }
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss">
.dialogClass{
//   .el-dialog {
    .el-dialog__body {
      // overflow: auto;
      .members-box {
        position: relative;
        border: 1px solid #eaecf0;
        .iconfont {
          color: #409eff;
        }
        .members-header {
          width: 100%;
          height: 35px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 6px 8px;
          border-bottom: 1px solid #eaecf0;
          .el-input {
            width: 70%;
            .el-input__inner {
              border: none;
              padding: 0 3px;
            }
          }
          .title {
            // border: 1px solid red;
          }
          .header-right {
            height: 26px;
            padding: 0 20px;
            >span{
                display: block;
                line-height: 26px;
            }

          }
        }
        .members-list {
          overflow: auto;
          height: 250px;
          .member-item {
            padding: 4px 8px;
            cursor: pointer;
            .member-name {
            }
            .leader-title {
              color: #0dbc15;
            }
          }
          .select {
            background-color: #daf1ff;
          }
        }
        .hasLeader {
          padding-top: 28px;
          position: relative;
          .isLeader {
            position: absolute;
            top: 0px;
            width: 100%;
          }
        }
      }
    }
    .el-dialog__header {
      // background: #52bee5;
      padding: 0 80px 0 15px;
      height: 30px;
      line-height: 30px;
      .el-dialog__headerbtn .el-dialog__close {
        // color: #ffffff;
      }
      // .el-dialog__close {
      //   position: relative;
      //   top: -3px;
      //   font-size: 18px;
      //   font-weight: bold;
      // }
    }
    .dialog-footer {
      text-align: center;
    }
//   }
}
</style>
<!--/***
setLeader(item){
      //某按钮点击后弹出此框，将参数传递，并调用此组件的初始化方法
         this.configTarget.param.menuId = this.menuId;//主题页
      或 this.configTarget.param.roleId = item;//角色管理
        this.$refs.TargetAlert.targetInitialize()//调用此组件的初始化方法
      },
      例子参见：importantTargetTopic.vue或index_role.vue
*-->
