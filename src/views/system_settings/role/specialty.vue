<template>
  <div class="Specialty">
    <el-dialog
      title="分配菜单"
      :visible.sync="dialogTargetVisible"
      width="450px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      custom-class="dialogClass"
      @opened="getSpec"
    >
      <div class="special-menu">
        <el-tree
          v-if="showTree"
          ref="specsIds"
          :data="specs"
          show-checkbox
          node-key="id"
          :default-expanded-keys="checkedKeys"
          :default-checked-keys="checkedKeys"
          :props="defaultProps"
        >
        </el-tree>
      </div>
      <!--弹框确定和取消-->
      <div slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="primary"
          @click="bindTargetServiceAjax"
        >确 定</el-button>
        <el-button size="mini" @click="dialogTargetVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSpecService, // 获取菜单列表
  getSelectService, // 获取已绑定菜单列表
  bindSpecService // 绑定菜单
} from '@/api/system_settings/role'
export default {
  name: 'Specialty',
  props: ['config'],
  data() {
    return {
      specs: [],
      // 后台选中的专业
      checkedKeys: [],
      // 页面选中的专业
      ids: [],
      // 新增的专业
      addId: [],
      // 移除的专业
      delIds: [],
      // 重载树
      showTree: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      dialogTargetVisible: false // 弹出框默认隐藏
    }
  },
  methods: {
    getSpec() {
      const that = this
      // 获取所有选中专业ID
      that.getChild = function(spec) {
        const _temArr = []
        const _fn = function(spec) {
          if (spec) {
            spec.forEach((ele, index) => {
              if (ele.children && ele.children.length) {
                _fn(ele.children)
              } else {
                _temArr.push(ele.id)
              }
            })
          }
        }
        _fn(spec)
        that.checkedKeys = _temArr
      }
      getSpecService({ queryAll: true }).then(function(response) {
        that.specs = response.data
      })
      getSelectService({ roleId: that.config.roleId, queryAll: true }).then(
        res => {
          that.getChild(res.data)
        }
      )
    },
    // 初始化，显示弹框，并请求下拉框和指标列表的数据
    specialtyInitialize() {
      this.dialogTargetVisible = true
      this.showTree = false
      setTimeout(() => {
        this.showTree = true
      }, 500)
    },
    // 数组去重
    unique(array) {
      var res = array.filter(function(item, index, array) {
        return array.indexOf(item) === index
      })
      return res
    },
    // 确定按钮，调用父类的刷新事件，并关闭弹框
    bindTargetServiceAjax() {
      const that = this
      const specsIds = this.$refs.specsIds
        .getCheckedNodes()
        .concat(this.$refs.specsIds.getHalfCheckedNodes())
      that.ids = []
      specsIds.forEach(function(ele, index) {
        that.ids.push(ele.id)
      })
      if (0) {
        that.dialogTargetVisible = true // 无增无减时，不关闭弹窗
      } else {
        bindSpecService({
          resourceIds: that.ids.toString(),
          roleId: that.config.roleId
        }).then(function(response) {
          that.$message({
            showClose: true,
            message: '菜单分配成功',
            type: 'success',
            duration: 500,
            onClose: function() {
              that.dialogTargetVisible = false
            }
          })
        })
      }
    },

    // 删除的id
    del(a, b) {
      const arr = []
      a.forEach(function(ele, index) {
        if (b.indexOf(ele) < 0) {
          arr.push(ele)
        }
      })
      return arr
    },
    // 新增的id
    add(a, b) {
      const arr = []
      b.forEach(function(ele, index) {
        if (a.indexOf(ele) < 0) {
          arr.push(ele)
        }
      })
      return arr
    }
  }
}
</script>

<style lang="scss">
// .Specialty {
.dialogClass {
  .special-menu {
    overflow: auto;
    height: 400px;
    background-color: #fff;
  }
}
</style>
<!--/***
target主要适用于以下两种情况：角色管理的绑定指标、主题页的自定义指标
1、引用
 import Target from './target.vue'
  components: { Target},
  2、父组件调用
   <Target @table-fresh="tableFresh" :config="configTarget" ref="TargetAlert"></Target>
3、父组件中的data设置
  configTarget: {
          param:{ menuId: '' },//若是主题页调用，传入menuId;
       或 param:{ roleId: '' },//若是角色管理调用，传入roleId;
          tableListService: this.UrlServiceBase + '/target/selectTargetPage', //指标url,角色管理需要更改后面的地址
          saveTargetService: this.UrlServiceBase + '/target/bindTarget', //绑定指标，角色管理需要更改后面的地址
          optionListService: this.UrlServiceBase + '/target/selectTargetType',  // 指标类别下拉框地址
          saveTargetType:'put',//绑定指标（指标选中）-提交方式
          tableListType:'post',//指标table列表-请求方式
          optionListType:'get',//指标类别下拉框-请求方式
          parentType:'role',//若是角色管理调用，role,字符串;
       或 parentType:'topic',//若是主题页调用，topic，字符串;
        },
 4、父组件添加方法：
 tableFresh(){
       //点击弹框的确定后，执行父页面需要刷新操作
       //若是主题页，需要在最外层的父类刷新数据，如下使用
       <ImportantTargetTopic :location="location" :configs="targetListData" :menuId="menuId"
                            @table-fresh="targetTableFresh"></ImportantTargetTopic>
      }
    注：  其中targetTableFresh为重点指标请求，以此刷新页面

bindTarget(item){
//某按钮点击后弹出此框，将参数传递，并调用此组件的初始化方法
         this.configTarget.param.menuId = this.menuId;//主题页
      或 this.configTarget.param.roleId = item;//角色管理
        this.$refs.TargetAlert.targetInitialize()//调用此组件的初始化方法
      },
5、例子参见：importantTargetTopic.vue或index_role.vue
*-->
