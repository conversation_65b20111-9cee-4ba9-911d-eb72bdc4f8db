<!-- 物资平衡预警确认 -->
<template>
  <div class="design_fee_confirm">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="物资平衡预警确认">
      <div slot="content">
        <mssTable
          ref="table"
          serial
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { commonDown,commonOneDel } from "@/utils/btn"
import { pages } from "@/api/dwep_manager/material";
export default {
  name: "dwep_manager",
  data() {
    return {
      formConfig: [
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        }
      ],
      tableHeader: [
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "创建时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          minWidth: 150,
          tooltip:true,
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          minWidth: 120,
          fixed:'right',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
                {row.isAllowDelete ? (
                  <span
                    class="table_btn "
                    onClick={() => {this.action(row, "del")}}
                  >
                    删除
                  </span>
                ) : (
                  ""
                )}
              </span>
            );
          },
        },
      ],
      tableApi: pages,
      loadParam: {},
      staticSearchParam:{}
    };
  },
  mounted() {

  },
  methods: {
    // 查询
    search(form) {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(JSON.stringify(form))
      if (obj.createTime && Array.isArray(obj.createTime)) {
        obj.createDate = obj.createTime[0];
        obj.finishDate = obj.createTime[1];
      }
      this.staticSearchParam = obj
      this.loadParam = obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj);
      })
    },

    // 重置
    reset(form) {
      form.createTime=["",""]
      this.search(form);
    },

    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/dwep_manager/completion_balance/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/dwep_manager/completion_balance/view",
            query: { boId: row.id },
          });
          break;
        case "del":
          commonOneDel.call(this,row.id, delService, (res) => {
            if(res.code=='0000'){
              this.$message.success('删除成功')
              this.search()
            }
          })
          break;
      }
    },

  },
};
</script>
