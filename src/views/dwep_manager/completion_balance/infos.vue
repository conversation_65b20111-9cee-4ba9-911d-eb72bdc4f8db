<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="design_link_info">
    <div id="sectionInfo" class="page-anchor-point"></div>

    <mssCard title="基本信息">
      <div slot="content">
        <div slot="content">
          <mssForm
            ref="basicForm"
            labelWidth="200px"
            :config="baseConfig"
            labelPosition="left"
            :form="baseForm"
            :disableForm="true"
            :rules="formRules"
          ></mssForm>
        </div>
      </div>
    </mssCard>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="预警数据">
      <div slot="content">
        <el-table height="450" :data="aboveDesignData" border style="width: 100%" v-loading="loading">
          <el-table-column prop="projectCode" label="项目编码"></el-table-column>
          <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materialCode" label="物料编码"></el-table-column>
          <el-table-column prop="materialName" label="物料名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="expenditureVolume" label="项目支出量"></el-table-column>
          <el-table-column prop="auditVolume" label="结算审定量"></el-table-column>
          <el-table-column prop="quantityVariance" label="差异数量"></el-table-column>
          <el-table-column prop="quantityReason" label="差异原因"></el-table-column>
        </el-table>

        <div style="text-align: right;margin-top: 10px">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next"
            :total="pagination.total">
          </el-pagination>
        </div>
      </div>
    </mssCard>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
          v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import { pages } from "@/api/dwep_manager/difference";
import {getInfos} from "@/api/dwep_manager/material";
import { commonDown } from "@/utils/btn"
export default {
  name: "material_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
    disableForm:{
      type: Boolean,
      default: false,
    }
  },
  watch:{
  },
  data() {
    return {
      loading: false,
      aboveDesignData: [],
      isEdit:true,
      boId: "",
      businessType: "",
      nodeName: "草稿",
      dealPage:false,
      baseForm: {},
      stationary1:[],
      stationary2:[],
      pageAnchorConfig: [
        {
          text: "基础<br>信息",
          id: "sectionInfo",
        },
        {
          text: "预警<br>数据",
          id: "sectionBasic",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
      baseConfig: [
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 6,
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 6,
        }
      ],
      basicForm:{
        totalDeduction:''
      },
      searchFieldList: [

      ],
      projectColumns: [

      ],
      tableQueryParams:{},
      TaskParam1:{},
      TaskParam2:{},
      pagination: {
        current: 1,
        size: 10,
        total: 0
      }
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      this.isEdit=false
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
    } else {
      this.dealPage=true
    }
    for (let i in this.baseConfig) {
      this.baseConfig[i].label = `${this.baseConfig[i].label}：`;
    }
    // 浏览器传参
    this.boId = this.$route.query.boId;
    this.TaskParam1={
      boId:this.boId,
      sourceType:1
    }
    this.TaskParam2={
      boId:this.boId,
      sourceType:2
    }
    this.getDetailsData();
  },
  methods: {
    handleSizeChange(size) {
      this.pagination.size = size;
      this.getData();
    },
    handleCurrentChange(current) {
      this.pagination.current = current;
      this.getData();
    },
    getDetailsData() {
      getInfos(this.boId).then((res) => {
        if (res.code === '0000' && res.data) {
          this.baseForm = res.data;
          this.basicForm = { ...this.basicForm, ...res.data };
          this.businessType = res.data.businessType;
          this.$emit("getWorkCode", this.businessType);
          console.log(res.data);
          this.getData();
        } else {
          console.error('接口返回无有效数据:', res);
          this.aboveDesignData = []; // 清空旧数据
        }
      }).catch(err => {
        console.error('获取详情数据失败:', err);
      });
    },
    getData() {
      const { current, size } = this.pagination;

      const params = {
        projectCode: this.baseForm.projectCode || '',
        current: current,
        size: size
      };

      if (!params.projectCode ) {
        console.warn('缺少必要参数，无法加载数据');
        this.aboveDesignData = [];
        return;
      }
      this.loading = true
      pages(params).then((res) => {
        if (res.code === '0000' && res.data) {
          this.aboveDesignData = res.data.data || [];
          this.pagination.total = Number(res.data.total) || 0; // 强制转为数字
        } else {
          this.aboveDesignData = [];
          this.pagination.total = 0;
        }
        console.log(res);
      }).finally(() => {
        this.loading = false
      })
    }
  },
};
</script>

<style lang="scss" >
.fee-form_edit{
  margin-bottom: 0 ;
  .el-form-item__label {
        visibility: hidden;
        height: 0;
  }

}
.fee-form_edit.is-error{
  margin-bottom: 22px;
}
.design_link_info{
  .upload-btn {
    display: inline-block;
    margin-right: 10px;
  }
  .red{
    color: red;
  }
}
</style>
