   <template>
  <div class="project-management-container">
    <el-form :model="form" label-width="100px" style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="项目名称">
            <el-input v-model="form.projectName" placeholder="请输入项目名称" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="项目编号">
            <el-input v-model="form.projectNo" placeholder="请输入项目编号" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类型">
            <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="text-align: right;">
        <el-col :span="24">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="handleAdd">新增</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="projectList" style="width: 100%" v-loading="loading">
      <el-table-column label="序号" type="index" min-width="50" align="center"></el-table-column>
      <el-table-column prop="projectName" label="项目名称" min-width="100" align="center"></el-table-column>
      <el-table-column prop="projectNo" label="项目编号" min-width="100" align="center"></el-table-column>
      <el-table-column prop="taskCode" label="任务编号" min-width="100" align="center"></el-table-column>
      <el-table-column prop="type" label="类型" min-width="100" align="center">
        <template slot-scope="scope">
          {{ getTypeName(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="createdBy" label="创建人" min-width="100" align="center"></el-table-column>
      <el-table-column prop="createdTime" label="创建时间" min-width="100" align="center"></el-table-column>
      <el-table-column label="操作" width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.pageNum"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next"
      :total="pagination.total">
    </el-pagination>

    <!-- 弹窗表单 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
      @close="resetForm"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="projectForm"
        label-width="100px"
        style="max-width: 600px; margin: auto;"
      >
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName"></el-input>
        </el-form-item>

        <el-form-item label="项目编号" prop="projectNo">
          <el-input v-model="form.projectNo"></el-input>
        </el-form-item>

        <el-form-item label="任务编号" prop="taskCode" v-if="form.type === 'MMAT_TRANS_CREATE_APP'">
          <el-input type="textarea" v-model="form.taskCode" placeholder="多个任务编号请用英文逗号隔开"></el-input>
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>


      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDwepWhiteListPageService,
  addDwepWhiteListService,
  deleteDwepWhiteListByIdService,
  editDwepWhiteListService
} from '@/api/dwep_manager/whitelist'

export default {
  data() {
    return {
      loading: false,
      projectList: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      dialogVisible: false,
      dialogTitle: "新增项目",
      currentIndex: -1,
      form: {
        projectName: "",
        projectNo: "",
        type: "",
        taskCode: "",
      },
      rules: {
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" }
        ],
        projectNo: [
          { required: true, message: "请输入项目编号", trigger: "blur" }
        ],
        type: [
          { required: true, message: "请选择类型", trigger: "change" }
        ]
      },
      typeOptions: [
        { value: "MMAT_TRANS_CREATE_APP", label: "调拨" },
        { value: "project_verify", label: "验证" }
      ]
    };
  },
  mounted() {
    console.log('11111')
    this.fetchData();
  },
  methods: {
    // 获取类型名称
    getTypeName(type) {
      const option = this.typeOptions.find(item => item.value === type);
      return option ? option.label : "未知类型";
    },

    // 加载数据
    fetchData() {
      this.loading = true;
      getDwepWhiteListPageService( {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize

      }).then(res => {
        console.log(res.data)
        this.projectList = res.data.data || [];
        this.pagination.total = res.data.total || 0;
      }).finally(()=>{
        this.loading = false;
      }).catch(()=>{
        this.$message.error("获取数据失败");
      })
    },

    // 分页事件
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.fetchData();
    },
    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.fetchData();
    },
    // 新增
    handleAdd() {
      this.dialogTitle = "新增项目";
      this.currentIndex = -1;
      this.form = {
        projectName: "",
        projectNo: "",
        type: ""
      };
      this.dialogVisible = true;
    },
    handleSearch() {
      this.loading = true;
      const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          projectName: this.form.projectName,
          projectNo: this.form.projectNo,
          taskCode: this.form.taskCode,
          type: this.form.type

      };

      getDwepWhiteListPageService(params)
        .then(res => {
          console.log(res.data);
          this.projectList = res.data.data || [];
          this.pagination.total = res.data.total || 0;
        })
        .finally(() => {
          this.loading = false;
        })
        .catch(() => {
          this.$message.error("查询数据失败");
        });
    },
    // 编辑
    handleEdit(index, row) {
      this.dialogTitle = "编辑项目";
      this.currentIndex = index;
      this.form = {
        id: row.id,
        projectName: row.projectName,
        projectNo: row.projectNo,
        type: row.type,
        taskCode: row.taskCode
      };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(index, row) {
      this.$confirm("确认删除该项目吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        try {
          await deleteDwepWhiteListByIdService({ id: row.id });
          this.$message.success("删除成功");
          this.fetchData();
        } catch (error) {
          this.$message.error("删除失败");
          console.error("删除失败:", error);
        }
      });
    },
    // 提交表单
    async submitForm() {
      try {
        const submitData = {
          id: this.form.id,
          projectName: this.form.projectName,
          projectNo: this.form.projectNo,
          type: this.form.type,
          taskCode: this.form.taskCode,
        };

        if (this.currentIndex === -1) {
          await addDwepWhiteListService(submitData);
          this.$message.success("新增成功");
        } else {
          await editDwepWhiteListService(submitData);
          this.$message.success("编辑成功");
        }

        this.dialogVisible = false;
        this.fetchData();
      } catch (error) {
        console.error("提交失败:", error);
      }
    },
    resetQuery() {
      this.form = {
        projectName: '',
        projectNo: '',
        taskCode: '',
        type: ''
      };
      this.handleSearch(); // 可选：重置后自动执行一次空查询
    },
    resetForm() {
      this.form = {
        id: null,
        projectName: "",
        projectNo: "",
        type: "",
        taskCode: ""
      };
      this.$refs.projectForm.resetFields();
    }
  }
};
</script>

<style scoped>
.project-management-container {
  padding: 20px;
}
</style>
