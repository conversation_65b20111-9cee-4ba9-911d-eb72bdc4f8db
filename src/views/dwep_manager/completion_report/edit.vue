<!-- 设计费确认处理页面 -->
<template>
  <div class="fee_confirm_detail page-anchor-parentpage">
    <div class="operate-btn">
      <el-button v-if="warnType === '1'" type="primary" @click="submit()">提交</el-button>
      <el-button v-else type="success" @click="read()">已阅</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <infos
      labelPosition="top"
      ref="infos"
      :formRules="formRules"
      :nodeCode=nodeCode
      :disableForm="disableForm"
      @getWorkCode="getWorkCode"
    ></infos>
  </div>
</template>

<script>
import infos from "./infos.vue";
import {readAlready} from "@/api/dwep_manager/material";
export default {
  name: "material_detail",
  components: { infos },
  data() {
    return {
      boId: "",
      workflowCode: "",
      completeTaskUrl: "", //流程处理地址
      returnAddress: "/dwep_manager/completion_report", //流程提交成功返回路由地址
      formRules: {},
      disableForm:false,
      nodeCode: [],
      taskId: '',
      warnType: "",
    };
  },
  created() {
    this.boId = this.$route.query.boId;
  },
  methods: {
    read() {
      readAlready(this.taskId, this.boId).then(res => {
        this.goBack();
      })
    },
    getWorkCode(param) {
      this.workflowCode = param;
      this.$nextTick(() => {
        this.$refs.workFlow.init();
      });
    },
    // 返回上一页
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.$route.query.returnUrl||"/dwep_manager/completion_report",
      });
    },
    getNodeData(data) {
      console.log(data)
      this.warnType = this.$refs.infos.basicForm.warnType
      this.taskId = data.taskId
      this.nodeCode = data.nodeCode || ''
      // 草稿才可以编辑
      if (this.nodeCode === 'draft' || !this.nodeCode) {
        this.disableForm = false
      } else {
        this.disableForm = true
      }
    },
    // 保存
    save(cb){
      let param={
        ...this.$refs.infos.$refs.basicForm.modelForm
      }
      cb(param)
    },
    // 提交--先保存后提交
    submit() {
      this.completeTaskUrl = this.$refs.infos.basicForm.completeTaskUrl;
      this.$refs.workFlow.opendialogInitNextPath();
    },
    //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      let name = {code: 'name', value: this.$refs.infos.$refs.basicForm.modelForm.name}
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit() {
      return "";
    },

    beforeNode() {
      const fileNameArr = {

      };
      // 设置默认处理人
      return fileNameArr;
    },
    // 必填校验
    initFormRules(param) {
      this.formRules = param;
    },
    // 只读表单
    getReadonlyList(param) {
      if (param.length) {
        this.$refs.infos.basicConfig.forEach((item) => {
          if (param.includes(item.prop)) {
            item.disabled = true;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
