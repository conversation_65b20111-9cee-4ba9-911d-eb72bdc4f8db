<!-- 设计费确认 -->
<template>
  <div class="design_fee_confirm">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="物资支出数量预警确认">
      <div slot="content">
        <mssTable
          ref="table"
          serial
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { commonDown,commonOneDel } from "@/utils/btn"
import { page } from "@/api/dwep_manager/material";
export default {
  name: "dwep_manager",
  data() {
    return {
      formConfig: [
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "预警类型",
          type: "select",
          option:[],
          fieldName: "warnType",
        },
        {
          label: "创建日期",
          type: "daterange",
          format:'yyyy-MM-dd',
          valueFormat:'yyyy-MM-dd',
          fieldName: "createTime",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
      ],
      tableHeader: [
        {
          prop: "id",
          label: "任务单编号",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "name",
          label: "任务单名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "warnType",
          label: "预警类型",
          align: "center",
          minWidth: 150,
          tooltip:true,
          formatter: (row) => {
            // 预警类型（1低于；2高于）
            return row.warnType==='1'?'低于':'高于'
          }
        },
        {
          prop: "createDate",
          label: "创建时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          minWidth: 150,
          tooltip:true,
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          minWidth: 120,
          fixed:'right',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
                {row.isAllowDelete ? (
                  <span
                    class="table_btn "
                    onClick={() => {this.action(row, "del")}}
                  >
                    删除
                  </span>
                ) : (
                  ""
                )}
              </span>
            );
          },
        },
      ],
      tableApi: page,
      loadParam: {},
      staticSearchParam:{}
    };
  },
  mounted() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      this.$set(this.formConfig[2], 'options', [
        { label: '低于', value: '1' },
        { label: '高于', value: '2' }
      ]);

      this.$set(this.formConfig[4], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 查询
    search(form) {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(JSON.stringify(form))
      if (obj.createTime && Array.isArray(obj.createTime)) {
        obj.createDate = obj.createTime[0];
        obj.finishDate = obj.createTime[1];
      }
      this.staticSearchParam = obj
      this.loadParam = obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj);
      })
    },

    // 重置
    reset(form) {
      form.createTime=["",""]
      this.search(form);
    },

    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/dwep_manager/completion_report/edit",
            query: { boId: row.id },
          });
          break;
        case "view":
          this.$router.push({
            path: "/dwep_manager/completion_report/view",
            query: { boId: row.id },
          });
          break;
        case "del":
          commonOneDel.call(this,row.id, delService, (res) => {
            if(res.code=='0000'){
              this.$message.success('删除成功')
              this.search()
            }
          })
          break;
      }
    },

  },
};
</script>
