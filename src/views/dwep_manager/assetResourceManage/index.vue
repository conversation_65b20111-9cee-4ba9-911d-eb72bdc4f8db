<template>
  <div class="assetResourceManage_index">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="资产类别与资源类型映射关系">
      <div slot="headerBtn">
        <el-button @click="handleNew">新增</el-button>
        <el-button type="primary" >导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          border
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
    <el-dialog title="新增资产类别与资源类型映射关系" :visible.sync="dialogVisible" :show-close="false" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="资产类别编码" prop="categoryCode">
          <el-input v-model="form.categoryCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="资产类别名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="资产类别描述" prop="categoryDesc">
          <el-input v-model="form.categoryDesc" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="资源类别" prop="resourceCategory">
          <el-input v-model="form.resourceCategory" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">提 交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  pageService,
  addService,
  updateService,
  delService
} from '@/api/dwep_manager/assetResource'
import { commonOneDel } from "@/utils/btn"

export default {
  name: 'AssetResourceManage',

  data() {
    return {
      formConfig: [
        {
          label: "资产类别编码",
          type: "input",
          fieldName: "categoryCode",
        },
        {
          label: "资产类别名称",
          type: "input",
          fieldName: "categoryName",
        },
        {
          label: "资源类型",
          type: "input",
          fieldName: "resourceCategory",
        }
      ],
      rules: {
        categoryCode: [
          { required: true, message: "请输入资产类别编码", trigger: "blur" },
          { pattern: /^\d{2}\.\d{2}-\d{2}-\d{2}-\d{2}\.\d{4}$/, message: "资产类别编码格式应为：XX.XX-XX-XX-XX.XXXX", trigger: "blur" }
        ],
        categoryName: [
          { required: true, message: "请输入资产类别名称", trigger: "blur" }
        ],
        categoryDesc: [
          { required: true, message: "请输入资产类别描述", trigger: "blur" }
        ],
        resourceCategory: [
          { required: true, message: "请输入资源类别", trigger: "blur" }
        ]
      },
      tableHeader: [
        {
          prop: "categoryCode",
          label: "资产类别编码",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "categoryName",
          label: "资产类别名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "categoryDesc",
          label: "资产类别描述",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "resourceCategory",
          label: "资源类别",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "createdTime",
          label: "创建时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "updatedTime",
          label: "修改时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          minWidth: 120,
          fixed:'right',
          formatter: (row) => {
            return (
              <span>
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "view")}}
                >查看</span>
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "del")}}
                >删除</span>
              </span>
            );
          },
        },
      ],
      tableApi: pageService,
      staticSearchParam:{},
      dialogVisible: false,
      form: {},
    }
  },
  created() {
  },
  methods: {
    handleNew() {
      // 初始化表单数据
      this.form = {
        categoryCode: '',
        categoryName: '',
        categoryDesc: '',
        resourceCategory: ''
      }
      this.dialogVisible = true
    },
    cancel(){
      this.$refs.form.resetFields()
      this.dialogVisible = false;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 调用新增API
          addService(this.form).then(res => {
            if (res.code === '0000') {
              this.$message.success('新增成功')
              // 关闭弹窗
              this.cancel()
              // 刷新表格数据
              this.$refs.table.getTableData()
            } else {
              this.$message.error(res.message || '新增失败')
            }
          }).catch(error => {
            console.error('新增失败:', error)
            this.$message.error('新增失败，请稍后重试')
          })
        }
      })
    },
    // 查询
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {...form}
      this.$nextTick(()=>{
        this.$refs.table.getTableData(this.staticSearchParam);
      })
    },
    // 重置
    reset(form) {
      this.search(form);
    },
    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.dialogVisible = true;
          break;
        case "del":
          commonOneDel.call(this,row.id, delService, (res) => {
            if(res.code=='0000'){
              this.$message.success('删除成功')
              this.search()
            }
          }, 'path')
          break;
      }
    },
  }
}
</script>

<style scoped>

</style>
