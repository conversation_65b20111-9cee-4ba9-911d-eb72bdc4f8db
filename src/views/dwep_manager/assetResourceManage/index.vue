<template>
  <div class="assetResourceManage_index">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="资产类别与资源类型映射关系">
      <div slot="headerBtn">
        <!-- 使用 flex 容器包裹所有按钮和上传组件 -->
        <div style="display: flex; gap: 8px; align-items: center;">
          <el-button @click="handleNew" :loading="loading">新增</el-button>
          <el-button @click="downloadTemplate" :loading="loading">下载模板</el-button>
          <el-upload
            ref="excelFile"
            :show-file-list="false"
            :auto-upload="true"
            :http-request="handleImportExcel"
            action="">
            <el-button type="primary" :loading="loading">导入</el-button>
          </el-upload>
          <el-button type="primary" @click="exportHandle" :loading="loading">导出</el-button>
        </div>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          border
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :show-close="false" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="资产类别编码" prop="categoryCode">
          <el-input v-model="form.categoryCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="资产类别名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="资产类别描述" prop="categoryDesc">
          <el-input v-model="form.categoryDesc" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="资源类别" prop="resourceCategory">
          <el-input v-model="form.resourceCategory" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">提 交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  pageService,
  addService,
  updateService,
  delService,
  downloadService,
  downloadTemplateService,
  importExcelService
} from '@/api/dwep_manager/assetResource'
import { commonDown, commonOneDel } from "@/utils/btn"

export default {
  name: 'AssetResourceManage',

  data() {
    return {
      formConfig: [
        {
          label: "资产类别编码",
          type: "input",
          fieldName: "categoryCode",
        },
        {
          label: "资产类别名称",
          type: "input",
          fieldName: "categoryName",
        },
        {
          label: "资源类型",
          type: "input",
          fieldName: "resourceCategory",
        }
      ],
      rules: {
        categoryCode: [
          { required: true, message: "请输入资产类别编码", trigger: "blur" },
          { pattern: /^\d{2}\.\d{2}-\d{2}-\d{2}-\d{2}\.\d{4}$/, message: "资产类别编码格式应为：XX.XX-XX-XX-XX.XXXX", trigger: "blur" }
        ],
        categoryName: [
          { required: true, message: "请输入资产类别名称", trigger: "blur" }
        ],
        categoryDesc: [
          { required: true, message: "请输入资产类别描述", trigger: "blur" }
        ],
        resourceCategory: [
          { required: true, message: "请输入资源类别", trigger: "blur" }
        ]
      },
      tableHeader: [
        {
          prop: "categoryCode",
          label: "资产类别编码",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "categoryName",
          label: "资产类别名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "categoryDesc",
          label: "资产类别描述",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "resourceCategory",
          label: "资源类别",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "createdTime",
          label: "创建时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "updatedTime",
          label: "修改时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          minWidth: 120,
          fixed:'right',
          formatter: (row) => {
            return (
              <span>
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "edit")}}
                >编辑</span>
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "del")}}
                >删除</span>
              </span>
            );
          },
        },
      ],
      tableApi: pageService,
      staticSearchParam:{},
      dialogVisible: false,
      form: {},
      isEdit: false, // 是否为编辑模式
      dialogTitle: '新增资产类别与资源类型映射关系', // 弹窗标题
      loading: false
    }
  },
  created() {
  },
  methods: {
    handleNew() {
      // 设置为新增模式
      this.isEdit = false
      this.dialogTitle = '新增资产类别与资源类型映射关系'
      // 初始化表单数据
      this.form = {
        categoryCode: '',
        categoryName: '',
        categoryDesc: '',
        resourceCategory: ''
      }
      this.dialogVisible = true
    },
    downloadTemplate() {
      commonDown({}, downloadTemplateService);
    },
    cancel(){
      this.$refs.form.resetFields()
      this.dialogVisible = false;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 根据模式调用不同的API
          const apiService = this.isEdit ? updateService : addService
          const successMessage = this.isEdit ? '编辑成功' : '新增成功'
          const errorMessage = this.isEdit ? '编辑失败' : '新增失败'

          apiService(this.form).then(res => {
            if (res.code === '0000') {
              this.$message.success(successMessage)
              // 关闭弹窗
              this.cancel()
              // 刷新表格数据
              this.$refs.table.getTableData()
            }
          }).catch(error => {
            console.error(`${errorMessage}:`, error)
            this.$message.error(`${errorMessage}，请稍后重试`)
          })
        }
      })
    },
    // 查询
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {...form}
      this.$nextTick(()=>{
        this.$refs.table.getTableData(this.staticSearchParam);
      })
    },
    // 重置
    reset(form) {
      this.search(form);
    },
    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          // 设置为编辑模式
          this.isEdit = true
          this.dialogTitle = '编辑资产类别与资源类型映射关系'
          // 将选中行数据填充到表单中
          this.form = {
            id: row.id,
            categoryCode: row.categoryCode,
            categoryName: row.categoryName,
            categoryDesc: row.categoryDesc,
            resourceCategory: row.resourceCategory
          }
          this.dialogVisible = true
          break;
        case "del":
          commonOneDel.call(this,row.id, delService, (res) => {
            if(res.code=='0000'){
              this.$message.success('删除成功')
              this.search(this.$refs.searchForm.searchForm)
            }
          }, 'path')
          break;
      }
    },
    //导出
    exportHandle() {
      commonDown(this.$refs.searchForm.searchForm, downloadService);
    },
    handleImportExcel(params) {
      this.loading = true
      const formData = new FormData()
      formData.append('file', params.file)
      importExcelService(formData).then((res) => {
        this.loading = false
        if (res.code === '0000') {
          this.$message.success(res.data)
          // 刷新表格数据
          this.$refs.table.getTableData()
        } else {
          this.$message.error(res.data)
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
