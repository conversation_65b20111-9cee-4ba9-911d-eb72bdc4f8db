/**
* @author: ty
* @date: 2023-06-25
* @description:
*/
<template>
  <div>
    <h1>首页</h1>
    <mssBreadcrumb id="breadcrumb-container" class="breadcrumb-container" />
    <hr>
    <h1>h1文字abcde</h1>
    <h2>h2文字abcde</h2>
    <p>p标签文字没空就</p>
    <span>span标签文字没空就</span>
    <div>div内的内容信息178mmmkkkkiii</div>

    <h2 id="section-btn" class="page-anchor-point">按钮</h2>
    <el-button>默认按钮</el-button>
    <el-button type="primary">主要按钮</el-button>
    <el-button type="success">成功按钮</el-button>
    <el-button type="info">信息按钮</el-button>
    <el-button type="warning">警告按钮</el-button>
    <el-button type="danger">危险按钮</el-button>
    <div style="margin: 6px 0;" />
    <el-button type="primary">查询</el-button>
    <el-button>重置</el-button>

    <h2 id="section-msg" class="page-anchor-point">message</h2>
    <el-button :plain="true" @click="open2">成功</el-button>
    <el-button :plain="true" @click="open3">警告</el-button>
    <el-button :plain="true" @click="open1">消息</el-button>
    <el-button :plain="true" @click="open4">错误</el-button>

    <h2 id="section-form" class="page-anchor-point">form（查询表单、查询条件）</h2>
    <mssSearchForm
      :search-config="searchConfig"
    />

    <h2 id="section-card" class="page-anchor-point">Card组件</h2>
    <h3>demo1</h3>
    <mssCard>
      <div slot="headerBtn">
        <el-button type="primary">查询</el-button>
        <el-button>重置</el-button>
      </div>
    </mssCard>
    <h3>demo2</h3>
    <mssCard>
      <div slot="headerBtn">
        <el-button type="primary">查询</el-button>
        <el-button>重置</el-button>
      </div>
      <div slot="content">
        <mssForm :config="formConfig" :label-position="'top'" />
      </div>
    </mssCard>
    <h3>demo3</h3>
    <mssCard>
      <div slot="headerBtn">
        <el-button>查询</el-button>
        <el-button>查询</el-button>
      </div>
      <div slot="content">
        <mssForm
          :config="formConfig"
          :label-position="'left'"
          :form="formModel"
          :disable-form="true"
        />
      </div>
    </mssCard>

    <h2>页面滚动定位</h2>
    引入mssPageAnchor并且配置config，同时也要给页面上锚点元素加上id="section-2" class="page-anchor-point"
    <br>
    约定：为保持页面美观，文字保持在3~4字之间，格式为: AA\<\br>BB, AB\<\br>CD
    <mssPageAnchor :config="pageAnchorConfig" />

    <h2>table</h2>
    <el-table
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column
        prop="date"
        label="日期"
        width="180"
      />
      <el-table-column
        prop="name"
        label="姓名"
        width="180"
      />
      <el-table-column
        prop="address"
        label="地址"
        :formatter="formatter"
      />
    </el-table>
  </div>

</template>

<script>

export default {
  name: 'DashboardIndex',
  data() {
    return {
      form: {
        name: '',
        region: '',
        date1: '',
        date2: '',
        delivery: false,
        type: [],
        resource: '',
        desc: ''
      },
      searchConfig: [
        {
          label: '租户名称：',
          type: 'input',
          fieldName: 'platformId'
        },
        {
          label: '租户名称：',
          type: 'input',
          fieldName: 'platformId1'
        },
        {
          label: '租户名称：',
          type: 'input',
          fieldName: 'platformId2'
        },
        {
          label: '消息渠道：',
          type: 'dialog',
          fieldName: 'channelId3'
        },
        {
          label: '渠道账号：',
          type: 'select',
          options: [],
          fieldName: 'channelAccId3'
        },
        {
          label: '渠道账号：',
          type: 'select',
          options: [],
          fieldName: 'channelAccId3'
        },
        {
          label: '渠道账号2：',
          type: 'daterange',
          span: 12,
          fieldName: 'channelAccId6'
        },
        {
          label: '渠道账号2：',
          type: 'date1',
          span: 8,
          fieldName: 'startDate',
          fieldName2: 'endDate'
        },
        {
          label: '渠道账号2：',
          type: 'date2',
          span: 12,
          fieldName: 'startDate',
          fieldName2: 'endDate'
        }
      ],
      formConfig: [
        {
          label: '附件类型名称：',
          type: 'timePicker',
          prop: 'name',
          span: 12
        },
        {
          label: '附件类型编码：',
          type: 'input',
          span: 12,
          prop: 'businessType',
          disabled: false,
          rules: [
            { required: true, message: '附件类型编码不能为空', trigger: 'blur' }
          ]
        },
        {
          label: '附件类型权限：',
          type: 'select',
          span: 12,
          prop: 'bucketPolicy',
          // labelWidth: "140px",
          data: [{
            id: true,
            name: '启用'
          }]
        }
      ],
      formModel: {
        name: '1233',
        businessType: '13213',
        bucketPolicy: 321421
      },
      pageAnchorConfig: [
        {
          text: '按钮',
          id: 'section-btn'
        },
        {
          text: '消息<br>提示',
          id: 'section-msg'
        },
        {
          text: 'form',
          id: 'section-form'
        },
        {
          text: 'Card',
          id: 'section-card'
        }
      ],
      tableData: [{
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      }]
    }
  },
  methods: {
    open1() {
      this.$message('这是一条消息提示')
    },
    open2() {
      this.$message({
        message: '恭喜你，这是一条成功消息',
        type: 'success'
      })
    },

    open3() {
      this.$message({
        message: '警告哦，这是一条警告消息',
        type: 'warning'
      })
    },

    open4() {
      this.$message.error('错了哦，这是一条错误消息')
    },

    onSubmit() {
    },

    formatter(row, column) {
      var str = 'aaa<br>bbbb'
      return (
        <div v-html={str}></div>
      )
      // return (
      //   <div domPropsInnerHTML={str}></div>
      // )
      // return (
      //   <el-input v-model={row.address}></el-input>
      // )
    }
  }
}
</script>
