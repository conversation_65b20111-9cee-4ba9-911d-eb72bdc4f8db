/**
* @author: ty
* @date: 2023-07-22
* @description: 待阅消息|已阅消息的列表
*/
<template>
  <mssTable
    ref="table"
    :api="tableApi"
    :columns="columns"
    border
    :staticSearchParam="staticSearchParam"
  ></mssTable>
</template>

<script>
import {personalMsgsService} from "@/api/message_manager/message_manager_api";

export default {
  name: 'MsgList',
  props: {
    status: {
      type: String,
      default: ''
    }
  },
  data(){
    return {
      tableApi: personalMsgsService,
      staticSearchParam: {
        acceptorId: sessionStorage.getItem('userId'),
        status: this.status
      },
      columns: [
        {
          label: '消息',
          prop: 'title',
          tooltip: true,
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >{row.title}</a>
            )
          }
        },
        {
          label: '派发人',
          prop: 'creatorName',
          width: 100
        },
        {
          label: '接收人',
          prop: 'acceptorName',
          width: 100
        },
        {
          label: '派发时间',
          prop: 'createDate',
          width: 200
        },
        {
          label: '消息类型',
          prop: 'messageTypeName',
          width: 100
        },
      ]
    }
  },
  created(){
  },
  methods: {
    // 点击标题名称，跳转页面
    operateHandle(row){
      // 区分消息的消息类型 messageType: 0一般消息 1待确认后消除 2业务触发消除
      this.$router.push({
        path: row.url || '/message/view',
        query: {
          messageId: row.id,
          messageType: row.messageType
        }
      })
    }
  }
}
</script>

