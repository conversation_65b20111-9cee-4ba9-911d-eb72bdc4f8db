/**
* @author: ty
* @date: 2023-07-22
* @description: 待阅消息\已阅消息 1.daiyue 2 yiyue
*/
<template>
  <div class="MessageManagerIndex">
    <div class="head-btns">
      <a v-if="showMoreBtn" href="javascript:;" @click="showMore">更多 > </a>
    </div>
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="待阅" name="1">
        <msgList key="1" status="1"></msgList>
      </el-tab-pane>
      <el-tab-pane label="已阅" name="2" lazy>
        <msgList key="2" status="2"></msgList>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import msgList from './components/msg_list'
export default {
  name: 'MessageManagerIndex',
  components: {msgList},
  props: {
    // 是否显示【更多】按钮
    showMoreBtn: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      activeName: '1',// 默认显示待阅列表
    }
  },
  methods: {
    handleClick() {},
    showMore() {
      this.$router.push('/message/index')
    }
  }
}
</script>

<style scoped lang="scss">
  @import "~@/styles/variables.scss";
  .MessageManagerIndex{
    position: relative;
    .head-btns{
      position: absolute;
      right: 0;
      top: 0;
      z-index: 99;
      font-size: 14px;
      font-weight: 500;
      color: #909399;
      height: 40px;
      line-height: 40px;
      padding-right: 10px;
      &:hover{
        color: $color-sub1;
      }
    }
  }
</style>
