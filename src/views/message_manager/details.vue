/**
* @author: ty
* @date: 2023-07-22
* @description: 一句话消息的展示页面
*/
<template>
  <div class="MessageDetails" v-loading="pageLoading">
    <div style="margin-top: 30px;"></div>
    <mssCard title="消息详情">
      <div slot="content">
        <p class="message-title" v-if="showTitle">{{messageTitle}}</p>
        <div v-if="orderFlag">
          截至{{closeData}}晨，根据系统统计显示，您名下有采购费用订单未接收完毕，请您及时根据业务进展情况进行相应的接收处理。
          <br>如您已完成订单的费用接收，或订单业务仍在进行过程中，未达到完全费用接收的条件，请忽略本提示。
        </div>
      </div>
    </mssCard>
    <purchase-order :date="formattedDate" :manager="order.manager" :dispatchTime="order.dispatchTime"
      v-if="orderFlag"></purchase-order>
  </div>
</template>

<script>
import {findByMsgIdService} from "@/api/message_manager/message_manager_api";
import purchaseOrder from "./purchase_order/index.vue"
export default {
  components: {
    purchaseOrder
  },
  name: 'MessageDetails',
  data(){
    return {
      showTitle: true,
      orderFlag: false,
      closeData: '',
      formattedDate: '',
      order: {
        dispatchTime: '',
        manager: '',
      },
      orderUrl: '/pccw_menu/acceptance_link/purchase_order/warning_list?',
      id: '',
      messageType: '',
      messageTitle: '',
      pageLoading: false
    }
  },
  created(){
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.messageId
    this.messageType = urlQuery.type
    this.getData()
  },
  methods: {
    getData(){
      this.pageLoading = true
      findByMsgIdService(this.id)
      .then(res => {
        this.messageTitle = res?.data?.title || ''
        if (res.code === '0000') {
          let subString = "费用采购订单接收提醒"
          let title = res.data.title
          // 未完成接收费用订单列表
          if (title.includes(subString)) {
            const regex = /\(([^)]+)\)/
            const matches = title.match(regex)
            this.closeData = matches ? matches[1] : null
            this.formatted()
            this.order.dispatchTime = res.data.createDate
            this.order.manager = res.data.acceptorName
            this.showTitle = false
            this.orderFlag = true
            this.orderUrl = this.orderUrl + "manager=" + this.order.manager + "&dispatchTime=" + this.order
              .dispatchTime
          }
        }
      })
      .finally(_ => {
        this.pageLoading = false
      })
    },
    formatted() {
      // 将中文字符替换为英文字符
      const formattedDateStr = this.closeData.replace(/年|月/g, '-').replace('日', '');

      // 创建 Date 对象
      const date = new Date(formattedDateStr);

      // 获取年、月、日
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // 月份从0开始，需要加1
      const day = date.getDate();

      // 组合成所需的格式
      this.formattedDate = `${year}-${month}-${day}`;
    }
  }
}
</script>

<style scoped lang="scss">
  ::v-deep .msg-url {
    text-decoration: underline;
    color: #02a7f0;
  }
  .MessageDetails{
    .message-title{

    }
  }
</style>
