<template>
  <div>
    <mssCard :title="title">
      <div slot="headerBtn">
        <el-button type="primary" @click="downloadData">导出</el-button>
        <el-button @click="$router.go(-1)">返回</el-button>
      </div>
      <div slot="content">
        <mssTable ref="table" selection :api="api" :columns="columns" :static-search-param="staticSearchParam" border>
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
  import qs from 'qs';
  import {
    getReceiverList,exportTotal,exportDetail
  } from "@/api/pccw/comprehensive/purchase_order/index.js";
  export default {
    props: {
      dispatchTime: {},
      manager: {},
      date:{}
    },
    // computed: {
    //   staticSearchParam() {
    //     return {
    //       dispatchTime: this.dispatchTime,
    //       manager: this.manager
    //     };
    //   }
    // },
    data() {
      return {
        title: '未完成接收费用订单列表 ( ' + this.date + ' )',
        // 静态列表的参数
        staticSearchParam: {
          dispatchTime: this.dispatchTime,
          manager: this.manager
        },
        searchConfig: [{
            label: '地市',
            type: 'input',
            fieldName: 'qualityCode'
          },
          {
            label: '项目名称',
            type: 'input',
            fieldName: 'projectName'
          },
          {
            label: '项目编号',
            type: 'input',
            fieldName: 'projectCode'
          },
          {
            label: '所在部门',
            type: 'select',
            options: [],
            fieldName: 'qualityCode'
          },
          {
            label: '费用类型',
            type: 'select',
            options: [],
            fieldName: 'status'
          },

        ],
        api: getReceiverList,
        columns: [{
            prop: "businessEntity",
            label: "公司/组织",
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "purchaseOrder",
            label: '订单编号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "orderDescription",
            label: '订单说明',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "supplierName",
            label: '供应商编号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "supplierName",
            label: '供应商名称',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "projectNumber",
            label: '项目编码',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "projectName",
            label: '项目名称',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "taskNumber",
            label: '任务编号',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "allotmentAmount",
            label: '订单分配总额',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "receivedAmount",
            label: '订单接收总额',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "percentageOfReception",
            label: '订单接收百分比',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "approveStatus",
            label: '订单审批状态',
            align: "center",
            tooltip: true,
            width: 200
          },
          {
            prop: "lastApprovalProcessDate",
            label: '结算审计完成时间',
            align: "center",
            tooltip: true,
            width: 200
          }
        ]
      }
    },
    created() {
      // this.staticSearchParam = {
      //   a: '1123',
      //   dispatchTime: this.dispatchTime,
      //   manager: this.manager
      // }
    },

    methods: {
      async downloadData() {

        console.log("t",this.staticSearchParam);
        let res =await exportDetail(qs.stringify(this.staticSearchParam))
        console.log("res",res);
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', '未完成接收费用订单列表.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
      async getReceiverList() {
        let res = await getReceiverList()
        console.log("res", res)
      }
    }
  }
</script>
