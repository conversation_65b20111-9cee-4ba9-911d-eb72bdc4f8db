<!--
* @author: hcm
* @date: 2023-07-03
* @description: 合作单位人员黑白名单-查看
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="申请信息">
      <div slot="content">
        <mssForm
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm"
          :label-width="'180px'"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :business-type="workflowCode" :node-name="nodeName" :bo-id="boId" :deal-page="false"></mssAttachment>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig" />
  </div>
</template>

<script>

import {
  queryDetailService } from '@/api/cooperation_company/personnel_blacklist_whitelist.js'
export default {
  name: 'PersonnelBlacklistWhitelistDetail',
  data() {
    return {
      basicConfig: [
        {
          label: '合作单位名称：',
          type: 'input',
          prop: 'cooperationCompanyName',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位编码：',
          type: 'input',
          prop: 'cooperationCompanyCode',
          span: 12,
          disabled: true
        },
        {
          label: '姓名：',
          type: 'input',
          prop: 'cooperationPersonName',
          span: 12,
          disabled: true
        },
        {
          label: '性别：',
          type: 'select',
          prop: 'cooperationPersonSex',
          disabled: true,
          span: 12,
          options: [
            { label: '男', value: '0' },
            { label: '女', value: '1' }
          ]
        },
        {
          label: '身份证号：',
          type: 'input',
          prop: 'coopcompanyPersonIdcard',
          span: 12,
          disabled: true
        },
        {
          label: '申请加入名单类型：',
          type: 'select',
          prop: 'applyType',
          span: 12,
          disabled: true,
          options: []
        },
        {
          label: '手机号码：',
          type: 'input',
          prop: 'coopcompanyPersonTel',
          span: 12
        },
        {
          label: '申请理由：',
          type: 'input',
          mode: 'textarea',
          prop: 'applyReason',
          span: 24,
          disabled: true
        }
      ],
      basicForm: {},
      labelPosition: 'left',
      id: '',
      boId: '',
      workflowCode: 'PartnerUserBlacklist',
      nodeName: '',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow'
        }]
    }
  },
  async created() {
    this.blackWhitelist = await this.$dictOptions({ parentValue: '*********', appCode: '001001' })
    this.$set(this.basicConfig[5], 'options', this.blackWhitelist)
    this.id = this.$route.query.boId
    this.getInfo()
  },
  methods: {
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.basicForm = res.data
          this.basicForm.cooperationCompanyName = res.data.cooperationCompanyItemDto.cooperationcompanyname
          this.basicForm.cooperationCompanyCode = res.data.cooperationCompanyItemDto.cooperationcompanycode
          this.basicForm.cooperationPersonName = res.data.cooperationCompanyItemDto.cooperationpersonname
          this.basicForm.cooperationPersonSex = res.data.cooperationCompanyItemDto.cooperationpersonsex
          this.basicForm.coopcompanyPersonIdcard = res.data.cooperationCompanyItemDto.coopcompanypersonidcard
          this.basicForm.cooperationPersonId = res.data.cooperationCompanyItemDto.cooperationpersonid
          this.basicForm.coopcompanyPersonTel = res.data.cooperationCompanyItemDto.coopcompanypersontel
          this.boId = this.id
        }
      })
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/personnel_blacklist_whitelist'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cooperation-company{
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
