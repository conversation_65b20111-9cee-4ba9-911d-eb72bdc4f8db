<!--
* @author: hcm
* @date: 2023-07-03
* @description: 合作单位人员黑白名单-新增、审批
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button v-if="boId" type="primary" @click="submit">提交审核</el-button>
      <el-button v-if="!disableForm" type="primary" @click="saveInfo">保存草稿</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="申请信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm"
          :disable-form="disableForm"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :bo-id="boId" :business-type="workflowCode" :node-name="nodeName"></mssAttachment>

    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig" />
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="!disableForm"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <!-- 人员弹框 -->
    <mssChooseUser ref="chooseUser" :mult-select="false" @showCheckList="showCheckList"></mssChooseUser>
  </div>
</template>

<script>
import {
  queryDetailService,
  saveInfoService,
  getPersonByUserIdService,
  updateInfoService,
  notExistsProcessService
} from '@/api/cooperation_company/personnel_blacklist_whitelist.js'
export default {
  name: 'PersonnelBlacklistWhitelistEdit',
  data() {
    return {
      disableForm: false,
      basicConfig: [
        {
          label: '合作单位名称',
          type: 'input',
          prop: 'cooperationCompanyName',
          disabled: true,
          span: 12
        },
        {
          label: '合作单位编码',
          type: 'input',
          prop: 'cooperationCompanyCode',
          span: 12,
          disabled: true
        },
        {
          label: '姓名',
          type: 'input',
          prop: 'cooperationPersonName',
          span: 12,
          rules: { required: true, message: '请选择人员姓名', trigger: 'blur,change' },
          eventListeners: {
            focus: () => { this.openUserSelectDialog() }// 打开选择部门弹窗
          }
        },
        {
          label: '身份证号',
          type: 'input',
          prop: 'coopcompanyPersonIdcard',
          span: 12
        },
        {
          label: '性别',
          type: 'radio',
          prop: 'cooperationPersonSex',
          span: 12,
          options: [
            { label: '男', value: '0' },
            { label: '女', value: '1' }
          ]
        },
        {
          label: '申请加入名单类型',
          type: 'radio',
          prop: 'applyType',
          span: 12,
          options: [],
          rules: { required: true, message: '请选择加入名单类型', trigger: 'blur' }
        },
        {
          label: '手机号码',
          type: 'input',
          prop: 'coopcompanyPersonTel',
          span: 12
        },
        {
          label: '申请理由',
          type: 'input',
          mode: 'textarea',
          prop: 'applyReason',
          span: 24
        }
      ],
      basicForm: {
        cooperationCompanyName: '',
        cooperationCompanyCode: '',
        cooperationPersonName: '',
        cooperationPersonSex: '',
        coopcompanyPersonIdcard: '',
        creatorId: sessionStorage.getItem('userId'),
        creatorName: sessionStorage.getItem('realName'),
        createDate: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
        applyDeptId: sessionStorage.getItem('deptId'),
        applyDeptName: sessionStorage.getItem('deptName')
      },
      labelPosition: 'top',
      blackWhitelist: [],
      boId: '',
      workflowCode: 'PartnerUserBlacklist',
      nodeName: '草稿',
      nodeCode: '',
      completeTaskUrl: '',
      returnAddress: '/cooperation_company/personnel_blacklist_whitelist'
    }
  },
  computed: {
    pageAnchorConfig() {
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile',
          show: this.boId
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow',
          show: this.boId
        }
      ]
    }
  },
  async created() {
    this.blackWhitelist = await this.$dictOptions({ parentValue: '*********', appCode: '001001' })
    this.$set(this.basicConfig[5], 'options', this.blackWhitelist)
    this.id = this.$route.query.boId
    this.boId = this.id
    if (this.id) {
      this.getInfo()
    }
  },
  methods: {
    // 限制黑白名单选择
    ableToList(val) {
      switch (val) {
        case '*********001':
          this.blackWhitelist = this.blackWhitelist.map(item => {
            if (item.value === '*********001') {
              return { ...item, disabled: true }
            } else {
              return item
            }
          })
          this.$set(this.basicConfig[5], 'options', this.blackWhitelist)
          break
        case '*********002':
          this.blackWhitelist = this.blackWhitelist.map(item => {
            if (item.value === '*********002') {
              return { ...item, disabled: true }
            } else {
              return item
            }
          })
          this.$set(this.basicConfig[5], 'options', this.blackWhitelist)
          break
        case '*********003':
          this.blackWhitelist = this.blackWhitelist.map(item => {
            return { ...item, disabled: true }
          })
          this.$set(this.basicConfig[5], 'options', this.blackWhitelist)
          break
      }
    },
    // 详情
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.basicForm = res.data
          this.basicForm.cooperationCompanyName = res.data.cooperationCompanyItemDto.cooperationcompanyname
          this.basicForm.cooperationCompanyCode = res.data.cooperationCompanyItemDto.cooperationcompanycode
          this.basicForm.cooperationPersonName = res.data.cooperationCompanyItemDto.cooperationpersonname
          this.basicForm.cooperationPersonSex = res.data.cooperationCompanyItemDto.cooperationpersonsex
          this.basicForm.coopcompanyPersonIdcard = res.data.cooperationCompanyItemDto.coopcompanypersonidcard
          this.basicForm.cooperationPersonId = res.data.cooperationCompanyItemDto.cooperationpersonid
          this.basicForm.coopcompanyPersonTel = res.data.cooperationCompanyItemDto.coopcompanypersontel
          this.$refs.workFlow.init()
          this.completeTaskUrl = res.data.completeTaskUrl
          this.ableToList(this.basicForm.currentType)
        }
      })
    },
    // 打开选人弹框
    openUserSelectDialog() {
      const deptParams = {	conditionType: 'p_cooperation', rootId: '-1', orgChildId: '-1' }
      this.$refs.chooseUser.init({
        excuterNames:this.basicForm.cooperationPersonName || '',
        excuterIds:this.basicForm.userId || ''
      }, deptParams)
    },
    showCheckList({ checkList }) {
      // 根据用户id查询合作单位信息
      if(checkList[0]&&checkList[0].userId)getPersonByUserIdService(checkList[0].userId).then(res => {
        if (res.code === '0000' && res.data.cooperationCompanyItemDto) {
          const currentType = res.data.currentType ? res.data.currentType : '*********001'
          this.ableToList(currentType)
          this.basicForm['currentType'] = res.data.currentType || ''
          this.basicForm.cooperationCompanyName = res.data.cooperationCompanyItemDto.cooperationcompanyname
          this.basicForm.cooperationCompanyCode = res.data.cooperationCompanyItemDto.cooperationcompanycode
          this.basicForm.cooperationPersonName = res.data.cooperationCompanyItemDto.cooperationpersonname
          this.basicForm.cooperationPersonSex = res.data.cooperationCompanyItemDto.cooperationpersonsex
          this.basicForm.coopcompanyPersonIdcard = res.data.cooperationCompanyItemDto.coopcompanypersonidcard
          this.basicForm.cooperationPersonId = res.data.cooperationCompanyItemDto.cooperationpersonid
          this.basicForm.coopcompanyPersonTel = res.data.cooperationCompanyItemDto.coopcompanypersontel
          this.basicForm.userId = checkList[0].userId
        } else {
          this.$message.warning('暂无该人员信息')
        }
      })
    },
    // 新增前校验
    async verifyWorkOrder() {
      let ableSave = true
      await notExistsProcessService(this.basicForm.userId).then(res => {
        if (res.code === '0000') {
          ableSave = res.data
        }
      })
      return ableSave
    },
    async saveInfo() {
      let ableSave = true
      let flag = false
      this.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          flag = true
        } else {
          return false
        }
      })
      if (flag) {
        const reqData = { ...this.$refs.basicForm.modelForm, id: this.id }
        // 新增校验
        if (!reqData.id) {
          ableSave = await this.verifyWorkOrder()
          if (!ableSave) {
            this.$message.warning(`${this.basicForm.cooperationPersonName}存在流程中工单，请先完成流转中的工单`)
            return
          }
        }
        (reqData.id ? updateInfoService : saveInfoService)(reqData).then(res => {
          if (res.code === '0000') {
            this.$message.success('保存成功')
            this.id = res.data.id
            this.boId = this.id
            this.completeTaskUrl = res.data.completeTaskUrl
            this.$nextTick(() => {
              this.$refs.workFlow.init()
            })
          }
        })
      }
    },
    // 提交前保存表单信息
    save(cb) {
      const reqData = { ...this.$refs.basicForm.modelForm, id: this.id }
      updateInfoService(reqData).then(res => {
        if (res.code === '0000') {
          if (cb) {
            cb()
          } else {
            this.$message.success('保存成功')
          }
        }
      })
    },
    // 提交审核
    submit() {
      // let flag = false
      // this.$refs.basicForm.$refs.form.validateScroll((valid) => {
      //   if (valid) {
      //     flag = true
      //   } else {
      //     return false
      //   }
      // })
      // if (flag) {
        this.$refs.workFlow.opendialogInitNextPath()
      // }
    },
    async beforeSubmit(){
      let str=""
      await this.$refs.basicForm.$refs.form.validateScroll((valid)=>{
        str = valid?'':'有必填信息未填写完整，请检查'
      })
      return str
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: `关于${this.$refs.basicForm.modelForm.cooperationCompanyName}单位下${this.$refs.basicForm.modelForm.cooperationPersonName}合作单位人员黑名单管理`
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode() {
      const fileNameArr = {
        hzdwzhy: { userName: 'admin', userId: '1' }
      }
      // 设置默认处理人
      return fileNameArr
    },
    getNodeData(data) {
      this.nodeName = data.nodeName || ''
      this.nodeCode = data.nodeCode || ''
      if (this.nodeCode === 'dshzdwgly' || !this.nodeCode) {
        this.disableForm = false
      } else {
        this.disableForm = true
      }
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/personnel_blacklist_whitelist'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cooperation-company{
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
