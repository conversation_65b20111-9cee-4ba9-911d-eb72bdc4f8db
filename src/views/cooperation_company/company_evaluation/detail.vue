<!--
* @author: hcm
* @date: 2023-07-07
* @description: 合作单位考核-详情
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssCard :title="'基本信息'" class="form-card">
      <div slot="content">
        <mssForm
          ref="evalForm"
          :config="formConfig"
          :label-position="'left'"
          :form="formModel"
          :is-change="true"
          :disable-form="true"
          :label-width="'220px'"
        />
      </div>
    </mssCard>
    <mssCard :title="'考核信息'">
      <div slot="content">
        <mssTable
          v-if="id"
          ref="evalTable"
          :api="tableApi"
          :columns="evalColumns"
          :pagination="false"
          :show-summary="true"
          :static-search-param="evalTableParam"
          :get-summaries="getSummaries"
          :wrapper-class="'eval-table'"
        />
      </div>
    </mssCard>
    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="boId"
      :deal-page="dealPage"
      :bo-id="boId"
      :business-type="workflowCode"
      :node-name="nodeName"
    ></mssAttachment>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
  </div>
</template>
<script>
import { templateDetailService, queryDetailService } from '@/api/cooperation_company/company_evaluation.js'
export default {
  name: 'UnitEvaluation',
  data() {
    return {
      tableApi: templateDetailService,
      formModel: {},
      formConfig: [
        {
          label: '地市：',
          type: 'input',
          prop: 'evalCityName',
          span: 12
        },
        {
          label: '区县：',
          type: 'input',
          prop: 'evalCountyName',
          span: 12
        },
        {
          label: '被考核合作单位名称：',
          type: 'input',
          prop: 'cooperationCompanyName',
          span: 12
        },
        {
          label: '被考核合作单位类型：',
          type: 'input',
          prop: 'partnerDeptTypeName',
          span: 12
        },
        {
          label: '单位负责人：',
          type: 'input',
          prop: 'orgManagerName',
          span: 12
        },
        {
          label: '单位负责人电话：',
          type: 'input',
          prop: 'orgManagerMobile',
          span: 12
        },
        {
          label: '框架合同名称：',
          type: 'input',
          prop: 'frameContName',
          span: 12
        },
        {
          label: '框架合同编号：',
          type: 'input',
          prop: 'frameContCode',
          span: 12
        },
        {
          label: '框架合采集分类：',
          type: 'input',
          prop: 'frameContPurType',
          span: 12
        },
        {
          label: '月份：',
          type: 'input',
          prop: 'evalMonth',
          span: 12
        },
        {
          label: '考核方式：',
          type: 'input',
          prop: 'evalModeName',
          span: 12
        },
        {
          label: '考核专业：',
          type: 'input',
          prop: 'evalSpecName',
          span: 12
        },
        {
          label: '考核人：',
          type: 'input',
          prop: 'creatorName',
          span: 12
        },
        {
          label: '考核编部门：',
          type: 'input',
          prop: 'applyDeptName',
          span: 12
        },
        {
          label: '考核时间：',
          type: 'input',
          prop: 'createDate',
          span: 12
        }
      ],
      evalStationary: [],
      evalColumns: [
        { label: '考核类别', prop: 'evalCol1Category' },
        { label: '考核小类', prop: 'evalCol2SubCategory' },
        { label: '考核内容', prop: 'evalCol3Content' },
        { label: '考核标准', prop: 'evalCol4Standard' },
        { label: '单项满分', prop: 'fullScore' },
        // { label: '单项权重（100%）', prop: 'weight' },
        { label: '得分', prop: 'evalScore' },
        { label: '扣分理由', prop: 'deductionReason' }
      ],
      workflowCode: 'PartnerEval',
      evalTableParam: {},
      dealPage: false,
      nodeName: ''
    }
  },
  created() {
    this.boId = this.$route.query.boId
    this.id = this.boId
    this.evalTableParam = { boId: this.boId }
    this.getInfo()
  },
  methods: {
    // 修改样式
    evalTableStyle() {
      const tds = document.querySelectorAll('.eval-table .el-table__footer-wrapper tr>td')
      // colSpan合并列  这里打印一下看一下
      tds[0].colSpan = 5 // 这里是要合并几行
      tds[0].style.textAlign = 'center'
      tds[1].style.display = 'none' // 上述合并3行也就对应的隐藏到第3个格子
      tds[2].style.display = 'none'
      tds[3].style.display = 'none'
      tds[4].style.display = 'none'
      // 这里注意一下  要合并几行就隐藏到第几个格子，我合并3个格子，第0个格子是 合计 字段不用隐藏，后面两个要隐藏因为是合并3个嘛
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      const noSumColumn = [1, 2, 3, 4, 8]
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        } else if (noSumColumn.includes(index)) {
          // 不需要合计的列
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = (values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)).toFixed(2)
        }
      })
      return sums
    },
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.formModel = res.data
          this.$nextTick(() => {
            this.evalTableStyle()
          })
        }
      })
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/company_evaluation'
      })
    }
  }
}
</script>
<style lang="scss" scoped>

.cooperation-company{
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
