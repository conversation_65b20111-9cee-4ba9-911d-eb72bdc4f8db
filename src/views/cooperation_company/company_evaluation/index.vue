<!--
* @author: hcm
* @date: 2023-07-07
* @description: 合作单位考核-列表
-->
<template>
  <div class="template-list">
    <mssSearchForm
      :search-config="searchConfig"
      @search="search"
      @reset="reset"
    />
    <mssCard :title="'合作单位考核列表'">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportMethod">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="commonTable"
          :columns="columns"
          :api="tableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
  </div>
</template>
<script>
import { queryListService,exportMonthlyExcelService } from '@/api/cooperation_company/company_evaluation.js'
import { queryAreaListService } from "@/api/common_api"
import { commonDown } from '@/utils/btn'
export default {
  name: 'UnitevaluationList',
  data() {
    return {
      searchConfig: [
        {
          label: '被考核合作单位名称',
          type: 'input',
          fieldName: 'cooperationCompanyName'
        },
        {
          label: '合作单位类型',
          type: 'select',
          fieldName: 'cooperationCompanyType',
          options: []
        },
        {
          label: '框架合同名称',
          type: 'input',
          fieldName: 'frameContName'
        },
        {
          label: '框架合同编号',
          type: 'input',
          fieldName: 'frameContCode'
        },
        {
					type: 'select',
					fieldName: 'evalCity',
					label: '地市',
					options: []
				},
        {
					type: 'date1',
					fieldName: 'scoreMonth',
					label: '评分年月',
          valueFormat: 'yyyy-MM',
          format: 'yyyy-MM',
          dateType: 'month'
				},
        {
          label: '考评方式',
          type: 'select',
          fieldName: 'evalMode',
          options: []
        },
        {
          label: '工单状态',
          type: 'select',
          fieldName: 'entityStatus',
          options: []
        }
      ],
      tableApi: queryListService,
      columns: [
        {
          label: '被考核合作单位名称',
          prop: 'cooperationCompanyName',
          tooltip: true,
          minWidth: '180px'
        },
        {
          label: '合作单位类型',
          prop: 'partnerDeptTypeName',
          tooltip: true,
          minWidth: '100px'
        },
        {
          label: '框架合同名称',
          prop: 'frameContName',
          tooltip: true,
          minWidth: '180px'
        },
        {
          label: '框架合同编号',
          prop: 'frameContCode',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '考评方式',
          prop: 'evalModeName',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '月度',
          tooltip: true,
          minWidth: '120px',
          formatter:(row)=>{
            return `${row.evalYear}年${row.evalMonth}月`
          }
        },
        {
          label: '地市',
          prop: 'evalCityName',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '评分部门',
          prop: 'applyDeptName',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '考评角色',
          prop: 'evalroleName',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '评分人',
          prop: 'creatorName',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '得分',
          prop: 'evalScore',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '工单状态',
          prop: 'status',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '当前处理人',
          prop: 'currDealUserName',
          tooltip: true,
          minWidth: '120px'
        },
        { label: '操作',
          tooltip: true,
          fixed:'right',
          minWidth: '100px',
          formatter: row => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class='table_btn mr10'
                    onClick={() => { this.acitonHandle('edit', row) }}
                  >
                    处理
                  </span>
                ) : ('')}
                <span class='table_btn' onClick={() => { this.acitonHandle('detail', row) }}>
                  查看
                </span>
              </span>
            )
          } }
      ],
      staticSearchParam: {}
    }
  },
  async created() {
    this.$set(this.searchConfig[1], 'options', await this.$dictOptions({ parentValue: '001001001', appCode: '001001' }))
    this.$set(this.searchConfig[7], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB' }))
    this.$set(this.searchConfig[6], 'options', await this.$dictOptions({ parentValue: '001001002001', appCode: '001001' }))
    // 查询地市下拉选项
    this.getAreaList()
  },
  methods: {
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.commonTable.page.current = 1
      this.$refs.commonTable.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
    acitonHandle(type, row) {
      const id = row ? row.id : ''
      if (type === 'detail') {
        this.$router.push({
          path: '/cooperation_company/company_evaluation/view',
          query: {
            boId: id
          }
        })
      } else {
        this.$router.push({
          path: '/cooperation_company/company_evaluation/edit',
          query: {
            boId: id
          }
        })
      }
    },
	  // 查询地市下拉选项数据
		getAreaList() {
			queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
				if (res.code === '0000') {
					const list = []
					res.data.forEach(item => {
						list.push({ label: item.name, value: item.id })
					})
					this.$set(this.searchConfig[4], 'options', list)
				}
			})
		},
    exportMethod() {
      commonDown(this.staticSearchParam, exportMonthlyExcelService)
    },
  }
}
</script>
