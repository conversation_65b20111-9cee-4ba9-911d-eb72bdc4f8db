<!--
* @author: hcm
* @date: 2023-07-07
* @description: 合作单位考核-打分/审批
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button v-if="boId" type="primary" @click="submit">提交审核</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssCard :title="'基本信息'" class="form-card">
      <div slot="content">
        <mssForm
          ref="evalForm"
          :config="formConfig"
          :label-position="'left'"
          :form="formModel"
          :is-change="true"
          :disable-form="disableForm"
          :label-width="'220px'"
        />
      </div>
    </mssCard>
    <mssCard :title="'考核信息'">
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            v-if="id"
            ref="evalTable"
            :columns="evalColumns"
            :api="tableApi"
            :pagination="false"
            :show-summary="true"
            :get-summaries="getSummaries"
            :static-search-param="evalTableParam"
            :wrapper-class="'eval-table'"
          />
        </el-form>
      </div>
    </mssCard>
    <mssAttachment
      v-if="boId"
      :deal-page="dealPage"
      :bo-id="boId"
      :business-type="workflowCode"
      :node-name="nodeName"
    ></mssAttachment>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory ref="flowHistory" :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssWorkFlowHandel
      v-if="boId"
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :dept-params-only-transfer="deptParamsOnlyTransfer"
      :person-params-only-transfer="personParamsOnlyTransfer"
      :save-main-form="saveMainForm"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
  </div>
</template>
<script>
import { templateDetailService, queryDetailService, saveService } from '@/api/cooperation_company/company_evaluation.js'
export default {
  name: 'Evalscore',
  data() {
    return {
      deptParamsOnlyTransfer: {},
      personParamsOnlyTransfer: {},
      bargainMoney: (rule, value, callback, full) => {
        if (!value) {
          callback(new Error('请填写得分'))
        } else if (value > full) {
          callback(new Error('得分不能超过满分'))
        } else {
          callback()
        }
      },
      tableApi: templateDetailService,
      formModel: {},
      formConfig: [
        {
          label: '地市：',
          type: 'input',
          prop: 'evalCityName',
          span: 12
        },
        {
          label: '区县：',
          type: 'input',
          prop: 'evalCountyName',
          span: 12
        },
        {
          label: '被考核合作单位名称：',
          type: 'input',
          prop: 'cooperationCompanyName',
          span: 12
        },
        {
          label: '被考核合作单位类型：',
          type: 'input',
          prop: 'partnerDeptTypeName',
          span: 12
        },
        {
          label: '单位负责人：',
          type: 'input',
          prop: 'orgManagerName',
          span: 12
        },
        {
          label: '单位负责人电话：',
          type: 'input',
          prop: 'orgManagerMobile',
          span: 12
        },
        {
          label: '框架合同名称：',
          type: 'input',
          prop: 'frameContName',
          span: 12
        },
        {
          label: '框架合同编号：',
          type: 'input',
          prop: 'frameContCode',
          span: 12
        },
        {
          label: '框架合采集分类：',
          type: 'input',
          prop: 'frameContPurType',
          span: 12
        },
        {
          label: '月份：',
          type: 'input',
          prop: 'evalMonth',
          span: 12
        },
        {
          label: '考核方式：',
          type: 'input',
          prop: 'evalModeName',
          span: 12
        },
        {
          label: '考核专业：',
          type: 'input',
          prop: 'evalSpecName',
          span: 12
        },
        {
          label: '考核人：',
          type: 'input',
          prop: 'creatorName',
          span: 12
        },
        {
          label: '考核编部门：',
          type: 'input',
          prop: 'applyDeptName',
          span: 12
        },
        {
          label: '考核时间：',
          type: 'input',
          prop: 'createDate',
          span: 12
        }
      ],
      evalColumns: [],
      evalTableParam: {},
      id: '',
      disableForm: true,
      boId: '',
      workflowCode: 'PartnerEval',
      nodeName: '草稿',
      completeTaskUrl: '',
      returnAddress: '/cooperation_company/company_evaluation',
      saveMainForm: false, // 只有评分人可以打分
      dealPage: true,
      nodeCode: '',
      form: {},
      rules: {}
    }
  },
  watch: {
    saveMainForm: {
      handler(n, o) {
        this.generateColums()
        this.$nextTick(() => {
          this.evalTableStyle()
        })
      },
      immediate: true
    }
  },
  created() {
    this.id = this.$route.query.boId
    this.boId = this.id
    this.evalTableParam = { boId: this.boId }
    this.getInfo()
  },
  mounted() {
    this.$refs.workFlow.init()
  },
  methods: {
    // 修改样式
    evalTableStyle() {
      const tds = document.querySelectorAll('.eval-table .el-table__footer-wrapper tr>td')
      // colSpan合并列  这里打印一下看一下
      tds[0].colSpan = 5 // 这里是要合并几行
      tds[0].style.textAlign = 'center'
      tds[1].style.display = 'none' // 上述合并3行也就对应的隐藏到第3个格子
      tds[2].style.display = 'none'
      tds[3].style.display = 'none'
      tds[4].style.display = 'none'
      // 这里注意一下  要合并几行就隐藏到第几个格子，我合并3个格子，第0个格子是 合计 字段不用隐藏，后面两个要隐藏因为是合并3个嘛
    },
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.formModel = res.data
        }
      })
    },
    generateColums() {
      this.evalColumns = [
        { label: '考核类别', prop: 'evalCol1Category' },
        { label: '考核小类', prop: 'evalCol2SubCategory' },
        { label: '考核内容', prop: 'evalCol3Content' },
        { label: '考核标准', prop: 'evalCol4Standard' },
        { label: '单项满分', prop: 'fullScore' },
        // { label: '单项权重（%）', prop: 'weight' },
        { label: '得分', prop: 'evalScore', formatter: (row, column, cellValue, index) => {
          if (!this.nodeName || this.nodeName.indexOf('评分人') === -1 || row.deletedFlag) {
            return row.evalScore
          } else if (this.nodeName.indexOf('评分人') !== -1 && this.formModel.evalMode === '001001002001001') {
            // 使用人员考核
            return (
              <el-form-item prop={`evalScore${index}`}>
                <el-select v-model={row.evalScore} onChange={(value) => {
                  this.form[`evalScore${index}`] = value
                }}>
                  <el-option value='A' label='A' ></el-option>
                  <el-option value='B' label='B' ></el-option>
                  <el-option value='C' label='C' ></el-option>
                  <el-option value='D' label='D' ></el-option>
                  <el-option value='E' label='E' ></el-option>
                </el-select>
              </el-form-item>
            )
          } else {
            return <el-form-item prop={`evalScore${index}`}>
              <el-input v-model={row.evalScore} onChange={(value) => {
                this.form[`evalScore${index}`] = value
              }}></el-input>
            </el-form-item>
          }
        }
        },
        { label: '扣分理由', prop: 'deductionReason', formatter: (row, column, cellValue, index) => {
          if (!this.nodeName || this.nodeName.indexOf('评分人') === -1 || row.deletedFlag) {
            return row.deductionReason
          } else {
            return <el-form-item prop={`deductionReason${index}`}>
              <el-input v-model={row.deductionReason} type='textarea' autosize={true} onBlur={(value) => {
                this.form[`deductionReason${index}`] = value
              }}></el-input>
            </el-form-item>
          }
        } }
      ]
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      const noSumColumn = [1, 2, 3, 4, 8]
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        } else if (noSumColumn.includes(index)) {
          // 不需要合计的列
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = (values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)).toFixed(2)
        }
      })
      return sums
    },
    // 添加必填校验
    commonsetRule(arr) {
      this.form = {}
      this.rules = {}
      const a = [
        'evalScore'
      ]
      a.forEach((item) => {
        for (const index in arr) {
          this.$set(this.form, `${item}${index}`, arr[index][item]) // 必须用set，否则form校验识别不到
          this.rules[`${item}${index}`] = {
            required: true,
            validator: (rule, value, callback) => { this.bargainMoney(rule, value, callback, arr[index].fullScore) },
            trigger: ['blur', 'change']
          }
          if (
            (arr[index].evalScore && this.saveMainForm && this.formModel.evalMode === '001001002001001' && arr[index].evalScore != 'A') ||
            (arr[index].evalScore && this.saveMainForm && arr[index].evalScore < arr[index].fullScore)) {
            // 使用人员考核
            this.$set(this.form, `${'deductionReason'}${index}`, arr[index]['deductionReason']) // 必须用set，否则form校验识别不到
            this.rules[`${'deductionReason'}${index}`] = {
              required: true,
              message: '请填写扣分理由',
              trigger: ['blur', 'change']
            }
          }
        }
      })
    },
    // 保存主单信息
    save(cb) {
      const reqData = {
        ...this.$refs.evalForm.modelForm,
        detailDtoList: this.$refs.evalTable.tableData
      }
      saveService(reqData).then(res => {
        if (res.code === '0000') {
          if (cb) {
            cb()
          } else {
            this.$message({ type: 'success', message: '保存成功！' })
          }
        }
      })
    },
    // 提交
    submit() {
      // 校验打分,校验扣分
      this.commonsetRule(this.$refs.evalTable.tableData)
      this.$nextTick(() => {
        // this.$refs.editform.validate((valid) => {
        //   if (valid) {
        //     this.$refs.editform.resetFields()
            this.completeTaskUrl = this.formModel.completeTaskUrl
            this.deptParamsOnlyTransfer = {
              rootId: this.$refs.evalForm.modelForm.evalCity || '',
              orgChildId: this.$refs.evalForm.modelForm.evalCity || ''
            }
            this.personParamsOnlyTransfer = {
              rootId: this.$refs.evalForm.modelForm.evalCity || '',
              orgChildId: this.$refs.evalForm.modelForm.evalCity || ''
            }

            this.$refs.workFlow.opendialogInitNextPath()
          // }
        // })
      })
    },
    async beforeSubmit(){
      let str=""
      await this.$refs.editform.validate((valid)=>{
        str = valid?'':'考核信息列表有必填信息未填写完整，请检查'
      })
      return str
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/company_evaluation'
      })
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: this.$refs.evalForm.modelForm.name
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode() {
      const fileNameArr = {
        swzglycl: { userName: 'admin', userId: '1' }
      }
      // 设置默认处理人
      return fileNameArr
    },
    getNodeData(data) {
      this.nodeName = data.nodeName || ''
      this.nodeCode = data.nodeCode || ''
      if (this.nodeCode === 'CitieNetwork' || this.nodeCode === 'GradingPeople') {
        this.saveMainForm = true
      } else {
        this.saveMainForm = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>

.cooperation-company{
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
