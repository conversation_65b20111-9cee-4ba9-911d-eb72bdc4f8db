<!--
* @author: hcm
* @date: 2023-07-06
* @description: 分包单位-列表
-->
<template>
  <div class="informationList">
    <mssSearchForm
      ref="searchForm"
      :search-config="formConfig"
      @search="search"
      @reset="reset"
    />
    <mssCard title="分包单位信息列表">
      <div slot="headerBtn">
        <el-button v-if="powerData['subcontractor_info_add']" @click="acitonHandle('add')">新增</el-button>
        <el-button type="primary" @click="exportList">导出</el-button>
        <el-button type="primary" @click="exportAttachment">导出附件</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="commonTable"
          selection
          :columns="tableHeader"
          :api="tableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
  </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
import {
  queryListService,
  removeIdsService,
  exportPageService,
  exportAttachmentService
} from '@/api/cooperation_company/subcontractor_info.js'
export default {
  name: 'SubcontractorInfoList',
  data() {
    return {
      powerData:[],
      tableApi: queryListService,
      formConfig: [
        {
          label: '合作单位名称',
          type: 'input',
          fieldName: 'cooperationCompanyName'
        },
        {
          label: '合作单位编码',
          type: 'input',
          fieldName: 'cooperationCompanyCode'
        },
        {
          label: '分包单位名称',
          type: 'input',
          fieldName: 'subcontractorName'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'entityStatus'
        }
      ],
      tableHeader: [
        {
          prop: 'cooperationCompanyName',
          label: '合作单位名称',
          tooltip: true,
          minWidth: '140px'
        },
        {
          prop: 'cooperationCompanyCode',
          label: '合作单位编码',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'cooperationCompanyType',
          label: '合作单位类型',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'applyDeptName',
          label: '分包单位名称',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'status',
          label: '工单状态',
          tooltip: true,
          minWidth: '100px'
        },
        {
          label: '操作',
          tooltip: true,
          minWidth: '120px',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (<span
                  class='table_btn mr10'
                  onClick={() => { this.acitonHandle('edit', row) }}
                >
                  处理
                </span>) : ('')}
                {this.powerData['subcontractor_info_view'] ? <span class='table_btn mr10' onClick={() => { this.acitonHandle('detail', row) }}>
                  查看
                </span> : ('')}
                {row.isAllowDelete ? (
                  <span
                    class='table_btn mr10'
                    onClick={() => { this.delHandle(row) }}
                  >
                  删除
                  </span>
                ) : ('')}
              </span>
            )
          }
        }
      ],
      tableData: [],
      staticSearchParam: {}
    }
  },
  async created() {
    this.getPower()
    this.$set(this.formConfig[2], 'options', await this.$dictOptions({ parentValue: '001001001', appCode: '001001' }))
    this.$set(this.formConfig[3], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB' }))
  },
  methods: {
    getPower(){
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item)=>{
        this.powerData[item.authority]=true
      })
    },
    exportList() {
      const params = {
        ...this.$refs.searchForm.searchForm,
        limit: this.$refs.commonTable.page.size,
        page: this.$refs.commonTable.page.current,
        exportType: 'all'
      }
      commonDown(params, exportPageService)
    },
    // 导出附件
    exportAttachment() {
      const selection = this.$refs.commonTable.multipleSelection
      const ids = []
      if (selection.length > 0) {
        selection.forEach(item => {
          ids.push(item.id)
        })
        commonDown({ ids: ids.join(',') }, exportAttachmentService)
      } else {
        this.$message.warning('请勾选数据')
      }
    },
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.commonTable.page.current = 1
      this.$refs.commonTable.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
    acitonHandle(type, row) {
      const id = row ? row.id : ''
      if (type === 'detail') {
        this.$router.push({
          path: '/cooperation_company/subcontractor_info/view',
          query: {
            boId: id
          }
        })
      } else {
        this.$router.push({
          path: '/cooperation_company/subcontractor_info/edit',
          query: {
            boId: id
          }
        })
      }
    },
    delHandle(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeIdsService({ ids: row.id }).then(res => {
          if (res.code === '0000') {
            this.$message({
              type: 'success',
              message: '删除成功！'
            })
            this.search(this.$refs.searchForm.searchForm)
          }
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
