<!--
* @author: hcm
* @date: 2023-07-06
* @description: 分包单位-新增、处理
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button v-if="boId" type="primary" @click="submit">提交审核</el-button>
      <el-button v-if="!disableBasicForm" type="primary" @click="saveInfo">保存草稿</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm ref="basicForm" :disable-form="disableBasicForm" :config="basicConfig" :label-position="labelPosition" :form="basicForm" />
      </div>
    </mssCard>
    <div id="sectionFb" class="page-anchor-point"></div>
    <mssCard v-if="subcontractorParam.subcontractorId" title="分包信息">
      <div slot="headerBtn">
        <el-upload
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button v-if="!disableBasicForm" type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportSubcontractor">导出</el-button>
        <el-button v-if="!disableBasicForm" @click="addTable">新增</el-button>
        <el-button v-if="!disableBasicForm" type="primary" @click="delTable">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="subcontractorTable"
          selection
          :columns="subcontractorColumns"
          :api="subcontractorApi"
          :static-search-param="subcontractorParam"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :bo-id="boId" :business-type="workflowCode" :node-name="nodeName"></mssAttachment>

    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig" />
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="!disableBasicForm"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <!-- 分包单位 -->
    <el-dialog
      title="提示"
      :visible.sync="subcontractorVisible"
      width="70%"
    >
      <mssForm
        ref="subcontractorForm"
        :config="subcontractorFormConfig"
        :label-position="subcontractorLabelPosition"
        :disable-form="subcontractorDisableForm"
        :form="subcontractorForm"
      ></mssForm>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="!subcontractorDisableForm" type="primary" @click="saveSubcontractor">确 定</el-button>
        <el-button @click="cancalSubcontractor">取 消</el-button>
      </span>
    </el-dialog>
    <mssChooseSpecialty ref="chooseSpecialty" :mult-select="true" @showCheckList="showSpecialty"></mssChooseSpecialty>
    <mssChooseDept ref="chooseDept" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseDept>
  </div>
</template>
<script>
import { checkPhone, checkIdCard } from '@/utils/validate.js'
import { commonDown } from '@/utils/btn.js'
import {
  subcontractorByDeptIdService,
  saveService,
  detailService,
  updateService,
  subcontractorListService,
  subcontractorSaveService,
  subcontractorUpdateService,
  subcontractorDetailService,
  subcontractorBatchremoveService,
  subcontractorExportExcelService,
  subcontractorImportExcelService,
  notExistsProcessService
} from '@/api/cooperation_company/subcontractor_info.js'
export default {
  name: 'SubcontractorInfoEdit',
  data() {
    return {
      // 劳务分包单位资质有效期起
      subCertPeriodStPickerOptions: {
        disabledDate: (time) => {
          if (this.$refs.subcontractorForm.modelForm['subCertPeriodEnd']) {
            const end = new Date(this.$refs.subcontractorForm.modelForm['subCertPeriodEnd'])
            return time.getTime() > end.getTime()
          }
        }
      },
      // 劳务分包单位资质有效期止
      subCertPeriodEndPickerOptions: {
        disabledDate: (time) => {
          if (this.$refs.subcontractorForm.modelForm['subCertPeriodSt']) {
            const start = new Date(this.$refs.subcontractorForm.modelForm['subCertPeriodSt'])
            return start.getTime() > time.getTime()
          }
        }
      },
      // 劳务分包对应框架合同有效期从
      subFrameContPeriodStPickerOptions: {
        disabledDate: (time) => {
          if (this.$refs.subcontractorForm.modelForm['subFrameContPeriodEnd']) {
            const end = new Date(this.$refs.subcontractorForm.modelForm['subFrameContPeriodEnd'])
            return time.getTime() > end.getTime()
          }
        }
      },
      // 劳务分包对应框架合同有效期至
      subFrameContPeriodEndPickerOptions: {
        disabledDate: (time) => {
          if (this.$refs.subcontractorForm.modelForm['subFrameContPeriodSt']) {
            const start = new Date(this.$refs.subcontractorForm.modelForm['subFrameContPeriodSt'])
            return start.getTime() > time.getTime()
          }
        }
      },
      subcontractorApi: subcontractorListService,
      basicConfig: [
        {
          label: '合作单位名称',
          type: 'input',
          prop: 'cooperationCompanyName', // 展示
          span: 12,
          disabled: true
        },
        {
          label: '合作单位编码', // 展示
          type: 'input',
          prop: 'cooperationCompanyCode',
          span: 12
        },
        {
          label: '合作单位类型', // 展示
          type: 'input',
          prop: 'cooperationCompanyType',
          span: 12,
          disabled: true
        },
        {
          label: '统一信用代码',
          type: 'input',
          prop: 'creditCode',
          span: 12,
          rules: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        {
          label: '专业',
          type: 'input',
          prop: 'specialtyName',
          span: 12,
          readonly: true,
          eventListeners: {
            focus: () => { this.openSpecSelectDialog() }// 打开选择部门弹窗
          }
        },
        {
          label: '合作部门',
          type: 'input',
          prop: 'partnerDeptName',
          span: 12,
          eventListeners: {
            focus: () => { this.openDeptSelectDialog('dept') }// 打开选择部门弹窗
          },
          rules: [{ required: true, message: '请选择合作部门', trigger: ['blur', 'change'] }]
        },
        {
          label: '合作单位负责人',
          type: 'input',
          prop: 'partnerCharge',
          span: 12,
          rules: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        {
          label: '合作单位负责人手机号码',
          type: 'input',
          prop: 'partnerChargeMobile',
          span: 12,
          rules: [{ required: true, validator: (rule, value, callback) => { checkPhone(rule, value, callback, true) }, message: '请输入正确的手机号码', trigger: 'blur' }]
        },
        {
          label: '合作单位负责人身份证号码',
          type: 'input',
          prop: 'partnerChargeIdcard',
          span: 12,
          rules: [{ required: true, validator: (rule, value, callback) => { checkIdCard(rule, value, callback, true) }, message: '请输入正确的身份证号', trigger: 'blur' }]
        },
        {
          label: '合作单位项目经理',
          type: 'input',
          prop: 'partnerPm',
          span: 12,
          rules: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        {
          label: '合作单位项目经理职务',
          type: 'input',
          prop: 'partnerPmDuty',
          span: 12,
          rules: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        {
          label: '合作单位项目经理主要负责工作',
          type: 'input',
          prop: 'partnerPmWork',
          span: 12,
          rules: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        {
          label: '合作单位项目经理手机号码',
          type: 'input',
          prop: 'partnerPmMobile',
          span: 12,
          rules: [{ required: true, validator: (rule, value, callback) => { checkPhone(rule, value, callback, true) }, message: '请输入正确的手机号码', trigger: 'blur' }]
        },
        {
          label: '合作单位项目经理身份证号',
          type: 'input',
          prop: 'partnerPmIdcard',
          span: 12,
          rules: [{ required: true, validator: (rule, value, callback) => { checkIdCard(rule, value, callback, true) }, message: '请输入正确的身份证号', trigger: 'blur' }]
        },
        {
          label: '编制人',
          type: 'input',
          prop: 'creatorName', // 当前系统
          span: 12,
          disabled: true
        },
        {
          label: '编制部门',
          type: 'input',
          prop: 'applyDeptName', // 当前系统
          span: 12,
          disabled: true
        },
        {
          label: '编制时间',
          type: 'input',
          prop: 'createDate', // 当前系统
          span: 12,
          disabled: true
        },
        {
          label: '服务范围',
          type: 'input',
          mode:'textarea',
          prop: 'businessScopeName',
          span: 24,
          disabled: true
        }
      ],
      basicForm: {
        creatorId: sessionStorage.getItem('userId'),
        creatorName: sessionStorage.getItem('realName'),
        createDate: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
        applyDeptId: sessionStorage.getItem('deptId'),
        applyDeptName: sessionStorage.getItem('deptName'),
        partnerDept: '',
        partnerDeptName: '',
        cooperationCompanyName: '',
        cooperationCompanyCode: '',
        cooperationCompanyType: '',
        cooperationCompanyId: '',
        specialty: '',
        specialtyName: '',
        deptId: sessionStorage.getItem('deptId')
      },
      subcontractorData: [],
      labelPosition: 'top',
      // 分包单位表单
      subcontractorVisible: false,
      subcontractorForm: {},
      specailty: [], // 选择专业
      deptList: [], // 选择的部门
      chooseType: '',
      multSelect: false,
      subcontractorDisableForm: false,
      subcontractorLabelPosition: 'top',
      id: '',
      subcontractorParam: {},
      // 附件相关
      boId: '',
      workflowCode: 'Subcontractor',
      nodeName: '草稿',
      nodeCode: '',
      disableBasicForm: false,
      completeTaskUrl: '',
      returnAddress: '/cooperation_company/subcontractor_info'
    }
  },
  computed: {
    pageAnchorConfig() {
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '分包<br>信息',
          id: 'sectionFb',
          show: this.boId
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile',
          show: this.boId
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow',
          show: this.boId
        }
      ]
    },
    subcontractorColumns() {
      return [
        {
          prop: 'lev1Dept',
          label: '一级劳务分包单位',
          minWidth: '140'
        }, {
          prop: 'lev1Charge',
          label: '一级劳务分包单位负责人',
          minWidth: '150'
        }, {
          prop: 'subCertAndLevel',
          label: '劳务分包单位资质名称及等级',
          minWidth: '160'
        }, {
          prop: 'subCertScope',
          label: '劳务分包单位资质范围',
          minWidth: '150'
        }, {
          prop: 'subCertPeriodSt',
          label: '劳务分包单位资质有效期起',
          minWidth: '160'
        }, {
          prop: 'subCertPeriodEnd',
          label: '劳务分包单位资质有效期止',
          minWidth: '150'
        }, {
          prop: 'subFrameContCode',
          label: '劳务分包对应框架合同编码',
          minWidth: '160'
        }, {
          prop: 'subFrameContName',
          label: '劳务分包对应框架合同名称',
          minWidth: '160'
        }, {
          prop: 'subFrameContPeriodSt',
          label: '劳务分包对应框架合同有效期从',
          minWidth: '170'
        }, {
          prop: 'subFrameContPeriodEnd',
          label: '劳务分包对应框架合同有效期至',
          minWidth: '170'
        }, {
          prop: 'lev1ChargeMobile',
          label: '一级劳务分包单位负责人电话号码',
          minWidth: '170'
        }, {
          prop: 'lev1ChargeIdcard',
          label: '一级劳务分包单位负责人身份证号码',
          minWidth: '170'
        }, {
          prop: 'lev1ContactU',
          label: '一级劳务分包单位对接人',
          minWidth: '170'
        }, {
          prop: 'lev1ContactUMobile',
          label: '一级劳务分包单位对接人电话号码',
          minWidth: '170'
        }, {
          prop: 'lev1ContactUIdcard',
          label: '一级劳务分包单位对接人身份证号码',
          minWidth: '170'
        }, {
          prop: 'subCounty',
          label: '劳务分包单位服务区县',
          minWidth: '150'
        }, {
          prop: 'subSpecialtyProp',
          label: '劳务分包单位专业份额占比',
          minWidth: '150'
        }, {
          prop: 'teamCounty',
          label: '施工队服务区县',
          minWidth: '140'
        }, {
          prop: 'teamCharge',
          label: '施工队负责人(包工头)',
          minWidth: '150'
        }, {
          prop: 'teamChargeMobile',
          label: '施工队负责人电话',
          minWidth: '140'
        }, {
          prop: 'teamChargeIdcard',
          label: '施工队负责人身份证号',
          minWidth: '160'
        }, {
          prop: 'teamChargeSpecialtyProp',
          label: '施工队本专业份额占比',
          minWidth: '160'
        }, {
          prop: 'teamLeader',
          label: '施工小队长姓名',
          minWidth: '140'
        }, {
          prop: 'teamLeaderMobile',
          label: '施工小队长电话号码',
          minWidth: '150'
        }, {
          prop: 'teamLeaderIdcard',
          label: '施工小队长身份证号',
          minWidth: '150'
        }, {
          prop: 'teamLeaderProp',
          label: '施工小队份额占比（占整个施工队的比例）',
          minWidth: '180'
        }, {
          prop: 'laborContractFlag',
          label: '是否提供施工小队长、施工人员与中标单位或劳务分包单位签订的劳动合同',
          minWidth: '200'
        }, {
          prop: 'certSalaryFlag',
          label: '是否提供按合同约定定期足额发放工资的证明',
          minWidth: '200'
        }, {
          prop: 'socialCertFlag',
          label: '是否提供缴纳社保证明',
          minWidth: '150'
        }, {
          prop: 'coverCertFlag',
          label: '是否提供购买足额保险证明',
          minWidth: '150'
        },
        {
          label: '操作',
          fixed: 'right',
          formatter: (row) => {
            return (
              <span>
                { this.disableBasicForm ? ('') : (<span
                  class='table_btn mr10'
                  onClick={() => { this.acitonHandle('edit', row) }}
                >
                  编辑
                </span>)}
                <span class='table_btn' onClick={() => { this.acitonHandle('detail', row) }}>
                  查看
                </span>
              </span>
            )
          }
        }
      ]
    },
    subcontractorFormConfig() {
      return [
        {
          prop: 'lev1Dept',
          label: '一级劳务分包单位',
          type: 'input',
          span: 12
        },
        {
          prop: 'lev1Charge',
          label: '一级劳务分包单位负责人',
          type: 'input',
          span: 12
        },
        {
          prop: 'subCertAndLevel',
          label: '劳务分包单位资质名称及等级',
          type: 'input',
          span: 12
        },
        {
          prop: 'subCertScope',
          label: '劳务分包单位资质范围',
          type: 'input',
          span: 12
        },
        {
          prop: 'subCertPeriodSt',
          label: '劳务分包单位资质有效期起',
          type: 'datePicker',
          span: 12,
          pickerOptions: this.subCertPeriodStPickerOptions
        },
        {
          prop: 'subCertPeriodEnd',
          label: '劳务分包单位资质有效期止',
          type: 'datePicker',
          span: 12,
          pickerOptions: this.subCertPeriodEndPickerOptions
        },
        {
          prop: 'subFrameContCode',
          label: '劳务分包对应框架合同编码',
          type: 'input',
          span: 12
        },
        {
          prop: 'subFrameContName',
          label: '劳务分包对应框架合同名称',
          type: 'input',
          span: 12
        },
        {
          prop: 'subFrameContPeriodSt',
          label: '劳务分包对应框架合同有效期从',
          type: 'datePicker',
          span: 12,
          pickerOptions: this.subFrameContPeriodStPickerOptions
        },
        {
          prop: 'subFrameContPeriodEnd',
          label: '劳务分包对应框架合同有效期至',
          type: 'datePicker',
          span: 12,
          pickerOptions: this.subFrameContPeriodEndPickerOptions
        },
        {
          prop: 'lev1ChargeMobile',
          label: '一级劳务分包单位负责人电话号码',
          type: 'input',
          rules: [{ required: false, validator: (rule, value, callback) => { checkPhone(rule, value, callback, false) }, message: '请输入正确的电话号码', trigger: 'blur' }],
          span: 12
        },
        {
          prop: 'lev1ChargeIdcard',
          label: '一级劳务分包单位负责人身份证号码',
          type: 'input',
          span: 12,
          rules: [{ required: false, validator: (rule, value, callback) => { checkIdCard(rule, value, callback, false) }, message: '请输入正确的身份证号', trigger: 'blur' }]
        },
        {
          prop: 'lev1ContactU',
          label: '一级劳务分包单位对接人',
          type: 'input',
          span: 12
        },
        {
          prop: 'lev1ContactUMobile',
          label: '一级劳务分包单位对接人电话号码',
          type: 'input',
          span: 12,
          rules: [{ required: false, validator: (rule, value, callback) => { checkPhone(rule, value, callback, false) }, message: '请输入正确的电话号码', trigger: 'blur' }]
        },
        {
          prop: 'lev1ContactUIdcard',
          label: '一级劳务分包单位对接人身份证号码',
          type: 'input',
          span: 12,
          rules: [{ required: false, validator: (rule, value, callback) => { checkIdCard(rule, value, callback, false) }, message: '请输入正确的身份证号', trigger: 'blur' }]
        }, {
          prop: 'subCounty',
          label: '劳务分包单位服务区县',
          type: 'input',
          span: 12
        },
        {
          prop: 'subSpecialtyProp',
          label: '劳务分包单位专业份额占比',
          type: 'input',
          span: 12
        },
        {
          prop: 'teamCounty',
          label: '施工队服务区县',
          type: 'input',
          span: 12
        }, {
          prop: 'teamCharge',
          label: '施工队负责人(包工头)',
          type: 'input',
          span: 12
        }, {
          prop: 'teamChargeMobile',
          label: '施工队负责人电话',
          type: 'input',
          span: 12,
          rules: [{ required: false, validator: (rule, value, callback) => { checkPhone(rule, value, callback, false) }, message: '请输入正确的电话号码', trigger: 'blur' }]
        }, {
          prop: 'teamChargeIdcard',
          label: '施工队负责人身份证号',
          type: 'input',
          span: 12,
          rules: [{ required: false, validator: (rule, value, callback) => { checkIdCard(rule, value, callback, false) }, message: '请输入正确的身份证号', trigger: 'blur' }]
        }, {
          prop: 'teamChargeSpecialtyProp',
          label: '施工队本专业份额占比',
          type: 'input',
          span: 12
        }, {
          prop: 'teamLeader',
          label: '施工小队长姓名',
          type: 'input',
          span: 12
        }, {
          prop: 'teamLeaderMobile',
          label: '施工小队长电话号码',
          type: 'input',
          span: 12,
          rules: [{ required: false, validator: (rule, value, callback) => { checkPhone(rule, value, callback, false) }, message: '请输入正确的电话号码', trigger: 'blur' }]
        }, {
          prop: 'teamLeaderIdcard',
          label: '施工小队长身份证号',
          type: 'input',
          span: 12,
          rules: [{ required: false, validator: (rule, value, callback) => { checkIdCard(rule, value, callback, false) }, message: '请输入正确的身份证号', trigger: 'blur' }]
        }, {
          prop: 'teamLeaderProp',
          label: '施工小队份额占比（占整个施工队的比例）',
          type: 'input',
          span: 12
        }, {
          prop: 'laborContractFlag',
          label: '是否提供施工小队长、施工人员与中标单位或劳务分包单位签订的劳动合同',
          type: 'input',
          span: 12
        }, {
          prop: 'certSalaryFlag',
          label: '是否提供按合同约定定期足额发放工资的证明',
          type: 'input',
          span: 12
        }, {
          prop: 'socialCertFlag',
          label: '是否提供缴纳社保证明',
          type: 'input',
          span: 12
        }, {
          prop: 'coverCertFlag',
          label: '是否提供购买足额保险证明',
          type: 'input',
          span: 12
        }
      ]
    }
  },
  created() {
    this.id = this.$route.query.boId
    this.boId = this.id
    if (this.id) {
      this.getInfo()
    } else {
      this.getCompanyInfo()
    }
  },
  methods: {
    // 详情
    getInfo() {
      detailService(this.id).then(res => {
        if (res.code === '0000') {
          this.basicForm = res.data
          this.basicForm['cooperationCompanyName'] = res.data.cooperationCompanyDto.cooperationcompanyname || ''
          this.basicForm['cooperationCompanyCode'] = res.data.cooperationCompanyDto.cooperationcompanycode || ''
          this.basicForm['cooperationCompanyType'] = res.data.cooperationCompanyDto.cooperationcompanytypeName || ''
          this.basicForm['cooperationCompanyId'] = res.data.cooperationCompanyDto.cooperationcompanyid || ''
          this.completeTaskUrl = res.data.completeTaskUrl
          this.$refs.workFlow.init()
          this.subcontractorParam = {
            cooperationCompanyId: this.basicForm['cooperationCompanyId'],
            subcontractorId: res.data.id
          }
        }
      })
    },
    getCompanyInfo() {
      // 根据合作单位id查询合作单位信息
      subcontractorByDeptIdService(this.basicForm.deptId).then(res => {
        if (res.code === '0000' && res.data.cooperationCompanyDto) {
          this.basicForm = { ...this.basicForm, ...res.data }
          this.basicForm['cooperationCompanyName'] = res.data.cooperationCompanyDto.cooperationcompanyname || ''
          this.basicForm['cooperationCompanyCode'] = res.data.cooperationCompanyDto.cooperationcompanycode || ''
          this.basicForm['cooperationCompanyType'] = res.data.cooperationCompanyDto.cooperationcompanytypeName || ''
          this.basicForm['cooperationCompanyId'] = res.data.cooperationCompanyDto.cooperationcompanyid || ''
          this.subcontractorParam = {
            cooperationCompanyId: this.basicForm['cooperationCompanyId']
          }
        } else {
          this.$message.warning('没有获取到相关单位信息')
        }
      })
    },
    openSpecSelectDialog() {
      // 回显
      this.specailty = []
      const name = this.basicForm.specialtyName ? this.basicForm.specialtyName.split(',') : []
      const id = this.basicForm.specialty ? this.basicForm.specialty.split(',') : []
      name.forEach((item, index) => {
        this.specailty.push({
          id: id[index],
          name: item
        })
      })
      this.$refs.chooseSpecialty.init(this.specailty)
    },
    showSpecialty({ checkList }) {
      const list = checkList
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.name)
        ids.push(item.id)
      })
      this.basicForm['specialty'] = ids.join(',')
      this.basicForm['specialtyName'] = names.join(',')
    },
    // 打开部门选择弹框
    openDeptSelectDialog(type) {
      this.chooseType = type
      const deptParams = { conditionType: 'p_cooperation' }
      this.multSelect = true
      this.deptList = []
      // 回显
      const partnerDept = this.basicForm.partnerDept ? this.basicForm.partnerDept.split(',') : []
      const partnerDeptName = this.basicForm.partnerDeptName ? this.basicForm.partnerDeptName.split(',') : []
      partnerDept.forEach((item, index) => {
        this.deptList.push({
          id: item,
          text: partnerDeptName[index]
        })
      })

      this.$refs.chooseDept.init(this.deptList, deptParams)
    },
    // 部门弹框关闭
    showCheckList({ checkList }) {
      this.deptList = checkList
      const names = []; const ids = []
      this.deptList.forEach(item => {
        names.push(item.text)
        ids.push(item.id)
      })
      this.basicForm['partnerDept'] = ids.join(',')
      this.basicForm['partnerDeptName'] = names.join(',')
      this.$refs.chooseDept.ishowTree = false
    },
    // 打开弹窗（分包信息）
    addTable() {
      this.subcontractorVisible = true
      for (const key in this.subcontractorForm) {
        this.subcontractorForm[key] = ''
      }
    },
    // 弹框取消
    cancalSubcontractor() {
      this.subcontractorVisible = false
    },
    // 分包信息--修改和查看
    acitonHandle(type, row) {
      if (type === 'detail') {
        this.subcontractorDisableForm = true
      } else {
        this.subcontractorDisableForm = false
      }
      subcontractorDetailService(row.id).then(res => {
        if (res.code === '0000' && res.data) {
          this.subcontractorForm = res.data
          this.subcontractorVisible = true
        }
      })
    },
    // 弹框分包信息保存/修改
    saveSubcontractor() {
      let flag = false
      this.$refs.subcontractorForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          flag = true
        } else {
          return false
        }
      })
      if (flag) {
        const reqData = { ...this.$refs.subcontractorForm.modelForm, subcontractorId: this.id, cooperationCompanyId: this.basicForm['cooperationCompanyId'] };
        (reqData.id ? subcontractorUpdateService : subcontractorSaveService)(reqData).then(res => {
          if (res.code === '0000') {
            this.$message({ type: 'success', message: '保存成功！' })
            this.subcontractorVisible = false
            this.searchSubcontractorList()
          }
        })
      }
    },
    // 删除
    delTable() {
      const selection = this.$refs.subcontractorTable.multipleSelection
      if (selection.length) {
        const ids = []
        selection.forEach(item => {
          ids.push(item.id)
        })
        subcontractorBatchremoveService({ ids: ids.join(',') }).then(res => {
          if (res.code === '0000') {
            this.$message({
              message: '删除成功！',
              type: 'success'
            })
            this.searchSubcontractorList()
          }
        })
      } else {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
    },
    // 查询分包列表
    searchSubcontractorList() {
      this.$refs.subcontractorTable.page.current = 1
      this.$refs.subcontractorTable.getTableData(this.subcontractorParam)
    },
    // 导出-分包信息
    exportSubcontractor() {
      commonDown({ boId: this.id }, subcontractorExportExcelService)
    },
    // 导入-分包信息
    importFile(params) {
      const param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.id)
      subcontractorImportExcelService(param).then((res) => {
        if (res.data) {
          this.$message.warning(res.data)
        } else if (res.code === '0000') {
          this.$message.success('导入成功')
          this.searchSubcontractorList()
        }
      })
    },
    // 新增前校验
    async verifyWorkOrder() {
      let ableSave = true
      await notExistsProcessService(this.basicForm['deptId']).then(res => {
        if (res.code === '0000') {
          ableSave = res.data
        }
      })
      return ableSave
    },
    // 保存草稿
    async saveInfo() {
      let ableSave = true
      let flag = false
      this.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          flag = true
        } else {
          return false
        }
      })
      if (flag) {
        const reqData = { ...this.$refs.basicForm.modelForm, id: this.id }
        // 新增校验
        if (!reqData.id) {
          ableSave = await this.verifyWorkOrder()
          if (!ableSave) {
            this.$message.warning(`${this.basicForm.cooperationCompanyName}存在流程中工单，请先完成流转中的工单`)
            return
          }
        }
        (reqData.id ? updateService : saveService)(reqData).then(res => {
          if (res.code === '0000') {
            this.$message.success('保存成功')
            this.id = res.data.id
            this.boId = this.id
            this.completeTaskUrl = res.data.completeTaskUrl
            this.subcontractorParam = {
              cooperationCompanyId: this.basicForm['cooperationCompanyId'],
              subcontractorId: this.id
            }
            if (this.$refs.subcontractorTable) {
              this.$refs.subcontractorTable.getTableData(this.subcontractorParam)
            }
            this.$nextTick(() => {
              this.$refs.workFlow.init()
            })
          }
        })
      }
    },
    // 提交前保存表单信息
    save(cb) {
      const reqData = { ...this.$refs.basicForm.modelForm, id: this.id }
      updateService(reqData).then(res => {
        if (res.code === '0000') {
          if (cb) {
            cb()
          } else {
            this.$message.success('保存成功')
          }
        }
      })
    },
    // 提交审核
    submit() {
      // let flag = false
      // this.$refs.basicForm.$refs.form.validateScroll((valid) => {
      //   if (valid) {
      //     flag = true
      //   } else {
      //     return false
      //   }
      // })
      // if (flag) {
        this.$refs.workFlow.opendialogInitNextPath()
      // }
    },
    async beforeSubmit(){
      let str=""
      await this.$refs.basicForm.$refs.form.validateScroll((valid)=>{
        str = valid?'':'有必填信息未填写完整，请检查'
      })
      return str
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: `关于${this.$refs.basicForm.modelForm.cooperationCompanyName}分包单位信息维护`
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode() {
      // const fileNameArr = {
      //   hzdwzhy: { userName: 'admin', userId: '1' }
      // }
      // // 设置默认处理人
      // return fileNameArr
    },
    getNodeData(data) {
      this.nodeCode = data.nodeCode
      this.nodeName = data.nodeName
      if (this.nodeCode === 'draft' || !this.nodeCode) {
        this.disableBasicForm = false
      } else {
        this.disableBasicForm = true
      }
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/subcontractor_info'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cooperation-company{
  padding-right: 70px;
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
  .upload-btn {
    display: inline-block;
    margin: 0 10px;
  }
}
</style>
