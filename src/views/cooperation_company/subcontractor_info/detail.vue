<!--
* @author: hcm
* @date: 2023-07-06
* @description: 分包单位-查看
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm :config="basicConfig" :label-position="labelPosition" :form="basicForm" :label-width="'220px'" />
      </div>
    </mssCard>
    <div id="sectionFb" class="page-anchor-point"></div>
    <mssCard title="分包信息">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportSubcontractor">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          v-if="boId"
          ref="farmerAccTable"
          :columns="subcontractorColumns"
          :api="tableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment
      v-if="boId"
      :business-type="workflowCode"
      :node-name="nodeName"
      :bo-id="boId"
      :deal-page="false"
    ></mssAttachment>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig" />
  </div>
</template>
<script>
import { commonDown } from '@/utils/btn.js'
import { detailService, subcontractorListService, subcontractorExportExcelService } from '@/api/cooperation_company/subcontractor_info.js'
export default {
  name: 'SubcontractorInfoDetail',
  data() {
    return {
      tableApi: subcontractorListService,
      basicConfig: [
        {
          label: '合作单位名称：',
          type: 'input',
          prop: 'cooperationCompanyName',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位编码：',
          type: 'input',
          prop: 'cooperationCompanyCode',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位类型：',
          type: 'input',
          prop: 'cooperationCompanyType',
          span: 12,
          disabled: true
        },
        {
          label: '统一信用代码：',
          type: 'input',
          prop: 'creditCode',
          span: 12,
          disabled: true
        },
        {
          label: '专业：',
          type: 'input',
          prop: 'specialtyName',
          span: 12
        },
        {
          label: '合作部门：',
          type: 'input',
          prop: 'partnerDeptName',
          span: 12
        },
        {
          label: '合作单位负责人：',
          type: 'input',
          prop: 'partnerCharge',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位负责人手机号码：',
          type: 'input',
          prop: 'partnerChargeMobile',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位负责人身份证号码：',
          type: 'input',
          prop: 'partnerChargeIdcard',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位项目经理：',
          type: 'input',
          prop: 'partnerPm',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位项目经理职务：',
          type: 'input',
          prop: 'partnerPmDuty',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位项目经理主要负责工作：',
          type: 'input',
          prop: 'partnerPmWork',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位项目经理手机号码：',
          type: 'input',
          prop: 'partnerPmMobile',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位项目经理身份证号：',
          type: 'input',
          prop: 'partnerPmIdcard',
          span: 12,
          disabled: true
        },
        {
          label: '编制人：',
          type: 'input',
          prop: 'creatorName',
          span: 12,
          disabled: true
        },
        {
          label: '编制部门：',
          type: 'input',
          prop: 'applyDeptName',
          span: 12,
          disabled: true
        },
        {
          label: '编制时间：',
          type: 'input',
          prop: 'createDate',
          span: 12,
          disabled: true
        },
        {
          label: '服务范围：', // todo没有这个字段
          type: 'input',
          prop: 'businessScopeName',
          span: 24,
          disabled: true
        }
      ],
      basicForm: {},
      subcontractorColumns: [
        {
          prop: 'lev1Dept',
          label: '一级劳务分包单位',
          minWidth: '140'
        }, {
          prop: 'lev1Charge',
          label: '一级劳务分包单位负责人',
          minWidth: '150'
        }, {
          prop: 'subCertAndLevel',
          label: '劳务分包单位资质名称及等级',
          minWidth: '160'
        }, {
          prop: 'subCertScope',
          label: '劳务分包单位资质范围',
          minWidth: '150'
        }, {
          prop: 'subCertPeriodSt',
          label: '劳务分包单位资质有效期起',
          minWidth: '160'
        }, {
          prop: 'subCertPeriodEnd',
          label: '劳务分包单位资质有效期止',
          minWidth: '150'
        }, {
          prop: 'subFrameContCode',
          label: '劳务分包对应框架合同编码',
          minWidth: '160'
        }, {
          prop: 'subFrameContName',
          label: '劳务分包对应框架合同名称',
          minWidth: '160'
        }, {
          prop: 'subFrameContPeriodSt',
          label: '劳务分包对应框架合同有效期从',
          minWidth: '170'
        }, {
          prop: 'subFrameContPeriodEnd',
          label: '劳务分包对应框架合同有效期至',
          minWidth: '170'
        }, {
          prop: 'lev1ChargeMobile',
          label: '一级劳务分包单位负责人电话号码',
          minWidth: '170'
        }, {
          prop: 'lev1ChargeIdcard',
          label: '一级劳务分包单位负责人身份证号码',
          minWidth: '170'
        }, {
          prop: 'lev1ContactU',
          label: '一级劳务分包单位对接人',
          minWidth: '170'
        }, {
          prop: 'lev1ContactUMobile',
          label: '一级劳务分包单位对接人电话号码',
          minWidth: '170'
        }, {
          prop: 'lev1ContactUIdcard',
          label: '一级劳务分包单位对接人身份证号码',
          minWidth: '170'
        }, {
          prop: 'subCounty',
          label: '劳务分包单位服务区县',
          minWidth: '150'
        }, {
          prop: 'subSpecialtyProp',
          label: '劳务分包单位专业份额占比',
          minWidth: '150'
        }, {
          prop: 'teamCounty',
          label: '施工队服务区县',
          minWidth: '140'
        }, {
          prop: 'teamCharge',
          label: '施工队负责人(包工头)',
          minWidth: '150'
        }, {
          prop: 'teamChargeMobild',
          label: '施工队负责人电话',
          minWidth: '140'
        }, {
          prop: 'teamChargeIdcard',
          label: '施工队负责人身份证号',
          minWidth: '160'
        }, {
          prop: 'teamChargeSpecialtyProp',
          label: '施工队本专业份额占比',
          minWidth: '160'
        }, {
          prop: 'teamLeader',
          label: '施工小队长姓名',
          minWidth: '140'
        }, {
          prop: 'teamLeaderMobile',
          label: '施工小队长电话号码',
          minWidth: '150'
        }, {
          prop: 'teamLeaderIdcard',
          label: '施工小队长身份证号',
          minWidth: '150'
        }, {
          prop: 'teamLeaderProp',
          label: '施工小队份额占比（占整个施工队的比例）',
          minWidth: '180'
        }, {
          prop: 'laborContractFlag',
          label: '是否提供施工小队长、施工人员与中标单位或劳务分包单位签订的劳动合同',
          minWidth: '200'
        }, {
          prop: 'certSalaryFlag',
          label: '是否提供按合同约定定期足额发放工资的证明',
          minWidth: '200'
        }, {
          prop: 'socialCertFlag',
          label: '是否提供缴纳社保证明',
          minWidth: '150'
        }, {
          prop: 'coverCertFlag',
          label: '是否提供购买足额保险证明',
          minWidth: '150'
        }
      ],
      subcontractorData: [],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '分包<br>信息',
          id: 'sectionFb'
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow'
        }],
      id: '',
      boId: '',
      workflowCode: 'Subcontractor',
      nodeName: ''
    }
  },
  created() {
    this.labelPosition = 'left'
    this.id = this.$route.query.boId
    this.getInfo()
  },
  methods: {
    exportSubcontractor() {
      commonDown({ boId: this.id }, subcontractorExportExcelService)
    },
    getInfo() {
      detailService(this.id).then(res => {
        if (res.code === '0000') {
          this.basicForm = res.data
          this.basicForm['cooperationCompanyName'] = res.data.cooperationCompanyDto.cooperationcompanyname || ''
          this.basicForm['cooperationCompanyCode'] = res.data.cooperationCompanyDto.cooperationcompanycode || ''
          this.basicForm['cooperationCompanyType'] = res.data.cooperationCompanyDto.cooperationcompanytypeName || ''
          this.basicForm['cooperationCompanyId'] = res.data.cooperationCompanyDto.cooperationcompanyid || ''
          this.staticSearchParam = {
            cooperationCompanyId: this.basicForm['cooperationCompanyId'],
            subcontractorId: res.data.id
          }
          this.boId = this.id
        }
      })
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/subcontractor_info'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cooperativeUnitEdit{
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
