/**
* @author: ty
* @date: 2023-12-06
* @description: 查看详情
*/
<template>
  <div class="MonthlyRelationTableDetail page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard :title="'基本信息'" class="form-card">
      <div slot="content">
        <mssForm
          ref="editForm"
          :config="editConfig"
          :label-position="labelPosition"
          :form="editForm"
          :disable-form="true"
        />
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig" />
 </div>
</template>

<script>
import {
  queryByIdService,
} from '@/api/cooperation_company/partner_eval_framecont.js'
import { queryAreaListService } from '@/api/common_api'

export default {
  name: 'MonthlyRelationTableDetail',
  data() {
    return {
      id: '',
      editConfig: [
        {
          label: '考评专业：',
          type: 'select',
          prop: 'evalSpec',
          span: 12,
          options: [],
        },
        {
          label: '地市：',
          type: 'select',
          prop: 'deptId',
          span: 12,
          options: [],
        },
        {
          label: '考核单位：',
          type: 'input',
          prop: 'businessName',
          span: 12,
        },
        {
          label: '采购层级：',
          type: 'select',
          prop: 'frameContPurType',
          span: 12,
          options: [],
        },
        {
          label: '框架合同名称：',
          type: 'input',
          mode: 'textarea',
          prop: 'frameContName',
          span: 24,
        },
        {
          label: '框架合同编码：',
          type: 'input',
          prop: 'frameContCode',
          span: 12,
        },
      ],
      editForm: {
        evalSpec: '',
        evalSpecName: '',
        deptId: '',
        deptName: '',
        businessCode: '',
        businessName: '',
        frameContName: '',
        frameContCode: '',
        frameContPurType: '',
        frameContPurTypeName: ''
      },
      companyList: [],
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        }
      ],
    }
  },
  async created() {
    this.id = this.$route.query.id || ''
    // 考评专业
    this.$set(this.editConfig[0], 'options', await this.$dictOptions({ parentValue: '*********', appCode: '001001' }))
    // 采购层级
    this.$set(this.editConfig[3], 'options', await this.$dictOptions({
      parentValue: '001001005001',
      appCode: '001001'
    }))
    // 获取地市
    this.getAreaList()
    // 有id的话，是修改，向后端请求详情
    if (this.id) {
      this.getInfo()
    }
  },
  methods: {
    getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.editConfig[1], 'options', list)
        }
      })
    },
    // 详情
    getInfo() {
      queryByIdService(this.id).then(res => {
        if (res.code === '0000') {
          this.editForm = res.data
        }
      })
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/relation_table'
      })
    },
    // 考评专业找中文 evalSpecName
    evalSpecChangeHandle(val){
      const evalSpecName = this.editConfig[0].options.find(item => item.value === val).label
      this.$set(this.$refs.editForm.modelForm, 'evalSpecName', evalSpecName)
    },
    // 地市找中文 deptName
    deptIdChangeHandle(val){
      const deptName = this.editConfig[1].options.find(item => item.value === val).label
      this.$set(this.$refs.editForm.modelForm, 'deptName', deptName)
    },
    // 采购层级找中文 frameContPurTypeName
    frameContPurTypeChangeHandle(val){
      const frameContPurTypeName = this.editConfig[3].options.find(item => item.value === val).label
      this.$set(this.$refs.editForm.modelForm, 'frameContPurTypeName', frameContPurTypeName)
    }
  }
}
</script>
