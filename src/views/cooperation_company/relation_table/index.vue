/**
* @author: ty
* @date: 2023-12-05
* @description:
*/
<template>
  <div class="monthlyscore">
    <mssSearchForm :searchConfig="searchConfig"
                   ref="searchForm"
                   @search="search"
                   @reset="reset"
                   @changeSelect="changeSelect">
    </mssSearchForm>
    <mssCard :title="tableName">
      <div slot="headerBtn">
        <el-button @click="actionHandle('add')">新增</el-button>
<!--        <el-button @click="delHandle('')">删除</el-button>-->
        <el-upload
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportMethod">导出</el-button>
      </div>
      <div slot="content">
        <mssTable ref="table"
                  border
                  :columns="columns"
                  :staticSearchParam="staticSearchParam"
                  :api="tableApi"/>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  queryListService,
  queryByIdService,
  deleteByIdService,
  addService,
  updateService,
  batchRemoveService,
  exportDataService,
  importExcelService
} from '@/api/cooperation_company/partner_eval_framecont.js'
import {commonDown} from "@/utils/btn";
import {queryAreaListService} from "@/api/common_api"

export default {
  name: 'MonthlyRelationTable',
  data() {
    return {
      searchConfig: [
        {
          type: 'select',
          fieldName: 'evalSpec',
          label: '考评专业',
          options: []
        },
        {
          type: 'select',
          fieldName: 'deptId',
          label: '地市',
          options: []
        },
        {
          type: 'input',
          fieldName: 'businessName',
          label: '考核单位名称',
          options: []
        },
        {
          type: 'input',
          fieldName: 'frameContName',
          label: '框架合同名称'
        },
        {
          type: 'select',
          fieldName: 'frameContPurType',
          label: '采购层级',
          options: []
        }
      ],
      tableName: this.$route.meta.title,
      staticSearchParam: {},
      tableApi: queryListService,
      columns:
        [
          {
            prop: "evalSpecName",
            label: "考评专业",
            minWidth: 140,
          },
          {
            prop: "deptName",
            label: "地市",
            width: 140,
          },
          {
            prop: "businessName",
            label: "考核单位名称",
            tooltip: true,
            minWidth: 160
          },
          {
            prop: 'frameContName',
            label: '框架合同名称',
            minWidth: 180,
            tooltip: true
          },
          {
            prop: 'frameContCode',
            label: '框架合同编号',
            minWidth: 160
          },
          {
            prop: 'frameContPurTypeName',
            label: "采购层级",
            width: 100,
          },
          {
            label: '操作',
            width: 100,
            fixed: 'right',
            formatter: row => {
              // <el-button type='text' onClick={() => this.actionHandle('detail', row)}>查看</el-button>

              return (
                <span>
                  <el-button type='text' onClick={() => this.actionHandle('edit', row)}>编辑</el-button>
                  <el-button type='text' onClick={() => this.delHandle(row)}>删除</el-button>
                </span>
              )
            }
          }
        ],
      specailty: [],
      dept: []
    }
  },
  async created() {
    // 考评专业
    this.$set(this.searchConfig[0], 'options', await this.$dictOptions({parentValue: "001001012", appCode: "001001"}))
    // 采购层级
    this.$set(this.searchConfig[4], 'options', await this.$dictOptions({
      parentValue: "001001005001",
      appCode: "001001"
    }))
    // 获取地市
    this.getAreaList()
  },
  methods: {
    getAreaList() {
      queryAreaListService({parentId: '-2', typeCode: 'area'}).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({label: item.name, value: item.id})
          })
          this.$set(this.searchConfig[1], 'options', list)
        }
      })
    },
    search(searchData) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {...this.staticSearchParam, ...searchData}
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      searchData.evalDept && (searchData.evalDept = '')
      searchData.evalSpecialty && (searchData.evalSpecialty = '')

      searchData.evalSpec && (searchData.evalSpec = '')
      searchData.evalSpecName && (searchData.evalSpecName = '')

      searchData.frameContPurType && (searchData.frameContPurType = '')
      searchData.frameContPurTypeName && (searchData.frameContPurTypeName = '')

      searchData.deptId && (searchData.deptId = '')
      searchData.deptName && (searchData.deptName = '')

      this.$refs.table.page.current = 1
      this.staticSearchParam = {...this.staticSearchParam, ...searchData}
      this.$refs.table.getTableData(searchData)
    },
    changeSelect(name, val) {
      // 考评专业找中文 evalSpecName
      if (name == 'evalSpec') {
        const selectedData = this.searchConfig[0].options.filter(item => item.value === val)
        this.$set(this.$refs.searchForm.searchForm, 'evalSpecName', selectedData[0].label)
      }
      // 采购层级找中文 frameContPurTypeName
      if (name == 'frameContPurType') {
        const selectedData = this.searchConfig[4].options.filter(item => item.value === val)
        this.$set(this.$refs.searchForm.searchForm, 'frameContPurTypeName', selectedData[0].label)
      }
      // 地市找中文 deptName
      if (name == 'deptId') {
        const selectedData = this.searchConfig[1].options.filter(item => item.value === val)
        this.$set(this.$refs.searchForm.searchForm, 'deptName', selectedData[0].label)
      }
    },
    // 导入
    importFile(params) {
      const param = new FormData()
      param.append('file', params.file)
      importExcelService(param).then((res) => {
        if (res.data.includes('preValidInfo')) {
          this.$message.warning(res.data)
        } else if (res.code === '0000') {
          this.$message.success('导入成功')
          this.search(this.$refs.searchForm.searchForm)
        }
      })
    },
    // 点击【导出】。导出的是当前查询条件下的所有数据
    exportMethod() {
      commonDown(this.staticSearchParam, exportDataService)
    },
    // 批量删除或者单行删除
    delHandle(row) {
      let delFlag = false
      let ids = ''
      // 批量删除
      if (row === '') {
        const selectData = this.$refs.table.multipleSelection
        if (selectData && selectData.length) {
          delFlag = true
          ids = selectData.map(item => item.id).join(',')
        } else {
          this.$message.warning('请选择框架合同！')
          delFlag = false
          return;
        }
      } else {// 单行删除
        ids = row.id
        delFlag = true
      }

      if (delFlag) {
        this.$confirm('是否确认删除该数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          batchRemoveService(ids)
            .then(res => {
              if (res.code === '0000') {
                this.search(this.$refs.searchForm.searchForm)
                this.$message({type: 'success', message: '删除成功！'})
              }
            })
        }).catch(() => {
        })
      }
    },

    actionHandle(type, row) {
      // 新增
      if(type === 'add'){
        this.$router.push({
          path: '/cooperation_company/relation_table/edit'
        })
      } else if(type === 'edit'){
        this.$router.push({
          path: '/cooperation_company/relation_table/edit',
          query: {
            id: row.id
          }
        })
      }
      else if (type === 'detail') {
        this.$router.push({
          path: '/cooperation_company/relation_table/view',
          query: {
            id: row.id
          }
        })
      }
    }
  }
}
</script>
<style lange="scss">

.el-input-number {
  width: 100%
}

.upload-btn {
  display: inline-block;
  margin: 0 10px;
}
</style>
