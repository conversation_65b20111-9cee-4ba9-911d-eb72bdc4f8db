/**
* @author: ty
* @date: 2023-12-06
* @description: 新增或者是编辑修改详情
*/
<template>
  <div class="form-edit">
    <div class="operate-btn">
      <el-button type="primary" @click="saveInfo">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssCard title="编辑信息">
      <div slot="content">
        <mssForm
          ref="editForm"
          :config="editConfig"
          :label-position="labelPosition"
          :form="editForm"
        />
      </div>
    </mssCard>

    <!-- 部门弹框 -->
    <mssChooseDept ref="chooseDept" @showCheckList="showCheckList"></mssChooseDept>
  </div>
</template>

<script>
import {
  queryByIdService,
  addService,
  updateService,
} from '@/api/cooperation_company/partner_eval_framecont.js'
import { queryAreaListService } from '@/api/common_api'
import {queryCompanyByDeptIdService} from '@/api/cooperation_company/company_balcklist_whitelist'

export default {
  name: 'MonthlyRelationTableEdit',
  data() {
    return {
      id: '',// 有id就是编辑，没有就是新增
      editConfig: [
        {
          label: '考评专业',
          type: 'select',
          prop: 'evalSpec',
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择考评专业', trigger: ['blur', 'change'] }],
          eventListeners: {
            change: (val) => { this.evalSpecChangeHandle(val) }// 考评专业改变
          }
        },
        {
          label: '地市',
          type: 'select',
          prop: 'deptId',
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择地市', trigger: ['blur', 'change'] }],
          eventListeners: {
            change: (val) => { this.deptIdChangeHandle(val) }// 地市改变
          }
        },
        {
          label: '考核单位',
          type: 'input',
          prop: 'businessName',
          span: 12,
          rules: [{ required: true, message: '请选择单位', trigger: ['blur', 'change'] }],
          eventListeners: {
            focus: () => { this.openDeptSelectDialog() }// 打开选择部门弹窗
          }
        },
        {
          label: '采购层级',
          type: 'select',
          prop: 'frameContPurType',
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择层级', trigger: ['blur', 'change'] }],
          eventListeners: {
            change: (val) => { this.frameContPurTypeChangeHandle(val) }
          }
        },
        {
          label: '框架合同名称',
          type: 'input',
          mode: 'textarea',
          prop: 'frameContName',
          span: 24,
          rules: [{ required: true, message: '请输入合同名称', trigger: ['blur', 'change'] }]
        },
        {
          label: '框架合同编码',
          type: 'input',
          prop: 'frameContCode',
          span: 12,
          rules: [{ required: true, message: '请输入合同编码', trigger: ['blur', 'change'] }]
        },
      ],
      editForm: {
        evalSpec: '',
        evalSpecName: '',
        deptId: '',
        deptName: '',
        businessCode: '',
        businessName: '',
        frameContName: '',
        frameContCode: '',
        frameContPurType: '',
        frameContPurTypeName: ''
      },
      companyList: [],
      labelPosition: 'top'
    }
  },
  async created() {
    this.id = this.$route.query.id || ''
    // 考评专业
    this.$set(this.editConfig[0], 'options', await this.$dictOptions({ parentValue: '*********', appCode: '001001' }))
    // 采购层级
    this.$set(this.editConfig[3], 'options', await this.$dictOptions({
      parentValue: '001001005001',
      appCode: '001001'
    }))
    // 获取地市
    this.getAreaList()
    // 有id的话，是修改，向后端请求详情
    if (this.id) {
      this.getInfo()
    }
  },
  methods: {
    getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.editConfig[1], 'options', list)
        }
      })
    },
    // 详情
    getInfo() {
      queryByIdService(this.id).then(res => {
        if (res.code === '0000') {
          this.editForm = res.data
        }
      })
    },
    saveInfo() {
      // 校验表单
      this.$refs.editForm.$refs.form.validateScroll(valid => {
        if (valid) {
          // 提交到后端接口
          const reqData = { ...this.$refs.editForm.modelForm}
          if(this.id){// 有id的话，是修改
            reqData.id = this.id
          }
          (reqData.id ? updateService : addService)(reqData).then(res => {
            if (res.code === '0000') {
              this.$message.success('保存成功')
              this.goBack()
            }
          })
        }
      })
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/relation_table'
      })
    },
    openDeptSelectDialog() {
      const deptParams = { conditionType: 'p_cooperation' }
      this.$refs.chooseDept.init(this.companyList, deptParams)
    },
    // 考评专业找中文 evalSpecName
    evalSpecChangeHandle(val){
      const evalSpecName = this.editConfig[0].options.find(item => item.value === val).label
      this.$set(this.$refs.editForm.modelForm, 'evalSpecName', evalSpecName)
    },
    // 地市找中文 deptName
    deptIdChangeHandle(val){
      const deptName = this.editConfig[1].options.find(item => item.value === val).label
      this.$set(this.$refs.editForm.modelForm, 'deptName', deptName)
    },
    // 采购层级找中文 frameContPurTypeName
    frameContPurTypeChangeHandle(val){
      const frameContPurTypeName = this.editConfig[3].options.find(item => item.value === val).label
      this.$set(this.$refs.editForm.modelForm, 'frameContPurTypeName', frameContPurTypeName)
    },
    showCheckList({ checkList }) {
      this.companyList = checkList
      // 根据合作单位id查询单位信息
      queryCompanyByDeptIdService(checkList[0].id).then(res => {
        if (res.code === '0000' && res.data.cooperationCompanyDto) {
          this.$set(this.$refs.editForm.modelForm, 'currentType', res.data.currentType || '')
          this.$set(this.$refs.editForm.modelForm, 'businessName', res.data.cooperationCompanyDto.cooperationcompanyname || '')
          this.$set(this.$refs.editForm.modelForm, 'businessCode', res.data.cooperationCompanyDto.cooperationcompanycode || '')
          this.$set(this.$refs.editForm.modelForm, 'businessType', res.data.cooperationCompanyDto.cooperationcompanytypeName || '')
        } else {
          this.$message.warning('暂无该公司信息！')
        }
      })
    }
  }
}
</script>
