<!--
* @author: hcm
* @date: 2023-07-28
* @description: 合作单位后评估-考核月度得分-审计部门
-->
<template>
  <mssCard title="考核月度得分-审计部门">
    <div slot="headerBtn">
      <el-upload
        ref="newFile"
        class="upload-btn"
        action="string"
        :show-file-list="false"
        :auto-upload="true"
        :http-request="importFile"
      >
        <el-button type="primary">导入</el-button>
      </el-upload>
      <el-button @click="exportList">导出</el-button>
    </div>
    <div slot="content">
      <mssTable
        ref="commonTable"
        :columns="tableHeader"
        :api="tableApi"
      />
    </div>
  </mssCard>
</template>
<script>
import { queryListService, importExcelService, exportExcelService } from '@/api/cooperation_company/audit_company_evaluation.js'
import { commonDown } from '@/utils/btn'
export default {
  name: 'AuditCompanyEvaluation',
  data() {
    return {
      tableApi: queryListService,
      tableHeader: [
        {
          prop: 'evalYear',
          label: '年度',
          tooltip: true,
          minWidth: '100px'
        }, {
          prop: 'evalMonth',
          label: '月度',
          tooltip: true,
          minWidth: '100px'
        }, {
          prop: 'evalCity',
          label: '地市',
          tooltip: true,
          minWidth: '100px'
        }, {
          prop: 'cooperationCompanyName',
          label: '合作单位名称',
          tooltip: true,
          minWidth: '140px'
        }, {
          prop: 'cooperationCompanyCode',
          label: '合作单位编码',
          tooltip: true,
          minWidth: '120px'
        }, {
          prop: 'cooperationCompanyType',
          label: '合作单位类型',
          tooltip: true,
          minWidth: '120px'
        }, {
          prop: 'frameContName',
          label: '框架合同名称',
          tooltip: true,
          minWidth: '120px'
        }, {
          prop: 'frameContCode',
          label: '框架合同编号',
          tooltip: true,
          minWidth: '120px'
        }, {
          prop: 'evalDeptName',
          label: '评分部门',
          tooltip: true,
          minWidth: '120px'
        }, {
          prop: 'evalScore',
          label: '评分数',
          tooltip: true,
          minWidth: '120px'
        }
      ]
    }
  },
  methods: {
    importFile(params) {
      const param = new FormData()
      param.append('file', params.file)
      importExcelService(param).then((res) => {
        if (res.data.includes('preValidInfo')) {
          this.$message.warning(res.data)
        } else if (res.code === '0000') {
          this.$message.success('导入成功')
          this.$refs.commonTable.getTableData()
        }
      })
    },
    exportList() {
      commonDown({}, exportExcelService)
    }
  }
}
</script>
<style lang="scss">
.upload-btn {
    display: inline-block;
    margin: 0 10px;
  }
</style>
