<!-- 合作单位考核年度得分 -->
<template>
	<div class="monthlyscore">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="SearchForm"
			@search="search"
			@reset="reset"
      @changeSelect="changeSelectHandle"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
        <el-button type="primary" @click="openUploadFiles">附件管理</el-button>
        <el-upload
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入年度奖惩得分</el-button>
        </el-upload>
        <el-button type="primary" @click="exportMethod2">导出年度奖惩得分</el-button>
        <el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					ref="table"
					border
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				/>
			</div>
		</mssCard>
	</div>
</template>

<script>
import { commonDown } from "@/utils/btn";
import { queryYearScoreService,exportYearScoreExcelService, exportBonusPenaltyService, importBonusPenaltyService, getYearScoreAttInfoService} from '@/api/cooperation_company/period_score'
export default {
	name: 'yearScore',
	data() {
		return {
			searchConfig: [
				{
					type: 'input',
					fieldName: 'cooperationCompanyName',
					label: '合作单位名称'
				},
				{
					type: 'select',
					fieldName: 'cooperationCompanyTypeEn',
					label: '合作单位类型',
					options: []
				},
				{
					type: 'date1',
					fieldName: 'evalYear',
					label: '年度',
					dateType: 'year',
					format: 'yyyy',
					valueFormat: 'yyyy'
				}
			],
			tableName: this.$route.meta.title,
			tableApi: queryYearScoreService,
			staticSearchParam: {},
      isCanOperation: false
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "constructionDept",
						label: "合作单位",
						minWidth: 140,
					},
          {
            prop: "evalYear",
            label: "考核年度"
          },
          {
            prop: "frameContName",
            label: "框架合同名称",
            minWidth: 180,
          },
          {
            prop: "frameContCode",
            label: "框架合同编码",
          },
					{
						label: "初评得分",
						multilevelColumn: [
							{
								prop: "acceptPhase",
								minWidth: 110,
								label: "项目月度考评得分的算术平均分"
							}
						]
					},
					{
						prop: "bonusScore",
						label: "奖励加分"
					},
					{
						prop: "deductionScore",
						label: "处罚扣分"
					},
					{
						prop: "evalScore",
						label: "考评得分"
					},
					{
						prop: "contractAmount",
						label: "合同金额（元）"
					}
				]
			}
		}
	},
	async created(){
		this.$set(this.searchConfig[1], 'options',await this.$dictOptions({ parentValue: "*********", appCode: "001001" }))
    this.getYearScoreAttInfoService()
	},
	methods: {
		search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData(form)
    },
    reset(searchData) {
      searchData.cooperationCompanyType && (searchData.cooperationCompanyType = '')
      searchData.cooperationCompanyTypeEn && (searchData.cooperationCompanyTypeEn = '')
      this.search(searchData)
    },
		exportMethod(){
			commonDown(this.staticSearchParam, exportYearScoreExcelService);
		},
    exportMethod2(){
			commonDown(this.staticSearchParam, exportBonusPenaltyService);
		},
    // 导入
    importFile(params) {
      const param = new FormData()
      param.append('file', params.file)
      importBonusPenaltyService(param).then((res) => {
        if (res.data.includes('preValidInfo')) {
          this.$message.warning(res.data)
        } else if (res.code === '0000') {
          this.$message.success('导入成功')
          this.search(this.$refs.SearchForm.searchForm)
        }
      })
    },
    changeSelectHandle(name, val){
      // 合作单位类型改成中文
      if (name == 'cooperationCompanyTypeEn') {
        const selectedData = this.searchConfig[1].options.filter(item => item.value === val)
        this.$set(this.$refs.SearchForm.searchForm, 'cooperationCompanyType', selectedData[0].label)
      }
    },
    getYearScoreAttInfoService(){
      getYearScoreAttInfoService()
      .then(res => {
        if(res.code === '0000'){
          this.isCanOperation = res.data.isCanOperation || false
        }
      })
    },
    openUploadFiles(){
		  this.$router.push({
        path: '/cooperation_company/year_score/upload'
      })
    }
	}
}
</script>
<style lange="scss">
.upload-btn {
  display: inline-block;
  margin: 0 10px;
}
</style>
