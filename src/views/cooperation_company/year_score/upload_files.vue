/**
* @author: ty
* @date: 2023-12-15
* @description:
*/
<template>
  <div>
    <mssAttachment
      v-if="boId"
      :deal-page="isCanOperation"
      :bo-id="boId"
      :business-type="businessType"
    ></mssAttachment>
  </div>
</template>

<script>
import {getYearScoreAttInfoService} from "@/api/cooperation_company/period_score";

export default {
  name: "yearScoreUploadFiles",
  data(){
    return {
      isCanOperation: false,
      boId: '',
      businessType: '',
    }
  },
  created() {
    this.getYearScoreAttInfoService()
  },
  methods: {
    getYearScoreAttInfoService(){
      getYearScoreAttInfoService()
        .then(res => {
          if(res.code === '0000'){
            this.isCanOperation = res.data.isCanOperation || false
            this.boId = res.data.boId || ''
            this.businessType = res.data.businessType || ''
          }
        })
    },
  }
}
</script>

<style scoped>

</style>
