<!-- 合作单位考核项目阶段得分-施工单位/监理单位/设计单位（根据路由区分） -->
<template>
	<div class="monthlyscore">
		<mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset">
		</mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable ref="table" border :columns="columns" :staticSearchParam="staticSearchParam" :api="tableApi" />
			</div>
		</mssCard>
	</div>
</template>

<script>
import { commonDown } from "@/utils/btn";
import { queryListService, exportExcelService } from '@/api/cooperation_company/period_score'
export default {
	name: 'periodScore',
	data() {
		return {
			searchArr: [
				{
					type: 'input',
					fieldName: 'cooperationCompanyName',
					label: '合作单位名称'
				},
				{
					type: 'select',
					fieldName: 'cooperationCompanyType',
					label: '合作单位类型',
					options: [{
						label: "施工单位",
						value: "3001"
					}, { label: "监理单位", value: "2001" }]
				},
				{
					type: 'input',
					fieldName: 'projectName',
					label: '项目名称'
				},
				{
					type: 'input',
					fieldName: 'projectCode',
					label: '项目编号'
				},
				{
					type: 'date1',
					fieldName: 'evalYear',
					dateType: 'year',
					label: '打分年',
					format: 'yyyy',
					valueFormat: 'yyyy'
				},
				{
					type: 'date1',
					fieldName: 'evalMonth',
					dateType: 'month',
					label: '打分月',
					format: 'MM',
					valueFormat: 'MM'
				},
			],
			searchConfig: [],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: queryListService,
			specailty: []
		}
	},
	watch: {
		$route: {
			handler(n, o) {
				const _searchConfig = this.searchArr.slice()
				if (n && n.path == "/cooperation_company/period_score/conSupervision") {
					this.searchConfig = this.searchArr
					this.staticSearchParam = { stage: "2001,3001" }
				} else {
					_searchConfig.splice(1, 1)
					this.searchConfig = _searchConfig.slice()
					this.staticSearchParam = { stage: "1001" }
				}
			},
			immediate: true
		}
	},
	computed: {
		columns: {
			get() {
				if (this.$route.path == '/cooperation_company/period_score/conSupervision') {
					return [
						{
							prop: "evalMonth",
							label: "月度",
							minWidth: 120,
						},
						{
							prop: "cooperationCompanyName",
							label: "合作单位名称",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "cooperationCompanyType",
							label: "合作单位类型",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "frameContName",
							label: "框架合同名称",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "frameContCode",
							label: "框架合同编号",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "frameContPurType",
							label: "框架合同集采类型",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "projectName",
							label: "项目名称",
							minWidth: 240,
							tooltip: true
						},
						{
							prop: "projectCode",
							label: "项目编号",
							minWidth: 160,
						},
						{
							prop: "projectTotalScore",
							label: "项目总得分",
							minWidth: 140,
						},
						{
							prop: "preparePhase",
							label: "工程准备阶段得分",
							minWidth: 120,
						},
						{
							prop: "carryOutPhase",
							label: "实施阶段得分",
							minWidth: 120,
						},
						{
							prop: "projectAcceptPhase",
							label: "工程验收阶段",
							minWidth: 120,
						}
					]
				} else {
					return [
						{
							prop: "evalMonth",
							label: "月度",
							minWidth: 120,
						},
						{
							prop: "cooperationCompanyName",
							label: "合作单位名称",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "cooperationCompanyType",
							label: "合作单位类型",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "frameContName",
							label: "框架合同名称",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "frameContCode",
							label: "框架合同编号",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "frameContPurType",
							label: "框架合同集采类型",
							minWidth: 160,
							tooltip: true
						},
						{
							prop: "projectName",
							label: "项目名称",
							minWidth: 240,
							tooltip: true
						},
						{
							prop: "projectCode",
							label: "项目编号",
							minWidth: 160,
						},
						{
							prop: "projectTotalScore",
							label: "项目总得分",
							minWidth: 140,
						},
						{
							prop: "designPhase",
							label: "勘察设计阶段",
							minWidth: 120,
						},
						{
							prop: "acceptPhase",
							label: "实验验收阶段",
							minWidth: 120,
						}
					]
				}
			}
		}

	},
	created() {
	},
	methods: {
		search(form) {
      this.staticSearchParam = { ...this.staticSearchParam,...form }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
		exportMethod() {
			commonDown(this.staticSearchParam, exportExcelService);
		}
	}
}
</script>
