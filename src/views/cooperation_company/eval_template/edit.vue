<!--
* @author: hcm
* @date: 2023-07-03
* @description: 考核任务模板-新增或修改
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button type="primary" @click="saveInfo">保存</el-button>
      <el-button @click="back">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard :title="'基本信息'">
      <div slot="content">
        <mssForm
          ref="evalForm"
          :config="formConfig"
          :label-position="labelPosition"
          :form="formModel"
          :is-change="true"
        />
      </div>
    </mssCard>
    <div id="sectionEval" class="page-anchor-point"></div>
    <mssCard :title="'考核任务内容自定义'">
      <div slot="headerBtn">
        <el-button @click="addEval">新增</el-button>
      </div>
      <div slot="content">
        <mss-table
          :columns="columns"
          :stationary="evalStationary"
          :pagination="false"
        />
      </div>
    </mssCard>
    <div id="sectionRole" class="page-anchor-point"></div>
    <mssCard :title="'考评角色配置'">
      <div slot="headerBtn">
        <!-- 考评角色不能再新增 4个角色 -->
        <!-- <el-button @click="addEvalRole">新增</el-button> -->
      </div>
      <div slot="content">
        <mssTable
          :columns="roleColumns"
          :stationary="roleStationary"
          :pagination="false"
        />
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig" />
    <mssChooseUser ref="chooseUser" :mult-select="true" @showCheckList="showCheckList"></mssChooseUser>
  </div>
</template>
<script>
import { saveTemplateService, detailTemplateService, copyTemplateService, checkUnitService, initRoleListService } from '@/api/cooperation_company/eval_template.js'
export default {
  name: 'AddTemplate',
  data() {
    return {
      labelPosition: '',
      formConfig: [
        {
          label: '任务模板名称',
          type: 'input',
          prop: 'name',
          span: 12,
          rules: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        {
          label: '合作单位类型',
          type: 'select',
          prop: 'partnerDeptType',
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择', trigger: 'blur,change' }],
          eventListeners: {
            change: (val) => { this.partnerDeptTypeChange(val) }
          }
        },
        {
          label: '考评方式',
          type: 'select',
          prop: 'evalMode',
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择', trigger: 'blur,change' }],
          eventListeners: {
            change: (val) => { this.evalModeChange(val) }
          }
        },
        {
          label: '考评专业',
          type: 'select',
          prop: 'evalSpecialty',
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择', trigger: 'blur,change' }],
          eventListeners: {
            change: (val) => { this.evalSpecialtyChange(val) }
          }
        },
        {
          label: '考核分类',
          type: 'select',
          prop: 'evalType',
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择', trigger: 'blur' }]
        },
        {
          label: '下发时间',
          type: 'select',
          prop: 'deliveryDate',
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择', trigger: 'blur,change' }]
        },
        {
          label: '制作人',
          type: 'input',
          prop: 'creatorName',
          disabled: true,
          span: 12
        },
        {
          label: '制作时间',
          type: 'input',
          prop: 'createDate',
          disabled: true,
          span: 12
        }
      ],
      formModel: {
        creatorName: sessionStorage.getItem('realName'),
        createDate: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
        name: '',
        partnerDeptType: '',
        evalMode: '',
        evalSpecialty: '',
        evalSpecialtyName: '',
        evalType: '',
        deliveryDate: ''
      },
      evalStationary: [
        { id: 0, evalCol1Category: '', evalCol2SubCategory: '', evalCol3Content: '', evalCol4Standard: '', fullScore: '', weight: '' }
      ],
      columns: [
        { label: '考核类别', prop: 'evalCol1Category', formatter: row => { // 必填
          if (row.deletedFlag) {
            return row.evalCol1Category
          } else {
            return (<el-input type='textarea' autosize={true} v-model={row.evalCol1Category}></el-input>)
          }
        } },
        { label: '考核小类', prop: 'evalCol2SubCategory', formatter: row => {
          if (row.deletedFlag) {
            return row.evalCol2SubCategory
          } else {
            return <el-input type='textarea' minRows='1' autosize={true} v-model={row.evalCol2SubCategory}></el-input>
          }
        } },
        { label: '考核内容', prop: 'evalCol3Content', formatter: row => { // 必填
          if (row.deletedFlag) {
            return row.evalCol3Content
          } else {
            return <el-input type='textarea' minRows='1' autosize={true} v-model={row.evalCol3Content}></el-input>
          }
        } },
        { label: '考核标准', prop: 'evalCol4Standard', formatter: row => {
          if (row.deletedFlag) {
            return row.evalCol4Standard
          } else {
            return <el-input type='textarea' minRows='1' autosize={true} v-model={row.evalCol4Standard}></el-input>
          }
        } },
        { label: '单项满分', prop: 'fullScore', formatter: row => {
          if (row.deletedFlag) {
            return row.fullScore
          } else {
            return <el-input v-model={row.fullScore}></el-input>
          }
        } },
        // { label: '单项权重（%）', prop: 'weight', formatter: row => {
        //   if (row.deletedFlag) {
        //     return row.weight
        //   } else {
        //     return <el-input type='number' v-model={row.weight}></el-input>
        //   }
        // } },
        { label: '操作', formatter: row => {
          if (row.id) {
            return (<span>
              <el-button type='text' onClick={() => { this.deleteEval(row) }}>删除</el-button>
              <el-button type='text' onClick={() => { this.editStatus(row) }}>{row.deletedFlag ? '启用' : '禁用'}</el-button>
            </span>)
          } else {
            return <el-button type='text' onClick={() => { this.deleteEval(row) }}>删除</el-button>
          }
        } }
      ],
      roleStationary: [],
      roleColumns: [
        { label: '考评角色', prop: 'cityName' },
        { label: '人员信息', prop: 'evalUserName', formatter: row => {
          return <el-input v-model={row.evalUserName} multiple readonly onFocus={() => {
            this.openChooseUserDailog(row)
          }}></el-input>
        } },
        { label: '操作', formatter: row => {
          return (<span>
            <el-button type='text' onClick={() => { this.deleteEvalRole(row) }}>删除人员</el-button>
          </span>)
        } }
      ],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '考核<br>内容',
          id: 'sectionEval'
        },
        {
          text: '考评<br>角色',
          id: 'sectionRole'
        }
      ],
      actionType: 'add', // 区分新增、修改
      boId: '',
      roleRow: {},
      specailty: [],
      idList: [] // 页面删除模板项数据集合
    }
  },
  async created() {
    this.actionType = this.$route.query.type
    this.boId = this.$route.query.id
    this.labelPosition = this.actionType === 'detail' ? 'left' : 'top'

    if (this.actionType === 'edit' || this.actionType === 'copy') {
      this.getInfo()
    }
    this.$set(this.formConfig[1], 'options', await this.$dictOptions({ parentValue: '001001001', appCode: '001001' }))
    this.$set(this.formConfig[2], 'options', await this.$dictOptions({ parentValue: '001001002001', appCode: '001001' }))
    this.$set(this.formConfig[3], 'options', await this.$dictOptions({ parentValue: '001001012', appCode: '001001' }))// 考评专业
    this.$set(this.formConfig[4], 'options', await this.$dictOptions({ parentValue: '001001002002', appCode: '001001' }))

    // 下发时间:1~28
    const _1to28arr = []
    for (let i = 1; i <= 28; i++) {
      _1to28arr.push({ label: i+"号", value: i })
    }
    this.$set(this.formConfig[5], 'options', _1to28arr)// 下发时间

    if (this.actionType === 'add') {
      // 获取考评角色
      this.getRoleStationaryList()
    }
  },
  methods: {
    // 获取考评角色列表数据
    getRoleStationaryList() {
      initRoleListService().then(res => {
        if (res.code === '0000' && res.data) {
          this.evalRole = res.data || []
          this.evalRole.forEach((item, index) => {
            const obj = {
              fontId: index + 1,
              evalRole: item.evalRole,
              evalRoleName: item.evalRoleName,
              city: item.city,
              cityName: item.cityName
            }
            this.roleStationary.push(obj)
          })
        }
      })
    },
    openChooseUserDailog(row) {
      this.roleRow = row
      const item = {
        excuterNames: row.evalUserName,
        excuterIds: row.evalUser
      }
      // 过滤单位
      const deptParams = { rootId: row.city, orgChildId: row.city }
      this.$refs.chooseUser.init(item, deptParams)
    },
    showCheckList({ checkList }) {
      const list = checkList
      this.specailty = list
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.realName)
        ids.push(item.userId)
      })
      this.roleStationary = this.roleStationary.map((item) => {
        if (item.city === this.roleRow.city) {
          return { ...item, evalUserName: names.join(','), evalUser: ids.join(',') }
        } else {
          return { ...item }
        }
      })
    },
    // 验证专业和合作单位类型有无合作单位
    async checkUnit() {
      let flag = false
      const currentFormModel = this.$refs.evalForm.modelForm
      const req = {
        deptId: currentFormModel['evalSpecialty'] ? currentFormModel['evalSpecialty'] : '',
        partnerDeptType: currentFormModel['partnerDeptType'] ? currentFormModel['partnerDeptType'] : '',
        evalMode: currentFormModel['evalMode'] ? currentFormModel['evalMode'] : ''
      }
      await checkUnitService(req).then(res => {
        if (res.code === '0000' && res.data == 'success') {
          flag = true
        } else if (res.code === '0000' && res.data == 'fail') {
          flag = false
        }
      })
      return flag
    },
    async partnerDeptTypeChange(val) {
      this.$set(this.$refs.evalForm.modelForm, 'partnerDeptType', val)
      const flag = await this.checkUnit()
      if (!flag) {
        this.$set(this.$refs.evalForm.modelForm, 'partnerDeptType', '')
        this.$message.warning('当前专业，考核方式、合作单位类型有无合作单位')
      }
    },
    async evalModeChange(val) {
      this.$set(this.$refs.evalForm.modelForm, 'evalMode', val)
      const flag = await this.checkUnit()
      if (!flag) {
        this.$set(this.$refs.evalForm.modelForm, 'evalMode', '')

        this.$message.warning('当前专业，考核方式、合作单位类型有无合作单位')
      }
    },
    async getInfo() {
      this.evalRole = await this.$dictOptions({ parentValue: '001001002003', appCode: '001001' })
      detailTemplateService({ boId: this.boId }).then(res => {
        if (res.code === '0000' && res.data) {
          this.roleStationary = res.data.partnerTplRoleList;
          this.evalStationary = res.data.partnerTplDetailList
          for (const key in this.formModel) {
            this.formModel[key] = res.data[key] || ''

            if(key === 'deliveryDate' && this.formModel[key]){
              try{
                this.formModel[key] = Number(this.formModel[key])
              }catch(e){ }
            }
          }
        }
      })
    },
    // 考核内容新增行
    addEval() {
      this.$nextTick(() => {
        const obj = { fontId: this.evalStationary.length, evalCol1Category: '', evalCol2SubCategory: '', evalCol3Content: '', evalCol4Standard: '', fullScore: '', weight: '' }
        this.evalStationary.push(obj)
      })
    },
    // 删除考核内容,fontId用于前端删除某行
    deleteEval(row) {
      this.evalStationary.forEach((item, index) => {
        if (row.id && row.id === item.id) {
          this.idList.push(row.id)
          this.evalStationary.splice(index, 1)
        } else if (row.fontId && row.fontId === item.fontId) {
          this.evalStationary.splice(index, 1)
        }
      })
    },
    // 存在没有保存的模板项即没有id的 需要先保存
    ableToSave() {
      let flag = false
      try {
        this.evalStationary.forEach(item => {
          if (!item.id) {
            throw new Error('存在没保存数据，请选保存')
          }
        })
      } catch (error) {
        flag = true
      }
      return flag
    },
    // 修改状态
    editStatus(row) {
      this.evalStationary = this.evalStationary.map(item => {
        if (item.id && item.id === row.id) {
          item.deletedFlag = !item.deletedFlag
        } else if (item.fontId && item.fontId === row.fontId) {
          item.deletedFlag = !item.deletedFlag
        }
        return item
      })
    },
    addEvalRole() {
      this.$nextTick(() => {
        const obj = { fontId: this.roleStationary.length, evalRole: '', evalUser: '' }
        this.roleStationary.push(obj)
      })
    },
    deleteEvalRole(row) {
      if (row.evalUserName) {
        this.$confirm('是否确认删除人员吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.roleStationary.forEach((item, index) => {
            if (row.fontId === item.fontId) {
              this.roleStationary[index].evalUser = ''
              this.roleStationary[index].evalUserName = ''
            }
          })
        }).catch(() => {})
      }
    },
    // 考核任务内容自定义 必填校验
    evalValidate() {
      let flag = true
      try {
        this.evalStationary.forEach(item => {
          if (!item.evalCol1Category) {
            throw new Error('考核类别不能为空')
          } else if (!item.evalCol3Content) {
            throw new Error('考核内容不能为空')
          }
        })
      } catch (error) {
        this.$message.warning(error.message)
        flag = false
      }
      if (flag) {
        const sum = this.evalStationary.reduce((pre, cur) => {
          if (cur.deletedFlag) { // 禁用项不算满分
            return pre + 0
          } else {
            return pre + parseInt(cur.fullScore)
          }
        }, 0)
        if (sum !== 100) {
          this.$message.warning('单项满分之和应等于100')
          flag = false
        }
      }
      return flag
    },
    saveInfo() {
      let flag = this.evalValidate()
      this.$refs.evalForm.$refs.form.validateScroll((valid) => {
        if (valid && flag) {
          flag = true
        } else {
          return false
        }
      })
      const reqData = {
        id: this.boId,
        idList: this.idList,
        ...this.$refs.evalForm.modelForm,
        partnerTplDetailList: this.evalStationary,
        partnerTplRoleList: this.roleStationary
      }
      if (flag) {
        if (this.actionType === 'copy') {
          // 复制
          copyTemplateService(reqData).then(res => {
            if (res.code === '0000') {
              this.$message({
                type: 'success',
                message: '保存成功！'
              })
              this.back()
            }
          })
        } else {
          if (this.actionType === 'add') {
            delete reqData.id
          }
          saveTemplateService(reqData).then(res => {
            if (res.code === '0000') {
              this.$message({
                type: 'success',
                message: '保存成功！'
              })
              this.back()
            }
          })
        }
      }
    },
    back() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/eval_template'
      })
    },
    async evalSpecialtyChange(val){
      let selectedData = this.formConfig[3].options.filter(item => item.value === val)
      this.$set(this.$refs.evalForm.modelForm, 'evalSpecialty', val)
      this.$set(this.$refs.evalForm.modelForm, 'evalSpecialtyName', selectedData[0].label)
      const flag = await this.checkUnit()
      if (!flag) {
        this.$set(this.$refs.evalForm.modelForm, 'evalSpecialty', '')
        this.$set(this.$refs.evalForm.modelForm, 'evalSpecialtyName', '')
        this.$message.warning('当前专业，考核方式、合作单位类型有无合作单位')
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.cooperation-company{
  padding-right: 70px;
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
