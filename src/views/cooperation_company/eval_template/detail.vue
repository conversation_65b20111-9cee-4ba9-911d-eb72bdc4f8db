<!--
* @author: hcm
* @date: 2023-07-03
* @description: 考核任务模板-查看
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button @click="back">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard :title="'基本信息'" class="form-card">
      <div slot="content">
        <mssForm
          ref="evalForm"
          :config="formConfig"
          :label-position="labelPosition"
          :form="formModel"
          :disable-form="true"
        />
      </div>
    </mssCard>
    <div id="sectionEval" class="page-anchor-point"></div>
    <mssCard :title="'考核任务内容自定义'">
      <div slot="content">
        <mss-table
          :columns="columns"
          :stationary="evalStationary"
          :pagination="false"
        />
      </div>
    </mssCard>
    <div id="sectionRole" class="page-anchor-point"></div>
    <mssCard :title="'考评角色配置'">
      <div slot="content">
        <mssTable
          :columns="roleColumns"
          :stationary="roleStationary"
          :pagination="false"
        />
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig" />
  </div>
</template>
<script>
import { detailTemplateService } from '@/api/cooperation_company/eval_template.js'
export default {
  name: 'TemplateDetail',
  data() {
    return {
      labelPosition: 'left',
      formConfig: [
        {
          label: '任务模板名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '合作单位类型：',
          type: 'input',
          prop: 'partnerDeptTypeName',
          span: 12
        },
        {
          label: '考评方式：',
          type: 'input',
          prop: 'evalModeName',
          span: 12
        },
        {
          label: '考评专业：',
          type: 'input',
          prop: 'evalSpecialtyName',
          span: 12
        },
        {
          label: '考核分类：',
          type: 'input',
          prop: 'evalTypeName',
          span: 12
        },
        {
          label: '下发时间：',
          type: 'input',
          prop: 'deliveryDate'
        },
        {
          label: '制作人：',
          type: 'input',
          prop: 'creatorName'
        },
        {
          label: '制作时间：',
          type: 'input',
          prop: 'createDate'
        }
      ],
      formModel: {
        name: '',
        partnerDeptTypeName: '',
        evalModeName: '',
        evalSpecialtyName: '',
        evalTypeName: '',
        deliveryDate: '',
        creatorName: '',
        createDate: ''
      },
      evalStationary: [],
      columns: [
        { label: '考核类别', prop: 'evalCol1Category' },
        { label: '考核小类', prop: 'evalCol2SubCategory' },
        { label: '考核内容', prop: 'evalCol3Content' },
        { label: '考核标准', prop: 'evalCol4Standard' },
        { label: '单项满分', prop: 'fullScore' },
        // { label: '单项权重（100%）', prop: 'weight' },
        { label: '是否启用', formatter: row => {
          return row.deletedFlag ? '否' : '是'
        } }
      ],
      roleStationary: [],
      roleColumns: [
        { label: '考评角色', prop: 'cityName' },
        { label: '人员信息', prop: 'evalUserName' }
      ],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '考核<br>内容',
          id: 'sectionEval'
        },
        {
          text: '考评<br>角色',
          id: 'sectionRole'
        }
      ],
      boId: ''
    }
  },
  created() {
    this.boId = this.$route.query.id
    this.getInfo()
  },
  methods: {
    getInfo() {
      detailTemplateService({ boId: this.boId }).then(res => {
        if (res.code === '0000' && res.data) {
          this.evalStationary = res.data.partnerTplDetailList
          this.roleStationary = res.data.partnerTplRoleList
          this.formModel = res.data
        }
      })
    },
    back() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/eval_template'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.cooperation-company{
  padding-right: 70px;
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
