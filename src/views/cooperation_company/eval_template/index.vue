<!--
* @author: hcm
* @date: 2023-07-05
* @description: 考核任务模板-列表
-->
<template>
  <div class="template-list">
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @openDialog="openSpecSelectDialog"
      @search="search"
      @reset="reset"
      @changeSelect="changeSelect"
    />
    <mssCard :title="'考评表表头配置信息'">
      <div slot="headerBtn">
        <el-button @click="actionHandle('add')">新增</el-button>
        <el-button @click="delHandle">删除</el-button>
        <el-button type="primary" @click="actionHandle('publish')">发布</el-button>
      </div>
      <div slot="content">
        <mssTable ref="commonTable" :columns="columns" :api="tableApi" :serial="false" :selection="true" :static-search-param="staticSearchParam" />
      </div>
    </mssCard>
    <mssChooseSpecialty ref="chooseSpecialty" :mult-select="false" @showCheckList="showSpecialty"></mssChooseSpecialty>
  </div>
</template>
<script>
import {
  templateListService,
  deleteAllTemplateService,
  toPublishService,
  switchEnableService,
  uniquenessCheckService
} from '@/api/cooperation_company/eval_template.js'
export default {
  name: 'TemplateList',
  data() {
    return {
      searchConfig: [
        {
          label: '任务模板名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '合作单位类型',
          type: 'select',
          fieldName: 'partnerDeptType',
          options: []
        },
        {
          label: '考评方式',
          type: 'select',
          fieldName: 'evalMode',
          options: []
        },
        {
          label: '考评专业',
          type: 'select',
          fieldName: 'evalSpecialty',
          options: []
        }
      ],
      tableApi: templateListService,
      columns: [
        { label: '任务模板名称', prop: 'name', tooltip: true,
          minWidth: '120px' },
        { label: '合作单位类型', prop: 'partnerDeptTypeName', tooltip: true,
          minWidth: '120px' },
        { label: '考评方式', prop: 'evalModeName', tooltip: true,
          minWidth: '120px' },
        { label: '考评专业', prop: 'evalSpecialtyName', tooltip: true,
          minWidth: '120px' },
        { label: '制作人', prop: 'creatorName', tooltip: true,
          minWidth: '120px' },
        { label: '状态', prop: 'entityStatus', tooltip: true,
          minWidth: '100px', formatter: (row) => {
            if (row.entityStatus === 'draft') {
              return '草稿'
            } else if (row.entityStatus === 'publish') {
              return '发布'
            } else {
              return '过期'
            }
          } },
        { label: '启禁状态', prop: 'entityStatus', minWidth: '180px', formatter: (row) => {
          if (row.entityStatus === 'publish') {
            return <el-switch
              style='display: block'
              v-model={row.enableStatus}
              active-text='启用'
              inactive-text='禁用'
              onChange={() => {
                this.toChangeState(row)
              }}
            >
            </el-switch>
          }
        } },
        // { label: '制作人', prop: 'creatorName', minWidth: '120px', tooltip: true },
        { label: '操作', minWidth: '120px', formatter: row => {
          return (
            <span>
              {(row.entityStatus === 'draft' || !row.enableStatus) ? (
                <el-button type='text' onClick={() => this.actionHandle('edit', row)}>编辑</el-button>
              ) : ('')}
              <el-button type='text' onClick={() => this.actionHandle('detail', row)}>查看</el-button>
              <el-button type='text' onClick={() => this.actionHandle('copy', row)}>复制</el-button>
            </span>
          )
        } }
      ],
      specailty: [],
      staticSearchParam: {}
    }
  },
  async created() {
    this.$set(this.searchConfig[1], 'options', await this.$dictOptions({ parentValue: '001001001', appCode: '001001' }))
    this.$set(this.searchConfig[2], 'options', await this.$dictOptions({ parentValue: '001001002001', appCode: '001001' }))
    this.$set(this.searchConfig[3], 'options', await this.$dictOptions({ parentValue: '001001012', appCode: '001001' }))// 考评专业
  },
  methods: {
    toChangeState(row) {
      switchEnableService({ id: row.id, status: row.enableStatus ? '1' : '0' }).then(res => {
        if (res.code === '0000') {
          this.search()
        }
      })
    },
    openSpecSelectDialog() {
      const item = this.specailty
      this.$refs.chooseSpecialty.init(item)
    },
    showSpecialty({ checkList }) {
      const list = checkList
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.name)
        ids.push(item.id)
      })
      this.$set(this.$refs.searchForm.searchForm, 'evalSpecialty', ids.join(','))
      this.$set(this.$refs.searchForm.searchForm, 'evalSpecialtyName', names.join(','))
    },
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.commonTable.multipleSelection = []
      this.$refs.commonTable.$refs.table.clearSelection()
      this.$refs.commonTable.page.current = 1
      this.$refs.commonTable.getTableData(form)
    },
    reset(form) {
      this.$refs.searchForm.searchForm.evalSpecialty = '';
      this.$refs.searchForm.searchForm.evalSpecialtyName = '';
      this.specailty = []
      this.search({...form,evalSpecialty:''})
    },
    delHandle() {
      const selectData = this.$refs.commonTable.multipleSelection
      const ids = []
      let flag = true
      if (selectData.length > 0) {
        selectData.forEach(item => {
          ids.push(item.id)
        })
        try {
          selectData.forEach(item => {
            if (item.entityStatus !== 'publish') {
              ids.push(item.id)
            } else {
              throw new Error('已发布模板不能删除')
            }
          })
        } catch (error) {
          flag = false
          this.$message.warning(error.message)
        }
      } else {
        this.$message.warning('请选择模板！')
      }

      if (flag) {
        flag = false
        this.$confirm('是否确认删除这些数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteAllTemplateService({ idList: ids }).then(res => {
            if (res.code === '0000') {
              this.search(this.$refs.searchForm.searchForm)
              this.$message({ type: 'success', message: '删除成功！' })
            }
          })
        }).catch(() => {})
      }
    },
    actionHandle(type, row) {
      if (type === 'detail') {
        this.$router.push({
          path: '/cooperation_company/eval_template/view',
          query: {
            id: row.id
          }
        })
      } else if (type === 'publish') {
        this.publishTemplate()
      } else {
        this.$router.push({
          path: '/cooperation_company/eval_template/edit',
          query: {
            type,
            id: row ? row.id : ''
          }
        })
      }
    },
    publishTemplate() {
      // 草稿可发布，同时需要唯一性校验
      const selectData = this.$refs.commonTable.multipleSelection
      const idsArr = []
      let flag = true
      if (selectData.length > 0) {
        try {
          selectData.forEach(item => {
            if (item.entityStatus === 'draft') {
              idsArr.push(item.id)
            } else {
              flag = false
              throw new Error('存在已发布模板或过期模板')
            }
          })
        } catch (error) {
          this.$message.warning(error.message)
        }
      } else {
        flag = false
        this.$message.warning('请选择模板！')
      }
      if (flag) {
        const ids = idsArr.join(',')
        uniquenessCheckService({ ids }).then(res => {
          if (res.code === '0000' && res.data === 'fail') {
            this.$confirm('存在同类型模板, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.publishFn(ids)
            }).catch(() => {})
          } else {
            this.publishFn(ids)
          }
        })
      }
    },
    publishFn(ids) {
      toPublishService({ ids, status: 'publish' }).then(res => {
        if (res.code === '0000') {
          this.search()
        }
      })
    },
    changeSelect(name, val){
      // 考评专业找中文
      if (name == 'evalSpecialty') {
        const selectedData = this.searchConfig[0].options.filter(item => item.value === val)
        this.$set(this.$refs.searchForm.searchForm, 'evalSpecialtyName', selectedData[0].label)
      }
    }
  }
}
</script>
