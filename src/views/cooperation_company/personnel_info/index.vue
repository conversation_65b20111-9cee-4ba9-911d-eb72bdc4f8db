<!--
* @author: hcm
* @date: 2023-07-07
* @description: 合作单位人员-列表
-->
<template>
  <div class="personnel_info">
    <mssSearchForm
      ref="searchForm"
      :form="searchForm"
      :search-config="searchConfig"
      @search="search"
      @reset="reset"
    />
    <mssCard :title="'合作单位人员信息列表'">
      <div slot="headerBtn">
        <el-button v-if="powerData['personnel_info_add']" @click="acitonHandle('add')">新增</el-button>
        <el-button type="primary" @click="exportList">导出</el-button>
        <el-button type="primary" @click="exportAttachment">导出附件</el-button>
      </div>
      <div slot="content">
        <mssTable
          rowKey="$index"
          ref="commonTable"
          :columns="columns"
          :api="tableApi"
          :reserveSelection="false"
          selection
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
  </div>
</template>
<script>
import { commonDown } from '@/utils/btn.js'
import {
  queryListService,
  removeIdsService,
  exportPageService,
  exportAttachmentService
} from '@/api/cooperation_company/personnel_info.js'
import { queryAreaListService } from '@/api/common_api.js'
export default {
  name: 'PersonnelInfoList',
  data() {
    return {
      powerData:[],//权限
      searchConfig: [
        {
          label: '合作单位名称',
          type: 'input',
          fieldName: 'cooperationCompanyName'
        },
        {
          label: '合作单位类型',
          type: 'select',
          fieldName: 'cooperationCompanyType',
          options: [
            { label: '设计', value: '0' },
            { label: '监理', value: '1' },
            { label: '施工', value: '2' }
          ]
        },
        {
          label: '姓名',
          type: 'input',
          fieldName: 'cooperationPersonName'
        },
        {
          label: '手机号',
          type: 'input',
          fieldName: 'coopcompanyPersonTel'
        },
        {
          label: '身份证号码',
          type: 'input',
          fieldName: 'coopcompanyPersonIdcard'
        },
        {
          label: '负责地市',
          type: 'select',
          fieldName: 'city',
          options: []
        },
        {
          label: '角色名称',
          type: 'input',
          fieldName: 'roleName'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'entityStatus'
        },
        {
          label: '数据状态',
          type: 'select',
          options: [
            {label:'全部',value:'all'},
            {label:'单位人员最新数据',value:'lastData'},
          ],
          fieldName: 'dataStatus'
        }
      ],
      tableApi: queryListService,
      columns: [
        { label: '合作单位名称', prop: 'cooperationCompanyName', tooltip: true,
          minWidth: '220px' },
        { label: '合作单位编码', prop: 'cooperationCompanyCode', tooltip: true,
          minWidth: '100px' },
        { label: '姓名', prop: 'cooperationPersonName', tooltip: true,
          minWidth: '100px' },
        { label: '性别', prop: 'cooperationPersonSex', tooltip: true,
          minWidth: '100px' },
        { label: '身份证号码', prop: 'coopcompanyPersonIdcard', tooltip: true,
          minWidth: '140px',formatter:(row)=>{
            if( row.coopcompanyPersonIdcard){
              return row.coopcompanyPersonIdcard.replace(/^(.{3})(?:\d+)(.{3})$/, "$1************$2")
            }
          } },
        { label: '手机号', prop: 'coopcompanyPersonTel', tooltip: true,
          minWidth: '100px',formatter:(row)=>{
            if(row.coopcompanyPersonTel){
              return row.coopcompanyPersonTel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }
          }},
        { label: '联系邮箱', prop: 'coopcompanyPersonEmail', tooltip: true,
          minWidth: '100px' },
        { label: '服务区域（地市）', prop: 'city', tooltip: true,
          minWidth: '120px' },
        { label: '负责区县', prop: 'countys', tooltip: true,
          minWidth: '100px' },
        { label: '角色名称', prop: 'roleName', tooltip: true,
          minWidth: '100px' },
        { label: '职务', prop: 'duties', tooltip: true,
          minWidth: '100px' },
        { label: '职称', prop: 'technicalTitle', tooltip: true,
          minWidth: '100px' },
        { label: '资质证书种类', prop: 'certTypeName', tooltip: true,
          minWidth: '100px' },
        { label: '资质证书编号', prop: 'certCode', tooltip: true,
          minWidth: '100px' },
        { label: '资质证书文件（上传资质文件的名称）', prop: 'certFilePath', tooltip: true,
          minWidth: '240px' },
        { label: '资质有效期从', prop: 'certPeriodSt', tooltip: true,
          minWidth: '100px' },
        { label: '资质有效期至', prop: 'certPeriodEnd', tooltip: true,
          minWidth: '100px'},
        { label: '是否有正式劳动合同', prop: 'laborContFlag', tooltip: true,
          minWidth: '140px' },
        { label: '劳动合同扫描件（上传资质文件的名称）', prop: 'scanning', tooltip: true,
          minWidth: '240px' },
        { label: '劳动合同签订单位', prop: 'laborContsign', tooltip: true,
          minWidth: '130px' },
        { label: '劳动合同从', prop: 'laborPeriodSt', tooltip: true,
          minWidth: '100px'},
        { label: '劳动合同至', prop: 'laborPeriodEnd', tooltip: true,
          minWidth: '100px' },
        { label: '投保险种', prop: 'insuranceCoverage', tooltip: true,
          minWidth: '100px' },
        { label: '保单号', prop: 'insuranceNumber', tooltip: true,
          minWidth: '100px' },
        { label: '投保单位名称', prop: 'subscriberEmployer', tooltip: true,
          minWidth: '100px' },
        { label: '保险公司名称', prop: 'insuranceCompany', tooltip: true,
          minWidth: '120px' },
        { label: '保险金额（万元）', prop: 'insuranceAmount', tooltip: true,
          minWidth: '130px' },
        { label: '保险金额起时间', prop: 'insurancePeriodSt', tooltip: true,
          minWidth: '140px' },
        { label: '保险金额至时间', prop: 'insurancePeriodEnd', tooltip: true,
          minWidth: '140px' },
        { label: '社保账号', prop: 'insuranceAccount', tooltip: true,
          minWidth: '100px' },
        { label: '社保地址', prop: 'insuranceAddress', tooltip: true,
          minWidth: '100px' },
        { label: '工作年限', prop: 'serviceLength', tooltip: true,
          minWidth: '100px' },
        { label: '是否农民工', prop: 'peasantWorkerEnabled', tooltip: true,
          minWidth: '100px' },
        { label: '是否支付月度工资', prop: 'wagesAmountFlag', tooltip: true,
          minWidth: '140px' },
        { label: '支付工资金额', prop: 'wagesAmount', tooltip: true,
          minWidth: '100px' },
        { label: '培训时间', prop: 'trainTime', tooltip: true,
          minWidth: '100px' },
        { label: '培训内容', prop: 'trainContent', tooltip: true,
          minWidth: '100px' },
        { label: '培训考核结果', prop: 'trainEvalResult', tooltip: true,
          minWidth: '100px' },
        { label: '是否通过', prop: 'trainPassFlag', tooltip: true,
          minWidth: '100px' },
        { label: '移动认证编号', prop: 'mobileAuthNumber', tooltip: true,
          minWidth: '100px' },
        { label: '人员状态', prop: 'enabledFlag', tooltip: true,
          minWidth: '100px' },
        { prop: 'entityStatus', label: '状态', tooltip: true,
          minWidth: '100px' },
        { label: '操作', fixed: 'right', tooltip: true,
          minWidth: '120px', formatter: row => {
            return (
              <span>
                {row.isAllowOperate ? (<span
                  class='table_btn mr10'
                  onClick={() => { this.acitonHandle('edit', row) }}
                >
                  处理
                </span>) : ('')}
                {this.powerData['personnel_info_view'] &&row.id? (<span class='table_btn mr10' onClick={() => { this.acitonHandle('detail', row) }}>
                  查看
                </span>) : ('')}
                {row.isAllowDelete ? (
                  <span
                    class='table_btn'
                    onClick={() => { this.delHandle(row) }}
                  >
                  删除
                  </span>
                ) : ('')}
              </span>
            )
          } }
      ],
      searchForm:{dataStatus:"lastData"},
      staticSearchParam: {dataStatus:'lastData'}
    }
  },
  async created() {
    this.getPower()
    this.getAreaList()
    // this.$set(this.searchConfig[1], 'options', await this.$dictOptions({ parentValue: '001001001', appCode: '001001' }))
    this.$set(this.searchConfig[7], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB' }))
  },
  methods: {
    getPower(){
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item)=>{
        this.powerData[item.authority]=true
      })
    },
    getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.searchConfig[5], 'options', list)
        }
      })
    },
    exportList() {
      const params = {
        ...this.$refs.searchForm.searchForm,
        limit: this.$refs.commonTable.page.size,
        page: this.$refs.commonTable.page.current,
        exportType: 'all'
      }
      commonDown(params, exportPageService)
    },
    // 导出附件
    exportAttachment() {
      const selection = this.$refs.commonTable.multipleSelection
      const ids = []
      if (selection.length > 0) {
        selection.forEach(item => {
          if(item.id){
            ids.push(`${item.id}_${item.coopcompanyPersonIdcard}`)
          }else{
            ids.push(`_${item.coopcompanyPersonIdcard}`)
          }
        })
        commonDown({ ids: ids.join(',') }, exportAttachmentService)
      } else {
        this.$message.warning('请勾选数据')
      }
    },
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.commonTable.page.current = 1
      this.$refs.commonTable.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
    acitonHandle(type, row) {
      const id = row ? row.id : ''
      if (type === 'detail') {
        this.$router.push({
          path: '/cooperation_company/personnel_info/view',
          query: {
            boId: id
          }
        })
      } else {
        this.$router.push({
          path: '/cooperation_company/personnel_info/edit',
          query: {
            boId: id
          }
        })
      }
    },
    delHandle(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeIdsService({ ids: row.id }).then(res => {
          if (res.code === '0000') {
            this.$message({
              type: 'success',
              message: '删除成功！'
            })
            this.search(this.$refs.searchForm.searchForm)
          }
        })
      }).catch(() => {})
    }
  }
}
</script>
