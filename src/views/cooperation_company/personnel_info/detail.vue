<!--
* @author: hcm
* @date: 2023-07-07
* @description: 合作单位人员-查看
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard :title="'基本信息'" class="form-card">
      <div slot="content">
        <mssForm
          ref="baseForm"
          :config="baseFormConfig"
          :label-position="'left'"
          :form="baseFormModel"
          :disable-form="true"
        />
      </div>
    </mssCard>
    <div id="sectionZh" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.coopcompanyPersonIdcard" :title="'账号信息'">
      <div slot="content">
        <mss-table
          :columns="accountColumns"
          :api="accountTableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionRole" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.coopcompanyPersonIdcard" :title="'角色信息'">
      <div slot="content">
        <h4 style="font-size: 12px;font-weight: bold;">综合管理员权限</h4>
        <mss-table
          ref="roleZHYTable"
          :columns="roleZYHColumns"
          :api="roleZHYTableApi"
          :static-search-param="staticSearchParam"
        />
          <h4 style="font-size: 12px;font-weight: bold;">项目部负责人权限</h4>
          <mss-table
            :columns="roleColumns"
            :api="roleTableApi"
            :static-search-param="staticSearchParam"
          />
      </div>
    </mssCard>
    <div id="sectionLd" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.coopcompanyPersonIdcard" :title="'劳动合同信息'">
      <div slot="content">
        <mss-table
          :columns="contractColumns"
          :api="contractTableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionZz" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.coopcompanyPersonIdcard" :title="'资质信息'">
      <div slot="content">
        <mss-table
          :columns="qualificationColumns"
          :api="certTableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionBc" class="page-anchor-point"></div>
    <mssCard v-if="insuranceParam.cooperationPersonId" :title="'保险信息'">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportInsurance">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="insuranceTable"
          :columns="insuranceColumns"
          :api="insuranceApi"
          :static-search-param="insuranceParam"
        />
      </div>
    </mssCard>
    <div id="sectionPx" class="page-anchor-point"></div>
    <mssCard v-if="insuranceParam.cooperationPersonId" :title="'培训信息'">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportTraining">导出</el-button>
      </div>

      <div slot="content">
        <mssTable
          ref="trainingTable"
          :columns="trainingColumns"
          :api="trainingApi"
          :static-search-param="trainingParam"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment
      v-if="boId"
      :business-type="workflowCode"
      :node-name="nodeName"
      :bo-id="boId"
      :deal-page="false"
    ></mssAttachment>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig" />
  </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
import {
  queryDetailService,
  insuranceListService,
  trainingListService,
  trainingExportExcelService,
  insuranceExportExcelService,
  accListService, // 账号信息
  roleListService, // 角色信息
  laborContListService, // 劳动合同信息
  certContListService, // 资质信息
  roleListZHYService
} from '@/api/cooperation_company/personnel_info.js'
import { queryAreaListService } from '@/api/common_api.js'
export default {
  name: 'PersonnelInfoDetail',
  data() {
    return {
      insuranceApi: insuranceListService,
      trainingApi: trainingListService,
      staticSearchParam: {},
      baseFormModel: {},
      // 账号信息
      // 账号信息
      accountTableApi: accListService,
      accountColumns: [
        { label: '登录账号', prop: 'account' },
        { label: '登录密码', prop: 'pwd', formatter:row=>{
         if(row.pwd){
          return row.pwd.replace(/./g, "*")
          }
        }},
        { label: '允许登录', prop: 'enabled' },
        { label: '是否工程质检', prop: 'projectQualityEnable' }],
      // 角色信息（综合员权限）
      roleConfig:[
        {
          label: '服务范围：',
          type: 'input',
          prop: 'businessScopeName',
          disabled: true,
          span: 12
        }, {
          label: '角色名称：',
          type: 'input',
          prop: 'roleName',
          disabled: true,
          span: 12
        }
      ],
      roleFormModel:{},
      roleZYHColumns:[
        { label: '服务范围', prop: 'businessScopeName' },
        { label: '角色名称', prop: 'roleName' }
      ],
      roleZHYTableApi:roleListZHYService,
      roleTableApi: roleListService,
      roleColumns: [
        { label: '服务范围', prop: 'businessScopeName' },
        { label: '角色名称', prop: 'roleName' },
        { label: '角色状态', prop: 'roleStatus' }],
      // 劳动合同信息
      contractTableApi: laborContListService,
      // 资质信息
      certTableApi: certContListService,
      insuranceColumns: [
        { label: '投保险种', prop: 'insuranceCoverage' },
        { label: '保单号', prop: 'insuranceNumber' },
        { label: '保险公司名称', prop: 'insuranceCompany' },
        { label: '保险金额（万元）', prop: 'insuranceAmount' },
        { label: '保险起时间', prop: 'insurancePeriodSt' },
        { label: '保险止时间', prop: 'insurancePeriodEnd' },
        { label: '投保单位', prop: 'subscriberEmployer' }
      ],
      trainingColumns: [
        { label: '培训时间', prop: 'trainTime' },
        { label: '培训内容', prop: 'trainContent' },
        { label: '培训考核结果', prop: 'trainEvalResult' },
        { label: '是否通过', prop: 'trainPassFlag' }
      ],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '账号<br>信息',
          id: 'sectionZh'
        },
        {
          text: '资质<br>信息',
          id: 'sectionZz'
        },
        {
          text: '保险<br>信息',
          id: 'sectionBc'
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow'
        }
      ],
      boId: '',
      workflowCode: 'PartnerUser',
      insuranceParam: {},
      trainingParam: {},
      nodeName: '',
      cityList: [],
      countyList: []
    }
  },
  computed: {
    baseFormConfig() {
      return [
        {
          label: '合作单位名称：',
          type: 'input',
          prop: 'cooperationCompanyName',
          disabled: true,
          span: 12
        }, {
          label: '合作单位编码：',
          type: 'input',
          prop: 'cooperationCompanyCode',
          disabled: true,
          span: 12
        }, {
          label: '姓名：',
          type: 'input',
          prop: 'cooperationPersonName',
          span: 12
        }, {
          label: '性别：',
          type: 'select',
          prop: 'cooperationPersonSex',
          disabled: true,
          span: 12,
          options: [
            { label: '男', value: '0' },
            { label: '女', value: '1' }
          ]
        }, {
          label: '身份证号：',
          type: 'input',
          prop: 'personIdcard',
          disabled: true,
          span: 12
        }, {
          label: '手机号码：',
          type: 'input',
          prop: 'personTel',
          disabled: true,
          span: 12
        }, {
          label: '联系邮箱：',
          type: 'input',
          prop: 'coopcompanyPersonEmail',
          disabled: true,
          span: 12
        }, {
          label: '社保账号：',
          type: 'input',
          prop: 'insuranceAccount',
          disabled: true,
          span: 12
        }, {
          label: '社保地址：',
          type: 'input',
          prop: 'insuranceAddress',
          disabled: true,
          span: 12
        },
        {
          label: '学历：',
          type: 'input',
          prop: 'degree',
          disabled: true,
          span: 12
        },
        {
          label: '工作年限：',
          type: 'input',
          prop: 'serviceLength',
          disabled: true,
          span: 12
        },{
          label: '职称：',
          type: 'input',
          prop: 'technicalTitle',
          disabled: true,
          span: 12
        }, {
          label: '是否农民：',
          type: 'input',
          prop: 'peasantWorkerEnabledName',
          disabled: true,
          span: 12
        }, {
          label: '用工性质：',
          type: 'input',
          prop: 'jobNatureName',
          disabled: true,
          span: 12
        }, {
          label: '支付工资金额：',
          type: 'input',
          prop: 'wagesAmount',
          disabled: true,
          span: 12
        },
        {
          label: '负责地市：',
          type: 'select',
          prop: 'city',
          disabled: true,
          options: this.cityList,
          span: 12
        }, {
          label: '负责区县：',
          type: 'select',
          prop: 'countys',
          span: 12,
          options: this.countyList,
          multiple: true
        }, {
          label: '职务：',
          type: 'input',
          prop: 'duties',
          span: 12,
          disabled: true
        }, {
          label: '角色名称：',
          type: 'input',
          prop: 'roleNames',
          span: 24,
          disabled: true
        }, {
          label: '移动认证编号：',
          type: 'input',
          prop: 'mobileAuthNumber',
          span: 12
        }, {
          label: '编制人：',
          type: 'input',
          prop: 'creatorName',
          span: 12,
          disabled: true
        },
        {
          label: '编制部门：',
          type: 'input',
          prop: 'applyDeptName',
          span: 12,
          disabled: true
        },
        {
          label: '编制时间：',
          type: 'input',
          prop: 'createDate',
          span: 12,
          disabled: true
        }
      ]
    },
    qualificationColumns(){
      return [
        { label: '证书分类', prop: 'certTypeName' },
        { label: '证书编号', prop: 'certCode' },
        { label: '证书有效时间', formatter: row => {
          if(row.certPeriodEnd){
            return this.$moment(row.certPeriodEnd).format('yyyy-MM-DD')
          }
        } },
        { label: '证书文件', prop: 'certFileName',minWidth:'120px', formatter: (row, column, cellValue, index) => {
          if(row.fileIds){
            return  <mssFileTemplate ref={'fileList'+index} customParams={{ids:row.fileIds}}></mssFileTemplate>
          }
        } },
        {
          label:'操作',
          formatter:(row, column, cellValue, index)=>{
            if(row.fileIds){
              return <span  class='table_btn mr10' onClick={()=>{this.downLoadCert(row,index)}}>批量下载证书</span>
            }
          }
        }
      ]
    },
    contractColumns(){
      return [
        { label: '合同扫描件', prop: 'scanning',
        formatter: (row, column, cellValue, index) => {
          if(row.laborContFileIds){
            return  <mssFileTemplate showCheckBox={false} ref={'fileList'+index} customParams={{ids:row.laborContFileIds}}></mssFileTemplate>
          }else{
            return row.scanning
          }
        }
       },
        { label: '合同有效期', prop:'laborPeriodEnd', formatter: row => {
          if(row.laborPeriodEnd){
            return this.$moment(row.laborPeriodEnd).format('yyyy-MM-DD')
          }
        } },
        { label: '社保缴纳记录', prop: 'socialSecurityRecord' , formatter: (row, column, cellValue, index) => {
          if(row.socialCertFileIds){
            return  <mssFileTemplate showCheckBox={false} ref={'fileList'+index} customParams={{ids:row.socialCertFileIds}}></mssFileTemplate>
          }
        }}]
      }
  },
  created() {
    this.labelPosition = 'left'
    this.id = this.$route.query.boId
    this.getAreaList('city', { parentId: '-2', typeCode: 'area' })
    if (this.id) {
      this.getInfo()
    }
  },
  methods: {
    // 下载资质证书
    downLoadCert(row,index) {
      this.$refs[`fileList${index}`].getFilesBatchDownload();
    },
    getAreaList(type, params) {
      queryAreaListService(params).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          if (type === 'city') {
            this.cityList = list
          } else {
            this.countyList = list
          }
        }
      })
    },
    exportTraining() {
      commonDown(this.trainingParam, trainingExportExcelService)
    },
    exportInsurance() {
      commonDown(this.insuranceParam, insuranceExportExcelService)
    },
    // 详情
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.baseFormModel = { ...this.baseFormModel, ...res.data }
          this.baseFormModel.cooperationCompanyName = res.data.cooperationCompanyItemDto.cooperationcompanyname
          this.baseFormModel.cooperationCompanyCode = res.data.cooperationCompanyItemDto.cooperationcompanycode
          this.baseFormModel.cooperationPersonName = res.data.cooperationCompanyItemDto.cooperationpersonname
          this.baseFormModel.cooperationPersonSex = res.data.cooperationCompanyItemDto.cooperationpersonsex
          this.baseFormModel.coopcompanyPersonIdcard = res.data.cooperationCompanyItemDto.coopcompanypersonidcard
          this.baseFormModel.coopcompanyPersonTel = res.data.cooperationCompanyItemDto.coopcompanypersontel
          this.baseFormModel.coopcompanyPersonEmail = res.data.cooperationCompanyItemDto.coopcompanypersonemail
          this.baseFormModel.technicalTitle = res.data.technicalTitle||''
          this.baseFormModel.cooperationPersonId = res.data.cooperationCompanyItemDto.cooperationpersonid
          
          // 身份证号和手机号脱敏
          if(this.baseFormModel.coopcompanyPersonIdcard){
            this.baseFormModel.personIdcard = this.baseFormModel.coopcompanyPersonIdcard.replace(/^(.{3})(?:\d+)(.{3})$/, "$1************$2")
          }
          if(this.baseFormModel.coopcompanyPersonTel){
            this.baseFormModel.personTel = this.baseFormModel.coopcompanyPersonTel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
          }
          // 人员未入库字段
          // this.baseFormModel.insuranceAccount = res.data.cooperationCompanyItemDto.insuranceaccount||''
          // this.baseFormModel.insuranceAddress = res.data.cooperationCompanyItemDto.insuranceaddress||''
          // this.baseFormModel.serviceLength = res.data.cooperationCompanyItemDto.servicelength||''
          // this.baseFormModel.peasantWorkerEnabled = res.data.cooperationCompanyItemDto.peasantworkerenabled||''
          // this.baseFormModel.jobNature = res.data.cooperationCompanyItemDto.jobnature||''
          // this.baseFormModel.wagesAmount = res.data.cooperationCompanyItemDto.wagesamount||''
          // this.baseFormModel.roleNames = res.data.roleNames
          // this.baseFormModel.mobileauthNumber = res.data.cooperationCompanyItemDto.mobileauthnumber||''
         
          this.insuranceParam = {
            cooperationPersonId: this.baseFormModel.cooperationPersonId,
            partnerUserId: res.data.id
          }
          this.trainingParam = {
            cooperationPersonId: this.baseFormModel.cooperationPersonId,
            partnerUserId: res.data.id
          }
          this.staticSearchParam = {
            coopcompanyPersonIdcard: this.baseFormModel.coopcompanyPersonIdcard,
            coopcompanyPersonTel: this.baseFormModel.coopcompanyPersonTel
          }
          if (this.baseFormModel.countys) {
            // 处理区县
            this.baseFormModel.countys = this.baseFormModel.countys.split(',')
          }
          if (this.baseFormModel.city) {
            this.getAreaList('county', { parentId: this.baseFormModel.city, typeCode: 'area' })// 获取地市
          }
          this.boId = this.id
        }
      })
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/personnel_info'
      })
    }
  }
}
</script>
<style lang="scss">
.cooperation-company{
  padding-right: 70px;
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
  .file-down{
    .el-input{
      width: 75%;
      margin-right: 10px;
    }
  }
}
</style>
