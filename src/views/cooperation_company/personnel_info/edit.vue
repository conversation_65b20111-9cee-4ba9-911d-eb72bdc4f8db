<!--
* @author: hcm
* @date: 2023-07-07
* @description: 合作单位人员-新增、处理
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button v-if="boId" type="primary" @click="submit">提交审核</el-button>
      <el-button v-if="!disableBasicForm" type="primary" @click="saveInfo">保存草稿</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard :title="'基本信息'" class="form-card">
      <div slot="content">
        <mssForm
          ref="baseForm"
          :config="baseFormConfig"
          :label-position="'top'"
          :form="baseFormModel"
          :disable-form="disableBasicForm"
        />
      </div>
    </mssCard>
    <div id="sectionZh" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.coopcompanyPersonIdcard||staticSearchParam.coopcompanyPersonTel" :title="'账号信息'">
      <div slot="content">
        <mss-table
          ref="accountTable"
          :columns="accountColumns"
          :api="accountTableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionRole" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.coopcompanyPersonIdcard||staticSearchParam.coopcompanyPersonTel" :title="'角色信息'">
      <div slot="content">
        <h4 style="font-size: 12px;font-weight: bold;">综合管理员权限</h4>
        <mss-table
          ref="roleZHYTable"
          :columns="roleZYHColumns"
          :api="roleZHYTableApi"
          :static-search-param="staticSearchParam"
        />
        <h4 style="font-size: 12px;font-weight: bold;">项目部负责人权限</h4>
        <mss-table
          ref="roleTable"
          :columns="roleColumns"
          :api="roleTableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionLd" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.coopcompanyPersonIdcard||staticSearchParam.coopcompanyPersonTel" :title="'劳动合同信息'">
      <div slot="content">
        <mss-table
          ref="contractTable"
          :columns="contractColumns"
          :api="contractTableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionZz" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.coopcompanyPersonIdcard||staticSearchParam.coopcompanyPersonTel" :title="'资质信息'">
      <div slot="content">
        <mss-table
          ref="certTable"
          :columns="qualificationColumns"
          :api="certTableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionBc" class="page-anchor-point"></div>
    <mssCard v-if="insuranceParam.partnerUserId" :title="'保险信息'">
      <div slot="headerBtn">
        <el-upload
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importInsuranceFile"
        >
          <el-button v-if="!disableBasicForm" type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportInsurance">导出</el-button>
        <el-button v-if="!disableBasicForm" @click="addTable('insurance')">新增</el-button>
        <el-button v-if="!disableBasicForm" type="primary" @click="delTable('insurance')">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="insuranceTable"
          :columns="insuranceColumns"
          :api="insuranceApi"
          :selection="true"
          :static-search-param="insuranceParam"
        />
      </div>
    </mssCard>
    <div id="sectionPx" class="page-anchor-point"></div>
    <mssCard v-if="insuranceParam.partnerUserId" :title="'培训信息'">
      <div slot="headerBtn">
        <el-upload
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importTrainingFile"
        >
          <el-button v-if="!disableBasicForm" type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportTraining">导出</el-button>
        <el-button v-if="!disableBasicForm" @click="addTable('training')">新增</el-button>
        <el-button v-if="!disableBasicForm" type="primary" @click="delTable('training')">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="trainingTable"
          :columns="trainingColumns"
          :api="trainingApi"
          :static-search-param="trainingParam"
          :selection="true"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :bo-id="boId" :business-type="workflowCode" :node-name="nodeName"></mssAttachment>

    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig" />

    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="!disableBasicForm"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>

    <!--保险信息/培训信息 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="70%"
    >
      <mssForm
        v-if="dialogVisible"
        ref="dialogForm"
        :config="dialogConfig"
        :label-position="dialogPosition"
        :disable-form="dialogDisableForm"
        :form="dialogForm"
      ></mssForm>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="!dialogDisableForm" type="primary" @click="saveDialog">确 定</el-button>
        <el-button @click="cancalDialog">取 消</el-button>

      </span>
    </el-dialog>
    <!-- 人员弹框 -->
    <mssChooseUser ref="chooseUser" :mult-select="false" @showCheckList="showCheckList"></mssChooseUser>
  </div>
</template>
<script>
import { commonDown } from '@/utils/btn'
import {
  saveInfoService,
  updateInfoService,
  queryDetailService,
  getPersonByUserIdService,
  insuranceListService,
  insuranceSaveService,
  insuranceUpdateService,
  insuranceDetailService,
  insuranceBatchremoveService,
  insuranceExportExcelService,
  insuranceImportExcelService,
  trainingListService,
  trainingSaveService,
  trainingUpdateService,
  trainingDetailService,
  trainingBatchremoveService,
  trainingExportExcelService,
  trainingImportExcelService,
  accListService, // 账号信息
  roleListService, // 角色信息
  laborContListService, // 劳动合同信息
  certContListService, // 资质信息
  notExistsProcessService,
  roleListZHYService
} from '@/api/cooperation_company/personnel_info.js'
import { queryAreaListService } from '@/api/common_api.js'
export default {
  name: 'PersonnelInfoEdit',
  data() {
    return {
      // 保险金额起时间
      insurancePeriodStPickerOptions: {
        disabledDate: (time) => {
          if (this.$refs.dialogForm.modelForm['insurancePeriodEnd']) {
            const end = new Date(this.$refs.dialogForm.modelForm['insurancePeriodEnd'])
            return time.getTime() > end.getTime()
          }
        }
      },
      // 保险金额至时间
      insurancePeriodEndPickerOptions: {
        disabledDate: (time) => {
          if (this.$refs.dialogForm.modelForm['insurancePeriodSt']) {
            const start = new Date(this.$refs.dialogForm.modelForm['insurancePeriodSt'])
            return start.getTime() > time.getTime()
          }
        }
      },
      baseFormModel: {
        creatorId: sessionStorage.getItem('userId'),
        creatorName: sessionStorage.getItem('realName'),
        createDate: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
        applyDeptId: sessionStorage.getItem('deptId'),
        applyDeptName: sessionStorage.getItem('deptName'),
        userId: '',
        cooperationCompanyName: '',
        cooperationCompanyCode: '',
        cooperationPersonName: '',
        cooperationPersonSex: '',
        coopcompanyPersonIdcard: '',
        coopcompanyPersonTel: '',
        coopcompanyPersonEmail: '',
        insuranceAccount: '',
        insuranceAddress: '',
        workyear: '',
        technicalTitle: '',
        city: '',
        countys: '',
        duties: '',
        roleName: '',
        jobNature: '',
        peasantWorkerEnabled: '',
        mobileAuthNumber: '',
        cooperationPersonId: '',
        personIdcard:'',
        personTel:''
      },
      staticSearchParam: {},
      // 账号信息
      accountTableApi: accListService,
      accountColumns: [
        { label: '登录账号', prop: 'account' },
        { label: '登录密码', prop: 'pwd', formatter:row=>{
         if(row.pwd){
          return row.pwd.replace(/./g, "*")
          }
        } },
        { label: '允许登录', prop: 'enabled' },
        { label: '是否工程质检', prop: 'projectQualityEnable' }],
      // 角色信息（综合员权限）
      roleZYHColumns:[
        { label: '服务范围', prop: 'businessScopeName' },
        { label: '角色名称', prop: 'roleName' }
      ],
      roleZHYTableApi:roleListZHYService,
      roleTableApi: roleListService,
      roleColumns: [
        { label: '服务范围', prop: 'businessScopeName' },
        { label: '角色名称', prop: 'roleName' },
        { label: '角色状态', prop: 'roleStatus' }],
      // 劳动合同信息
      contractTableApi: laborContListService,
      // 资质信息
      certTableApi: certContListService,
      insuranceApi: insuranceListService,
      trainingApi: trainingListService,
      personnal: [], // 选择的人员信息
      dialogVisible: false,
      dialogType: '', // 区分保险和培训
      dialogConfig: [],
      dialogPosition: 'top',
      dialogDisableForm: false,
      dialogForm: {},
      dialogTitle: '',
      insuranceParam: {},
      trainingParam: {},
      boId: '',
      nodeName: '草稿',
      nodeCode: '',
      workflowCode: 'PartnerUser',
      completeTaskUrl: '',
      returnAddress: '/cooperation_company/personnel_info',
      disableBasicForm: false,
      cityList: [],
      countyList: [],
      deptId:sessionStorage.getItem('deptId')
    }
  },
  computed: {
    pageAnchorConfig() {
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '账号<br>信息',
          id: 'sectionZh'
        },
        {
          text: '资质<br>信息',
          id: 'sectionZz'
        },
        {
          text: '保险<br>信息',
          id: 'sectionBc',
          show: this.boId
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile',
          show: this.boId
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow',
          show: this.boId
        }
      ]
    },
    baseFormConfig() {
      return [
        {
          label: '合作单位名称',
          type: 'input',
          prop: 'cooperationCompanyName',
          disabled: true,
          span: 12
        }, {
          label: '合作单位编码',
          type: 'input',
          prop: 'cooperationCompanyCode',
          disabled: true,
          span: 12
        }, {
          label: '姓名',
          type: 'input',
          prop: 'cooperationPersonName',
          eventListeners: {
            focus: () => { this.openUserSelectDialog() }// 打开选择部门弹窗
          },
          rules: [
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12
        }, {
          label: '性别',
          type: 'select',
          prop: 'cooperationPersonSex',
          disabled: true,
          options: [
            { label: '男', value: '0' },
            { label: '女', value: '1' }
          ],
          span: 12
        }, {
          label: '身份证号',
          type: 'input',
          prop: 'personIdcard',
          disabled: true,
          span: 12
        }, {
          label: '手机号码',
          type: 'input',
          prop: 'personTel',
          disabled: true,
          span: 12
        }, {
          label: '联系邮箱',
          type: 'input',
          prop: 'coopcompanyPersonEmail',
          disabled: true,
          span: 12
        }, {
          label: '社保账号',
          type: 'input',
          prop: 'insuranceAccount',
          disabled: true,
          span: 12
        }, {
          label: '社保地址',
          type: 'input',
          prop: 'insuranceAddress',
          disabled: true,
          span: 12
        },
        {
          label: '工作年限',
          type: 'input',
          prop: 'serviceLength',
          disabled: true,
          span: 12
        }, {
          label: '职称',
          type: 'input',
          prop: 'technicalTitle',
          disabled: true,
          span: 12
        }, {
          label: '是否农民',
          type: 'input',
          prop: 'peasantWorkerEnabledName',
          disabled: true,
          span: 12
        },{
          label: '学历',
          type: 'input',
          prop: 'degree',
          span: 12
        }, {
          label: '用工性质',
          type: 'input',
          prop: 'jobNatureName',
          disabled: true,
          span: 12
        }, {
          label: '支付工资金额',
          type: 'input',
          prop: 'wagesAmount',
          disabled: true,
          span: 12
        },
        {
          label: '负责地市',
          type: 'select',
          prop: 'city',
          span: 12,
          options: this.cityList,
          eventListeners: {
            change: (val) => { this.handleCityChange(val) }// 打开选择部门弹窗
          }
        }, {
          label: '负责区县',
          type: 'select',
          prop: 'countys',
          span: 12,
          options: this.countyList,
          multiple: true
        }, {
          label: '职务',
          type: 'input',
          prop: 'duties',
          span: 12
        }, {
          label: '角色名称',
          type: 'input',
          mode:'textarea',
          prop: 'roleNames',
          span: 24,
          disabled: true
        }, {
          label: '移动认证编号',
          type: 'input',
          prop: 'mobileAuthNumber',
          span: 12
        }, {
          label: '编制人',
          type: 'input',
          prop: 'creatorName',
          span: 12,
          disabled: true
        },
        {
          label: '编制部门',
          type: 'input',
          prop: 'applyDeptName',
          span: 12,
          disabled: true
        },
        {
          label: '编制时间',
          type: 'input',
          prop: 'createDate',
          span: 12,
          disabled: true
        }
      ]
    },
    insuranceColumns() {
      return [
        { label: '投保险种', prop: 'insuranceCoverage' },
        { label: '保单号', prop: 'insuranceNumber' },
        { label: '保险公司名称', prop: 'insuranceCompany' },
        { label: '保险金额（万元）', prop: 'insuranceAmount' },
        { label: '保险起时间', prop: 'insurancePeriodSt' },
        { label: '保险止时间', prop: 'insurancePeriodEnd' },
        { label: '投保单位', prop: 'subscriberEmployer' },
        { label: '操作', formatter: row => {
          return (<span>
            {
              this.disableBasicForm ? ('') : (<span
                class='table_btn mr10'
                onClick={() => { this.acitonHandle('insurance', 'edit', row) }}
              >
                  编辑
              </span>)
            }

            <span class='table_btn' onClick={() => { this.acitonHandle('insurance', 'detail', row) }}>
                  查看
            </span>
          </span>)
        } }
      ]
    },
    trainingColumns() {
      return [
        { label: '培训时间', prop: 'trainTime' },
        { label: '培训内容', prop: 'trainContent' },
        { label: '培训考核结果', prop: 'trainEvalResult' },
        { label: '是否通过', prop: 'trainPassFlag' },
        { label: '操作', formatter: row => {
          return (<span>
            { this.disableBasicForm ? ('') : (
              <span
                class='table_btn mr10'
                onClick={() => { this.acitonHandle('training', 'edit', row) }}
              >
                  编辑
              </span>
            ) }
            <span class='table_btn' onClick={() => { this.acitonHandle('training', 'detail', row) }}>
                  查看
            </span>
          </span>)
        } }
      ]
    },
    qualificationColumns(){
      return [
        { label: '证书分类', prop: 'certTypeName' },
        { label: '证书编号', prop: 'certCode' },
        { label: '证书有效时间', formatter: row => {
          if(row.certPeriodEnd){
            return this.$moment(row.certPeriodEnd).format('yyyy-MM-DD')
          }
        } },
        { label: '证书文件', prop: 'certFileName',minWidth:'120px', formatter: (row, column, cellValue, index) => {
          if(row.fileIds){
            return  <mssFileTemplate ref={'fileList'+index} customParams={{ids:row.fileIds}}></mssFileTemplate>
          }
        } },
        {
          label:'操作',
          formatter:(row, column, cellValue, index)=>{
            if(row.fileIds){
              return <span  class='table_btn mr10' onClick={()=>{this.downLoadCert(row,index)}}>批量下载证书</span>
            }
          }
        }
      ]
    },
    contractColumns(){
      return [
        { label: '合同扫描件', prop: 'scanning',
        formatter: (row, column, cellValue, index) => {
          if(row.laborContFileIds){
            return  <mssFileTemplate showCheckBox={false} ref={'fileList'+index} customParams={{ids:row.laborContFileIds}}></mssFileTemplate>
          }else{
            return row.scanning
          }
        }
       },
        { label: '合同有效期', prop:'laborPeriodEnd', formatter: row => {
          if(row.laborPeriodEnd){
            return this.$moment(row.laborPeriodEnd).format('yyyy-MM-DD')
          }
        } },
        { label: '社保缴纳记录', prop: 'socialSecurityRecord' , formatter: (row, column, cellValue, index) => {
          if(row.socialCertFileIds){
            return  <mssFileTemplate showCheckBox={false} ref={'fileList'+index} customParams={{ids:row.socialCertFileIds}}></mssFileTemplate>
          }
        }}]
    }
  },
  created() {
    this.id = this.$route.query.boId
    this.type = this.$route.query.type
    this.getAreaList('city', { parentId: '-2', typeCode: 'area' })
    this.boId = this.id
    if (this.id) {
      this.getInfo()
    }
  },
  methods: {
    handleCityChange(val) {
      this.$set(this.baseFormModel, 'countys', '')
      this.$set(this.$refs.baseForm.modelForm, 'countys', '')
      this.getAreaList('county', { parentId: val, typeCode: 'area' })
    },
    // 下载资质证书
    downLoadCert(row,index) {
      this.$refs[`fileList${index}`].getFilesBatchDownload();
    },
    getAreaList(type, params) {
      queryAreaListService(params).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          if (type === 'city') {
            this.cityList = list
          } else {
            this.countyList = list
          }
        }
      })
    },
    // 详情
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.baseFormModel = { ...this.baseFormModel, ...res.data }
          this.baseFormModel.cooperationCompanyName = res.data.cooperationCompanyItemDto.cooperationcompanyname
          this.baseFormModel.cooperationCompanyCode = res.data.cooperationCompanyItemDto.cooperationcompanycode
          this.baseFormModel.cooperationPersonName = res.data.cooperationCompanyItemDto.cooperationpersonname
          this.baseFormModel.cooperationPersonSex = res.data.cooperationCompanyItemDto.cooperationpersonsex
          this.baseFormModel.coopcompanyPersonIdcard = res.data.cooperationCompanyItemDto.coopcompanypersonidcard
          this.baseFormModel.coopcompanyPersonTel = res.data.cooperationCompanyItemDto.coopcompanypersontel
          this.baseFormModel.coopcompanyPersonEmail = res.data.cooperationCompanyItemDto.coopcompanypersonemail
          this.baseFormModel.cooperationPersonId = res.data.cooperationCompanyItemDto.cooperationpersonid
          this.baseFormModel.technicalTitle = res.data.technicalTitle||''
          // 身份证号和手机号脱敏
          if(this.baseFormModel.coopcompanyPersonIdcard){
            this.baseFormModel.personIdcard = this.baseFormModel.coopcompanyPersonIdcard.replace(/^(.{3})(?:\d+)(.{3})$/, "$1************$2")
          }
          if(this.baseFormModel.coopcompanyPersonTel){
            this.baseFormModel.personTel = this.baseFormModel.coopcompanyPersonTel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
          }
          // 人员未入库字段
          // this.baseFormModel.insuranceAccount = res.data.cooperationCompanyItemDto.insuranceaccount||''
          // this.baseFormModel.insuranceAddress = res.data.cooperationCompanyItemDto.insuranceaddress||''
          // this.baseFormModel.serviceLength = res.data.cooperationCompanyItemDto.servicelength||''
          // this.baseFormModel.peasantWorkerEnabled = res.data.cooperationCompanyItemDto.peasantworkerenabled||''
          // this.baseFormModel.jobNature = res.data.cooperationCompanyItemDto.jobnature||''
          // this.baseFormModel.wagesAmount = res.data.cooperationCompanyItemDto.wagesamount||''
          // this.baseFormModel.roleNames = res.data.roleNames
          // this.baseFormModel.mobileauthNumber = res.data.cooperationCompanyItemDto.mobileauthnumber||''

          this.insuranceParam = {
            cooperationPersonId: this.baseFormModel.cooperationPersonId,
            partnerUserId: res.data.id
          }
          this.trainingParam = {
            cooperationPersonId: this.baseFormModel.cooperationPersonId,
            partnerUserId: res.data.id
          }
          this.staticSearchParam = {
            coopcompanyPersonTel: this.baseFormModel.coopcompanyPersonTel,
            coopcompanyPersonIdcard: this.baseFormModel.coopcompanyPersonIdcard
          }
          this.completeTaskUrl = res.data.completeTaskUrl
          if (this.baseFormModel.countys) {
            // 处理区县
            this.baseFormModel.countys = this.baseFormModel.countys.split(',')
          }

          if (this.baseFormModel.city) {
            this.getAreaList('county', { parentId: this.baseFormModel.city, typeCode: 'area' })// 获取地市
          }
          this.$refs.workFlow.init()
        }
      })
    },
    // 打开选人弹框
    openUserSelectDialog() {
      const excuterIds = this.baseFormModel.userId || ''
      const excuterNames = this.baseFormModel.cooperationPersonName || ''
      if (excuterIds) {
        this.personnal = {
          excuterIds,
          excuterNames
        }
      }
      // const deptParams = {	conditionType: 'p_cooperation', rootId: '-1', orgChildId: '-1' }
      const deptParams = { rootId:this.deptId }
      const personParams = { orgChildId:this.deptId }
      this.$refs.chooseUser.init(this.personnal, deptParams,personParams)
    },
    showCheckList({ checkList }) {
      // 根据用户id查询人员信息
      getPersonByUserIdService(checkList[0].userId).then(res => {
        if (res.code === '0000' && res.data.cooperationCompanyItemDto) {
          this.baseFormModel = { ...this.baseFormModel, ...res.data }
          this.baseFormModel.userId = checkList[0].userId
          this.baseFormModel.cooperationCompanyName = res.data.cooperationCompanyItemDto.cooperationcompanyname
          this.baseFormModel.cooperationCompanyCode = res.data.cooperationCompanyItemDto.cooperationcompanycode
          this.baseFormModel.cooperationPersonName = res.data.cooperationCompanyItemDto.cooperationpersonname
          this.baseFormModel.cooperationPersonSex = res.data.cooperationCompanyItemDto.cooperationpersonsex
          this.baseFormModel.coopcompanyPersonIdcard = res.data.cooperationCompanyItemDto.coopcompanypersonidcard
          this.baseFormModel.coopcompanyPersonTel = res.data.cooperationCompanyItemDto.coopcompanypersontel
          this.baseFormModel.coopcompanyPersonEmail = res.data.cooperationCompanyItemDto.coopcompanypersonemail
          this.baseFormModel.cooperationPersonId = res.data.cooperationCompanyItemDto.cooperationpersonid
          this.baseFormModel.technicalTitle = res.data.technicalTitle||''

          // 身份证号和手机号脱敏
          if(this.baseFormModel.coopcompanyPersonIdcard){
            this.baseFormModel.personIdcard = this.baseFormModel.coopcompanyPersonIdcard.replace(/^(.{3})(?:\d+)(.{3})$/, "$1************$2")
          }
          if(this.baseFormModel.coopcompanyPersonTel){
            this.baseFormModel.personTel = this.baseFormModel.coopcompanyPersonTel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
          }
          // 人员未入库字段
          // this.baseFormModel.insuranceAccount = res.data.cooperationCompanyItemDto.insuranceaccount
          // this.baseFormModel.insuranceAddress = res.data.cooperationCompanyItemDto.insuranceaddress
          // this.baseFormModel.serviceLength = res.data.cooperationCompanyItemDto.servicelength
          // this.baseFormModel.peasantWorkerEnabled = res.data.cooperationCompanyItemDto.peasantworkerenabled
          // this.baseFormModel.jobNature = res.data.cooperationCompanyItemDto.jobnature
          // this.baseFormModel.wagesAmount = res.data.cooperationCompanyItemDto.wagesamount
          // this.baseFormModel.roleNames = res.data.roleNames
          // this.baseFormModel.mobileauthNumber = res.data.cooperationCompanyItemDto.mobileauthnumber

          this.staticSearchParam = {
            coopcompanyPersonTel: this.baseFormModel.coopcompanyPersonTel,
            coopcompanyPersonIdcard: this.baseFormModel.coopcompanyPersonIdcard
          }
          this.insuranceParam = {
            cooperationPersonId: this.baseFormModel.cooperationPersonId
          }
          this.trainingParam = {
            cooperationPersonId: this.baseFormModel.cooperationPersonId
          }
          if (res.data.countys) {
            this.baseFormModel.countys = res.data.countys.split(',')
          }
          if (this.baseFormModel.city) {
            this.getAreaList('county', { parentId: this.baseFormModel.city, typeCode: 'area' })// 获取地市
          }
          // 修改单位时刷新固定表格
          if (this.$refs.accountTable) {
            this.$refs.accountTable.getTableData(this.staticSearchParam)
            this.$refs.roleTable.getTableData(this.staticSearchParam)
            this.$refs.roleZHYTable.getTableData(this.staticSearchParam)
            this.$refs.contractTable.getTableData(this.staticSearchParam)
            this.$refs.certTable.getTableData(this.staticSearchParam)
          }
        } else {
          this.$message.warning('暂无该人员信息')
        }
      })
    },
    // 新增
    addTable(type) {
      this.dialogType = type
      this.dialogVisible = true
      this.dialogDisableForm = false
      if (type === 'insurance') {
        this.dialogTitle = '保险信息'
      } else {
        this.dialogTitle = '培训信息'
      }
      for (const key in this.dialogForm) {
        this.dialogForm[key] = ''
      }
      this.generateFormConfig()
    },
    // 删除
    delTable(type) {
      this.dialogType = type
      const selection = this.$refs[type + 'Table'].multipleSelection
      if (selection.length) {
        const ids = []
        selection.forEach(item => {
          ids.push(item.id)
        })
        this.$confirm('是否确认删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          (type === 'insurance' ? insuranceBatchremoveService : trainingBatchremoveService)({ ids: ids.join(',') }).then(res => {
            if (res.code === '0000') {
              this.$message({
                message: '删除成功！',
                type: 'success'
              })
              this.searchList()
            }
          })
        }).catch(() => {})
      } else {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
    },
    async generateFormConfig() {
      if (this.dialogType === 'insurance') {
        this.dialogConfig = [
          {
            label: '投保险种',
            type: 'select',
            prop: 'insuranceCoverage',
            span: 12,
            options: []
          },
          {
            label: '保单号',
            type: 'input',
            prop: 'insuranceNumber',
            span: 12
          },
          {
            label: '保险公司名称',
            type: 'input',
            prop: 'insuranceCompany',
            span: 12
          },
          {
            label: '保险金额（万元）',
            type: 'input',
            prop: 'insuranceAmount',
            span: 12
          },
          {
            label: '保险金额起时间',
            type: 'datePicker',
            prop: 'insurancePeriodSt',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            dateType: 'datetime',
            span: 12,
            pickerOptions: this.insurancePeriodStPickerOptions
          },
          {
            label: '保险金额至时间',
            type: 'datePicker',
            prop: 'insurancePeriodEnd',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            dateType: 'datetime',
            span: 12,
            pickerOptions: this.insurancePeriodEndPickerOptions
          },
          {
            label: '投保单位',
            type: 'input',
            prop: 'subscriberEmployer',
            span: 12
          }
        ]
        this.$set(this.dialogConfig[0], 'options', await this.$dictOptions({ parentValue: '001001008', appCode: '001001' }))
      } else {
        this.dialogConfig = [
          {
            label: '培训时间',
            type: 'datePicker',
            prop: 'trainTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            dateType: 'datetime',
            span: 12
          },
          {
            label: '培训内容',
            type: 'input',
            prop: 'trainContent',
            span: 12
          },
          {
            label: '培训考核结果',
            type: 'input',
            prop: 'trainEvalResult',
            span: 12
          },
          {
            label: '是否通过',
            type: 'select',
            prop: 'trainPassFlag',
            span: 12,
            options: [
              { label: '是', value: '是' },
              { label: '否', value: '否' }
            ]
          }
        ]
      }
    },
    importInsuranceFile(params) {
      this.dialogType = 'insurance'
      const param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.id)
      insuranceImportExcelService(param).then((res) => {
        // if (res.data) {
        //   this.$message.warning(res.data)
        // } else
        if (res.code === '0000') {
          this.$message.success('导入成功')
          this.searchList()
        }
      })
    },
    exportInsurance() {
      commonDown(this.insuranceParam, insuranceExportExcelService)
    },
    importTrainingFile(params) {
      this.dialogType = 'training'
      const param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.id)
      trainingImportExcelService(param).then((res) => {
        // if (res.data) {
        //   this.$message.warning(res.data)
        // } else
        if (res.code === '0000') {
          this.$message.success('导入成功')
          this.searchList()
        }
      })
    },
    exportTraining() {
      commonDown(this.trainingParam, trainingExportExcelService)
    },
    // 保险信息和培训信息--修改和查看
    acitonHandle(type, action, row) {
      this.addTable(type)
      if (action === 'detail') {
        this.dialogDisableForm = true
      } else {
        this.dialogDisableForm = false
      }
      if (type === 'insurance') {
        insuranceDetailService(row.id).then(res => {
          if (res.code === '0000' && res.data) {
            this.dialogForm = res.data
            this.dialogVisible = true
          }
        })
      } else {
        trainingDetailService(row.id).then(res => {
          if (res.code === '0000' && res.data) {
            this.dialogForm = res.data
            this.dialogVisible = true
          }
        })
      }
    },
    cancalDialog() {
      this.dialogVisible = false
    },
    saveDialog() {
      const reqData = { ...this.$refs.dialogForm.modelForm, partnerUserId: this.id, cooperationPersonId: this.baseFormModel.cooperationPersonId }
      if (this.dialogType === 'insurance') {
        (reqData.id ? insuranceUpdateService : insuranceSaveService)(reqData).then(res => {
          if (res.code === '0000') {
            this.$message({ type: 'success', message: '保存成功！' })
            this.dialogVisible = false
            this.searchList()
          }
        })
      } else {
        (reqData.id ? trainingUpdateService : trainingSaveService)(reqData).then(res => {
          if (res.code === '0000') {
            this.$message({ type: 'success', message: '保存成功！' })
            this.dialogVisible = false
            this.searchList()
          }
        })
      }
    },
    // 查询信息列表
    searchList() {
      if (this.dialogType === 'insurance') {
        this.$refs.insuranceTable.page.current = 1
        this.$refs.insuranceTable.getTableData(this.insuranceParam)
      } else {
        this.$refs.trainingTable.page.current = 1
        this.$refs.trainingTable.getTableData(this.trainingParam)
      }
    },
    // 新增前校验
    async verifyWorkOrder() {
      let ableSave = true
      await notExistsProcessService(this.baseFormModel.userId).then(res => {
        if (res.code === '0000') {
          ableSave = res.data
        }
      })
      return ableSave
    },
    async saveInfo() {
      let ableSave = true
      let flag = false
      this.$refs.baseForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          flag = true
        } else {
          return false
        }
      })
      if (flag) {
        const reqData = {
          ...this.$refs.baseForm.modelForm,
          id: this.id
        }
        if (reqData.countys && reqData.countys.length > 0) {
          reqData.countys = reqData.countys.join(',')
        } else {
          reqData.countys = ''
        }
        // 新增校验
        if (!reqData.id) {
          ableSave = await this.verifyWorkOrder()
          if (!ableSave) {
            this.$message.warning(`${this.baseFormModel.cooperationPersonName}存在流程中工单，请先完成流转中的工单`)
            return
          }
        }
        (reqData.id ? updateInfoService : saveInfoService)(reqData).then(res => {
          if (res.code === '0000') {
            this.$message.success('保存成功')
            this.id = res.data.id
            this.boId = this.id
            this.completeTaskUrl = res.data.completeTaskUrl
            this.insuranceParam = {
              partnerUserId: this.id,
              cooperationPersonId: this.baseFormModel.cooperationPersonId
            }
            this.trainingParam = {
              partnerUserId: this.id,
              cooperationPersonId: this.baseFormModel.cooperationPersonId
            }
            if (this.$refs.insuranceTable) {
              this.$refs.insuranceTable.getTableData(this.insuranceParam)
              this.$refs.trainingTable.getTableData(this.trainingParam)
            }
            this.completeTaskUrl = res.data.completeTaskUrl
            this.$nextTick(() => {
              this.$refs.workFlow.init()
            })
          }
        })
      }
    },
    // 提交前保存表单信息
    save(cb) {
      const reqData = { ...this.$refs.baseForm.modelForm, id: this.id }
      if (reqData.countys && reqData.countys.length > 0) {
        reqData.countys = reqData.countys.join(',')
      } else {
        reqData.countys = ''
      }
      updateInfoService(reqData).then(res => {
        if (res.code === '0000') {
          if (cb) {
            cb()
          } else {
            this.$message.success('保存成功')
          }
        }
      })
    },
    // 提交审核
    submit() {
      // let flag = false
      // this.$refs.baseForm.$refs.form.validateScroll((valid) => {
      //   if (valid) {
      //     flag = true
      //   } else {
      //     return false
      //   }
      // })
      // if (flag) {
        this.$refs.workFlow.opendialogInitNextPath()
      // }
    },
    async beforeSubmit(){
      let str=""
      await this.$refs.baseForm.$refs.form.validateScroll((valid)=>{
        str = valid?'':'有必填信息未填写完整，请检查'
      })
      return str
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: `关于${this.$refs.baseForm.modelForm.cooperationPersonName}合作单位人员信息维护`
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode() {
      const fileNameArr = {
        hzdwzhy: { userName: 'admin', userId: '1' }
      }
      // 设置默认处理人
      return fileNameArr
    },
    getNodeData(data) {
      this.nodeName = data.nodeName || ''
      this.nodeCode = data.nodeCode || ''
      if (this.nodeCode === 'xz' || !this.nodeCode) {
        this.disableBasicForm = false
      } else {
        this.disableBasicForm = true
      }
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/personnel_info'
      })
    }
  }
}
</script>
<style lang="scss">
.cooperation-company{
  padding-right: 70px;
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
  .upload-btn {
    display: inline-block;
    margin: 0 10px;
  }
  .file-down{
    .el-input{
      width: 75%;
      margin-right: 10px;
    }
  }
}
</style>
