<!--
* @author: hcm
* @date: 2023-07-10
* @description: 合作单位黑白名单-列表
-->
<template>
  <div class="informationList">
    <mssSearchForm
      ref="searchForm"
      :search-config="formConfig"
      @search="search"
      @reset="reset"
    />
    <mssCard title="合作单位黑白名单信息列表">
      <div slot="headerBtn">
        <el-button @click="acitonHandle('add')">新增</el-button>
        <el-button type="primary" @click="exportList">导出</el-button>
      </div>
      <div slot="content">
        <mssTable ref="commonTable" :columns="tableHeader" :api="tableApi" :static-search-param="staticSearchParam" />
      </div>
    </mssCard>
  </div>
</template>

<script>
import { commonDown } from '@/utils/btn.js'
import { queryListService, exportPageService, removeIdsService } from '@/api/cooperation_company/company_balcklist_whitelist.js'
export default {
  name: 'CompanyBalcklistWhitelistList',
  data() {
    return {
      tableApi: queryListService,
      formConfig: [
        {
          label: '合作单位名称',
          type: 'input',
          fieldName: 'cooperationCompanyName'
        },
        {
          label: '合作单位类型',
          type: 'select',
          fieldName: 'cooperationCompanyType',
          options: [
            { label: '设计', value: '0' },
            { label: '监理', value: '1' },
            { label: '施工', value: '2' }
          ]
        },
        {
          label: '申请名单类型',
          type: 'select',
          fieldName: 'applyType',
          options: []
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'entityStatus'
        }
      ],
      tableHeader: [
        {
          prop: 'applyType',
          label: '申请加入名单类型',
          tooltip: true,
          minWidth: '130px'
        },
        {
          prop: 'cooperationCompanyName',
          label: '合作单位名称',
          tooltip: true,
          minWidth: '200px'
        },
        {
          prop: 'cooperationCompanyCode',
          label: '合作单位编码',
          tooltip: true,
          minWidth: '130px'
        },
        {
          prop: 'orgManager',
          label: '单位负责人',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'orgManagerMobile',
          label: '单位负责人电话',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'currentType',
          label: '当前归属名单类型',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'entityStatus',
          label: '状态',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'operate',
          label: '操作',
          tooltip: true,
          minWidth: '120px',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (<span
                  class='table_btn mr10'
                  onClick={() => { this.acitonHandle('edit', row) }}
                >
                  处理
                </span>) : ('')}
                <span class='table_btn mr10' onClick={() => { this.acitonHandle('detail', row) }}>
                  查看
                </span>
                {row.isAllowDelete ? (
                  <span
                    class='table_btn'
                    onClick={() => { this.delHandle(row) }}
                  >
                  删除
                  </span>
                ) : ('')}
              </span>
            )
          }
        }
      ],
      tableData: [],
      staticSearchParam: {}
    }
  },
  async created() {
    // this.$set(this.formConfig[1], 'options', await this.$dictOptions({ parentValue: '001001001', appCode: '001001' }))
    this.$set(this.formConfig[2], 'options', await this.$dictOptions({ parentValue: '001001010', appCode: '001001' }))
    this.$set(this.formConfig[3], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB' }))
  },
  methods: {
    exportList() {
      const params = {
        ...this.$refs.searchForm.searchForm,
        limit: this.$refs.commonTable.page.size,
        page: this.$refs.commonTable.page.current,
        exportType: 'all'
      }
      commonDown(params, exportPageService)
    },
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.commonTable.page.current = 1
      this.$refs.commonTable.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
    acitonHandle(type, row) {
      const id = row ? row.id : ''
      if (type === 'detail') {
        this.$router.push({
          path: '/cooperation_company/company_balcklist_whitelist/view',
          query: {
            boId: id
          }
        })
      } else {
        this.$router.push({
          path: '/cooperation_company/company_balcklist_whitelist/edit',
          query: {
            boId: id
          }
        })
      }
    },
    delHandle(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeIdsService({ ids: row.id }).then(res => {
          if (res.code === '0000') {
            this.$message({
              type: 'success',
              message: '删除成功！'
            })
            this.search(this.$refs.searchForm.searchForm)
          }
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
