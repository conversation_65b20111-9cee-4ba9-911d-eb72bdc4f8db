<!--
* @author: hcm
* @date: 2023-07-10
* @description: 合作单位黑白名单-编辑/审批
-->
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button v-if="boId" type="primary" @click="submit">提交审核</el-button>
      <el-button v-if="!disableForm" type="primary" @click="saveInfo">保存草稿</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="申请信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm"
          :disable-form="disableForm"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :bo-id="boId" :business-type="workflowCode" :node-name="nodeName"></mssAttachment>

    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig" />
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="!disableForm"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <!-- 部门弹框 -->
    <mssChooseDept ref="chooseDept" @showCheckList="showCheckList"></mssChooseDept>
  </div>
</template>

<script>
import {
  queryCompanyByDeptIdService,
  queryDetailService,
  saveInfoService,
  updateInfoService,
  notExistsProcessService
} from '@/api/cooperation_company/company_balcklist_whitelist.js'
export default {
  name: 'CompanyBalcklistWhitelistEdit',
  data() {
    return {
      disableForm: false,
      basicConfig: [
        {
          label: '合作单位名称',
          type: 'input',
          prop: 'cooperationCompanyName',
          span: 12,
          eventListeners: {
            focus: () => { this.openDeptSelectDialog() }// 打开选择部门弹窗
          },
          rules: { required: true, message: '请选择合作单位', trigger: 'blur,change' }
        },
        {
          label: '合作单位编码',
          type: 'input',
          prop: 'cooperationCompanyCode',
          span: 12
        },
        {
          label: '申请加入名单类型',
          type: 'radio',
          prop: 'applyType',
          span: 12,
          options: [],
          rules: { required: true, message: '请选择加入名单类型', trigger: 'blur' }
        },
        {
          label: '申请理由',
          type: 'input',
          mode: 'textarea',
          prop: 'applyReason',
          span: 24
        }
      ],
      basicForm: {
        creatorId: sessionStorage.getItem('userId'),
        creatorName: sessionStorage.getItem('realName'),
        createDate: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
        applyDeptId: sessionStorage.getItem('deptId'),
        applyDeptName: sessionStorage.getItem('deptName'),
        cooperationCompanyName: '',
        cooperationCompanyCode: '',
        cooperationCompanyType: ''
      },
      labelPosition: 'top',
      blackWhitelist: [],
      companyList: [],
      boId: '',
      workflowCode: 'PartnerDeptBlacklist',
      nodeName: '草稿',
      completeTaskUrl: '',
      returnAddress: '/cooperation_company/company_balcklist_whitelist',
      nodeCode: ''
    }
  },
  computed: {
    pageAnchorConfig() {
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile',
          show: this.boId
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow',
          show: this.boId
        }]
    }
  },
  async created() {
    this.blackWhitelist = await this.$dictOptions({ parentValue: '*********', appCode: '001001' })
    this.$set(this.basicConfig[2], 'options', this.blackWhitelist)
    this.id = this.$route.query.boId
    this.boId = this.id
    if (this.id) {
      this.getInfo()
    }
  },
  methods: {
    // 限制黑白名单选择
    ableToList(val) {
      switch (val) {
        case '*********001':
          this.blackWhitelist = this.blackWhitelist.map(item => {
            if (item.value === '*********001') {
              return { ...item, disabled: true }
            } else {
              return item
            }
          })
          this.$set(this.basicConfig[2], 'options', this.blackWhitelist)
          break
        case '*********002':
          this.blackWhitelist = this.blackWhitelist.map(item => {
            if (item.value === '*********002') {
              return { ...item, disabled: true }
            } else {
              return item
            }
          })
          this.$set(this.basicConfig[2], 'options', this.blackWhitelist)
          break
        case '*********003':
          this.blackWhitelist = this.blackWhitelist.map(item => {
            return { ...item, disabled: true }
          })
          this.$set(this.basicConfig[2], 'options', this.blackWhitelist)
          break
      }
    },
    // 详情
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.basicForm = res.data
          this.ableToList(this.basicForm.currentType)
          this.$refs.workFlow.init()
          this.completeTaskUrl = res.data.completeTaskUrl
          this.companyList=[{
            text: res.data.cooperationCompanyName||'',
            id:res.data.deptId||'',
          }]
        }
      })
    },
    openDeptSelectDialog() {
      const deptParams = { conditionType: 'p_cooperation' }
      this.$refs.chooseDept.init(this.companyList, deptParams)
    },
    showCheckList({ checkList }) {
      this.companyList = checkList
      // 根据合作单位id查询合作单位信息
      queryCompanyByDeptIdService(checkList[0].id).then(res => {
        if (res.code === '0000' && res.data.cooperationCompanyDto) {
          const currentType = res.data.currentType ? res.data.currentType : '*********001'
          this.ableToList(currentType)
          this.basicForm['currentType'] = res.data.currentType || ''
          this.basicForm['cooperationCompanyName'] = res.data.cooperationCompanyDto.cooperationcompanyname || ''
          this.basicForm['cooperationCompanyCode'] = res.data.cooperationCompanyDto.cooperationcompanycode || ''
          this.basicForm['cooperationCompanyType'] = res.data.cooperationCompanyDto.cooperationcompanytype || ''
          this.basicForm['cooperationCompanyId'] = res.data.cooperationCompanyDto.cooperationcompanyid || ''
        }else{
          this.$message.warning('暂无该公司信息！')
        }
      })
      this.basicForm['deptId'] = this.companyList[0].id
    },
    // 新增前校验
    async verifyWorkOrder() {
      let ableSave = true
      await notExistsProcessService(this.basicForm['deptId']).then(res => {
        if (res.code === '0000') {
          ableSave = res.data
        }
      })
      return ableSave
    },
    async saveInfo() {
      let ableSave = true
      let flag = false
      this.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          flag = true
        } else {
          return false
        }
      })
      if (flag) {
        const reqData = { ...this.$refs.basicForm.modelForm, id: this.id }
        // 新增校验
        if (!reqData.id) {
          ableSave = await this.verifyWorkOrder()
          if (!ableSave) {
            this.$message.warning(`${this.basicForm.cooperationCompanyName}存在流程中工单，请先完成流转中的工单`)
            return
          }
        }
        (reqData.id ? updateInfoService : saveInfoService)(reqData).then(res => {
          if (res.code === '0000') {
            this.$message.success('保存成功')
            const currentType = res.data.currentType ? res.data.currentType : '*********001'
            this.ableToList(currentType)
            this.id = res.data.id
            this.boId = this.id
            this.completeTaskUrl = res.data.completeTaskUrl
            this.$nextTick(() => {
              this.$refs.workFlow.init()
            })
          }
        })
      }
    },
    // 提交前保存表单信息
    save(cb) {
      const reqData = { ...this.$refs.basicForm.modelForm, id: this.id }
      updateInfoService(reqData).then(res => {
        if (res.code === '0000') {
          if (cb) {
            cb()
          } else {
            this.$message.success('保存成功')
          }
        }
      })
    },
    // 提交审核
    submit() {
      this.$refs.workFlow.opendialogInitNextPath()
    },
    async beforeSubmit(){
      let str=""
      await this.$refs.basicForm.$refs.form.validate((valid)=>{
        str = valid?'':'有必填信息未填写完整，请检查'
      })
      return str
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: `关于${this.$refs.basicForm.modelForm.cooperationCompanyName}合作单位黑白名单管理`
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode() {
      const fileNameArr = {
        hzdwzhy: { userName: 'admin', userId: '1' }
      }
      // 设置默认处理人
      return fileNameArr
    },
    getNodeData(data) {
      this.nodeName = data.nodeName || ''
      this.nodeCode = data.nodeCode || ''
      if (this.nodeCode === 'dshzgly' || !this.nodeCode) {
        this.disableForm = false
      } else {
        this.disableForm = true
      }
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/company_balcklist_whitelist'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cooperation-company{
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
