<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-13 17:21:58
 * @Description: 合作单位考核月度得分-详情
-->

<template>
	<div class="monthlyscoreDetail">
		<mssSearchForm 
			:searchConfig="searchConfig" 
			ref="SearchForm" 
			@search="search" 
			@reset="reset"
			@openDialog="openDialog">
		</mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable border ref="table" :columns="columns" :staticSearchParam="staticSearchParam" :api="tableApi" />
			</div>
		</mssCard>
		<mssChooseSpecialty ref="chooseSpecialty" :mult-select="false" @showCheckList="showSpecialty"></mssChooseSpecialty>
	</div>
</template>

<script>
import {
	queryMonthlyScoreDetaiService,
	exportMSDetailExcelService
} from '@/api/cooperation_company/monthly_score'
import { commonDown } from "@/utils/btn";
import { queryAreaListService } from "@/api/common_api"
export default {
	name: 'monthlyscoreDetail',
	data() {
		return {
			searchConfig: [
				{
					type: 'input',
					fieldName: 'cooperationCompanyName',
					label: '合作单位名称'
				},
				{
					type: 'select',
					fieldName: 'cooperationCompanyType',
					label: '合作单位类型',
					options: []
				},
				{
					type: 'input',
					fieldName: 'frameContName',
					label: '框架合同名称'
				},
				{
					type: 'input',
					fieldName: 'frameContCode',
					label: '框架合同编号'
				},
				{
					type: 'select',
					fieldName: 'evalCity',
					label: '地市',
					options: []
				},
				{
					type: 'dialog',
					fieldName: 'evalSpecialtyName',
					label: '打分专业',
				},
				{
					type: 'number',
					fieldName: 'evalScoreBegin',
					label: '打分从'
				},
				{
					type: 'number',
					fieldName: 'evalScoreEnd',
					label: '打分至'
				},
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: queryMonthlyScoreDetaiService,
			loadParams: {},
			columns:
				[
					{
						prop: "evalMonth",
						label: "月份",
						minWidth: 120,
					},
					{
						prop: "evalCity",
						label: "地市",
						minWidth: 120,
					},
					{
						prop: "cooperationCompanyName",
						label: "合作单位名称",
						minWidth: 160,
						tooltip: true
					},
					{
						prop: "cooperationCompanyType",
						label: "合作单位类型",
						minWidth: 160,
					},
					{
						prop: "frameContName",
						label: "框架合同名称",
						minWidth: 180,
						tooltip: true
					},
					{
						prop: "frameContCode",
						label: "框架合同编号",
						minWidth: 160,
						tooltip: true
					},
					{
						prop: "useEvalScore",
						label: "使用人员评分（70%）",
						minWidth: 160
					},
					{
						prop: "managerEvalScore",
						label: "管理人员评分（30%）",
						minWidth: 160
					},
					{
						prop: "projectImplEvalScore",
						label: "工程实施部门",
						minWidth: 160
					},
					{
						prop: "networkEvalScore",
						label: "网络维护部门",
						minWidth: 160
					},
					{
						prop: "auditEvalScore",
						label: "内审部",
						minWidth: 140,
					},
					{
						prop: "evalScore",
						label: "月度考评得分",
						minWidth: 120,
					}
				],
			specailty: []
		}
	},
	async created() {
		this.$set(this.searchConfig[1], 'options', await this.$dictOptions({ parentValue: "001001001", appCode: "001001" }))
	},
	methods: {
		getAreaList() {
			queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
				if (res.code === '0000') {
				const list = []
				res.data.forEach(item => {
					list.push({ label: item.name, value: item.id })
				})
				this.$set(this.searchConfig[4], 'options', list)
				}
			})
    	},
		search(searchData) {
			this.$refs.table.page.current = 1
			this.loadParams = JSON.parse(JSON.stringify(searchData))
			this.$refs.table.getTableData(searchData)
		},
		reset(searchData) {
			searchData.evalSpecialty && (searchData.evalSpecialty = '')
			this.$refs.table.page.current = 1
			this.loadParams = JSON.parse(JSON.stringify(searchData))
			this.$refs.table.getTableData(searchData)
		},
		exportMethod() {
			commonDown({ boId: '', ...this.loadParams }, exportMSDetailExcelService);
		},
		openDialog(){
			const item = this.specailty
			this.$refs.chooseSpecialty.init(item);
		},
		showSpecialty({ checkList }){
			const list = checkList
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.name)
        ids.push(item.id)
      })
      this.$set(this.$refs.searchForm.searchForm, 'evalSpecialty', ids.join(','))
			this.$set(this.$refs.searchForm.searchForm, 'evalSpecialtyName', names.join(','))
		},
	}
}
</script>