/**
* @author: ty
* @date: 2023-12-08
* @description:
*/
<template>
  <div class="scorePerSettings">
    <div class="operate-btn">
      <el-button type="primary" @click="saveInfo">保存</el-button>
      <el-button @click="back">返回</el-button>
    </div>
    <mssCard :title="'合作单位评分权重配置'">
      <div slot="content">
        <mss-table
          :columns="columns"
          :stationary="tableData"
          :pagination="false"
        />
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  getWeightInfoService,
  saveWeightInfoService
} from '@/api/cooperation_company/monthly_score'
export default {
  name: 'ScorePerSettings',
  data() {
    return {
      columns: [
        {
          label: '评分项',
          prop: 'contractType',
          formatter: row => {
            return (
              <div>
                <span>{row.contractType}</span>
                <div>（可以填写，并且影响后期）</div>
              </div>
            )
          }
        },
        {
          label: '使用人员评分（一线直评）',
          prop: 'useRate',
          formatter: row => {
            return <el-input type='number' min='0' max='100' v-model={row.useRate}></el-input>
          }
        },
        {
          label: '管理人员评分（专业考评）',
          prop: 'managerRate',
          formatter: row => {
            return <el-input type='number' min='0' max='100' v-model={row.managerRate}></el-input>
          }
        },
        {
          label: '管理人员评分-工程实施部门',
          prop: 'projectRate',
          formatter: row => {
            return <el-input type='number' min='0' max='100' v-model={row.projectRate}></el-input>
          }
        },
        {
          label: '管理人员评分-维护部门',
          prop: 'whRate',
          formatter: row => {
            return <el-input type='number' min='0' max='100' v-model={row.whRate}></el-input>
          }
        },
        {
          label: '管理人员评分-审计部门',
          prop: 'sjRate',
          formatter: row => {
            return <el-input type='number' min='0' max='100' v-model={row.sjRate}></el-input>
          }
        }
      ],
      tableData: [
      ]
    }
  },
  async created() {
    this.getInfo()
  },
  methods: {
    async getInfo() {
      const res = await getWeightInfoService()
      if (res.code === '0000') {
        this.tableData = res.data
      }
    },
    // 检查使用人员评分+管理人员评分的总和是否为100
    evalValidate() {
      let flag = true
      try {
        // 检查每一行数据的使用人员评分、管理人员评分是否没有填,没有填的话需要提示不能为空
        this.tableData.forEach(item => {
          if (item.useRate === null || item.useRate === undefined) {
            throw new Error('使用人员评分不能为空')
          }else if(item.managerRate === null || item.managerRate === undefined){
            throw new Error('管理人员评分不能为空')
          }else if(Number(item.useRate) < 0){
            throw new Error('使用人员评分不能小于0')
          }else if(Number(item.managerRate) < 0){
            throw new Error('管理人员评分不能小于0')
          }
        })
      } catch (error) {
        this.$message.warning(error.message)
        flag = false
      }

      if(flag){
        try{
          this.tableData.forEach(item => {
            if (Number(item.useRate) + Number(item.managerRate) !== 100) {
              throw new Error('使用人员评分+管理人员评分总和必须为100')
            }
          })
        }catch (error) {
          this.$message.warning(error.message)
          flag = false
        }
      }

      return flag
    },
    // 检查管理人员评分的3个子项的总和是否为100
    managerRateChildrenValidate() {
      let flag = true
      try {
        // 检查每一行数据的管理人员评分的3个子项是否没有填,没有填的话需要提示不能为空
        this.tableData.forEach(item => {
          if (item.projectRate === null || item.projectRate === undefined) {
            throw new Error('管理人员评分-工程实施部门不能为空')
          }else if(item.whRate === null || item.whRate === undefined){
            throw new Error('管理人员评分-维护部门不能为空')
          }else if(item.sjRate === null || item.sjRate === undefined){
            throw new Error('管理人员评分-审计部门不能为空')
          }else if(Number(item.projectRate) < 0){
            throw new Error('管理人员评分-工程实施部门不能小于0')
          }else if(Number(item.whRate) < 0){
            throw new Error('管理人员评分-维护部门不能小于0')
          }else if(Number(item.sjRate) < 0){
            throw new Error('管理人员评分-审计部门不能小于0')
          }
        })
      } catch (error) {
        this.$message.warning(error.message)
        flag = false
      }

      if(flag){
        try{
          this.tableData.forEach(item => {
            if (Number(item.projectRate) + Number(item.whRate) + Number(item.sjRate) !== 100) {
              throw new Error('管理人员评分-工程实施部门、维护部门、审计部门的总和必须为100')
            }
          })
        }catch (error) {
          this.$message.warning(error.message)
          flag = false
        }
      }

      return flag
    },
    saveInfo() {
      // 检查评分项是否填写，总和是否正确
      const validated1 = this.evalValidate()
      const validated2 = this.managerRateChildrenValidate()

      if(validated1 && validated2){
        const reqData = JSON.parse(JSON.stringify(this.tableData))
        saveWeightInfoService(reqData)
        .then(res => {
          if (res.code === '0000') {
            this.$message({
              type: 'success',
              message: '保存成功！'
            })
            this.back()
          }
        })
      }
    },
    back() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/monthly_score/good'
      })
    }
  }
}
</script>
