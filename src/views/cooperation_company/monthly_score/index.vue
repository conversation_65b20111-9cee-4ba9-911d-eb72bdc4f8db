<!-- 合作单位考核月度得分、合作单位考核月度扣分理由明细
（根据路由区分）-->
<template>
	<div class="monthlyscore">
		<mssSearchForm :searchConfig="searchConfig" ref="searchForm" @search="search" @reset="reset"
			:form="formData"
			@openDialog="openDialog"
      @changeSelect="changeSelectHandle">
		</mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
				<el-button type="primary" @click="scorePerSettingHandle">评分权重配置</el-button>
			</div>
			<div slot="content">
				<mssTable ref="table" border :columns="columns" :staticSearchParam="staticSearchParam" :api="tableApi" />
			</div>
		</mssCard>
		<mssChooseSpecialty ref="chooseSpecialty" :mult-select="false" @showCheckList="showSpecialty"></mssChooseSpecialty>
		<mssChooseDept ref="chooseDept" :multSelect="true" @showCheckList="showDeptList"></mssChooseDept>
	</div>
</template>

<script>
import {
	queryListService,
	exportMonthlyExcelService,
	queryDeductionListService,
	exportDeductionExcelService
} from '@/api/cooperation_company/monthly_score'
import { commonDown } from "@/utils/btn";
import { queryAreaListService } from "@/api/common_api"
import moment from 'moment';
export default {
	name: 'MonthlyScore',
	data() {
		return {
			searchArr: [
				{
					type: 'input',
					fieldName: 'cooperationCompanyName',
					label: '合作单位名称'
				},
				{
					type: 'select',
					fieldName: 'cooperationCompanyTypeEn',
					label: '合作单位类型',
					options: []
				},
				{
					type: 'input',
					fieldName: 'frameContName',
					label: '框架合同名称',
					tooltip: true
				},
				{
					type: 'input',
					fieldName: 'frameContCode',
					label: '框架合同编号'
				},
				{
					type: 'select',
					fieldName: 'evalCity',
					label: '地市',
					options: []
				},
				// {
				// 	fieldName: 'evalSpecialty',
				// 	label: '打分专业',
        //   type: 'select',
        //   options: []
        // },
				{
					type: 'number',
					fieldName: 'evalScoreBegin',
					label: '打分从'
				},
				{
					type: 'number',
					fieldName: 'evalScoreEnd',
					label: '打分至'
				},
				{
					type: 'date1',
					fieldName: 'evalYear',
					dateType: 'year',
					label: '考核年',
					format: 'yyyy',
					valueFormat: 'yyyy'
				},
				{
					type: 'date1',
					fieldName: 'evalMonth',
					dateType: 'month',
					label: '考核月',
					format: 'MM',
					valueFormat: 'MM'
				}
			],
			formData: {
				evalYear: moment().format('yyyy'),
				evalMonth: moment().format('MM'),
			},
			searchConfig: [],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: queryListService,
			columns: [],
			columns1:
				[
					{
						prop: "evalMonth",
						label: "月度",
						minWidth: 120,
					},
					{
						prop: "evalCity",
						label: "地市",
						minWidth: 120,
					},
					{
						prop: "cooperationCompanyName",
						label: "合作单位名称",
						tooltip: true,
						minWidth: 160
					},
					{
						prop: "cooperationCompanyType",
						label: "合作单位类型",
						minWidth: 160,
					},
					{
						prop: "frameContPurType",
						label: "框架合同采集分类",
						width: 120,
					},
					{
						prop: "frameContName",
						label: "框架合同名称",
						minWidth: 180,
						tooltip: true
					},
					{
						prop: "frameContCode",
						label: "框架合同编号",
						minWidth: 160,
					},
          {
            prop: 'useEvalScore',
            label: '使用人员评分',
            width: 100
          },
          {
            prop: 'managerEvalScore',
            label: '管理人员评分',
            width: 100
          },
          {
            prop: 'projectImplEvalScore',
            label: '管理人员评分-工程实施部门',
            width: 100
          },
          {
            prop: 'networkEvalScore',
            label: '管理人员评分-维护部门',
            width: 100
          },
          {
            prop: 'auditEvalScore',
            label: '管理人员评分-审计部门',
            width: 100
          },
					{
						prop: "evalScore",
						label: "评分数",
            width: 80
          }
				],
			columns2:
				[
					{
						prop: "evalMonth",
						label: "月度",
						minWidth: 120,
					},
					{
						prop: "evalCity",
						label: "地市",
						minWidth: 120,
					},
					{
						prop: "cooperationCompanyName",
						label: "合作单位名称",
						minWidth: 160,
						tooltip: true
					},
					{
						prop: "cooperationCompanyType",
						label: "合作单位类型",
						minWidth: 160,
					},
					{
						prop: "frameContName",
						label: "框架合同名称",
						minWidth: 180,
						tooltip: true
					},
					{
						prop: "frameContCode",
						label: "框架合同编号",
						minWidth: 160,
					},
					{
						prop: "evalDept",
						label: "评分部门",
						minWidth: 140,
					},
					{
						prop: "evalUser",
						label: "评分人",
						minWidth: 120
					},
					{
						prop: "evalCol3Content",
						label: "扣分项",
						minWidth: 180,
						tooltip: true
					},
					{
						prop: "deductionReason",
						label: "扣分理由",
						minWidth: 160,
						tooltip: true
					}
				],
			specailty: [],
			dept: []
		}
	},
	watch: {
		$route: {
			handler(n, o) {
				const _searchConfig = this.searchArr.slice()
				if (n && n.path.includes('good')) {
					this.searchConfig = this.searchArr
					this.staticSearchParam = {...this.formData}
					this.tableApi = queryListService
					this.columns = this.columns1
				} else {
					const searchItem = {
						type: 'dialog',
						fieldName: 'evalDeptName',
						label: '扣分部门',
						handleDialog: 'deptDialog'
					}, yearItem = {
						type: 'date1',
						fieldName: 'evalYear',
						dateType: 'year',
						label: '打分年',
						format: 'yyyy',
						valueFormat: 'yyyy'
					}, monthItem = {
						type: 'date1',
						fieldName: 'evalMonth',
						dateType: 'month',
						label: '打分月',
						format: 'MM',
						valueFormat: 'MM'
					}
					// _searchConfig.splice(5, 0, searchItem)
					_searchConfig.splice(7, 3, searchItem,yearItem, monthItem)
					this.searchConfig = _searchConfig
					this.staticSearchParam = {...this.formData}
					this.tableApi = queryDeductionListService
					this.columns = this.columns2
				}
			},
			immediate: true
		}
	},
  async created() {
    // 合作单位类型下拉选项数据
    this.$set(this.searchArr[1], 'options', await this.$dictOptions({ parentValue: "001001001", appCode: "001001" }))
    // 查询地市下拉选项
    this.getAreaList()
	},
	methods: {
	  // 查询地市下拉选项数据
		getAreaList() {
			queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
				if (res.code === '0000') {
					const list = []
					res.data.forEach(item => {
						list.push({ label: item.name, value: item.id })
					})
					this.$set(this.searchArr[4], 'options', list)
				}
			})
		},
		search(searchData) {
		  if(!this.$route.path.includes('good')){
        searchData.cooperationCompanyType = searchData.cooperationCompanyTypeEn || '';
        delete searchData.cooperationCompanyTypeEn;
      }
			this.$refs.table.page.current = 1
			this.staticSearchParam = { ...this.staticSearchParam,...searchData }
			this.$refs.table.getTableData(searchData)
		},
		reset(searchData) {
			searchData.evalDept && (searchData.evalDept = '')
			searchData.evalSpecialty && (searchData.evalSpecialty = '')
      	  	searchData.cooperationCompanyType && (searchData.cooperationCompanyType = '')
      	  	searchData.cooperationCompanyTypeEn && (searchData.cooperationCompanyTypeEn = '')
			this.$refs.table.page.current = 1
			this.staticSearchParam = { ...this.staticSearchParam,...searchData }
			this.$refs.table.getTableData(searchData)
		},
		exportMethod() {
			if (this.$route.path.includes('good')) {
				commonDown(this.staticSearchParam, exportMonthlyExcelService);
			} else {
				commonDown(this.staticSearchParam, exportDeductionExcelService);
			}
		},
    // 点击【评分权重配置】按钮，打开评分权重配置页面
    scorePerSettingHandle(){
      this.$router.push({
        path: '/cooperation_company/monthly_score/score_settings'
      })
    },
		openDialog(val) {
			if (val == 'specialtyDialog') {
				const item = this.specailty
				this.$refs.chooseSpecialty.init(item);
			} else {
				const item = this.dept
				this.$refs.chooseDept.init(item);
			}
		},
		showSpecialty({ checkList }) {
			const list = checkList
			const names = []
			const ids = []
			list.forEach(item => {
				names.push(item.name)
				ids.push(item.id)
			})
			this.$set(this.$refs.searchForm.searchForm, 'evalSpecialty', ids.join(','))
			this.$set(this.$refs.searchForm.searchForm, 'evalSpecialtyName', names.join(','))
		},
		showDeptList({ checkList }) {
			const list = checkList
			const names = []
			const ids = []
			list.forEach(item => {
				names.push(item.text)
				ids.push(item.id)
			})
			this.$set(this.$refs.searchForm.searchForm, 'evalDept', ids.join(','))
			this.$set(this.$refs.searchForm.searchForm, 'evalDeptName', names.join(','))
		},
    changeSelectHandle(name, val){
      // 合作单位类型改成中文
      if (name == 'cooperationCompanyTypeEn') {
        const selectedData = this.searchConfig[1].options.filter(item => item.value === val)
        this.$set(this.$refs.searchForm.searchForm, 'cooperationCompanyType', selectedData[0].label)
      }
    }
	}
}
</script>
<style lange="scss">

	.el-input-number{
		width: 100%
	}

</style>
