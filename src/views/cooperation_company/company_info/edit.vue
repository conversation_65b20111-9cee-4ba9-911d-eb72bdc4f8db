/**
* @author: hcm
* @date: 2023-07-06
* @description: 合作单位根据boid编辑或者是新增工单
*/
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button v-if="boId" type="primary" @click="submit">提交审核</el-button>
      <el-button v-if="!disableBasicForm" type="primary" @click="saveInfo">保存草稿</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard
      title="基本信息"
    >
      <div slot="content">
        <mssForm ref="basicForm" :config="basicConfig" :label-position="labelPosition" :form="basicForm" :disable-form="disableBasicForm" />
      </div>
    </mssCard>
    <div id="sectionZh" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.cooperationCompanyCode" title="账号信息">
      <div slot="content">
        <mssTable
          ref="accTable"
          :api="accTableApi"
          :columns="accountColumns"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionJg" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.cooperationCompanyCode" title="机构信息">
      <div slot="content">
        <mssTable
          ref="orgTable"
          :api="orgTableApi"
          :columns="orgColumns"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionZz" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.cooperationCompanyCode" title="资质信息">
      <div slot="content">
        <mssTable
          ref="certTable"
          :api="certTableApi"
          :columns="certColumns"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionNm" class="page-anchor-point"></div>
    <mssCard v-if="farmerAccParam.partnerDeptId" title="农民工工资专用账号信息">
      <div slot="headerBtn">
        <el-upload
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importAccFile"
        >
          <el-button v-if="!disableBasicForm" type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportFarmerAcc">导出</el-button>
        <el-button v-if="!disableBasicForm" @click="addTable('farmerAcc')">新增</el-button>
        <el-button v-if="!disableBasicForm" type="primary" @click="delTable('farmerAcc')">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="farmerAccTable"
          selection
          :columns="farmerAccColumns"
          :static-search-param="farmerAccParam"
          :api="farmerAccApi"
        />
      </div>
    </msscard>
    <div id="sectionGg" class="page-anchor-point"></div>
    <mssCard v-if="seniorParam.partnerDeptId" title="高管信息">
      <div slot="headerBtn">
        <el-upload
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importSeniorFile"
        >
          <el-button v-if="!disableBasicForm" type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportSenior">导出</el-button>
        <el-button v-if="!disableBasicForm" @click="addTable('senior')">新增</el-button>
        <el-button v-if="!disableBasicForm" type="primary" @click="delTable('senior')">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="seniorTable"
          selection
          :columns="seniorColumns"
          :api="seniorApi"
          :static-search-param="seniorParam"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :business-type="workflowCode" :node-name="nodeName" :bo-id="boId"></mssAttachment>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode" :deal-page="false"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig" />
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="!disableBasicForm"
      :dept-params="deptParams"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <!--农民工工资专用账号信息、高管信息 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="70%"
    >
      <mssForm
        v-if="dialogVisible"
        ref="dialogForm"
        :config="dialogConfig"
        :label-position="dialogPosition"
        :disable-form="dialogDisableForm"
        :form="dialogForm"
      ></mssForm>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="!dialogDisableForm" type="primary" @click="saveDialog">确 定</el-button>
        <el-button @click="cancalDialog">取 消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { checkPhone, checkEmail } from '@/utils/validate.js'
import { commonDown } from '@/utils/btn.js'
import { queryAreaListService } from '@/api/common_api.js'
import {
  queryCompanyByDeptIdService,
  queryDetailService,
  saveInfoService,
  updateInfoService,
  seniorListService,
  seniorSaveService,
  seniorUpdateService,
  seniorDetailService,
  seniorBatchremoveService,
  seniorExportExcelService,
  seniorImportExcelService,
  farmerAccListService,
  farmerAccSaveService,
  farmerAccUpdateService,
  farmerAccDetailService,
  farmerAccBatchremoveService,
  farmerAccExportExcelService,
  farmerAccImportExcelService,
  accListService, // 账号信息
  orgListService, // 机构信息
  certListService, // 资质信息
  notExistsProcessService
} from '@/api/cooperation_company/company_info.js'
export default {
  name: 'CooperativeUnitEdit',
  data() {
    return {
      deptParams: {},
      workflowCode: 'PartnerDept',
      nodeCode: '',
      completeTaskUrl: '',
      returnAddress: '/cooperation_company/company_info',
      disableBasicForm: false,
      basicConfig: [
        {
          label: '合作单位名称',
          type: 'input',
          prop: 'cooperationCompanyName',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位编码',
          type: 'input',
          prop: 'cooperationCompanyCode',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位类型',
          type: 'input',
          prop: 'cooperationCompanyType',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位法人',
          type: 'input',
          prop: 'legalRepre',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位注册地址',
          type: 'input',
          prop: 'cooperationCompanyAddress',
          span: 12,
          disabled: true
        },
         // 传给后台
         {
          label: '地市',
          type: 'select',
          prop: 'city',
          multiple: true,
          span: 12,
          options: [],
          rules: [{ required: true, message: '请选择地市', trigger: 'blur' }]
        },
        {
          label: '合作单位状态',
          type: 'input',
          prop: 'cooperationStatus',
          span: 12,
          disabled: true
        },
        {
          label: '编制人',
          type: 'input',
          prop: 'creatorName',
          span: 12,
          disabled: true
        },
        {
          label: '编制部门',
          type: 'input',
          prop: 'applyDeptName',
          span: 12,
          disabled: true
        },
        {
          label: '编制时间',
          type: 'input',
          prop: 'createDate',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位曾用名',
          type: 'input',
          mode:'textarea',
          prop: 'beforeName',
          span: 24,
          disabled: true
        },
        {
          label: '服务范围',
          type: 'input',
          prop: 'businessScopeName',
          span: 24,
          mode:'textarea',
          disabled: true
        }
      ],
      basicForm: {
        creatorId: sessionStorage.getItem('userId'),
        creatorName: sessionStorage.getItem('realName'),
        createDate: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
        applyDeptId: sessionStorage.getItem('deptId'),
        applyDeptName: sessionStorage.getItem('deptName'),
        cooperationCompanyName: '',
        cooperationCompanyCode: '',
        cooperationCompanyType: '',
        cooperationCompanyId: '', // 单位id
        legalRepre: '',
        cooperationCompanyAddress: '',
        beforeName: '',
        cooperationStatus: '',
        city: '',
        deptId: sessionStorage.getItem('deptId')
      },
      staticSearchParam: {}, // 静态列表的参数
      accTableApi: accListService,
      accountColumns: [
        { label: '综合员账号', prop: 'integratorAccount' },
        { label: '综合员账号密码', prop: 'integratorPwd' , formatter:row=>{
         if(row.integratorPwd){
          return row.integratorPwd.replace(/./g, "*")
          }
        } },
        { label: '允许登录', prop: 'integratorEnabled' },
        { label: '综合员姓名', prop: 'integratorUsername' },
        { label: '综合员电话', prop: 'integratorMobile',formatter:(row)=>{
            if(row.integratorMobile){
              return row.integratorMobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }
          }  },
        { label: '综合员邮箱', prop: 'integratorEmail' }],
      orgTableApi: orgListService,
      orgColumns: [
        { label: '机构名称', prop: 'orgName' },
        { label: '机构地址', prop: 'orgAddress' },
        { label: '单位负责人', prop: 'orgManager' },
        { label: '单位负责人电话', prop: 'orgManagerMobile',formatter:(row)=>{
            if(row.orgManagerMobile){
              return row.orgManagerMobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }
          }  }
      ],
      certTableApi: certListService,
      farmerAccApi: farmerAccListService, // 工资信息数据
      seniorApi: seniorListService, // 高管信息数据
      labelPosition: 'top',
      dialogVisible: false,
      dialogType: '', // 区分高管和账号
      dialogConfig: [],
      dialogPosition: 'top',
      dialogDisableForm: false,
      dialogForm: {},
      dialogTitle: '',
      seniorParam: {},
      farmerAccParam: {},
      boId: '',
      businessType: 'Subcontractor',
      nodeName: '草稿'
    }
  },
  computed: {
    pageAnchorConfig() {
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '机构<br>信息',
          id: 'sectionJg',
          show: this.basicForm['cooperationCompanyId']
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile',
          show: this.boId
        },
        {
          text: '流程<br>信息',
          id: 'sectionWorkFlow',
          show: this.boId
        }
      ]
    },
    data2(){
      return [
        {'date':'1'},
        {
          'date':'2'
        }
      ]
    },
    certFileColumns(){
      return [
      { prop: 'date'},
      ]
    },
    certColumns() {
      return [
        { label: '资质名称及等级', prop: 'certName' },
        { label: '资质有效期', formatter: row => {
          if(row.certPeriodEnd){
            return this.$moment(row.certPeriodEnd).format('yyyy-MM-DD')
          }
        } },
        { label: '资质范围', prop: 'certScope' },
        { label: '资质证书文件', prop: 'certDocument', minWidth:'120px', formatter: (row, column, cellValue, index) => {
          if(row.fileIds){
            return  <mssFileTemplate ref={'fileList'+index} customParams={{ids:row.fileIds}}></mssFileTemplate>
          }
        } },
        {
          label:'操作',
          formatter:(row, column, cellValue, index)=>{
            if(row.fileIds){
              return <span  class='table_btn mr10' onClick={()=>{this.downLoadCert(row,index)}}>批量下载证书</span>
            }
          }
        }
      ]
    },
    seniorColumns() {
      return [
        {
          label: '高管姓名',
          prop: 'managerName'
        },
        {
          label: '联系电话',
          prop: 'mobile'
        },
        {
          label: '邮箱地址',
          prop: 'emailAddress'
        },
        {
          label: '职务名称',
          prop: 'position'
        },
        {
          label: '分管内容',
          prop: 'submanagement'
        },
        {
          label: '操作',
          formatter: (row) => {
            return (
              <span>
                {this.disableBasicForm ? ('') : (
                  <span
                    class='table_btn mr10'
                    onClick={() => { this.acitonHandle('senior', 'edit', row) }}
                  >
                  编辑
                  </span>
                )}
                <span class='table_btn' onClick={() => { this.acitonHandle('senior', 'detail', row) }}>
                  查看
                </span>
              </span>
            )
          }
        }
      ]
    },
    farmerAccColumns() {
      return [
        {
          label: '开户行',
          prop: 'accBank'
        },
        {
          label: '开户名',
          prop: 'accName'
        },
        {
          label: '开立时间',
          prop: 'accTime'
        },
        {
          label: '操作',
          formatter: (row) => {
            return (
              <span>
                { this.disableBasicForm ? ('') : (<span
                  class='table_btn mr10'
                  onClick={() => { this.acitonHandle('farmerAcc', 'edit', row) }}
                >
                  编辑
                </span>)}
                <span class='table_btn' onClick={() => { this.acitonHandle('farmerAcc', 'detail', row) }}>
                  查看
                </span>
              </span>
            )
          }
        }
      ]
    }

  },
  created() {
    this.id = this.$route.query.boId
    this.boId = this.id
    this.getAreaList()
    if (this.id) {
      this.getInfo()
    } else {
      this.showCheckList()
    }
  },
  methods: {
    // 下载资质证书
    downLoadCert(row,index) {
      this.$refs[`fileList${index}`].getFilesBatchDownload();
    },
    getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.basicConfig[5], 'options', list)
        }
      })
    },
    // 详情
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.basicForm = {
            ...res.data,
            cooperationCompanyName: res.data.cooperationCompanyDto.cooperationcompanyname || '',
            cooperationCompanyCode: res.data.cooperationCompanyDto.cooperationcompanycode || '',
            cooperationCompanyType: res.data.cooperationCompanyDto.cooperationcompanytypeName || '',
            cooperationCompanyId: res.data.cooperationCompanyDto.cooperationcompanyid || '',
            legalRepre: res.data.cooperationCompanyDto.legalrepre || '',
            // cooperationCompanyAddress: res.data.cooperationCompanyDto.registeraddress || '',//2023.8.19改字段
            cooperationCompanyAddress : res.data.registerAddress || '',
            beforeName: res.data.cooperationCompanyDto.beforename || '',
            cooperationStatus: res.data.cooperationCompanyDto.cooperationstatus ? '否' : '是',
            city: res.data.city ? res.data.city.split(',') : [],
            creatorName: res.data.creatorName || '',
            applyDeptName: res.data.applyDeptName || '',
            createDate: res.data.createDate || '',
            businessScopeName: res.data.businessScopeName || ''
          }
          this.farmerAccParam = {
            cooperationCompanyId: this.basicForm.cooperationCompanyId,
            partnerDeptId: res.data.id
          }
          this.seniorParam = {
            cooperationCompanyId: this.basicForm.cooperationCompanyId,
            partnerDeptId: res.data.id
          }
          this.staticSearchParam = {
            cooperationCompanyCode: this.basicForm.cooperationCompanyCode
          }
          this.completeTaskUrl = res.data.completeTaskUrl
          this.$refs.workFlow.init()
        }
      })
    },
    // 根据合作单位id查询合作单位信息
    showCheckList() {
      queryCompanyByDeptIdService(this.basicForm.deptId).then(res => {
        if (res.code === '0000' && res.data.cooperationCompanyDto) {
          this.basicForm = { ...this.basicForm, ...res.data }
          this.basicForm['cooperationCompanyName'] = res.data.cooperationCompanyDto.cooperationcompanyname || ''
          this.basicForm['cooperationCompanyCode'] = res.data.cooperationCompanyDto.cooperationcompanycode || ''
          this.basicForm['cooperationCompanyType'] = res.data.cooperationCompanyDto.cooperationcompanytypeName || ''
          this.basicForm['cooperationCompanyId'] = res.data.cooperationCompanyDto.cooperationcompanyid || ''
          this.basicForm['legalRepre'] = res.data.cooperationCompanyDto.legalrepre || ''
          // this.basicForm['cooperationCompanyAddress'] = res.data.cooperationCompanyDto.registeraddress || ''
          this.basicForm['cooperationCompanyAddress'] = res.data.registerAddress || ''
          this.basicForm['beforeName'] = res.data.cooperationCompanyDto.beforename || ''
          this.basicForm['cooperationStatus'] = res.data.cooperationCompanyDto.cooperationstatus ? '否' : '是'
          this.staticSearchParam = {
            cooperationCompanyCode: this.basicForm.cooperationCompanyCode
          }
          this.farmerAccParam = {
            cooperationCompanyId: this.basicForm.cooperationCompanyId
          }
          this.seniorParam = {
            cooperationCompanyId: this.basicForm.cooperationCompanyId
          }
          // 处理地市
          if (res.data.city) {
            this.basicForm['city'] = res.data.city.split(',')
          }
          // 修改单位时刷新固定表格
          if (this.$refs.accTable) {
            this.$refs.accTable.getTableData(this.staticSearchParam)
            this.$refs.certTable.getTableData(this.staticSearchParam)
            this.$refs.orgTable.getTableData(this.staticSearchParam)
          }
        } else {
          this.$message.warning('没有找到该单位信息')
        }
      })
    },
    // 新增
    addTable(type) {
      this.dialogType = type
      this.dialogVisible = true
      this.dialogDisableForm = false
      if (type === 'farmerAcc') {
        this.dialogTitle = '农民工工资专用账号信息'
      } else {
        this.dialogTitle = '高管信息'
      }
      for (const key in this.dialogForm) {
        this.dialogForm[key] = ''
      }
      this.generateFormConfig()
    },
    // 删除
    delTable(type) {
      this.dialogType = type
      const selection = this.$refs[type + 'Table'].multipleSelection
      if (selection.length) {
        const ids = []
        selection.forEach(item => {
          ids.push(item.id)
        })
        this.$confirm('是否确认删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          (type === 'senior' ? seniorBatchremoveService : farmerAccBatchremoveService)({ ids: ids.join(',') }).then(res => {
            if (res.code === '0000') {
              this.$message({
                message: '删除成功！',
                type: 'success'
              })
              this.searchList()
            }
          })
        }).catch(() => {})
      } else {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
    },
    // 导入-农民工工资专用账号信息
    importAccFile(params) {
      this.dialogType = 'farmerAcc'
      const param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.id)
      farmerAccImportExcelService(param).then((res) => {
        // if (res.data) {
        //   this.$message.warning(res.data)
        // }
        if (res.code === '0000') {
          this.$message.success('导入成功')
          this.searchList()
        }
      })
    },
    // 导出-农民工工资专用账号信息
    exportFarmerAcc() {
      commonDown(this.farmerAccParam, farmerAccExportExcelService)
    },
    // 导入-高管信息
    importSeniorFile(params) {
      this.dialogType = 'senior'
      const param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.id)
      seniorImportExcelService(param).then((res) => {
        // if (res.data) {
        //   this.$message.warning(res.data)
        // }
        if (res.code === '0000') {
          this.$message.success('导入成功')
          this.searchList()
        }
      })
    },
    // 导出-高管信息
    exportSenior() {
      commonDown(this.seniorParam, seniorExportExcelService)
    },
    generateFormConfig() {
      if (this.dialogType === 'senior') {
        this.dialogConfig = [
          {
            label: '高管姓名',
            type: 'input',
            prop: 'managerName',
            span: 12
          },
          {
            label: '联系电话',
            type: 'input',
            prop: 'mobile',
            span: 12,
            rules: [{ validator: (rule, value, callback) => { checkPhone(rule, value, callback, false) }, trigger: 'blur' }]
          },
          {
            label: '邮箱地址',
            type: 'input',
            prop: 'emailAddress',
            rules: [{ validator: (rule, value, callback) => { checkEmail(rule, value, callback, false) }, trigger: 'blur' }]
          },
          {
            label: '职务名称',
            type: 'input',
            prop: 'position',
            span: 12
          },
          {
            label: '分管内容',
            type: 'input',
            prop: 'submanagement',
            span: 12
          }
        ]
      } else {
        this.dialogConfig = [
          {
            label: '开户行',
            type: 'input',
            prop: 'accBank',
            span: 12
          },
          {
            label: '开户名',
            type: 'input',
            prop: 'accName',
            span: 12
          },
          {
            label: '开立时间',
            dateType: 'datetime',
            type: 'datePicker',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            prop: 'accTime',
            span: 12
          }
        ]
      }
    },
    // 分包信息--修改和查看
    acitonHandle(type, action, row) {
      this.addTable(type)
      if (action === 'detail') {
        this.dialogDisableForm = true
      } else {
        this.dialogDisableForm = false
      }
      if (type === 'senior') {
        seniorDetailService(row.id).then(res => {
          if (res.code === '0000' && res.data) {
            this.dialogForm = res.data
            this.dialogVisible = true
          }
        })
      } else {
        farmerAccDetailService(row.id).then(res => {
          if (res.code === '0000' && res.data) {
            this.dialogForm = res.data
            this.dialogVisible = true
          }
        })
      }
    },
    cancalDialog() {
      this.dialogVisible = false
    },
    saveDialog() {
      this.$refs.dialogForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          const reqData = { ...this.$refs.dialogForm.modelForm, 'partnerDeptId': this.id, 'cooperationCompanyId': this.basicForm['cooperationCompanyId'] }
          if (this.dialogType === 'senior') {
            (reqData.id ? seniorUpdateService : seniorSaveService)(reqData).then(res => {
              if (res.code === '0000') {
                this.$message({ type: 'success', message: '保存成功！' })
                this.dialogVisible = false
                this.searchList()
              }
            })
          } else {
            (reqData.id ? farmerAccUpdateService : farmerAccSaveService)(reqData).then(res => {
              if (res.code === '0000') {
                this.$message({ type: 'success', message: '保存成功！' })
                this.dialogVisible = false
                this.searchList()
              }
            })
          }
        }
      })
    },
    // 查询信息列表
    searchList() {
      if (this.dialogType === 'senior') {
        this.$refs.seniorTable.page.current = 1
        this.$refs.seniorTable.getTableData(this.seniorParam)
      } else {
        this.$refs.farmerAccTable.page.current = 1
        this.$refs.farmerAccTable.getTableData(this.farmerAccParam)
      }
    },
    // 新增前校验
    async verifyWorkOrder() {
      let ableSave = true
      await notExistsProcessService(this.basicForm['deptId']).then(res => {
        if (res.code === '0000') {
          ableSave = res.data
        }
      })
      return ableSave
    },

    async saveInfo() {
      let ableSave = true
      let flag = false
      this.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          flag = true
        } else {
          return false
        }
      })
      if (flag) {
        const reqData = {
          ...this.$refs.basicForm.modelForm,
          id: this.id
        }
        if (reqData.city && reqData.city.length > 0) {
          reqData.city = reqData.city.join(',')
        } else {
          reqData.city = ''
        }
        // 新增校验
        if (!reqData.id) {
          ableSave = await this.verifyWorkOrder()
          if (!ableSave) {
            this.$message.warning(`${this.basicForm.cooperationCompanyName}存在流程中工单，请先完成流转中的工单`)
            return
          }
        }
        (reqData.id ? updateInfoService : saveInfoService)(reqData).then(res => {
          if (res.code === '0000') {
            this.$message.success('保存成功')
            this.id = res.data.id
            this.completeTaskUrl = res.data.completeTaskUrl
            this.farmerAccParam = {
              partnerDeptId: this.id,
              cooperationCompanyId: this.basicForm.cooperationCompanyId
            }
            this.seniorParam = {
              partnerDeptId: this.id,
              cooperationCompanyId: this.basicForm.cooperationCompanyId
            }
            this.staticSearchParam = {
              cooperationCompanyCode: this.basicForm.cooperationCompanyCode
            }
            //  刷新静态table(单点保存的)
            if (this.$refs.seniorTable) {
              this.$refs.seniorTable.getTableData(this.seniorParam)
              this.$refs.farmerAccTable.getTableData(this.farmerAccParam)
            }
            this.boId = this.id
            this.completeTaskUrl = res.data.completeTaskUrl
            this.$nextTick(() => {
              this.$refs.workFlow.init()
            })
          }
        })
      }
    },
    // 提交前保存表单信息
    save(cb) {
      const reqData = { ...this.$refs.basicForm.modelForm, id: this.id }
      if (reqData.city && reqData.city.length > 0) {
        reqData.city = reqData.city.join(',')
      } else {
        reqData.city = ''
      }
      updateInfoService(reqData).then(res => {
        if (res.code === '0000') {
          if (cb) {
            cb()
          } else {
            this.$message.success('保存成功')
          }
        }
      })
    },
    // 提交审核
    submit() {
      // let flag = false
      // this.$refs.basicForm.$refs.form.validateScroll((valid) => {
      //   if (valid) {
      //     flag = true
      //   } else {
      //     return false
      //   }
      // })
      // if (flag) {
        if(this.nodeCode=='sp'){//合作单位负责人 审批 (根据地市区县传参)
          this.deptParams = {
            rootId: this.$refs.basicForm.modelForm.city.length == 1 ? this.$refs.basicForm.modelForm.city[0] : '',
            orgChildId: this.$refs.basicForm.modelForm.city.length == 1 ? this.$refs.basicForm.modelForm.city[0] : ''
          }
        }
        this.$refs.workFlow.opendialogInitNextPath()
      // }
    },
    async beforeSubmit(){
      let str=""
      await this.$refs.basicForm.$refs.form.validateScroll((valid)=>{
        str = valid?'':'有必填信息未填写完整，请检查'
      })
      return str
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: `关于${this.$refs.basicForm.modelForm.cooperationCompanyName}合作单位信息维护`
      }
      const isManyCity = {
        code: 'isManyCity',
        value: this.basicForm.isManyCity ? '1' : '0'
      }
      workFlowPrams.push(name, isManyCity)
      return workFlowPrams
    },
    getNodeData(data) {
      this.nodeCode = data.nodeCode
      this.nodeName = data.nodeName
      if (this.nodeCode === 'xzscbg' || !this.nodeCode) {
        this.disableBasicForm = false
      } else {
        this.disableBasicForm = true
      }
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/company_info'
      })
    }
  }
}
</script>

<style lang="scss">

.cooperation-company{
  padding-right: 70px;
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
  .upload-btn {
    display: inline-block;
    margin: 0 10px;
  }
  .file-down{
    .el-input{
      width: 75%;
      margin-right: 10px;
    }
  }
}
</style>
