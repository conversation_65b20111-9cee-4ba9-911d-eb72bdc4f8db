/**
* @author:
* @date: 2023-07-06
* @description: 合作单位信息列表
*/
<template>
  <div class="informationList">
    <mssSearchForm
      ref="searchForm"
      :form="searchForm"
      :search-config="formConfig"
      @search="search"
      @reset="reset"
    />
    <mssCard title="合作单位信息列表">
      <div slot="headerBtn">
        <el-button v-if="powerData['company_info_add']" @click="acitonHandle('add')">新增</el-button>
        <el-button type="primary" @click="exportList">导出</el-button>
        <el-button type="primary" @click="exportAttachment">导出附件</el-button>
      </div>
      <div slot="content">
        <mssTable
          rowKey="$index"
          ref="commonTable"
          selection
          :reserveSelection="false"
          :columns="tableHeader"
          :api="tableApi"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
  </div>
</template>

<script>
import { commonDown } from '@/utils/btn.js'
import {
  queryListService,
  removeIdsService,
  exportPageService,
  exportAttachmentService
} from '@/api/cooperation_company/company_info.js'
export default {
  name: 'CompanyInfoList',
  data() {
    return {
      powerData:[],//权限
      tableApi: queryListService,
      formConfig: [
        {
          label: '合作单位名称',
          type: 'input',
          fieldName: 'cooperationCompanyName'
        },
        {
          label: '合作单位编码',
          type: 'input',
          fieldName: 'cooperationCompanyCode'
        },
        {
          label: '合作单位类型',
          type: 'select',
          fieldName: 'cooperationCompanyType',
          options: [
            { label: '设计', value: '0' },
            { label: '监理', value: '1' },
            { label: '施工', value: '2' }
          ]
        },
        {
          label: '服务范围',
          type: 'input',
          fieldName: 'businessScopeName'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'entityStatus'
        },
        {
          label: '数据状态',
          type: 'select',
          options: [
            {label:'全部',value:'all'},
            {label:'单位最新数据',value:'lastData'},
          ],
          fieldName: 'dataStatus'
        }
      ],
      tableHeader: [
        {
          prop: 'cooperationCompanyName',
          label: '合作单位名称',
          tooltip: true,
          minWidth: '200px'
        },
        {
          prop: 'cooperationCompanyCode',
          label: '合作单位编码',
          tooltip: true,
          minWidth: '140px'
        },
        {
          prop: 'cooperationCompanyType',
          label: '合作单位类型',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'legalRepre',
          label: '合作单位法人',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'businessScopeName',
          label: '服务范围',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'registerAddress',
          label: '合作单位注册地址',
          tooltip: true,
          minWidth: '140px'
        },
        {
          prop: 'beforeName',
          label: '合作单位曾用名',
          tooltip: true,
          minWidth: '140px'
        },
        {
          prop: 'integratorAccount',
          label: '综合员账号',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'integratorUsername',
          label: '综合员姓名',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'integratorMobile',
          label: '综合员电话',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'integratorEmail',
          label: '综合员邮箱',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'certName',
          label: '资质名称及等级',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'certScope',
          label: '资质范围',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'certPeriodSt',
          label: '资质有效期起',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'certPeriodEnd',
          label: '资质有效期止',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'managerName',
          label: '高管姓名',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'mobile',
          label: '联系电话',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'emailAddress',
          label: '邮箱地址',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'position',
          label: '职务名称',
          tooltip: true,
          minWidth: '100px'
        },
        {
          prop: 'submanagement',
          label: '分管内容',
          tooltip: true,
          minWidth: '120px'
        },
        {
          prop: 'accBank',
          label: '农民工工资专用账号开户行',
          tooltip: true,
          minWidth: '180px'
        },
        {
          prop: 'accName',
          label: '农民工工资专用账号开户名',
          tooltip: true,
          minWidth: '180px'
        },
        {
          prop: 'accTime',
          label: '农民工工资专用账号开立时间',
          tooltip: true,
          minWidth: '180px'
        },
        {
          prop: 'entityStatus',
          label: '状态',
          tooltip: true,
          minWidth: '120px'
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          minWidth: '120px',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (<span
                  class='table_btn mr10'
                  onClick={() => { this.acitonHandle('edit', row) }}
                >
                  处理
                </span>) : ('')}
                {this.powerData['company_info_view']&&row.id ? (<span class='table_btn mr10' onClick={() => { this.acitonHandle('detail', row) }}>
                  查看
                </span>) : ('')}
                {row.isAllowDelete ? (
                  <span
                    class='table_btn'
                    onClick={() => { this.delHandle(row) }}
                  >
                  删除
                  </span>
                ) : ('')}
              </span>
            )
          }
        }
      ],
      searchForm:{dataStatus:"lastData"},
      staticSearchParam: {dataStatus:'lastData'}
    }
  },
  async created() {
    this.getPower()
    this.$set(this.formConfig[4], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB' }))
  },
  methods: {
    getPower(){
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item)=>{
        this.powerData[item.authority]=true
      })
    },
    exportList() {
      const params = {
        ...this.$refs.searchForm.searchForm,
        limit: this.$refs.commonTable.page.size,
        page: this.$refs.commonTable.page.current,
        exportType: 'all'
      }
      commonDown(params, exportPageService)
    },
    // 导出附件
    exportAttachment() {
      const selection = this.$refs.commonTable.multipleSelection
      const ids = []
      if (selection.length > 0) {
        selection.forEach(item => {
          if(item.id){
            ids.push(`${item.id}_${item.cooperationCompanyCode}`)
          }else{
            ids.push(`_${item.cooperationCompanyCode}`)
          }
        })
        commonDown({ ids: ids.join(',') }, exportAttachmentService)
      } else {
        this.$message.warning('请勾选数据')
      }
    },
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.commonTable.page.current = 1
      this.$refs.commonTable.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
    acitonHandle(type, row) {
      const id = row ? row.id : ''
      if (type === 'detail') {
        this.$router.push({
          path: '/cooperation_company/company_info/view',
          query: {
            boId: id
          }
        })
      } else {
        this.$router.push({
          path: '/cooperation_company/company_info/edit',
          query: {
            boId: id
          }
        })
      }
    },
    delHandle(row) {
      this.$confirm('是否确认删除这条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeIdsService({ ids: row.id }).then(res => {
          if (res.code === '0000') {
            this.$message({
              type: 'success',
              message: '删除成功！'
            })
            this.search(this.$refs.searchForm.searchForm)
          }
        })
      }).catch(() => {})
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
