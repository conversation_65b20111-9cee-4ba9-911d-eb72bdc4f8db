/**
* @author: hcm
* @date: 2023-07-06
* @description: 合作单位:查看
*/
<template>
  <div class="cooperation-company">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm :disable-form="true" :config="basicConfig" :label-position="labelPosition" :form="basicForm" :label-width="'180px'" />
      </div>
    </mssCard>
    <div id="sectionZh" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.cooperationCompanyCode" title="账号信息">
      <div slot="content">
        <mssTable
          ref="accTable"
          :api="accTableApi"
          :columns="accountColumns"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionJg" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.cooperationCompanyCode" title="机构信息">
      <div slot="content">
        <mssTable
          ref="orgTable"
          :api="orgTableApi"
          :columns="orgColumns"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionZz" class="page-anchor-point"></div>
    <mssCard v-if="staticSearchParam.cooperationCompanyCode" title="资质信息">
      <div slot="content">
        <mssTable
          ref="certTable"
          :api="certTableApi"
          :columns="certColumns"
          :static-search-param="staticSearchParam"
        />
      </div>
    </mssCard>
    <div id="sectionNm" class="page-anchor-point"></div>
    <mssCard title="农民工工资专用账号信息">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportFarmerAcc">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          v-if="boId"
          ref="farmerAccTable"
          :columns="farmerAccColumns"
          :api="farmerAccApi"
          :static-search-param="farmerAccParam"
        />
      </div>
    </mssCard>
    <div id="sectionGg" class="page-anchor-point"></div>
    <mssCard title="高管信息">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportSenior">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          v-if="boId"
          ref="seniorTable"
          :columns="seniorColumns"
          :api="serniorApi"
          :static-search-param="seniorParam"
        />
      </div>
    </mssCard>
    <div id="sectionFile" class="page-anchor-point"></div>
    <mssAttachment
      v-if="boId"
      :business-type="'PartnerDept'"
      :node-name="nodeName"
      :bo-id="boId"
      :deal-page="false"
    ></mssAttachment>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="'PartnerDept'"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig" />
  </div>
</template>

<script>
import { commonDown } from '@/utils/btn.js'
import { queryAreaListService } from '@/api/common_api.js'
import {
  queryDetailService,
  seniorListService,
  farmerAccListService,
  farmerAccExportExcelService,
  seniorExportExcelService,
  accListService, // 账号信息
  orgListService, // 机构信息
  certListService // 资质信息
} from '@/api/cooperation_company/company_info.js'
export default {
  name: 'CooperativeUnitDetail',
  data() {
    return {
      serniorApi: seniorListService,
      farmerAccApi: farmerAccListService,
      basicConfig: [
        {
          label: '合作单位名称：',
          type: 'input',
          prop: 'cooperationCompanyName',
          span: 12
        },
        {
          label: '合作单位编码：',
          type: 'input',
          prop: 'cooperationCompanyCode',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位类型：',
          type: 'input',
          prop: 'cooperationCompanyType',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位法人：',
          type: 'input',
          prop: 'legalRepre',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位注册地址：',
          type: 'input',
          prop: 'cooperationCompanyAddress',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位状态：',
          type: 'input',
          prop: 'cooperationStatus',
          span: 12,
          disabled: true
        },
        {
          label: '地市：',
          type: 'select',
          prop: 'city',
          span: 12,
          disabled: true,
          multiple: true,
          options: []
        },
        {
          label: '编制人：',
          type: 'input',
          prop: 'creatorName',
          span: 12,
          disabled: true
        },
        {
          label: '编制部门：',
          type: 'input',
          prop: 'applyDeptName',
          span: 12,
          disabled: true
        },
        {
          label: '编制时间：',
          type: 'input',
          prop: 'createDate',
          span: 12,
          disabled: true
        },
        {
          label: '合作单位曾用名：',
          type: 'input',
          prop: 'beforeName',
          span: 24,
          disabled: true
        },
        {
          label: '服务范围：',
          type: 'input',
          prop: 'businessScopeName',
          span: 24,
          disabled: true
        }
      ],
      basicForm: {},
      staticSearchParam: {}, // 静态列表的参数
      accTableApi: accListService,
      accountColumns: [
        { label: '综合员账号', prop: 'integratorAccount' },
        { label: '综合员账号密码', prop: 'integratorPwd', formatter:row=>{
         if(row.integratorPwd){
          return row.integratorPwd.replace(/./g, "*")
          }
        } },
        { label: '允许登录', prop: 'integratorEnabled' },
        { label: '综合员姓名', prop: 'integratorUsername' },
        { label: '综合员电话', prop: 'integratorMobile',formatter:(row)=>{
            if(row.integratorMobile){
              return row.integratorMobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }
          } },
        { label: '综合员邮箱', prop: 'integratorEmail' }],
      orgTableApi: orgListService,
      orgColumns: [
        { label: '机构名称', prop: 'orgName' },
        { label: '机构地址', prop: 'orgAddress' },
        { label: '单位负责人', prop: 'orgManager' },
        { label: '单位负责人电话', prop: 'orgManagerMobile' ,formatter:(row)=>{
            if(row.orgManagerMobile){
              return row.orgManagerMobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }
          } }
      ],
      certTableApi: certListService,
      farmerAccColumns: [
        {
          label: '开户行',
          prop: 'accBank'
        },
        {
          label: '开户名',
          prop: 'accName'
        },
        {
          label: '开立时间',
          prop: 'accTime'
        }
      ],
      seniorColumns: [
        {
          label: '高管姓名',
          prop: 'managerName'
        },
        {
          label: '联系电话',
          prop: 'mobile'
        },
        {
          label: '职务名称',
          prop: 'position'
        },
        {
          label: '分管内容',
          prop: 'submanagement'
        }
      ],
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '工资<br>信息',
          id: 'sectionNm'
        },
        {
          text: '附件<br>信息',
          id: 'sectionFile'
        },
        {
          text: '流程<br>信息',
          id: 'sectionWorkFlow'
        }
      ],
      boId: '',
      nodeName: '',
      seniorParam: {},
      farmerAccParam: {}
    }
  },
  computed: {
    certColumns() {
      return [
        { label: '资质名称及等级', prop: 'certName' },
        { label: '资质有效期', formatter: row => {
          if(row.certPeriodEnd){
            return this.$moment(row.certPeriodEnd).format('yyyy-MM-DD')
          }
        } },
        { label: '资质范围', prop: 'certScope' },
        { label: '资质证书文件', prop: 'certDocument', minWidth:'120px', formatter: (row, column, cellValue, index) => {
          if(row.fileIds){
            return  <mssFileTemplate ref={'fileList'+index} customParams={{ids:row.fileIds}}></mssFileTemplate>
          }
        } },
        {
          label:'操作',
          formatter:(row, column, cellValue, index)=>{
            if(row.fileIds){
              return <span  class='table_btn mr10' onClick={()=>{this.downLoadCert(row,index)}}>批量下载证书</span>
            }
          }
        }
      ]
    }
  },
  created() {
    this.labelPosition = 'left'
    this.id = this.$route.query.boId
    if (this.id) {
      this.getInfo()
    }
    this.getAreaList()
  },
  methods: {
    // 下载资质证书
    downLoadCert(row,index) {
      this.$refs[`fileList${index}`].getFilesBatchDownload();
    },
    getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.basicConfig[6], 'options', list)
        }
      })
    },
    // 导出-农民工工资专用账号信息
    exportFarmerAcc() {
      commonDown(this.farmerAccParam, farmerAccExportExcelService)
    },
    // 导出-高管信息
    exportSenior() {
      commonDown(this.seniorParam, seniorExportExcelService)
    },
    // 详情
    getInfo() {
      queryDetailService(this.id).then(res => {
        if (res.code === '0000') {
          this.basicForm = {
            ...res.data,
            ...res.data.cooperationCompanyDto,
            cooperationCompanyName: res.data.cooperationCompanyDto.cooperationcompanyname || '',
            cooperationCompanyCode: res.data.cooperationCompanyDto.cooperationcompanycode || '',
            cooperationCompanyType: res.data.cooperationCompanyDto.cooperationcompanytypeName || '',
            cooperationCompanyId: res.data.cooperationCompanyDto.cooperationcompanyid || '',
            legalRepre: res.data.cooperationCompanyDto.legalrepre || '',
            // cooperationCompanyAddress: res.data.cooperationCompanyDto.registeraddress || '',
            cooperationCompanyAddress : res.data.registerAddress || '',
            beforeName: res.data.cooperationCompanyDto.beforename || '',
            cooperationStatus: res.data.cooperationCompanyDto.cooperationstatus ? '否' : '是',
            city: res.data.city ? res.data.city.split(',') : [],
            creatorName: res.data.creatorName || '',
            applyDeptName: res.data.applyDeptName || '',
            createDate: res.data.createDate || '',
            businessScopeName: res.data.businessScopeName || ''
          }
          this.farmerAccParam = {
            cooperationCompanyId: this.basicForm.cooperationCompanyId,
            partnerDeptId: res.data.id
          }
          this.seniorParam = {
            cooperationCompanyId: this.basicForm.cooperationCompanyId,
            partnerDeptId: res.data.id
          }
          this.staticSearchParam = {
            cooperationCompanyCode: this.basicForm.cooperationCompanyCode
          }
          this.boId = this.id
        }
      })
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/cooperation_company/company_info'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cooperation-company{
  padding-right: 70px;
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
