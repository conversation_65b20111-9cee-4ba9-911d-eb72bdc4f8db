<!-- 框架合同月度考核得分
（根据路由区分）-->
<template>
  <div class="monthlyscore">
    <mssSearchForm
      :searchConfig="searchConfig"
      ref="searchForm"
      @search="search"
      @reset="reset"
      @changeSelect="changeSelectHandle"
    ></mssSearchForm>
    <mssCard :title="tableName">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportMethod">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          border
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          :api="tableApi"
        />
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  queryListService,
  exportMonthlyExcelService,
} from '@/api/cooperation_company/framework_score'
import { commonDown } from '@/utils/btn'
export default {
  name: 'MonthlyScore',
  data() {
    return {
      searchConfig: [
        {
          type: 'input',
          fieldName: 'frameContName',
          label: '框架合同名称',
        },
        {
          type: 'input',
          fieldName: 'frameContCode',
          label: '框架合同编码'
        },
        {
          type: 'input',
          fieldName: 'cooperationCompanyName',
          label: '合作单位名称'
        },
        {
          type: 'input',
          fieldName: 'cooperationCompanyCode',
          label: '合作单位编码',
        },
        {
          type: 'select',
          fieldName: 'companyTypes',
          label: '合作单位类型',
          options: []
        },
        {
					type: 'date1',
					fieldName: 'scoreMonth',
					label: '评分月份',
          valueFormat: 'yyyy-MM',
          format: 'yyyy-MM',
          dateType: 'month'
				}
      ],
      tableName: this.$route.meta.title,
      staticSearchParam: {},
      tableApi: queryListService,
      columns: [
        {
          prop: 'cooperationCompanyType',
          label: '合作单位类型',
          tooltip: true,
          width: 120
        },
        {
          prop: 'frameContCode',
          label: '框架合同编码',
          tooltip: true,
          width: 180
        },
        {
          prop: 'frameContName',
          label: '框架合同名称',
          tooltip: true,
          minWidth: 160
        },
        {
          prop: 'cooperationCompanyName',
          label: '合作单位名称',
          tooltip: true,
          width: 220
        },
        {
          prop: 'cooperationCompanyCode',
          label: '合作单位编码',
          tooltip: true,
          width: 160
        },
        {
          prop: 'evalMonth',
          label: '评分月份',
          width: 80,
          tooltip: true
        },
        {
          prop: 'scoreCompanyName',
          label: '建设单位',
          tooltip: true,
          width: 160
        },
        {
          prop: 'evalScore',
          label: '得分',
          tooltip: true,
          width: 100
        },
      ],
    }
  },
  async created() {
    // 合作单位类型下拉选项数据
    this.$set(
      this.searchConfig[4],
      'options',
      await this.$dictOptions({ parentValue: '*********', appCode: '001001' })
    )
  },
  methods: {
    search(searchData) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...this.staticSearchParam, ...searchData }
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...this.staticSearchParam, ...searchData }
      this.$refs.table.getTableData(searchData)
    },
    exportMethod() {
      commonDown(this.staticSearchParam, exportMonthlyExcelService)
    },
    changeSelectHandle(name, val) {
      // 合作单位类型改成中文
      if (name == 'companyTypes') {
        const selectedData = this.searchConfig[4].options.filter(
          (item) => item.value === val
        )
        this.$set(
          this.$refs.searchForm.searchForm,
          'companyTypes',
          selectedData[0].label
        )
      }
    }
  }
}
</script>
<style lange="scss">
.el-input-number {
  width: 100%;
}
</style>
