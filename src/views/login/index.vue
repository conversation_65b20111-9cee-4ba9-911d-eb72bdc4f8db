<template>
  <div class="login">
    <el-row class="login-top">
      <img src="../../assets/login_imges/login_logo.png" alt="login logo" />
    </el-row>
    <div class="main-content">
      <el-row class="login-content">
        <img src="../../assets/login_imges/login_bc.png" alt="login bc" />
        <div class="form-box">
          <el-form
            ref="loginForm"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            autocomplete="on"
          >
            <div class="title-container">
              <span class="title">欢 迎 登 录</span>
              <div class="short-line"></div>
            </div>
            <el-tabs v-model="loginType" @tab-click="switchLoginType">
              <el-tab-pane label="密码登录" name="password">
                <el-form-item prop="username">
                  <el-input
                    ref="username"
                    v-model="loginForm.username"
                    placeholder="请输入用户名称"
                    name="username"
                    type="text"
                    autocomplete="off"
                    prefix-icon="iconfont icon-yonghuming"
                  />
                </el-form-item>

                <el-tooltip v-model="capsTooltip" content="大写锁定已打开" placement="right" manual>
                  <el-form-item prop="password">
                    <el-input
                      :key="passwordType"
                      ref="password"
                      v-model="loginForm.password"
                      :type="passwordType"
                      placeholder="请输入用户密码"
                      name="password"
                      autocomplete="off"
                      @keyup.native="checkCapslock"
                      @blur="capsTooltip = false"
                      @keyup.enter.native="handleLogin"
                      prefix-icon="iconfont icon-mima"
                    />
                  </el-form-item>
                </el-tooltip>
                <el-form-item prop="code" class="yzm-container">
                  <div class="yzm-input">
                    <el-input
                      v-model="loginForm.code"
                      name="yzm"
                      placeholder="请输入验证码"
                      type="text"
                      autocomplete="off"
                      prefix-icon="iconfont icon-yanzhengma"
                    ></el-input>
                  </div>
                  <img
                    class="yzmImg"
                    :src="this.verificationImg"
                    alt=""
                    @click="getPicCode"
                  />
                  <el-button  type="text" class="forgotPwd" @click="forgotPwd">忘记密码</el-button>
                </el-form-item>

                <el-button
                  :loading="loading"
                  type="primary"
                  class="submitBtn"
                  @click.native.prevent="handleLogin"
                >登 录</el-button>
              </el-tab-pane>
              <el-tab-pane label="短信登录" name="message">
                <el-form-item label="" lable-width="0px" prop="phoneNum">
                  <el-input
                    v-model="loginForm.phoneNum"
                    name="phoneNum"
                    placeholder="请输入手机号"
                    maxlength="11"
                    prefix-icon="iconfont icon-yonghuming"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label=""
                  lable-width="0px"
                  prop="messageCode"
                  class="code-box"
                >
                  <el-input
                    v-model="loginForm.messageCode"
                    type="text"
                    placeholder="手机验证码"
                  ></el-input>
                  <el-button
                    type="primary"
                    class="messageCode-btn"
                    :disabled="sendingMsg"
                    @click="getMessageCode()"
                  >{{ verifiMsg }}</el-button>
                </el-form-item>
                <el-button
                  :loading="loading"
                  type="primary"
                  class="submitBtn"
                  @click.native.prevent="messageLogin"
                >登 录</el-button>
              </el-tab-pane>
            </el-tabs>
          </el-form>
        </div>
      </el-row>
    </div>
    <el-row class="footer">Copyright by@中国移动通信集团江西有限公司</el-row>
    <!-- 修改密码弹框 -->
    <findPassword :key="findKey"  ref="findPassword" />
  </div>
</template>

<script>
import { encryption } from '@/utils/encryption'
import {
  getPicCodeService,
  getUserMenuService,
  sendMsgService
} from '@/api/user'
import findPassword from './findPassword'
import { iterat } from '@/utils/index'
export default {
  name: 'Login',
  components:{
    findPassword
  },
  data() {
    // 手机号验证
    const validatePhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入手机号"));
      } else {
        if (!/^1[3456789]\d{9}$/.test(value)) {
          callback(new Error("请输入正确的手机号"));
        } else {
          callback();
        }
      }
    };
    return {
      loginForm: {
        username: '',
        password: '',
        code: ''
      },
      loginRules: {
        username: [
          { required: true, trigger: 'blur', message: '请填写用户名！' }
        ],
        password: [
          { required: true, trigger: 'blur', message: '请填写密码！' }
        ],
        code: [{ required: true, trigger: 'blur', message: '请填写验证码！' }],
        phoneNum: [{ validator: validatePhone, trigger: "blur" }],
        messageCode: [{ required: true, trigger: 'blur', message: '请填写短信验证码！' }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      redirect: undefined,
      otherQuery: {},
      verificationImg: '',
      findKey: 1,
      loginType: "password",
      verifiMsg: "获取验证码",
      sendingMsg: false,
    }
  },
  computed: {
    msgShow: {
      get() {
        return window.g?.loginType || 'account_pwd'
      }
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    this.getPicCode()
  },
  mounted() {
    // if (this.loginForm.username === '') {
    //   this.$refs.username.focus()
    // } else if (this.loginForm.password === '') {
    //   this.$refs.password.focus()
    // }
    window.addEventListener('keydown', this.addEnterHandle)
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.addEnterHandle)
  },
  methods: {
    // 点击回车登录的事件
    addEnterHandle() {
      let e = event || window.event
      if (e.keyCode === 13) {
        // 回车则执行登录方法
        this.handleLogin()
      }
    },
    checkCapslock(e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    // 请求图片验证码
    getPicCode() {
      this.randData = this.randomString()
      getPicCodeService({
        randomStr: encryption({ data: this.randData, key: '****************' })
      }).then((res) => {
        this.verificationImg = window.URL.createObjectURL(res.data)
      })
    },
    //随机产生四位验证码
    randomString() {
      let e = 4
      var t = '1234567890',
        a = t.length,
        n = ''
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    },
    forgotPwd(){
      this.$nextTick(()=>{
        this.$refs.findPassword.init()
      })
    },
    // 点击登录按钮
    handleLogin() {
      let validStr = ""
      this.$refs.loginForm.validateField(["username","password","code"],(valid) => {
        validStr += valid
      })
      if (!validStr) {
        this.loading = true
        const client_id = window.g.client_id
        const client_secret = window.g.client_secret
        const loginParams = {
          grant_type: this.msgShow,
          scope: 'server',
          platformId: window.g?.platformId || 'eoms',
          client_id: encryption({ data: client_id, key: '****************' }),
          client_secret: encryption({
            data: client_secret,
            key: '****************'
          }),
          account: encryption({
            data: this.loginForm.username,
            key: '****************'
          }),
          password: encryption({
            data: this.loginForm.password,
            key: '****************'
          }),
          code: this.loginForm.code,
          randomStr: encryption({
            data: this.randData,
            key: '****************'
          })
        }
        this.$store
          .dispatch('user/login', loginParams)
          .then((res) => {
            if (
              window.g.enablePwdExpired &&
              res.data.attrs &&
              res.data.attrs.pwdExpiredTime
            ) {
              let flag = res.data.attrs.pwdExpiredTime <= 0
              if (flag) {
                sessionStorage.setItem('enablePwdExpired', flag)
              }
            }
            this.getMenu()
            this.loading = false
          })
          .catch(() => {
            this.getPicCode()
            this.loading = false
          })
      } else {
        return false
      }
    },
    getMenu() {
      //获取当前用户菜单
      getUserMenuService({
        queryAll: true,
        rootId: 'root-jx-fz',
        type: '0'
      }).then(async (res) => {
        if (res && res.data) {
          sessionStorage.setItem('private_menu_pts', JSON.stringify(res.data))
          let data = res.data || []
          let logMenu = {
            path:iterat(data,'path'),
            id:iterat(data,'id'),
            name:iterat(data,'name')
          }
          sessionStorage.setItem('logMenu',JSON.stringify(logMenu))
          if (
            this.loginType == "password" &&
            window.g.enablePwdCheck &&
            window.g.pwdCheckRule &&
            window.g.pwdCheckRule.regex
          ) {
            let psw = this.loginForm.password
            let flag = !window.g.pwdCheckRule.regex.test(psw)
            if (flag) {
              sessionStorage.setItem('enablePwdCheck', flag)
            }
          }
          // 对于新登录的用户，如果有redirect的，需要检查redirect是否有页面权限,有则进入到redirect页面，没有就显示首页
          this.$router.push({ path: '/', query: this.otherQuery })
        } else {
          this.$message({
            showClose: true,
            message: '用户不存在！',
            type: 'warning'
          })
          this.getPicCode()
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    switchLoginType(data){
      this.loginType = data.name
      this.loginForm = {}
      this.$nextTick(()=>{
        this.$refs.loginForm.clearValidate()
      })
    },
    getMessageCode(){
      this.sendingMsg = true
      this.$refs.loginForm.validateField("phoneNum", (valid)=>{
        if(!valid){
          const req = {
            mobile: this.loginForm.phoneNum,
            moduleName: "login-mobile-sms",
            platformId: window.g?.platformId || 'eoms'
          }
          sendMsgService(req).then(res=>{
            if(res.code === "0000" && res.data.status == "1"){
              this.$message({
                type: "success",
                message: res.data.msg
              })
              sessionStorage.setItem("sn", res.data.sn)
            }else{
              this.$message({
                type: "error",
                message: res.data.msg || "获取验证码失败"
              })
            }
            this.sendingMsg = false
          }).catch(()=>{
            this.sendingMsg = false
          })
        }else{
          this.sendingMsg = false
        }
      })
    },
    messageLogin(){
      let validStr = ""
      this.$refs.loginForm.validateField(["phoneNum","messageCode"],(valid) => {
        validStr += valid
      })
      if (!validStr) {
        const client_id = window.g.client_id
        const client_secret = window.g.client_secret
        const params = {
          grant_type: "mobile_sms",
          scope: 'server',
          platformId: window.g?.platformId || 'eoms',
          client_id: encryption({ data: client_id, key: '****************' }),
          client_secret: encryption({
            data: client_secret,
            key: '****************'
          }),
          mobile: encryption({data: this.loginForm.phoneNum, key: '****************'}),
          sn: sessionStorage.getItem('sn'),
          sms: this.loginForm.messageCode
        }
        this.$store
          .dispatch('user/login', params)
          .then((res) => {
            if(res && res.data){
              this.getMenu()
              this.loading = false
            }
          })
          .catch(() => {
            this.loading = false
          })
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.login {
  background-color: #fff;
  .login-top {
    height: 7rem;
    display: flex;
    align-items: center;

    img {
      margin-left: 7%;
      height: 70%;
    }
  }
  .main-content {
    .login-content {
      position: relative;
      > img {
        width: 100%;
      }
      .form-box {
        position: absolute;
        right: 8%;
        top: 10%;
        width: 34%;
        height: 80%;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        .login-form {
          width: 92%;
          height: 92%;
          border: 10px solid rgba(255, 255, 255, 0.5);
          border-radius: 10px;
          padding: 2% 30px;
          box-shadow: 0px 7px 10px 0px rgba(183, 198, 206, 0.57);
          background: #ffffff no-repeat;
          text-align: center;
          .code-box {
            .el-form-item__content {
              .el-input {
                width: 58%;
                float: left;
              }
              .el-button {
                width: 38%;
                float: right;
              }
            }
          }
          .title-container {
            width: 100%;
            .title {
              font-weight: 500;
              color: #181818;
            }
            .short-line {
              width: 50px;
              border-radius: 3px;
              background-color: #0085ff;
              height: 3px;
              margin: 10px auto 3%;
            }
          }
          .yzm-container {
            .el-form-item__content {
              display: flex;
              justify-content: space-between;
              .yzmbtn {
                background: rgba(203, 241, 254, 0.8);
                border-radius: 4px;
                text-align: center;
                color: #3987fc;
                padding: 0 15px;
                cursor: pointer;
              }

              .yzm-input {
                flex: none;
                width: 50%;
              }
              .forgotPwd{
                padding-left: 10px;
                flex: none;
                width: 19%;
                &:hover{
                  text-decoration: underline;
                }
              }
            }
          }
          .el-input__prefix{
            color: #33acfb;
          }
        }
      }
    }
  }

  .el-form-item__error {
    font-size: 1rem;
  }
  .footer {
    height: 6.5rem;
    text-align: center;
    line-height: 6.5rem;
    color: #888888;
    font-size: 1rem;
    font-weight: 400;
  }
  .login-error-msg {
    color: $wf-color-danger;
  }
}
@media screen and (min-width: 1000px) {
  .login {
    .el-form-item__error {
      font-size: 0.8rem;
    }
  }
  .login {
    .title {
      font-size: 20px;
    }
    .el-tabs__item{
      font-size: 16px;
    }
    .yzmbtn {
      height: 36px;
      line-height: 36px;
      font-size: 16px;
    }
    .forgotPwd{
      font-size: 14px;
    }
    .yzmImg{
      flex: none;
      width: 27%;
      height: 36px;
      margin-left: 10px;
      border-radius: 4px;
    }
    .messageCode-btn{
      font-size: 16px;
      height: 36px;
    }
    .el-form-item__content {
      .el-input__inner {
        height: 36px;
        font-size: 16px;
        padding-left: 35px;
      }
    }
    .el-input__prefix {
      .el-input__icon {
        font-size: 18px;
      }
    }
    .el-form-item {
      margin-bottom: 22px;
    }
    .submitBtn {
      width: 100%;
      border-radius: 32px;
      height: 36px;
      font-size: 16px;
      margin-top: 12px;
    }
    .login-top {
      height: 3.8rem;
    }
    .footer {
      height: 3rem;
      line-height: 3rem;
      font-size: 0.75rem;
    }
  }
}
@media screen and (min-width: 1920px) {
  .login {
    .title {
      font-size: 32px;
    }
    .el-tabs__item{
      font-size: 1.5rem
    }
    .yzmbtn {
      height: 54px;
      line-height: 54px;
      font-size: 19px;
    }
    .forgotPwd{
      font-size: 18px;
    }
    .yzmImg{
      flex: none;
      width: 27%;
      height: 4rem;
      margin-left: 10px;
      border-radius: 4px;
    }
    .messageCode-btn{
      font-size: 1.2rem;
      height: 4rem;
    }
    .el-form-item__content {
      .el-input__inner {
        height: 4rem;
        line-height: 4rem;
        font-size: 1.5rem;
        padding-left: 3.5rem;
      }
    }
    .el-input__prefix {
      .el-input__icon {
        font-size: 2rem;
      }
    }
    .el-form-item {
      margin-bottom: 2rem;
    }
    .submitBtn {
      width: 100%;
      border-radius: 32px;
      height: 3.5rem;
      margin-top: 1rem;
      font-size: 1.5rem;
    }
    .login-top {
      height: 7rem;
    }
    .footer {
      height: 6.2rem;
      line-height: 6.2rem;
      font-size: 1rem;
    }
  }
}
// 宽度700px以下的样式（目前来说，只是为了手机端显示）
@media screen and (max-width:700px) {
  .login{
    position: relative;
    height: 100%;
    .main-content{
      .login-content{
        .form-box{
          // 为了手机端加的
          width: 80%;
          min-height: 300px;
        }
      }
    }
    .footer{
      height: 50px;
      line-height: 50px;
      bottom: 10px;
      position: absolute;
      width: 100%;
    }
  }
}
</style>
