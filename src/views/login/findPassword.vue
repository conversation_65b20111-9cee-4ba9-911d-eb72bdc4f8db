<template>
  <div class="find_psd">
    <SystemDialog
      :custom-class=" flag ? 'psdDialogClass': 'undatePswClass'"
      :showDialog.sync="dialogRevise"
      dialogTitle="忘记密码"
      :dialogWidth="'40rem'"
      :dialogTop="'25vh'"
      :needModal="false"
    >
      <el-form :model="data" label-width="120px" :rules="rules" ref="form">
        <el-row key="account">
          <el-col :span="20">
            <el-form-item label="用户名" prop="account">
              <el-input :disabled="!flag" autocomplete="off" v-model="data.account"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="flag" key="verifyCode">
          <el-col :span="20">
            <el-form-item label="手机验证码" prop="verifyCode">
              <el-input autocomplete="off" v-model="data.verifyCode"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!flag" key="newPassword">
          <el-col :span="20">
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                autocomplete="off"
                type="password"
                v-model="data.newPassword"
                :placeholder="placeholder"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!flag" key="newPasswordCheck">
          <el-col :span="20">
            <el-form-item label="确认新密码" prop="newPasswordCheck">
              <el-input
                autocomplete="off"
                type="password"
                v-model="data.newPasswordCheck"
                :placeholder="placeholder"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item>
              <el-button
                v-if="flag"
                border
                :disabled="sendingMsg"
                fillPrimary
                size="small"
                class="sendCode"
                @click="sendCode"
              >{{ verifiMsg }}</el-button>
              <el-button class="nextStep" size="small" @click="next">下一步</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </SystemDialog>
  </div>
</template>

<script>
import SystemDialog from "@/components/SystemDialog"
import {encryption,decryption} from '@/utils/encryption'
import {
  genVerifyCode,
  checkVerifyCode,
  accountandverifycode
} from '@/api/user.js'
export default {
  components: { SystemDialog },
  data() {
    let validcodeName = (rule, value, callback) => {
      if (this.data.newPassword == this.data.newPasswordCheck) {
        return callback()
      } else {
        return callback(new Error('两次输入密码不一致'))
      }
    }
    let validPassword = (rule, value, callback) => {
      if (
        window.g.enablePwdCheck &&
        window.g.pwdCheckRule &&
        window.g.pwdCheckRule.regex
      ) {
        if (!window.g.pwdCheckRule.regex.test(value)) {
          return callback(new Error(window.g.pwdCheckRule.failedMsg))
        } else {
          return callback()
        }
      } else {
        return callback()
      }
    }
    return {
      dialogRevise: false,
      data: {
        account: '',
        verifyCode: '',
        newPasswordCheck: '',
        newPassword: ''
      },
      rules: {
        account: { required: true, message: '请输入用户名', trigger: 'blur' },
        verifyCode: {
          required: true,
          message: '请输入手机验证码',
          trigger: 'blur'
        },
        newPassword: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { validator: validPassword, trigger: 'blur' }
        ],
        newPasswordCheck: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validcodeName, trigger: 'blur' }
        ]
      },
      placeholder: '',
      flag: true,
      countdown: 60, // 倒计时
      verifiMsg: '发送手机验证码',
      sendingMsg: false,
      loop: null,
    }
  },
  methods: {
    // 初始化事件
    init() {
      this.data = {
        account: '',
        verifyCode: '',
        newPasswordCheck: '',
        newPassword: ''
      }
      if(!this.sendingMsg){
        this.verifiMsg = '发送手机验证码'
      }
      this.flag = true
      this.dialogRevise = true
    },

    // 关闭弹框
    exit() {
      this.dialogRevise = false
    },
    //发送验证码
    sendCode() {
      this.$refs.form.validateField('account', (error) => {
        if (!error) {
          this.setTime()
          genVerifyCode({ account: encryption({data:this.data.account,key:'****************'}) }).then((res) => {
            if (res && res.code == '0000' && res.data) {
              this.$message({
                showClose: true,
                message: res.data.msg,
                duration: 3000,
                type: res.data.code == '0000' ? 'success' : 'warning'
              })
            }
          })
        }
      })
    },
    // 短信计时
    setTime() {
      let num = this.countdown
      this.verifiMsg = num + 's'
      let clearTime = this.countdown + 2 // 在此处加二是因为当loop循环还未置空权限和重置文字时
      this.sendingMsg = true // 取消循环的计时已经到了，经试验 粗略+2 可达到设计需求
      // 计时，到时间后取消循环
      setTimeout(() => {
        clearInterval(this.loop)
      }, clearTime + '000')
      // 循环计时
      this.loop = setInterval(() => {
        if (num <= 0) {
          this.sendingMsg = false // 可重新获取短信
          this.verifiMsg = '重新发送'
        } else {
          num--
          this.verifiMsg = num + 's'
        }
      }, 1000)
    },
    //下一步
    next() {
      if (this.flag) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            let req = {
              account: encryption({data:this.data.account,key:'****************'}),
              verifyCode: this.data.verifyCode
            }
            checkVerifyCode(req).then((res) => {
              if (res && res.code == '0000' && res.data) {
                if (res.data.code == '0000') {
                  this.flag = false
                } else {
                  this.$message({
                    showClose: true,
                    message: res.data.msg,
                    duration: 3000,
                    type: 'warning'
                  })
                }
              }
            })
          }
        })
      } else {
        this.$refs.form.validate((valid) => {
          if (valid) {
            let req = {
              account: encryption({data:this.data.account,key:'****************'}),
              newPassword: encryption({data:this.data.newPassword,key:'****************'}),
              verifyCode: this.data.verifyCode
            }
            accountandverifycode(req).then((res) => {
              if (res && res.code == '0000') {
                this.$message({
                  showClose: true,
                  message: '你设置的密码已经修改成功',
                  duration: 3000,
                  type: 'success'
                })
                this.exit()
              }
            })
          }
        })
      }
    }
  }
}
</script>

<style lang='scss'>
@import "~@/styles/variables.scss";
.sendCode,.nextStep {
  padding: 6px 17.5px;
  margin-right: 10px;
  background-color: $color-main;
  border: 1px solid $color-main !important;
  color: #fff;
  border-radius: 20px;
  &:hover{
    background-color: #51BEFF;
    color: #fff;
  }
  
}
.undatePswClass{
  height: 16rem; 
  .is-error{
    margin-bottom: 10px !important;
    .el-form-item__error{
      line-height: 0;
    }
  }
  .el-form-item{
    margin-bottom: 5px;
  }
}
.psdDialogClass{
  height: 14.5rem;  
   .el-input__inner,
  .empDialog .el-input__inner,
  .config-dialog .el-input__inner,
  .cus-dialog .el-input__inner {
    height: 30px;
    min-height: 30px;
  }
  .is-error{
    margin-bottom: 22px !important;
  }
  .el-form-item{
    margin-bottom: 5px;
  }
}
</style>