<template>
  <div class="login">
    <el-row class="login-top">
      <img src="../../assets/login_imges/login_logo.png" alt="">
    </el-row>
    <div>
      <el-row class="login-content">
      <div class="form-box">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on">
          <div class="title-container">
            <span class="title">欢 迎 登 录</span>
            <div class="short-line"></div>
          </div>

          <el-form-item prop="username">
            <el-input ref="username" v-model="loginForm.username" placeholder="请输入用户名称" name="username" type="text"
              autocomplete="off" prefix-icon="iconfont icon-yonghuming" />
          </el-form-item>

          <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
            <el-form-item prop="password">
              <el-input :key="passwordType" ref="password" v-model="loginForm.password" :type="passwordType"
                placeholder="请输入用户密码" name="password" autocomplete="off" @keyup.native="checkCapslock"
                @blur="capsTooltip = false" @keyup.enter.native="handleLogin" prefix-icon="iconfont icon-mima" />
            </el-form-item>
          </el-tooltip>
          <el-form-item  prop="code" class="yzm-container">
            <div class="yzm-input">
              <el-input v-model="loginForm.code" name="yzm" placeholder="请输入验证码" type="text" autocomplete="off"
                prefix-icon="iconfont icon-yanzhengma">
              </el-input>
            </div>
            <div v-if="codeBtnFlog" class="yzmbtn" @click="getPicCode">获取验证码</div>
            <img v-else  class="yzmImg" :src="this.verificationImg" alt="获取验证码" @click="getPicCode" style="margin-left: 17px;">
          </el-form-item>

          <el-button :loading="loading" type="primary" class="submitBtn"
            @click.native.prevent="handleLogin">登 录</el-button>
        </el-form>
      </div>
    </el-row>
    </div>
    <el-row class="footer">
      Copyright by@中国移动通信集团江西有限公司
    </el-row>
  </div>
</template>

<script>
import { encryption } from '@/utils/encryption'
import { getPicCodeService,getUserMenuService } from '@/api/user'
import store from "@/store";
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: 'admin',
        password: '123456',
        code: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: '请填写用户名！' }],
        password: [{ required: true, trigger: 'blur', message: '请填写密码！' }],
        code:  [{ required: true, trigger: 'blur', message: '请填写验证码！' }],
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      redirect: undefined,
      otherQuery: {},
      verificationImg: '',
      codeBtnFlog: false, // 验证码文字和图片切换控制
    }
  },
  computed: {
    msgShow: {
      get() {
        return window.g?.loginType || 'account_pwd'
      }
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    this.getPicCode()
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
    window.addEventListener('keydown', this.addEnterHandle)
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.addEnterHandle)
  },
  methods: {
    // 点击回车登录的事件
    addEnterHandle() {
      let e = event || window.event
      if (e.keyCode === 13) {
        // 回车则执行登录方法
        this.handleLogin()
      }
    },
    checkCapslock(e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    // 请求图片验证码
    getPicCode() {
      this.randData = this.randomString();
      getPicCodeService({
        randomStr: encryption({ data: this.randData, key: "****************" }),
      }).then((res) => {
        this.codeBtnFlog = false
        this.verificationImg = window.URL.createObjectURL(res.data);
      });
    },
    //随机产生四位验证码
    randomString() {
      let e = 4;
      var t = "1234567890",
        a = t.length,
        n = "";
      for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
      return n;
    },
    // 点击登录按钮
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          const client_id = window.g.client_id
          const client_secret = window.g.client_secret
          const loginParams = {
            grant_type: this.msgShow,
            scope: 'server',
            platformId: window.g?.platformId || 'eoms',
            client_id: encryption({ data: client_id, key: '****************' }),
            client_secret: encryption({ data: client_secret, key: '****************' }),
            account: encryption({ data: this.loginForm.username, key: '****************' }),
            password: encryption({ data: this.loginForm.password, key: '****************' }),
            code: this.loginForm.code,
            randomStr: encryption({
                    data: this.randData,
                    key: "****************",
                  }),
          }
          this.$store.dispatch('user/login', loginParams)
            .then(() => {
              this.getMenu()
              this.loading = false
            })
            .catch(() => {
              this.getPicCode();
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    getMenu(){
      //获取当前用户菜单
                getUserMenuService({
                  queryAll: true,
                  rootId: 'root-jx-fz',
                  type: '0'
                }).then(async(res) => {
                  if (res && res.data) {
                    sessionStorage.setItem(
                      'private_menu_pts',
                      JSON.stringify(res.data)
                    )
                    // 对于新登录的用户，如果有redirect的，需要检查redirect是否有页面权限,有则进入到redirect页面，没有就显示首页
                    this.$router.push({path: '/', query: this.otherQuery})
                  } else {
                    this.$message({
                      showClose: true,
                      message: '用户不存在！',
                      type: 'warning'
                    })
                    this.getPicCode();
                  }
                })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";
.login {
  height: 100vh;
  width: 100%;
  background-color: #fff;
  overflow: hidden;

  .login-top {
    height: 12%;
    display: flex;
    align-items: center;

    img {
      margin-left: 7%;
      height: 70%;
    }
  }
  .login-content {
    height: 76.2vh;
    background: url('../../assets/login_imges/login_bc.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    .form-box {
      position: absolute;
      right: 8%;
      top: 10%;
      width: 34%;
      height: 80%;
      background-color: rgba(255, 255, 255, 0.5);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      .login-form {
        width: 92%;
        height: 92%;
        border: 10px solid rgba(255, 255, 255, .5);
        border-radius: 10px;
        padding: 2% 30px;
        box-shadow: 0px 7px 10px 0px rgba(183, 198, 206, 0.57);
        background: #FFFFFF no-repeat;
        text-align: center;

        .title-container {
          width: 100%;
          .title {
            font-weight: 500;
            color: #181818;
            font-size: 24px;
          }
          .short-line {
            width: 50px;
            border-radius: 3px;
            background-color: #0085ff;
            height: 3px;
            margin: 10px auto 3%;
          }
        }

        .yzm-container {
          .el-form-item__content {
            display: flex;
            justify-content: space-between;
            .yzmbtn {
              background: rgba(203, 241, 254, 0.8);
              border-radius: 4px;
              text-align: center;
              color: #3987FC;
              padding: 0 15px;
              cursor: pointer;
              margin-left: 17px;
            }

            .yzm-input {
              flex: 2;
            }
          }
        }
      }
    }
  }
  .el-form-item__error{
    font-size: 1rem
  }
  .footer {
    height: 11.8%;
    text-align: center;
    line-height: 11vh;
    color: #888888;
    font-size: 1rem;
    font-weight: 400;
  }
  .login-error-msg{
    color: $wf-color-danger;
  }
}
@media screen and (min-width: 1366px){
  .login{
    .el-form-item__error{
      font-size: 0.8rem
    }
  }
  .login{
    .title{
    font-size: 20px;
  }
  .yzmbtn{
    height: 36px;
    line-height: 36px;
    font-size: 16px;
  }
  .yzmImg{
    height: 36px;
    width: 40%;
  }
  .el-input__prefix {
    color: #33ACFB;
    top: 0;
    .el-input__icon{
      font-size: 16px;
    }
  }
  .el-input--prefix{
    .el-input__inner{
      height: 36px;
      font-size: 16px;
      padding-left: 35px;
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .submitBtn {
    width: 100%;
    border-radius: 32px;
    height: 36px;
    font-size: 16px;
    margin-top: 12px;
  }
  .title-container{
    margin-bottom: 20px;
  }
  }
}
@media screen and (min-width: 1920px){

  .login{
    .title{
    font-size: 32px;
  }
  .yzmbtn{
    height: 54px;
    line-height: 54px;
    font-size: 19px;
  }
  .yzmImg{
    height: 54px;
    width: 40%;
  }
  .el-input__prefix {
    color: #33ACFB;
    top: 0.2rem;
    margin: 0 0.5rem;
    .el-input__icon{
      font-size: 2rem;
    }
  }
  .el-input--prefix{
    .el-input__inner{
      height: 4rem;
      line-height: 4rem;
      font-size: 1.5rem;
      padding-left: 3.5rem;
    }
  }
  .el-form-item {
    margin-bottom: 2rem;
  }
  .submitBtn {
    width: 100%;
    border-radius: 32px;
    height: 3.5rem;
    margin-top: 1rem;
    font-size: 1.5rem;
  }
  .title-container{
    margin-bottom: 2rem;
  }
  }
}
</style>
