<!--  月安全工作记录模块 -->
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      :form="formModel"
      @search="search"
      @reset="reset"
      @changeSelect="changeSelect"
    ></mssSearchForm>

    <mssCard title="月安全工作记录">
      <div slot="headerBtn">
        <el-button v-if="powerData.safety_work_record_add" type="primary" @click="actionHandle('','add')">新增</el-button>
        <el-button v-if="powerData.safety_work_record_delete" @click="actionHandle('','delete')">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :selection="powerData.safety_work_record_delete"
          :api="api"
          :columns="columns"
          :static-search-param="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { queryAreaListService } from '@/api/common_api'
import { queryListService, worksafetyDeleteService } from '@/api/monthly_report/safety_work_record.js'
export default {
  name: 'SafetyWorkRecord',
  data() {
    const validateCreateStartEnd = (rule, value, callback) => {
      if (this.$refs.searchForm.searchForm.createStart && this.$refs.searchForm.searchForm.createEnd) {
        if (new Date(this.$refs.searchForm.searchForm.createStart).getTime() > new Date(this.$refs.searchForm.searchForm.createEnd).getTime()) {
          callback(new Error('开始日期需小于结束日期'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      powerData:[],
      formModel: {
        configtypecode: '',
        cityId: '',
        countyId: '',
        createStart: '',
        createEnd: ''
      },
      cityType: sessionStorage.getItem('firstDeptId'), // 省公司、地市用户
      searchConfig: [
        {
          label: '类型名称',
          type: 'select',
          fieldName: 'configtypecode',
          options: []
        },
        {
          label: '地市',
          type: 'select',
          fieldName: 'cityId',
          options: [],
          disabled: false
        },
        {
          label: '区县',
          type: 'select',
          fieldName: 'countyId',
          options: []
        },
        {
          label: '查询日期',
          type: 'date2',
          fieldName: 'createStart',
          fieldName2: 'createEnd',
          span: 12,
          rules: [
            { validator: validateCreateStartEnd, trigger: ['blur', 'change'] }
          ]
        }
      ],
      api: queryListService,
      staticSearchParam: {},
      columns: [
        {
          label: '年',
          prop: 'safetyDate',
          formatter: (row) => {
            return this.$moment(row.safetyDate).format('yyyy')
          }
        },
        {
          label: '月',
          prop: 'safetyDate',
          formatter: (row) => {
            return this.$moment(row.safetyDate).format('MM')
          }
        },
        {
          label: '类型名称',
          prop: 'configTypeName'
        },
        {
          label: '记录填报人',
          prop: 'creatorName'
        },
        {
          label: '填报时间',
          prop: 'createTime'
        },
        {
          label: '操作',
          prop: '_operationCol',
          formatter: row => {
            if(this.powerData.safety_work_record_update&&(this.cityType=='1'?row.configTypeCode!='Cooperativecard'&&row.configTypeCode!='penaltypoints'&&row.configTypeCode!='partnerassessment'&&row.configTypeCode!='Systemimp'&&row.configTypeCode!='awards': true)){
              return (
              <span><a 
               class='table_btn mr10'
               onClick={() => { this.actionHandle(row.id, 'edit')}}
              >编辑</a>
              <a class='table_btn' onClick={() => { this.actionHandle(row.id, 'view')}}
              >查看</a>
              </span>)
            }else{
              return <a class='table_btn' onClick={() => { this.actionHandle(row.id, 'view')}}
              >查看</a>
            }
          }
        }
      ]
    }
  },
  async created() {
    this.getPower()
    this.getAreaList('city', { parentId: '-2', typeCode: 'area' })
    if (this.cityType !== '1') {
      this.$set(this.formModel, 'cityId', this.cityType)
      this.$set(this.searchConfig[1], 'disabled', true)
      this.getAreaList('county', { parentId: this.cityType, typeCode: 'area' })
      this.staticSearchParam = this.formModel
    }
    await this.$set(this.searchConfig[0], 'options', await this.$dictOptions({ parentValue: 'worknoteType', appCode: '001001' }))
  },
  methods: {
    getPower(){
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item)=>{
        this.powerData[item.authority]=true
      })
    },
    getAreaList(type, params) {
      queryAreaListService(params).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          if (type === 'city') {
            this.$set(this.searchConfig[1], 'options', list)
          } else {
            this.$set(this.searchConfig[2], 'options', list)
          }
        }
      })
    },
    changeSelect(name, val) {
      if (name === 'cityId') {
        this.$refs.searchForm.searchForm['countyId'] = ''
        this.getAreaList('country', { parentId: val, typeCode: 'area' })
      }
    },
    // 点击操作列的按钮，处理或者查看单子
    actionHandle(id, type) {
      const name = type === 'view' ? '详情页' : type === 'add' ? '新增页' : '编辑页'
      if (type === 'edit' || type === 'add' || type === 'view') {
        this.$router.push({
          path: '/monthly_report/config_manage/safety_work_record/edit',
          query: {
            id,
            type,
            pathlabel: encodeURIComponent(`月安全工作记录${name}`)
          }
        })
      } else {
      // 删除
        this.delInfo()
      }
    },
    delInfo() {
      const selection = this.$refs.table.multipleSelection
      const ids = []
      if (selection.length > 0) {
        selection.forEach(item => {
          ids.push(item.id)
        })
        this.$confirm('是否确认删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          worksafetyDeleteService({ ids: ids.join(',') }).then(res => {
            if (res.code === '0000') {
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
              this.$refs.table.getTableData()
            }
          })
        }).catch(() => {})
      }else{
        this.$message.warning('请勾选')
      }
    },
    search(searchData) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      if (this.cityType == 1) {
        this.$set(this.formModel, 'cityId', '')
        this.$refs.searchForm.searchForm['cityId'] = ''
      } else {
        this.$refs.searchForm.searchForm['cityId'] = this.cityType
      }
      this.search({ ...searchData, cityId: this.formModel.cityId })
    }
  }
}
</script>
