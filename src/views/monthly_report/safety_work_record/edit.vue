<template>
  <div v-loading="pageLoading" class="page-anchor-parentpage safety_work_record">
    <div class="operate-btn">
      <el-button v-if="dealPage" type="primary" @click="save">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>

    <!-- <div id="sectionBasic" class="page-anchor-point"></div> -->
    <mssCard title="基本信息">
      <div slot="content" class="common-form">
        <el-form
          ref="form"
          :model="modelForm"
          :disabled="disableForm"
          :rules="rules"
          :class="labelPosition=='left'?'detail':'edit'"
          :label-position="labelPosition"
        >
          <el-row style="width: 100%;">
            <template v-for="(item, index) in basicConfig">
              <el-col
                v-if="item.show||true"
                v-show="!item.hide"
                :key="index"
                :span="item.span || 12"
                :offset="item.offset || 0"
              >
                <el-form-item
                  :ref="item.prop"
                  :label="item.label"
                  :class="item.class"
                  :prop="item.prop"
                  :rules="item.rules"
                  :title="item.title"
                >
                  <div
                    v-if="item.type === 'input' && labelPosition=='left'"
                    style="background-color: #fff;color: #666;margin-left: 10px;"
                  >{{ modelForm[item.prop] }}</div>
                  <el-input
                    v-if="item.type === 'input' && labelPosition!='left'"
                    v-model="modelForm[item.prop]"
                    :type="item.mode || 'text'"
                    :maxlength="item.maxlength"
                    :show-word-limit="item.showWordLimit"
                    :show-password="item.showPassword"
                    :clearable="item.clearable"
                    :prefix-icon="item.prefixIcon"
                    :suffix-icon="item.suffixIcon"
                    :rows="item.rows || 2"
                    :autosize="item.autosize"
                    :resize="item.resize"
                    :disabled="item.disabled"
                    :readonly="item.readonly || false"
                    :placeholder="item.placeholder || ''"
                    v-on="item.eventListeners"
                  />
                  <!-- 下拉选择 -->
                  <el-select
                    v-if="item.type === 'select'"
                    v-model="modelForm[item.prop]"
                    :placeholder="item.placeholder || ''"
                    filterable
                    :clearable="item.clearable"
                    :value-key="item.itemValue ? item.itemValue : 'id'"
                    :multiple="item.multiple"
                    :multiple-limit="item.multipleLimit"
                    :collapse-tags="item.collapseTags"
                    :disabled="item.disabled"
                    v-on="item.eventListeners"
                  >
                    <el-option
                      v-for="option in item.options"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                      :disabled="option.disabled"
                    />
                  </el-select>

                  <!-- 日期选择 -->
                  <div v-if="item.type === 'datePicker'">
                    <el-date-picker
                      v-model="modelForm[item.prop]"
                      :placeholder="item.placeholder"
                      :type="item.dateType || 'date'"
                      :format="item.format || 'yyyy-MM-dd'"
                      :value-format="item.valueFormat || 'yyyy-MM-dd'"
                      :disabled="item.disabled"
                      :picker-options="item.pickerOptions"
                      :clearable="item.clearable"
                      :readonly="item.readonly || false"
                      v-on="item.eventListeners"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <template v-if="modelForm['configTypeCode']==='partnerassessment'">
            <el-form
              v-for="(ele,i) in companyArr"
              :ref="'partner'+[i]"
              :key="penaltiesFormModel[i].frontId"
              :model="penaltiesFormModel[i]"
              :disabled="disableForm"
              :class="labelPosition=='left'?'detail':'edit'"
              :label-position="labelPosition"
            >
              <el-row>
                <el-col v-for="(item, index) in ele" :key="index" :span="item.span">
                  <el-form-item
                    :ref="item.prop"
                    :label="item.label"
                    :class="item.class"
                    :prop="item.prop"
                    :rules="item.rules"
                    :title="item.title"
                  >
                    <div
                      v-if="item.type === 'input' && labelPosition=='left'"
                      style="background-color: #fff;color: #666;margin-left: 10px;"
                    >{{ penaltiesFormModel[i][item.prop] }}</div>
                    <el-input
                      v-if="item.type === 'input' && labelPosition!='left'"
                      v-model="penaltiesFormModel[i][item.prop]"
                      :readonly="item.focus"
                      :type="item.type"
                      @focus="()=>{
                        item.focus&&openDeptSelectDialog(penaltiesFormModel[i])
                      }"
                      @blur="()=>{
                        item.blur&&handleTotalPenaltiesAmount(item)
                      }"
                    ></el-input>
                    <div v-if="!disableForm" class="muli-btn">
                      <el-button v-show="index/2" size="mini" circle type="primary" icon="el-icon-plus" @click="penaltiesPlus"></el-button>
                      <el-button v-show="index/2 && i!==0" size="mini" circle type="primary" icon="el-icon-minus" @click="()=>{penaltiesMinus(i)}"></el-button>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </template>
        </el-form>
      </div>
    </mssCard>
    <mssAttachment v-if="boId" :bo-id="boId" :business-type="workflowCode" :deal-page="dealPage"></mssAttachment>
    <!-- 部门弹框 -->
    <mssChooseDept ref="chooseDept" :mult-select="false" @showCheckList="showCheckList"></mssChooseDept>
  </div>
</template>

<script>
import { updateService, getInfoService } from '@/api/monthly_report/safety_work_record.js'
import { queryAreaListService } from '@/api/common_api'
export default {
  name: 'SafetyWorkRecordEdit',
  data() {
    return {
      pageLoading: false,
      modelForm: { cityId: '', countyId: '', stats: '', totalPenaltiesAmount: '' },
      rules: {},
      configTypeList: [],
      cityList: [],
      countyList: [],
      type: 'Implementation', //  根据不同类型显示不同表单字段
      boId: '',
      workflowCode: 'worksafetyrecord',
      cityType: sessionStorage.getItem('firstDeptId'),
      disableForm: false,
      labelPosition: 'top',
      dealPage: true,
      colon: '',
      companyArr: [],
      penaltiesFormModel: [],
      companyList: [], // 单位
      penaltiesItem: []// 某列
    }
  },
  computed: {
    basicConfig() {
      return [
        {
          label: '类型名称' + this.colon,
          type: 'select',
          prop: 'configTypeCode',
          options: this.configTypeList,
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          eventListeners: {
            change: (val) => this.handleConfigTypeChange(val)
          }
        },
        {
          label: '属性' + this.colon, // 省级or市级
          type: 'input',
          prop: 'stats',
          span: 12,
          disabled: true,
          show: this.type === 'Implementation' || this.type === 'Educationtraining' || this.type === 'Emergencyplans'
        },
        {
          label: '地市' + this.colon, // 省级选 市级直接显示
          type: 'select',
          prop: 'cityId',
          options: this.cityList,
          eventListeners: {
            change: (val) => { this.cityChange(val) }
          },
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          disabled: this.cityType !== '1'
        },
        {
          label: '区县' + this.colon, // 市级 选
          type: 'select',
          prop: 'countyId',
          options: this.countyList,
          span: 12,
          show: this.cityType !== '1'
        },
        // 1）类型名称：制度落地 Implementation start
        {
          label: '建立安全相关制度数量' + this.colon,
          type: 'input',
          mode: 'number',
          prop: 'numTwo',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Implementation'
        },
        {
          label: '修订安全相关制度数量' + this.colon,
          type: 'input',
          prop: 'numOne',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Implementation'
        },
        {
          label: '废除安全相关制度数量' + this.colon,
          type: 'input',
          prop: 'numThree',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Implementation'
        },

        // 1）类型名称：制度落地 end

        // 2）类型名称：教育培训 Educationtraining start
        {
          label: '开展安全生产教育培训次数' + this.colon,
          type: 'input',
          mode: 'number',
          prop: 'numOne',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Educationtraining'
        },
        {
          label: '参加安全生产教育培训人次' + this.colon,
          type: 'input',
          prop: 'numTwo',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Educationtraining'
        },
        {
          label: '培训时间' + this.colon,
          type: 'datePicker',
          prop: 'safetyDate',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Educationtraining'
        },
        // 2）类型名称：教育培训end

        // 3）类型名称：合作单位考核处罚-考核扣款 start
        {
          label: '处罚月份' + this.colon,
          type: 'datePicker',
          dateType:'month',
          prop: 'safetyDate',
          format:'yyyy-MM',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'partnerassessment' || this.type === 'penaltypoints' || this.type === 'Cooperativecard'
        },
        {
          label: '合计处罚次数' + this.colon,
          type: 'input',
          prop: 'numOne',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'partnerassessment'
        },
        {
          span: 24,
          formSlot: true
        },
        {
          label: '处罚合作单位家数' + this.colon,
          type: 'input',
          prop: 'numTwo',
          disabled: true,
          show: this.type === 'partnerassessment'
        },
        {
          label: '罚金总额（万元）' + this.colon,
          type: 'input',
          prop: 'totalPenaltiesAmount',
          disabled: true,
          show: this.type === 'partnerassessment'
        },
        {
          label: '处罚合作单位其他方式说明' + this.colon,
          type: 'input',
          mode: this.disableForm ? 'text' : 'textarea',
          prop: 'content',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 24,
          show: this.type === 'partnerassessment'
        },
        // 3）类型名称：合作单位考核处罚-考核扣款 end

        // 4）类型名称：合作单位考核处罚-后评估扣分 start
        // 5）类型名称：合作单位考核处罚-红黄牌 start
        {
          label: '本月新增' + this.colon,
          type: 'input',
          prop: 'numOne',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'penaltypoints' || this.type === 'Cooperativecard'
        },
        {
          label: '累计' + this.colon,
          type: 'input',
          prop: 'numTwo',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'penaltypoints' || this.type === 'Cooperativecard'
        }, {
          label: '具体描述' + this.colon,
          type: 'input',
          mode: 'textarea',
          prop: 'content',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 24,
          show: this.type === 'penaltypoints' || this.type === 'Cooperativecard'
        },
        // 6）类型名称：应急预案及演练
        {
          label: '新建立应急预案数量' + this.colon,
          type: 'input',
          prop: 'numOne',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Emergencyplans'
        },
        {
          label: '新修订应急预案数量' + this.colon,
          type: 'input',
          prop: 'numTwo',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Emergencyplans'
        },
        {
          label: '开展应急预案演练次数-专项应急预案' + this.colon,
          type: 'input',
          prop: 'numThree',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.cityType != 1 && this.type === 'Emergencyplans'
        },
        {
          label: '开展应急预案演练次数-现场处置方案' + this.colon,
          type: 'input',
          prop: 'numFour',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.cityType != 1 && this.type === 'Emergencyplans'
        },
        {
          label: '参加应急预案演练人次-专项应急预案' + this.colon,
          type: 'input',
          prop: 'numFive',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.cityType != 1 && this.type === 'Emergencyplans'
        },
        {
          label: '参加应急预案演练人次-现场处置方案' + this.colon,
          type: 'input',
          prop: 'numSix',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.cityType != 1 && this.type === 'Emergencyplans'
        },
        {
          label: '开展演练的专业场景' + this.colon,
          type: 'input',
          prop: 'numSeven',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.cityType != 1 && this.type === 'Emergencyplans'
        },
        {
          label: '日期' + this.colon,
          type: 'datePicker',
          prop: 'safetyDate',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Emergencyplans' || this.type === 'Combatexercises'
        }, {
          label: this.cityType == 1 ? '处罚合作单位其他方式说明' + this.colon : '说明' + this.colon,
          type: 'input',
          mode: 'textarea',
          prop: 'content',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 24,
          show: this.type === 'Emergencyplans'
        },
        // 7）类型名称：比武实操
        {
          label: '开展比武实操次数' + this.colon,
          type: 'input',
          prop: 'numOne',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Combatexercises'
        },
        {
          label: '参加比武实操人次' + this.colon,
          type: 'input',
          prop: 'numTwo',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Combatexercises'
        },
        {
          label: '开展比武的专业场景' + this.colon,
          type: 'input',
          prop: 'numThree',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Combatexercises'
        },
        // 8）类型名称：获奖情况
        {
          label: '安全生产领域获奖情况（省部级及以上）' + this.colon,
          type: 'input',
          prop: 'numTwo',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'awards'
        },
        {
          label: '日期' + this.colon,
          type: 'datePicker',
          prop: 'safetyDate',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Implementation' || this.type === 'awards' || this.type === 'Reportsituation' || this.type === 'Systemimp'
        },
        {
          label: '获奖情况' + this.colon,
          type: 'input',
          mode: 'textarea',
          prop: 'content',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 24,
          show: this.type === 'awards'
        },

        // 9）类型名称：通报情况
        {
          label: '管局通报次数' + this.colon,
          type: 'input',
          prop: 'numOne',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Reportsituation'
        },
        {
          label: '集团通报次数' + this.colon,
          type: 'input',
          prop: 'numTwo',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Reportsituation'
        }, {
          label: '省公司通报次数' + this.colon,
          type: 'input',
          prop: 'numThree',
          mode: 'number',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'Reportsituation'
        },
        {
          label: '通报情况' + this.colon,
          type: 'input',
          mode: 'textarea',
          prop: 'content',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 24,
          show: this.type === 'Reportsituation'
        },
        // 10）类型名称：制度落实
        {
          label: '详细情况' + this.colon,
          type: 'input',
          mode: 'textarea',
          prop: 'content',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 24,
          show: this.type === 'Systemimp'
        },

        {
          label: '开展情况' + this.colon,
          type: 'input',
          mode: 'textarea',
          prop: 'content',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 24,
          show: this.type === 'Implementation' || this.type === 'Educationtraining' || this.type === 'Combatexercises'
        }
      ]
    },
    penalizeItem() {
      return [
        {
          label: '处罚合作单位名称' + this.colon,
          type: 'input',
          prop: 'partnerName',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          show: this.type === 'partnerassessment',
          focus: true
        },
        {
          label: '金额（万元）' + this.colon,
          type: 'input',
          mode: 'number',
          prop: 'amount',
          rules: this.disableForm?[]:[
            { required: true, message: '请输入', trigger: 'blur,change' }
          ],
          span: 12,
          blur: true,
          class: 'hasbtn',
          show: this.type === 'partnerassessment'
        }
      ]
    }
  },
  async created() {
    this.modelForm = {
      stats: this.cityType === '1' ? '省级' : '地市级',
      cityId: this.cityType === '1' ? '' : this.cityType
    }

    this.getAreaList('city', { parentId: '-2', typeCode: 'area' })
    if (this.cityType !== '1') {
      this.getAreaList('county', { parentId: this.cityType, typeCode: 'area' })
    }

    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || null
    this.configTypeList = await this.$dictOptions({ parentValue: 'worknoteType', appCode: 'ImplementationImplementation' })

    if (this.id) {
      this.boId = this.id
      this.getInfo()
    }else{
      // 处理考核类型-省公司
      if(this.cityType=='1'){
        let arr = []
        this.configTypeList.forEach(ele => {
          if(ele.value!='Cooperativecard'&&ele.value!='penaltypoints'&&ele.value!='partnerassessment'&&ele.value!='Systemimp'&&ele.value!='awards'){
            arr.push(ele)
          }
        });
        this.configTypeList = arr;
      }
    }
    const type = this.$route.query.type
    if (type === 'view') {
      this.disableForm = true
      this.labelPosition = 'left'
      this.dealPage = false
      this.colon = '：'
    }
  },
  methods: {
    getAreaList(type, params) {
      queryAreaListService(params).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          if (type === 'city') {
            this.cityList = list
          } else {
            this.countyList = list
          }
        }
      })
    },
    cityChange(val) {
      this.modelForm['countyId'] = ''
      if (this.cityType !== '1') {
        this.getAreaList('county', { parentId: val, typeCode: 'area' })
      }
    },
    handleConfigTypeChange(val) {
      this.type = val
      if (this.type === 'partnerassessment') {
        this.companyArr = []
        this.penaltiesFormModel = []
        this.penaltiesFormModel.push({ frontId: this.penaltiesFormModel.length + 1, partnerName: '', partnerId: '', amount: '' })
        this.companyArr.push(this.penalizeItem)
        this.modelForm['numTwo'] = this.penaltiesFormModel.length
        this.handleTotalPenaltiesAmount()
      } else {
        this.companyArr = []
        this.penaltiesFormModel = []
      }
    },
    getInfo() {
      getInfoService({ id: this.id }).then(res => {
        if (res.code === '0000') {
          this.modelForm = res.data
          this.type = this.modelForm.configTypeCode
          if (res.data.penalties.length) {
            res.data.penalties.forEach(item => {
              this.companyArr.push(this.penalizeItem)
              this.penaltiesFormModel.push(item)
            })
          }
        }
      })
    },
    save() {
      let flag = true
      this.$refs.form.validate((valid) => {
        if (valid) {
          flag = true
        } else {
          flag = false
        }
      })
      if (this.companyArr.length) {
        try {
          this.companyArr.forEach((item, index) => {
            this.$refs['partner' + index][0].validate((valid) => {
              if (!valid) {
                throw new Error('请选择')
              }
            })
          })
        } catch (error) {
          flag = false
        }
      }
      if (flag) {
        // 获取类型名称
        let text = ''
        this.configTypeList.forEach(item => {
          if (item.value === this.modelForm.configTypeCode) {
            text = item.label
          }
        })
        const req = {
          ...this.modelForm,
          configTypeName: text,
          penalties: this.modelForm.configTypeCode == 'partnerassessment' ? this.penaltiesFormModel : []
        }
        updateService(req).then(res => {
          if (res.code === '0000') {
            if (this.boId) {
              this.goBack()
            } else {
              this.boId = res.data
              this.modelForm['id'] = res.data
            }
            this.$message.success('保存成功')
          }
        })
      }
    },
    penaltiesPlus() {
      this.penaltiesFormModel.push({ frontId: this.penaltiesFormModel.length + 1, partnerName: '', partnerId: '', amount: '' })
      this.companyArr.push(this.penalizeItem)
      this.modelForm['numTwo'] = this.penaltiesFormModel.length
    },
    penaltiesMinus(i) {
      this.companyArr.splice(i, 1)
      this.penaltiesFormModel.splice(i, 1)
      this.modelForm['numTwo'] = this.penaltiesFormModel.length
      this.handleTotalPenaltiesAmount()
    },
    openDeptSelectDialog(row) {
      this.penaltiesItem = row
      const item = []
      if (row.partnerName) {
        item.push({
          text: row.partnerName ? row.partnerName : '',
          id: row.partnerId ? row.partnerId : ''
        })
      }
      const deptParams = { conditionType: 'p_cooperation' }
      this.$refs.chooseDept.init(item, deptParams)
    },
    // 回显单位
    showCheckList({ checkList }) {
      this.companyList = checkList
      // 根据合作单位id查询合作单位信息
      const names = []
      const ids = []
      checkList.forEach(item => {
        names.push(item.text)
        ids.push(item.id)
      })

      this.penaltiesFormModel = this.penaltiesFormModel.map((item) => {
        if (this.penaltiesItem.id && item.id === this.penaltiesItem.id) {
          return { ...item, partnerName: names.join(','), partnerId: ids.join(',') }
        } else if (this.penaltiesItem.frontId && item.frontId === this.penaltiesItem.frontId) {
          return { ...item, partnerName: names.join(','), partnerId: ids.join(',') }
        } else {
          return item
        }
      })
    },
    // 处理-罚金总额
    handleTotalPenaltiesAmount() {
      const count = this.penaltiesFormModel.reduce((pre, cur) => {
        if (cur.amount) {
          return pre + parseFloat(cur.amount)
        } else {
          return pre
        }
      }, 0)
      this.$set(this.modelForm, 'totalPenaltiesAmount', count)
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/monthly_report/config_manage/safety_work_record'
      })
    }
  }
}
</script>
<style lang="scss">
.safety_work_record{
.hasbtn{
  .el-input{
    width: 80%;
  }
  .muli-btn{
    display: inline-block;
    margin-left: 20px;
  }
}
}
</style>

