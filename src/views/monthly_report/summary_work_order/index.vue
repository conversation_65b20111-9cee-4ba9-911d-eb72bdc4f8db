
<template>
  <div>
    <mssSearchForm ref="searchForm" :search-config="searchConfig" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :selection="false"
          :api="api"
          :columns="columns"
          :static-search-param="staticSearchParam"
          border
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { findPageService } from '@/api/monthly_report/summary_work_order_api.js'
export default {
  name: 'ReportSummaryWorkOrderList',
  data() {
    return {
      searchConfig: [
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        }
      ],
      api: findPageService,
      staticSearchParam: {},
      columns: [
        {
          label: '任务名称',
          prop: 'name',
          tooltip:true,
        },
        {
          label: '模板名称',
          prop: 'reportTemplateCityName',
          tooltip:true,
        },
        {
          label: '地市',
          prop: 'cityName',
          tooltip:true,
          width:100,
        },
        {
          label: '上报人',
          prop: 'reporterName',
          tooltip:true,
          width:100,
        },
        {
          label: '任务派发时间',
          prop: 'createDate',
          tooltip: true,
          width:150,
        },
        {
          label: '状态',
          prop: 'status',
          tooltip:true,
          width:100,
        },
        {
          label: '操作',
          prop: '_operationCol',
          width:100,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn"
                  onClick={() => {this.action(row, "details")}}
                >
                查看
                </span>
              </span>
            );
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      this.$set(
        this.searchConfig[1],
        'options',
        await this.$dictOptions({
          parentValue: 'wrokStatusAll',
          appCode: 'PUB'
        })
      )
      this.$set(this.$refs.searchForm.searchForm, 'status', 'all')
    },
    // 点击操作列的按钮，处理或者查看单子
    action(row,type) {
      this.$router.push({
        path: '/monthly_report/work_reporting_summary/work_order_edit',
        query: {
          boId:row.id,
          reportTemplateCityCode:row.reportTemplateCityCode,
          boType:'reportcity',
          operateType:type,
          pathlabel: encodeURIComponent(`月工作上报汇总工单${type=='edit'?'处理页':'详情页'}`),
        }
      })
    },
    // 查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    // 重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    }
  }
}
</script>
