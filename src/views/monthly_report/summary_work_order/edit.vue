<template>
  <div>
    <div class="operate-btn">
      <el-button v-if="boId && dealPage" type="primary" @click="submit">提交</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm"
          :disable-form="true"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionTaskDetails" class="page-anchor-point"></div>
    <mssCard title="任务详情">
      <div slot="headerBtn">
        <el-button v-if="templateCode!='safetyfinal'" @click="viewHis">历史填报信息查看</el-button>
        <el-button v-if="disableSave && templateCode=='safetyfinal'" type="primary" @click="save">保存</el-button>
      </div>
      <div slot="content">
        <template v-if="reports.length">
          <mssReportTemplate
            v-if="templateCode == 'safetyfinal'"
            :ref="templateCode"
            :yearMonth="yearMonth"
            :templateCode="templateCode"
            :reporterId="reports[0].reporterId"
            :isView="!disableSave"
            :cityId="cityId"
            :cityName="cityName"
            :boId="reports[0].reportCityId"
            :boType="'reportcity'"
          ></mssReportTemplate>
          <el-tabs v-else v-model="activeName">
            <el-tab-pane
              v-for="tab in reports"
              :key="tab.id"
              :label="tab.reportTemplateName + '-' +tab.reporterName"
              :name="tab.id"
              lazy
            >
              <mssReportTemplate
                :ref="tab.id"
                :yearMonth="yearMonth"
                :templateCode="tab.reportTemplateCode"
                :reporterId="tab.reporterId"
                :cityName="cityName"
                :isView="!disableSave"
                :reporterNameFlg="false"
                :boId="tab.reportCityId"
                :reportTemplageTab="tab"
                :boType="'reportcity'"
              ></mssReportTemplate>
            </el-tab-pane>
          </el-tabs>
        </template>
        <template v-else>
          <mssReportTemplate
            v-if="templateCode"
            ref="supervisersum"
            :yearMonth="yearMonth"
            :cityName="cityName"
            :templateCode="templateCode"
            :reporterId="reporterId"
            :isView="!disableSave"
            :boId="boId"
            :boType="'reportcity'"
          ></mssReportTemplate>
        </template>
      </div>
    </mssCard>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :boId="boId" :workflowCode="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :saveMainForm="disableSave && templateCode =='safetyfinal'"
      :saveMonthly="disableSave && templateCode !='safetyfinal'"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <el-dialog
      v-if="templateCode!='safetyfinal'"
      :title="'历史填报信息'"
      :visible.sync="dialogVisible"
      fullscreen
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-tabs v-if="dialogVisible && reports.length" v-model="activeHisName">
        <el-tab-pane
          v-for="tab in reports"
          :key="tab.id"
          :label="tab.reportTemplateName + '-' + tab.reporterName"
          :name="tab.id"
          lazy
        >
          <mssReportTemplate
            :isView="true"
            :isHis="true"
            :yearMonth="yearMonth"
            :templateCode="tab.reportTemplateCode"
            :reporterId="tab.reporterId"
            :reporterNameFlg="false"
            :boId="tab.reportCityId"
            :cityName="cityName"
            :boType="'reportcity'"
          ></mssReportTemplate>
        </el-tab-pane>
      </el-tabs>
      <mssReportTemplate
        v-else-if="dialogVisible && templateCode"
        :isView="true"
        :isHis="true"
        :templateCode="templateCode"
        :reporterId="reporterId"
        :yearMonth="yearMonth"
        :cityName="cityName"
        :boId="boId"
        :cityId="cityId"
        :boType="'reportcity'"
      ></mssReportTemplate>
    </el-dialog>
  </div>
</template>

<script>
import { getDetailService,supervisorcitygetbycityIdService } from '@/api/monthly_report/summary_work_order_api.js'
import { getSafetyManagerService } from '@/api/monthly_report/work_order_api.js'
import axios from 'axios'
export default {
  name: 'summaryWorkOrderEdit',
  data() {
    return {
      basicConfig: [
        {
          label: '任务名称：',
          type: 'input',
          prop: 'name',
          disabled: true
        },
        {
          label: '派发地市：',
          type: 'input',
          prop: 'cityName',
          disabled: true
        },
        {
          label: '任务处理人：',
          type: 'input',
          prop: 'reporterName',
          disabled: true
        },
        {
          label: '任务处理人角色：',
          type: 'input',
          prop: 'reportRoleName',
          disabled: true
        },
        {
          label: '任务派发时间：',
          type: 'input',
          prop: 'createDate',
          disabled: true
        },
        {
          label: '任务截至时间：',
          type: 'input',
          prop: 'planEndDate',
          disabled: true
        }
      ],
      labelPosition: 'left',
      basicForm: {},
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>详情',
          id: 'sectionTaskDetails'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow'
        }
      ],
      boId: '',
      templateCode: '', //模板id
      dealPage: true,
      workflowCode: 'SafetyReportCity',
      completeTaskUrl: '',
      returnAddress: '/monthly_report/work_reporting_summary/work_order_list',
      dialogVisible: false,
      yearMonth: '',
      nodeName: '',
      nodeCode: '',
      disableSave: false,
      operateType: '',
      reports: [],
      activeName: '',
      activeHisName:"",
      cityId:"",
      supervisoraudit:{},
      safetymanageraudit:{},
      reporterId:"",
      cityName:""
    }
  },
  created() {
    this.boId = this.$route.query.boId
    this.operateType = this.$route.query.operateType
    getDetailService(this.boId).then((res) => {
      if (res && res.code == '0000') {
        this.basicForm = res.data
        if(this.basicForm.planEndDate){
          this.basicForm.planEndDate = this.$moment(this.basicForm.planEndDate).format('YYYY-MM-DD')
        }
        this.completeTaskUrl = res.data.completeTaskUrl
        this.yearMonth = res.data.yearMonth
        this.cityId = res.data.cityId
        this.reporterId = res.data.reporterId
        this.cityName = res.data.cityName
        this.templateCode = res.data.reportTemplateCityCode
        this.reports = res.data.reports || []
        if (this.reports.length) {
          this.activeName = this.reports[0].id
        }
        if(this.operateType == 'edit'){
          let req = {
            cityId:this.cityId
          }
          supervisorcitygetbycityIdService(req).then(res=>{
            if(res && res.code =='0000'){
              this.supervisoraudit = res.data
            }
          })
          getSafetyManagerService(req).then(res=>{
            if(res && res.code =='0000'){
              this.safetymanageraudit = res.data
            }
          })
        }
      }
    })
  },
  mounted() {
    if (this.operateType == 'edit') {
      this.$refs.workFlow.init()
    } else {
      this.dealPage = false
    }
  },
  beforeDestroy(){
    if(window[this.boId]){
      delete window[this.boId]
    }
  },
  methods: {
    save(cb) {
      let reportTemplate
      if(this.templateCode == 'safetyfinal'){
        reportTemplate = this.$refs.safetyfinal
      }else if(this.templateCode == 'supervisersum'){
        reportTemplate = this.$refs.supervisersum
      }else{
        reportTemplate = this.$refs[this.activeName] && this.$refs[this.activeName][0]
      }
      if (reportTemplate.multipleTable) {
        if (
          reportTemplate.activeName &&
          reportTemplate.$refs[reportTemplate.activeName][0] &&
          reportTemplate.$refs[reportTemplate.activeName][0].save
        ) {
          reportTemplate.$refs[reportTemplate.activeName][0].save(cb)
        }
      } else {
        if (
          reportTemplate.activeName &&
          reportTemplate.$refs[reportTemplate.activeName] &&
          reportTemplate.$refs[reportTemplate.activeName].save
        ) {
          reportTemplate.$refs[reportTemplate.activeName].save(cb)
        }
      }
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.name
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode(){
      let fileNameArr = {
        supervisoraudit:this.supervisoraudit,
        safetymanageraudit:this.safetymanageraudit
      }
      return fileNameArr
    },
    // 提交
    submit() {
      this.$refs.workFlow.opendialogInitNextPath()
    },
    autoSave(cb){
      if(window[this.boId]){
        let reqList = []
        let flag = 0
        let that = this
        let errMsg = ""
        for (const key in window[this.boId]) {
          const element = window[this.boId][key];
          let returnObj = element.save(cb,true)
          if( typeof returnObj === 'string'){
            errMsg += returnObj + '</br>'
          }else{
            reqList.push(returnObj)
          }
        }
        if(errMsg.length > 0){
          this.$alert(`${errMsg}存在必填内容未填写，请按照要求填写后再提交！`,'提示',{
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true,
            customClass:'err-content'
          })
        }else if (reqList.length){
          axios.all(reqList).then(axios.spread(function(){
            for (let i = 0, len = arguments.length; i < len; i++){
              let res = arguments[i]
              if (res && res.code == '0000') {
                flag++
              }else if(res){
                that.$message({ message: res.data || res.msg || res.message, type: 'error' })
              }
            }
            if(reqList.length === flag){
              if (cb && cb.constructor === Function) {
                cb()
              }
            }
          })).catch((e) => {
            this.$message({ message: e, type: 'error' })
          })
        }
      }
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.returnAddress
      })
    },
    viewHis() {
      if(this.reports.length){
        this.activeHisName = this.reports[0].id
      }
      this.dialogVisible = true
    },
    getNodeData(data) {
      this.nodeName = data.nodeName
      this.nodeCode = data.nodeCode
      this.disableSave = data.nodeCode == 'draft'
    }
  }
}
</script>

<style lang="scss">
.err-content{
  width: auto;
}
</style>
