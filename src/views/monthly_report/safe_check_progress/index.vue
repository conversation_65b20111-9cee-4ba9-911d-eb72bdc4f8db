/**
* @author: ty
* @date: 2023-07-07
* @description: 安全检查进度-列表查询 TODO: 新增删除等待调试完成后需要去掉
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
      @changeSelect="changeSelect"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="send" type="primary">督办</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          selection
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          :autoCall="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { getDeptService } from '@/api/choose_dept'
import { findPageService, sendService, getUserInfoService, } from '@/api/monthly_report/safe_check_progress.js'
export default {
  name: 'SafeCheckProgressFindPage',
  data() {
    const validateStartEndDate = (rule, value, callback) => {
      if(this.$refs.searchForm.searchForm.startDate && this.$refs.searchForm.searchForm.endDate){
        if(new Date(this.$refs.searchForm.searchForm.startDate).getTime() > new Date(this.$refs.searchForm.searchForm.endDate).getTime()){
          callback(new Error('开始日期需小于结束日期'))
        }else{
          callback()
        }
      }else{
        callback()
      }
    }
    return {
      searchConfig: [
        {
          label: '地市',
          type: 'select',
          options: [],
          fieldName: 'city',
        },
        {
          label: '区县',
          type: 'select',
          options: [],
          fieldName: 'county'
        },
        {
          label: '工单名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '检查类型',
          type: 'select',
          options: [{label: '安全检查', value: '安全检查'}],
          fieldName: 'tempType'
        },
        {
          label: '工单发起开始-完成时间',
          tooltip: true,
          type: 'date2',
          fieldName: 'startDate',
          fieldName2: 'endDate',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          rules: [
            { validator: validateStartEndDate, trigger: ['blur', 'change'] },
          ]
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '工单名称',
          prop: 'name',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '工单编号',
          prop: 'code',
          tooltip: true,
        },
        {
          label: '检查类型',
          prop: 'tempType',
          tooltip: true,
        },
        {
          label: '检查模板名称',
          prop: 'tempName',
          tooltip: true,
        },
        {
          label: '检查地市/区县',
          prop: 'cityNames',
          tooltip: true,
        },
        {
          label: '派发时间',
          prop: 'sendDate',
          tooltip: true,
        },
        {
          label: '上一环节',
          prop: 'nodeName',
          tooltip: true,
        },
        {
          label: '上一环节处理人',
          prop: 'nodeUserName',
          tooltip: true,
        },
        {
          label: '状态',
          prop: 'status',
          tooltip: true,
        },
        {
          label: '操作',
          prop: '_operationCol',
          formatter: (row, column, cellValue, index) => {
            return (
              <a href='javascript:;' onClick={() => { this.operateHandle(row.id) }}>查看</a>
            )
          }
        }
      ],
      userInfo: {},
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      let that = this
      getUserInfoService().then(res => {
        that.userInfo = res.data
        that.$set(that.$refs.searchForm.searchForm, 'tempType', '安全检查')
        that.getList()
      })
    },
    getList() {
      let that = this
      const deptParams = {isReturnUser: '0', parentId: '', rootId: '-2', orgChildId: '-2', conditionType: 'user_staff_con', conditionParams: '{deptId: "-2", deptType: "2" }' }
      if (that.userInfo.deptType == '1') {
        getDeptService(deptParams).then(res => {
          if (res.code === '0000' && res.data) {
            let ops = []
            res.data.forEach((item)=>{
              ops.push({label: item.text, value: item.id})
            })
            that.$set(this.searchConfig[0], 'options', ops)
            that.search()
          }
        })
      } else if (that.userInfo.deptType == '2') {
        let ops = [{label: that.userInfo.firstDeptName, value: that.userInfo.firstDeptId}]
        that.$set(this.searchConfig[0], 'options', ops)
        deptParams.conditionParams = '{deptId: "' + that.userInfo.firstDeptId + '", deptType: "3" }'
        deptParams.parentId = that.userInfo.firstDeptId
        getDeptService(deptParams).then(res => {
          if (res.code === '0000' && res.data) {
            let ops = []
            res.data.forEach((item)=>{
              ops.push({label: item.text, value: item.id})
            })
            that.$set(this.searchConfig[1], 'options', ops)
            that.search()
          }
        })
        that.$set(that.$refs.searchForm.searchForm, 'city', that.userInfo.firstDeptId)
        that.$set(that.$refs.searchForm.searchForm, 'county', '')
      } else if (that.userInfo.deptType == '3') {
        let ops = [{label: that.userInfo.firstDeptName, value: that.userInfo.firstDeptId}]
        that.$set(this.searchConfig[0], 'options', ops)
        let ops1 = [{label: that.userInfo.secondDeptName, value: that.userInfo.secondDeptId}]
        that.$set(this.searchConfig[1], 'options', ops1)
        that.$set(that.$refs.searchForm.searchForm, 'city', that.userInfo.firstDeptId)
        that.$set(that.$refs.searchForm.searchForm, 'county', that.userInfo.secondDeptId)
        that.search()
      } else {
        that.$set(that.$refs.searchForm.searchForm, 'city', ' ')
        that.$set(that.$refs.searchForm.searchForm, 'county', ' ')
        that.search()
      }
    },
    changeSelect(name, val){
      let that = this
      if (name == 'city') {
        const deptParams = {isReturnUser: '0', parentId: '', rootId: '-2', orgChildId: '-2', conditionType: 'user_staff_con', conditionParams: '{deptId: "-2", deptType: "2" }' }
        deptParams.conditionParams = '{deptId: "' + val + '", deptType: "3" }'
        deptParams.parentId = val
        getDeptService(deptParams).then(res => {
          if (res.code === '0000' && res.data) {
            let ops = []
            res.data.forEach((item)=>{
              ops.push({label: item.text, value: item.id})
            })
            that.$set(this.searchConfig[1], 'options', ops)
            that.$set(that.$refs.searchForm.searchForm, 'county', '')
          }
        })
      }
    },
    // 督办
    send(){
      let that = this
      let data = this.$refs.table.multipleSelection
      let key = 'id'
      let ids = data.map(x => {
        return x[key]
      }).join(',')
      if (ids.length == 0) {
        this.$message.warning('请勾选条目！')
        return
      }
      sendService({ids:ids}).then(res => {
        that.$message.success(res.data)
      })
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle(boId){
      this.$router.push({
        path: '/monthly_report/safe_check/safe_check_back/view',
        query: { boId }
      })
    },
    search() {
      let that = this
      that.$refs.table.page.current = 1
      that.staticSearchParam=JSON.parse(
        JSON.stringify(that.$refs.searchForm.searchForm)
      )
      that.loadParam = JSON.parse(
        JSON.stringify(that.$refs.searchForm.searchForm)
      );
      if (that.$refs.searchForm.searchForm.city != '' && that.$refs.searchForm.searchForm.city != ' '
        && that.$refs.searchForm.searchForm.county == '') {
        let data = JSON.parse(
          JSON.stringify(that.$refs.searchForm.searchForm)
        )
        let dataMsg = []
        that.searchConfig[1].options.forEach((ele, index)=>{
          if (dataMsg.length > 0) {
            dataMsg = dataMsg + ','
          }
          dataMsg = dataMsg + ele.value
        })
        data.countys = dataMsg
        that.$refs.table.getTableData(data);
      } else {
        that.$refs.table.getTableData(that.$refs.searchForm.searchForm);
      }
    },
    reset() {
      let that = this
      if (that.userInfo.deptType == '2') {
        that.$set(that.$refs.searchForm.searchForm, 'city', that.userInfo.firstDeptId)
        that.$set(that.$refs.searchForm.searchForm, 'county', '')
      } else if (that.userInfo.deptType == '3') {
        that.$set(that.$refs.searchForm.searchForm, 'city', that.userInfo.firstDeptId)
        that.$set(that.$refs.searchForm.searchForm, 'county', that.userInfo.secondDeptId)
      }
      that.$set(that.$refs.searchForm.searchForm, 'tempType', '安全检查')
      this.search()
    },
  }
}
</script>
