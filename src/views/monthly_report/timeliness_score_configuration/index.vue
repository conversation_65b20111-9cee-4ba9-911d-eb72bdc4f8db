<!-- 及时性分值配置 -->
<template>
  <div class="timeliness-score-configuration">
    <mssCard title="及时性分值配置信息">
      <div slot="headerBtn">
        <!-- <el-button type="primary" @click="action">新增</el-button> -->
        <!-- <el-button type="primary" @click="saveTableInfo">保存</el-button>
        <el-button type="primary" @click="delTableInfo">删除</el-button> -->
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          :stationary="stationary"
          :pagination="false"
          :object-span-method="objectSpanMethod"
          border
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService, intimesaveUpdateService, intimeDeleteService } from '@/api/monthly_report/timeliness_score_configuration.js'
export default {
  name: 'TimelyRiskAllocation',
  data() {
    return {
      powerData:[],
      api: queryListService,
      stationary: [],
      roleRow: {}, // 某行数据
    }
  },
  computed:{
    columns(){
      if(this.powerData.timeliness_score_configuration_save){
        return [
          {
            label: '流程名称',
            prop: 'workName'
          },
          {
            label: '分值名称',
            prop: 'scoreName'
          },
          {
            label: '排名名次',
            minWidth: '220px',
            formatter: (row) => {
              return (
                <div>
                  <span style='margin-right:15px;'>排名</span>
                  <el-input v-model={row.startRank} onBlur={() => this.validate(row, 'num1')}></el-input>
                  <span style='margin:0 15px;'>至</span>
                  <el-input v-model={row.endRank} onBlur={() => this.validate(row, 'num2')}></el-input>
                </div>
              )
            }
          }, {
            label: '得分分值',
            prop: 'score',
            formatter: (row) => {
              return (
                <el-input v-model={row.score} onBlur={() => this.validate(row, 'float')} ></el-input>
              )
            }
          },
          {
            label: '操作',
            prop: '_operationCol',
            formatter: (row) => {
              return (
                <span>
                  <el-button
                    type='text'
                    onClick={() => {
                      this.action(row, 'save')
                    }}
                  >
                    保存
                  </el-button>
                </span>)
            }
          }
        ]
      }else{
        return [
          {
            label: '流程名称',
            prop: 'workName'
          },
          {
            label: '分值名称',
            prop: 'scoreName'
          },
          {
            label: '排名名次',
            minWidth: '220px',
            formatter: (row) => {
              return (
                <div>
                  <span style='margin-right:15px;'>排名</span>
                  {row.startRank}
                  <span style='margin:0 15px;'>至</span>
                  {row.endRank}
                </div>
              )
            }
          }, {
            label: '得分分值',
            prop: 'score'
          }
        ]
      }
      
    }
  },
  created() {
    this.getPower()
  },
  methods: {
    getPower(){
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item)=>{
        this.powerData[item.authority]=true
      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 1 || columnIndex == 2) {
        // 如果是第一行，就合并两行
        if (rowIndex == 0) {
          return {
            rowspan: 3,
            colspan: 1
          }
        } else {
          return [0, 0]
        }
      }
    },
    // 校验小数
    validate(row, type) {
      const data = this.$refs.table.tableData
      this.stationary = data.map(item => {
        if (row.id === item.id) {
          if (type === 'num1') {
            let startRank = item.startRank
            const reg = /^\+?[1-9]\d*$/ // 大于0的正整数
            if (!reg.test(item.startRank)) {
              // 非大于0的正整数，给出提示
              this.$message.warning('排名为正整数')
              startRank = ''
            }
            return { ...item, startRank }
          } else if (type === 'num2') {
            let endRank = item.endRank
            const reg = /^\+?[1-9]\d*$/ // 大于0的正整数
            if (!reg.test(item.endRank)) {
              // 非大于0的正整数，给出提示
              this.$message.warning('排名为正整数')
              endRank = ''
            }
            return { ...item, endRank }
          } else {
            const score = item.score.replace(/^\D*(\d*(?:\.\d{0,1})?).*$/g, '$1')
            return { ...item, score }
          }
        } else {
          return item
        }
      })
    },
    async action(row) {
      const obj = row
      const res = await this.saveServiceFn(obj)
      if (res) {
        this.$message.success("保存成功！")
        this.$refs.table.getTableData()
      }
    },
    // 需求文档上是批量保存
    async saveTableInfo() {
      const data = this.$refs.table.tableData
      // 请求队列
      const list = []
      let flag = true
      try {
        data.forEach(item => {
          if (item.type === 'edit') {
            if (item.startRank && item.endRank && item.score) {
              const obj = item
              list.push(this.saveServiceFn(obj))
            } else {
              throw new Error('请输入排名和分值')
            }
          }
        })
      } catch (error) {
        flag = false
        this.$message.warning(error.message)
      }
      if (flag) {
        // 所有请求都已完成，直接返回或处理 res
        await Promise.all(list)
        this.$refs.table.getTableData()
      }
    },
    async saveServiceFn(obj) {
      let flag = true
      await intimesaveUpdateService(obj).then(res => {
        if (res.code === '0000') {
          flag = true
        } else {
          flag = false
        }
      })
      return flag
    },
    delTableInfo() {
      const selection = this.$refs.table.multipleSelection
      const ids = []
      if (selection.length > 0) {
        selection.forEach(item => {
          ids.push(item.id)
        })
        this.$confirm('是否确认删除数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          intimeDeleteService({ ids: ids.join(',') }).then(res => {
            if (res.code === '0000') {
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
              this.$refs.table.getTableData()
            }
          })
        }).catch(() => {})
      }
    }
  }
}
</script>
<style lang="scss">
.timeliness-score-configuration{
  .el-input{
    width: 100px;
  }
}
</style>
