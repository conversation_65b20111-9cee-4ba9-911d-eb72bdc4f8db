<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-21 16:25:34
 * @Description: 流程环节配置管理
-->
<template>
  <div class="process_link_config">
    <mssCard title="流程配置信息">
      <div slot="content">
        <mssTable
          ref="table"
          :serial="false"
          :stationary="tableData"
          :columns="tableHeader"
          :pagination="false"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>
<script>
  import {workNodeQueryService,workNodeSaveUpdateService} from '@/api/monthly_report/config_manage'
  export default {
    name: 'processLinkConfig',
    data(){
      return { 
        tableData: [
          {
            workName: '月度安全工作履职任务',
            nodeName: '1',
            nodeStatus: '',
          },
          {
            workName: '月度安全工作履职任务',
            nodeName: '2',
            nodeStatus: '',
          },
          {
            workName: '月度安全工作履职任务',
            nodeName: '3',
            nodeStatus: '',
          }
        ],
        statusList: [
          {label:'启用',value:'up'},
          {label:'禁用',value:'down'},
        ],
        selectDisabled: false
      }
    },
    computed:{
      tableHeader:{
        get(){
          return [
          {prop:"workName",label:"流程名称",minWidth:140},
          {prop:"nodeName",label:"环节节点名",minWidth:120},
          {prop:"nodeStatus",label:"环节状态",minWidth:220, formatter: row=>{
            return (
              <el-select style="width:200px" v-model={row.nodeStatus} onChange={()=>{this.selectChange(row)}} disabled={row.disabled}>
                {this.statusList.length && this.statusList.map(item=>{
                  return (
                    <el-option label={item.label} value={item.value}></el-option>
                  )
                })}
              </el-select>
            )
          }},
          {prop:"_caozuo",label:"操作",width: 80,formatter: row=>{
            return (
              <el-button type="text" disabled={row.disabled} onclick={()=>{ this.save(row) }}>
                保存
              </el-button>
            )
          }},
        ]
        }
      }
    },
    created(){
      this.getTableList()
    },
    methods: {
      getTableList(){
        workNodeQueryService({
          "limit": 0,
          "page": 0
        }).then(res => {
          if(res && res.code === '0000'){
            this.tableData = res.data.data.map((item, index)=>{
              return {
                ...item,
                fontId: index,
                disabled: item.nodeName == '监理审核'? true: false
              }
            })
            const status = this.tableData.filter(item => item.nodeCode == 'supervisorcheck')[0].nodeStatus
            const index = this.tableData.filter(item => item.nodeCode == "safetymanageraudit")[0].fontId
            if(status == 'down'){
              this.tableData[index].disabled = true
            }
          }
        })
      },
      selectChange(val){
        if(val.nodeCode == 'supervisorcheck'){
          const index = this.tableData.filter(item => item.nodeCode == "safetymanageraudit")[0].fontId
          if(val.nodeStatus == 'down'){
            this.tableData[index].disabled = true
          }else{
            this.tableData[index].disabled = false
          }
        }
      }, 
      save(data){
        workNodeSaveUpdateService({
          nodeCode: data.nodeCode,
          workName: data.workName,
          id: data.id,
          nodeStatus: data.nodeStatus,
          nodeName: data.nodeName,
          nodeStatusName: data.nodeStatus == 'up'?'启用':'禁用'
        }).then(res=>{
          if(res && res.code === "0000"){
            this.$message({
              type: 'success',
              message: '保存成功'
            })
            this.getTableList()
          }
        })
      },
    }
  }
</script>