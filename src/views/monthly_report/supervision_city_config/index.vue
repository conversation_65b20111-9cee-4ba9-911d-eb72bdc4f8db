<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-21 16:54:53
 * @Description: 监理与地市关系配置管理
-->
<template>
  <div class="supervision_city_config">
    <mssCard title="配置信息">
      <div slot="headerBtn" v-if="oprateType == 'writable'">
        <el-button type="primary" @click="add">新增</el-button>
        <el-button type="primary" @click="deleteTable(null, 'batch')">删除</el-button>
        <el-button type="primary" @click="save(null, 'batch')">保存</el-button>
      </div>
      <div slot="content">
        <mssTable v-if="tableFalse" ref="table" :selection="oprateType=='wrieable'"  :serial="false" :stationary="tableData" :columns="tableHeader" :pagination="false">
        </mssTable>
      </div>
    </mssCard>
    <mssChooseUser ref="chooseUser" @showCheckList="showCheckList"></mssChooseUser>
  </div>
</template>
<script>
const AUTHORITY_OPRATE_CODE ='supervision_city_config_operate'
const AUTHORITY_READONLY_CODE = 'supervision_city_config_readonly'
import {
  supervisorqueryService,
  supervisorcitysaveupdateService,
  supervisorcityDeleteService 
} from '@/api/monthly_report/config_manage'
import { queryAreaListService } from "@/api/common_api"
export default {
  name: 'supervisionCityConfig',
  data() {
    return {
      tableData: [],
      cityList: [],
      deptList: [],
      tableIndex: '',
      tableFalse: true,
      oprateType: '',
    }
  },
  computed: {
    tableHeader_write: {
      get() {
        return [
          { prop: "workName", label: "流程名称", minWidth: 140 },
          {
            prop: "supervisorName", label: "监理单位", minWidth: 230, formatter: row => {
              return (
                <el-input v-model={row.supervisorName} onfocus={()=>{this.opDeptDialog(row)}}></el-input>
              )
            }
          },
          {
            prop: "supervisorUserId", label: "监理人员", minWidth: 160, formatter: row => {
              return (
                <el-input v-model={row.supervisorUserName}></el-input>
              )
            }
          },
          {
            prop: "mobile", label: "联系电话", minWidth: 160, formatter: row => {
              return (
                <el-input v-model={row.mobile}></el-input>
              )
            }
          },
          {
            prop: "cityName", label: "核查地市", minWidth: 190, formatter: row => {
              return (
                <el-select 
                  ref="citySelect"
                  placeholder="--请选择--" 
                  style="width:180px" 
                  v-model={row.cityId}
                  multiple
                  >
                  {this.cityList.length && this.cityList.map(item => {
                    return (
                      <el-option label={item.label} value={item.value}></el-option>
                    )
                  })}
                </el-select>
              )
            }
          },
          {
            label: '操作',
            prop: '_caozuo',
            formatter: row => {
              return (<span>
                <span
                  class='table_btn mr10'
                  onClick={() => { this.deleteTable(row,'single') }}
                >
                  删除
                </span>
                <span class='table_btn' onClick={() => { this.save(row, 'single') }}>
                  保存
                </span>
              </span>)
            }
          }
        ]
      }
    },
    tableHeader_read:{
      get(){
        return [
          {
            label: '流程名称',
            prop: 'workName',
            minWidth: 160,
          },{
            label: '监理单位',
            prop: 'supervisorName',
            minWidth: 200,
            tooltip: true
          },{
            label: '监理人员',
            prop: 'supervisorUserName',
            minWidth: 160,
            tooltip: true
          },{
            label: '联系电话',
            prop: 'mobile',
            minWidth: 140,
            tooltip: true
          },{
            label: '核查地市',
            prop: 'cityName',
            minWidth: 160,
            formatter: row =>{
              return (
                <el-select 
                  class={"readSelect"}
                  ref="citySelect"
                  placeholder="--请选择--" 
                  style="width:180px" 
                  disabled={'disabled'}
                  v-model={row.cityId}
                  multiple
                  >
                  {this.cityList.length && this.cityList.map(item => {
                    return (
                      <el-option label={item.label} value={item.value}></el-option>
                    )
                  })}
                </el-select>
              )
            }
          }
        ]
      }
    }
  },
  created() {
    const authorities = JSON.parse(sessionStorage.getItem('authorities'))
    let index = authorities.findIndex(item=>{
      return item.authority === AUTHORITY_OPRATE_CODE
    })
    this.tableHeader = index!=-1 ?  this.tableHeader_write:  this.tableHeader_read
    this.oprateType = index!=-1 ? 'writable': 'readonly'
    this.handleTableData()
    this.getAreaList()
  },
  methods: {
    handleTableData(){
      supervisorqueryService({
        "limit": 0,
        "page": 0
      }).then(res => {
        if(res && res.code === '0000'){
          this.tableData = res.data.data.map((item,index)=>{
            return {
              ...item,
              cityId: item?.cityId?.split(','),
              supervisorId: item.supervisorId || '',
              supervisorUserId: item.supervisorUserId || '',
              mobile: item.mobile || '',
              fontId: index
            }
          })
        }
      })
    },  
    getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.cityList = list
        }
      })
    },
    add() {
      const data = {
        id: '',
        workName: '月工作上报任务',
        supervisorId: '',
        supervisorUserId: '',
        mobile: '',
        cityId: '',
      }
      this.tableData.push(data)
      this.tableData = this.tableData.map((item,index)=>{
        return { 
          ...item,
          fontId: index
        }
      })
    },
    // 
    save(data, saveType) { 
      let req = []
      if(saveType == 'single'){
        req = [{
          id: data.id,
          workName: data.workName,
          cityId: data.cityId.join(),
          mobile: data.mobile,
          supervisorId: this.deptList.length ? this.deptList[0].deptId: data.supervisorId,
          supervisorName: data.supervisorName,
          supervisorUserId: this.deptList.length ? this.deptList[0].userId: data.supervisorUserId,
          supervisorUserName: data.supervisorUserName
        }]
      }else{
        const chooseData =  this.tableData
        if(chooseData.length){
          req = chooseData.map(item=>{
            return {
              ...item,
              cityId: item.cityId.join()
            }
          })
        }else{
          this.$message.warning("请至少选择一条数据！")
          return false
        }
      }
      supervisorcitysaveupdateService(req).then(res=>{
        if(res && res.code === '0000'){
          this.$message({
            type: "success",
            message: res.data || "保存成功"
          })
          this.$refs.table.multipleSelection = []
          this.$refs.table.$refs.table.clearSelection()
          this.handleTableData()
        }
      })
    },
    deleteTable(row, deleteType){
      let ids = ''
      if(deleteType == 'single'){
        ids = row.id
      }else{
        const chooseData =  this.$refs.table.multipleSelection
        if(chooseData.length){
          ids = chooseData.map(item=>{
            return item.id
          }).join()
        }else{
          this.$message.warning("请至少选择一条数据！")
          return false
        }
      }
      supervisorcityDeleteService({ids: ids}).then(res=>{
        if(res && res.code === '0000'){
          this.$message({
            type: "success",
            message: "删除成功"
          })
          this.$refs.table.multipleSelection = []
          this.$refs.table.$refs.table.clearSelection()
          this.handleTableData()
        }
      })
    },
    opDeptDialog(row){
      this.tableIndex = row.fontId
      this.$refs.chooseUser.init(this.deptList, {rootId: "2001",orgChildId:'2001'},{});
    },
    showCheckList({checkList}) {
      this.deptList = checkList
      this.tableFalse = false
      this.$nextTick(()=>{
        this.tableFalse = true
        this.tableData[this.tableIndex]['supervisorId'] = checkList[0].deptId
        this.tableData[this.tableIndex]['supervisorName'] = checkList[0].deptName
        this.tableData[this.tableIndex]['supervisorUserId'] = checkList[0].userId
        this.tableData[this.tableIndex]['supervisorUserName'] = checkList[0].realName
        this.tableData[this.tableIndex]['mobile'] = checkList[0].mobile
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.supervision_city_config{
  .readSelect{
    ::v-deep .el-tag {
    &.el-tag--info{
      background-color: transparent;
      border: none;
      color: #666;
    }
  }
  ::v-deep .el-input{
    &.is-disabled {
      .el-input__inner{
        background-color: transparent !important;
        border: none !important;
        color: #C0C4CC !important;
        cursor:auto;
      }
      .el-input__suffix{
        display: none;
      }
    }
  }
  }
}


</style>