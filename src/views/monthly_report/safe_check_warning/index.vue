<!-- 安全生产费预警 -->
<template>
  <div class="safe_check_warning">
    <mssCard title="预警配置详情">
      <div slot="headerBtn">
        <el-button type="primary" @click="addTable">新增</el-button>
        <el-button type="primary" @click="save">保存</el-button>
        <el-button type="primary" @click="deleteBatch">删除</el-button>
      </div>
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
          </mssTable>
        </el-form>
      </div>
    </mssCard>
  </div>
</template>

<script>

import { findPageService, saveService, updateService, updateBatchService, deleteOneService, deleteBatchService, activeCheckService, activeService } from '@/api/monthly_report/safe_check_warning.js'
import { commonOneDel, commonMultDel } from '@/utils/btn'
export default {
  name: "safe_check_warning",
  data() {
    return {
      tableApi: ()=>{},
      tableData: [],
      levelList: [{label: '一级',value: 1},{label: '二级',value: 2},{label: '三级',value: 3}],
      warningType: [{label: '督办任务', value: '1'}, {label: '短信', value: '2'}],
      warningSend: [{label: '项目经理', value: '1'}, {label: '安全管理员', value: '2'}, {label: '市工建中心经理', value: '3'}],
      form:{},
      rules:{}
    };
  },
  async created() {
    this.getList();
  },
  computed:{
    tableHeader:{
      get(){
        return  [
        {
          prop: "level",
          label: "预警级别",
          minWidth: 120,
          formatter: (row, column, cellValue, index) => {
            return (
              <el-form-item prop={`level${index}`}>
                <el-select
                  v-model={row.level}
                  onChange={(value) => {
                    this.form[`level${index}`] = value;
                  }}
                >
                  { this.levelList.length && this.levelList.map(item=>{
                    return <el-option label={item.label} value={item.value} />
                  }) }
                </el-select>
              </el-form-item>
            )
          }
        },
        {
          prop: "num",
          label: "预警时间门限设置",
          minWidth: 350,
          formatter: (row, column, cellValue, index) => {
            return (
              <div>
                <el-form-item prop={`dayNum1${index}`}>
                  <el-input-number
                    style="width:80px"
                    v-model={row.dayNum1}
                    onChange={(value) => {
                      this.form[`dayNum1${index}`] = value;
                    }}
                    controls-position="right"
                    ></el-input-number>
                </el-form-item>
                <span>天</span>
                <span>{'<'}</span>
                <span>系统当前时间</span>
                <span>-</span>
                <span>开工时间</span>
                <span>{'<'}</span>
                <el-form-item prop={`dayNum2${index}`}>
                  <el-input-number
                    style="width:80px"
                    v-model={row.dayNum2}
                    onChange={(value) => {
                      this.form[`dayNum2${index}`] = value;
                    }}
                    controls-position="right"
                  ></el-input-number>
                </el-form-item>
                <span>天</span>
              </div>
            )
          }
        },
        {
          prop: "warningType",
          label: "预警方式",
          minWidth: 160,
          formatter: (row, column, cellValue, index) => {
            return (
              <el-form-item prop={`warningType${index}`}>
                <el-select
                  v-model={row.warningTypeArr}
                  multiple
                  onChange={(value) => {
                    this.form[`warningType${index}`] = value;
                  }}
                >
                  { this.warningType.length && this.warningType.map(item=>{
                    return <el-option label={item.label} value={item.value} />
                  }) }
                </el-select>
              </el-form-item>
            )
          }
        },
        {
          prop: "content1",
          label: "预警内容",
          minWidth: 400,
          formatter: (row, column, cellValue, index) => {
            return (
              <div style="width:400px">
                <p>预警：<el-input style="width:360px" v-model={row.content1}></el-input></p>
                <p>超期1：<el-input style="width:350px" v-model={row.content2}></el-input></p>
                <p>超期2：<el-input style="width:350px" v-model={row.content3}></el-input></p>
              </div>
            )
          }
        },
        {
          prop: "warningObject",
          label: "预警对象",
          minWidth: 120,
        },
        {
          prop: "warningSend",
          label: "抄送对象",
          minWidth: 180,
          formatter: (row, column, cellValue, index) => {
            return (
              <el-form-item prop={`warningSend${index}`}>
                <el-select
                  v-model={row.warningSendArr}
                  multiple
                  onChange={(value) => {
                    this.form[`warningSend${index}`] = value;
                  }}
                >
                  { this.warningSend.length && this.warningSend.map(item=>{
                    return <el-option label={item.label} value={item.value} />
                  }) }
                </el-select>
              </el-form-item>
            )
          }
        },
        {
          prop: "activeStatus",
          label: "状态",
          minWidth: 80,
          formatter: (row, column, cellValue, index) => {
            if (row.activeStatus == '1') {
              return (
                <span>启用</span>
              )
            } else {
              return (
                <span>禁用</span>
              )
            }
          }
        },
        {
          label: '操作',
          prop: '_caozuo',
          fixed: 'right',
          minWidth: 120,
          formatter: (row, column, cellValue, index) => {
            if (row.activeStatus == '1') {
              return (<span>
                <el-button type='text' onClick={() => { this.activeMethod(row, index, '2') }}>禁用</el-button>
                <el-button type='text' onClick={() => { this.update(row, index) }}>保存</el-button>
                <el-button type='text' onClick={() => { this.deleteOne(row) }}>删除</el-button>
              </span>)
            } else {
              return (<span>
                <el-button type='text' onClick={() => { this.activeMethod(row, index, '1') }}>启用</el-button>
                <el-button type='text' onClick={() => { this.update(row, index) }}>保存</el-button>
                <el-button type='text' onClick={() => { this.deleteOne(row) }}>删除</el-button>
              </span>)
            }
          }
        }
        ]
      }
    }
  },
  mounted() {},
  methods: {
    getList() {
      let that = this
      that.tableData = []
      findPageService(null).then(res => {
        if (res.code === '0000' && res.data) {
          res.data.data.forEach((item,index) => {
            let obj = JSON.parse(JSON.stringify(item))
            obj.warningTypeArr = obj.warningTypeId?obj.warningTypeId.split(','):[];
            obj.warningObjectArr = obj.warningObjectId?obj.warningObjectId.split(','):[];
            obj.warningSendArr = obj.warningSendId?obj.warningSendId.split(','):[];
            obj.fontId = index

            that.tableData.push(obj)
          })
        }
      })
    },
    addTable(){
      let that = this;
      that.$refs.editform.validate((validate)=>{
        if (validate) {
          let obj = { level: '1', activeStatus: '2', priority: that.tableData.length + 1 }
          saveService(obj).then((res) => {
            if (res.code === '0000' && res.data) {
              obj = res.data;
              obj.warningTypeArr = obj.warningTypeId?obj.warningTypeId.split(','):[];
              obj.warningObjectArr = obj.warningObjectId?obj.warningObjectId.split(','):[];
              obj.warningSendArr = obj.warningSendId?obj.warningSendId.split(','):[];
              obj.fontId = that.tableData.length

              that.tableData.push(obj)
            }
          })
        } else {
          return
        }
      })
    },
    update(row, index) {
      let that = this;
      that.generateJson(row);
      this.commonsetRule(row, index);
      this.$nextTick(() => {
        that.$refs.editform.validate((validate)=>{
          if (validate) {
            updateService(row).then((res1) => {
              if (res1.code === '0000' && res1.data) {
                that.$message({
                  dangerouslyUseHTMLString: true,
                  showClose: true,
                  message: "保存成功！",
                  type: "success",
                });
                row.warningTypeArr = res1.data.warningTypeId?res1.data.warningTypeId.split(','):[];
                row.warningObjectArr = res1.data.warningObjectId?res1.data.warningObjectId.split(','):[];
                row.warningSendArr = res1.data.warningSendId?res1.data.warningSendId.split(','):[];
                row.warningObject = res1.data.warningObject
              }
            })
          } else {
            return
          }
        })
      });
    },
    generateJson(row){
      let that = this;
      let warningTypeId = ''
      let warningType = ''
      row.warningTypeArr.forEach((item1,index1) => {
        that.warningType.forEach((item2,index2) => {
          if (item1 == item2.value) {
            if (warningType.length > 0) {
              warningType = warningType + ','
            }
            warningType = warningType + item2.label
          }
        })
        if (warningTypeId.length > 0) {
          warningTypeId = warningTypeId + ','
        }
        warningTypeId = warningTypeId + item1
      })
      row.warningTypeId = warningTypeId
      row.warningType = warningType

      let warningObjectId = ''
      let warningObject = ''
      row.warningObjectArr.forEach((item1,index1) => {
        that.warningSend.forEach((item2,index2) => {
          if (item1 == item2.value) {
            if (warningObject.length > 0) {
              warningObject = warningObject + ','
            }
            warningObject = warningObject + item2.label
          }
        })
        if (warningObjectId.length > 0) {
          warningObjectId = warningObjectId + ','
        }
        warningObjectId = warningObjectId + item1
      })
      row.warningObjectId = warningObjectId
      row.warningObject = warningObject

      let warningSendId = ''
      let warningSend = ''
      row.warningSendArr.forEach((item1,index1) => {
        that.warningSend.forEach((item2,index2) => {
          if (item1 == item2.value) {
            if (warningSend.length > 0) {
              warningSend = warningSend + ','
            }
            warningSend = warningSend + item2.label
          }
        })
        if (warningSendId.length > 0) {
          warningSendId = warningSendId + ','
        }
        warningSendId = warningSendId + item1
      })
      row.warningSendId = warningSendId
      row.warningSend = warningSend
    },
    save() {
      let that = this;
      let data = { dtos: that.tableData }
      data.dtos.forEach((item,index) => {
        that.generateJson(item);
      })
      that.commonsetRule(data.dtos);
      this.$nextTick(() => {
        that.$refs.editform.validate((validate)=>{
          if (validate) {
            updateBatchService(data).then((res1) => {
              if (res1.code === '0000' && res1.data) {
                that.$message({
                  dangerouslyUseHTMLString: true,
                  showClose: true,
                  message: "保存成功！",
                  type: "success",
                });
              }
            })
          } else {
            return
          }
        })
      });
    },
    // 点击操作列的按钮，处理或者查看单子
    deleteOne(row) {
      const req = {
        sucCb: (res) => {
          if (res.code === '0000') {
            this.$message.success('删除成功')
            
            this.tableData.forEach((item,index) => {
              if(item.fontId === row.fontId){
                delete this.form[`level${index}`]
                delete this.rules[`level${index}`]
                this.tableData.splice(index, 1)
              }
            })
          } else {
            this.$message.warning(res.data || '请稍后再试')
          }
          this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
        }
      }
      commonOneDel.call(this, row.id, deleteOneService, req.sucCb)
    },
    // 批量删除
    deleteBatch() {
      const req = {
        data: this.$refs.table.multipleSelection,
        delApi: deleteBatchService,
        key: 'id',
        sucCb: (res) => {
          if (res.code === '0000') {
            this.$message.success('批量删除成功')
            this.$refs.table.multipleSelection.forEach((item, index) => {
              this.tableData.splice(index, 1)
            })
          } else {
            this.$message.warning(res.data || '请稍后再试')
          }
          this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
          this.search(this.$refs?.searchForm?.searchForm || {})
        }
      }
      commonMultDel.call(this, req)
    },
    // 启用/禁用
    activeMethod(row, index, status) {
      let that = this
      that.generateJson(row);
      that.commonsetRule(row, index);
      that.$nextTick(() => {
        that.$refs.editform.validate((validate)=>{
          if (validate) {
            let data = {id: row.id, level: row.level}
            data.activeStatus = status
            if ('1' == status) {
              activeCheckService(data).then((res) => {
                if (res.code === '0000') {
                  if (res.data != '') {
                    this.$confirm(res.data, '提示', {
                      confirmButtonText: '确定',
                      cancelButtonText: '取消',
                      type: 'warning'
                    }).then(() => {
                      activeService(data).then((res1) => {
                        if (res1.code === '0000' && res1.data) {
                          row.activeStatus = status
                          that.$message({
                            dangerouslyUseHTMLString: true,
                            showClose: true,
                            message: "启用成功！",
                            type: "success",
                          });
                        }
                        that.getList()
                      })
                    }).catch(()=>{})
                  } else {
                    activeService(data).then((res1) => {
                      if (res1.code === '0000' && res1.data) {
                        row.activeStatus = status
                        that.$message({
                          dangerouslyUseHTMLString: true,
                          showClose: true,
                          message: "启用成功！",
                          type: "success",
                        });
                      }
                    })
                  }
                }
              })
            } else {
              activeService(data).then((res) => {
                if (res.code === '0000' && res.data) {
                  row.activeStatus = status
                  that.$message({
                    dangerouslyUseHTMLString: true,
                    showClose: true,
                    message: "禁用成功！",
                    type: "success",
                  });
                }
              })
            }
          } else {
            return
          }
        })
      });
    },

    // 添加必填校验
    commonsetRule(arr, index) {
      this.form = {};
      this.rules = {};
      let a = [
        "level",
        "dayNum1",
        "dayNum2",
        "warningType",
      ];
      a.forEach((item) => {
        if (index) {
          this.$set(this.form, `${item}${index}`, arr[item]); // 必须用set，否则form校验识别不到
          this.rules[`${item}${index}`] = {
            required: true,
            message: "该字段不能为空",
            trigger: ["blur", "change"],
          };
        } else {
          for (let ind in arr) {
            this.$set(this.form, `${item}${ind}`, arr[ind][item]); // 必须用set，否则form校验识别不到
            this.rules[`${item}${ind}`] = {
              required: true,
              message: "该字段不能为空",
              trigger: ["blur", "change"],
            };
          }
        }
      });
    },

  },
};
</script>


