<!-- 安全生产费预警 -->
<template>
  <div class="safe_check_warning">
    <mssCard title="预警配置详情">
      <div slot="content">
        <el-form ref="editform" :model="form" :rules="rules">
          <mssTable
            ref="table"
            selection
            :serial="false"
            :stationary="tableData"
            :columns="tableHeader"
            :pagination="false"
          >
          </mssTable>
        </el-form>
      </div>
    </mssCard>
  </div>
</template>

<script>

import { findPageService, } from '@/api/monthly_report/safe_check_warning.js'
export default {
  name: "safe_check_warning_view",
  data() {
    return {
      tableApi: ()=>{},
      tableData: [],
      levelList: [{label: '一级',value: 1},{label: '二级',value: 2},{label: '三级',value: 3}],
    };
  },
  async created() {
    this.getList();
  },
  computed:{
    tableHeader:{
      get(){
        return  [
        {
          prop: "level",
          label: "预警级别",
          minWidth: 120,
          formatter: (row, column, cellValue, index) => {
            if (row.level == 1) {
              return (
                <span>一级</span>
              )
            } else if (row.level == 2) {
              return (
                <span>二级</span>
              )
            } else if (row.level == 3) {
              return (
                <span>三级</span>
              )
            } else {
              return (
                <span>--</span>
              )
            }
          }
        },
        {
          prop: "num",
          label: "预警时间门限设置",
          minWidth: 350,
          formatter: (row, column, cellValue, index) => {
            return (
              <div>
                <span>{row.dayNum1}</span>
                <span>天</span>
                <span>{'<'}</span>
                <span>系统当前时间</span>
                <span>-</span>
                <span>开工时间</span>
                <span>{'<'}</span>
                <span>{row.dayNum2}</span>
                <span>天</span>
              </div>
            )
          }
        },
        {
          prop: "warningType",
          label: "预警方式",
          minWidth: 160,
          formatter: (row, column, cellValue, index) => {
            return (
              <span>{row.warningType}</span>
            )
          }
        },
        {
          prop: "content1",
          label: "预警内容",
          minWidth: 400,
          formatter: (row, column, cellValue, index) => {
            return (
              <div style="width:400px">
                <p>预警：{row.content1}</p>
                <p>超期1：{row.content2}</p>
                <p>超期2：{row.content3}</p>
              </div>
            )
          }
        },
        {
          prop: "warningObject",
          label: "预警对象",
          minWidth: 120,
        },
        {
          prop: "warningSend",
          label: "抄送对象",
          minWidth: 180,
          formatter: (row, column, cellValue, index) => {
            return (
              <span>{row.warningSend}</span>
            )
          }
        },
        {
          prop: "activeStatus",
          label: "状态",
          minWidth: 80,
          formatter: (row, column, cellValue, index) => {
            if (row.activeStatus == '1') {
              return (
                <span>启用</span>
              )
            } else {
              return (
                <span>禁用</span>
              )
            }
          }
        },
        ]
      }
    }
  },
  mounted() {},
  methods: {
    getList() {
      let that = this
      that.tableData = []
      findPageService(null).then(res => {
        if (res.code === '0000' && res.data) {
          res.data.data.forEach((item,index) => {
            let obj = JSON.parse(JSON.stringify(item))
            if (obj.activeStatus == '1') {
              obj.warningTypeArr = obj.warningTypeId?obj.warningTypeId.split(','):[];
              obj.warningObjectArr = obj.warningObjectId?obj.warningObjectId.split(','):[];
              obj.warningSendArr = obj.warningSendId?obj.warningSendId.split(','):[];
              obj.fontId = index
              that.tableData.push(obj)
            }
          })
        }
      })
    },

  },
};
</script>


