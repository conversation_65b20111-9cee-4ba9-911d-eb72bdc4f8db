/**
* @author: ty
* @date: 2023-07-06
* @description: 安全检查下发查看工单
*/
<template>
  <div class="SafeCheckViewForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button type="primary" @click="goBack()">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="检查列表信息">
      <div slot="content">
        <mssTable
          ref="table"
          :selection="false"
          :columns="columns"
          :stationary="stationary"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>
    
    <Info v-if='myObject.checkId && !canSelectDate && !canSelectUser' ref="checkDetail" :editFlag=false :myObject="myObject" />

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :business-type="businessType" :node-name="nodeName" :bo-id="boId" :dealPage="false"></mssAttachment>

    <div id="sectionWorkflow" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
          v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import Info from "@/views/monthly_report/safe_check_back/detail_form.vue";
import {
  getDetailService,
  detailViewPageService,
} from '@/api/monthly_report/safe_check.js'
export default {
  name: 'SafeCheckViewForm',
  components: {
    Info,
  },
  data() {
    return {
      myObject: {type:'total'},
      pageLoading: false,
      boId:'',
      businessType: '',
      completeTaskUrl:"",//流程处理地址
      returnAddress:'/monthly_report/safe_check/safe_check/list',//流程提交成功返回路由地址
      nodeName:'',
      nodeCode:'',
      canSelectDate:false,
      canSelectUser:false,
      formRules:{},
      stationary: [
        { id: 0, cityNames: '', groupLeaderName: '', teamMembersNames: '', backDate: '', creatorName: '' }
      ],
      columns: [
        {
          label: '检查地市',
          prop: 'cityNames',
          minWidth: 200
        },
        {
          label: '检查组长',
          prop: 'groupLeaderName',
          minWidth: 200
        },
        {
          label: '组员',
          prop: 'teamMembersNames',
          minWidth: 200
        },
        {
          label: '要求完成时间',
          prop: 'backDate',
          minWidth: 200,
          formatter: row => {
            if (this.canSelectDate) {
              return (
                <el-date-picker v-model={row.backDate} placeholder='请选择要求完成时间' type='date' format='yyyy-MM-dd' value-format='yyyy-MM-dd HH:mm:ss' />
              )
            } else {
              return (
                <span>{row.backDate}</span>
              )
            }
          }
        },
        {
          label: '工单填报人',
          prop: 'creatorName',
          minWidth: 200, 
          formatter: row => {
            if (this.canSelectUser) {
              return (
                <el-input v-model={row.creatorName} placeholder='选择反馈填报人' onFocus={() => {this.openChooseUserDailog(row)}}></el-input>
              )
            } else {
              return (
                <span>{row.creatorName}</span>
              )
            }
          }
        },
      ],
      basicConfig: [
        {
          label: '工单名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '工单编号：',
          type: 'input',
          prop: 'code',
          span: 12
        },
        {
          label: '检查类型：',
          type: 'input',
          prop: 'tempType',
          span: 12,
        },
        {
          label: '检查模板名称：',
          type: 'input',
          prop: 'tempName',
          span: 12,
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
          span: 12
        },
        {
          label: '创建时间：',
          type: 'input',
          prop: 'createDate',
          span: 12
        },
        {
          label: '说明：',
          type: 'input',
          mode: 'textarea',
          prop: 'description',
          span: 24
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '检查<br>列表',
          id: 'sectionList'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkflow'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId || null
    this.getBasicInfo()
    this.getList()
  },
  methods: {
    getBasicInfo(){
      getDetailService(this.boId).then(res=>{
        this.basicForm = Object.assign({}, this.basicForm, res.data)
        this.boId = res.data.id || null
        this.businessType = res.data.businessType
        this.completeTaskUrl = res.data.completeTaskUrl //流程处理地址

        this.myObject.checkId = this.boId
      })
    },
    getList() {
      detailViewPageService({mSafeCheckId:this.boId}).then(res => {
        if (res.code === '0000' && res.data) {
          this.stationary = res.data;
        }
      })
    },
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/monthly_report/safe_check/safe_check/list'
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.SafeCheckViewForm{

}
</style>
