/**
* @author: ty
* @date: 2023-07-06
* @description: 质监申报根据id编辑或者是新增工单
*/
<template>
  <div class="SafeCheckEditForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button type="primary" @click="submit()">提交</el-button>
      <el-button type="primary" @click="save()">保存</el-button>
      <el-button type="primary" @click="goBack()">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="检查列表信息">
      <div slot="headerBtn">
        <!-- <el-button @click="addHandle" type="primary">新增</el-button> -->
        <el-button @click="delBatchHandle" type="primary">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          selection
          :columns="columns"
          :stationary="stationary"
          :selectable="selectable"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <Info v-if='myObject.checkId && !canSelectDate && !canSelectUser' ref="checkDetail" :editFlag=false :myObject="myObject" />

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :business-type="businessType" :node-name="nodeName" :bo-id="boId"></mssAttachment>

    <div id="sectionWorkflow" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
          v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="businessType"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      :saveMainForm="true"
      @getNodeData="getNodeData"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
      @getHideList="getHideList"
    ></mssWorkFlowHandel>

    <mssChooseUser :key="userKey" ref="chooseUser" :mult-select="false" @showCheckList="showCheckList" @closeDialog="closeDialog"></mssChooseUser>
  </div>
</template>

<script>
import Info from "@/views/monthly_report/safe_check_back/detail_form.vue";
import { commonOneDel, commonMultDel } from '@/utils/btn';
import {
  getDetailService,
  detailPageService,
  updateService,
  beforeSubmitService,
} from '@/api/monthly_report/safe_check.js'
import { delBatchService, delService } from '@/api/monthly_report/safe_check_back.js'
export default {
  name: 'SafeCheckEditForm',
  components: {
    Info,
  },
  data() {
    return {
      myObject: {type:'total'},
      pageLoading: false,
      boId:'',
      businessType: '',
      completeTaskUrl:"",//流程处理地址
      returnAddress:'/monthly_report/safe_check/safe_check/list',//流程提交成功返回路由地址
      nodeName:'',
      nodeCode:'',
      canSelectDate:false,
      canSelectUser:false,
      formRules:{},
      stationary: [
        { id: 0, cityNames: '', groupLeaderName: '', teamMembersNames: '', backDate: '', creatorName: '' }
      ],
      columns: [
        {
          label: '检查地市',
          prop: 'cityNames',
          minWidth: 200
        },
        {
          label: '检查组长',
          prop: 'groupLeaderName',
          minWidth: 200
        },
        {
          label: '组员',
          prop: 'teamMembersNames',
          minWidth: 200
        },
        {
          label: '要求完成时间',
          prop: 'backDate',
          minWidth: 200,
          formatter: (row) => {
            if (this.canSelectDate) {
              return (
                <el-date-picker v-model={row.backDate} placeholder='请选择要求完成时间' type='date' format='yyyy-MM-dd' value-format='yyyy-MM-dd HH:mm:ss'></el-date-picker>
              )
            } else {
              return (
                <span>{row.backDate}</span>
              )
            }
          }
        },
        {
          label: '工单填报人',
          prop: 'creatorName',
          minWidth: 200,
          formatter: (row) => {
            if (this.canSelectUser) {
              return (
                <el-input v-model={row.creatorName} placeholder='选择反馈填报人' onFocus={() => {this.openChooseUserDailog(row)}}></el-input>
              )
            } else {
              return (
                <span>{row.creatorName}</span>
              )
            }
          }
        },
        {
          label: '操作',
          prop: 'op',
          minWidth: 120,
          formatter: (row) => {
            if (this.canSelectDate) {
              return (
                <div>
                <a href='javascript:;' onClick={() => { this.delHandle(row) }}>删除</a>
                </div>
              )
            } else {
              return (
                <span></span>
              )
            }
          }
        },
      ],
      basicConfig: [
        {
          label: '工单名称：',
          type: 'input',
          prop: 'name',
          disabled: true,
          span: 12
        },
        {
          label: '工单编号：',
          type: 'input',
          prop: 'code',
          disabled: true,
          span: 12
        },
        {
          label: '检查类型：',
          type: 'input',
          prop: 'tempType',
          disabled: true,
          span: 12,
        },
        {
          label: '检查模板名称：',
          type: 'input',
          prop: 'tempName',
          disabled: true,
          span: 12,
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
          disabled: true,
          span: 12
        },
        {
          label: '创建时间：',
          type: 'input',
          prop: 'createDate',
          disabled: true,
          span: 12
        },
        {
          label: '说明：',
          type: 'input',
          mode: 'textarea',
          prop: 'description',
          span: 24
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'top',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '检查<br>列表',
          id: 'sectionList'
        },
        {
          text: '附件',
          id: 'sectionAttachment'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkflow'
        }
      ],
      userKey:1
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId || null
    await this.getBasicInfo()
    await this.getList()
  },
  methods: {
    async getBasicInfo(){
      getDetailService(this.boId).then(res=>{
        this.basicForm = Object.assign({}, this.basicForm, res.data)
        this.boId = res.data.id || null
        this.businessType = res.data.businessType
        this.completeTaskUrl = res.data.completeTaskUrl //流程处理地址
        this.getWorkCode(this.businessType)

        this.myObject.checkId = this.boId
      })
    },
    async getList() {
      detailPageService({mSafeCheckId:this.boId}).then(res => {
        if (res.code === '0000' && res.data) {
          this.stationary = res.data;
        }
      })
    },
    save(cb){
      // todo 提交前校验
      this.pageLoading = true
      let param = {
        ...this.$refs.basicForm.modelForm, backDtos: this.stationary
      };
      updateService(param).then(res => {
        if (cb) {
          cb(param)
        } else {
          this.$message({
            dangerouslyUseHTMLString: true,
            showClose: true,
            message: '保存成功！',
            type: "success",
          });
        }
      }).finally(_=> {
        this.pageLoading = false
      })
    },
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/monthly_report/safe_check/safe_check/list'
      })
    },
    openChooseUserDailog(row) {
      this.backRow = row
      const item = {
        excuterNames: row.creatorName,
        excuterIds: row.creatorId
      }
      const deptParams = { rootId: '-2', }
      if ('1' != sessionStorage.getItem('firstDeptId')) {
        deptParams.orgChildId = sessionStorage.getItem('firstDeptId')
        deptParams.deptIds = sessionStorage.getItem('firstDeptId')
      }
      // const userParams = {orgChildId: row.cityIds }
      this.$refs.chooseUser.init(item, deptParams)
    },
    selectable(row, index) {
        if (this.canSelectDate) {
            return true
        } else {
            return false
        }
    },
    // 点击操作列的按钮，删除
    delHandle(row) {
      let that = this
      that.$confirm('请确认是否删除数据?删除之后无法添加，只能通过新增表单重新发起安全检查。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const req = {
          sucCb: (res) => {
            if (res && res.code === '0000') {
              this.$message.success('删除成功')
            } else {
              this.$message.warning(res.data || '请稍后再试')
            }
            that.getList()
          }
        }
        commonOneDel.call(this, row.id, delService, req.sucCb, 'path')
      }).catch(() => {})
    },
    //  批量删除 todo 接口未调试
    delBatchHandle() {
      let that = this
      that.$confirm('请确认是否删除数据?删除之后无法添加，只能通过新增表单重新发起安全检查。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const req = {
          data: this.$refs.table.multipleSelection,
          delApi: delBatchService,
          key: 'id',
          sucCb: (res) => {
            if (res.code === '0000') {
              that.$message.success('批量删除成功')
            } else {
              that.$message.warning(res.data || '请稍后再试')
            }
            that.$refs.table.$refs.table.clearSelection() // 清空表格勾选
            that.getList()
          }
        }
        commonMultDel.call(this, req)
      }).catch(() => {})
    },
    showCheckList({ checkList }) {
      const list = checkList
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.realName)
        ids.push(item.userId)
      })
      this.stationary = this.stationary.map((item) => {
        if (item.id === this.backRow.id) {
          return { ...item, creatorName: names.join(','), creatorId: ids.join(',') }
        } else {
          return { ...item }
        }
      })
    },
    closeDialog(){
      this.userKey ++
    },
    getNodeData(data) {
      this.nodeName = data.nodeName
      this.nodeCode = data.nodeCode
      if (data.nodeCode == 'draft') {
        this.canSelectDate = true
      }
      if (data.nodeCode == 'checkBackUser') {
        this.canSelectUser = true
      }
    },

    getWorkCode(param){
      this.workflowCode=param
      this.$nextTick(()=>{
        this.$refs.workFlow.init()
      })
    },
    // 提交--先保存后提交
    async submit(){
      // let that = this;
      // let str = ''
      // if (that.nodeCode == 'draft') {
      //   that.stationary.forEach((ele, index) => {
      //     if (ele.backDate && ele.backDate != '') {

      //     } else {
      //       str = str + "第" + (index + 1) + "行，未选择要求完成时间！<br>"
      //     }
      //   })
      // }
      // if (that.nodeCode == 'checkAll') {
      //   let res = await beforeSubmitService({id:that.boId})
      //   if(res.code=='0000'){
      //     str = res.data
      //   } else {
      //     str = '提交校验报错，请联系管理员!'
      //   }
      // }
      // if (that.nodeCode == 'checkBackUser') {
      //   that.stationary.forEach((ele, index) => {
      //     if (ele.creatorId && ele.creatorId != '' && ele.creatorName && ele.creatorName != '') {

      //     } else {
      //       str = str + "第" + (index + 1) + "行，未选择工单反馈人！<br>"
      //     }
      //   })
      // }
      // if (str == '') {
      //   that.$refs.basicForm.$refs.form.validate((valid)=>{
      //     if(valid){
            this.$refs.workFlow.opendialogInitNextPath()
      //     }else{
      //       return
      //     }
      //   })
      // } else {
      //   this.$message({
      //     dangerouslyUseHTMLString: true,
      //     showClose: true,
      //     message: str,
      //     type: "warning",
      //   });
      // }
    },
    //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      let name = { code: 'name', value: this.$refs.basicForm.modelForm.name }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit(){
      let str = ''
      if (this.nodeCode == 'draft') {
        this.stationary.forEach((ele, index) => {
          if (ele.backDate && ele.backDate != '') {

          } else {
            str = str + "第" + (index + 1) + "行，未选择要求完成时间！<br>"
          }
        })
        return str
      }
      if (this.nodeCode == 'checkAll') {
        let res = await beforeSubmitService({id:this.boId})
        if(res.code=='0000'){
          str = res.data
        } else {
          str = '提交校验报错，请联系管理员!'
        }
        return str
      }
      if (this.nodeCode == 'checkBackUser') {
        this.stationary.forEach((ele, index) => {
          if (ele.creatorId && ele.creatorId != '' && ele.creatorName && ele.creatorName != '') {

          } else {
            str = str + "第" + (index + 1) + "行，未选择工单反馈人！<br>"
          }
        })
        return str
      }
      this.$refs.basicForm.$refs.form.validate((valid)=>{
        return valid?'':'请填写完整信息！'
      })
      return str
    },

    beforeNode() {
      let fileNameArr = {}
      if (this.nodeCode == 'draft') {
        let userId = []
        let userName = []
        this.stationary.forEach((ele, index) => {
          if (!userId.includes(ele.groupLeaderId)) {
            userId.push(ele.groupLeaderId)
            userName.push(ele.groupLeaderName)
          }
        })
        fileNameArr = {
          checkBackUser:{ userName: userName.join(','), userId: userId.join(',') }
        }
      }
      // 设置默认处理人
      return fileNameArr
    },
    // 必填校验
    initFormRules(param) {
      this.formRules = param
    },
    // 只读表单
    getReadonlyList(param) {
      if(param.length){
        this.$nextTick(()=>{
          this.basicConfig.forEach(item=>{
            if(param.includes(item.prop)){
              this.$set(item,'disabled',true)
            }
          })
        })
      }
    },
    // 隐藏表单
    getHideList(param) {

    },
  }
}
</script>
