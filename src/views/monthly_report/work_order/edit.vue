<template>
  <div class="page-anchor-parentpage">
    <div class="operate-btn">
      <el-button v-if="boId && dealPage" type="primary" @click="submit">提交</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm"
          :disable-form="true"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionTaskDetails" class="page-anchor-point"></div>
    <mssCard title="任务详情">
      <div slot="headerBtn">
        <el-button v-if="templateCode!='safetyfinal'" @click="viewHis">历史填报信息查看</el-button>
        <el-button
          v-if="disableSave && templateCode =='safetyfinal'"
          type="primary"
          @click="save"
        >保存</el-button>
      </div>
      <div slot="content">
        <mssReportTemplate
          v-if="templateCode"
          ref="reportTemplate"
          :yearMonth="yearMonth"
          :cityName="cityName"
          :templateCode="templateCode"
          :reporterId="reporterId"
          :isView="!disableSave"
          :boId="boId"
          :boType="'report'"
        ></mssReportTemplate>
      </div>
    </mssCard>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :boId="boId" :workflowCode="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :saveMainForm="disableSave && templateCode =='safetyfinal'"
      :saveMonthly="disableSave && templateCode !='safetyfinal'"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <el-dialog
      v-if="templateCode!='safetyfinal'"
      :title="'历史填报信息'"
      :visible.sync="dialogVisible"
      fullscreen
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <mssReportTemplate
        v-if="dialogVisible && templateCode"
        :isView="true"
        :isHis="true"
        :templateCode="templateCode"
        :reporterId="reporterId"
        :yearMonth="yearMonth"
        :cityName="cityName"
        :boId="boId"
        :boType="'report'"
      ></mssReportTemplate>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDetailService,
  getSafetyManagerService,
  getDeptReportRoleUsersService
} from '@/api/monthly_report/work_order_api.js'
import axios from 'axios'
export default {
  name: 'ReportWorkOrderEdit',
  data() {
    return {
      basicConfig: [
        {
          label: '任务名称：',
          type: 'input',
          prop: 'name',
          disabled: true
        },
        {
          label: '派发地市：',
          type: 'input',
          prop: 'cityName',
          disabled: true
        },
        {
          label: '任务处理人：',
          type: 'input',
          prop: 'reporterName',
          disabled: true
        },
        {
          label: '任务处理人角色：',
          type: 'input',
          prop: 'reportRoleName',
          disabled: true
        },
        {
          label: '任务派发时间：',
          type: 'input',
          prop: 'createDate',
          disabled: true
        },
        {
          label: '任务截至时间：',
          type: 'input',
          prop: 'planEndDate',
          disabled: true
        }
      ],
      labelPosition: 'left',
      basicForm: {},
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>详情',
          id: 'sectionTaskDetails'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow'
        }
      ],
      boId: '',
      templateCode: '', //模板id
      dealPage: true,
      workflowCode: 'SafetyReport',
      completeTaskUrl: '',
      returnAddress: '/monthly_report/work_reporting/work_order_list',
      dialogVisible: false,
      yearMonth: '',
      nodeName: '',
      nodeCode: '',
      disableSave: false,
      operateType: '',
      cityName: '',
      cityId: '',
      reporterId: '',
      supervision: {},
      officerRoleUsers:[]
    }
  },
  created() {
    this.boId = this.$route.query.boId
    this.operateType = this.$route.query.operateType
    getDetailService(this.boId).then((res) => {
      if (res && res.code == '0000') {
        this.basicForm = res.data
        if(this.basicForm.planEndDate){
          this.basicForm.planEndDate = this.$moment(this.basicForm.planEndDate).format('YYYY-MM-DD')
        }
        this.completeTaskUrl = res.data.completeTaskUrl
        this.yearMonth = res.data.yearMonth
        this.reporterId = res.data.reporterId
        this.cityName = res.data.cityName
        this.templateCode = res.data.reportTemplateCode
        this.cityId = res.data.cityId
        if (this.operateType == 'edit') {
          let req = {
            cityId: this.cityId
          }
          getSafetyManagerService(req).then((res) => {
            if (res && res.code == '0000') {
              this.supervision = res.data
            }
          })
          let data = {
            cityId:this.cityId,
            reportRoleId:'6'
          }
          getDeptReportRoleUsersService(data).then(res=>{
            if (res && res.code == '0000') {
              this.officerRoleUsers = res.data || []
            }
          })
        }
      }
    })
  },
  mounted() {
    if (this.operateType == 'edit') {
      this.$confirm(
        '请在任务截至时间前完成处理，若超过任务截至时间未处理，系统会做自动提交处理，请知晓!',
        '提示',
        {
          confirmButtonText: '确定',
          showCancelButton: false,
          closeOnClickModa: false,
          closeOnPressEscape: false,
          type: 'warning'
        }
      )
        .then(() => {})
        .catch(() => {})
      this.$refs.workFlow.init()
    } else {
      this.dealPage = false
    }
  },
  beforeDestroy(){
    if(window[this.boId]){
      delete window[this.boId]
    }
  },
  methods: {
    save(cb) {
      let reportTemplate = this.$refs.reportTemplate
      if (reportTemplate.multipleTable) {
        if (
          reportTemplate.activeName &&
          reportTemplate.$refs[reportTemplate.activeName][0] &&
          reportTemplate.$refs[reportTemplate.activeName][0].save
        ) {
          reportTemplate.$refs[reportTemplate.activeName][0].save(cb)
        }
      } else {
        if (
          reportTemplate.activeName &&
          reportTemplate.$refs[reportTemplate.activeName] &&
          reportTemplate.$refs[reportTemplate.activeName].save
        ) {
          reportTemplate.$refs[reportTemplate.activeName].save(cb)
        }
      }
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.name
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    // 提交
    submit() {
      this.$refs.workFlow.opendialogInitNextPath()
    },
    autoSave(cb){
      if(window[this.boId]){
        let reqList = []
        let flag = 0
        let that = this
        let errMsg = ""
        for (const key in window[this.boId]) {
          const element = window[this.boId][key];
          let returnObj = element.save(cb,true)
          if( typeof returnObj === 'string'){
            errMsg += returnObj + '</br>'
          }else{
            reqList.push(returnObj)
          }
        }
        if(errMsg.length > 0){
          this.$alert(`${errMsg}存在必填内容未填写，请按照要求填写后再提交！`,'提示',{
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          })
        }else if (reqList.length){
          axios.all(reqList).then(axios.spread(function(){
            for (let i = 0, len = arguments.length; i < len; i++){
              let res = arguments[i]
              if (res && res.code == '0000') {
                flag++
              }else if(res){
                that.$message({ message: res.data || res.msg || res.message, type: 'error' })
              }
            }
            if(reqList.length === flag){
              if (cb && cb.constructor === Function) {
                cb()
              }
            }
          })).catch((e) => {
            this.$message({ message: e, type: 'error' })
          })
        }
      }
    },
    beforeNode() {
      let fileNameArr = {
        audit: this.supervision
      }
      if(this.officerRoleUsers.length == 1){
        let obj = {
          ...this.officerRoleUsers[0],
          userIds:this.officerRoleUsers[0].userId
        }
        fileNameArr.officer = obj
      }else if(this.officerRoleUsers.length > 1){
        let userIds = []
        this.officerRoleUsers.forEach(item=>{
          userIds.push(item.userId)
        })
        fileNameArr.officer = {
          userIds:userIds.join()
        }
      }
      return fileNameArr
    },
    // 返回
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.returnAddress
      })
    },
    viewHis() {
      this.dialogVisible = true
    },
    getNodeData(data) {
      this.nodeName = data.nodeName
      this.nodeCode = data.nodeCode
      let ary = ['draft', 'officer']
      this.disableSave = ary.includes(this.nodeCode)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
