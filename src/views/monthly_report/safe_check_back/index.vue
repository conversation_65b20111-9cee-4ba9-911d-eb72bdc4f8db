<!--
  @author: 莫海东
  @description:月报管理--安全检查--安全检查工单反馈-列表查询
-->
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
// api
import { findPageService } from '@/api/monthly_report/safe_check_back.js'
export default {
  name: 'SafeCheckBackFindPage',
  data() {
    const validateStartEndDate = (rule, value, callback) => {
      if(this.$refs.searchForm.searchForm.startDate && this.$refs.searchForm.searchForm.endDate){
        if(new Date(this.$refs.searchForm.searchForm.startDate).getTime() > new Date(this.$refs.searchForm.searchForm.endDate).getTime()){
          callback(new Error('开始日期需小于结束日期'))
        }else{
          callback()
        }
      }else{
        callback()
      }
    }
    return {
      searchConfig: [
        {
          label: '工单名称',
          type: 'input',
          fieldName: 'name'
        },
        {
          label: '工单编号',
          type: 'input',
          fieldName: 'code'
        },
        {
          label: '创建人',
          type: 'input',
          fieldName: 'creatorName'
        },
        {
          label: '流程状态',
          type: 'select',
          options: [],
          fieldName: 'status'
        },
        {
          label: '编制开始-完成时间',
          type: 'date2',
          fieldName: 'startDate',
          fieldName2: 'endDate',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          span: 12,
          // rules: [
          //   { validator: validateStartEndDate, trigger: ['blur', 'change'] },
          // ]
        }
      ],
      api: findPageService,
      staticSearchParam:{},
      columns: [
        {
          label: '工单名称',
          prop: 'name',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '工单编号',
          prop: 'code',
          tooltip: true,
          minWidth: 130,
        },
        {
          label: '检查类型',
          prop: 'tempType',
          tooltip: true,
        },
        {
          label: '检查模板名称',
          prop: 'tempName',
          tooltip: true,
        },
        {
          label: '检查地市/区县',
          prop: 'cityNames',
          tooltip: true,
        },
        {
          label: '派发时间',
          prop: 'sendDate',
          tooltip: true,
        },
        {
          label: '创建人',
          prop: 'creatorName',
          tooltip: true,
        },
        {
          label: '状态',
          prop: 'status',
          tooltip: true,
        },
        {
          label: '操作',
          prop: '_operationCol',
          formatter: (row, column, cellValue, index) => {
            if (row.isAllowOperate) {
              return (
                <div>
                <a href='javascript:;' onClick={() => { this.operateHandle(row.id, 'view') }}>查看</a>&nbsp;&nbsp;
                <a href='javascript:;' onClick={() => { this.operateHandle(row.id, 'edit') }}>处理</a>
                </div>
              )
            } else {
              return (
                <div>
                <a href='javascript:;' onClick={() => { this.operateHandle(row.id, 'view') }}>查看</a>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标3是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.searchConfig[3], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle(boId, type){
      if (type == 'edit') {
        this.$router.push({
          path: '/monthly_report/safe_check/safe_check_back/edit',
          query: { boId }
        })
      } else if (type == 'view') {
        this.$router.push({
          path: '/monthly_report/safe_check/safe_check_back/view',
          query: { boId }
        })
      }
    },
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    reset() {
      this.search()
    },
  }
}
</script>
