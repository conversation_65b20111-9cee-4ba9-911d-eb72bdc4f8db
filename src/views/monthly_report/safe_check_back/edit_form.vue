/**
* @author: ty
* @date: 2023-07-06
* @description: 质监申报根据id编辑或者是新增工单
*/
<template>
  <div class="SafeCheckBackEditForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button type="primary" @click="submit()">提交</el-button>
      <el-button type="primary" @click="save()">保存</el-button>
      <el-button type="primary" @click="goBack()">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionBasicBack" class="page-anchor-point"></div>
    <Info v-if="myObject.checkBackId" ref="checkDetail" :editFlag="editFlag" :myObject="myObject" />

    <div id="sectionAttachment" v-if="boId && businessType" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :business-type="businessType" :node-name="nodeName" :bo-id="boId"></mssAttachment>

    <div id="sectionWorkflow" v-if="boId && businessType" class="page-anchor-point"></div>
    <mssCard v-if="boId && businessType" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :boId="boId" :workflowCode="businessType"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="businessType"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      :saveMainForm="true"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
  </div>
</template>

<script>
import Info from "@/views/monthly_report/safe_check_back/detail_form.vue";
import {
  getByIdService,
  beforeSubmitService,
  updateService
} from '@/api/monthly_report/safe_check_back.js'
export default {
  name: 'SafeCheckBackEditForm',
  components: {
    Info,
  },
  data() {
    return {
      editFlag: false,
      myObject: {type:'single'},
      pageLoading: false,
      boId:'',
      businessType: '',
      completeTaskUrl:"",//流程处理地址
      returnAddress:'/monthly_report/safe_check/safe_check_back/list',//流程提交成功返回路由地址
      nodeName:'',
      nodeCode:'',
      checkId: '',
      checkBackId: '',
      formRules:{},
      basicConfig: [
        {
          label: '工单名称：',
          type: 'input',
          prop: 'name',
          disabled: true,
          span: 12
        },
        {
          label: '工单编号：',
          type: 'input',
          prop: 'code',
          disabled: true,
          span: 12
        },
        {
          label: '检查类型：',
          type: 'input',
          prop: 'tempType',
          disabled: true,
          span: 12,
        },
        {
          label: '检查模板名称：',
          type: 'input',
          prop: 'tempName',
          disabled: true,
          span: 12,
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
          disabled: true,
          span: 12
        },
        {
          label: '创建时间：',
          type: 'input',
          prop: 'createDate',
          disabled: true,
          span: 12
        },
        {
          label: '说明：',
          type: 'input',
          mode: 'textarea',
          prop: 'description',
          span: 24
        },
        {
          label: '检查地市：',
          type: 'input',
          prop: 'cityNames',
          disabled: true,
          span: 12
        },
        {
          label: '检查组长：',
          type: 'input',
          prop: 'groupLeaderName',
          disabled: true,
          span: 12
        },
        {
          label: '组员：',
          type: 'input',
          prop: 'teamMembersNames',
          disabled: true,
          span: 12
        },
        {
          label: '要求完成时间：',
          type: 'input',
          prop: 'backDate',
          disabled: true,
          span: 12
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'top',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '反馈<br>信息',
          id: 'sectionBasicBack'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment'
        },
        {
          text: '流程<br>信息',
          id: 'sectionWorkflow'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId || null
    await this.getDataById()
  },
  methods: {
    async getDataById() {
      getByIdService(this.boId).then(res => {
        if(res && res.code === '0000'){
          // this.basicForm = res.data
          this.basicForm = Object.assign({}, this.basicForm, res.data)
          this.boId = res.data.id || null
          this.businessType = res.data.businessType
          this.completeTaskUrl = res.data.completeTaskUrl //流程处理地址
          this.getWorkCode(this.businessType)

          this.myObject.checkBackId = this.boId
          this.myObject.nodeCode = res.data.nodeCode
        }
      })
    },
    save(cb){
      // todo 提交前校验
      let that = this;
      this.pageLoading = true
      let param = {
        ...this.$refs.basicForm.modelForm
      };
      updateService(param).then(res => {
        that.$refs.checkDetail.save();
        if (cb) {
          cb(param)
        } else {
          this.$message({
            dangerouslyUseHTMLString: true,
            showClose: true,
            message: '保存成功！',
            type: "success",
          });
        }
      })
      .finally(_=> {
        this.pageLoading = false
      })
    },
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/monthly_report/safe_check/safe_check_back/list'
      })
    },

    getNodeData(data) {
      this.nodeCode = data.nodeCode
      this.nodeName = data.nodeName
      this.myObject.nodeName = data.nodeName
      if (data.nodeCode == 'draft') {
        this.canSelectDate = true
        this.editFlag = true
      }
      if (data.nodeCode == 'CheckBack') {
        this.canSelectUser = true
      }
      if (data.nodeCode == 'CheckBackAll') {
        this.$alert('请在3天内完成检查结果的确认操作，若超过3天未处理，系统会做自动提交处理，请知晓！', '提示', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: ''
        })
      }
    },
    getWorkCode(param){
      this.workflowCode=param
      this.$nextTick(()=>{
        this.$refs.workFlow.init()
      })
    },

    // 提交--先保存后提交
    async submit(){
      // let that = this
      // let msg = that.$refs.checkDetail.submitCheck();
      // if (msg == '') {
      //   let res = await beforeSubmitService({id:this.boId})
      //   if(res.data && res.data.code!=0){
      //     msg = res.data
      //   }
      // }
      // if (msg == '') {
      //   that.$refs.basicForm.$refs.form.validate((valid)=>{
      //     if (valid) {
            this.$refs.workFlow.opendialogInitNextPath()
      //     } else {
      //       return
      //     }
      //   })
      // } else {
      //   this.$message({
      //     dangerouslyUseHTMLString: true,
      //     showClose: true,
      //     message: msg,
      //     type: "warning",
      //   });
      // }
    },
    //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = []
      let name = { code: 'name', value: this.$refs.basicForm.modelForm.name }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit(){
      let msg = this.$refs.checkDetail.submitCheck();
      if (msg == '') {
        let res = await beforeSubmitService({id:this.boId})
        if(res.data && res.data.code!=0){
          msg += res.data
        }
      }
      this.$refs.basicForm.$refs.form.validate((valid)=>{
        msg+=valid?'': '有必填字段未填写完整，请检查'
      })
      return msg
    },

    beforeNode() {
      let fileNameArr = {}
      if (this.nodeCode == 'draft') {
        fileNameArr = {
          CheckBack:{ userName: this.$refs.basicForm.modelForm.groupLeaderName, userId: this.$refs.basicForm.modelForm.groupLeaderId }
        }
      }else if (this.nodeCode == 'CheckBack') {
        fileNameArr = {
          CheckBackAll:{ userName: this.$refs.basicForm.modelForm.nodeLastUserName, userId: this.$refs.basicForm.modelForm.nodeLastUserId,userIds: this.$refs.basicForm.modelForm.nodeLastUserId }
        }
      }
      // 设置默认处理人
      return fileNameArr
    },
    // 必填校验
    initFormRules(param) {
      this.formRules=param
    },
    // 只读表单
    getReadonlyList(param) {
      if(param.length){
        this.basicConfig.forEach(item=>{
          if(param.includes(item.prop)){
            this.$set(item,'disabled',true);
          }
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.SafeCheckBackEditForm{

}
</style>
