/** * @author: ty * @date: 2023-07-06 * @description:
安全检查模板根据id编辑或者是新增工单 */
<template>
  <div class="SafeCheckBackDetailEditForm" v-loading="pageLoading">
    <mssCard title="反馈详情">
      <div slot="content">
        <el-tabs v-model="tabActive" @tab-click="handleTabClick">
          <el-tab-pane
            v-for="(tab, index) in tableTabsListLast"
            :key="index"
            :label="tab.label"
            :name="tab.name"
            :lazy="tab.lazy"
          >
            <Info :ref="tab.name" :myParams="tab" :myObject="myObject" :columns="tab.columns" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </mssCard>

    <el-dialog
      :visible.sync="showDialog"
      title="附件上传"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      append-to-body
    >
      <mssAttachment v-if="showDialog && chackBackSonId" :business-type="businessType" :node-name="myObject.nodeName" :dealPage="dealFlag" :bo-id="chackBackSonId"></mssAttachment>
    </el-dialog>

  </div>
</template>

<script>
import Info from "@/views/monthly_report/safe_check_back/detail_tab_form.vue";
import { commonDown } from '@/utils/btn.js'
// api
import { saveDetailsService, getFilesDownloadService } from '@/api/monthly_report/safe_check_back.js';
export default {
  name: "SafeCheckBackDetailEditForm",
  components: {
    Info,
  },
  props: {
    editFlag: {
      type: Boolean,
      default: false
    },
    myObject: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showDialog: false,
      chackBackSonId: '',
      businessType: 'SafeCheckBackSon',
      dealFlag: 'false',
      pageLoading: false,
      id: "",
      tabActive: this.myObject.type == 'total' ? 'totals' : 'safeWork',
      tableTabsList: [
        {
          id: 0,
          name: "totals",
          label: "总表",
          lazy: false,
          columns: [
            {
              label: "安全生产检查表",
              prop: "",
              multilevelColumn: [
                {
                  label: "检查项目",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 100,
                },
                {
                  label: "检查方法",
                  prop: "itemContent",
                  tooltip: true,
                  minWidth: 280,
                },
                {
                  label: "方式",
                  prop: "checkSituation",
                  tooltip: true,
                  minWidth: 80,
                },
                {
                  label: "发现问题数量",
                  prop: "attribute1",
                  multilevelColumn: [
                    {
                      label: "一般",
                      prop: "attribute1",
                      tooltip: true,
                      minWidth: 80,
                    },
                    {
                      label: "较严重",
                      prop: "attribute2",
                      tooltip: true,
                      minWidth: 80,
                    },
                    {
                      label: "严重",
                      prop: "attribute3",
                      tooltip: true,
                      minWidth: 80,
                    },
                    {
                      label: "特别严重",
                      prop: "attribute4",
                      tooltip: true,
                      minWidth: 80,
                    },
                  ],
                },
                {
                  label: "发现问题说明",
                  prop: "quesTypeName",
                  minWidth: 100,
                  formatter:(row, column, cellValue, index)=>{
                    return (<div domPropsInnerHTML={row.quesTypeName}></div>)
                  },
                },
              ],
            },
          ],
        },
        {
          id: 1,
          name: "safeWork",
          label: "安全工作整体落实",
          lazy: false,
          columns: [
            {
              label: "安全工作整体落实情况检查表",
              prop: "itemName",
              multilevelColumn: [
                {
                  label: "检查项目",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 60,
                },
                {
                  label: "检查内容",
                  prop: "itemContent",
                  tooltip: true,
                  minWidth: 180,
                },
                {
                  label: "是否存在问题",
                  minWidth: 60,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<span>{row.hasProbName}</span>)
                    }else{
                      return (
                        <el-radio-group v-model={row.hasProbId} onChange={(value) => { this.operateProbHandle(value, row) }}>
                          <el-radio label={1}>是</el-radio>
                          <el-radio label={2}>否</el-radio>
                        </el-radio-group>
                      )
                    }
                  }
                },
                {
                  label: "检查情况、问题描述",
                  prop: "checkSituation",
                  minWidth: 100,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<div domPropsInnerHTML={row.checkSituation}></div>)
                    }else{
                      return (
                        <el-input type="textarea" v-model={row.checkSituation} 
                          onChange={(value) => { this.operateSituHandle(value, row) }}></el-input>
                      )
                    }
                  }
                },
                {
                  label: "问题分类（参考负面行为清单，一般/较为严重/严重/特别严重问题）",
                  prop: "quesTypeId",
                  minWidth: 200,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<div domPropsInnerHTML={row.quesTypeName}></div>)
                    }else{
                      return (
                        <el-radio-group v-model={row.quesTypeId} onChange={(value) => { this.operateQuesHandle(value, row) }}>
                          <el-radio label={1}>一般问题</el-radio>
                          <el-radio label={2}>较为严重问题</el-radio>
                          <el-radio label={3}>严重问题</el-radio>
                          <el-radio label={4}>特别严重问题</el-radio>
                        </el-radio-group>
                      )
                    }
                  }
                },
                {
                  label: "附件",
                  prop: '_operationCol',
                  minWidth: 40,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      if (this.myObject.type == 'single') {
                        return (
                          <a href='javascript:;' onClick={() => { this.uploadFile(row, false) }}>查看附件</a>
                        )
                      } else {
                        return (
                          <a href='javascript:;' onClick={() => { this.batchDownLoad(row) }}>批量下载</a>
                        )
                      }
                    }else{
                      return (
                        <a href='javascript:;' onClick={() => { this.uploadFile(row, true) }}>上传</a>
                      )
                    }
                  }
                },
              ],
            },
          ],
        },
        {
          id: 2,
          name: "constructUnit",
          label: "建设单位安全措施",
          lazy:true,
          columns: [
            {
              label: "建设单位项目安全措施检查表",
              prop: "itemName",
              multilevelColumn: [
                {
                  label: "保障措施及责任落实内容",
                  prop: "itemContent",
                  tooltip: true,
                  minWidth: 240,
                },
                {
                  label: "是否存在问题",
                  minWidth: 60,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<span>{row.hasProbName}</span>)
                    }else{
                      return (
                        <el-radio-group v-model={row.hasProbId} onChange={(value) => { this.operateProbHandle(value, row) }}>
                          <el-radio label={1}>是</el-radio>
                          <el-radio label={2}>否</el-radio>
                        </el-radio-group>
                      )
                    }
                  }
                },
                {
                  label: "检查情况、问题描述",
                  prop: "checkSituation",
                  minWidth: 100,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<div domPropsInnerHTML={row.checkSituation}></div>)
                    }else{
                      return (
                        <el-input type="textarea" v-model={row.checkSituation} 
                          onChange={(value) => { this.operateSituHandle(value, row) }}></el-input>
                      )
                    }
                  }
                },
                {
                  label: "问题分类",
                  prop: "quesTypeId",
                  minWidth: 200,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<div domPropsInnerHTML={row.quesTypeName}></div>)
                    }else{
                      return (
                        <el-radio-group v-model={row.quesTypeId} onChange={(value) => { this.operateQuesHandle(value, row) }}>
                          <el-radio label={1}>一般问题</el-radio>
                          <el-radio label={2}>较为严重问题</el-radio>
                          <el-radio label={3}>严重问题</el-radio>
                          <el-radio label={4}>特别严重问题</el-radio>
                        </el-radio-group>
                      )
                    }
                  }
                },
                {
                  label: "附件",
                  prop: '_operationCol',
                  minWidth: 40,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      if (this.myObject.type == 'single') {
                        return (
                          <a href='javascript:;' onClick={() => { this.uploadFile(row, false) }}>查看附件</a>
                        )
                      } else {
                        return (
                          <a href='javascript:;' onClick={() => { this.batchDownLoad(row) }}>批量下载</a>
                        )
                      }
                    }else{
                      return (
                        <a href='javascript:;' onClick={() => { this.uploadFile(row, true) }}>上传</a>
                      )
                    }
                  }
                },
              ],
            },
          ],
        },
        {
          id: 3,
          name: "coopUnit",
          label: "合作单位安全措施",
          lazy:true,
          columns: [
            {
              label: "合作单位项目安全措施检查表",
              prop: "itemName",
              multilevelColumn: [
                {
                  label: "责任主体",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 60,
                },
                {
                  label: "检查内容",
                  prop: "itemContent",
                  tooltip: true,
                  minWidth: 180,
                },
                {
                  label: "是否存在问题",
                  minWidth: 60,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<span>{row.hasProbName}</span>)
                    }else{
                      return (
                        <el-radio-group v-model={row.hasProbId} onChange={(value) => { this.operateProbHandle(value, row) }}>
                          <el-radio label={1}>是</el-radio>
                          <el-radio label={2}>否</el-radio>
                        </el-radio-group>
                      )
                    }
                  }
                },
                {
                  label: "检查情况、问题描述",
                  prop: "checkSituation",
                  minWidth: 100,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<div domPropsInnerHTML={row.checkSituation}></div>)
                    }else{
                      return (
                        <el-input type="textarea" v-model={row.checkSituation} 
                          onChange={(value) => { this.operateSituHandle(value, row) }}></el-input>
                      )
                    }
                  }
                },
                {
                  label: "问题分类",
                  prop: "quesTypeId",
                  minWidth: 200,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<div domPropsInnerHTML={row.quesTypeName}></div>)
                    }else{
                      return (
                        <el-radio-group v-model={row.quesTypeId} onChange={(value) => { this.operateQuesHandle(value, row) }}>
                          <el-radio label={1}>一般问题</el-radio>
                          <el-radio label={2}>较为严重问题</el-radio>
                          <el-radio label={3}>严重问题</el-radio>
                          <el-radio label={4}>特别严重问题</el-radio>
                        </el-radio-group>
                      )
                    }
                  }
                },
                {
                  label: "附件",
                  prop: '_operationCol',
                  minWidth: 40,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      if (this.myObject.type == 'single') {
                        return (
                          <a href='javascript:;' onClick={() => { this.uploadFile(row, false) }}>查看附件</a>
                        )
                      } else {
                        return (
                          <a href='javascript:;' onClick={() => { this.batchDownLoad(row, true) }}>批量下载</a>
                        )
                      }
                    }else{
                      return (
                        <a href='javascript:;' onClick={() => { this.uploadFile(row) }}>上传</a>
                      )
                    }
                  }
                },
              ],
            },
          ],
        },
        {
          id: 4,
          name: "partnerEntity",
          label: "合作单位主体责任",
          lazy:true,
          columns: [
            {
              label: "合作单位主体责任落实检查表",
              prop: "itemName",
              multilevelColumn: [
                {
                  label: "责任主体",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 60,
                },
                {
                  label: "检查内容",
                  prop: "itemContent",
                  tooltip: true,
                  minWidth: 180,
                },
                {
                  label: "是否存在问题",
                  minWidth: 60,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<span>{row.hasProbName}</span>)
                    }else{
                      return (
                        <el-radio-group v-model={row.hasProbId} onChange={(value) => { this.operateProbHandle(value, row) }}>
                          <el-radio label={1}>是</el-radio>
                          <el-radio label={2}>否</el-radio>
                        </el-radio-group>
                      )
                    }
                  }
                },
                {
                  label: "检查情况、问题描述",
                  prop: "checkSituation",
                  minWidth: 100,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<div domPropsInnerHTML={row.checkSituation}></div>)
                    }else{
                      return (
                        <el-input type="textarea" v-model={row.checkSituation} 
                          onChange={(value) => { this.operateSituHandle(value, row) }}></el-input>
                      )
                    }
                  }
                },
                {
                  label: "问题分类",
                  prop: "quesTypeId",
                  minWidth: 200,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      return (<div domPropsInnerHTML={row.quesTypeName}></div>)
                    }else{
                      return (
                        <el-radio-group v-model={row.quesTypeId} onChange={(value) => { this.operateQuesHandle(value, row) }}>
                          <el-radio label={1}>一般问题</el-radio>
                          <el-radio label={2}>较为严重问题</el-radio>
                          <el-radio label={3}>严重问题</el-radio>
                          <el-radio label={4}>特别严重问题</el-radio>
                        </el-radio-group>
                      )
                    }
                  }
                },
                {
                  label: "附件",
                  prop: '_operationCol',
                  minWidth: 40,
                  formatter:(row, column, cellValue, index)=>{
                    if(!this.editFlag){
                      if (this.myObject.type == 'single') {
                        return (
                          <a href='javascript:;' onClick={() => { this.uploadFile(row, false) }}>查看附件</a>
                        )
                      } else {
                        return (
                          <a href='javascript:;' onClick={() => { this.batchDownLoad(row) }}>批量下载</a>
                        )
                      }
                    }else{
                      return (
                        <a href='javascript:;' onClick={() => { this.uploadFile(row, true) }}>上传</a>
                      )
                    }
                  }
                },
              ],
            },
          ],
        },
        {
          id: 5,
          name: "assessmentRules",
          label: "负面行为问题分类参考",
          lazy:true,
          columns: [
            {
              label: "中国移动江西公司通信工程管理负面清单（安全管理类）",
              prop: "itemName",
              multilevelColumn: [
                {
                  label: "责任主体",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 80,
                },
                {
                  label: "问题及标准",
                  prop: "itemContent",
                  tooltip: true,
                  minWidth: 700,
                },
                {
                  label: "一般问题",
                  prop: "attribute1",
                  tooltip: true,
                  minWidth: 80,
                },
                {
                  label: "较为严重问题",
                  prop: "attribute2",
                  tooltip: true,
                  minWidth: 80,
                },
                {
                  label: "严重问题",
                  prop: "attribute3",
                  tooltip: true,
                  minWidth: 80,
                },
                {
                  label: "特别严重问题",
                  prop: "attribute4",
                  tooltip: true,
                  minWidth: 80,
                },
              ],
            },
          ],
        },
      ],
      flag:true,
    };
  },

  watch: {
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query;
    this.id = urlQuery.boId || null;
    this.flag = this.$route.path.includes('/view')
  },
  computed: {
    tableTabsListLast: {
      get: function() {
        let arr = [];
        let that = this
        that.tableTabsList.forEach((ele, index)=>{
          if (that.myObject.type == 'single') {
            if (that.myObject.nodeCode == 'CheckBackAll' || that.myObject.nodeCode == 'CheckBack1' || that.flag) {
              arr.push(ele)
            } else {
              if (ele.id != 0) {
                arr.push(ele)
              }
            }
          } else {
            arr.push(ele)
          }
        });
        that.tabActive = arr[0].name
        return arr
      }
    }
  },
  methods: {
    // 点击操作列的按钮，处理或者查看单子
    operateProbHandle(value, row){
      if (value == 1) {
        row.hasProbName = '是'
      } else if (value == 2) {
        row.hasProbName = '否'
        row.checkSituation = ''
        row.quesTypeId = ''
        row.quesTypeName = ''
      }
    },
    operateSituHandle(value, row){
      if (value != '') {
        row.hasProbId = 1
        row.hasProbName = '是'
      }
    },
    operateQuesHandle(value, row){
      if (value == 1) {
        row.quesTypeName = '一般问题'
      } else if (value == 2) {
        row.quesTypeName = '较为严重问题'
      } else if (value == 3) {
        row.quesTypeName = '严重问题'
      } else if (value == 4) {
        row.quesTypeName = '特别严重问题'
      }
      row.hasProbId = 1
      row.hasProbName = '是'
    },
    save() {
      // todo 提交前校验
      this.pageLoading = true;
      const arr = new Array;
      if (this.$refs.safeWork && this.$refs.safeWork.length > 0 && this.$refs.safeWork[0].$children && this.$refs.safeWork[0].$children.length > 0) {
        this.$refs.safeWork[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }
      if (this.$refs.constructUnit && this.$refs.constructUnit.length > 0 && this.$refs.constructUnit[0].$children && this.$refs.constructUnit[0].$children.length > 0) {
        this.$refs.constructUnit[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }
      if (this.$refs.coopUnit && this.$refs.coopUnit.length > 0 && this.$refs.coopUnit[0].$children && this.$refs.coopUnit[0].$children.length > 0) {
        this.$refs.coopUnit[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }
      if (this.$refs.partnerEntity && this.$refs.partnerEntity.length > 0 && this.$refs.partnerEntity[0].$children && this.$refs.partnerEntity[0].$children.length > 0) {
        this.$refs.partnerEntity[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }
      if (this.$refs.assessmentRules && this.$refs.assessmentRules.length > 0 && this.$refs.assessmentRules[0].$children && this.$refs.assessmentRules[0].$children.length > 0) {
        this.$refs.assessmentRules[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }

      if (arr.length > 0) {
        let ff = {};
        ff.id = this.id;
        ff.sons = arr;
        saveDetailsService(ff)
          .then((res) => {
            
          })
          .finally((_) => {
            this.pageLoading = false;
          });
      } else {
        this.pageLoading = false;
      }
    },
    handleTabClick(tab) {
      if (tab.$children && tab.$children.length > 0 && !this.editFlag) {
        tab.$children[0].$refs.table.getTableData()
      }
    },
    submitCheck() {
      let that = this
      let msg=""
      if (that.$refs.safeWork && that.$refs.safeWork.length > 0 && that.$refs.safeWork[0].$children && that.$refs.safeWork[0].$children.length > 0) {
        that.$refs.safeWork[0].$children[0].tableData.forEach((ele, index) => {
          if ((ele.hasProbId||'') == '') {
            msg = msg + "安全工作整体落实 第"+(index+1)+"行“是否存在问题”未填写完全！<br>"
          } else if((ele.hasProbId||'') == '1') {
            if ((ele.checkSituation||'') == '' || (ele.quesTypeId||'') == '') {
              msg = msg + "安全工作整体落实 第"+(index+1)+"行“问题描述、问题分类”未填写完全！<br>"
            }
          }
        })
      }
      if (this.$refs.constructUnit && this.$refs.constructUnit.length > 0 && this.$refs.constructUnit[0].$children && this.$refs.constructUnit[0].$children.length > 0) {
        this.$refs.constructUnit[0].$children[0].tableData.forEach((ele, index) => {
          if ((ele.hasProbId||'') == '') {
            msg = msg + "建设单位安全措施 第"+(index+1)+"行“是否存在问题”未填写完全！<br>"
          } else if((ele.hasProbId||'') == '1') {
            if ((ele.checkSituation||'') == '' || (ele.quesTypeId||'') == '') {
              msg = msg + "建设单位安全措施 第"+(index+1)+"行“问题描述、问题分类”未填写完全！<br>"
            }
          }
        })
      }
      if (this.$refs.coopUnit && this.$refs.coopUnit.length > 0 && this.$refs.coopUnit[0].$children && this.$refs.coopUnit[0].$children.length > 0) {
        this.$refs.coopUnit[0].$children[0].tableData.forEach((ele, index) => {
          if ((ele.hasProbId||'') == '') {
            msg = msg + "合作单位安全措施 第"+(index+1)+"行“是否存在问题”未填写完全！<br>"
          } else if((ele.hasProbId||'') == '1') {
            if ((ele.checkSituation||'') == '' || (ele.quesTypeId||'') == '') {
              msg = msg + "合作单位安全措施 第"+(index+1)+"行“问题描述、问题分类”未填写完全！<br>"
            }
          }
        })
      }
      if (this.$refs.partnerEntity && this.$refs.partnerEntity.length > 0 && this.$refs.partnerEntity[0].$children && this.$refs.partnerEntity[0].$children.length > 0) {
        this.$refs.partnerEntity[0].$children[0].tableData.forEach((ele, index) => {
          if ((ele.hasProbId||'') == '') {
            msg = msg + "合作单位主体责任 第"+(index+1)+"行“是否存在问题”未填写完全！<br>"
          } else if((ele.hasProbId||'') == '1') {
            if ((ele.checkSituation||'') == '' || (ele.quesTypeId||'') == '') {
              msg = msg + "合作单位主体责任 第"+(index+1)+"行“问题描述、问题分类”未填写完全！<br>"
            }
          }
        })
      }
      return msg;
    },
    batchDownLoad(row){
      commonDown({ ids: row.ids.split(',') }, getFilesDownloadService)
    },
    uploadFile(row, dealFlag){
      let that = this
      that.chackBackSonId = row.id
      that.dealFlag = dealFlag
      that.showDialog = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.SafeCheckBackDetailEditForm {
}
</style>
