/**
* @author: ty
* @date: 2023-07-06
* @description: 质监申报根据id编辑或者是新增工单
*/
<template>
  <div class="SafeCheckBackViewForm page-anchor-parentpage" v-loading="pageLoading">
    <div class="operate-btn">
      <el-button type="primary" @click="goBack()">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          disable-form
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"></mssForm>
      </div>
    </mssCard>

    <div id="sectionBasicBack" class="page-anchor-point"></div>
    <Info v-if="myObject.checkBackId" ref="checkDetail" :editFlag=false :myObject="myObject" />

    <div id="sectionAttachment" v-if="boId && businessType" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" :business-type="businessType" :node-name="nodeName" :bo-id="boId" :dealPage="false"></mssAttachment>

    <div id="sectionWorkflow" v-if="boId && businessType" class="page-anchor-point"></div>
    <mssCard v-if="boId && businessType" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :boId="boId" :workflowCode="businessType"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import Info from "@/views/monthly_report/safe_check_back/detail_form.vue";
import {
  getByIdService,
} from '@/api/monthly_report/safe_check_back.js'
export default {
  name: 'SafeCheckBackViewForm',
  components: {
    Info,
  },
  data() {
    return {
      myObject: {type:'single'},
      pageLoading: false,
      boId: '',
      businessType: '',
      completeTaskUrl:"",//流程处理地址
      returnAddress:'/safe_check/safe_check_back/list',//流程提交成功返回路由地址
      nodeName: '草稿',
      checkId: '',
      checkBackId: '',
      formRules:{},
      basicConfig: [
        {
          label: '工单名称：',
          type: 'input',
          prop: 'name',
          span: 12
        },
        {
          label: '工单编号：',
          type: 'input',
          prop: 'code',
          span: 12
        },
        {
          label: '检查类型：',
          type: 'input',
          prop: 'tempType',
          span: 12,
        },
        {
          label: '检查模板名称：',
          type: 'input',
          prop: 'tempName',
          span: 12,
        },
        {
          label: '创建人：',
          type: 'input',
          prop: 'creatorName',
          span: 12
        },
        {
          label: '创建时间：',
          type: 'input',
          prop: 'createDate',
          span: 12
        },
        {
          label: '说明：',
          type: 'input',
          mode: 'textarea',
          prop: 'description',
          span: 24
        },
      ],
      basicForm: {
        name: ''
      },
      labelPosition: 'left',
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '反馈<br>信息',
          id: 'sectionBasicBack'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment'
        },
        {
          text: '流程<br>信息',
          id: 'sectionWorkflow'
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.boId = urlQuery.boId || null
    this.getDataById()
  },
  methods: {
    getDataById() {
      getByIdService(this.boId).then(res => {
        if(res && res.code === '0000'){
          this.basicForm = res.data
          this.boId = res.data.id || null
          this.businessType = res.data.businessType

          this.myObject.checkBackId = this.boId
          
          this.$emit("getWorkCode",this.businessType)
        }
      })
    },
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/monthly_report/safe_check/safe_check_back/list'
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.SafeCheckBackViewForm{

}
</style>
