/**
* @author: ty
* @date: 2023-07-06
* @description: 安全检查模板根据id编辑或者是新增工单
*/
<template>
  <div class="SafeCheckBackDetailTabEditForm" v-loading="pageLoading">
    <mssTable
      ref="table"
      :api="tableApi"
      :columns="columns"
      :pagination="false"
      :staticSearchParam="staticSearchParam"
      border
    >
    </mssTable>
  </div>
</template>

<script>
import {
  getDetailService,
} from '@/api/monthly_report/safe_check_back.js'
export default {
  name: 'SafeCheckBackDetailTabEditForm',
  props: {
    myParams: {
      type: Object,
      default: () => ({}),
    },
    myObject: {
      type: Object,
      default: () => ({}),
    },
    columns:{
      type:Array,
      default: () => ([]),
    },
  },
  data() {
    return {
      pageLoading: false,
      id: '',
      tableApi: getDetailService,
      staticSearchParam:{},
    }
  },
  async created() {
    this.staticSearchParam.type = this.myObject.type
    this.staticSearchParam.checkId = this.myObject.checkId || null
    this.staticSearchParam.checkBackId = this.myObject.checkBackId || null
    this.staticSearchParam.typeCode = this.myParams.name
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.SafeCheckBackDetailTabEditForm{

}
</style>
