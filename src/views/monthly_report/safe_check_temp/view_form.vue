/** * @author: ty * @date: 2023-07-06 * @description:
安全检查模板根据id编辑或者是新增工单 */
<template>
  <div
    class="SafeCheckTempViewForm page-anchor-parentpage"
    v-loading="pageLoading"
  >
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic1" class="page-anchor-point"></div>
    <mssCard title="模板详情">
      <div slot="content">
        <el-tabs v-model="tabActive" @tab-click="handleTabClick">
          <el-tab-pane
            v-for="(tab, index) in tableTabsList"
            :key="index"
            :label="tab.label"
            :name="tab.name"
            :lazy="tab.lazy"
          >
            <Info :ref="tab.name" :myParams="tab" :columns="tab.columns" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import Info from "@/views/monthly_report/safe_check_temp/edit_detail_form.vue";
export default {
  name: "SafeCheckTempViewForm",
  components: {
    Info,
  },
  data() {
    return {
      pageLoading: false,
      boId: "",
      businessType: "",
      basicForm: {
        name: "",
      },
      labelPosition: "top",
      pageAnchorConfig: [
        {
          text: "模板<br>详情",
          id: "sectionBasic",
        },
      ],
      tabActive: "safeWork",
      tableTabsList: [
        {
          id: 1,
          name: "safeWork",
          label: "安全工作整体落实",
          lazy:false,
          columns: [
            {
              label: "检查项目",
              prop: "itemName",
              minWidth: 150,
            },
            {
              label: "检查内容",
              prop: "itemContent",
              minWidth: 600,
            },
          ],
        },
        {
          id: 2,
          name: "constructUnit",
          label: "建设单位安全措施",
          lazy:true,
          columns: [
            {
              label: "保障措施及责任落实内容",
              prop: "itemContent",
              minWidth: 600,
            },
          ],
        },
        {
          id: 3,
          name: "coopUnit",
          label: "合作单位安全措施",
          lazy:true,
          columns: [
            {
              label: "责任主体",
              prop: "itemName",
              minWidth: 150,
            },
            {
              label: "检查内容",
              prop: "itemContent",
              minWidth: 600,
            },
          ],
        },
        {
          id: 4,
          name: "partnerEntity",
          label: "合作单位主体责任",
          lazy:true,
          columns: [
            {
              label: "责任主体",
              prop: "itemName",
              minWidth: 150,
            },
            {
              label: "检查内容",
              prop: "itemContent",
              minWidth: 600,
            },
          ],
        },
        {
          id: 5,
          name: "assessmentRules",
          label: "负面行为问题分类参考",
          lazy:true,
          columns: [
            {
              label: "责任主体",
              prop: "itemName",
              minWidth: 150,
            },
            {
              label: "问题及标准",
              prop: "itemContent",
              minWidth: 600,
              
            },
            {
              label: "一般问题",
              prop: "attribute1",
              minWidth: 100,
              
            },
            {
              label: "较为严重问题",
              prop: "attribute2",
              minWidth: 100,
              
            },
            {
              label: "严重问题",
              prop: "attribute3",
              minWidth: 100,
              
            },
            {
              label: "特别严重问题",
              prop: "attribute4",
              minWidth: 100,
              
            },
          ],
        },
      ],
    };
  },

  watch: {
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query;
    this.boId = urlQuery.boId || null;
  },
  methods: {
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: "/monthly_report/safe_check/safe_check_temp/list",
      });
    },
    handleTabClick(tab) {
      if (tab.$children && tab.$children.length > 0 && !this.editFlag) {
        tab.$children[0].$refs.table.getTableData()
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.SafeCheckTempViewForm {
}
</style>
