/**
* @author: ty
* @date: 2023-07-06
* @description: 安全检查模板根据id编辑或者是新增工单
*/
<template>
  <div class="SafeCheckSonTempEditForm page-anchor-parentpage" v-loading="pageLoading">
    <mssTable
          ref="table"
          :api="tableApi"
          :columns="columns"
          :pagination="false"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
  </div>
</template>

<script>
import {
  getDetailService,
} from '@/api/monthly_report/safe_check_temp.js'
export default {
  name: 'SafeCheckSonTempEditForm',
  props: {
    myParams: {
      type: Object,
      default: () => ({}),
    },
    columns:{
      type:Array,
      default:()=>[]
    },
  },
  data() {
    return {
      pageLoading: false,
      boId: '',
      tableApi: getDetailService,
      staticSearchParam:{},
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.staticSearchParam.parentTempId = urlQuery.boId
    this.staticSearchParam.typeCode = this.myParams.name
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.SafeCheckSonTempEditForm{

}
</style>
