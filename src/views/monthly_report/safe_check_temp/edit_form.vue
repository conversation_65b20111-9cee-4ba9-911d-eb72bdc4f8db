/** * @author: ty * @date: 2023-07-06 * @description:
安全检查模板根据id编辑或者是新增工单 */
<template>
  <div
    class="SafeCheckTempEditForm page-anchor-parentpage"
    v-loading="pageLoading"
  >
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic1" class="page-anchor-point"></div>
    <mssCard title="模板详情">
      <div slot="headerBtn">
        <el-button v-if="oprateType == 'writable'" @click="edit" type="primary">编辑</el-button>
        <el-button v-if="editFlag" @click="save" type="primary">保存</el-button>
      </div>
      <div slot="content">
        <el-tabs v-model="tabActive" @tab-click="handleTabClick">
          <el-tab-pane
            v-for="(tab, index) in tableTabsList"
            :key="index"
            :label="tab.label"
            :name="tab.name"
            :lazy="tab.lazy"
          >
            <Info :ref="tab.name" :myParams="tab" :columns="tab.columns" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
const AUTHORITY_OPRATE_CODE ='safe_check_temp_operate'
import Info from "@/views/monthly_report/safe_check_temp/edit_detail_form.vue";
// api
import { saveDetailsService } from '@/api/monthly_report/safe_check_temp.js';
export default {
  name: "SafeCheckTempEditForm",
  components: {
    Info,
  },
  data() {
    return {
      pageLoading: false,
      boId: "",
      businessType: "",
      basicForm: {
        name: "",
      },
      labelPosition: "top",
      pageAnchorConfig: [
        {
          text: "模板<br>详情",
          id: "sectionBasic",
        },
      ],
      tabActive: "safeWork",
      tableTabsList: [
        {
          id: 1,
          name: "safeWork",
          label: "安全工作整体落实",
          lazy:false,
          columns: [
            {
              label: "安全工作整体落实情况检查表",
              prop: "itemName",
              children: [
                {
                  label: "检查项目",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 150,
                },
                {
                  label: "检查内容",
                  prop: "itemContent",
                  minWidth: 600,
                  formatter:(row)=>{
                    if(!this.editFlag){
                      return (<span>{row.itemContent}</span>)
                    }else{
                      return (
                        <el-input v-model={row.itemContent}></el-input>
                      )
                    }
                  }
                },
              ]
            },
          ],
        },
        {
          id: 2,
          name: "constructUnit",
          label: "建设单位安全措施",
          lazy:true,
          columns: [
            {
              label: "建设单位项目安全措施检查表",
              prop: "itemName",
              children: [
                {
                  label: "保障措施及责任落实内容",
                  prop: "itemContent",
                  minWidth: 600,
                  formatter:(row)=>{
                    if(!this.editFlag){
                      return (<span>{row.itemContent}</span>)
                    }else{
                      return (
                        <el-input v-model={row.itemContent}></el-input>
                      )
                    }
                  }
                },
              ]
            },
          ],
        },
        {
          id: 3,
          name: "coopUnit",
          label: "合作单位安全措施",
          lazy:true,
          columns: [
            {
              label: "合作单位项目安全措施检查表",
              prop: "itemName",
              children: [
                {
                  label: "责任主体",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 150,
                },
                {
                  label: "检查内容",
                  prop: "itemContent",
                  minWidth: 600,
                  formatter:(row)=>{
                    if(!this.editFlag){
                      return (<span>{row.itemContent}</span>)
                    }else{
                      return (
                        <el-input v-model={row.itemContent}></el-input>
                      )
                    }
                  }
                },
              ]
            },
          ],
        },
        {
          id: 4,
          name: "partnerEntity",
          label: "合作单位主体责任",
          lazy:true,
          columns: [
            {
              label: "合作单位主体责任落实检查表",
              prop: "itemName",
              children: [
                {
                  label: "责任主体",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 150,
                },
                {
                  label: "检查内容",
                  prop: "itemContent",
                  minWidth: 600,
                  formatter:(row)=>{
                    if(!this.editFlag){
                      return (<span>{row.itemContent}</span>)
                    }else{
                      return (
                        <el-input v-model={row.itemContent}></el-input>
                      )
                    }
                  }
                },
              ]
            },
          ],
        },
        {
          id: 5,
          name: "assessmentRules",
          label: "负面行为问题分类参考",
          lazy:true,
          columns: [
            {
              label: "中国移动江西公司通信工程管理负面清单（安全管理类）",
              prop: "itemName",
              children: [
                {
                  label: "责任主体",
                  prop: "itemName",
                  tooltip: true,
                  minWidth: 150,
                },
                {
                  label: "问题及标准",
                  prop: "itemContent",
                  tooltip: true,
                  minWidth: 600,
                },
                {
                  label: "一般问题",
                  prop: "attribute1",
                  tooltip: true,
                  minWidth: 100,
                },
                {
                  label: "较为严重问题",
                  prop: "attribute2",
                  tooltip: true,
                  minWidth: 100,
                },
                {
                  label: "严重问题",
                  prop: "attribute3",
                  tooltip: true,
                  minWidth: 100,
                },
                {
                  label: "特别严重问题",
                  prop: "attribute4",
                  tooltip: true,
                  minWidth: 100,
                },
              ]
            },
          ],
        },
      ],
      editFlag: false,
      oprateType: '',
    };
  },

  watch: {
  },
  async created() {
    const authorities = JSON.parse(sessionStorage.getItem('authorities'))
    let index = authorities.findIndex(item=>{
      return item.authority === AUTHORITY_OPRATE_CODE
    })
    this.oprateType = index!=-1 ? 'writable' : 'readonly'
    // 浏览器传参
    const urlQuery = this.$route.query;
    this.boId = urlQuery.boId || null;
  },
  methods: {
    edit(){
      this.editFlag = true
    },
    save() {
      // todo 提交前校验
      this.pageLoading = true;
      const arr = new Array;
      if (this.$refs.safeWork && this.$refs.safeWork.length > 0 && this.$refs.safeWork[0].$children && this.$refs.safeWork[0].$children.length > 0) {
        this.$refs.safeWork[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }
      if (this.$refs.constructUnit && this.$refs.constructUnit.length > 0 && this.$refs.constructUnit[0].$children && this.$refs.constructUnit[0].$children.length > 0) {
        this.$refs.constructUnit[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }
      if (this.$refs.coopUnit && this.$refs.coopUnit.length > 0 && this.$refs.coopUnit[0].$children && this.$refs.coopUnit[0].$children.length > 0) {
        this.$refs.coopUnit[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }
      if (this.$refs.partnerEntity && this.$refs.partnerEntity.length > 0 && this.$refs.partnerEntity[0].$children && this.$refs.partnerEntity[0].$children.length > 0) {
        this.$refs.partnerEntity[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }
      if (this.$refs.assessmentRules && this.$refs.assessmentRules.length > 0 && this.$refs.assessmentRules[0].$children && this.$refs.assessmentRules[0].$children.length > 0) {
        this.$refs.assessmentRules[0].$children[0].tableData.forEach((ele) => {
          arr.push(JSON.parse(JSON.stringify(ele)));
        })
      }

      if (arr.length > 0) {
        let ff = {};
        ff.id = this.boId;
        ff.dtos = arr;
        saveDetailsService(ff)
          .then((res) => {
            this.editFlag = false
          })
          .finally((_) => {
            this.pageLoading = false;
          });
      } else {
        this.pageLoading = false;
      }
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: "/monthly_report/safe_check/safe_check_temp/list",
      });
    },
    handleTabClick(tab) {
      if (tab.$children && tab.$children.length > 0 && !this.editFlag) {
        tab.$children[0].$refs.table.getTableData()
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.SafeCheckTempEditForm {
}
</style>
