<!--
  @author: 莫海东
  @description:月报管理--安全检查--模板管理-列表查询
-->
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :searchConfig="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <!-- <div slot="headerBtn">
        <el-button @click="exportHandle" type="primary">导出</el-button>
        <el-button @click="exportAttachmentHandle" type="primary">导出附件</el-button>
      </div> -->
      <div slot="content">
        <mssTable
          ref="table"
          selection
          :api="api"
          :columns="columns"
          :staticSearchParam="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
const AUTHORITY_OPRATE_CODE ='safe_check_temp_operate'
// api
import { findPageService } from '@/api/monthly_report/safe_check_temp.js'
export default {
  name: 'SafeCheckTempFindPage',
  data() {
    return {
      searchConfig: [
        {
          label: '模板名称',
          type: 'input',
          fieldName: 'name'
        },
      ],
      api: findPageService,
      staticSearchParam:{},
      oprateType: '',
      columns: [
        {
          label: '模板类型',
          prop: 'entityType',
          tooltip: true,
          minWidth: 200,
        },
        {
          label: '模板名称',
          prop: 'name',
          tooltip: true,
          minWidth: 200
        },
        {
          label: '创建时间',
          prop: 'createDate',
          tooltip: true,
          width:150
        },
        {
          label: '最后一次修订人',
          prop: 'updateUserName',
          tooltip: true,
          width:130
        },
        {
          label: '修订人员部门',
          prop: 'updateOrgName',
          tooltip: true,
          width:130
        },
        {
          label: '最后一次修订时间',
          prop: 'updateDate',
          tooltip: true,
          width:150
        },
        {
          label: '操作',
          prop: '_operationCol',
          formatter: (row, column, cellValue, index) => {
            if (this.oprateType == "writable") {
              return (
                <div>
                  <a href='javascript:;' onClick={() => { this.operateHandle(row.id, 'edit') }}>处理</a>&nbsp;&nbsp;
                  <a href='javascript:;' onClick={() => { this.operateHandle(row.id, 'view') }}>查看</a>
                </div>
              )
            } else {
              return (
                <div>
                  <a href='javascript:;' onClick={() => { this.operateHandle(row.id, 'view') }}>查看</a>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    const authorities = JSON.parse(sessionStorage.getItem('authorities'))
    let index = authorities.findIndex(item=>{
      return item.authority === AUTHORITY_OPRATE_CODE
    })
    this.oprateType = index!=-1 ? 'writable' : 'readonly'
  },
  methods: {
    // 点击操作列的按钮，处理或者查看单子
    operateHandle(boId, type){
      if (type == 'edit') {
        this.$router.push({
          path: '/monthly_report/safe_check/safe_check_temp/edit',
          query: { boId }
        })
      } else {
        this.$router.push({
          path: '/monthly_report/safe_check/safe_check_temp/view',
          query: { boId }
        })
      }
    },
    search() {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      );
      this.$refs.table.getTableData(this.$refs.searchForm.searchForm);
    },
    reset() {
      this.search()
    },
  }
}
</script>
