<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-21 14:15:18
 * @Description: 派发时间配置管理
-->

<template>
  <div class="time_configuration">
    <mssCard title="配置管理信息">
      <div slot="headerBtn">
        <el-button type="primary" @click="save">保存</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :serial="false"
          :stationary="tableData"
          :columns="tableHeader"
          :pagination="false"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { findPageService,timeConfigSaveService } from '@/api/monthly_report/config_manage'
export default {
  name: 'time_configuration',
  data(){
    return {
      tableData: [
        {
          workName: "月度安全工作履职任务",
          triggerMode: "自动",
          sendTime: '',
          sendTimeType: '',
          endTime: '',
          endTimeType: ''
        },
      ],
      sendNumArr:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
      endNumArr:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],
      tableHeader:  [
        {prop:"workName",label:"流程名称",minWidth:140},
        {prop:"triggerMode",label:"触发方式",minWidth:80},
        {prop:"sendTime",label:"自动派发时间配置",minWidth:220, formatter: row=>{
          return (
            <span>
              每月
              <el-select v-model={row.sendTime} onChange={()=>{this.timeChange(row, 'sendTime')}}>
              {
                this.sendNumArr.length && this.sendNumArr.map(item=>{
                  return (
                    <el-option label={item} value={item}></el-option>
                  )
                })
              }
            </el-select>
            号
            </span>
          )
        }},
        {prop:"endTime",label:"反馈截至时间配置",minWidth:240, formatter: row=>{
          return (
            <span>
              每月
              <el-select v-model={row.endTime} onChange={()=>{this.timeChange(row, 'sendTime')}}>
              {
                this.endNumArr.length && this.endNumArr.map(item=>{
                  return (
                    <el-option label={item} value={item}></el-option>
                  )
                })
              }
            </el-select>
            号
            </span>
          )
        }}
      ],
      continueFlag: false
    }
  },
  created(){
    this.getTableList()
  },
  methods: {
    timeChange(row, picker){
      const index = this.tableData.findIndex(item => item.id == row.id)
      if(picker === 'sendTime'){
        if(row.endTime != ''){
          if(row.sendTime >= row.endTime){
            this.$message.warning("自动派发时间不能大于反馈截止时间！")
            this.tableData[index].sendTime  = ''
            return false
          }
        }
      }else{
        if(row.sendTime != ''){
          if(row.endTime <= row.sendTime){
            this.$message.warning("反馈截止时间不能小于自动派发时间！")
            this.tableData[index].endTime  = ''
            return false
          }
        }
      }
      this.continueFlag = true
    },
    getTableList(){
      findPageService({
        "limit": 0,
        "page": 0
      }).then(res => {
        if(res && res.code === '0000'){
          this.tableData = res.data.data || []
        }
      })
    },  
    save(){
      if(this.continueFlag){
        timeConfigSaveService(...this.tableData).then(res=>{
          if(res && res.code === "0000"){
            this.$message({
              type: 'success',
              message: '保存成功'
            })
            this.getTableList()
          }
        })
      }else{
        this.$message.warning("请选择符合要求的时间！")
      }
      
    }
      
  }
}
</script>