<template>
  <div class="ProvincialSummaryEdit">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm"
          :disable-form="disableBasicForm"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment v-if="boId" provincialFlag :dealPage="false" :boId="boId" :businessType="workflowCode"></mssAttachment>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
  </div>
</template>

<script>
import { provincialInfoService } from '@/api/monthly_report/provincial_summary_api'
export default {
  name: 'ProvincialSummaryEdit',
  data() {
    return {
      boId: '',
      basicConfig: [
        {
          label: '任务名称：',
          type: 'input',
          prop: 'name',
          span:24
        },
        {
          label: '任务派发时间：',
          type: 'input',
          prop: 'taskSendTime'
        },
        {
          label: '任务截至时间：',
          type: 'input',
          prop: 'taskCutoffTime'
        }
      ],
      labelPosition: 'left',
      disableBasicForm: true,
      basicForm:{},
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '附件<br>列表',
          id: 'sectionAttachment'
        }
      ],
      workflowCode:""
    }
  },
  created() {
    this.boId = this.$route.query.boId
    let req = {
      boId:this.boId
    }
    provincialInfoService(req).then(res=>{
      if(res && res.code =='0000'){
        this.basicForm = res.data
      }
    })
  },
  methods: {
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/monthly_report/work_reporting/provincial_summary_list'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>