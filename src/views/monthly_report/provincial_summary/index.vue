<template>
  <div class="todoList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :api="api"
          :columns="columns"
          border
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { provincialQueryService } from '@/api/monthly_report/provincial_summary_api'
export default {
  name: 'ProvincialSummaryList',
  data() {
    return {
      searchConfig: [
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '创建时间',
          type: 'date1',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          fieldName: 'createDate'
        }
      ],
      staticSearchParam: {},
      columns: [
        {
          label: '任务名称',
          prop: 'name',
          tooltip: true
        },
        {
          label: '上一环节处理人',
          prop: 'beforeUser',
          tooltip: true,
          width:120,
        },
        {
          label: '任务派发时间',
          prop: 'taskSendTime',
          tooltip: true,
          width:220,
        },
        {
          label: '创建时间',
          prop: 'createTime',
          tooltip: true,
          width:220,
        },
        {
          label: '操作',
          prop: '_operationCol',
          width:80,
          formatter: (row) => {
            return (
              <el-button
                type="text"
                onClick={() => {
                  this.action(row)
                }}
              >
                查看
              </el-button>
            )
          }
        }
      ],
      api: provincialQueryService
    }
  },
  methods: {
    // 查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    // 重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    action(row) {
      this.$router.push({
        path: '/monthly_report/work_reporting/provincial_summary_edit',
        query: {
          boId: row.id
        }
      })
    }
  }
}
</script>

<style lang="sass" scoped>
</style>