<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-27 14:22:49
 * @Description: 安全生产费支付情况-任务维度
-->

<template>
	<div class="fee_payment_status">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="SearchForm"
			@search="search"
			@reset="reset"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
				 	ref="table"
					border
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				/>
			</div>
		</mssCard>
	</div>
</template>

<script>
import { commonDown } from '@/utils/btn.js'
import { findTaskListService, exportTaskListService } from '@/api/monthly_report/safe_prodution_manege';
export default {
	name: 'fee_payment_status_task',
	data() {
		return {
			searchConfig: [
			{
					type: 'input',
					fieldName: 'projectName',
					label: '项目名称'
				},
				{
					type: 'input',
					fieldName: 'projectCode',
					label: '项目编码',
				},
				{
					type: 'input',
					fieldName: 'taskName',
					label: '任务名称',
				},
				{
					type: 'input',
					fieldName: 'taskCode',
					label: '任务编码',
				},
				{
					type: 'date1',
					fieldName: 'actualStartDate',
					label: '开工时间'
				},
        {
					type: 'date1',
					fieldName: 'payDate',
					label: '支付时间'
				}
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
      tableApi: findTaskListService,
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{prop: "projectName",label: "项目名称",minWidth: 180, tooltip: true},
					{prop:"projectCode",label: "项目编码", minWidth: 140, tooltip: true},
					{prop: "fascicleCode",label: "项目批次", minWidth: 100, tooltip: true },
					{prop: "taskName",label: "任务名称", minWidth: 180, tooltip: true },
					{prop: "taskCode",label: "任务编码", minWidth: 140, tooltip: true},
					{prop: "city",label: "地市", minWidth: 80, tooltip: true},
					{prop: "orgname",label: "建设单位", minWidth: 120, tooltip: true},
					{prop: "planManagerName",label: "项目经理", minWidth: 120, tooltip: true},
          {prop: "constructManagerName",label: "工程管理项目经理", minWidth: 140, tooltip: true},
					{prop: "projectManagerName",label: "工程实施项目经理", minWidth: 120, tooltip: true},
					{prop: "actualStartDate",label: "项目开工时间", minWidth: 160, tooltip: true},
					{prop: "secure",label: "应支付金额（元）",  minWidth: 140, tooltip: true},
					{prop: "payamount",label: "已支付金额（元）",  minWidth: 140, tooltip: true},
					{prop: "payDate",label: "支付时间", minWidth: 160, tooltip: true},
					{prop: "isPay",label: "是否足额支付",  minWidth: 140, tooltip: true,
					headerTitle:"是否足额支付的定义：已完成支付金额≥项目安全生产费总金额的50%"},
					{prop: "isPayTimely",label: "是否及时支付",  minWidth: 140, tooltip: true},
					{prop: "payLeaveDate",label: "支付剩余时间",  minWidth: 140, tooltip: true},
				]
			}
		}
	},
	methods: {
		search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
		exportMethod() {
			commonDown({ ...this.staticSearchParam }, exportTaskListService);
		}
	}
}
</script>
