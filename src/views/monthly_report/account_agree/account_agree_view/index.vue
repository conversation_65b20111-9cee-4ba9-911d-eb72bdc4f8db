<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-12-19 10:24:55
 * @Description: 新增空间资源入网功能
-->

<template>
	<div class="account_agree_view">
		<mssSearchForm
			:searchConfig="searchConfig"
			ref="searchForm"
			@search="search"
			@reset="reset"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable
					border
					ref="table"
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				>
				</mssTable>
			</div>
		</mssCard>
	</div>
</template>

<script>
import {
	queryIrmsResourcePageService,
  exportListService
} from '@/api/monthly_report/account_agree.js'
import { commonDown } from "@/utils/btn";
export default {
	name: 'account_agree_view',
	data() {
		return {
			searchConfig: [
        {
					type: 'input',
					fieldName: 'projectName',
					label: '项目名称',
          span: 8
				},
				{
					type: 'input',
					fieldName: 'projectCode',
					label: '项目编码',
          span: 8
				},
        {
					type: 'input',
					fieldName: 'taskName',
					label: '任务名称',
          span: 8
				},
				{
					type: 'input',
					fieldName: 'taskCode',
					label: '任务编码',
          span: 8
				},
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
			tableApi: queryIrmsResourcePageService,
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{
						prop: "projectName",
						label: "项目名称",
						minWidth: 140,
            tooltip: true
					},{
            prop: "projectCode",
            label: "项目编码",
            minWidth: 140,
            tooltip: true
          },{
						prop: "taskName",
						label: "任务名称",
						minWidth: 120,
            tooltip: true
					},{
						prop: "taskCode",
						label: "任务编码",
						minWidth: 120,
            tooltip: true
					},{
            prop: "buildType",
            label: "建设类型",
            minWidth: 160,
            tooltip: true
          },{
            prop: "isSupply",
            label: "是否新增空间资源数据补充",
            minWidth: 180,
            tooltip: true
          },{
            prop: "synDate",
            label: "资源生成时间",
            minWidth: 160,
            tooltip: true
          },{
            prop: "businessName",
            label: "空间资源名称",
            minWidth: 160,
            tooltip: true
          },{
            prop: "uniquecode",
            label: "空间资源编码",
            minWidth: 160,
            tooltip: true
          },{
            prop: "siteName",
            label: "资产地址名称",
            minWidth: 160,
            tooltip: true
          },{
						prop: "siteCode",
						label: "资产地址编码",
            minWidth: 160,
            tooltip: true
          }
        ]
      }
		}
	},
	methods: {
		search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData({...this.$refs.searchForm.searchForm});
    },
    reset(form) {
      this.search(form)
    },
		exportMethod() {
			commonDown(this.staticSearchParam, exportListService);
		}
	}
}
</script>

