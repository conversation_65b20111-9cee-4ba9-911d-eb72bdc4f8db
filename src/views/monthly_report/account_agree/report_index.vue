/**
* @author: ty
* @date: 2023-07-21
* @description: 账实相符进度查询列表页
*/
<template>
  <div>
    <mssSearchForm
      ref="searchForm"
      :search-config="searchConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>

    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="exportHandle" v-loading="exportBtnLoading">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          rowKey="$index"
          :auto-call="true"
          :api="api"
          :columns="columns"
          :static-search-param="staticSearchParam"
          border
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  exportExcelService, exportReportService,
  reportFindPageService
} from '@/api/monthly_report/account_agree'
import {commonDown, download} from '@/utils/btn'

export default {
  name: 'AccountAgreeControllerFindPage',
  data() {
    return {
      exportBtnLoading: false,
      searchConfig: [
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '建设单位',
          type: 'input',
          fieldName: 'constructOrgName'
        },
        {
          label: '开工日期',
          type: 'date1',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd',
          fieldName: 'actualStartDate'
        }
      ],
      api: reportFindPageService,
      staticSearchParam: {},
      columns: [
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true
        },
        {
          label: '建设单位名称',
          prop: 'constructOrgName',
          tooltip: true
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          tooltip: true
        },
        {
          label: '任务名称',
          prop: 'taskName',
          tooltip: true
        },
        {
          label: '开工日期',
          prop: 'actualStartDate',
          tooltip: true
        },
        {
          label: '状态',
          prop: 'status'
        },
        {
          label: '操作',
          prop: '_operationCol',
          width: 80,
          formatter: row => {
            return (
              <a href='javascript:;' onClick={() => {
                this.operateHandle(row)
              }}
              >查看</a>
            )
          }
        }
      ]
    }
  },
  async created() {
  },
  methods: {
    search(searchData = this.$refs.searchForm.searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = JSON.parse(
        JSON.stringify(searchData)
      )
      this.$refs.table.getTableData(searchData)
    },
    reset(searchData) {
      this.search()
    },
    exportHandle() {
      let loadParams = JSON.parse(JSON.stringify(this.$refs.searchForm.searchForm))
      // loadParams.limit = 99999
      this.exportBtnLoading = true
      exportReportService(loadParams)
      .then(res =>{
        if(res && res.data){
          try{
            download('账实相符报表.xlsx', res.data)
          }catch(e){
          }
        }
      })
      .finally(_ => {
        this.exportBtnLoading = false
      })
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle({ isAllowOperate, id, buildTypeName }) {
      let toPath = ''
      if(buildTypeName === '新建'){
        toPath = '/account_agree_add/view'
      }
      else if(buildTypeName === '新建'){
        toPath = '/account_agree_stock/view'
      }
      else{
        toPath = '/account_agree/view'
      }
      this.$router.push({
        path: toPath,
        query: {
          id,
          type: 'report'
        }
      })
    }
  }
}
</script>

