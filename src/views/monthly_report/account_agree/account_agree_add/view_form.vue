/**
* @author: ty
* @date: 2023-07-21
* @description:
*/
<template>
  <div v-loading="pageLoading" class="AccountAgreeEditForm page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          :config="basicConfig"
          :label-position="labelPosition"
          :form="basicForm"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionTaskDetails" class="page-anchor-point"></div>
    <!-- 务必加上v-if="buildTypeName"，因为table不支持动态修改hidden值，而这个值依赖于buildTypeName   -->
    <mssCard title="任务详情" v-if="buildTypeName">
      <div slot="headerBtn">
        <el-button
          :loading="taskDetailExportLoading"
          type="primary"
          @click="taskDetailExportHandle">导出</el-button>
      </div>
      <div slot="content">
        <taskDetails ref="taskDetailsTable"
                     :id="id"
                     :buildTypeName="buildTypeName"
                     tableType="1"></taskDetails>
      </div>
    </mssCard>

    <!--  综资反馈信息 -->
    <div id="sectionZongZi" class="page-anchor-point"></div>
    <!-- 务必加上v-if="buildTypeName"，因为table不支持动态修改hidden值，而这个值依赖于buildTypeName   -->
    <mssCard title="综资反馈信息" v-if="buildTypeName">
      <div slot="headerBtn">
        <el-button type="primary" @click="zongZiExportHandle">导出</el-button>
      </div>
      <div slot="content">
        <taskDetails ref="zongZiTable"
                     :id="id"
                     :buildTypeName="buildTypeName"
                     tableType="3"></taskDetails>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="id"
      :deal-page="dealPage"
      :bo-id="id"
      :business-type="workflowCode"
      :node-name="currentNodeName"
    ></mssAttachment>

    <div id="sectionWorkFlowHistory" class="page-anchor-point"></div>
    <mssCard v-if="id" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="id" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <!--  流程组件  -->
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="id"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="saveMainForm"
    ></mssWorkFlowHandel>
  </div>
</template>

<script>
import taskDetails from '../components/task_details'
import {
  accountagreeGetByIdService,
  exportItemExcelService,
  exportReportService,
  findTaskPageService
} from "@/api/monthly_report/account_agree";
import {download} from "@/utils/btn";
export default {
  name: 'AccountAgreeAddViewForm',
  components: {taskDetails},
  data(){
    return {
      pageLoading: false,
      id: '',
      routerType: '',
      dealPage: false,
      workflowCode: 'AccountAgree',
      currentNodeName: '', // 当前所属环节name
      currentNodeCode: '', // 当前所属环节code
      completeTaskUrl: '',
      saveMainForm: false,// todo 感觉没有生效，是因为我的页面有多个表单的问题嘛？
      returnAddress: '',
      buildTypeName: '',
      taskDetailExportLoading: false,
      labelPosition: 'left', // 查看页面放在左边
      basicForm: {
        projectName: ''
      },
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>详情',
          id: 'sectionTaskDetails',
        },
        {
          text: '综资<br>反馈',
          id: 'sectionZongZi',
        },
        {
          text: '附件',
          id: 'sectionAttachment',
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlowHistory',
        },
      ],
      basicConfig: [
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          span: 12
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          span: 12
        },
        {
          label: '建设单位：',
          type: 'input',
          prop: 'constructOrgName',
          span: 12
        },
        {
          label: '开工时间：',
          type: 'input',
          prop: 'actualStartDate',
          span: 12
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || urlQuery.boId || ''// 如果不存在id,那么有可能是从首页待办过来的，boid就是id
    this.routerType = urlQuery.type || ''
    this.pageLoading = true
    await this.getPageData()
    this.pageLoading = false
  },
  methods: {
    getPageData(){
      return new Promise(resolve => {
        accountagreeGetByIdService(this.id)
          .then(res => {
            if(res && res.code === '0000'){
              this.id = res.data.id
              this.buildTypeName = res.data.buildTypeName
              this.basicForm = Object.assign({}, this.basicForm, res.data)
            }
          }).finally(_ => {
          resolve('')
        })
      })

    },
    // 返回上一页
    goBack() {
      let toPath = '/account_agree/find_page'// 默认返回的列表页
      if(this.routerType === 'report'){
        toPath = '/account_agree/report_find_page'
      }else{
        if(this.buildTypeName === '新建'){
          toPath = '/account_agree_add/find_page'
        }else if(this.buildTypeName === '改扩建'){
          toPath = '/account_agree_stock/find_page'
        }
      }
      this.$closeTagView({
        close: this.$route.fullPath,
        to: toPath
      })
    },
    save(){},
    // 任务详情导出
    taskDetailExportHandle(){
      const tablePage = this.$refs.taskDetailsTable.$refs.table.page
      const loadParams = {
        isAllColumn: false,
        boId: this.id,
        limit: 9999,//tablePage.size,
        // page: tablePage.current
      }

      this.taskDetailExportLoading = true
      exportItemExcelService(loadParams)
        .then(res =>{
          if(res && res.data){
            try{
              download('任务详情列表.xlsx', res.data)
            }catch(e){
            }
          }
        })
      .finally(_=>{
        this.taskDetailExportLoading = false
      })
    },
    // 综资的导出
    zongZiExportHandle(){
      const tablePage = this.$refs.zongZiTable.$refs.table.page
      const loadParams = {
        isAllColumn: true,
        boId: this.id,
        limit: 99999, // tablePage.size,
        // page: tablePage.current
      }
      exportItemExcelService(loadParams)
        .then(res =>{
          if(res && res.data){
            try{
              download('综资反馈信息.xlsx', res.data)
            }catch(e){
            }
          }
        })
    },
  }
}
</script>
