/**
* @author: ty
* @date: 2023-07-21
* @description: 任务详情的列表 （buildTypeName:"新建"）
*/
<template>
  <mssTable
    ref="table"
    :autoCall="true"
    :api="api"
    :columns="columns"
    :staticSearchParam="staticSearchParam"
    border
  >
  </mssTable>
</template>

<script>
import {findTaskPageService} from '@/api/monthly_report/account_agree';
export default {
  name: "task_details",
  props: {
    id: {
      type: String,
      default: ''
    },
    buildTypeName: {
      type: String,
      default: ''
    },
    // 表格的类型:1,2,3。
    // 1-只有一个是否新增空间资源数据补充的select列。
    // 2-只有一个是否新增空间资源数据补充的普通文本列
    // 3-新增空间资源数据补充的普通文本列 + 多2列
    tableType: {
      type: String,
      default: ''
    },
  },
  data(){
    return {
      api: findTaskPageService,
      staticSearchParam: {
        boId: this.id
      },
      isSupplyList: [
        {
          label: '是',
          value: '是'
        }
      ],
      columns: [
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 180,
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          minWidth: 140,
          tooltip: true
        },
        {
          label: '任务名称',
          prop: 'taskName',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          minWidth: 100,
          tooltip: true
        },
        {
          label: '建设类型',
          prop: 'buildType',
          width: 80,
          tooltip: true
        },{
          label: '是否新增空间资源数据补充',
          prop: 'isSupply',
          tooltip: true,
          width: 170,
          hidden: this.buildTypeName !== '新建',
          formatter: (row, column, cellValue, index) => {
            return row.isSupply
            //
            // if(this.tableType == 1){
            //   if(this.buildTypeName == '新建'){
            //     row.isSupply = '是'
            //   }
            //   return (<el-select v-model={row.isSupply} onChange={(val) => { this.changeSupProviderHandle(val, row, column, cellValue, index) }}>
            //     {this.isSupplyList.map((item, index) => (
            //       <el-option key={index} label={item.label} value={item.value}></el-option>
            //     ))}
            //   </el-select>)
            // }else{
            //   return row.isSupply
            // }
          }
        },{
            prop: "synDate",
            label: "资源生成时间",
            minWidth: 160,
            tooltip: true
        },{
            prop: "businessName",
            label: "空间资源名称",
            minWidth: 160,
            tooltip: true
        },{
            prop: "uniquecode",
            label: "空间资源编码",
            minWidth: 160,
            tooltip: true
          },
        {
          label: '资产所属地点编码',
          prop: 'assetsCode',
          width: 120,
          tooltip: true,
          hidden: !(this.buildTypeName == '新建' && this.tableType == 3)
        },
        {
          label: '资产所属地点名称',
          prop: 'assetsName',
          width: 120,
          tooltip: true,
          hidden: !(this.buildTypeName == '新建' && this.tableType == 3)
        },
        {
          label: '是否与综资一致',
          prop: 'existCont',
          width: 120,
          tooltip: true,
          hidden: !(this.buildTypeName == '改扩建' && this.tableType == 3)
        },
      ]
    }
  }
}
</script>

<style scoped>

</style>
