/**
* @author: ty
* @date: 2023-07-21
* @description: buildTypeName:"新建"
*/
<template>
  <div v-loading="pageLoading" class="AccountAgreeEditForm page-anchor-parentpage">
    <div class="operate-btn">
      <el-button v-if="id" type="primary" @click="submit">提交审核</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          label-width="200px"
          :config="basicConfig"
          :label-position="labelPosition"
          :rules="basicRules"
          :form="basicForm"
        ></mssForm>
      </div>
    </mssCard>

    <div id="sectionTaskDetails" class="page-anchor-point"></div>
    <mssCard title="任务详情">
      <div slot="headerBtn">
<!--        currentNodeCode === 'draft' &&-->
        <el-upload
          v-if="buildTypeName === '新建'"
          ref="newFile"
          class="upload-btn"
          action="string"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="taskDetailImportHandle"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button
          type="primary"
          @click="taskDetailExportHandle">导出</el-button>
      </div>
      <div slot="content">
        <taskDetails v-if="buildTypeName"
                     ref="taskDetailsTable"
                     :id="id"
                     :buildTypeName="buildTypeName"
                     tableType="1"></taskDetails>
      </div>
    </mssCard>

    <!--  综资反馈信息 -->
    <div id="sectionZongZi" class="page-anchor-point"></div>
    <mssCard title="综资反馈信息" v-if="zongZiVisible">
      <div slot="headerBtn">
        <el-button type="primary" @click="zongZiExportHandle">导出</el-button>
      </div>
      <div slot="content">
        <taskDetails v-if="buildTypeName"
                     ref="zongZiTable"
                     :id="id"
                     :buildTypeName="buildTypeName"
                     tableType="3"></taskDetails>
      </div>
    </mssCard>

    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="id"
      :deal-page="dealPage"
      :bo-id="id"
      :business-type="workflowCode"
      :node-name="currentNodeName"
    ></mssAttachment>

    <div id="sectionWorkFlowHistory" class="page-anchor-point"></div>
    <mssCard v-if="id" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="id" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>

    <!--  流程组件  -->
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="id"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="saveMainForm"
      @getNodeData="getNodeData"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
    ></mssWorkFlowHandel>
  </div>
</template>

<script>
import taskDetails from './components/task_details'
import {
  accountagreeGetByIdService,
  exportItemExcelService,
  exportReportService,
  importExcelService,
  checknewprojectService
} from "@/api/monthly_report/account_agree";
import {download} from "@/utils/btn";
export default {
  name: 'AccountAgreeEditForm',
  components: {taskDetails},
  computed: {
    taskDetailsVisible(){
      return this.id && this.buildTypeName
      // return this.currentNodeCode === 'draft'
    },
    zongZiVisible(){
      // return this.currentNodeCode === 'draft'
      return this.currentNodeCode === 'managerDeal'
    },
    pageAnchorConfig(){
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '任务<br>详情',
          id: 'sectionTaskDetails',
          show: this.taskDetailsVisible
        },
        {
          text: '综资<br>反馈',
          id: 'sectionZongZi',
          show: this.zongZiVisible
        },
        {
          text: '附件',
          id: 'sectionAttachment',
          show: !!this.id
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlowHistory',
          show: !!this.id
        },
      ]
    }
  },
  data(){
    return {
      pageLoading: false,
      id: '',
      buildTypeName: '',
      dealPage: true,
      workflowCode: 'AccountAgree',
      currentNodeName: '', // 当前所属环节name
      currentNodeCode: '', // 当前所属环节code
      completeTaskUrl: '/monthly-report-jx/accountagree',// todo 地址是否正确需要验证
      saveMainForm: false,// todo 感觉没有生效，是因为我的页面有多个表单的问题嘛？
      returnAddress: '/account_agree/find_page',
      labelPosition: 'left',// 查看页面放在左边
      basicRules: {},
      basicForm: {
        projectName: ''
      },
      basicConfig: [
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          span: 12
        },
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          span: 12
        },
        {
          label: '建设单位：',
          type: 'input',
          prop: 'constructOrgName',
          span: 12
        },
        {
          label: '开工时间：',
          type: 'input',
          prop: 'actualStartDate',
          span: 12
        }
      ]
    }
  },
  async created() {
    // 浏览器传参
    const urlQuery = this.$route.query
    this.id = urlQuery.id || urlQuery.boId || ''// 如果不存在id,那么有可能是从首页待办过来的，boid就是id
    this.pageLoading = true
    await this.getPageData()
    this.pageLoading = false
    this.$refs.workFlow.init()
  },
  methods: {
    getPageData(){
      return new Promise(resolve => {
        accountagreeGetByIdService(this.id)
          .then(res => {
            if(res && res.code === '0000'){
              this.id = res.data.id
              this.buildTypeName = res.data.buildTypeName
              this.basicForm = Object.assign({}, this.basicForm, res.data)
              if(res.data.completeTaskUrl){
                this.completeTaskUrl = res.data.completeTaskUrl
              }
            }
          }).finally(_ => {
          resolve('')
        })
      })

    },
    // 返回上一页
    goBack() {
      const toPath = {
        '新建': '/account_agree_add/find_page',
        '改扩建': '/account_agree_stock/find_page'
      }[this.buildTypeName] || 'account_agree/find_page'
      this.$closeTagView({
        close: this.$route.fullPath,
        to: toPath
      })
    },
    save(cb){},
    // 提交
    submit() {
      this.$refs.workFlow.opendialogInitNextPath()
    },
    getNodeData(data) {
      this.currentNodeName = data.nodeName
      this.currentNodeCode = data.nodeCode
    },
    async beforeSubmit() {
      if(this.buildTypeName === '新建'){
        let res = await checknewprojectService({ boId: this.id })
        if(res.data === true || res.data === 'true'){
          return ''
        }else{
          return '校验失败，请稍后再试'
        }
      }else{
        return ''
      }
    },
    beforeNode() {
      const fileNameArr = {
      }
      // 设置默认处理人
      return fileNameArr
    },
    initFormRules(rules) {
      if (rules) {
        this.basicRules = rules
        this.$nextTick(_ => {
          this.$refs.basicForm.$refs.form.clearValidate()
        })
      }
    },
    // 只读表单
    getReadonlyList(param) {
      if (param && param.length) {
        [].concat(this.basicConfig).forEach(item => {
          if (param.includes(item.prop)) {
            item.disabled = true
          }
        })
      }
    },
    // 任务详情导出
    taskDetailExportHandle(){
      const tablePage = this.$refs.taskDetailsTable.$refs.table.page
      const loadParams = {
        isAllColumn: false,
        boId: this.id,
        limit: 9999,//tablePage.size,
        // page: tablePage.current
      }
      exportItemExcelService(loadParams)
        .then(res =>{
          if(res && res.data){
            try{
              download('账实相符任务列表.xlsx', res.data)
            }catch(e){
            }
          }
        })
    },
    // 综资的导出
    zongZiExportHandle(){
      const tablePage = this.$refs.zongZiTable.$refs.table.page
      const loadParams = {
        isAllColumn: true,
        boId: this.id,
        limit: 99999, //tablePage.size,
        // page: tablePage.current
      }
      exportItemExcelService(loadParams)
        .then(res =>{
          if(res && res.data){
            try{
              download('综资反馈信息.xlsx', res.data)
            }catch(e){
            }
          }
        })
    },
    refreshTables(){
      if(this.$refs.taskDetailsTable){
        this.$refs.taskDetailsTable.$refs.table.page.current = 1
        this.$refs.taskDetailsTable.$refs.table.getTableData()
      }
      if(this.$refs.zongZiTable){
        this.$refs.zongZiTable.$refs.table.page.current = 1
        this.$refs.zongZiTable.$refs.table.getTableData()
      }
    },
    // 任务详情导入
    taskDetailImportHandle(params){
      const param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.id)
      importExcelService(param).then((res) => {
        if (res.code === '0000') {
          this.$message.success('导入成功')
          this.refreshTables()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.upload-btn {
  display: inline-block;
  margin: 0 10px;
}
</style>
