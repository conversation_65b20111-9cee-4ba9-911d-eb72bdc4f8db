<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-21 20:42:34
 * @Description: 进度查询详情
-->
<template>
	<div class="progress_query_detail">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssSearchForm 
      :searchConfig="searchConfig" 
      ref="searchForm" 
      @search="search" 
      @reset="reset"
      @changeSelect="changeSelect"
    >
		</mssSearchForm>
		<mssCard :title="tableName">
      <div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
        <el-button type="primary" @click="urging">催办</el-button>
			</div>
			<div slot="content">
				<mssTable 
          ref="table" 
          selection
          border 
          :columns="columns" 
          :staticSearchParam="staticSearchParam" 
          :api="tableApi" 
          :selectable="selectable"
          :autoCall="false"
          :cuzPaginationOpt="cuzPaginationOpt"
        />
			</div>
		</mssCard>
    <el-dialog
      :visible.sync="showDialog"
      title="操作"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      :modal="true"
      append-to-body
    >
      <el-form>
        <el-form-item :required="true" label="催办说明：" label-width="100px">
          <el-input v-model="urgeRemark" type="textarea" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitOrder">确 定</el-button>
        <el-button size="mini" style="margin-left: 10px" @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
	</div>
</template>
<script>
import { findPageDetailService, noticeService, exportDetilService } from '@/api/monthly_report/month_report_schedule'
import {
  supervisorqueryService,
  getReportRolesService
} from '@/api/monthly_report/config_manage'
import { getUserInfoService } from '@/api/monthly_report/safe_check_progress.js'
import { queryAreaListService ,childdeptService} from "@/api/common_api"
export default{
  name:'progress_query_detail',
  data(){
    return {
      tableName: this.$route.meta.title,
      staticSearchParam:{},
      tableApi: findPageDetailService,
      columns: [
        {label:"地市",prop:"cityName",minWidth: 80, tooltip:true},
        {label:"区县",prop:"countyName",minWidth: 80, tooltip:true},
        {label:"任务状态",prop:"taskStatus",minWidth: 80, tooltip:true},
        {label:"反馈方式",prop:"opstr",minWidth: 80, tooltip:true},
        {label:"当前处理人",prop:"currUser",minWidth: 80,tooltip:true},
        {label:"角色名称",prop:"reportRoleName",minWidth: 140,tooltip:true},
        {label:"监理单位",prop:"supervisorName",minWidth: 160,tooltip:true},
        {label:"累计处理时长（小时）",prop:"dealTime",minWidth: 120},
        {label:"任务名称",prop:"taskName",minWidth: 340, tooltip: true},
        {label:"任务截止时间",prop:"endTime",minWidth: 100}
      ],
      showDialog: false,
      urgeRemark: '任务即将超时，请尽快处理！',
      cuzPaginationOpt: {current: 1,total: 0 ,size: 20},
      searchConfig: [
        {
					type: 'select',
					fieldName: 'taskStatus',
					label: '任务状态',
					options: [
            {label:'-请选择-',value: ''},
            {label:'已反馈',value: 'completed'},
            {label:'未反馈',value: 'in_process'},
          ]
				},
        {
					type: 'select',
					fieldName: 'cityId',
					label: '地市',
          options: []
				},
        {
					type: 'select',
					fieldName: 'deptId',
					label: '区县',
          options: []
				},
        {
					type: 'select',
					fieldName: 'opstr',
					label: '反馈方式',
					options: [
            {label:'正常提交',value: '正常提交'},
            {label:'强制提交',value: '强制提交'},
          ]
				},
        {
					type: 'select',
					fieldName: 'reportRoleId',
					label: '角色名称',
					options: []
				},
        {
					type: 'select',
					fieldName: 'supervisorId',
					label: '监理单位',
					options: []
				},
      ],
      userInfo: {},
    }
  },
  created(){
    supervisorqueryService({
        "limit": 0,
        "page": 0
      }).then(res=>{
      if(res && res.code === '0000' && res.data){
        const list = []
        let idAry = []
        res.data.data.forEach(item => {
          if(!idAry.includes(item.supervisorId)){
            list.push({ label: item.supervisorName, value: item.supervisorId })
            idAry.push(item.supervisorId)
          }
        })
        this.$set(this.searchConfig[5], 'options', list)
      }
    })
    getReportRolesService().then(res=>{
      if(res && res.code =='0000'){
        const list = []
        res.data.forEach(item => {
          list.push({ label: item.name, value: item.id })
        })
        this.$set(this.searchConfig[4], 'options', list)
      }
    })
    this.getUserInfo()
	},
  methods:{
    getUserInfo(){
      getUserInfoService().then(res => {
        this.userInfo = res.data
        this.getAreaList('-2', 1)
        this.$set(this.$refs.searchForm.searchForm, 'yearMonth', this.$route.query.yearMonth)
        if(this.userInfo.deptType == '1'){
          this.search()
        }else if(this.userInfo.deptType == '2'){
          this.$set(this.searchConfig[1], 'disabled', true)
          this.$set(this.$refs.searchForm.searchForm,'cityId',this.userInfo.firstDeptId)
          this.childdeptService(this.userInfo.firstDeptId, 2)
          this.search()

        }else if(this.userInfo.deptType == '3'){
          this.$set(this.searchConfig[1], 'disabled', true)
          this.$set(this.searchConfig[2], 'disabled', true)
          this.$set(this.$refs.searchForm.searchForm,'cityId',this.userInfo.firstDeptId)
          this.$set(this.$refs.searchForm.searchForm,'deptId',this.userInfo.secondDeptId)
          this.childdeptService(this.userInfo.firstDeptId, 2)
          this.search()
        }
      })
    },
    getAreaList(parentId, index) {
			queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
				if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.id })
          })
          this.$set(this.searchConfig[index], 'options', list)
				}
			})
    },
    childdeptService(deptId,index){
      childdeptService(deptId).then(res=>{
        if(res && res.code=='0000'){
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.deptName, value: item.deptId })
          })
          this.$set(this.searchConfig[index], 'options', list)
        }
      })
    },
		changeSelect(name, val){
			if(name == 'cityId'){
				this.$set(this.$refs.searchForm.searchForm,'deptId','')
				this.childdeptService(val, 2)
			}
		},
    urging(){
      const chooseData = this.$refs.table.multipleSelection
      if(chooseData.length){
        this.showDialog = true
      }else{
        this.$message({
          type: "warning",
          message: "请至少选择一条数据"
        })
      }
    },
    exportMethod(){
      exportDetilService({
        ...this.$refs.searchForm.searchForm
      }).then((res) => {
        console.log(res);
        if ((res.status = "200")) {
          // 定义下载文件名称
          let filename = "进度查询详情";
          // 文件导出
          if (!res.data) {
            this.$message({
              showClose: true,
              message: "导出失败....",
              type: "warning",
            });
            return;
          } else {
            this.$message({
              showClose: true,
              message: "正在导出中,请稍等....",
              type: "success",
            });
            const href = window.URL.createObjectURL(new Blob([res.data]))
            const link = document.createElement('a')
            link.style.display = 'none'
            link.href = href
            link.setAttribute('download', filename + '.xls')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) // 下载完成移除元素
            window.URL.revokeObjectURL(href) // 释放掉blob对象

          }
        }
      });
    },
    submitOrder(){
      const chooseData = this.$refs.table.multipleSelection
      let ids = chooseData.map(item=>{
        return item.currUserId
      }).join()
      noticeService({content: this.urgeRemark, ids: ids}).then(res => {
        if(res && res.code === '0000'){
          this.$message.success("督办成功！")
          this.closeDialog()
          this.$refs.table.multipleSelection = []
          this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
          this.$refs.table.getTableData()
        }
      })
    },
    closeDialog(){
      this.showDialog = false
    },
    selectable(row, index){
      return row.taskStatus == '未反馈'
    },
    search(){
      this.$refs.table.page.current = 1
			this.staticSearchParam=JSON.parse(
        JSON.stringify(this.$refs.searchForm.searchForm)
      )
			this.$refs.table.getTableData(this.$refs.searchForm.searchForm)
    },
    reset(){
      let that = this
      if (that.userInfo.deptType == '2') {
        that.$set(that.$refs.searchForm.searchForm, 'cityId', that.userInfo.firstDeptId)
        that.$set(that.$refs.searchForm.searchForm, 'deptId', '')
      } else if (that.userInfo.deptType == '3') {
        that.$set(that.$refs.searchForm.searchForm, 'cityId', that.userInfo.firstDeptId)
        that.$set(that.$refs.searchForm.searchForm, 'deptId', that.userInfo.secondDeptId)
      }
      this.$set(this.$refs.searchForm.searchForm, 'yearMonth', this.$route.query.yearMonth)
      this.search()
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/monthly_report/progress_query'
      })
    }
  }
}
</script>