<!--
 * @File name:
 * @Author: LT
 * @Date: 2023-07-21 19:50:36
 * @Description: 进度查询查询页面
-->
<template>
	<div class="progress_query">
		<mssSearchForm
      :searchConfig="searchConfig"
      ref="searchForm"
      @search="search"
      @reset="reset"
	  >
		</mssSearchForm>
		<mssCard :title="tableName">
			<div slot="content">
				<mssTable ref="table" border :columns="columns" :staticSearchParam="staticSearchParam" :api="tableApi" />
			</div>
		</mssCard>
	</div>
</template>
<script>
import { findPageService } from '@/api/monthly_report/month_report_schedule'
export default{
  name:'progressQuery',
  data(){
    return {
      tableName: this.$route.meta.title,
      staticSearchParam:{},
      tableApi: findPageService,
      columns: [
        {label:"月报年月",prop:"yearMonth"},
        {label:"已反馈数",prop:"feedbackTotal"},
        {label:"未反馈数",prop:"noFeedbackTotal"},
        {
          label: '操作',
          prop: '_caozuo',
          width: 100,
          formatter: row => {
            return (<span>
              <el-button type='text' onClick={() => { this.toDetail(row) }}>查看</el-button>
            </span>)
          }
        }
      ],
      searchConfig: [
        {
					type: 'date1',
					fieldName: 'yearMonth',
					label: '任务发起时间',
          valueFormat: 'yyyy-MM',
          format: 'yyyy-MM',
          dateType: 'month'
				}
      ],
    }
  },
  methods:{
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.table.page.current = 1
      this.$refs.table.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
		exportMethod(){
			commonDown(this.staticSearchParam, exportYearScoreExcelService);
		},
    toDetail(row){
      this.$router.push({
        path:"/monthly_report/progress_query/detail",
        query: {
          yearMonth: row.yearMonth
        }
      })
    }
  }
}
</script>
