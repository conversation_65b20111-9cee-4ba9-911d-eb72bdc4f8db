<!-- 月工作上报任务填报人员关系配置 -->
<template>
  <div>
    <mssCard title="人员配置信息">
      <div slot="headerBtn">
        <el-button v-if="powerData.personnel_allocation_save" type="primary" @click="saveTableInfo">保存</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :selection="false"
          :api="api"
          :columns="columns"
          :stationary="stationary"
          border
          :pagination="false"
        ></mssTable>
      </div>
    </mssCard>

    <mssChooseUser ref="chooseUser" :mult-select="multSelect" @showCheckList="showCheckList"></mssChooseUser>
  </div>
</template>

<script>
import { queryListService, filingofficersaveUpdateService } from '@/api/monthly_report/personnel_allocation'
export default {
  name: 'PersonnelAllocation',
  data() {
    return {
      powerData:[],
      api: queryListService,
      type: '',
      multSelect:false,
      stationary: [],
      roleRow: {} // 某行数据
    }
  },
  computed:{
    columns(){
      if(this.powerData.personnel_allocation_save){
        return [
          {
            label: '角色名称',
            prop: 'roleName'
          },
          {
            label: '处理人账号',
            prop: 'userNames',
            formatter: (row) => {
              return <el-input v-model={row.userNames} readonly onFocus={() => {
              this.openChooseUserDailog(row)
              }}></el-input>
            }
          },
          {
            label: '操作',
            prop: '_operationCol',
            formatter: (row) => {
                return (
                <span>
                  <el-button
                    type='text'
                    onClick={() => {
                      this.action(row)
                    }}
                  >
                    保存
                  </el-button>
                </span>
              )
            }
          }
        ]
      }else{
        return [
          {
            label: '角色名称',
            prop: 'roleName'
          },
          {
            label: '处理人账号',
            prop: 'userNames'
          }
        ]
      }
    }
  },
  created() {
    this.getPower()
  },
  methods: {
    getPower(){
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item)=>{
        this.powerData[item.authority]=true
      })
    },
    // 修改配置人员
    async action(row) {
      if (row.userIds) {
        // 保存
        const obj = {
          roleId: row.roleId,
          userId: row.userIds
        }
        const res = await this.saveServiceFn(obj)
        if (res) {
          this.$message.success("保存成功！")
          this.$refs.table.getTableData()
        }
      } else {
        this.$message.warning('请选择人员')
      }
    },
    openChooseUserDailog(row) {
      this.roleRow = row
      const item = {
        excuterNames: row.userNames,
        excuterIds: row.userIds
      }
      if(row.roleName=='市公司工程建设安全管理员'){
        this.multSelect = false
      }else{
        this.multSelect = true
      }
      const deptParams = { deptIds: sessionStorage.getItem('firstDeptId') }
      const personParams = { firstDeptId: sessionStorage.getItem('firstDeptId') }
      this.$refs.chooseUser.init(item, deptParams, personParams)
    },
    showCheckList({ checkList }) {
      const list = checkList
      this.specailty = list
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.realName)
        ids.push(item.userId)
      })
      const data = this.$refs.table.tableData
      this.stationary = data.map((item, index) => {
        if (item.roleId === this.roleRow.roleId) {
          return { ...item, userNames: names.join(','), userIds: ids.join(',') }
        } else {
          return item
        }
      })
    },
    // 需求文档上是批量保存
    async saveTableInfo() {
      const data = this.$refs.table.tableData
      // 请求队列
      const list = []
      let flag = true
      try {
        data.forEach(item => {
          if (item.userIds) {
            const obj = {
              roleId: item.roleId,
	            userId: item.userIds
            }
            list.push(this.saveServiceFn(obj))
          } else {
            flag = false
            throw new Error('请选择人员')
          }
        })
      } catch (error) {
        this.$message.warning(error.message)
      }
      if (flag) {
        // 所有请求都已完成，直接返回或处理 res
        await Promise.all(list)
        this.$message.success("保存成功！")
        this.$refs.table.getTableData()
      }
    },
    async saveServiceFn(obj) {
      let flag = true
      await filingofficersaveUpdateService(obj).then(res => {
        if (res.code === '0000') {
          flag = true
        } else {
          flag = false
        }
      })
      return flag
    }
  }
}
</script>
