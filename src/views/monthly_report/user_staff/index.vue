<!--
  @author: 莫海东
  @description:月报管理--安全检查--人员配置管理-列表查询
-->
<template>
  <div>
    <mssCard title="配置信息">
      <div slot="headerBtn">
        <el-button @click="add" type="primary">新增</el-button>
        <el-button @click="save" type="primary">保存</el-button>
        <el-button @click="deleteBatch" type="primary">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          selection
          :columns="columns"
          :stationary="staffStationary"
          :pagination="false"
          border
        >
        </mssTable>
      </div>
    </mssCard>

    <mssChooseUser ref="chooseUser" :mult-select="selectUserFlag" @showCheckList="showUserCheckList"></mssChooseUser>
  </div>
</template>

<script>
// api
import { findPageService, saveService, updateCheckService, updateService, updateBatchCheckService, updateBatchService, deleteOneService, deleteBatchService } from '@/api/monthly_report/user_staff.js'
import { commonOneDel, commonMultDel } from '@/utils/btn'
import { getDeptService } from '@/api/choose_dept'
export default {
  name: 'user_staff',
  data() {
    return {
      titleName: '选择地市/区县',
      checkDept: [],
      selectUserFlag: false,
      staffStationary: [
        { id: 0, parentTempName: '', groupLeaderId: '', groupLeaderName: '', teamMembersIds: '', teamMembersNames: '', cityIds: '', cityNames: '' }
      ],
      columns: [
        {
          label: '模板名称',
          prop: 'parentTempName',
          minWidth: 200
        },
        {
          label: '组长',
          prop: 'name',
          width: 120,
          formatter: (row, column, cellValue, index) => {
            return (
              <el-input v-model={row.groupLeaderName} multiple readonly onFocus={() => {
                this.openChooseLeaderUserDailog(row, 'lead')
              }}></el-input>
            )
          }
        },
        {
          label: '检查组成员配置',
          prop: 'createDate',
          minWidth: 200,
          formatter: (row, column, cellValue, index) => {
            return (
              <el-input v-model={row.teamMembersNames} multiple readonly onFocus={() => {
                this.openChooseTeamUserDailog(row, 'team')
              }}></el-input>
            )
          }
        },
        {
          label: '检查地市/区县',
          prop: 'updateUserName',
          minWidth: 200,
          formatter: (row, column, cellValue, index) => {
            return (
              <el-select v-model={row.cityIdArr} multiple>
                {this.checkDept.length &&
                  this.checkDept.map((item) => {
                    return (
                      <el-option label={item.text} value={item.id} />
                    );
                  })}
              </el-select>
            )
          }
        },
        {
          label: '操作',
          prop: '_operationCol',
          width: 100,
          formatter: (row, column, cellValue, index) => {
            return (
              <div>
                <a href='javascript:;' onClick={() => { this.operateHandle(row) }}>保存</a>&nbsp;&nbsp;
                <a href='javascript:;' onClick={() => { this.deleteOne(row) }}>删除</a>
              </div>
            )
          }
        }
      ]
    }
  },
  async created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    getList() {
      let that = this
      findPageService(null).then(res => {
        if (res.code === '0000' && res.data) {
          that.staffStationary=[]
          res.data.data.forEach((item,index) => {
            let obj = JSON.parse(JSON.stringify(item))
            obj.cityIdArr = obj.cityIds?obj.cityIds.split(','):[];
            obj.fontId = index
            that.staffStationary.push(obj)
          })
        }
      })
    },
    getDeptList() {
      let that = this
      that.checkDept = []
      if ('1' == sessionStorage.getItem('userId')) {
        const deptParams = {isReturnUser: '0', parentId: '', rootId: '-2', orgChildId: '-2', conditionType: 'user_staff_con', conditionParams: '{deptId: "-2", deptType: "2" }' }
        getDeptService(deptParams).then(res => {
          if (res.code === '0000' && res.data) {
            res.data.forEach((item)=>{
              that.checkDept.push(item)
              deptParams.conditionParams = '{deptId: "' + item.id + '", deptType: "3" }'
              deptParams.parentId = item.id
              getDeptService(deptParams).then(res => {
                if (res.code === '0000' && res.data) {
                  res.data.forEach((item)=>{
                    that.checkDept.push(item)
                  })
                }
              })
            })
          }
        })
      } else {
        const deptParams = {isReturnUser: '0', parentId: '', rootId: '-2', orgChildId: '-2', conditionType: 'user_staff_con', conditionParams: '{deptId: "-2", deptType: "2" }' }
        if ('1' != sessionStorage.getItem('firstDeptId')) {
          deptParams.rootId = sessionStorage.getItem('firstDeptId')
          deptParams.orgChildId = sessionStorage.getItem('firstDeptId')
          deptParams.conditionParams = '{deptId: "'+ sessionStorage.getItem('firstDeptId') +'", deptType: "3" }'
        }
        getDeptService(deptParams).then(res => {
          if (res.code === '0000' && res.data) {
            that.checkDept=res.data
          }
        })
      }
    },
    openChooseLeaderUserDailog(row, type) {
      this.userRow = row
      this.userRowType = type
      this.selectUserFlag = false
      const item = {
        excuterNames: row.groupLeaderName || '',
        excuterIds: row.groupLeaderId || ''
      }
      const deptParams = {rootId: '-2', orgChildId: '-2', conditionType: 'user_staff_con', conditionParams: '{deptId: "-2", deptType: "1,2" }' }
      if ('1' != sessionStorage.getItem('firstDeptId')) {
        deptParams.rootId = sessionStorage.getItem('firstDeptId')
        deptParams.orgChildId = sessionStorage.getItem('firstDeptId')
        deptParams.conditionParams = '{deptId: "'+ sessionStorage.getItem('firstDeptId') +'", deptType: "3" }'
      }
      this.$refs.chooseUser.init(item, deptParams)
    },
    openChooseTeamUserDailog(row, type) {
      this.userRow = row
      this.userRowType = type
      this.selectUserFlag = true
      const item = {
        excuterNames: row.teamMembersNames || '',
        excuterIds: row.teamMembersIds || ''
      }
      const deptParams = {rootId: '-2', orgChildId: '-2', conditionType: 'user_staff_con', conditionParams: '{deptId: "-2", deptType: "1,2" }' }
      if ('1' != sessionStorage.getItem('firstDeptId')) {
        deptParams.rootId = sessionStorage.getItem('firstDeptId')
        deptParams.orgChildId = sessionStorage.getItem('firstDeptId')
        deptParams.conditionParams = '{deptId: "'+ sessionStorage.getItem('firstDeptId') +'", deptType: "3" }'
      }
      this.$refs.chooseUser.init(item, deptParams)
      // this.$refs.chooseUser.init(item)
    },
    showUserCheckList({ checkList }) {
      let that = this;
      const list = checkList
      const names = []
      const ids = []
      list.forEach(item => {
        names.push(item.realName)
        ids.push(item.userId)
      })
      that.staffStationary = that.staffStationary.map(item => {
        if ((that.userRow.id ? item.id === that.userRow.id : false) && item.fontId === this.userRow.fontId) {
          if (that.userRowType == 'lead') {
            return { ...item, groupLeaderName: names.join(','), groupLeaderId: ids.join(',') }
          } else {
            return { ...item, teamMembersNames: names.join(','), teamMembersIds: ids.join(',') }
          }
        } else {
          return { ...item }
        }
      })
    },
    // 点击操作列的按钮，处理或者查看单子
    operateHandle(row) {
      // 校验数据完整性
      this.generateJson(row);
      let msg = '';
      if ((row.groupLeaderId||'') == '' || (row.teamMembersIds||'') == '' || (row.cityIds||'') == '') {
        msg = msg + "未填写完全！<br>"
      }
      if (msg.length > 0) {
        this.$message({
          dangerouslyUseHTMLString: true,
          showClose: true,
          message: msg,
          type: "warning",
        });
        return
      }
      updateCheckService(row).then((res) => {
        if (res && res.code == '0000') {
          if (res.data == '') {
            updateService(row).then((res1) => {
              this.$message({
                dangerouslyUseHTMLString: true,
                showClose: true,
                message: "修改成功！",
                type: "success",
              });
            })
            .finally((_) => {
              this.pageLoading = false;
            });
          } else {
            this.$message({
              dangerouslyUseHTMLString: true,
              showClose: true,
              message: res.data,
              type: "warning",
            });
          }
        } else {
          this.$message({
            dangerouslyUseHTMLString: true,
            showClose: true,
            message: "保存校验失败！",
            type: "warning",
          });
        }
      })
      .finally((_) => {
        this.pageLoading = false;
      });
    },
    // 点击操作列的按钮，处理或者查看单子
    deleteOne(row) {
      const req = {
        sucCb: (res) => {
          if (res.code === '0000') {
            this.$message.success('删除成功')
            this.staffStationary.forEach((item, index) => {
              if (row.fontId === item.fontId) {
                this.staffStationary.splice(index, 1)
              }
            })
          } else {
            this.$message.warning(res.data || '请稍后再试')
          }
          this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
        }
      }
      commonOneDel.call(this, row.id, deleteOneService, req.sucCb)
    },
    // 批量删除
    deleteBatch() {
      const req = {
        data: this.$refs.table.multipleSelection,
        delApi: deleteBatchService,
        key: 'id',
        sucCb: (res) => {
          if (res.code === '0000') {
            this.$message.success('批量删除成功')
            this.getList()
          } else {
            this.$message.warning(res.data || '请稍后再试')
          }
          this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
          this.search(this.$refs?.searchForm?.searchForm || {})
        }
      }
      commonMultDel.call(this, req)
    },
    // 新增行
    add() {
      let that = this;
      let obj = { parentTempId: 1, parentTempName: '', groupLeaderId: '', groupLeaderName: '', teamMembersIds: '', teamMembersNames: '', cityIds: '', cityNames: '', priority: that.staffStationary.length + 1 }
      saveService(obj).then((res) => {
        if (res.code === '0000' && res.data) {
          obj = res.data;
          obj.fontId = that.staffStationary.length
          that.$nextTick(() => {
            that.staffStationary.push(obj)
          })
        }
      }).finally((_) => {
        that.pageLoading = false;
      });
    },
    // 保存
    save() {
      let that = this;
      // 校验数据完整性
      let data = { dtos: that.staffStationary }
      data.dtos.forEach((item,index) => {
        that.generateJson(item);
      })
      let msg = '';
      that.staffStationary.forEach((ele, index) => {
        if ((ele.groupLeaderId||'') == '' || (ele.teamMembersIds||'') == '' || (ele.cityIds||'') == '') {
          msg = msg + "第"+(index+1)+"行未填写完全！<br>"
        }
      });
      if (msg.length > 0) {
        that.$message({
          dangerouslyUseHTMLString: true,
          showClose: true,
          message: msg,
          type: "warning",
        });
        return
      }
      updateBatchCheckService(data).then((res) => {
        if (res && res.code == '0000') {
          if (res.data == '') {
            updateBatchService(data).then((res1) => {
              if (res1.code === '0000' && res1.data) {
                that.$message({
                  dangerouslyUseHTMLString: true,
                  showClose: true,
                  message: "保存成功！",
                  type: "success",
                });
              }
            }).finally((_) => {
              that.pageLoading = false;
            });
          } else {
            that.$message({
              dangerouslyUseHTMLString: true,
              showClose: true,
              message: res.data,
              type: "warning",
            });
          }
        } else {
          that.$message({
            dangerouslyUseHTMLString: true,
            showClose: true,
            message: "保存校验失败！",
            type: "warning",
          });
        }
      })
      .finally((_) => {
        that.pageLoading = false;
      });
    },
    generateJson(row){
      let that = this;
      let cityIds = ''
      let cityNames = ''
      row.cityIdArr.forEach((item1,index1) => {
        that.checkDept.forEach((item2,index2) => {
          if (item1 == item2.id) {
            if (cityNames.length > 0) {
              cityNames = cityNames + ','
            }
            cityNames = cityNames + item2.text
          }
        })
        if (cityIds.length > 0) {
          cityIds = cityIds + ','
        }
        cityIds = cityIds + item1
      })
      row.cityIds = cityIds
      row.cityNames = cityNames
    },

  }
}
</script>
