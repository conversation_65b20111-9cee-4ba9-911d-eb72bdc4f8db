<template>
  <div>
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssCard title="模板基本信息">
      <div slot="content">
        <mssReportTemplate
          ref="reportTemplate"
          :templateCode="templateCode"
          isView
          :isTemplate="true"
        ></mssReportTemplate>
      </div>
    </mssCard>
  </div>
</template>

<script>
export default {
  name: 'TemplateManagementInfo',
  data() {
    return {
      returnAddress: '/monthly_report/work_reporting/template_management_list',
      id: '',
      templateCode: ''
    }
  },
  created() {
    this.templateCode = this.$route.query.reportTemplateCode
    this.id = this.$route.query.id
  },
  methods: {
    goBack() {
      let reportTemplate = this.$refs.reportTemplate.$refs
      if (reportTemplate) {
        let unsaved = []
        for (const key in reportTemplate) {
          if (reportTemplate[key].constructor == Array) {
            reportTemplate[key].forEach((item) => {
              if (item.editStatus) {
                unsaved.push(item.item.name)
              }
            })
          } else {
            if (reportTemplate[key].editStatus) {
              unsaved.push(reportTemplate[key].item.name)
            }
          }
        }
        if (unsaved.length) {
          this.$confirm(
            `${unsaved.join()}--模板编辑后未保存，是否继续退出？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
            .then(() => {
              this.$closeTagView({
                close: this.$route.fullPath,
                to: this.returnAddress
              })
            })
            .catch(() => {})
        } else {
          this.$closeTagView({
            close: this.$route.fullPath,
            to: this.returnAddress
          })
        }
      } else {
        this.$closeTagView({
          close: this.$route.fullPath,
          to: this.returnAddress
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>