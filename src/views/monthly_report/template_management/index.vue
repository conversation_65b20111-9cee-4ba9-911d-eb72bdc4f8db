
<template>
  <div>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :selection="false"
          :api="api"
          :serial="false"
          :pagination="false"
          :dealData="dealData"
          :columns="columns"
          border
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { getReportTempatesService } from '@/api/report_template_api'
export default {
  name: 'TemplateManagementList',
  data() {
    return {
      api: getReportTempatesService,
      columns: [
        {
          label: '模板名称',
          prop: 'name',
          tooltip:true,
        },
        {
          label: '对应角色名称',
          prop: 'reportRoleName',
          tooltip:true,
        },
        {
          label: '创建日期',
          prop: 'createDate',
          tooltip:true,
          width:200,
        },
        {
          label: '操作',
          prop: '_operationCol',
          width:80,
          formatter: (row) => {
            return (
              <span>
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row)
                  }}
                >
                详情
                </el-button>
              </span>
            )
          }
        }
      ]
    }
  },
  created() {
  },
  methods: {
    // 点击操作列的按钮，处理或者查看单子
    action(row) {
      this.$router.push({
        path: '/monthly_report/work_reporting/template_management_info',
        query: {
          id:row.id,
          reportTemplateCode:row.code,
          pathlabel: encodeURIComponent(`月工作上报模板管理详情页`),
        }
      })
    },
    dealData(data){
      data.forEach(item=>{
        this.$set(item,'createDate','2023-08-10')
      })
      return data
    }
  }
}
</script>
