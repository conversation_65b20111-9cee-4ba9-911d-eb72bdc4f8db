<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-07 15:36:26
 * @Description: 物资管理-完工稽核
-->
<template>
	<div class="meterial_completion_audit">
		<mssSearchForm 
			:searchConfig="searchConfig" 
			ref="searchForm" 
			@search="search" 
			@reset="reset"
			@openDialog="openDialog"
		></mssSearchForm>
		<mssCard :title="tableName">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable 
				 	ref="table"
					border
					:columns="columns"
					:staticSearchParam="staticSearchParam"
					:api="tableApi"
				/>
			</div>
		</mssCard>
		<mssChooseDept ref="chooseDept" @showCheckList="showDeptList"></mssChooseDept>
	</div>
</template>

<script>
import { commonDown } from '@/utils/btn.js'
import { findPageService, exportExcelService } from '@/api/material/completion_audit.js';
import { queryAreaListService } from "@/api/common_api"
export default {
	name: 'meterial_completion_audit',
	data() {
		return {
			searchConfig: [
				{
					type: 'select',
					fieldName: 'city',
					label: '地市',
					options: []
				},
				{
					type: 'input',
					fieldName: 'projectCode',
					label: '项目编码'
				},
				{
					type: 'input',
					fieldName: 'projectName',
					label: '项目名称'
				},
				{
					type: 'input',
					fieldName: 'taskCode',
					label: '任务编码'
				},
				{
					type: 'input',
					fieldName: 'taskName',
					label: '任务名称'
				},
        {
					type: 'dialog',
					fieldName: 'constructionName',
					label: '施工单位'
				},
				{
					type: 'input',
					fieldName: 'materialCode',
					label: '物料编码'
				},
				{
					type: 'input',
					fieldName: 'materialDesc',
					label: '物料名称'
				},
			],
			tableName: this.$route.meta.title,
			staticSearchParam: {},
      tableApi: findPageService,
			dept: []
		}
	},
	computed:{
		columns:{
			get(){
				return [
					{prop: "projectCode",label: "项目编码",minWidth: 180, tooltip: true,formatter: row => {
						return (
							<span class='table_btn' onClick={()=>{this.toDetail(row)}}>{row.projectCode}</span>
						)
					}},
					{label: "项目名称",prop:"projectName",minWidth: 180, tooltip: true},
					{prop: "city",label: "地市",minWidth: 120, tooltip: true},
					{prop: "materialCode",label: "物料编码",minWidth: 180, tooltip: true},
					{prop: "materialDesc",label: "物料名称",minWidth: 140,tooltip: true},
					{prop: "designNum",label: "设计数量",minWidth: 100, tooltip: true},
					{prop: "projectExpNumber",label: "领用数量",minWidth: 100, tooltip: true},
					{prop: "usedNum",label: "使用数量",minWidth: 100, tooltip: true},
          {prop: "outputNum",label: "需退库数量",minWidth: 100, tooltip: true}
				]
			}
		}
			
	},
	created(){
		this.getAreaList('-2', 0)
	},
	methods: {
		getAreaList(parentId, index) {
			queryAreaListService({ parentId: parentId, typeCode: 'area' }).then(res => {
				if (res.code === '0000') {
				const list = []
				res.data.forEach(item => {          
					list.push({ label: item.name, value: item.id })
				})
				this.$set(this.searchConfig[index], 'options', list)
				}
			})
    },
		openDialog(){
			const item = this.dept
			this.$refs.chooseDept.init(item, {conditionType: "completionConstructionDept"});
		},
		showDeptList({ checkList }) {
			const list = checkList
			const names = []
			const ids = []
			list.forEach(item => {
				names.push(item.text)
				ids.push(item.id)
			})
			this.$set(this.$refs.searchForm.searchForm, 'constructionUnit', ids.join(','))
			this.$set(this.$refs.searchForm.searchForm, 'constructionName', names.join(','))
		},
		search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = { ...this.staticSearchParam,...form }
			this.$refs.table.getTableData(form)
    },
    reset(form) {
			form.constructionUnit && (form.constructionUnit = '')
      this.search(form)
    },
		exportMethod(){
			commonDown(this.staticSearchParam, exportYearScoreExcelService);
		},
		exportMethod() {
			commonDown(this.staticSearchParam, exportExcelService);
		},
		toDetail(row){
			this.$router.push({
				path: '/material/completion_audit_detail',
				query: {
					id: row.id,
					projectName:  row.projectName,
					projectCode: row.projectCode,
					buildUnit: row.buildUnit
				}
			})
		}
	}
}
</script>
