<!--
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-07 09:38:15
 * @Description: xxx暂存点任务详情
-->

<template>
	<div class="completionAuditDetail">
		<div class="operate-btn">
			<el-button @click="goBack">返回</el-button>
		</div>

		<div id="sectionBasic" class="page-anchor-point"></div>
		<mssCard title="项目信息">
			<div slot="content">
				<mssForm labelWidth="120px" disableForm :config="basicConfig" :labelPosition="labelPosition" :form="basicForm">
				</mssForm>
			</div>
		</mssCard>

		<div id="sectionHistory" class="page-anchor-point"></div>
		<mssCard title="物资领用信息">
			<div slot="headerBtn">
				<el-button type="primary" @click="exportMethod">导出</el-button>
			</div>
			<div slot="content">
				<mssTable border :columns="columns" :staticSearchParam="staticSearchParam" :api="tableApi">
				</mssTable>
			</div>
		</mssCard>
		<mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
	</div>
</template>
  
<script>
import { commonDown } from '@/utils/btn';
import { detailPageService, exportDetailExcelService, getMaterialInfoById } from '@/api/material/completion_audit.js';
export default {
	name: 'completionAuditDetail',
	data() {
		return {
			boId: '',
			basicConfig: [
				{
					label: '项目编码：',
					type: 'input',
					prop: 'projectCode',
					span: 12
				},
				{
					label: '项目名称：',
					type: 'input',
					prop: 'projectName',
					span: 12
				},
				{
					label: '建设单位：',
					type: 'input',
					prop: 'buildUnitName',
					span: 12
				},
				{
					label: '监理单位：',
					type: 'input',
					prop: 'supervisionName',
					span: 12
				}
			],
			basicForm: {},
			labelPosition: 'left', // 查看页面放在左边
			pageAnchorConfig: [
				{
					text: '项目<br>信息',
					id: 'sectionBasic'
				},
				{
					text: '领用<br>信息',
					id: 'sectionHistory'
				}
			],
			staticSearchParam: {
				projectCode: this.$route.query.projectCode,
				projectName: this.$route.query.projectName,
				taskCode: this.$route.query.taskCode,
				buildUnit: this.$route.query.buildUnit
			},
			tableApi: detailPageService
		}
	},
	computed: {
		columns: {
			get() {
				return [
					{ prop: "projectCode", label: "项目编码" ,minWidth: 160, tooltip: true},
					{ prop: "projectName", label: "项目名称", minWidth: 180, tooltip: true },
					{ prop: "taskCode", label: "任务编码",minWidth: 160, tooltip: true },
					{ prop: "taskName", label: "任务名称",minWidth: 160, tooltip: true},
					{ prop: "constructionUnitName", label: "施工单位",minWidth: 160, tooltip: true},
					{ prop: "materialDesc", label: "物料名称",minWidth: 160, tooltip: true},
					{ prop: "materialCode", label: "物料编码",minWidth: 160, tooltip: true},
					{ prop: "unit", label: "单位" ,tooltip: true},
					{ prop: "designNum", label: "设计数量" ,tooltip: true},
					{ prop: "projectExpNumber", label: "领用量" ,tooltip: true},
					{ prop: "usedNum", label: "使用量" ,tooltip: true},
					{ prop: "outputNum", label: "需退库量" ,tooltip: true},
				]
			}
		}
	},
	created() {
		this.getMaterialInfo()
	},
	methods: {
		getMaterialInfo() {
			getMaterialInfoById(this.$route.query.id).then(res => {
				if(res && res.code === "0000"){
					this.basicForm = res.data
				}
			})	
		},
		goBack() {
			this.$closeTagView({
        close: this.$route.fullPath,
        to: '/material/completion_audit'
      })
		},
		exportMethod() {
			commonDown(this.staticSearchParam, exportDetailExcelService);
		},
	}
}
</script>
  
<style lang="scss" scoped>
.completionAuditDetail {
	padding-right: 70px;

	.operate-btn {
		text-align: right;
		margin-bottom: 10px;
	}
}
</style>
  
  