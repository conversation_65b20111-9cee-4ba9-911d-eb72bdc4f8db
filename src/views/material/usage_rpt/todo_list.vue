<template>
  <div class="todoList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :staticSearchParam="staticSearchParam"
          :api="api"
          :columns="columns"
          border
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { findPageService } from '@/api/material/usage_rpt_api.js'
export default {
  name: 'todoList',
  data() {
    return {
      searchConfig: [
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        }
      ],
      api: findPageService,
      staticSearchParam: {
        entityStatus: 'in_process'
      },
      columns: [
        {
          label: '任务单编号',
          prop: 'code',
          tooltip:true,
        },
        {
          label: '任务申请单名称',
          prop: 'name',
          tooltip:true,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip:true,
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip:true,
          width:120,
        },
        // {
        //   label: '任务名称',
        //   prop: 'taskName'
        // },
        // {
        //   label: '任务编码',
        //   prop: 'taskCode'
        // },
        {
          prop: 'operate',
          label: '操作',
          width:100,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.action(row, 'edit')
                    }}
                  >
                    处理
                  </el-button>
                ) : (
                  ''
                )}
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row, 'details')
                  }}
                >
                  查看
                </el-button>
              </span>
            )
          }
        }
      ]
    }
  },
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'in_process',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'in_process',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    action(row,type) {
      this.$router.push({
        path: '/material/usage_rpt_edit',
        query: {
          boId: row.id,
          operateType: type,
          pathlabel: encodeURIComponent(
            `物资使用填报任务${type == 'edit' ? '处理页' : '详情页'}`
          )
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
