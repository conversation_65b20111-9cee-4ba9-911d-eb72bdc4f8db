<template>
  <div class="usageRptEdit page-anchor-parentpage">
    <div class="operate-btn">
      <el-button v-if="boId && dealPage" type="primary" @click="submit">提交</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionUsageRpt" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="物资使用填报信息">
      <div slot="headerBtn">
        <el-upload
          v-if="importBtnFlag"
          class="upload-btn"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button @click="exportTable">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="usageRptTable"
          class="usageRptTable"
          :columns="columns"
          :api="api"
          :staticSearchParam="staticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :boId="boId" :workflowCode="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      :saveMainForm="false"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
  </div>
</template>

<script>
import {
  getDetailsById,
  detailFindPageService,
  exportExcelService,
  importExcelService,
  flowValidateService
} from '@/api/material/usage_rpt_api'
import { commonDown } from '@/utils/btn'
export default {
  name: 'usageRptEdit',
  data() {
    return {
      basicConfig: [
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          disabled: true
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          disabled: true
        },
        {
          label: '建设单位：',
          type: 'input',
          prop: 'buildUnitName',
          disabled: true
        },
        {
          label: '监理单位：',
          type: 'input',
          prop: 'supervisionName',
          disabled: true
        }
      ],
      labelPosition: 'left',
      basicForm: {},
      boId: '',
      columns: [
        {
          prop: 'projectCode',
          label: '项目编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectName',
          label: '项目名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskName',
          label: '任务名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskCode',
          label: '任务编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialDesc',
          label: '物料名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialCode',
          label: '物料编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'unit',
          label: '单位',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'designNum',
          label: '设计数量',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'receiveNum',
          label: '领用量',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'usedNum',
          label: '使用量',
          align: 'center',
          tooltip: true
        }
      ],
      api: detailFindPageService,
      staticSearchParam: {},
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '填报<br>信息',
          id: 'sectionUsageRpt'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow'
        }
      ],
      workflowCode: 'UsageRpt',
      completeTaskUrl: '',
      returnAddress: '/material/usage_rpt_list',
      operateType: '',
      dealPage: true,
      submitFlag: true,
      nodeName: '草稿',
      nodeCode:'',
      approval: true
    }
  },
  computed:{
    importBtnFlag() {
      return this.nodeCode == 'syltb'
    },
  },
  created() {
    this.boId = this.$route.query.boId
    this.staticSearchParam.rptId = this.boId
    this.operateType = this.$route.query.operateType
    this.getDetailsById()
  },
  mounted() {
    if (this.operateType == 'edit') {
      this.$refs.workFlow.init()
    } else {
      this.dealPage = false
    }
  },
  methods: {
    getDetailsById() {
      getDetailsById(this.boId).then((res) => {
        if (res && res.code == '0000') {
          this.basicForm = res.data
          this.completeTaskUrl = res.data.completeTaskUrl
          this.approval = res.data.approval
        }
      })
    },
    submit() {
      if (this.submitFlag) {
        this.$refs.workFlow.opendialogInitNextPath()
      } else {
        this.$message({
          showClose: true,
          message: '请先导入物资使用信息',
          type: 'warning'
        })
      }
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const approval = {
        code: 'approval',
        value: this.approval
      }
      workFlowPrams.push(approval)
      const name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.name
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit() {
      let str = "";
      let res = await flowValidateService({ boId: this.boId });
      if (res.data && res.data.code != 0) {
        str = res.data.msg;
      }
      return str;
    },
    beforeNode() {
      // 设置默认处理人
      const fileNameArr = {
        sylqr: {
          userName: this.$refs.basicForm.modelForm.supervisionerName,
          userId: this.$refs.basicForm.modelForm.supervisionerId
        },
        sylqr2: {
          userName: this.$refs.basicForm.modelForm.projectManagerName,
          userId: this.$refs.basicForm.modelForm.projectManagerId
        }
      }
      return fileNameArr
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/material/usage_rpt_list'
      })
    },
    //导出
    exportTable() {
      let req = {
        boId: this.boId
      }
      commonDown(req, exportExcelService)
    },
    //导入
    importFile(params) {
      let param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.boId)
      importExcelService(param).then((res) => {
        if (res.code == '0000' && !res.data) {
          this.$message.success('导入成功')
          this.$refs.usageRptTable.page.current = 1
          this.$refs.usageRptTable.getTableData()
          this.getDetailsById()
        } else if (res.data) {
          this.$message({
            showClose: true,
            message: res.data,
            type: 'warning'
          })
        }
      })
    },
    getNodeData(data) {
      this.nodeName = data.nodeName
      this.nodeCode = data.nodeCode
    }
  }
}
</script>

<style lang="scss" scoped>
.usageRptEdit {
  .upload-btn {
    display: inline-block;
    margin-right: 10px;
  }
}
</style>
