<!--
 * @Author: hcm
 * @Date: 2023-07-17
 * @Description: 物资管理-领用稽核
-->
<template>
  <div class="meterial_receive_audit">
    <mssSearchForm
      ref="searchForm"
      :search-config="formConfig"
      @search="search"
      @reset="reset"
    />
    <mssCard :title="'领用督办信息'">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportMethod">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          border
          ref="commonTable"
          :columns="columns"
          :api="tableApi"
        />
      </div>
    </mssCard>
  </div>
</template>

<script>
import {queryAreaListService } from '@/api/common_api.js'
import { commonDown } from '@/utils/btn'
import { findPageService, exportPageService } from '@/api/material/receive_audit.js'
export default {
  name: 'ReceiveAudit',
  data() {
    return {
      tableApi: findPageService,
      cityList: []
    }
  },
  computed: {
    formConfig:{
      get() {
        return [
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },{
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },{
          label: '地市',
          type: 'select',
          fieldName: 'city',
          options: this.cityList
        },{
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        },{
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },{
          label: '施工单位',
          type: 'input',
          fieldName: 'providerName'
        },
      ]
      }
    },
    columns: {
      get() {
        return [
          { prop: 'projectCode', label: '项目编号', tooltip: true,
            minWidth: '100px' },
          { prop: 'projectName', label: '项目名称', tooltip: true,
            minWidth: '100px' },
            { prop: 'cityName', label: '地市', tooltip: true,
            minWidth: '80px' },
          { prop: 'receiveAnomalyTaskNum', label: '领用量异常任务数', tooltip: true,
            minWidth: '100px', formatter: (row) => {
              return <span style='color:red;'> {row.receiveAnomalyTaskNum}</span>
            } },
          { prop: 'receiveAnomalyMatNum', label: '领用异常物料类型', tooltip: true,
            minWidth: '100px', formatter: (row) => {
              return <span style='color:red;'> {row.receiveAnomalyMatNum}</span>
            } },
          { label: '操作', tooltip: true,
            minWidth: '100px', formatter: (row) => {
              return <span>
                {row.isAllowOperate ? (<span
                  class='table_btn mr10'
                  onClick={() => { this.view(row, 'edit') }}
                >
                  处理
                </span>) : ('')}
                <span class='table_btn' onClick={() => { this.view(row, 'view') }}>查看</span>
              </span>
            } }
        ]
      }
    }
  },
  created(){
    this.getAreaList()
  },
  methods: {
    search(form) {
      this.staticSearchParam = { ...form }
      this.$refs.commonTable.page.current = 1
      this.$refs.commonTable.getTableData(form)
    },
    reset(form) {
      this.search(form)
    },
    getAreaList() {
      queryAreaListService({ parentId: '-2', typeCode: 'area' }).then(res => {
        if (res.code === '0000') {
          const list = []
          res.data.forEach(item => {
            list.push({ label: item.name, value: item.name })//后台让给传名字
          })
          this.cityList = list
        }
      })
    },
    exportMethod() {
      const params = {
        ...this.$refs.searchForm.searchForm,
        limit: this.$refs.commonTable.page.size,
        page: this.$refs.commonTable.page.current,
        exportType: 'all'
      }
      commonDown(params, exportPageService)
    },
    view(row, operateType) {
      this.$router.push({
        path: '/material/receive_audit_detail',
        query: {
          boId: row.id,
          operateType
        }
      })
    }
  }
}
</script>
