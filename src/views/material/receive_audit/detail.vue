<!--
 * @Author: hcm
 * @Date: 2023-07-17
 * @Description: 物资管理-领用稽核详情
-->

<template>
  <div class="completionAuditDetail">
    <div class="operate-btn">
      <el-button v-if="operateType" type="primary" @click="submit">提交</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>

    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm ref="basicForm" label-width="120px" disable-form :config="basicConfig" :label-position="labelPosition" :form="basicForm">
        </mssForm>
      </div>
    </mssCard>

    <div id="sectionHistory" class="page-anchor-point"></div>
    <mssCard title="物资领用信息">
      <div slot="headerBtn">
        <el-button type="primary" @click="exportMethod">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          border
          :columns="columns"
          :static-search-param="staticSearchParam"
          :api="receiveApi"
        >
        </mssTable>
      </div>
    </mssCard>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :bo-id="boId" :workflow-code="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssWorkFlowHandel
      ref="workFlow"
      :bo-id="boId"
      :workflow-code="workflowCode"
      :action-url="completeTaskUrl"
      :return-address="returnAddress"
      :save-main-form="false"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
  </div>
</template>

<script>
import { commonDown } from '@/utils/btn'
import { receiveauditdetailService, auditDetailService, exportReceiveaudit } from '@/api/material/receive_audit.js'
export default {
  name: 'CompletionAuditDetail',
  data() {
    return {
      receiveApi: receiveauditdetailService,
      basicConfig: [
        {
          label: '项目编码：',
          type: 'input',
          prop: 'projectCode',
          span: 12
        },
        {
          label: '项目名称：',
          type: 'input',
          prop: 'projectName',
          span: 12
        },
        {
          label: '建设单位：',
          type: 'input',
          prop: 'constructUnitInfos',
          span: 12
        },
        {
          label: '监理单位：',
          type: 'input',
          prop: 'supervisionUnitInfos',
          span: 12
        }
      ],
      basicForm: {
        projectName: '',
        projectCode: '',
        constructUnitInfos: '',
        supervisionUnitInfos: ''
      },
      labelPosition: 'left', // 查看页面放在左边
      pageAnchorConfig: [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '领用<br>信息',
          id: 'sectionHistory'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow'
        }
      ],
      staticSearchParam: {},
      boId: '',
      workflowCode: 'ReceiveAudit',
      completeTaskUrl: '',
      returnAddress: '/material/receive_audit',
      type: null
    }
  },
  computed: {
    columns: {
      get() {
        return [
          { prop: 'projectCode', label: '项目编码' },
          { prop: 'projectName', label: '项目名称' },
          { prop: 'providerName', label: '施工单位' },
          { prop: 'taskName', label: '任务名称' },
          { prop: 'taskCode', label: '任务编码' },
          // { prop: 'materialType', label: '物料类型' },
          { prop: 'materialCode', label: '物料编码' },
          { prop: 'materialName', label: '物料名称' },
          { prop: 'unit', label: '单位' },
          { prop: 'designNum', label: '设计数量' },
          { prop: 'receiveNum', label: '领用量', formatter: (row) => {
            return <span style='color:red;'>{row.receiveNum}</span>
          } },
          { prop: 'receiveStatus', label: '领用量状态', formatter: (row) => {
            return <span style='color:red;'>异常</span>
          } }
        ]
      }
    }
  },
  created() {
    this.boId = this.$route.query.boId
    this.operateType = this.$route.query.operateType === 'edit'
    this.staticSearchParam = {
      auditId: this.boId
    }
    this.getInfo()
  },
  methods: {
    getInfo() {
      auditDetailService(this.boId).then(res => {
        if (res.code === '0000') {
          this.basicForm = res.data
          if (this.operateType) {
            this.completeTaskUrl = res.data.completeTaskUrl
            this.$refs.workFlow.init()
          }
        }
      })
    },
    goBack() {
      this.$router.push({
        path: '/material/receive_audit'
      })
    },
    exportMethod() {
      commonDown({ exportType: 'all', id: this.boId }, exportReceiveaudit)
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.name
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    beforeNode() {
      const fileNameArr = {
        sp: { userName: 'admin', userId: '1' }
      }
      // 设置默认处理人
      return fileNameArr
    },
    submit() {
      this.$refs.workFlow.opendialogInitNextPath()
    },
    getNodeData(data) {
    }
  }
}
</script>

<style lang="scss" scoped>
.completionAuditDetail {
	padding-right: 70px;

	.operate-btn {
		text-align: right;
		margin-bottom: 10px;
	}
}
</style>

