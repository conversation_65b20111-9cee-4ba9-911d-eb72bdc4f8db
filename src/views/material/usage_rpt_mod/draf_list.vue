<template>
  <div class="todoList">
    <mssSearchForm :searchConfig="searchConfig" ref="searchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="headerBtn">
        <el-button @click="delHandle('multiple')">删除</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          :staticSearchParam="staticSearchParam"
          :api="api"
          :columns="columns"
          border
          selection
          :selectable="selectable"
          :reserveSelection="false"
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {
  findModPageService,
  multipleDelService,
  singleDelService
} from '@/api/material/usage_rpt_api.js'
import { commonMultDel, commonOneDel } from '@/utils/btn'
export default {
  name: 'draftList',
  data() {
    return {
      searchConfig: [
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        }
      ],
      api: findModPageService,
      staticSearchParam: {
        entityStatus: 'draft'
      },
      columns: [
        {
          label: '任务单编号',
          prop: 'code',
          tooltip: true
        },
        {
          label: '任务申请单名称',
          prop: 'name',
          tooltip: true
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip: true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip: true,
          width: 120
        },
        // {
        //   label: '任务名称',
        //   prop: 'taskName'
        // },
        // {
        //   label: '任务编码',
        //   prop: 'taskCode'
        // },
        {
          prop: 'operate',
          label: '操作',
          width: 120,
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.action(row, 'edit')
                    }}
                  >
                    处理
                  </el-button>
                ) : (
                  ''
                )}
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row, 'details')
                  }}
                >
                  查看
                </el-button>
                {row.isAllowDelete ? (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.delHandle('single', row)
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ''
                )}
              </span>
            )
          }
        }
      ]
    }
  },
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'in_process',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: 'in_process',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    delHandle(type, row) {
      if (type == 'single') {
        commonOneDel.call(
          this,
          row.id,
          singleDelService,
          (res) => {
            if (res.code === '0000') {
              this.$message.success('删除成功')
            }
            this.search(this.$refs?.searchForm?.searchForm || {})
          },
          'path'
        )
      } else {
        const req = {
          data: this.$refs.table.multipleSelection,
          delApi: multipleDelService,
          key: 'id',
          sucCb: (res) => {
            if (res.code === '0000') {
              this.$message.success('删除成功')
            }
            this.$refs.table.$refs.table.clearSelection() // 清空表格勾选
            this.search(this.$refs?.searchForm?.searchForm || {})
          }
        }
        commonMultDel.call(this, req)
      }
    },
    action(row, type) {
      this.$router.push({
        path: '/material/usage_rpt_mod_edit',
        query: {
          boId: row.id,
          hisId: row.hisId,
          operateType: type,
          pathlabel: encodeURIComponent(
            `物资使用修订任务${type == 'edit' ? '处理页' : '详情页'}`
          )
        }
      })
    },
    selectable(row) {
      return row.isAllowDelete
    }
  }
}
</script>

<style lang="scss" scoped>
</style>