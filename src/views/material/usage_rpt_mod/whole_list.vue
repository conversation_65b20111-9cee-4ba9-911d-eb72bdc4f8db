<template>
  <div class="wholeList">
    <mssSearchForm :searchConfig="searchConfig" ref="SearchForm" @search="search" @reset="reset"></mssSearchForm>
    <mssCard title="查询结果">
      <div slot="content">
        <mssTable
          ref="table"
          :staticSearchParam="staticSearchParam"
          :api="api"
          :columns="columns"
          border
        ></mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { findModPageService } from '@/api/material/usage_rpt_api.js'
export default {
  name: 'wholeList',
  data() {
    return {
      searchConfig: [
        {
          label: '项目编码',
          type: 'input',
          fieldName: 'projectCode'
        },
        {
          label: '项目名称',
          type: 'input',
          fieldName: 'projectName'
        },
        {
          label: '任务编码',
          type: 'input',
          fieldName: 'taskCode'
        },
        {
          label: '任务名称',
          type: 'input',
          fieldName: 'taskName'
        }
      ],
      api: findModPageService,
      staticSearchParam: {
        entityStatus: ''
      },
      columns: [
        {
          label: '任务单编号',
          prop: 'code',
          tooltip:true,
        },
        {
          label: '任务申请单名称',
          prop: 'name',
          tooltip:true,
        },
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip:true,
        },
        {
          label: '项目编号',
          prop: 'projectCode',
          tooltip:true,
          width:120,
        },
        {
          label: '任务名称',
          prop: 'taskName',
          tooltip:true,
        },
        {
          label: '任务编码',
          prop: 'taskCode',
          tooltip:true,
          width:120,
        },
        {
          prop: 'operate',
          label: '操作',
          width:80,
          formatter: (row) => {
            return (
              <span>
                <el-button
                  type="text"
                  onClick={() => {
                    this.action(row)
                  }}
                >
                查看
                </el-button>
              </span>
            )
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    //查询
    search(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: '',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    //重置
    reset(searchForm) {
      this.$refs.table.page.current = 1
      this.staticSearchParam = {
        entityStatus: '',
        ...searchForm
      }
      this.$refs.table.getTableData(searchForm)
    },
    action(row) {
      this.$router.push({
        path: '/material/usage_rpt_mod_edit',
        query: {
          boId: row.id,
          operateType: 'details'
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
