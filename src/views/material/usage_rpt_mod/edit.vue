<template>
  <div class="usageRptEdit page-anchor-parentpage">
    <div class="operate-btn">
      <el-button v-if="boId && dealPage" type="primary" @click="submit">提交</el-button>
      <el-button v-if="!boId" @click="save">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          :config="basicConfig"
          :rules="basicRules"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disable-form="disableBasicForm"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionUsageRpt" class="page-anchor-point"></div>
    <mssCard v-if="hisStaticSearchParam.projectCode" title="物资使用填报信息（历史）">
      <div slot="headerBtn">
        <el-button @click="exportTable">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="usageRptTable"
          class="usageRptTable"
          :columns="columns"
          :api="hisApi"
          :staticSearchParam="hisStaticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
    <div id="sectionUsageRptMod" class="page-anchor-point"></div>
    <mssCard v-if="modStaticSearchParam.rptModId" title="物资使用修订填报信息">
      <div slot="headerBtn">
        <el-upload
          v-if="importBtnFlag"
          class="upload-btn"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="importFile"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button @click="exportTable1">导入模板下载</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="usageRptModTable"
          class="usageRptTable"
          :columns="modColumns"
          :api="modApi"
          :staticSearchParam="modStaticSearchParam"
        ></mssTable>
      </div>
    </mssCard>
    <div id="sectionWorkFlow" class="page-anchor-point"></div>
    <mssCard v-if="boId" title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory :boId="boId" :workflowCode="workflowCode"></mssWorkFlowHistory>
      </div>
    </mssCard>
    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      :saveMainForm="false"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
  </div>
</template>

<script>
import {
  getModDetailsById,
  exportHisExcelService,
  importModExcelService,
  getDetailsByHisId,
  savePageService,
  findHistoryPageService,
  detailFindModPageService,
  exportTemplateService,
  modFlowValidateService
} from '@/api/material/usage_rpt_api'
import { commonDown } from '@/utils/btn'
export default {
  name: 'usageRptEdit',
  data() {
    return {
      basicConfig: [
        {
          label: '任务工单编号（历史）',
          type: 'input',
          prop: 'hisEntityCode',
          disabled: true
        },
        {
          label: '任务工单主题（历史）',
          type: 'input',
          prop: 'hisEntityName',
          disabled: true
        },
        {
          label: '任务工单编号（修订）',
          type: 'input',
          prop: 'code',
          disabled: true
        },
        {
          label: '任务工单主题（修订）',
          type: 'input',
          prop: 'name',
          disabled: true
        },
        {
          label: '项目编码',
          type: 'input',
          prop: 'projectCode',
          disabled: true
        },
        {
          label: '项目名称',
          type: 'input',
          prop: 'projectName',
          disabled: true
        },
        {
          label: '建设单位',
          type: 'input',
          prop: 'buildUnitName',
          disabled: true
        },
        {
          label: '监理单位',
          type: 'input',
          prop: 'supervisionName',
          disabled: true
        },
        {
          label: '申请人',
          type: 'input',
          prop: 'creatorName',
          disabled: true
        },
        {
          label: '申请时间',
          type: 'input',
          prop: 'createDate',
          disabled: true
        },
        {
          label: '修订原因',
          type: 'input',
          prop: 'applyReason'
        }
      ],
      labelPosition: 'top',
      basicForm: {},
      boId: '',
      columns: [
        {
          prop: 'projectCode',
          label: '项目编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectName',
          label: '项目名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskName',
          label: '任务名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskCode',
          label: '任务编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialDesc',
          label: '物料名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialCode',
          label: '物料编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'unit',
          label: '单位',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'designNum',
          label: '设计数量',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'receiveNum',
          label: '领用量',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'usedNum',
          label: '使用量',
          align: 'center',
          tooltip: true
        }
      ],
      modColumns: [
        {
          prop: 'projectCode',
          label: '项目编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'projectName',
          label: '项目名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskName',
          label: '任务名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'taskCode',
          label: '任务编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialDesc',
          label: '物料名称',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'materialCode',
          label: '物料编码',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'unit',
          label: '单位',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'designNum',
          label: '设计数量',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'receiveNum',
          label: '领用量',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'hisUsedNum',
          label: '修订前使用量',
          align: 'center',
          tooltip: true
        },
        {
          prop: 'reviseUsedNum',
          label: '本次修订使用量',
          align: 'center',
          tooltip: true
        }
      ],
      hisApi: findHistoryPageService,
      modApi: detailFindModPageService,
      hisStaticSearchParam: {},
      modStaticSearchParam: {},
      workflowCode: 'UsageRptMod',
      completeTaskUrl: '',
      returnAddress: '/material/usage_rpt_mod_list',
      operateType: '',
      dealPage: true,
      nodeName: '草稿',
      hisId: '',
      basicRules: {
        applyReason: [
          { required: true, message: '请输入修订原因', trigger: 'blur' }
        ]
      },
      disableBasicForm: true,
      nodeCode:"",
      submitFlag: true
    }
  },
  computed:{
    importBtnFlag() {
      return this.nodeCode == 'cg'
    },
    pageAnchorConfig(){
      return [
        {
          text: '基本<br>信息',
          id: 'sectionBasic'
        },
        {
          text: '历史<br>信息',
          id: 'sectionUsageRpt'
        },
        {
          text: '修订<br>信息',
          id: 'sectionUsageRptMod'
        },
        {
          text: '流程<br>记录',
          id: 'sectionWorkFlow',
          show:!!this.boId
        }
      ]
    }
  },
  created() {
    this.hisId = this.$route.query.hisId
    this.boId = this.$route.query.boId
    this.modStaticSearchParam.rptModId = this.boId
    this.operateType = this.$route.query.operateType
    if (this.boId) {
      this.getModDetailsById()
    } else {
      getDetailsByHisId(this.hisId).then((res) => {
        if (res && res.code == '0000') {
          this.basicForm = res.data
          this.hisStaticSearchParam.projectCode = res.data.projectCode
          this.hisStaticSearchParam.taskCode = res.data.taskCode
          this.hisStaticSearchParam.rptId = this.hisId
          this.hisStaticSearchParam.buildUnit = res.data.buildUnit
        }
      })
    }
  },
  mounted() {
    if (this.operateType == 'edit') {
      this.$refs.workFlow.init()
    } else {
      this.dealPage = false
    }
  },
  methods: {
    save() {
      this.$refs.basicForm.$refs.form.validate((valid) => {
        if (valid) {
          let req = {
            hisId: this.hisId,
            id: this.boId,
            ...this.$refs.basicForm.modelForm
          }
          savePageService(req).then((res) => {
            if (res.code === '0000') {
              this.basicForm = res.data
              this.completeTaskUrl = res.data.completeTaskUrl
              this.boId = res.data.id
              this.modStaticSearchParam.rptModId = this.boId
              this.$closeTagView({
                close: this.$route.fullPath,
                to: `${this.$route.fullPath}&boId=${this.boId}`
              })
              this.$message({
                type: 'success',
                message: '保存成功！'
              })
            }
          })
        }
      })
    },
    getModDetailsById() {
      getModDetailsById(this.boId).then((res) => {
        if (res && res.code == '0000') {
          this.basicForm = res.data
          this.hisStaticSearchParam.projectCode = res.data.projectCode
          this.hisStaticSearchParam.taskCode = res.data.taskCode
          this.hisStaticSearchParam.buildUnit = res.data.buildUnit
          this.completeTaskUrl = res.data.completeTaskUrl
          this.hisId = res.data.hisId
          this.hisStaticSearchParam.rptId = this.hisId
        }
      })
    },
    submit() {
      if (this.submitFlag) {
        // this.$refs.basicForm.$refs.form.validateScroll((valid) => {
        //   if (valid) {
            this.$refs.workFlow.opendialogInitNextPath()
        //   }
        // })
      } else {
        this.$message({
          showClose: true,
          message: '请先导入物资使用修订填报信息',
          type: 'warning'
        })
      }
    },
    // 设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      const workFlowPrams = []
      const name = {
        code: 'name',
        value: this.$refs.basicForm.modelForm.name
      }
      workFlowPrams.push(name)
      return workFlowPrams
    },
    async beforeSubmit() {
      let str = "";
      await this.$refs.basicForm.$refs.form.validateScroll((valid) => {
          str+=valid?'':'有必填字段未填写完整，请检查；'
        })
      let res = await modFlowValidateService({ boId: this.boId });
      if (res.data && res.data.code != 0) {
        str+= res.data.msg;
      }
      return str;
    },
    beforeNode() {
      // 设置默认处理人
      const fileNameArr = {
        qr: {
          userName: this.$refs.basicForm.modelForm.supervisionerName,
          userId: this.$refs.basicForm.modelForm.supervisionerId
        },
        sp: {
          userName: this.$refs.basicForm.modelForm.projectManagerName,
          userId: this.$refs.basicForm.modelForm.projectManagerId
        }
      }
      return fileNameArr
    },
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: '/material/usage_rpt_mod_list'
      })
    },
    //导出
    exportTable1() {
      let req = {
        boId: this.hisId
      }
      commonDown(req, exportTemplateService)
    },
    //导出
    exportTable() {
      commonDown(this.hisStaticSearchParam, exportHisExcelService)
    },
    //导入
    importFile(params) {
      let param = new FormData()
      param.append('file', params.file)
      param.append('boId', this.boId)
      importModExcelService(param).then((res) => {
        if (res.code == '0000' && !res.data) {
          this.$message.success('导入成功')
          this.$refs.usageRptModTable.page.current = 1
          this.$refs.usageRptModTable.getTableData()
        } else if (res.data) {
          this.$message({
            showClose: true,
            message: res.data,
            type: 'warning'
          })
        }
      })
    },
    getNodeData(data) {
      this.nodeName = data.nodeName
      this.nodeCode = data.nodeCode
      this.disableBasicForm = data.nodeCode != 'cg'
    }
  }
}
</script>

<style lang="scss" scoped>
.usageRptEdit {
  .upload-btn {
    display: inline-block;
    margin-right: 10px;
  }
}
</style>
