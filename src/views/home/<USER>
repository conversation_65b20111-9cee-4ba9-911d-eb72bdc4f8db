<template>
  <el-dialog
    class="flowDialog"
    title="流程图"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="80%"
    @close="close"
  >
    <div class="flow-img-wrap">
      <iframe width="100%" height="100%" :src="src" id="iframe"></iframe>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'flowDailog',
  data() {
    return {
      dialogVisible: false,
      src: '',
      instanceId: '',
      businessType: '',
      workflowId: '',
      boId: ''
    }
  },
  mounted() {},
  methods: {
    init(row) {
      this.dialogVisible = true
      this.workflowId = row.workflowId
      this.instanceId = row.instanceId
      this.businessType = row.workflowCode
      if (row.boId) {
        this.boId = row.boId
      } else if (row.url) {
        let ary = row.url.split('?')[1].split('&')
        ary.forEach((item) => {
          if (item.includes('boId=')) {
            this.boId = item.split('=')[1]
          }
        })
      }
      this.getWorkFlowCode()
      // this.$nextTick(() => {
      //   document.getElementById('iframe').onload = () => {
      //     if (this.src) {
      //       window.addEventListener(
      //         'message',
      //         (e) => {
      //           debugger
      //           if (e && typeof e.data === 'number') {
      //             const num = e.data + 100
      //             document.getElementById('iframe').style.height = num + 'px'
      //           }
      //         },
      //         false
      //       )
      //     }
      //   }
      // })
    },
    getWorkFlowCode() {
      let access_token = sessionStorage.getItem('access_token') //需要把token传过去
      this.src =
        window.g.flowPreviewUrl +
        '/preview?id=' +
        this.workflowId +
        (this.instanceId ? `&instanceId=${this.instanceId}` : '') +
        `&boId=${this.boId}&businessType=${this.businessType}&access_token=${access_token}`
    },
    close() {
      this.$emit('closeFlowDialog')
    }
  }
}
</script>
<style scoped lang="scss">
.flow-img-wrap {
  width: 100%;
  height: 70vh;
}
</style>
