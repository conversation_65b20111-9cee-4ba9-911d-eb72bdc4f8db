<!--  首页待办--自定义快捷菜单-->
<template>
  <div class="home_menu_dialog">
    <el-dialog title="自定义" :visible.sync="dialogVisible" width="40%" height="400">
      <el-row>
        <el-col :span="12">
          <el-tree
            :data="treeData"
            show-checkbox
            node-key="id"
            ref="tree"
            check-strictly
            highlight-current
            @check="getCheckedNodes"
            :default-expanded-keys="defaultCheck"
            :default-checked-keys="defaultCheck"
            :props="treeProps"
          ></el-tree>
        </el-col>
        <el-col :span="12">
          <el-table :data="tableData" height="400" border style="width: 100%">
            <el-table-column label="序号" type="index" width="50" align="center">
              <template slot-scope="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column prop="label" label="名称" align="center"></el-table-column>
            <el-table-column prop="handel" label="操作" align="center"  width="50">
              <template slot-scope="scope">
                <i class="el-icon-delete del" title="删除" @click="del(scope.row)"></i>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="clear" size="mini">清空</el-button>
        <el-button type="primary" @click="save" size="mini">保存</el-button>
      </span>
    </el-dialog>
    <!-- 二次提示框 -->
    <el-dialog title="提示" :visible.sync="dialogVisible2" width="20%" top="30vh">
      <div>将清空自定义导航，请确认是否继续操作！</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false" size="mini">取消</el-button>
        <el-button type="primary" @click="sureClear" size="mini">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getUserMenuService, saveShortCutService } from '@/api/user.js'
export default {
  name: '',
  data() {
    return {
      dialogVisible: false,
      treeData: [],
      treeProps: {
        children: 'children',
        label: 'label',
        disabled: 'hasChlid'
      },
      defaultCheck: [],
      tableData: [],
      dialogVisible2: false
    }
  },
  created() {
    let that = this
    getUserMenuService({
      type: '0',
      queryAll: true,
      rootId: 'root-jx-fz'
    }).then((res) => {
      if (res.data && res.code == '0000') {
        that.treeData = res.data
      }
    })
  },
  methods: {
    // 初始化
    init(data) {
      this.dialogVisible = true
      this.tableData = []
      this.defaultCheck = []
      data.forEach((item) => {
        this.tableData.push({
          id: item.menuId,
          label: item.menuName
        })
        this.defaultCheck.push(item.menuId)
      })
    },
    getCheckedNodes(curNode, checkNode) {
      let arr = this.tableData.filter((item) => item.id != curNode.id)
      if (arr.length == this.tableData.length) {
        this.tableData.push(curNode)
      } else {
        this.tableData = JSON.parse(JSON.stringify(arr))
      }
    },
    // 清空
    clear() {
      this.dialogVisible2 = true
    },
    del(row) {
      this.tableData.forEach((item,index)=>{
        if(row.id == item.id){
          this.tableData.splice(index,1)
        }
      })
      this.defaultCheck.forEach((item,index)=>{
        if(row.id == item){
          this.defaultCheck.splice(index,1)
        }
      })
      this.$refs.tree.setCheckedKeys(this.defaultCheck)
    },
    sureClear() {
      this.tableData = []
      this.$refs.tree.setCheckedKeys([])
      this.dialogVisible2 = false
    },
    // 保存
    save() {
      this.dialogVisible = false
      let arr = []
      this.tableData.forEach((item) => {
        arr.push(item.id)
      })
      saveShortCutService({ menus: arr.join(','), typeId: 1 }).then((res) => {
        if (res.data && res.code == '0000') {
          this.$message.success('设置成功')
          this.$emit('refresh')
        }
      })
    }
  }
}
</script>
<style lang="scss">
.home_menu_dialog {
  .el-row {
    height: 400px;
  }
  .el-tree {
    max-height: 400px;
    overflow: auto;
    .el-tree-node > .el-tree-node__content > .el-checkbox {
      display: none;
    }
    .el-tree-node__children
      > .el-tree-node
      > .el-tree-node__content
      > .el-checkbox {
      display: block;
    }
  }
  .del {
    cursor: pointer;
    color: #02a7f0;
  }
}
</style>
