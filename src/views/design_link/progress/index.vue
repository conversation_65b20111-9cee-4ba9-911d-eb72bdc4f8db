<!-- 进度查询 -->
<template>
  <div class="design_progress">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="进度查询">
      <div slot="headerBtn">
        <el-button type="primary" @click="downLoad">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          serial
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService, downloadService} from "@/api/design_link/progress_api";
import { commonDown } from "@/utils/btn"
export default {
  name: "design_progress",
  data() {
    return {
      formConfig: [
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "任务名称",
          type: "input",
          fieldName: "taskName",
        },
        {
          label: "任务编码",
          type: "input",
          fieldName: "taskCode",
        },
        {
          label: "创建日期",
          type: "daterange",
          format:'yyyy-MM-dd',
          valueFormat:'yyyy-MM-dd',
          fieldName: "createTime",
        },
      ],
      tableHeader: [
        {
          prop: "designType",
          label: "任务类型",
          align: "center",
          formatter: (row) => {
            return (
              <span
                class="table_btn"
                onClick={() => {
                  this.toDetail(row);
                }}
              >
                {row.designType}
              </span>
            );
          }
        },
        // {
        //   prop: "entityName",
        //   label: "任务名称",
        //   align: "center",
        // },
        // {
        //   prop: "entityCode",
        //   label: "任务编码",
        //   align: "center",
        // },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "designDeptName",
          label: "设计单位",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "创建时间",
          align: "center",
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
        },
      ],
      tableApi: queryListService,
      loadParam: {},
      staticSearchParam: {},
    };
  },
  mounted() {},
  methods: {
    // 查询
    search(form) {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(JSON.stringify(form))
      if(obj.createTime){
        obj.createDate=obj.createTime[0]
        obj.finishDate=obj.createTime[1]
      }
      delete obj.createTime
      this.staticSearchParam=obj
      this.loadParam=obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj);
      })

    },

    // 重置
    reset(form) {
      form.createTime=["",""]
      this.search(form);
    },

    downLoad() {
      commonDown({ ...this.loadParam }, downloadService)
    },

    toDetail(row){
      if(row.designType=='设计费确认'){
        this.$router.push({
          path: `/design_link/fee_confirm/${row.isAllowOperate?'edit':'view'}`,
          query: { boId: row.boId ,returnUrl:'/design_link/progress'},
        })
      }else{
        this.$router.push({
          path: `/design_link/fee_modify/${row.isAllowOperate?'edit':'view'}`,
          query: { boId: row.boId ,returnUrl:'/design_link/progress'},
        })
      }
    }
  },
};
</script>
