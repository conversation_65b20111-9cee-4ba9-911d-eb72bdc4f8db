<!-- 设计费确认 -->
<template>
  <div class="design_fee_confirm">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="设计费确认">
      <!-- <div slot="headerBtn">
        <el-button type="primary" @click="downLoad">导出</el-button>
      </div> -->
      <div slot="content">
        <mssTable
          ref="table"
          serial
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import {queryListService,downloadService,delService} from '@/api/design_link/fee_confirm_api'
import { commonDown,commonOneDel } from "@/utils/btn"
export default {
  name: "design_fee_confirm",
  data() {
    return {
      formConfig: [
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "设计通用合同编号",
          type: "input",
          fieldName: "contractCode",
        },
        {
          label: "创建日期",
          type: "daterange",
          format:'yyyy-MM-dd',
          valueFormat:'yyyy-MM-dd',
          fieldName: "createTime",
        },
        {
          label: "流程状态",
          type: "select",
          option:[],
          fieldName: "status",
        },
      ],
      tableHeader: [
        {
          prop: "entityCode",
          label: "任务单编号",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "entityName",
          label: "任务单名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "designDeptName",
          label: "设计单位",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "designFee",
          label: "设计费金额（元）",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "createDate",
          label: "创建时间",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "status",
          label: "状态",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          minWidth: 120,
          fixed:'right',
          formatter: (row) => {
            return (
              <span>
                {row.isAllowOperate ? (
                  <span
                    class="table_btn mr10"
                    onClick={() => {this.action(row, "edit")}}
                  >
                    处理
                  </span>
                ) : (
                  ""
                )}
                <span
                  class="table_btn mr10"
                  onClick={() => {this.action(row, "view")}}
                >
                  查看
                </span>
                {row.isAllowDelete ? (
                  <span
                    class="table_btn "
                    onClick={() => {this.action(row, "del")}}
                  >
                    删除
                  </span>
                ) : (
                  ""
                )}
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      loadParam: {},
      staticSearchParam:{}
    };
  },
  mounted() {
    this.getStatusList()
  },
  methods: {
    async getStatusList() {
      // 下标11是固定的查询条件，是流程状态，如果更改了查询条件，记得这里也要改
      this.$set(this.formConfig[4], 'options', await this.$dictOptions({ parentValue: 'wrokStatusAll', appCode: 'PUB'}))
    },
    // 查询
    search(form) {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(JSON.stringify(form))
      if(obj.createTime){
        obj.createDate=obj.createTime[0]
        obj.finishDate=obj.createTime[1]
      }
      delete obj.createTime
      this.staticSearchParam=obj
      this.loadParam=obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj);
      })
    },

    // 重置
    reset(form) {
      form.createTime=["",""]
      this.search(form);
    },

    // 导出
    downLoad(){
      commonDown({ ...this.loadParam }, downloadService)
    },
    // 处理
    action(row, type) {
      switch (type) {
        case "edit":
          this.$router.push({
            path: "/design_link/fee_confirm/edit",
            query: { boId: row.boId },
          });
          break;
        case "view":
          this.$router.push({
            path: "/design_link/fee_confirm/view",
            query: { boId: row.boId },
          });
          break;
        case "del":
          commonOneDel.call(this,row.boId, delService, (res) => {
            if(res.code=='0000'){
              this.$message.success('删除成功')
              this.search()
            }
          })
          break;
      }
    },

  },
};
</script>
