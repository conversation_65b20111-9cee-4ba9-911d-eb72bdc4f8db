<!-- 施工分派查看页面 -->
<template>
  <div class="fee_confirm_look page-anchor-parentpage">
    <div class="operate-btn">
      <el-button @click="goBack">返回</el-button>
    </div>

    <infos labelPosition="left"></infos>
  </div>
</template>

<script>
import infos from './infos.vue'
export default {
  name: 'fee_confirm_look',
  components:{infos},
  data() {
    return {

    }
  },
  created() {
  },
  methods: {
    goBack(){
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.$route.query.returnUrl||'/design_link/fee_confirm'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
