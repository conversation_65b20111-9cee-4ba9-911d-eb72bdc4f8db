<!-- 设计费人工确认 -->
<template>
  <div class="design_fee_confirm_person">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="设计费确认人工申请">
      <div slot="content">
        <mssTable
          ref="table"
          serial
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService,confirmService } from "@/api/design_link/fee_confirm_person_api";
export default {
  name: "design_fee_confirm_person",
  data() {
    return {
      formConfig: [
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        // {
        //   label: "任务名称",
        //   type: "input",
        //   fieldName: "taskName",
        // },
        // {
        //   label: "任务编码",
        //   type: "input",
        //   fieldName: "taskCode",
        // },
        // {
        //   label: "创建日期",
        //   type: "daterange",
        //   format:'yyyy-MM-dd',
        //   valueFormat:'yyyy-MM-dd',
        //   fieldName: "createTime",
        // },
      ],
      tableHeader: [
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "constructOrgName",
          label: "建设单位",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        // {
        //   prop: "createDate",
        //   label: "创建时间",
        //   align: "center",
        //   width: 130,
        // },
        // {
        //   prop: "status",
        //   label: "状态",
        //   align: "center",
        //   width: 130,
        // },
        {
          prop: "operate",
          label: "操作",
          align: "center",
          width: 130,
          formatter: (row) => {
            return (
              <span
                class="table_btn"
                onClick={() => {
                  this.action(row);
                }}
              >
                发起设计费确认
              </span>
            );
          },
        },
      ],
      tableApi: queryListService,
      staticSearchParam: {},
    };
  },
  mounted() {},
  methods: {
    search(form) {
      this.$refs.table.page.current = 1
      this.staticSearchParam=JSON.parse(
        JSON.stringify(form)
      )
      this.loadParam = JSON.parse(
        JSON.stringify(form)
      );
      this.$refs.table.getTableData(form);
    },

    // 重置
    reset(form) {
      this.search(form);
    },
    // 处理
    action(row, type) {
      confirmService({...row,id:''}).then(res=>{
        if(res.code=='0000'){
          this.$message.success('发起成功')
          this.$router.push({
            path: "/design_link/fee_confirm/edit",
            query: { boId: res.data},
          });
        }
      })
    },
  },
};
</script>
