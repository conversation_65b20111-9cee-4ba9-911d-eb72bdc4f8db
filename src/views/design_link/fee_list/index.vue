<!-- 设计费报表统计 -->
<template>
  <div class="design_fee_list">
    <mssSearchForm
      ref="searchForm"
      :searchConfig="formConfig"
      @search="search"
      @reset="reset"
    ></mssSearchForm>
    <mssCard title="报表统计">
      <div slot="headerBtn">
        <el-button type="primary" @click="downLoad">导出</el-button>
      </div>
      <div slot="content">
        <mssTable
          ref="table"
          serial
          :api="tableApi"
          :columns="tableHeader"
          :staticSearchParam="staticSearchParam"
        >
        </mssTable>
      </div>
    </mssCard>
  </div>
</template>

<script>
import { queryListService,downloadService } from "@/api/design_link/fee_list_api";
import { commonDown } from "@/utils/btn"
export default {
  name: "design_fee_list",
  data() {
    return {
      formConfig: [
        {
          label: "项目名称",
          type: "input",
          fieldName: "projectName",
        },
        {
          label: "项目编码",
          type: "input",
          fieldName: "projectCode",
        },
        {
          label: "任务名称",
          type: "input",
          fieldName: "taskName",
        },
        {
          label: "任务编码",
          type: "input",
          fieldName: "taskCode",
        },
        {
          label: "设计单位",
          type: "input",
          fieldName: "designDeptName",
        },
        {
          label: "创建日期",
          type: "daterange",
          format:'yyyy-MM-dd',
          valueFormat:'yyyy-MM-dd',
          fieldName: "createTime",
        },
      ],
      tableHeader: [
        {
          prop: "designDeptName",
          label: "设计单位",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "designFee",
          label: "确认前设计费(元)(不含税)",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "confirmDesignFee",
          label: "确认设计费(元)(不含税)",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
          minWidth: 150,
          tooltip:true
        },
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
          minWidth: 100,
          tooltip:true
        },
        {
          prop: "tasmNum",
          label: "任务数",
          align: "center",
          minWidth: 100,
          tooltip:true
        },
        {
          prop: "createDate", // todo
          label: "发起时间",
          align: "center",
        },
        {
          prop: "finishDate",// todo
          label: "完成时间",
          align: "center",
        },
        {
          prop: "processingDate",// todo
          label: "处理时长",
          align: "center",
        },
      ],
      tableApi: queryListService,
      loadParam: {},
      staticSearchParam: {},
    };
  },
  mounted() {},
  methods: {
    // 查询
    search(form) {
      this.$refs.table.page.current = 1
      let obj=JSON.parse(JSON.stringify(form))
      if(obj.createTime){
        obj.createDate=obj.createTime[0]
        obj.finishDate=obj.createTime[1]
      }
      delete obj.createTime
      this.staticSearchParam=obj
      this.loadParam=obj
      this.$nextTick(()=>{
        this.$refs.table.getTableData(obj);
      })


    },

    // 重置
    reset(form) {
      form.createTime=["",""]
      this.search(form);
    },

    downLoad() {
      commonDown({ ...this.loadParam }, downloadService)
    },

  },
};
</script>
