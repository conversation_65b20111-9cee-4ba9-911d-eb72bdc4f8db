<!-- 公共信息（处理与查看公用） -->
<template>
  <div class="design_link_info">
    <div id="sectionBasic" class="page-anchor-point"></div>
    <mssCard title="基本信息">
      <div slot="content">
        <mssCard title="立项信息">
          <div slot="content">
            <mssForm
              labelWidth="200px"
              :config="baseConfig1"
              labelPosition="left"
              :form="baseForm"
              :disableForm="true"
            ></mssForm>
          </div>
        </mssCard>
        <mssCard title="设计批复基本信息">
          <div slot="content">
            <mssForm
              labelWidth="200px"
              :config="baseConfig2"
              labelPosition="left"
              :form="baseForm"
              :disableForm="true"
            ></mssForm>
          </div>
        </mssCard>
        <mssCard title="设计批复内容">
          <div slot="content">
            <mssForm
              labelWidth="200px"
              :config="baseConfig3"
              labelPosition="left"
              :form="baseForm"
              :disableForm="true"
            ></mssForm>
          </div>
        </mssCard>
      </div>
    </mssCard>

    <div id="sectionList" class="page-anchor-point"></div>
    <mssCard title="费用信息">
      <div slot="content">
        <mssCard title="项目设计费用信息(确认前)">
          <div slot="content">
            <mssTable
              serial
              :stationary="stationary1"
              :columns="tableHeader1"
              :pagination="false"
            >
            </mssTable>
            <div style="margin: 10px 0;font-size:12px">说明：数据取自集团PMS系统</div>
          </div>
        </mssCard>
        <mssCard title="项目设计费用确认信息">
          <div slot="content">
              <mssTable
                serial
                :stationary="stationary2"
                :columns="tableHeader11"
                :pagination="false"
              >
              </mssTable>
              <div style="margin: 10px 0;font-size:12px">说明：核减后的设计费取自导入的‘任务设计费用确认明细信息’栏数据的汇总</div>
          </div>
        </mssCard>
        <mssCard title="项目设计费用变更信息">
          <div slot="content">
            <el-form ref="projectTabelform" :model="basicForm" :rules="tableRules">
              <mssTable
                serial
                :stationary="stationary3"
                :columns="tableHeader12"
                :pagination="false"
              >
              </mssTable>
            </el-form>
            <div style="margin: 10px 0;font-size:12px">说明：核减后的设计费取自导入的‘任务设计费用变更明细信息’栏数据的汇总</div>
          </div>
        </mssCard>
        <mssCard title="任务设计费用明细信息(确认前)">
          <div slot="headerBtn">
            <el-button type="primary" @click="downLoad(1)">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              serial
              :api="tableTaskApi"
              :staticSearchParam="TaskParam1"
              :columns="tableHeader2"
            >
            </mssTable>
          </div>
        </mssCard>
        <mssCard title="任务设计费用确认明细信息">
          <div slot="headerBtn">
            <el-button type="primary" @click="downLoad(2)">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              serial
              :api="tableTaskApi"
              :staticSearchParam="TaskParam2"
              :columns="tableHeader3"
            >
            </mssTable>
          </div>
        </mssCard>
        <mssCard title="任务设计费用变更明细信息">
          <div slot="headerBtn" >
            <el-upload
              v-if="isEdit"
              class="upload-btn"
              action="string"
              ref="newFile"
              :show-file-list="false"
              :auto-upload="true"
              :http-request="importFile"

            >
              <el-button type="primary">导入</el-button>
            </el-upload>
            <el-button v-else type="primary" @click="downLoad(3)">导出</el-button>
          </div>
          <div slot="content">
            <mssTable
              ref="confirmTable"
              serial
              :api="tableTaskApi"
              :staticSearchParam="TaskParam3"
              :columns="tableHeader3"
              :cell-class-name="cellClassName"
            >
            </mssTable>
          </div>
        </mssCard>
      </div>
    </mssCard>

    <div id="sectionContract" class="page-anchor-point"></div>
    <mssCard title="设计通用合同信息">
      <div slot="content">
        <mssForm
          ref="basicForm"
          labelWidth="200px"
          :config="basicConfig"
          :labelPosition="labelPosition"
          :form="basicForm"
          :disableForm="disableForm"
          :rules="formRules"
        ></mssForm>
      </div>
    </mssCard>
    <div id="sectionAttachment" class="page-anchor-point"></div>
    <mssAttachment
      v-if="businessType"
      :boId="boId"
      :businessType="businessType"
      :nodeName="nodeName"
      :dealPage="dealPage"
      :fileTypeFlag="true"
    ></mssAttachment>

    <div id="sectionOperation" class="page-anchor-point"></div>
    <mssCard title="流程记录">
      <div slot="content">
        <mssWorkFlowHistory
          v-if="businessType"
          :boId="boId"
          :workflowCode="businessType"
        ></mssWorkFlowHistory>
      </div>
    </mssCard>

    <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
    <mssTableSearchDialog
      ref="projectNameSearchDialog"
      dialog-width="70%"
      :table-api="projectTableApi"
      :search-fields="searchFieldList"
      :tableColumns="projectColumns"
      @confirm="projectConfirm"
      dialogTitle="选择设计通用合同"
      rowKey="contractDeptId"
      :tableQueryParams="tableQueryParams"
      :tableSingleChoice="true"
    ></mssTableSearchDialog>
  </div>
</template>

<script>
import {
  queryDetailService,
  queryProjectInfoService,
  exportTaskService,
  queryTaskInfoService,
  getContrctService,
  importService
} from "@/api/design_link/fee_modify_api";
import { commonDown } from "@/utils/btn"
export default {
  name: "design_link_info",
  props: {
    labelPosition: {
      type: String,
      default: "top",
    },
    formRules: {
      type: Object,
      default: () => {
        return {}
      },
    },
    disableForm:{
      type: Boolean,
      default: false,
    }
  },
  watch:{
    'basicForm.totalDeduction': {
        handler: function (newValue, oldValue) {
          if(newValue){
            this.stationary3[0].totalFeeOne=(this.stationary3[0].designFee-newValue).toFixed(2)
          }
        },
        deep: true
      },
    disableForm:{
      handler: function (newValue, oldValue) {
        this.isEdit=!newValue
      },
        deep: true
    }
  },
  data() {
    return {
      isEdit:true,
      boId: "",
      businessType: "",
      nodeName: "草稿",
      dealPage:false,
      baseConfig1: [
        {
          label: "项目名称",
          type: "input",
          prop: "projectName",
          span: 12,
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCode",
          span: 12,
        },
        {
          label: "立项模式",
          type: "input",
          prop: "feaMode",
          span: 12,
        },
        {
          label: "立项决策金额（万元）",
          type: "input",
          prop: "feaDecisionAmount",
          span: 12,
        },
        {
          label: "项目创建日期",
          type: "input",
          prop: "projectCreatDate",
          span: 12,
        },
        {
          label: "项目集投资余额（万元）",
          type: "input",
          prop: "projectInvest",
          span: 12,
        },
        {
          label: "项目决策日期",
          type: "input",
          prop: "projectDecisionDate",
          span: 12,
        },
        {
          label: "建设类型",
          type: "input",
          prop: "constructType",
          span: 12,
        }
      ],
      baseConfig2: [
        {
          label: "设计批复单号",
          type: "input",
          prop: "entityCode",
          span: 12,
        },
        {
          label: "设计批复名称",
          type: "input",
          prop: "entityName",
          span: 12,
        },
        {
          label: "项目编码",
          type: "input",
          prop: "projectCodeDesign",
          span: 12,
        },
        {
          label: "项目名称",
          type: "input",
          prop: "projectNameDesign",
          span: 12,
        },
        {
          label: "投资主体",
          type: "input",
          prop: "investMain",
          span: 12,
        },
        {
          label: "立项批复金额（万元）",
          type: "input",
          prop: "feaReplyInvest",
          span: 12,
        },
        {
          label: "创建人",
          type: "input",
          prop: "entityCreatorName",
          span: 12,
        },
        {
          label: "创建时间",
          type: "input",
          prop: "entityCreateDate",
          span: 12,
        },
        {
          label: "发起单位",
          type: "input",
          prop: "entityCreateOrg",
          span: 12,
        },
        {
          label: "是否最后一次批复",
          type: "input",
          prop: "isLastReply",
          span: 12,
        },
        {
          label: "状态",
          type: "input",
          prop: "status",
          span: 12,
        }
      ],
      baseConfig3: [
        {
          label: "批复类型",
          type: "input",
          prop: "replyType",
          span: 12,
        },
        {
          label: "设计批复模式",
          type: "input",
          prop: "replyMode",
          span: 12,
        },
        {
          label: "已批复金额（万元）",
          type: "input",
          prop: "approvedInvest",
          span: 12,
        },
        {
          label: "本次设计会审总金额（万元）",
          type: "input",
          prop: "thisReplyInvest",
          span: 12,
        },
        {
          label: "本次设计批复总金额（万元）",
          type: "input",
          prop: "thisTotalInvest",
          span: 12,
        },
        {
          label: "设计批复日期",
          type: "input",
          prop: "designReplyDate",
          span: 12,
        },
        {
          label: "设计批复文号",
          type: "input",
          prop: "designOaCode",
          span: 12,
        },
        {
          label: "建设规模",
          type: "input",
          prop: "buildMode",
          span: 12,
        }
      ],
      baseForm: {},
      tableHeader1:[
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
        },
        {
          prop: "constructOrgName",
          label: "建设单位",
          align: "center",
        },
        // {
        //   prop: "feasFee",
        //   label: "可研费(元)(不含税)",
        //   align: "center",
        // },
        // {
        //   prop: "surveyFee",
        //   label: "勘察费(元)(不含税)",
        //   align: "center",
        // },
        {
          prop: "designFee",
          label: "设计费(元)(不含税)",
          align: "center",
        },
        // {
        //   prop: "totalFee",
        //   label: "合计(可研费+勘察费+设计费)(不含税)",
        //   align: "center",
        // },
        {
          prop: "totalDeduction",
          label: "汇总核减(元)(不含税)",
          align: "center",
        },
        {
          prop: "totalFeeOne",
          label: "合计(元)(不含税)",
          align: "center",
        },
      ],
      tableHeader11:[
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
        },
        {
          prop: "constructOrgName",
          label: "建设单位",
          align: "center",
        },
        // {
        //   prop: "feasFee",
        //   label: "可研费(元)(不含税)",
        //   align: "center",
        // },
        // {
        //   prop: "surveyFee",
        //   label: "勘察费(元)(不含税)",
        //   align: "center",
        // },
        {
          prop: "designFee",
          label: "核减后的设计费(元)(不含税)",
          align: "center",
        },
        // {
        //   prop: "totalFee",
        //   label: "合计(可研费+勘察费+设计费)(不含税)",
        //   align: "center",
        // },
        {
          prop: "totalDeduction",
          label: "其他核减(元)(不含税)",
          align: "center",
        },
        {
          prop: "totalFeeOne",
          label: "合计(元)(不含税)",
          align: "center",
        },
      ],
      tableHeader12:[
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
        },
        {
          prop: "constructOrgName",
          label: "建设单位",
          align: "center",
        },
        // {
        //   prop: "feasFee",
        //   label: "可研费(元)(不含税)",
        //   align: "center",
        // },
        // {
        //   prop: "surveyFee",
        //   label: "勘察费(元)(不含税)",
        //   align: "center",
        // },
        {
          prop: "designFee",
          label: "核减后的设计费(元)(不含税)",
          align: "center",
        },
        // {
        //   prop: "totalFee",
        //   label: "合计(可研费+勘察费+设计费)(不含税)",
        //   align: "center",
        // },
        {
          prop: "totalDeduction",
          label: "其他核减(元)(不含税)",
          align: "center",
          formatter: (row) => {
            if(this.isEdit){
              return (
                <el-form-item prop="totalDeduction" label="totalDeduction" class="fee-form_edit">
                  <el-input v-model={row.totalDeduction} type="number" onChange={(value)=>{this.basicForm[`totalDeduction`]=value}}>
                    </el-input>
                </el-form-item>
              );
            }else{
              return (
                <span>{row.totalDeduction}</span>
              );
            }
          }
        },
        {
          prop: "totalFeeOne",
          label: "合计(元)(不含税)",
          align: "center",
        },
      ],
      tableHeader2:[
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
        },
        {
          prop: "taskCode",
          label: "任务编码",
          align: "center",
        },
        {
          prop: "taskName",
          label: "任务名称",
          align: "center",
        },
        // {
        //   prop: "feasFee",
        //   label: "可研费(元)(不含税)",
        //   align: "center",
        // },
        // {
        //   prop: "surveyFee",
        //   label: "勘察费(元)(不含税)",
        //   align: "center",
        // }, // todo:需求临时说暂时取消，娶不到值
        {
          prop: "designFee",
          label: "设计费(元)(不含税)",
          align: "center",
        },
        // {
        //   prop: "totalFee",
        //   label: "合计(元)(不含税)",
        //   align: "center",
        // },
      ],
      tableHeader3:[
        {
          prop: "projectCode",
          label: "项目编码",
          align: "center",
        },
        {
          prop: "projectName",
          label: "项目名称",
          align: "center",
        },
        {
          prop: "taskCode",
          label: "任务编码",
          align: "center",
        },
        {
          prop: "taskName",
          label: "任务名称",
          align: "center",
        },
        {
          prop: "designFee",
          label: "确认前设计费(元)(不含税)",
          align: "center",
        },
        {
          prop: "confirmDesignFee",
          label: "确认设计费(元)(不含税)",
          align: "center",
        },
      ],
      stationary1:[],
      stationary2:[],
      stationary3:[],
      pageAnchorConfig: [
        {
          text: "基本<br>信息",
          id: "sectionBasic",
        },
        {
          text: "费用<br>信息",
          id: "sectionList",
        },
        {
          text: "合同<br>信息",
          id: "sectionContract",
        },
        {
          text: "附件<br>列表",
          id: "sectionAttachment",
        },
        {
          text: "流程<br>记录",
          id: "sectionOperation",
        },
      ],
      basicConfig:[
        {
          label: "设计通用合同名称",
          type: "input",
          prop: "contractName",
          span: 12,
          readonly:true,
          eventListeners: {
            focus: this.openContrctDialog, // 打开选择项目的弹窗
          },
        },
        {
          label: "设计通用合同编码",
          type: "input",
          prop: "contractCode",
          readonly:true,
          span: 12,
          eventListeners: {
            focus: this.openContrctDialog, // 打开选择项目的弹窗
          },
        },
        {
          label: "设计单位名称",
          type: "input",
          prop: "designDeptName",
          span: 24,
        },
        {
          label: "拟稿人",
          type: "input",
          prop: "creatorName",
          span: 12,
        },
        {
          label: "拟稿时间",
          type: "input",
          prop: "createDate",
          span: 12,
        },
        {
          label: "费用差异说明",
          type: "input",
          mode:'textarea',
          prop: "feeVarianceDesc",
          span: 24,
        },
      ],
      basicForm:{
        totalDeduction:''
      },
      tableRules:{
        totalDeduction:{ required: true, message: '该字段不能为空', trigger: 'blur' }
      },
      projectTableApi: getContrctService,
      searchFieldList: [
        {
          label: "合同名称",
          fieldName: "contractName",
        },
        {
          label: "合同编号",
          fieldName: "contractCode",
        },
      ],
      projectColumns: [
        {
          label: "合同名称",
          prop: "contractName",
          tooltip:true,
        },
        {
          label: "合同编码",
          prop: "contractCode",
        },
        // INFO---皮旭0904要求删掉
        // {
        //   label: "项目编码",
        //   prop: "projectCode",
        // },
        {
          label: "设计单位",
          prop: "designDeptName",
        },
      ],
      tableQueryParams:{},
      tableTaskApi:queryTaskInfoService,
      TaskParam1:{},
      TaskParam2:{},
      TaskParam3:{}
    };
  },
  async created() {
    if (this.labelPosition == "left") {
      this.isEdit=false
      for (let i in this.basicConfig) {
        this.basicConfig[i].label = `${this.basicConfig[i].label}：`;
      }
    } else {
      this.dealPage=true
    }
    for (let i in this.baseConfig1) {
      this.baseConfig1[i].label = `${this.baseConfig1[i].label}：`;
    }
    for (let i in this.baseConfig2) {
      this.baseConfig2[i].label = `${this.baseConfig2[i].label}：`;
    }
    for (let i in this.baseConfig3) {
      this.baseConfig3[i].label = `${this.baseConfig3[i].label}：`;
    }
    // 浏览器传参
    this.boId = this.$route.query.boId;
    this.getDetailsData();
    this.getProjectInfo(1)
    this.getProjectInfo(2)
    this.getProjectInfo(3)
    this.TaskParam1={
      boId:this.boId,
      sourceType:1
    }
    this.TaskParam2={
      boId:this.boId,
      sourceType:2
    }
    this.TaskParam3={
      boId:this.boId,
      sourceType:3
    }
  },
  methods: {
    getDetailsData() {
      queryDetailService({ boId: this.boId }).then((res) => {
        this.baseForm = res.data;
        this.basicForm={...this.basicForm,...res.data};
        this.businessType=res.data.businessType
        this.$emit("getWorkCode",this.businessType)
        this.tableQueryParams={
          projectCode:res.data.projectCode,
          designReplyId:res.data.designReplyId
        }
      });
    },
    getProjectInfo(type){
      queryProjectInfoService({
      boId:this.boId,
      sourceType:type
    }).then(res=>{
        if(res.data){
          switch(type){
            case 1:this.stationary1=[{...res.data}]
            break
            case 2:this.stationary2=[{...res.data}]
            break
            case 3:this.stationary3=[{...res.data}]
            if(this.stationary3[0]){
              this.basicForm.totalDeduction=this.stationary3[0].totalDeduction
            }
            break
          }
        }
      })
    },
    // 导出
    downLoad(key){
      commonDown({ boId:this.boId,sourceType:key}, exportTaskService)
    },

    // 打开选合同的弹出框
    openContrctDialog(){
      this.$refs.projectNameSearchDialog.openDialog();
    },
    // 合同确认回调
    projectConfirm(data){
      if(data.length){
        this.basicForm={...this.basicForm,...data[0],
          designUserName:data[0].designUserName||'',
          designUserId:data[0].designUserId||''
        }
      }
    },

    cellClassName({row,column}){
      return ['taskCode','taskName','designFee','confirmDesignFee'].includes(column.property)&& row.isRead=='1'?'red':''
    },

    // 导入
    importFile(params) {
      let param = new FormData();
      param.append("file", params.file)
      param.append("boId", this.boId)
      importService(param).then((res) => {
        if (res.code == "0000") {
          this.$message.success("导入成功");
          this.$refs.confirmTable.getTableData()
          this.getProjectInfo(3)
        }
      });
    },
  },
};
</script>

<style lang="scss" >
.fee-form_edit{
  margin-bottom: 0 ;
  .el-form-item__label {
        visibility: hidden;
        height: 0;
  }
}
.fee-form_edit.is-error{
  margin-bottom: 22px;
}
.design_link_info{
  .upload-btn {
    display: inline-block;
    margin-right: 10px;
  }
  .red{
    color: red;
  }
}

</style>
