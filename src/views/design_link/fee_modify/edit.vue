<!-- 确认费变更处理页面 -->
<template>
  <div class="fee_modify_detail page-anchor-parentpage">
    <div class="operate-btn">
      <el-button type="primary" @click="submit()">提交</el-button>
      <el-button type="primary" @click="save()" v-if="!disableForm">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
    <mssWorkFlowHandel
      ref="workFlow"
      :boId="boId"
      :workflowCode="workflowCode"
      :actionUrl="completeTaskUrl"
      :returnAddress="returnAddress"
      @initFormRules="initFormRules"
      @getReadonlyList="getReadonlyList"
      @getNodeData="getNodeData"
    ></mssWorkFlowHandel>
    <infos
      labelPosition="top"
      ref="infos"
      :formRules="formRules"
      :disableForm="disableForm"
      @getWorkCode="getWorkCode"
    ></infos>
  </div>
</template>

<script>
import infos from "./infos.vue";
import {
  saveService,
  beforeSubmitService,
} from "@/api/design_link/fee_modify_api";
export default {
  name: "fee_modify_detail",
  components: { infos },
  data() {
    return {
      boId: "",
      workflowCode: "",
      completeTaskUrl: "", //流程处理地址
      returnAddress: "/design_link/fee_modify", //流程提交成功返回路由地址
      formRules: {},
      disableForm:false,
    };
  },
  created() {
    this.boId = this.$route.query.boId;
  },
  methods: {
    getWorkCode(param) {
      this.workflowCode = param;
      this.$nextTick(() => {
        this.$refs.workFlow.init();
      });
    },
    // 返回上一页
    goBack() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.$route.query.returnUrl||"/design_link/fee_modify",
      });
    },
    getNodeData(data) {
      this.nodeCode = data.nodeCode || ''
      // 草稿才可以编辑
      if (this.nodeCode === 'draft' || !this.nodeCode) {
        this.disableForm = false
      } else {
        this.disableForm = true
      }
    },
    // 保存
    save(cb) {
      if(this.validate().flag1 && this.validate().flag2){
        // 处理数据
          let param = {
            ...this.$refs.infos.$refs.basicForm.modelForm,
            totalDeduction:this.$refs.infos.stationary3.length?this.$refs.infos.stationary3[0].totalDeduction:''
          };
          saveService(param).then((res) => {
            if (res.code == "0000") {
              if (cb) {
                cb(param);
              } else {
                this.$message.success("保存成功");
              }
            }
          });
      }
    },
    // 判断校验必填
    validate(){
      let obj={
        flag1:false,
        flag2:false
      }
      if(!this.$refs.infos.stationary3.length){
        this.$message.warning('请至少导入一条任务信息')
      }else{
        this.$refs.infos.$refs.projectTabelform.validateScroll((valid) => {
          if (valid) {
            obj.flag1=true
          }
        });
      }
      this.$refs.infos.$refs.basicForm.$refs.form.validateScroll((valid) => {
        if (valid) {
          obj.flag2=true
        }
      });
      return obj
    },
    // 提交--先保存后提交
    submit() {
      // if(this.validate().flag1 && this.validate().flag2){
        this.completeTaskUrl = this.$refs.infos.basicForm.completeTaskUrl;
        this.$refs.workFlow.opendialogInitNextPath();
      // }
    },
    //设置流程的业务参数和流程参数
    setWorkFlowPrams() {
      let workFlowPrams = [];
      let name = {
        code: "name",
        value: this.$refs.infos.$refs.basicForm.modelForm.name,
      };
      workFlowPrams.push(name);
      return workFlowPrams;
    },
    async beforeSubmit() {
      if(!this.validate().flag1 ||!this.validate().flag2){
        return '有必填字段未填写完整，请检查'
      }
      let str = "";
      let res = await beforeSubmitService({ boId: this.boId });
      if (res.data && res.data.code != 0) {
        str = res.data.msg;
      }
      return str;
    },

    beforeNode() {
      const fileNameArr = {
        designConfirm:{ userName: this.$refs.infos.basicForm.designUserName, userId: this.$refs.infos.basicForm.designUserId },
        ceatorConfirm:{userName: this.$refs.infos.basicForm.creatorName, userId: this.$refs.infos.basicForm.creatorId},
        dsbmld:{ userName: this.$refs.infos.basicForm.deptManagerName, userId: this.$refs.infos.basicForm.deptManagerId },
        dsfgld:{ userName: this.$refs.infos.basicForm.supervisorName, userId: this.$refs.infos.basicForm.supervisorId }
      };
      // 设置默认处理人
      return fileNameArr;
    },
    // 必填校验
    initFormRules(param) {
      this.formRules = param;
    },
    // 只读表单
    getReadonlyList(param) {
      if (param.length) {
        this.$refs.infos.basicConfig.forEach((item) => {
          if (param.includes(item.prop)) {
            item.disabled = true;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
