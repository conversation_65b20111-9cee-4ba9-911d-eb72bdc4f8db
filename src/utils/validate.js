/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === 'string' || str instanceof String) {
    return true
  }
  return false
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}
/**
 * 校验手机号(非必填校验required: false,必填校验required: true)
 * @param {Boolean} required
 */
export const checkPhone = (rule, value, callback, required) => {
  const phoneReg = /^1[34578]\d{9}$/
  if (!value && !required) {
    // 当数据为空时，不进行校验
    return callback()
  } else if (!value && required) {
    callback(new Error('请输入电话'))
  } else if (phoneReg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的手机号码'))
  }
}
/**
 * 身份证号(非必填校验required: false,必填校验required: true)
 * @param {Boolean} required
 */
export const checkIdCard = (rule, value, callback, required) => {
  const reg = /^[1-9]\d{5}(19|20|21)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  // 当数据为空时，不进行校验
  if (!value && !required) {
    return callback()
  } else if (!value && required) {
    callback(new Error('请输入身份证号'))
  } else if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的身份证号'))
  }
}

/**
 * 邮箱地址(非必填校验required: false,必填校验required: true)
 * @param {Boolean} required
 */
export const checkEmail = (rule, value, callback, required) => {
  const reg = /^([A-Za-z0-9_\-\.])+\@(163.com|qq.com|42du.cn)$/
  // 当数据为空时，不进行校验
  if (!value && !required) {
    return callback()
  } else if (!value && required) {
    callback(new Error('请输入邮箱'))
  } else if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的邮箱'))
  }
}

export const getElementTopDistance = (ele) =>{
  let rec = ele.getBoundingClientRect();
  let eleHeader = document.getElementsByClassName('headerbar-container')[0];
  let eleHeaderMenu = document.getElementsByClassName('sub-main-container--tagsview')[0];
  let eleHeaderRec = eleHeader?.getBoundingClientRect() || 0;
  let eleHeaderMenuRec = eleHeaderMenu?.getBoundingClientRect() || 0;
  let totalHeaderHeight = (eleHeaderRec.height || 0) + (eleHeaderMenuRec.height || 0);// 头部页签
  let footerHeight = 50// 大概的一个值，比悬浮操作栏(46px)还要高一些
  let windowHeight = window.innerHeight || document.documentElement.clientHeight;
  // 不要小数点，向大取整
  let recTop = Math.floor(rec.top);
  let recBottom = Math.floor(rec.bottom);
  if(recTop < totalHeaderHeight){
    return recTop - totalHeaderHeight;
  }else if(recBottom > windowHeight - footerHeight){
    return recBottom - (windowHeight - footerHeight)+50;
  }else{
    return 0;
  }
};

export const validateFormFileds = (formEle, obj) => {
  let firstProp = Object.keys(obj)[0];
  let label = formEle.querySelector(".el-form-item__label[for=" + firstProp + "]");
  let mainPage = document.getElementsByTagName('html')[0]
  if (label) {
    let elementTopDistance = getElementTopDistance(label)
    if (elementTopDistance !== 0) {
      mainPage.scrollTo(0, mainPage.scrollTop + elementTopDistance)
      // label.scrollIntoView() // 需要修改样式
    }
  }
}
