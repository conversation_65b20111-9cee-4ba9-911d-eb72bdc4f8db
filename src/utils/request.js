import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  withCredentials: true, // send cookies when cross-domain requests
  timeout: window.g.timeout || 0
})

// request interceptor
service.interceptors.request.use(
  config => {
    if (getToken()) { // token存在,则在请求头上加上token
      config.headers[window.g.tokenParams] = `Bearer ${getToken()}`
    }
    return config
  },
  error => {
    // do something with request error
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    if (response.status === 200) {
      if (response.config.responseType === 'blob') { // 流文件
        return response
      } else if (response.data.code === '0000') {
        return response.data
      } else if (response.data.code === '5000') {
        Message({
          message: response.data.msg|| '系统错误',
          type: 'error',
          dangerouslyUseHTMLString: true,
          offset:100,
          duration: 5 * 1000
        })
        return response.data
      }else if(response.data.code != '6000'){ // code为其他值的情况
        Message({
          message: response.data.msg || '服务器错误',
          type: 'error',
          dangerouslyUseHTMLString: true,
          offset:100,
          duration: 5 * 1000
        })
        return response
      }
    }
  },
  error => {
    Message({
      message: error.message,
      type: 'error',
      dangerouslyUseHTMLString: true,
      offset:100,
      duration: 5 * 1000
    })
    if (error.response && error.response.status === 401) {
      store.dispatch('user/resetToken').then(() => {
        location.reload()
      })
    }
    return Promise.reject(error)
  }
)

export default service
