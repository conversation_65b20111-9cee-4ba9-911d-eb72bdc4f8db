// 公共的js
const userAccessToken = sessionStorage.getItem('access_token')

/** 多个删除公共方法
 *  @param {data[Array]:勾选数据,delApi[function]:删除api,key[String]:删除的唯一标识，默认id,sucCb[function]：成功回调}
 *  @use 示例：    commonMultDel.call(this,{data:this.$refs.table.multipleSelection, delApi:delService,key:'boId', sucCb:(res) => {//成功后的一些操作}}）
 * 注意：需要用call绑定this,否则这里访问不到this实例
 */
export function commonMultDel({ data = [], delApi, key = 'id', sucCb = () => {} }) {
  if (data.length) {
    this.$confirm('是否确认删除这些数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const ids = data.map(x => { return x[key] }).join(',')
      delApi({ ids: ids }).then(res => {
        if (res.code == '0000') {
          if (sucCb)sucCb(res)
        } else {
          this.$message.error(`${res.msg || '删除失败'}`)
        }
      })
    }).catch(() => {})
  } else {
    this.$message.warning('请勾选需要删除的数据')
  }
}
/** 单个删除公共方法
 *  @param {id[String]:删除id,delApi[function]:删除api,sucCb[function]：成功回调,paramsType[String]:请求参数path还是query}
 *  @use  示例：  commonOneDel.call(this,row.id, delService, (res) => {//成功后的一些操作},'path'）
 *   注意：需要用call绑定this,否则这里访问不到this实例
 */
export function commonOneDel(id, delApi, sucCb,paramsType) {
  this.$confirm('是否确认删除这条数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    let req
    if(paramsType == 'path'){
      req = id
    }else{
      req = {
        ids: id
      }
    }
    delApi(req).then(res => {
      if (res.code == '0000') {
        if (sucCb)sucCb(res)
      } else {
        this.$message.error(`${res.msg || '删除失败'}`)
      }
    })
  }).catch(() => {})
}

/** 导出
 * @param {param[obj]:下载参数,downApi[function]:下载api}
 * @use  示例：  commonDown({ ...this.loadParam,//其他参数}, downloadService);
 */
export function commonDown(param, downApi) {
  const arr = []
  for (const i in param) {
    if (param[i]) {
      try{
        arr.push(`${i}=${encodeURIComponent(param[i])}`)
      }catch(e){
        arr.push(`${i}=${param[i]}`)
      }
    }
  }
  arr.push(`access_token=${userAccessToken}`)
  const fullUrl = `${downApi()}?${arr.join('&')}`

  let newWindow = window.open(fullUrl, '_blank')
  if (newWindow) {
    newWindow.opener = null
  }
}
// 导出-解析下载文件流(subcontractor_info/detail.vue)
export const download = (fileName, content) => {
  const blob = new Blob([content],
    {
      type: 'application/vnd.ms-excel'
    }
  )
  if ('download' in document.createElement('a')) {
    // 非IE
    const elink = document.createElement('a')
    elink.download = fileName
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href) // 释放URL 对象
    document.body.removeChild(elink)
  } else {
    // IE10+
    navigator.msSaveBlob(blob, fileName + '.xls')// msSaveBlob导出文件要加后缀
  }
}
