import request from '@/utils/request.js'
const BASEURL = window.g.baseMaterialUrl
// 通过暂存点编号查询流程工单（修改，撤销初始化页面）
export const getByStoragePointCode = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/storageTempController/getByStoragePointCode`,
    params:data
  })
}

//根据地市，区县，暂存点类型生成任务单编号,任务单名称,暂存点编号
export const getAutoCodeAndName = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/storageTempController/getAutoCodeAndName`,
    params:data
  })
}

//保存主单详细信息
export const savePageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/storageTempController/save`,
    data: data
  })
}

//修改主单详细信息
export const updateByIdPageService = (data) => {
  return request({
    method: 'put',
    url: BASEURL + `/storageTempController/updateById`,
    data: data
  })
}

//通过id查询流程工单
export const getByIdService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/storageTempController/${data}`,
  })
}

// 获取流程工单列表
export const findPageService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/storageTempController/findPage`,
    params: data
  })
}

// 获取暂存点工单列表
export const findTempPageService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/storageTempController/findTempPage`,
    params: data
  })
}

// 获取暂存点工单详细信息
export const getTempByIdService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/storageTempController/getTempById`,
    params: data
  })
}

// 撤销校验，判断暂存点里是否存在物资（
export const undoCheckService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/storageTempController/undoCheck`,
    params: data
  })
}

// 通过id批量删除流程工单
export const multipleDelService = (data) => {
  return request({
    method: 'delete',
    url: BASEURL + `/storageTempController/batchremove`,
    params: data
  })
}

// 通过id删除流程工单
export const singleDelService = (data) => {
  return request({
    method: 'delete',
    url: BASEURL + `/storageTempController/${data}`,
  })
}