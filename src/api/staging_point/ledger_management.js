import request from '@/utils/request.js'
const BASEURL = window.g.baseMaterialUrl  +  '/storageTempStaticController'
// 获取台账列表
export const findPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/findPage`,
    data: data
  })
}
// 导出
export const downloadService = () => {
  return  BASEURL + `/exportExcel`
}
// 获取暂存点详细信息
export const getBasicInfoService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/getById`,
    params: data
  })
}

// 获取物料历史信息
export const getMaterialHistoryService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/getMaterialHistory`,
    params: data
  })
}
// 获取暂存点历史信息
export const getStorageHistoryService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/getStorageTempHistory`,
    params: data
  })
}
//获取暂存点删除信息
export const getDeleteInfoService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/getDeleteHistory`,
    params: data
  })
}
//获取物资详情信息
export const getMaterialDerailService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/getMaterialDerail`,
    params: data
  })
}
// 物资详情导出
export const exportPageMaterial = () => {
  return  BASEURL + `/exportPageMaterialDerail`
}
// 物资历史详情导出
export const exportPageMaterialHis = () => {
  return  BASEURL + `/exportMaterialHistory`
}
