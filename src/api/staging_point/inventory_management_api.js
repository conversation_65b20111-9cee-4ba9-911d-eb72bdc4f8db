import request from '@/utils/request.js'
const BASEURL = window.g.baseMaterialUrl
// 通过暂存点编号查询流程工单（修改，撤销初始化页面）
export const findPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/inventoryController/findPage`,
    data:data
  })
}

//通过id查询流程工单
export const getByIdService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/inventoryController/${data}`,
  })
}

// 导出暂存点物资盘点信息
export const exportPageService = () => {
  return BASEURL + '/inventoryController/exportPage'
}

// 暂存点物资盘点信息导入
export const importExcelService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/inventoryController/importExcel`,
    data: data
  })
}

// 暂存点物资盘点信息导入
export const savePageService = (data) => {
  return request({
    method: 'put',
    url: BASEURL + `/inventoryController`,
    data: data
  })
}

// 获取暂存点库存物资明细列表(盘点工单使用)
export const findInventoryMateriaPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/inventoryController/material/findInventoryMateriaPage`,
    data: data
  })
}

// 提交前校验
export function beforeSubmitService(data){
  return request({
    method:'get',
    url:BASEURL + '/inventoryController/flowValidate' ,
    params: data
  })
}
