import request from '@/utils/request.js'
const BASEURL = window.g.baseMaterialUrl
// 获取物资出入库列表
export const outputInputFindPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/outputInputController/findPage`,
    params: data
  })
}

// 获取库存物资列表
export const materialFindPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/outputInputController/material/findPage`,
    params: data
  })
}

// 申请物质获取列表
export const findDetailPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/outputInputController/detail/findPage`,
    params: data
  })
}

// 已完成暂存点列表
export const storageTempFindPage = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/outputInputController/storageTemp/findPage`,
    params: data
  })
}

// 跳转到新增页面
export const addPageService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/outputInputController/addPage`,
    params: data
  })
}

// 新增物资出入库及退库
export const saveService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/outputInputController`,
    data: data
  })
}

// 修改物资出入库及退库
export const putService = (data) => {
  return request({
    method: 'put',
    url: BASEURL + `/outputInputController`,
    data: data
  })
}

// 导出库存物资明细表
export const materialExportPage = () => {
  return BASEURL + '/outputInputController/material/exportPage'
}

// 导出清单模版
export const detailExportPage = () => {
  return BASEURL + '/outputInputController/detail/exportPage'
}

// 导入物资明细
export const outputImportExcelService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/outputInputController/outputImportExcel`,
    data: data
  })
}

// 通过id查询
export const getDetailsById = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/outputInputController/${data}`,
  })
}

// 导出申请物资明细表(审批环节)
export const exportDetailService = () => {
  return BASEURL + '/outputInputController/detail/exportDetail'
}
 
// 导入操作物资明细
export const detailActualExcelService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/outputInputController/detailActualExcel`,
    data: data
  })
}

// 通过id批量删除流程工单
export const multipleDelService = (data) => {
  return request({
    method: 'delete',
    url: BASEURL + `/outputInputController/batchremove`,
    params: data
  })
}

// 通过id删除流程工单
export const singleDelService = (data) => {
  return request({
    method: 'delete',
    url: BASEURL + `/outputInputController/${data}`,
  })
}

// 提交前校验
export function beforeSubmitService(data){
  return request({
    method:'get',
    url:BASEURL + '/outputInputController/flowValidate' ,
    params: data
  })
}

// 导出申请物资(实际数量驳回后重新申请)
export const exportPageDownService = () => {
  return BASEURL + '/outputInputController/material/exportPageDown'
}

// 导出工单列表
export const exportPageService = () => {
  return BASEURL + '/outputInputController/exportPage'
}
