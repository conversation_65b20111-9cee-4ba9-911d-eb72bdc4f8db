// 集客专线--成本类项目--api
import request from '@/utils/request'
const filesServiceUrl = window.g.baseLineUrl + '/groupCustomerProjectAction/'

// 列表
export function queryListService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findPage' ,
    data: data
  })
}

// 导出
export function downloadService() {
  return filesServiceUrl + 'exportData'
}

//导入
export function importService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'importData',
    data: data
  })
}

// 删除
export function delService(data) {
  return request({
    method: 'get',
    url: filesServiceUrl + 'remove',
    params: data
  })
}
