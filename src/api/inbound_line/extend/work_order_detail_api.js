import request from '@/utils/request'
const filesServiceUrl = window.g.baseExtendLineUrl + '/groupWorkOrderDetailAction/'
/**
 * 成本类工单详情
 */
export function queryListService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findPage' ,
    data: data
  })
}
/**
 * 投资类(非紧急)工单详情
 */
export function queryListServiceInvest(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findInvestPage' ,
    data: data
  })
}
/**
 * 投资类(紧急)工单详情
 */
export function queryListServiceInvestUrgent(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findInvestUrgentPage' ,
    data: data
  })
}
// 导出
export function downloadService(){
  return filesServiceUrl + 'exportData'
}
