// 集客专线--投资完工--api
import request from '@/utils/request'
const filesServiceUrl = window.g.baseExtendLineUrl + '/groupCustomerInvestCompleteAction/'

// 列表
export function queryListService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findPage' ,
    data: data
  })
}

// 查询详情--
export function queryDetailService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getInfo' ,
    params: data
  })
}

// 保存
export function saveService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'save' ,
    data: data
  })
}

// 提交前校验
export function beforeSubmitService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'flowValidate' ,
    params: data
  })
}

export function durationValidate(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'durationValidate' ,
    params: data
  })
}
// 导出
export function exportListService(){
  return filesServiceUrl + 'exportData'
}
