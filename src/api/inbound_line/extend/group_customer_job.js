// 集客专线定时器--api
import request from '@/utils/request'
const filesServiceUrl = window.g.baseExtendLineUrl + '/groupCustomerJobAction/'

// 列表
export function queryReminderTask(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findReminderTask' ,
    data: data
  })
}

export function readAlready(taskId){
  return request({
    method:'post',
    url:filesServiceUrl + 'readAlready?taskId=' + taskId,
  })
}
