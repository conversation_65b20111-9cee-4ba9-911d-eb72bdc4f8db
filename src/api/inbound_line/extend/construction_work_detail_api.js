import request from '@/utils/request'
const filesServiceUrl = window.g.baseExtendLineUrl + '/groupConstructionWorkDetailAction/'
/**
 * 商机勘察工单详情
 */
export function querySurveyListService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findSurveyPage' ,
    data: data
  })
}
/**
 * 预覆盖建设工单详情
 */
export function queryPrecoveringListService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findPrecoveringPage' ,
    data: data
  })
}
// 导出
export function downloadService(){
  return filesServiceUrl + 'exportData'
}
