import request from '@/utils/request'
const filesServiceUrl = window.g.baseExtendLineUrl + '/groupConsTaskOrderInvestAction/'

// 列表
export function queryListService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findPage' ,
    data: data
  })
}

// 查询详情--
export function queryDetailService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getInfo' ,
    params: data
  })
}

// 保存
export function saveService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'save' ,
    data: data
  })
}
//合同选择
export function getContracListService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'findContractPage',
    data: data
  })
}

//合作单位选择
export function getProviderListService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'findProviderPage',
    data: data
  })
}
// 提交前校验
export function beforeSubmitService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'flowValidate' ,
    params: data
  })
}
// 导出
export function downloadService() {
  return filesServiceUrl + 'exportData'
}
