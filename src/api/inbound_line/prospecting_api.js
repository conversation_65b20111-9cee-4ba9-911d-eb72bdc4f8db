// 集客专线--勘查--api
import request from '@/utils/request'
const filesServiceUrl = window.g.baseLineUrl + '/groupCustomerLineAction/'

// 列表
export function queryListService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findPage' ,
    data: data
  })
}

// 查询详情--
export function queryDetailService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getInfo' ,
    params: data
  })
}

// 保存
export function saveService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'save' ,
    data: data
  })
}
//项目选择---成本类
export function getProjectListService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'findProjectPage',
    data: data
  })
}
// 提交前校验
export function beforeSubmitService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'flowValidate' ,
    params: data
  })
}
// 导出
export function downloadService() {
  return filesServiceUrl + 'exportData'
}
