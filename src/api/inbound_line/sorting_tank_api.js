// 集客专线--归类池--api
import request from '@/utils/request'
const filesServiceUrl = window.g.baseLineUrl + '/groupCustomerArchiveAction/'
// 列表
export function queryListService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'findPage' ,
    data: data
  })
}

// 查询详情--
export function queryDetailService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getInfo' ,
    params: data
  })
}

// 保存
export function saveService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'save' ,
    data: data
  })
}
// 导出
export function downloadService() {
  return filesServiceUrl + 'exportData'
}

//导入
export function importService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'importData',
    data: data
  })
}

//项目选择
export function getProjectListService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'findProjectPage',
    data: data
  })
}

//勘查单选择
export function getOrderListService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'findGroupCustomerLineDetailList',
    data: data
  })
}

//绑定勘查单选择
export function bindOrderListService(data) {
  return request({
    method: 'get',
    url: filesServiceUrl + 'addGroupCustomerLineDetail',
    params: data
  })
}

// 提交前校验
export function beforeSubmitService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'flowValidate' ,
    params: data
  })
}
//集客专线归类情况统计表
export function findArchiveReportListService(data) {
  return request({
    method: 'get',
    url: window.g.baseLineUrl + '/groupCustomerReportAction/findArchiveReportList',
    params: data
  })
}
// 删除
export function delService(data) {
  return request({
    method: 'get',
    url: filesServiceUrl + 'deleteGroupCustomerArchive',
    params: data
  })
}
// 删除明细
export function deldetailService(data) {
  return request({
    method: 'get',
    url: filesServiceUrl + 'deleteGroupCustomerArchiveDetail',
    params: data
  })
}
// 添加选择任务
export function getTaskListService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'findPmsTaskPage',
    data: data
  })
}
