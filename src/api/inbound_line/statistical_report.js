/*
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-26 09:25:57
 * @Description: 集客专线统计报表
 */
import request from '@/utils/request'
const baseUrl = window.g.baseLineUrl + '/groupCustomerReportAction/'

// 4.成本类-集客专线耗时统计报表
export const findCostCompListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findGroupCustomerCostCompList',
    params: data
  })
}
// 4.成本类-集客专线耗时统计详情
export const findCostCompDetailService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findGroupCustomerCostCompDetailList',
    params: data
  })
}
// 4.导出成本类-集客专线耗时统计报表
export const exportCostCompListService = () => {
  return baseUrl + 'exportGroupCustomerCostCompList'
}
// 4.导出成本类-集客专线耗时统计报表明细报表
export const exportCostCompDetailService = () => {
  return baseUrl + 'exportGroupCustomerCostCompDetailList'
}
// 2.成本类-在途集客专线统计报表
export const findCostProcessListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findGroupCustomerCostProcessList',
    params: data
  })
}
// 2.成本类-在途集客专线统计详情
export const findCostProcessDetailService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findGroupCustomerCostProcessDetailList',
    params: data
  })
}
// 2.导出在途集客专线统计报表
export const exportCostProcessListService = () => {
  return baseUrl + 'exportGroupCustomerCostProcessList'
}
// 2.导出在途集客专线统计明细报表
export const exportCostProcessDetailService = () => {
  return baseUrl + 'exportGroupCustomerCostProcessDetailList'
}
// 8.集客专线进度统计报表 
export const findScheduleListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findGroupCustomerScheduleList',
    params: data
  })
}
// 8.集客专线进度详情报表 
export const findScheduleDetailService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findGroupCustomerScheduleDetailList',
    params: data
  })
}
//8.导出集客专线进度统计报表
export const exportScheduleListService = () => {
  return baseUrl + 'exportGroupCustomerScheduleList'
}
//8.导出集客专线进度统计明细报表
export const exportScheduleDetailService = () => {
  return baseUrl + 'exportGroupCustomerScheduleDetailList'
}

// 1.投资类-在途集客专线统计报表
export const findInvestProcessListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findInvestProcessList',
    params: data
  })
}
// 1.投资类-在途集客专线详情报表
export const findInvestProcessDetailListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findInvestProcessDetailList',
    params: data
  })
}
//1.导出投资类-在途集客专线统计报表
export const exportInvestProcessListService = () => {
  return baseUrl + 'exportInvestProcessList'
}
//1.导出投资类-在途集客专线统计明细报表
export const exportInvestProcessDetailService = () => {
  return baseUrl + 'exportInvestProcessDetailList'
}

// 3.投资类-集客专线耗时统计报表
export const findInvestCompListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findInvestCompList',
    params: data
  })
}
// 3.投资类-集客专线耗时详情报表
export const findInvestCompDetaiService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findInvestCompDetailList',
    params: data
  })
}
//3.导出投资类-集客专线耗时统计报表
export const exportInvestCompListService = () => {
  return baseUrl + 'exportInvestCompList'
}
//3.导出投资类-集客专线耗时详情报表
export const exportInvestCompDetailService = () => {
  return baseUrl + 'exportInvestCompDetailList'
}

// 5.宽带一天响应进展统计报表
export const findNetScheduleListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findNetScheduleList',
    params: data
  })
}
// 5.宽带一天响应进展详情报表
export const findNetScheduleDetailService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findNetScheduleDetailList',
    params: data
  })
}
//导出宽带一天响应进展统计报表
export const exportNetScheduleListService = () => {
  return baseUrl + 'exportNetScheduleList'
}
//导出宽带一天响应进展详情报表
export const exportNetScheduleDetailService = () => {
  return baseUrl + 'exportNetScheduleDetailList'
}

// 6.宽带建设各环节时长统计
export const findNetScheduleDayListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findNetScheduleDayList',
    params: data
  })
}
//导出宽带建设各环节时长统计
export const exportNetScheduleDayListService = () => {
  return baseUrl + 'exportNetScheduleDayList'
}

// 7.宽带进度统计报表
export const findBroadbandProcessListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findBroadbandProcessList',
    params: data
  })
}
// 7.宽带进度详情报表
export const findBroadbandProcessDetailervice = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findBroadbandProcessDetailList',
    params: data
  })
}
export const exportBroadbandProcessListService = () => {
  return baseUrl + 'exportBroadbandProcessList'
}
export const exportBroadbandProcessDetailService = () => {
  return baseUrl + 'exportBroadbandProcessDetailList'
}