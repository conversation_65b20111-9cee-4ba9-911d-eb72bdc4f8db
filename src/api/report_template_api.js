import request from '@/utils/request.js'
const BASEURL = window.g.baseMonthlyUrl
const baseProjectUrl = window.g.baseProjectUrl
// 根据模板编码获取模板
export const getTempateDtoByCodeService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/reportTemplate/getTempateDtoByCode`,
    params: reqData
  })
}

// 查询数据
export const getDataService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/templatebussiness/getData`,
    params: reqData
  })
}

// 保存数据
export const saveDataService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/templatebussiness/saveData`,
    data: reqData
  })
}

// 查询历史数据
export const getHisDataService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/templatebussiness/getHisData`,
    params: reqData
  })
}

// 根据所有模板
export const getReportTempatesService = () => {
  return request({
    method: 'get',
    url: BASEURL + `/reportTemplate/getReportTempates`,
  })
}

// 查询模板数据
export const getTemplateDataService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/templatebussiness/getTemplateData`,
    params: reqData
  })
}

// 分页查询项目
export const findPageByMonthReportTemplateService = (reqData) => {
  return request({
    method: 'post',
    url: baseProjectUrl + `/projectSelect/findPageByMonthReportTemplate`,
    data: reqData
  })
}

// 查询模板数据
export const deleteDataService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/templatebussiness/deleteData`,
    params: reqData
  })
}

// 查询模板数据
export const saveTemplateDataService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/templatebussiness/saveTemplateData`,
    data: reqData
  })
}


