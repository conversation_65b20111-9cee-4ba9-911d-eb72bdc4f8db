
import request from "@/utils/request.js";
const workflowServiceUrl = window.g.baseUrl + '/workflow';
// 流程记录
export const getHistorysService = (reqData) => {
  return request({
    method: "post",
    url: workflowServiceUrl + `/engine/getHistorys`,
    data: reqData,
  });
};

export const getCurrentTasksService = (reqData) => {
  return request({
    method: "post",
    url: workflowServiceUrl + `/engine/getTasks`,
    data: reqData,
  });
};

// 流程操作组织
export const getDeptTreeListService = (reqData) => {
  return request({
    method: "post",
    url: workflowServiceUrl + `/user/queryOrgTree`,
    data: reqData,
  });
};

// 流程操作人员
export const getUserListService = (reqData) => {
  return request({
    method: "post",
    url: workflowServiceUrl + `/user/getUsers`,
    data: reqData,
  });
};

//获取当前流程任务配置信息
export function initWorkFlowService(data) {
  return request({
    method: "post",
    url: workflowServiceUrl + "/engine/initWorkflow",
    data: data,
  });
}
//获取下一节点
export const initNextPathService = (reqData) => {
  return request({
    method: "post",
    url: workflowServiceUrl + `/engine/initNextPath`,
    data: reqData,
  });
};

export const queryNodeAccessService = (reqData) => {
  return request({
    method: "post",
    url: workflowServiceUrl + `/engine/getFieldAccess`,
    data: reqData,
  });
};

export const getTaskService = (reqData) => {
  return request({
    method: "post",
    url: workflowServiceUrl + `/task/getMyTasks`,
    data: reqData,
  });
};

export const getWorkflowService = (reqData) => {
  return request({
    method: "post",
    url: workflowServiceUrl + `/getWorkflows`,
    data: reqData,
  });
};

//根据流程编号和业务id得到流程配置ID
export function getWorkflowByBoId(data){
  return request({
    method: 'post',
    url: workflowServiceUrl + '/engine/getWorkflowByBoId',
    params: data,
  })
}

