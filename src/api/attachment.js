import request from '@/utils/request'
const filesServiceUrl = window.g.baseUrl + '/senon-file-center/files/'
/**
 * 获取附件列表
 */
export function queryFilesListService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getAllFiles' ,
    params: data
  })
}

// 单个附件下载
export function getFilesDownloadService() {
  return filesServiceUrl + 'download/'
}

export function getFilesDownloadService2(data){
  const userAccessToken = sessionStorage.getItem('access_token') || ''
  return request({
    method: 'get',
    url: filesServiceUrl + 'download/' + data,
    params: {
      access_token: userAccessToken
    },
    responseType: 'blob'
  })
}

//批量附件下载
export function getFilesBatchDownloadService() {
  return filesServiceUrl + 'downloads'
}

//批量附件下载
export function getFilesBatchDownloadService2(params) {
  return request({
    method: 'get',
    url: filesServiceUrl + 'downloads',
    params,
    responseType: 'blob'
  })
}

// 删除附件
export function delFiles(data) {
  return request({
    method: 'DELETE',
    url: filesServiceUrl + 'dels',
    params: data
  })
}

// 上传附件
export function uploadFiles(data, url){
  return request({
    method: 'post',
    url:filesServiceUrl + 'upload',
    data: data,
    attFile: true,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
// 附件类型
export function getFileTypesService(data) {
  return request({
    method: "get",
    url: filesServiceUrl + "getFileTypes",
    params: data,
  });
}

/**
 * 根据票据获取文件列表
 */
export function getFilesService(data){
  return request({
    method:'get',
    url:filesServiceUrl + `getFiles/${data}`,
  })
}
