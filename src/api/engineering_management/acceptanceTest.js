/**
 * @author: ty
 * @date: 2023-07-10
 * @description: 【验收测试】接口api
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const acceptanceTestBaseUrl = `${baseUrl}/acceptanceTest`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${acceptanceTestBaseUrl}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${acceptanceTestBaseUrl}/getDetail`,
    params
  })
}
export const getByIdService = (urlParams, params) => {
  return request({
    method: 'get',
    url: `${acceptanceTestBaseUrl}/${urlParams}`,
    params
  })
}
// 删除
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${acceptanceTestBaseUrl}`,
    params
  })
}
