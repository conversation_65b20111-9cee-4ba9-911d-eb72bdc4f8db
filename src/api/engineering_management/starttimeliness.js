/**
 * @author: ty
 * @date: 2023-07-26
 * @description: 完工及时性
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const starttimelinessBaseUrl = `${baseUrl}/starttimeliness`

// 通过id查询(待阅不消除)
export const getByIdService = (id) => {
  return request({
    method: 'get',
    url: `${starttimelinessBaseUrl}/${id}`,
  })
}
// 通过id查询(待阅消除)
export const viewByIdService = (id) => {
  return request({
    method: 'get',
    url: `${starttimelinessBaseUrl}/view/${id}`,
  })
}
