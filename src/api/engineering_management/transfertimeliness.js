/**
 * @author: ty
 * @date: 2023-07-26
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const transfertimelinessBaseUrl = `${baseUrl}/transfertimeliness`

// 通过id查询(待阅不消除)
export const getByIdService = (id) => {
  return request({
    method: 'get',
    url: `${transfertimelinessBaseUrl}/${id}`,
  })
}
// 通过id查询(待阅消除)
export const viewByIdService = (id) => {
  return request({
    method: 'get',
    url: `${transfertimelinessBaseUrl}/view/${id}`,
  })
}

// 任务列表
export const taskCutoverByIdService = ({id}) => {
  return request({
    method: 'get',
    url: `${transfertimelinessBaseUrl}/taskCutover/${id}`,
  })
}
