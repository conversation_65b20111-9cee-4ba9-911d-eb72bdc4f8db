/**
 * @author: ty
 * @date: 2023-07-10
 * @description:  开工启动会相关接口地址
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const kickoffMeetingBaseUrl = `${baseUrl}/kickoffMeeting`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${kickoffMeetingBaseUrl}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${kickoffMeetingBaseUrl}/getDetail`,
    params
  })
}
export const getByIdService = (urlParams, params) => {
  return request({
    method: 'get',
    url: `${kickoffMeetingBaseUrl}/${urlParams}`,
    params
  })
}
// 删除
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${kickoffMeetingBaseUrl}`,
    params
  })
}
