/**
 * @author: ty
 * @date: 2023-07-11
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const projectMessageBaseUrl = `${baseUrl}/projectMessage`
const projectCompletionTaskUrl = `${baseUrl}/cutovertimewarning`

export const exportExcelService = (params) => {
  return `${projectMessageBaseUrl}/exportExcel`
}

export const getByIdService = (id) => {
  return request({
    method: 'get',
    url: `${projectMessageBaseUrl}/getProjectById/${id}`,
  })
}

export const getConTaskByIdService = (params) => {
  return request({
    method: 'get',
    url: `${projectMessageBaseUrl}/getConTaskById`,
    params
  })
}

export const getDesTaskByIdService = (params) => {
  return request({
    method: 'get',
    url: `${projectMessageBaseUrl}/getDesTaskById`,
    params
  })
}

export const getSupTaskByIdService = (params) => {
  return request({
    method: 'get',
    url: `${projectMessageBaseUrl}/getSupTaskById`,
    params
  })
}

// �����б�
export const taskCutoverByIdService = ({id}) => {
  return request({
    method: 'get',
    url: `${projectCompletionTaskUrl}/taskUncutover/${id}`,
  })
}
