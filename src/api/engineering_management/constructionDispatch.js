/**
 * @author: ty
 * @date: 2023-07-10
 * @description:  施工排工相关接口地址
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const qualityDeclarationBaseUrl = `${baseUrl}/constructionDispatch`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}/getDetail`,
    params
  })
}
export const getByIdService = (urlParams, params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}/${urlParams}`,
    params
  })
}
// 删除
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${qualityDeclarationBaseUrl}`,
    params
  })
}
