/**
 * @author: ty
 * @date: 2023-07-18
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const supervisorFeeConfirmBaseUrl = `${baseUrl}/supervisorFeeConfirm`
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/findPage`,
    params
  })
}

export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${supervisorFeeConfirmBaseUrl}/delete`,
    params
  })
}

export const initService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/init`,
    params
  })
}

export const findProjectService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/findProject`,
    params
  })
}

// 获取结算审计清单
export const getConstructionAuditService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getConstructionAudit`,
    params
  })
}

export const exportConstructionAuditService = (params) => {
  return `${supervisorFeeConfirmBaseUrl}/exportConstructionAudit`
}

// 导入结算审计列表
export const importExcelService = (data) => {
  return request({
    method: 'post',
    url: `${supervisorFeeConfirmBaseUrl}/importExcel`,
    data
  })
}

// 保存结算审计列表
export const saveChargingStandardService = (data) => {
  return request({
    method: 'post',
    url: `${supervisorFeeConfirmBaseUrl}/saveChargingStandard`,
    data
  })
}

export const saveCivilService = (data) => {
  return request({
    method: 'post',
    url: `${supervisorFeeConfirmBaseUrl}/saveCivil`,
    data
  })
}

// 删除计费额标准
export const deleteChargingStandardService = (params) => {
  return request({
    method: 'post',
    url: `${supervisorFeeConfirmBaseUrl}/deleteChargingStandard`,
    params
  })
}

// 计费额标准列表
export const getChargingStandardService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getChargingStandard`,
    params
  })
}
// 选择区县监理单位
export const getCityService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getCity`,
    params
  })
}
export const getSupervisorDeptService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getSupervisorDept`,
    params
  })
}

export const getConsTendersByCountyIdService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getConsTendersByCountyId`,
    params
  })
}

export const getProviderByCountyIdService = (data) => {
  return request({
    method: 'post',
    url: `${supervisorFeeConfirmBaseUrl}/getProviderByCountyId`,
    data
  })
}

// 获取通信类审定监理费列表
export const getCivilService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getCivil`,
    params
  })
}

export const getCivilProviderListService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getCivilProviderList`,
    params
  })
}

// 获取土建类审定监理单位
export const getCivilProviderService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getCivilProvider`,
    params
  })
}

export const delCivilItemService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/delCivilItem`,
    params
  })
}

export const saveService = (data) => {
  return request({
    method: 'post',
    url: `${supervisorFeeConfirmBaseUrl}/saveSupervisor`,
    data
  })
}

export const getSupervisorUserService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/getSupervisorUser`,
    params
  })
}

export const checkFeeService = (params) => {
  return request({
    method: 'get',
    url: `${supervisorFeeConfirmBaseUrl}/checkFee`,
    params
  })
}


