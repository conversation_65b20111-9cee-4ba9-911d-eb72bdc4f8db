import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const qualityDeclarationBaseUrl = `${baseUrl}/supervisionDispatch`
// 获取列表
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}`,
    params
  })
}
// 详细列表
export const detailPageService = (params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}/${params}`
  })
}
// 任务列表
export const taskListService = (params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}/getDetail`,
    params
  })
}

