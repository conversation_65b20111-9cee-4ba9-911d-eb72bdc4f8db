import request from '@/utils/request'
const BASEURL = window.g.baseUrl

//获取列表
export const exchangerQueryService = (params) => {
  return request({
    method: 'GET',
    url: `${BASEURL}/rpa-exchanger/exchanger/query`,
    params
  })
}

//补偿
export const exchangerCompensateService = (params) => {
  return request({
    method: 'post',
    url: `${BASEURL}/rpa-exchanger/exchanger/compensate`,
    data:params
  })
}

//获取业务模型编码
export const exchangerGetmodulecodesService = () => {
  return request({
    method: 'get',
    url: `${BASEURL}/rpa-exchanger/exchanger/getmodulecodes`,
  })
}
//日志详情
export const exchangerDetailService = (dataId,moduleCode) => {
  return request({
    method: 'post',
    url: `${BASEURL}/rpa-exchanger/exchanger/detail/${dataId}/${moduleCode}`,
  })
}

//批量补偿
export const compensateAllNoFileService = (params) => {
  return request({
    method: 'post',
    url: `${BASEURL}/rpa-exchanger/exchanger/compensateAllNoFile`,
    data:params
  })
}