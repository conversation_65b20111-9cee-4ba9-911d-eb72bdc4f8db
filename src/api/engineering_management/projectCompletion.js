/**
 * @author: ty
 * @date: 2023-07-12
 * @description: 项目完工相关接口api
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const projectCompletionBaseUrl = `${baseUrl}/projectCompletion`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${projectCompletionBaseUrl}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${projectCompletionBaseUrl}/getDetail`,
    params
  })
}
export const getByIdService = (urlParams, params) => {
  return request({
    method: 'get',
    url: `${projectCompletionBaseUrl}/${urlParams}`,
    params
  })
}
// 删除
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${projectCompletionBaseUrl}`,
    params
  })
}
