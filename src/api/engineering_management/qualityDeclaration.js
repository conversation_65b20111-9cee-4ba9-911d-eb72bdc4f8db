/**
 * @author: ty
 * @date: 2023-07-08
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const qualityDeclarationBaseUrl = `${baseUrl}/qualityDeclaration`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}/getDetail`,
    params
  })
}
export const getByIdService = (urlParams, params) => {
  return request({
    method: 'get',
    url: `${qualityDeclarationBaseUrl}/${urlParams}`,
    params
  })
}
export const saveService = (params) => {
  return request({
    method: 'POST',
    url: `${qualityDeclarationBaseUrl}`,
    params
  })
}

// 删除
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${qualityDeclarationBaseUrl}`,
    params
  })
}
