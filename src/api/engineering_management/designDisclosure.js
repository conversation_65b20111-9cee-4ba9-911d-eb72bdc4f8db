  import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const designDisclosureBaseUrl = `${baseUrl}/designDisclosure`
// 获取列表
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${designDisclosureBaseUrl}`,
    params
  })
}
// 详情列表
export const detailPageService = (params) => {
  return request({
    method: 'get',
    url: `${designDisclosureBaseUrl}/${params}`,
  })
}
// 任务列表
export const taskListService = (params) => {
  return request({
    method: 'get',
    url: `${designDisclosureBaseUrl}/getDetail`,
    params: params
  })
}