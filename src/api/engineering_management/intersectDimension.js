/**
 * @author: ty
 * @date: 2023-07-11
 * @description: 割接交维接口api
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const intersectDimensionBaseUrl = `${baseUrl}/intersectDimension`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${intersectDimensionBaseUrl}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${intersectDimensionBaseUrl}/getDetail`,
    params
  })
}
export const getByIdService = (urlParams, params) => {
  return request({
    method: 'get',
    url: `${intersectDimensionBaseUrl}/${urlParams}`,
    params
  })
}
// 删除
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${intersectDimensionBaseUrl}`,
    params
  })
}
