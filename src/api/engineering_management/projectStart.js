/**
 * @author: ty
 * @date: 2023-07-10
 * @description: 开工报告相关接口地址
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const projectStartBaseUrl = `${baseUrl}/projectStart`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${projectStartBaseUrl}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${projectStartBaseUrl}/getDetail`,
    params
  })
}
export const getByIdService = (urlParams, params) => {
  return request({
    method: 'get',
    url: `${projectStartBaseUrl}/${urlParams}`,
    params
  })
}
// 删除
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${projectStartBaseUrl}`,
    params
  })
}
