/**
 * @author: ty
 * @date: 2023-07-03
 * @description: 工程签证相关接口
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseProjectUrl
const projectTaskVisaBaseUrl = `${baseUrl}/projectTaskVisaAction`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/findPage`,
    params
  })
}
export const exportFindPageService = (params) => {
  return `${projectTaskVisaBaseUrl}/exportFindPage`
}

export const findPageCoopService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/findPageCoop`,
    params
  })
}

export const delService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/delete`,
    params
  })
}

export const viewService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/view`,
    params
  })
}

export const initService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/init`,
    params
  })
}

export const getListService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/getList`,
    params
  })
}

// loadProjectList
export const findProject4visaService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/findProject4visa`,
    params
  })
}

export const findtask4visaService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/findtask4visa`,
    params
  })
}
// 查询合同列表
export const findContractService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/findContract`,
    params
  })
}

// 获取监理单位
export const getSuporgService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/getSuporg`,
    params
  })
}

export const refreshGrandTotalMoneyService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/refreshGrandTotalMoney`,
    params
  })
}
// 导出doc
export const expVisaReportService = (params) => {
  return `${projectTaskVisaBaseUrl}/expVisaReport`
}

// 导出pdf
export const generateVisaReportService = (params) => {
  return `${projectTaskVisaBaseUrl}/generateVisaReport`
}

export const validService = (params) => {
  return request({
    method: 'get',
    url: `${projectTaskVisaBaseUrl}/valid`,
    params
  })
}
export const saveService = (data) => {
  return request({
    method: 'post',
    url: `${projectTaskVisaBaseUrl}/save`,
    data
  })
}

