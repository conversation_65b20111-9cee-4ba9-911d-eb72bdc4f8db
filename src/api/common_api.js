import request from '@/utils/request.js'
const BASEURL = window.g.baseCommonUrl
const areaTree = window.g.baseUrl + '/admin/sysarea'
// 查地市区县下拉枚举接口
export const queryAreaListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/common/getlistByParentId`,
    params: reqData
  })
}
/**
 * 地域树接口
 */
export function getTreeListService(data) {
  return request({
    method: 'get',
    url: areaTree + '/tree',
    params: data
  })
}

export const childdeptService = (reqData) => {
  return request({
    method: 'get',
    url: window.g.baseUrl + `/admin/sysdept/childdept/${reqData}`,
  })
}
