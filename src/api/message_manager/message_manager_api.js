/**
 * @author: ty
 * @date: 2023-07-22
 * @description: 消息管理
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseCommonUrl
const messageManagerActionBaseUrl = `${baseUrl}/messageManagerAction`
export const personalMsgsService = (params) => {
  return request({
    method: 'get',
    url: `${messageManagerActionBaseUrl}/personalMsgs`,
    params
  })
}

export const findByMsgIdService = (id) => {
  return request({
    method: 'get',
    url: `${messageManagerActionBaseUrl}/findByMsgId/${id}`,
  })
}
export const confirmMessageService = (params) => {
  return request({
    method: 'put',
    url: `${messageManagerActionBaseUrl}/confirmMessage`,
    params
  })
}
