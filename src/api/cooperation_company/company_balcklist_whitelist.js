import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl
// 列表
export const queryListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptBlacklistController/findPage`,
    params: reqData
  })
}
// 通过部门id查询（多次修改带出最新一次数据）
export const queryCompanyByDeptIdService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptBlacklistController/getByDeptId/${reqData}`
  })
}
// 详情
export const queryDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptBlacklistController/${reqData}`
  })
}
// 删除
export const removeIdsService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerDeptBlacklistController/batchremove`,
    params: reqData
  })
}
// 导出
export const exportPageService = (data) => {
  return BASEURL + `/partnerDeptBlacklistController/exportPage`
}
// 保存主单信息
export const saveInfoService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerDeptBlacklistController`,
    data: reqData
  })
}
// 更新信息
export const updateInfoService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/partnerDeptBlacklistController`,
    data: reqData
  })
}
// 合作单位黑白名单-提交时校验是否存在流程中工单(true:没有在流程中的，可以新增；false:有流程中工单，不能新增)
export const notExistsProcessService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptBlacklistController/notExistsProcess/${reqData}`
  })
}
