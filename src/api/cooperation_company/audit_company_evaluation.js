import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl
// 列表
export const queryListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerevalaudit/findPage`,
    params: reqData
  })
}
// 列表-导入
export const importExcelService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerevalaudit/importExcel`,
    data: reqData
  })
}
// 列表-导出
export const exportExcelService = (reqData) => {
  return BASEURL + `/partnerevalaudit/exportExcel`
}
