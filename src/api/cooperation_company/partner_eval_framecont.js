/**
 * @author: ty
 * @date: 2023-12-06
 * @description: 合作单位框架合同管理后端接口
 */
import request from '@/utils/request'
const BASEURL = window.g.basePartnerUrl + '/partnerevalframecont'

// 分页查询
export const queryListService = (params) => {
  return request({
    method: 'get',
    url: BASEURL + `/findPage`,
    params
  })
}

// 通过id查询
export const queryByIdService = (id) => {
  return request({
    method: 'get',
    url: `${BASEURL}/${id}`,
  })
}

// 通过id删除
export const deleteByIdService = (id) => {
  return request({
    method: 'delete',
    url: `${BASEURL}/${id}`,
  })
}

// 通过id批量删除
export const batchRemoveService = (ids) => {
  return request({
    method: 'delete',
    url: `${BASEURL}/batchremove`,
    params: {ids}
  })
}

// 新增
export const addService = (data) => {
  return request({
    method: 'post',
    url: BASEURL,
    data,
  })
}

// 修改
export const updateService = (data) => {
  return request({
    method: 'put',
    url: BASEURL,
    data,
  })
}

// 导出
export const exportDataService = () => {
  return `${BASEURL}/exportData`
}
// 导入
export const importExcelService = (data) => {
  return request({
    method: 'post',
    url: `${BASEURL}/importExcel`,
    data,
  })
}
