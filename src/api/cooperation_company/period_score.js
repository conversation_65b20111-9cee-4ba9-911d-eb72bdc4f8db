import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl + '/partnerevalrpt'
// 列表
export const queryListService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/findProjectStageScoreRptPage`,
    data
  })
}
// 阶段导出
export const exportExcelService = () => {
    return  BASEURL + `/exportProjectStageScoreRpt`
}
// 年度得分导出
export const exportYearScoreExcelService = () => {
  return  BASEURL + `/exportYearScoreRpt`
}
// 年度得分
export const queryYearScoreService = (data) => {
    return request({
      method: 'post',
      url: BASEURL + `/findYearScoreRptPage`,
      data
    })
  }

export const exportBonusPenaltyService = () => {
  return BASEURL + `/exportBonusPenalty`
}

// 导入
export const importBonusPenaltyService = (data) => {
  return request({
    method: 'post',
    url: `${BASEURL}/importBonusPenalty`,
    data,
  })
}

// 查询用户上传附件的操作权限等信息
export const getYearScoreAttInfoService = (data) => {
  return request({
    method: 'get',
    url: `${BASEURL}/getYearScoreAttInfo`,
    data,
  })
}
