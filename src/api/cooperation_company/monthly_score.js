import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl + '/partnerevalrpt'
// 得分列表
export const queryListService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/findMonthlyScoreRptPage`,
    data
  })
}
// 扣分列表
export const queryDeductionListService = (data) => {
    return request({
      method: 'post',
      url: BASEURL + `/findMonthlyDeductionScoreRptPage`,
      data
    })
  }
// 合作单位考核月度得分--导出
export const exportMonthlyExcelService = () => {
    return  BASEURL + `/exportMonthlyScoreRpt`
}
// 合作单位考核月度得分--导出
export const exportDeductionExcelService = () => {
  return  BASEURL + `/exportMonthlyDeductionScoreRpt`
}
// 合作单位考核月度得分详情--导出
export const exportMSDetailExcelService = () => {
  return  BASEURL + `/exportMonthlyScoreDetailRpt`
}
// 月度得分详情
export const queryMonthlyScoreDetaiService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/findMonthlyScoreDetailRptPage`,
    data
  })
}
// 获取合作单位评分权重配置列表
export const getWeightInfoService = (params) => {
  return request({
    method: 'get',
    url: BASEURL + `/getWeightInfo`,
    params
  })
}
// 保存合作单位评分权重配置列表
export const saveWeightInfoService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/saveWeightInfo`,
    data
  })
}

