import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl
// 列表
export const queryListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/findPage`,
    params: reqData
  })
}
// 根据用户表DeptId获取合作单位信息
export const queryCompanyByDeptIdService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/getByDeptId/${reqData}`
  })
}
// 详情
export const queryDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/${reqData}`
  })
}
// 删除
export const removeIdsService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerDeptController/batchremove`,
    params: reqData
  })
}
// 导出合作单位主单列表
export const exportPageService = (data) => {
  return BASEURL + `/partnerDeptController/exportPage`
}
// 导出附件
export const exportAttachmentService = (data) => {
  return BASEURL + `/partnerDeptController/downloadZip`
}
// 保存主单信息
export const saveInfoService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerDeptController`,
    data: reqData
  })
}
// 修改主单信息
export const updateInfoService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/partnerDeptController`,
    data: reqData
  })
}
// 账号信息
export const accListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/acc/page`,
    params: reqData
  })
}
// 机构信息
export const orgListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/org/page`,
    params: reqData
  })
}
// 资质信息
export const certListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/cert/page`,
    params: reqData
  })
}
// 高管信息列表
export const seniorListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/manager/findPage`,
    params: reqData
  })
}
// 高管信息新增
export const seniorSaveService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerDeptController/manager/save`,
    data: reqData
  })
}
// 高管信息修改
export const seniorUpdateService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/partnerDeptController/manager/updateById`,
    data: reqData
  })
}
// 高管信息-查询详情
export const seniorDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/manager/${reqData}`
  })
}
// 高管信息-删除
export const seniorBatchremoveService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerDeptController/manager/batchremove`,
    params: reqData
  })
}

// 高管信息-导出
export const seniorExportExcelService = (reqData) => {
  return BASEURL + `/partnerDeptController/manager/exportExcel`
}
// 高管信息-导入
export const seniorImportExcelService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerDeptController/manager/importExcel`,
    data: reqData
  })
}

// 单位相关账号信息列表
export const farmerAccListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/bankAcc/findPage`,
    params: reqData
  })
}
// 单位相关账号信息新增
export const farmerAccSaveService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerDeptController/bankAcc/save`,
    data: reqData
  })
}
// 单位相关账号信息修改
export const farmerAccUpdateService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/partnerDeptController/bankAcc/updateById`,
    data: reqData
  })
}
// 单位相关账号信息-查询详情
export const farmerAccDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/bankAcc/${reqData}`
  })
}
// 单位相关账号信息-删除
export const farmerAccBatchremoveService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerDeptController/bankAcc/batchremove`,
    params: reqData
  })
}
// 单位相关账号信息-导出
export const farmerAccExportExcelService = (reqData) => {
  return BASEURL + `/partnerDeptController/bankAcc/exportExcel`
}
// 单位相关账号信息-导入
export const farmerAccImportExcelService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerDeptController/bankAcc/importExcel`,
    data: reqData
  })
}
// 合作单位-提交时校验是否存在流程中工单(true:没有在流程中的，可以新增；false:有流程中工单，不能新增)
export const notExistsProcessService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerDeptController/notExistsProcess/${reqData}`
  })
}
