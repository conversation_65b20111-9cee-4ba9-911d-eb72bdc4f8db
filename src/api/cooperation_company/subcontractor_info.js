import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl
// 列表
export const queryListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/subcontractor/findPage`,
    params: reqData
  })
}
// 导出合作单位主单列表
export const exportPageService = (data) => {
  return BASEURL + `/subcontractor/detail/exportPage`
}
// 导出附件
export const exportAttachmentService = (data) => {
  return BASEURL + `/subcontractor/downloadZip`
}
// 根据用户表DeptId获取合作单位信息
export const queryCompanyByDeptIdService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/cooperationCompany/findByDeptId`,
    params: reqData
  })
}
// 通过部门id查询（多次修改带出最新一次数据）
export const subcontractorByDeptIdService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/subcontractor/getByDeptId/${reqData}`
  })
}
// 保存信息
export const saveService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/subcontractor`,
    data: reqData
  })
}
// 修改信息
export const updateService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/subcontractor`,
    data: reqData
  })
}
// 删除
export const removeIdsService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/subcontractor/batchremove`,
    params: reqData
  })
}
// 详情
export const detailService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/subcontractor/${data}`
  })
}
// 分包信息列表
export const subcontractorListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/subcontractor/detail/findPage`,
    params: reqData
  })
}
// 分包信息新增
export const subcontractorSaveService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/subcontractor/detail/save`,
    data: reqData
  })
}
// 分包信息修改
export const subcontractorUpdateService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/subcontractor/detail/updateById`,
    data: reqData
  })
}
// 分包信息-查询详情
export const subcontractorDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/subcontractor/detail/${reqData}`
  })
}
// 分包信息-删除
export const subcontractorBatchremoveService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/subcontractor/detail/batchremove`,
    params: reqData
  })
}
// 分包信息-导出
export const subcontractorExportExcelService = (reqData) => {
  return BASEURL + `/subcontractor/detail/exportExcel`
}
// 分包信息-导入
export const subcontractorImportExcelService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/subcontractor/detail/importExcel`,
    data: reqData
  })
}
// 合作单位分包信息-提交时校验是否存在流程中工单(true:没有在流程中的，可以新增；false:有流程中工单，不能新增)
export const notExistsProcessService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/subcontractor/notExistsProcess/${reqData}`
  })
}
