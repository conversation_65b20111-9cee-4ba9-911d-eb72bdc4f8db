import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl
// 考核任务模板列表
export const templateListService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerTplController/findPage`,
    data: reqData
  })
}
// 考核任务模板新增/修改
export const saveTemplateService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerTplController/save`,
    data: reqData
  })
}
// 考核任务模板复制
export const copyTemplateService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerTplController/copyTpl`,
    data: reqData
  })
}
// 考核任务模板详情
export const detailTemplateService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerTplController/info`,
    params: reqData
  })
}
// 考核任务模板删除
export const deleteTemplateService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerTplController/delete`,
    params: reqData
  })
}
// 考核任务模板批量删除
export const deleteAllTemplateService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerTplController/deleteAll`,
    data: reqData
  })
}
// 唯一性校验,fail:存在相同数据,success:没有相同数据
export const uniquenessCheckService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerTplController/uniquenessCheck`,
    params: reqData
  })
}
// 发布
export const toPublishService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerTplController/toPublish`,
    params: reqData
  })
}
// 启用、禁用-模板
export const switchEnableService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerTplController/switchEnable`,
    params: reqData
  })
}
// 根据专业筛查单位
export const checkUnitService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerTplController/checkUnit`,
    params: reqData
  })
}

export const initRoleListService = (reqData = {}) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerTplController/initRoleList`,
    params: reqData
  })
}
