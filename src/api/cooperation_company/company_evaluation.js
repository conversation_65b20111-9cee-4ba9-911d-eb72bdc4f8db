import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl
// 列表
export const queryListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnereval/findPage`,
    params: reqData
  })
}
// 详情-合作单位
export const queryDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnereval/${reqData}`
  })
}
// 详情-考核模板详细
export const templateDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnereval/getDetails`,
    params: reqData
  })
}
// 保存信息
export const saveService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnereval`,
    data: reqData
  })
}
// 导出
export const exportService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnereval/exportPage`,
    data: reqData
  })
}

// 导出
export const exportMonthlyExcelService = () => {
  return  BASEURL + `/partnereval/exportData`
}
