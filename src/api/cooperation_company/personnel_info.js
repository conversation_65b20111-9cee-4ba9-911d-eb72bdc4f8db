import request from '@/utils/request.js'
const BASEURL = window.g.basePartnerUrl
// 根据用户表userId获取合作单位信息
export const getCompanyByUserIdService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/cooperationCompany/findByUserId`,
    params: reqData
  })
}
// 根据用户id获取合作单位人员信息
export const getPersonByUserIdService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/getByUserId/${reqData}`
  })
}
// 列表
export const queryListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/findPage`,
    params: reqData
  })
}
// 账号信息
export const accListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/acc/findPage`,
    params: reqData
  })
}
// 角色信息
export const roleListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/role/findPage`,
    params: reqData
  })
}
// 综合管理员权限
export const roleListZHYService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/role/findPageRoleZHY`,
    params: reqData
  })
}
// 劳动合同信息
export const laborContListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/laborCont/findPage`,
    params: reqData
  })
}
// 资质信息
export const certContListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/cert/findPage`,
    params: reqData
  })
}
// 详情
export const queryDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/${reqData}`
  })
}
// 删除
export const removeIdsService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerUserController/batchremove`,
    params: reqData
  })
}
// 保存主单信息
export const saveInfoService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerUserController`,
    data: reqData
  })
}
// 修改主单信息
export const updateInfoService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/partnerUserController`,
    data: reqData
  })
}
// 导出合作单位主单列表
export const exportPageService = (data) => {
  return BASEURL + `/partnerUserController/exportPage`
}
// 导出附件
export const exportAttachmentService = (data) => {
  return BASEURL + `/partnerUserController/downloadZip`
}
// 保险信息列表
export const insuranceListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/userIns/findPage`,
    params: reqData
  })
}
// 保险信息新增
export const insuranceSaveService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerUserController/userIns/save`,
    data: reqData
  })
}
// 保险信息修改
export const insuranceUpdateService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/partnerUserController/userIns/updateById`,
    data: reqData
  })
}
// 保险信息-查询详情
export const insuranceDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/userIns/${reqData}`
  })
}
// 保险信息-删除
export const insuranceBatchremoveService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerUserController/userIns/batchremove`,
    params: reqData
  })
}
// 保险信息-导出
export const insuranceExportExcelService = (reqData) => {
  return BASEURL + `/partnerUserController/userIns/exportExcel`
}
// 保险信息-导入
export const insuranceImportExcelService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerUserController/userIns/importExcel`,
    data: reqData
  })
}

// 培训信息列表
export const trainingListService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/userTrain/findPage`,
    params: reqData
  })
}
// 培训信息新增
export const trainingSaveService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerUserController/userTrain/save`,
    data: reqData
  })
}
// 培训信息修改
export const trainingUpdateService = (reqData) => {
  return request({
    method: 'put',
    url: BASEURL + `/partnerUserController/userTrain/updateById`,
    data: reqData
  })
}
// 培训信息-查询详情
export const trainingDetailService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/userTrain/${reqData}`
  })
}
// 培训信息-删除
export const trainingBatchremoveService = (reqData) => {
  return request({
    method: 'DELETE',
    url: BASEURL + `/partnerUserController/userTrain/batchremove`,
    params: reqData
  })
}
// 培训信息-导出
export const trainingExportExcelService = (reqData) => {
  return BASEURL + `/partnerUserController/userTrain/exportExcel`
}
// 培训信息-导入
export const trainingImportExcelService = (reqData) => {
  return request({
    method: 'post',
    url: BASEURL + `/partnerUserController/userTrain/importExcel`,
    data: reqData
  })
}
// 合作单位人员-提交时校验是否存在流程中工单(true:没有在流程中的，可以新增；false:有流程中工单，不能新增)
export const notExistsProcessService = (reqData) => {
  return request({
    method: 'get',
    url: BASEURL + `/partnerUserController/notExistsProcess/${reqData}`
  })
}
