import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
// 获取列表
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/safetyReport/findPage`,
    params
  })
}

//通过id查询
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/safetyReport/${params}`,
  })
}

// 获取安全管理员
export const getSafetyManagerService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/safetyReport/getSafetyManager`,
    params
  })
}

// 获取安全管理员
export const getDeptReportRoleUsersService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/safetyReport/getDeptReportRoleUsers`,
    params
  })
}