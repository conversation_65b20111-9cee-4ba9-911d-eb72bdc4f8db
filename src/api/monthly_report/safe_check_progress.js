/**
 * @author: ty
 * @date: 2023-07-08
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const safeCheckBackBaseUrl = `${baseUrl}/safeCheckBack`
// 查询
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckBackBaseUrl}/getProgress`,
    params
  })
}
export const sendService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckBackBaseUrl}/send`,
    params
  })
}
export const getUserInfoService = () => {
  return request({
    method: 'get',
    url: `${safeCheckBackBaseUrl}/getCurrentUserInfo`,
  })
}
