/**
 * @author: ty
 * @date: 2023-07-08
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const safeCheckBaseUrl = `${baseUrl}/safeCheck`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckBaseUrl}`,
    params
  })
}
// 批量保存检查表
export const saveService = (data) => {
  return request({
    method: 'post',
    url: `${safeCheckBaseUrl}`,
    data
  })
}
// 批量保存检查表校验
export const beforeSaveService = () => {
  return request({
    method: 'post',
    url: `${safeCheckBaseUrl}/beforeSaveCheck`,
  })
}
// 详细列表
export const detailPageService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckBaseUrl}/findBackPage`,
    params
  })
}
// 详细列表
export const detailViewPageService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckBaseUrl}/findBackViewPage`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckBaseUrl}/${params}`,
  })
}
// 批量保存检查表
export const updateService = (data) => {
  return request({
    method: 'put',
    url: `${safeCheckBaseUrl}`,
    data
  })
}
// 删除检查表
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${safeCheckBaseUrl}/${params}`,
  })
}
// 批量删除检查表
export const delBatchService = (params) => {
  return request({
    method: 'delete',
    url: `${safeCheckBaseUrl}/removeBatch`,
    params
  })
}

// 提交前校验
export function beforeSubmitService(params){
  return request({
    method:'get',
    url: `${safeCheckBaseUrl}/beforeSubmitCheck` ,
    params
  })
}
