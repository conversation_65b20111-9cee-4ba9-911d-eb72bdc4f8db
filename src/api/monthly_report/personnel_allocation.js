/**
 * @author: hcm
 * @date: 2023-07-24
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const personnelAllocationBaseUrl = `${baseUrl}/templetesupport`
export const queryListService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/filingofficerquery`,
    data: params
  })
}
// 新增、编辑
export const filingofficersaveUpdateService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/filingofficersaveupdate`,
    data: params
  })
}
