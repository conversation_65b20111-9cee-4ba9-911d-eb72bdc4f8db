/**
 * @author: ty
 * @date: 2023-07-08
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const warningBaseUrl = `${baseUrl}/safeCheckWarning`
// 查询列表
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${warningBaseUrl}`,
    params
  })
}
// 保存配置
export const saveService = (data) => {
  return request({
    method: 'post',
    url: `${warningBaseUrl}`,
    data
  })
}
// 修改配置
export const updateService = (data) => {
  return request({
    method: 'put',
    url: `${warningBaseUrl}`,
    data
  })
}
// 批量修改配置
export const updateBatchService = (data) => {
  return request({
    method: 'put',
    url: `${warningBaseUrl}/updateBatchById`,
    data
  })
}
// 删除配置
export const deleteOneService = (params) => {
  return request({
    method: 'delete',
    url: `${warningBaseUrl}`,
    params
  })
}
// 批量删除配置
export const deleteBatchService = (params) => {
  return request({
    method: 'delete',
    url: `${warningBaseUrl}/removeBatch`,
    params
  })
}
// 启用/禁用校验
export const activeCheckService = (data) => {
  return request({
    method: 'post',
    url: `${warningBaseUrl}/activeCheck`,
    data
  })
}
// 启用/禁用
export const activeService = (data) => {
  return request({
    method: 'post',
    url: `${warningBaseUrl}/activeById`,
    data
  })
}
