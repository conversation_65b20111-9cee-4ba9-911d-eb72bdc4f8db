import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl + '/monthreportschedule'

// 列表
export const findPageService = (data) => {
    return request({
      method: 'POST',
      url: baseUrl + '/totalpage',
      data
    })
  }
  // 详情列表
export const findPageDetailService = (data) => {
  return request({
    method: 'POST',
    url: baseUrl + '/itempage',
    data
  })
}
// 督办
export const noticeService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + '/notice',
    params: data
  })
}
// 导出详情
export const exportDetilService = (data) => {
  return request({
    method: 'post',
    url: baseUrl + '/exportitems',
    data,
    responseType: "blob"
  })
}