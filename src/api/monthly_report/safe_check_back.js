/**
 * @author: ty
 * @date: 2023-07-08
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const safeCheckBackBaseUrl = `${baseUrl}/safeCheckBack`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckBackBaseUrl}`,
    params
  })
}
export const getByIdService = (urlParams, params) => {
  return request({
    method: 'get',
    url: `${safeCheckBackBaseUrl}/${urlParams}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckBackBaseUrl}/getDetail`,
    params
  })
}
// 批量保存检查表
export const updateService = (data) => {
  return request({
    method: 'put',
    url: `${safeCheckBackBaseUrl}`,
    data
  })
}
// 批量保存检查表
export const saveDetailsService = (data) => {
  return request({
    method: 'post',
    url: `${safeCheckBackBaseUrl}/updateDetials`,
    data
  })
}

// 删除检查反馈表
export const delService = (params) => {
  return request({
    method: 'delete',
    url: `${safeCheckBackBaseUrl}/${params}`,
  })
}
// 批量删除检查反馈表
export const delBatchService = (params) => {
  return request({
    method: 'delete',
    url: `${safeCheckBackBaseUrl}/removeBatch`,
    params
  })
}

// 提交前校验
export function beforeSubmitService(params){
  return request({
    method:'get',
    url: `${safeCheckBackBaseUrl}/beforeSubmitCheck` ,
    params
  })
}

// 附件下载
export function getFilesDownloadService() {
  return `${safeCheckBackBaseUrl}/downloadZip`
}
