/*
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-27 15:25:26
 * @Description: 安全费支付管理
 */

import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl + '/taskScheduleController/'

// 项目维度列表
export const findProjectListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findProjectList',
    params: data
  })
}
export const exportProjectListService = () => {
  return baseUrl + 'exportProjectList'
}
// 批次维度列表
export const findBatchListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findProjectBatchList',
    params: data
  })
}
export const exportBatchListService = () => {
  return baseUrl + 'exportProjectBatchList'
}
// 任务维度列表
export const findTaskListService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + 'findTaskScheduleList',
    params: data
  })
}
export const exportTaskListService = () => {
  return baseUrl + 'exportTaskScheduleList'
}