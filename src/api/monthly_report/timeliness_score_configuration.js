/**
 * @author: hcm
 * @date: 2023-07-24
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const personnelAllocationBaseUrl = `${baseUrl}/templetesupport`
export const queryListService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/intimequery`,
    data: params
  })
}
// 保存
export const intimesaveUpdateService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/intimesaveupdate`,
    data: params
  })
}
// 删除
export const intimeDeleteService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/intimeDelete`,
    params
  })
}
