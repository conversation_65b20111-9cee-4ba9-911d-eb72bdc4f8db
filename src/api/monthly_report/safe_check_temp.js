/**
 * @author: ty
 * @date: 2023-07-08
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const safeCheckTempBaseUrl = `${baseUrl}/safeCheckTemp`
// 流程记录
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckTempBaseUrl}`,
    params
  })
}
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${safeCheckTempBaseUrl}/getDetail`,
    params
  })
}
// 批量保存检查表
export const saveDetailsService = (data) => {
  return request({
    method: 'post',
    url: `${safeCheckTempBaseUrl}`,
    data
  })
}
