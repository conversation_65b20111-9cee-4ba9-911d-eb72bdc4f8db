/**
 * @author: ty
 * @date: 2023-07-21
 * @description:
 */
import request from "@/utils/request"
const BASEURL = window.g.baseMonthlyUrl
const accountAgreeBaseUrl = `${BASEURL}/accountagree`

export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${accountAgreeBaseUrl}/findPage`,
    params
  })
}

export const reportFindPageService = (params) => {
  return request({
    method: 'get',
    url: `${accountAgreeBaseUrl}/reportFindPage`,
    params
  })
}

export const exportExcelService = (data) => {
  return request({
    method: 'post',
    url: `${accountAgreeBaseUrl}/exportExcel`,
    data,
    responseType: 'blob'
  })
}

export const exportItemExcelService = (data) => {
  return request({
    method: 'post',
    url: `${accountAgreeBaseUrl}/exportItemExcel`,
    data,
    responseType: 'blob'
  })
}

export const exportReportService = (data) => {
  return request({
    method: 'post',
    url: `${accountAgreeBaseUrl}/exportReport`,
    data,
    responseType: 'blob'
  })
}

export const importExcelService = (data) => {
  return request({
    method: 'post',
    url: `${accountAgreeBaseUrl}/importExcel`,
    data,
  })
}

// 通过id查询
export const accountagreeGetByIdService = (id) => {
  return request({
    method: 'get',
    url: `${accountAgreeBaseUrl}/${id}`
  })
}

export const findTaskPageService = (params) => {
  return request({
    method: 'get',
    url: `${accountAgreeBaseUrl}/findTaskPage`,
    params
  })
}

// 提交前校验
export function checknewprojectService(params){
  return request({
    method: 'get',
    url: `${accountAgreeBaseUrl}/checknewproject`,
    params
  })
}

// 空间资源入网功能-查询综资同步接口数据
export function queryIrmsResourcePageService(params){
  return request({
    method: 'get',
    url: `${accountAgreeBaseUrl}/findIrmsResourcePage`,
    params
  })
}
// 导出空间资源入网
export function exportListService(){
  return `${accountAgreeBaseUrl}/exportIrmsResourceExcel`
}
