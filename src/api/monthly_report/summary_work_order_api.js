import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
// 获取列表
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/safetyReportCity/findPage`,
    params
  })
}

//通过id查询
export const getDetailService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/safetyReportCity/${params}`,
  })
}

// 获取月报总结数据
export const getSafetyFinalDtoService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/safetyReportCity/getSafetyFinalDto`,
    params
  })
}

// 根据地市id获取监理人员
export const supervisorcitygetbycityIdService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/templetesupport/supervisorcitygetbycityId`,
    params
  })
}