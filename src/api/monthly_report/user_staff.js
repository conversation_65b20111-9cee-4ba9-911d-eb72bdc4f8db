/**
 * @author: ty
 * @date: 2023-07-08
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const userStaffBaseUrl = `${baseUrl}/userStaff`
// 查询列表
export const findPageService = (params) => {
  return request({
    method: 'get',
    url: `${userStaffBaseUrl}`,
    params
  })
}
// 保存配置
export const saveService = (data) => {
  return request({
    method: 'post',
    url: `${userStaffBaseUrl}`,
    data
  })
}
// 修改配置前校验
export const updateCheckService = (data) => {
  return request({
    method: 'post',
    url: `${userStaffBaseUrl}/checkById`,
    data
  })
}
// 修改配置
export const updateService = (data) => {
  return request({
    method: 'put',
    url: `${userStaffBaseUrl}`,
    data
  })
}
// 批量修改配置前校验
export const updateBatchCheckService = (data) => {
  return request({
    method: 'post',
    url: `${userStaffBaseUrl}/checkBatchById`,
    data
  })
}
// 批量修改配置
export const updateBatchService = (data) => {
  return request({
    method: 'put',
    url: `${userStaffBaseUrl}/updateBatchById`,
    data
  })
}
// 删除配置
export const deleteOneService = (params) => {
  return request({
    method: 'delete',
    url: `${userStaffBaseUrl}`,
    params
  })
}
// 批量删除配置
export const deleteBatchService = (params) => {
  return request({
    method: 'delete',
    url: `${userStaffBaseUrl}/removeBatch`,
    params
  })
}
