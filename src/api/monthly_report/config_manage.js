/*
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-24 11:22:07
 * @Description:月报-配置管理
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl + '/templetesupport'

// 列表
export const findPageService = (data) => {
    return request({
      method: 'POST',
      url: baseUrl + '/timeconfigquery',
      data
    })
  }
// 派发时间配置保存
export const timeConfigSaveService = (data) => {
  return request({
    method: 'POST',
    url: baseUrl + '/timeconfigsave',
    data
  })
}
// 流程环节查询
export const workNodeQueryService = (data) => {
  return request({
    method: 'POST',
    url: baseUrl + '/worknodequery',
    data
  })
}
// 流程环节配置保存
export const workNodeSaveUpdateService = (data) => {
  return request({
    method: 'POST',
    url: baseUrl + '/worknodesaveupdate',
    data
  })
}

// 监理地市查询
export const supervisorqueryService = (data) => {
  return request({
    method: 'POST',
    url: baseUrl + '/supervisorquery',
    data
  })
}

// 监理地市新增修改
export const supervisorcitysaveupdateService = (data) => {
  return request({
    method: 'POST',
    url: baseUrl + '/supervisorcitysaveupdate',
    data
  })
}
// 监理地市删除
export const supervisorcityDeleteService = (data) => {
  return request({
    method: 'get',
    url: baseUrl + '/supervisordelete',
    params: data
  })
}

// 获取角色
export const getReportRolesService = (reqData) => {
  return request({
    method: 'get',
    url: window.g.baseMonthlyUrl + `/reportTemplate/getReportRoles`,
    params: reqData
  })
}