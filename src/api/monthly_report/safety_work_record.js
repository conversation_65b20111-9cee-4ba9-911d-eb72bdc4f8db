/**
 * @author: hcm
 * @date: 2023-07-24
 * @description:
 */
import request from '@/utils/request.js'
const baseUrl = window.g.baseMonthlyUrl
const personnelAllocationBaseUrl = `${baseUrl}/templetesupport`
export const queryListService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/worksafetyquery`,
    data: params
  })
}
export const updateService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/worksafetysaveupdate`,
    data: params
  })
}
export const getInfoService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/worksafetyinfo`,
    params
  })
}
// 删除
export const worksafetyDeleteService = (params) => {
  return request({
    method: 'post',
    url: `${personnelAllocationBaseUrl}/worksafetyDelete`,
    params
  })
}
