// 费用进度-api
import request from '@/utils/request'
const filesServiceUrl = window.g.baseProjectUrl + '/designWarn/'

// 列表
export function queryListService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'findPage' ,
    params: data
  })
}

// 删除
export function delService(data) {
  return request({
    method: 'get',
    url: filesServiceUrl + 'delete',
    params: data
  })
}
// 保存
export function saveService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'saveDesignWarn',
    data: data
  })
}
// 保存--全量
export function saveAllService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'save',
    data: data
  })
}
