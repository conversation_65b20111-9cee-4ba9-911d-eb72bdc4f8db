// 费用修改api
import request from '@/utils/request'
const filesServiceUrl = window.g.baseProjectUrl + '/designFeeConfirmChange/'

// 列表
export function queryListService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'findPage' ,
    params: data
  })
}

// 发起修改
export function confirmService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'saveNewDesignFee' ,
    data: data
  })
}

// 查询详情--
export function queryDetailService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getInfo' ,
    params: data
  })
}
// 项目设计费信息
export function queryProjectInfoService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getProjectDesignFee' ,
    params: data
  })
}

// 项目设计费信息--任务明细
export function queryTaskInfoService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getTaskDesignFee' ,
    params: data
  })
}

// 项目设计费信息--任务导出
export function exportTaskService(data){
  return filesServiceUrl + 'exportTaskData'
}

//导入
export function importService(data) {
  return request({
    method: 'post',
    url: filesServiceUrl + 'importExcel',
    data: data
  })
}

// 设计通用合同
export function getContrctService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getContract' ,
    params: data
  })
}

// 保存
export function saveService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'saveDesignFeeChange' ,
    data: data
  })
}
// 提交前校验
export function beforeSubmitService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'attValidate' ,
    params: data
  })
}
// 删除
export function delService(data){
  return request({
    method:'delete',
    url:filesServiceUrl + 'batchremove' ,
    params: data
  })
}
