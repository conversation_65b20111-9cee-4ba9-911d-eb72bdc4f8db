// 费用确认人工--api
import request from '@/utils/request'
const filesServiceUrl = window.g.baseProjectUrl + '/designFeeConfirm/'

// 列表
export function queryListService(data){
  return request({
    method:'get',
    url:filesServiceUrl + 'getDesignFeeApply' ,
    params: data
  })
}
// 发起人工确认
export function confirmService(data){
  return request({
    method:'post',
    url:filesServiceUrl + 'saveNewDesignFee' ,
    data: data
  })
}
