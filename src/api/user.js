import request from '@/utils/request'
const baseUrl = window.g.baseUrl

export function login(params) {
  return request({
    url: `${baseUrl}/auth/oauth/token`,
    method: 'post',
    params
  })
}

export function logout() {
  return request({
    url: `${baseUrl}/auth/token/logout`,
    method: 'delete'
  })
}
// 获取图片验证码
export function getPicCodeService(data) {
  return request({
    method: 'get',
    url: `${baseUrl}/code`,
    params: data,
    responseType: 'blob'
  })
}
// 获取用户菜单
export function getUserMenuService(data){
  return request({
    method: 'get',
    url: `${baseUrl}/admin/sysresource/tree/user`,
    params: data
  })
}

// 首页-保存快速导航
export function saveShortCutService(data) {
  return request({
    method: "post",
    url: baseUrl + "/admin/shortcuts/saveShortCut",
    params: data,
  });
}

export function getShortCutService(data) {
  return request({
    method: "get",
    url: baseUrl + "/admin/shortcuts/showShortCut",
    params: data,
  });
}

// 修改密码api
export const changePasswordService = (data) => {
  return request({
      method: 'put',
      url: baseUrl + '/admin/sysuser/password',
      data: data
  })
};

// 获取手机验证码
export function genVerifyCode(data){
  return request({
    method: 'post',
    url: baseUrl + '/admin/pwd/find/genVerifyCode',
    params: data
  })
}

// 验证码验证
export function checkVerifyCode(data){
  return request({
    method: 'post',
    url: baseUrl + '/admin/pwd/find/checkVerifyCode',
    params: data
  })
}

// 确认修改密码
export function accountandverifycode(data){
  return request({
    method: 'GET',
    url: baseUrl + '/admin/sysuser/retrieved/accountandverifycode',
    params: data
  })
}

// 新增PV日志数据表
export function logpagevisit (data) {
  return request({
    url: baseUrl + '/logger/logpagevisit',
    method: 'post',
    data
  })
}

// 发送短信验证码
export const sendMsgService = (data) => {
  return request({
      method: 'post',
      url: baseUrl + '/auth/verify-code/send',
      params: data
  })
};


