/**
 * @author: hcm
 * @date: 2023-07-10
 * @description:选部门
 */
import request from '@/utils/request.js'

const BASEURL = `${window.g.baseUrl}/admin`
// 部门树形
export const getDeptService = (params) => {
  return request({
    method: 'post',
    url: `${BASEURL}/sysuser/queryUserTree`,
    data: params
  })
}
// 人员列表
export const getUserListService = (params) => {
  return request({
    method: 'GET',
    url: `${BASEURL}/sysuser/page`,
    params
  })
}
