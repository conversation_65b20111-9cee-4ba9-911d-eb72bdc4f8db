import { decryption } from '@/utils/encryption'
if (window.g.baseUrl && !(window.g.baseUrl.startsWith('http:') || window.g.baseUrl.startsWith('https:'))) {
  window.g.baseUrl = decryption({ data: window.g.baseUrl, key: '1234567890123456' })
}

if (window.g.flowPreviewUrl && !(window.g.flowPreviewUrl.startsWith('http:') || window.g.flowPreviewUrl.startsWith('https:'))) {
  window.g.flowPreviewUrl = decryption({ data: window.g.flowPreviewUrl, key: '1234567890123456' })
}

if (window.g.auth_cas_url && !(window.g.auth_cas_url.startsWith('http:') || window.g.auth_cas_url.startsWith('https:'))) {
  window.g.auth_cas_url = decryption({ data: window.g.auth_cas_url, key: '1234567890123456' })
}
// 公共服务、部门专业树
window.g.baseCommonUrl = window.g.baseUrl + '/common-center-jx'

// 合作单位
window.g.basePartnerUrl = window.g.baseUrl + '/partner-center-jx'

// 集客专线
window.g.baseLineUrl = window.g.baseUrl + '/groupcustomer-center-jx'

// 工程实施（包含设计环节）
window.g.baseProjectUrl = window.g.baseUrl + '/project-center-jx'

// 物资、暂存点等
window.g.baseMaterialUrl = window.g.baseUrl + '/material-center-jx'

// 月报（月报模板、账实相符）
window.g.baseMonthlyUrl = window.g.baseUrl + '/monthly-report-jx'

// 接口
window.g.baseInterfaceUrl = window.g.baseUrl + '/interface-center-jx'

//pccw业务
window.g.comprehensiveUrl = window.g.baseUrl + '/comprehensive-center-jx'

//pccw-Marche-表
window.g.flowableServiceUrl = window.g.baseUrl + '/pccw-flowable-center-jx'

//pccw-Marche-接口
window.g.flowUrl = window.g.baseUrl + '/pccw-flowable-center-jx'

//pccw-Marche-表
window.g.flowMaintenanceUrl = window.g.baseUrl + '/pccw-flow-maintenance-center-jx'

// 集客专线-二期拓展
window.g.baseExtendLineUrl = window.g.baseUrl + '/extend-groupcustomer-center-jx'
// 数字赋能
window.g.baseEmpowermentUrl = window.g.baseUrl + '/project-empowerment-jx-sjp'
