/*
 * @File name: 
 * @Author: LT
 * @Date: 2023-07-07 18:24:44
 * @Description: 物资管理-完工稽核api
 */

import request from '@/utils/request.js'
const baseUrl = window.g.baseMaterialUrl
const completeAuditBaseUrl = baseUrl + '/completeauditController'
// 列表
export const findPageService = (data) => {
    return request({
      method: 'POST',
      url: `${completeAuditBaseUrl}/findPage`,
      params: data
    })
  }
// 详情
export const detailPageService = (data) => {
    return request({
      method: 'post',
      url: `${completeAuditBaseUrl}/detail/findPage`,
      data
    })
  }
export const exportExcelService = () => {
  return `${completeAuditBaseUrl}/exportPage`
}
// 导出详情
export const exportDetailExcelService = () => {
  return `${completeAuditBaseUrl}/、detail/exportPage`
}
// 通过id查询物资填报信息
export const getMaterialInfoById = (data) => {
  return request({
    method: 'get',
    url:  baseUrl + `/usageRptController/${data}`,
  })
}