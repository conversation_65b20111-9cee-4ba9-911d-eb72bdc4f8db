import request from '@/utils/request.js'
const baseUrl = window.g.baseMaterialUrl
const receiveAuditBaseUrl = baseUrl + '/receiveauditController'
// 列表
export const findPageService = (data) => {
  return request({
    method: 'get',
    url: `${receiveAuditBaseUrl}/findPage`,
    params: data
  })
}
// 列表-导出
export const exportPageService = () => {
  return `${receiveAuditBaseUrl}/exportPage`
}
// 详情-物料列表
export const receiveauditdetailService = (params) => {
  return request({
    method: 'get',
    params:params,
    url: `${baseUrl}/receiveauditdetailController/findByAuditId`
  })
}
// 详情-物料列表导出
export const exportReceiveaudit = () => {
  return `${baseUrl}/receiveauditdetailController/exportPage`
}
// 详情-项目信息
export const auditDetailService = (params) => {
  return request({
    method: 'get',
    url: `${receiveAuditBaseUrl}/${params}`
  })
}
