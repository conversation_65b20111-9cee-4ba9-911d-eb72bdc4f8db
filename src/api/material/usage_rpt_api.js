import request from '@/utils/request.js'
const BASEURL = window.g.baseMaterialUrl
// 获取物资申报待办列表
export const findPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/usageRptController/findPage`,
    params: data
  })
}

// 通过id查询物资填报信息
export const getDetailsById = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/usageRptController/${data}`,
  })
}

// 获取物资出入库列表
export const detailFindPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/usageRptController/detail/findPage`,
    params: data
  })
}

// 物资填报信息-导出
export const exportExcelService = (data) => {
  return BASEURL + '/usageRptController/detail/exportExcel'
}

// 物资填报信息-导入
export const importExcelService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/usageRptController/detail/importExcel`,
    data: data
  })
}

// 获取物资修订待办列表
export const findModPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/usageRptModController/findPage`,
    params: data
  })
}

// 通过历史id初始化使用量修订详情信息
export const getDetailsByHisId = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/usageRptModController/getByhisId/${data}`,
  })
}

//保存主单详细信息
export const savePageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/usageRptModController`,
    data: data
  })
}

// 获取物资出入库列表
export const findHistoryPageService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/usageRptModController/detail/findHistoryPage`,
    params: data
  })
}

// 通过id查询修订填报信息
export const getModDetailsById = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/usageRptModController/${data}`,
  })
}

// 获取使用量修订明细详情信息列表
export const detailFindModPageService = (data) => {
  return request({
    method: 'get',
    url: BASEURL + `/usageRptModController/detail/findPage`,
    params: data
  })
}

// 导出物资使用填报信息 (历史)
export const exportHisExcelService = () => {
  return BASEURL + '/usageRptModController/detail/exportHisExcel'
}

// 物资修订信息-导入
export const importModExcelService = (data) => {
  return request({
    method: 'post',
    url: BASEURL + `/usageRptModController/detail/importExcel`,
    data: data
  })
}

//使用量修订导入模板下载
export const exportTemplateService = () => {
  return BASEURL + '/usageRptModController/detail/exportTemplate'
}

// 填报提交前校验
export function flowValidateService(data){
  return request({
    method:'get',
    url:BASEURL + '/usageRptController/detail/flowValidate' ,
    params: data
  })
}

// 修订提交前校验
export function modFlowValidateService(data){
  return request({
    method:'get',
    url:BASEURL + '/usageRptModController/detail/flowValidate' ,
    params: data
  })
}

// 通过id批量删除流程工单
export const multipleDelService = (data) => {
  return request({
    method: 'delete',
    url: BASEURL + `/usageRptModController/batchremove`,
    params: data
  })
}

// 通过id删除流程工单
export const singleDelService = (data) => {
  return request({
    method: 'delete',
    url: BASEURL + `/usageRptModController/${data}`,
  })
}