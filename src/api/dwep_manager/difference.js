
/*
 * @File name: dwepWhiteListTable.js
 * @Author: LT
 * @Date: 2024-03-15
 * @Description: 物资支出预警管理接口服务
 */

import request from '@/utils/request.js'

// 基础路径（根据你的实际项目配置）
const baseUrl = window.g.baseEmpowermentUrl +'/taskDifference'

const baseUrlx = window.g.baseEmpowermentUrl +'/dwepTaskBalances'

const baseUrlxc = 'http://localhost:7777/dwepTaskBalances';

export const page = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/page`,
    params: params
  })
}
export const pages = (params) => {
  return request({
    method: 'get',
    url: `${baseUrlx}/page`,
    params: params
  })
}
