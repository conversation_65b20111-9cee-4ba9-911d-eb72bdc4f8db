
/*
 * @File name: dwepWhiteListTable.js
 * @Author: LT
 * @Date: 2024-03-15
 * @Description: 物资支出预警管理接口服务
 */

import request from '@/utils/request.js'

// 基础路径（根据你的实际项目配置）
const baseUrl =window.g.baseEmpowermentUrl +'/dwepMaterialExpenseAlertTask'



// 基础路径（根据你的实际项目配置）
const baseUrls =window.g.baseEmpowermentUrl +'/groupDwepTaskBalanceAction'

const baseUrlsx ='http://localhost:7777/groupDwepTaskBalanceAction'
export const page = (params) => {
  return request({
    method: 'post',
    url: `${baseUrl}/page`,
    data: params
  })
}

export const getInfo = (boId) => {
  return request({
    method: 'get',
    url: `${baseUrl}/getInfo?boId=` + boId,
  })
}

export const pages = (params) => {
  return request({
    method: 'post',
    url: `${baseUrls}/page`,
    data: params
  })
}

export const getInfos = (boId) => {
  return request({
    method: 'get',
    url: `${baseUrls}/getInfo?boId=` + boId,
  })
}

export const getUser= (boid) => {
  return request({
    method: 'get',
    url: `${baseUrls}/getUser?boId=` + boid,
  })
}

export const callback= (boid) => {
  return request({
    method: 'get',
    url: `${baseUrls}/getUser?boId=` + boid,
  })
}

export function readAlready(taskId, boId){
  return request({
    method:'post',
    url: `${baseUrl}/readAlready?taskId=` + taskId + '&boId=' + boId,
  })
}
