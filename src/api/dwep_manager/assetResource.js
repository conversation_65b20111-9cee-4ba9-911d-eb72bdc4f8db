import request from '@/utils/request'

const baseUrl = window.g.baseEmpowermentUrl +'/plugin/assetResourceManage'

// 获取资产资源分页列表
export function pageService(data) {
  return request({
    url: `${baseUrl}/page`,
    method: 'post',
    data
  })
}

// 新增资产资源
export function addService(data) {
  return request({
    url: `${baseUrl}`,
    method: 'post',
    data
  })
}

// 更新资产资源
export function updateService(data) {
  return request({
    url: `${baseUrl}`,
    method: 'put',
    data
  })
}

// 删除资产资源
export function delService(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 导出
export function downloadService() {
  return `${baseUrl}/exportExcel`
}

// 下载模板
export function downloadTemplateService() {
  return `${baseUrl}/downloadTemplate`
}

// 导入
export const importExcelService = (data) => {
  return request({
    method: 'post',
    url: `${baseUrl}/importExcel`,
    data: data
  })
}
