
/*
 * @File name: dwepWhiteListTable.js
 * @Author: LT
 * @Date: 2024-03-15
 * @Description: 白名单管理接口服务
 */

import request from '@/utils/request.js'

// 基础路径（根据你的实际项目配置）
const baseUrl =window.g.baseEmpowermentUrl +'/dwepWhiteListTable'



export const getDwepWhiteListPageService = (params) => {
  return request({
    method: 'get',
    url: `${baseUrl}/page`,
    params
  })
}


export const getDwepWhiteListByTypeService = (data) => {
  return request({
    method: 'get',
    url: `${baseUrl}/queryWhiteList`,
    params: data
  })
}


export const addDwepWhiteListService = (params) => {
  return request({
    method: 'post',
    url: `${baseUrl}/add`,
    data:params
  })
}
export function editDwepWhiteListService(params){
  return request({
    method:'post',
    url:`${baseUrl}/update`,
    data:params
  })
}

export const deleteDwepWhiteListByIdService = (data) => {
  return request({
    method: 'get',
    url: `${baseUrl}/deleted`,
    params: data
  })
}

// 删除白名单（GET /dwepWhiteListTable/{id}）
// export const deleteDwepWhiteListByIdService = createGetApi('/:id')
// 封装通用POST请求
// const createPostApi = (url) => (data) =>
//   request({
//     method: 'post',
//     url: baseUrl + url,
//     data
//   })

// ======================
// 白名单相关接口定义
// ======================




// 新增白名单（POST /dwepWhiteListTable）
// export const addDwepWhiteListService = createPostApi('')

// 编辑白名单（POST /dwepWhiteListTable）
// export const editDwepWhiteListService = createPostApi('')

// 删除白名单（GET /dwepWhiteListTable/{id}）
// export const deleteDwepWhiteListByIdService = createGetApi('/:id')
