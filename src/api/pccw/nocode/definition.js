import request from '@/utils/request'
const serviceUrl = window.g.nocodeUrl
// 查询modeler列表
export function listDefinition(query) {
  return request({
    url: serviceUrl + '/nocode/processDefinition/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getDefinitionsByInstanceId(instanceId) {
  return request({
    url: serviceUrl + '/nocode/processDefinition/getDefinitions/' + instanceId,
    method: 'get'
  })
}

// 挂起激活转换
export function suspendOrActiveApply(data) {
  return request({
    url: serviceUrl + '/nocode/processDefinition/suspendOrActiveApply',
    method: 'post',
    data:data
  })
}


// 删除Modeler
export function delDefinition(id) {
  return request({
    url: serviceUrl + '/nocode/processDefinition/remove/' + id,
    method: 'delete'
  })
}


