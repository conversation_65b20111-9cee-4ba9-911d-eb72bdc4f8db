import request from '@/utils/request.js'
const serviceUrl = window.g.nocodeUrl

// 查询表单定义列表
export function listDef(data) {
  return request({
    url: serviceUrl + '/nocode/form/list',
    method: 'post',
    data: data
  })
}

// 查询表单定义详细
export function getDef(id) {
  return request({
    url: serviceUrl + '/nocode/form/' + id,
    method: 'get'
  })
}

// 新增表单定义
export function addDef(data) {
  return request({
    url: serviceUrl + '/nocode/form',
    method: 'post',
    data: data
  })
}

// 修改表单定义
export function updateDef(data) {
  return request({
    url: serviceUrl + '/nocode/form',
    method: 'put',
    data: data
  })
}

// 删除表单定义
export function delDef(id) {
  return request({
    url: serviceUrl + '/nocode/form/' + id,
    method: 'delete'
  })
}

// 导出表单定义
export function exportDef() {
  return serviceUrl + '/nocode/form/export'
}
