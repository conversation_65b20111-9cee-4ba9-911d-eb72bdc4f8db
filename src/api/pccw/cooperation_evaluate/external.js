// 合作单位 外部
import request from '@/utils/request.js'

const serviceUrl = window.g.comprehensiveUrl

//外部列表
export const getEntryCheckList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/cooperationEvaluate/entryCheck/getEntryCheckList',
    data: query
  })
}
// 外部-导入附件
export const uploadFile = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/cooperationEvaluate/entryCheck/uploadFile`,
    data: reqData
  })
}
// 外部-导入
export const importEntryCheckData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/cooperationEvaluate/entryCheck/importEntryCheckData`,
    data: reqData
  })
}
// 外部-附件列表
export const selectFileList = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/cooperationEvaluate/entryCheck/selectFileList`,
    data: reqData
  })
}
// 外部-下载
export function downloadFile () {
  return serviceUrl + '/cooperationEvaluate/entryCheck/downloadFile'
}
// 外部-下载模板
export function downloadTemplate () {
  return serviceUrl + '/cooperationEvaluate/entryCheck/downloadTemplate'
}
// 外部-导出列表
export function exportTotal () {
  return serviceUrl + '/cooperationEvaluate/entryCheck/exportTotal'
}
// 外部-附件列表
export const deleteFile = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/cooperationEvaluate/entryCheck/deleteFile`,
    data: reqData
  })
}
// 外部-保存数据
export const saveEntryCheckData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/cooperationEvaluate/entryCheck/saveEntryCheckData`,
    data: reqData
  })
}
// 外部-删除列表数据
export const deleteEntryCheckData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/cooperationEvaluate/entryCheck/deleteEntryCheckData`,
    data: reqData
  })
}

// 外部考核报表-列表
export const getAllList = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/cooperationEvaluate/entryCheck/getAllList`,
    data: reqData
  })
}

// 外部考核报表-导出列表
export function exportList () {
  return serviceUrl + '/cooperationEvaluate/entryCheck/exportList'
}