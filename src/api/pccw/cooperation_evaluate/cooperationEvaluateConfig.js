// 合作单位后评估流程配置管理
import request from '@/utils/request.js'

const serviceUrl = window.g.comprehensiveUrl + '/cooperationEvaluate/config'

//根据 deptId 和 合作单位名称 - 查询合作单位编码
export const selectCooperationCompanyCode = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/constructionNucleusConfig/selectCooperationCompanyCode',
    data: query
  })
}

//建设单位合作单位考核接口人配置-列表
export const selectConstructionNucleusConfigList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/constructionNucleusConfig/list',
    data: query
  })
}

//建设单位合作单位考核接口人配置-新增/修改
export const editConstructionNucleusConfig = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/constructionNucleusConfig/edit',
    data: query
  })
}

//建设单位合作单位考核接口人配置-删除
export const deleteConstructionNucleusConfig = (id) => {
  return request({
    method: 'get',
    url: serviceUrl + '/constructionNucleusConfig/delete/' + id,
  })
}

//获取建设单位合作单位考核接口人配置-详细信息
export const selectConstructionNucleusConfigById = (id) => {
  return request({
    method: 'get',
    url: serviceUrl + '/constructionNucleusConfig/getInfo/' + id,
  })
}

//获取省公司合作单位管理员配置-判断当前用户是否有查看权限
export const checkPermissionCooperationConfig = () => {
  return request({
    method: 'post',
    url: serviceUrl + '/checkPermission',
  })
}

//获取省公司合作单位管理员配置-列表
export const selectProvinceAdminConfigList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/provinceAdminConfig/list',
    data: query
  })
}

//获取省公司合作单位管理员配置-新增/修改
export const editProvinceAdminConfig = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/provinceAdminConfig/edit',
    data: query
  })
}

//获取省公司合作单位管理员配置-删除
export const deleteProvinceAdminConfig = (id) => {
  return request({
    method: 'get',
    url: serviceUrl + '/provinceAdminConfig/delete/' + id,
  })
}

//区县超时待办失效时长-列表
export const selectCountyTimeoutConfigConfigList = () => {
  return request({
    method: 'post',
    url: serviceUrl + '/countyTimeoutConfig/list',
  })
}
//区县超时待办失效时长-新增/修改
export const editCountyTimeoutConfig = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/countyTimeoutConfig/edit',
    data: query
  })
}

//获取区县反馈人配置页面-列表
export const selectCountyFeedbackConfigList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/countyFeedbackConfig/list',
    data: query
  })
}

//区县反馈人配置页面-新增/修改
export const editCountyFeedbackConfig = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/countyFeedbackConfig/edit',
    data: query
  })
}

//获取区县反馈人配置页面-删除
export const deleteCountyFeedbackConfig = (id) => {
  return request({
    method: 'get',
    url: serviceUrl + '/countyFeedbackConfig/delete/' + id,
  })
}

//获取区县建维主管、分管领导配置页面-列表
export const selectCountyMaintainBranchingConfigList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/countyMaintainBranchingConfig/list',
    data: query
  })
}

//区县建维主管、分管领导配置页面-新增/修改
export const editCountyMaintainBranchingConfig = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/countyMaintainBranchingConfig/edit',
    data: query
  })
}

//获取区县建维主管、分管领导配置页面-删除
export const deleteCountyMaintainBranchingConfig = (id) => {
  return request({
    method: 'get',
    url: serviceUrl + '/countyMaintainBranchingConfig/delete/' + id,
  })
}

//获取省公司合作单位管理员、建设单位合作单位管理员配置-判断当前用户是否有查看权限
export const constructionCheckPermissionCooperationConfig = () => {
  return request({
    method: 'post',
    url: serviceUrl + '/constructionCheckPermission',
  })
}

//获取维护部门评分人配置-详细信息
export const selectMaintenanceScoreConfigById = (id) => {
  return request({
    method: 'get',
    url: serviceUrl + '/maintenanceScoreConfig/getInfo/' + id,
  })
}

//查询维护部门评分人配置-列表
export const selectMaintenanceScoreConfigList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/maintenanceScoreConfig/list',
    data: query
  })
}

//获取维护部门评分人配置-新增/修改
export const editMaintenanceScoreConfig = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/maintenanceScoreConfig/edit',
    data: query
  })
}

//获取维护部门评分人配置-删除
export const deleteMaintenanceScoreConfig = (id) => {
  return request({
    method: 'get',
    url: serviceUrl + '/maintenanceScoreConfig/delete/' + id,
  })
}

//合作单位监督发现问题分类配置-列表
export const selectSuperviseIssueTypeConfigList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/superviseIssueTypeConfig/list',
    data: query
  })
}

//合作单位监督发现问题分类配置-详细信息
export function selectSuperviseIssueTypeConfigById(id) {
  return request({
    method: 'get',
    url: serviceUrl + '/superviseIssueTypeConfig/getInfo/' + id,
  })
}

//获取合作单位监督发现问题分类配置-新增/修改
export const editSuperviseIssueTypeConfig = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/superviseIssueTypeConfig/edit',
    data: query
  })
}

//合作单位监督发现问题分类配置-删除
export function deleteSuperviseIssueTypeConfig(id) {
  return request({
    method: 'get',
    url: serviceUrl + '/superviseIssueTypeConfig/delete/' + id,
  })
}

//获取项目任务所在阶段考核表模板配置-列表
export const selectProjectTaskAssessmentConfigList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/projectTaskAssessmentConfig/list',
    data: query
  })
}
//获取项目任务所在阶段考核表模板配置-新增/修改
export const editProjectTaskAssessmentConfig = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/projectTaskAssessmentConfig/edit',
    data: query
  })
}
//申诉工单列表
export const getFlowListByInterfacePerson = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/getFlowListByInterfacePerson',
    data: query
  })
}
//查询申诉工单明细列表
export const getDeductionList = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/getDeductionList',
    data: query
  })
}
//申诉工单详情
export const getDeductionDetail = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/getDeductionDetail',
    data: query
  })
}
//申诉上传附件
export const uploadFile = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/uploadFile',
    data: query
  })
}

//申诉删除附件
export const deleteFile = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/deleteFile',
    data: query
  })
}

//申诉查看附件列表
export const selectFileList = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/selectFileList',
    data: query
  })
}
//申诉保存
export const saveDetail = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/saveDetail',
    data: query
  })
}
//申诉保存
export const deleteAppeal = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/deleteAppeal',
    data: query
  })
}
// 查询待阅扣分详情
export const getReadingDetail = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/getReadingDetail',
    data: query
  })
}

// 申诉流程中查看详情
export const getAppealDetailInFlow = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/getAppealDetailInFlow',
    data: query
  })
}
// 申诉流程中查看保存
export const saveNewDetailInFlow = (query) => {
  return request({
    method: 'post',
    url: window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/saveNewDetailInFlow',
    data: query
  })
}
export function downloadFiles() {
  return window.g.comprehensiveUrl + '/cooperationEvaluate/appeal/downloadFiles'
}
