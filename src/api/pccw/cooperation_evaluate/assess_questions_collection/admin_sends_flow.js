import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/cooperationEvaluate/problemCollection'
const serviceUrl2 = window.g.comprehensiveUrl + '/cooperationEvaluate/problemCollectionBase'



// 考核问题收集流程-判断是否可以提交
export const judgeReject = async (data) => {
  return request({
    url: serviceUrl + `/judgeReject`,
    method: 'post',
    data
  });
};

// 问题公示/ 管理员发送流程 - > 接口人
export const startProblemPublicity = async (data) => {
  return request({
    url: serviceUrl + `/startProblemPublicity`,
    method: 'post',
    data
  });
};

// 接口人发起申述 - 发起流程
export const startAppealByInter = async (data) => {
  return request({
    url: serviceUrl + `/startAppealByInter`,
    method: 'post',
    data
  });
};


// 发起驳回流程
export const startReject = async (data) => {
  return request({
    url: serviceUrl + `/startReject`,
    method: 'post',
    data
  });
};

// 导出
export const exportData = async (data) => {
  return request({
    url: serviceUrl + `/export?`+data,
    method: 'get',
    responseType:'blob'
  });
}

// 考核问题收集流程-催办-处理
export const handleUrge = async (data) => {
  return request({
    url: serviceUrl + `/handleUrge`,
    method: 'post',
    data
  });
};

// 考核问题收集流程-催办-查询
export const urgeList = async (data) => {
  return request({
    url: serviceUrl + `/urgeList`,
    method: 'post',
    data
  });
};

// 考核问题收集流程-报表
export const reportList = async (data) => {
  return request({
    url: serviceUrl + `/reportList`,
    method: 'post',
    data
  });
};

// 考核问题收集流程-查询
export const getList = async (data) => {
  return request({
    url: serviceUrl + `/list`,
    method: 'post',
    data
  });
};

// 获取下一节点审批人姓名-查询
export const getNextUsersName = async (data) => {
  return request({
    url: serviceUrl + `/getNextUsersName`,
    method: 'post',
    data
  });
};

// 考核问题收集流程-基础信息列表-查询
export const getBaseInfoList = async (data) => {
  return request({
    url: serviceUrl2 + `/list`,
    method: 'get',
    params: data
  });
};

// 考核问题收集流程-基础信息列表-子流程获取要求反馈时间
export const getRequiredFeedbackDate = async (data) => {
  return request({
    url: serviceUrl2 + `/getRequiredFeedbackDate`,
    method: 'get',
    params: data
  });
};

// 考核问题收集流程-基础信息列表-保存/修改
export const editBaseInfo = async (data) => {
  return request({
    url: serviceUrl2 + `/edit`,
    method: 'post',
    data
  });
};

// 区县反馈人列表-查询
export const getFeedbackByBid = async (data) => {
  return request({
    url: serviceUrl + `/getFeedbackByBid`,
    method: 'post',
    data
  });
};

// 一键派发/发起-区县待办
export const startDistrictFeedbackFlow = async (data) => {
  return request({
    url: serviceUrl + `/startDistrictFeedbackFlow`,
    method: 'post',
    data
  });
};
