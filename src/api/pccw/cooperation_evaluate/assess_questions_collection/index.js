import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/cooperationEvaluate/problemCollectionDistribute'




// 外部录入考核事项-查询
export const getEntryCheckList = async (data) => {
  return request({
    url: serviceUrl + `/getEntryCheckList`,
    method: 'post',
    data
  });
};
// 合作单位发现问题-查询
export const getSuperviseIssue = async (data) => {
  return request({
    url: serviceUrl + `/getSuperviseIssue`,
    method: 'post',
    data
  });
};

// 考核指标-查询
export const getAssessmentIndex = async (data) => {
  return request({
    url: serviceUrl + `/getAssessmentIndex`,
    method: 'post',
    data
  });
};

export function downloadFile() {
  return window.g.comprehensiveUrl + '/cooperationEvaluate/entryCheck/downloadFile'
}