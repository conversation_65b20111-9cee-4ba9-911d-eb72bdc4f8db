import request from '@/utils/request.js'
import qs from 'qs';
const serviceUrl = window.g.comprehensiveUrl + '/videoAcceptance'
const serviceUrl2 = window.g.comprehensiveUrl + '/overdue'

// 提交
export const submit = async (data) => {
  return request({
    url: serviceUrl + `/submit`,
    method: 'post',
    data
  });
};

// 待阅
export const getMsgList = async (data) => {
  return request({
    url: serviceUrl2 + `/getList`,
    method: 'post',
    data
  });
};

// 待阅-视频验收
// 工程质量抽查工单即将到达整改期限提醒
export const getRectificationFrontList = async (data) => {
  return request({
    url: serviceUrl2 + `/getRectificationFrontList`,
    method: 'post',
    data
  });
};
// 工程质量抽查工单已经到达整改期限提醒
export const getRectificationList = async (data) => {
  return request({
    url: serviceUrl2 + `/getRectificationList`,
    method: 'post',
    data
  });
};

// 工程质量抽查工单超期未处理/超期未整改提醒
export const getRectificationOverdueList = async (data) => {
  return request({
    url: serviceUrl2 + `/getRectificationOverdueList`,
    method: 'post',
    data
  });
};

// 问题分类查询

// 1.2.1关键工序抽查工单 [待办->工序质量抽查单据]

//1、省公司稽核人员抽查页面 2、工程实施经理（主）抽查页面

// 待办 查询
export const getVideoAcceptance = async (params) => {
  // const serializedParams = qs.stringify(params); // 序列化参数对象
  return request({
    url: serviceUrl + '/getVideoAcceptance',
    method: 'get',
    params
    // data: serializedParams, // 将序列化后的参数放入请求体中
  })
}

// 视频验收退回代办工单(退回)
// 退回代办工单(退回)
export const returnFlow = async (data) => {
  return request({
    url: serviceUrl + `/return`,
    method: 'post',
    data
  });
}

export const returnTask = async (data) => {
  return request({
    url: serviceUrl + `/returnTask`,
    method: 'post',
    data
  });
}

// 查询  type为1：验收审核意见；type为2：验收整改复核意见
export const getRectificationTasks = async (data) => {
  return request({
    url: serviceUrl + `/getRectificationTasks`,
    method: 'post',
    data
  });
};
// 查询省公司审核意见
export const getReviewComments = async (data) => {
  return request({
    url: serviceUrl + `/getReviewComments`,
    method: 'post',
    data
  });
};


// 新增整改任务：type为1：验收审核意见；type为2：验收整改复核意见
export const addRectificationTasks = async (data) => {
  return request({
    url: serviceUrl + `/addRectificationTasks`,
    method: 'post',
    data
  });
};
// 新增省公司审核意见
export const addReviewComments = async (data) => {
  return request({
    url: serviceUrl + `/addReviewComments`,
    method: 'post',
    data
  });
};


// 删除整改任务
export const deleteRectificationTasks = async (data) => {
  return request({
    url: serviceUrl + `/deleteRectificationTasks?ids=`+data,
    method: 'get'
  });
};

// 删除整改任务
export const deleteReviewComments = async (data) => {
  return request({
    url: serviceUrl + `/deleteReviewComments?ids=`+data,
    method: 'get'
  });
};

// 校验整改附件是否上传
export const checkFile = async (data) => {
  return request({
    url: serviceUrl + `/checkFile?`+data,
    method: 'get'
  });
};

// 附件查询
export const getFileList = async (data) => {
  return request({
    url: serviceUrl + `/getFileList`,
    method: 'post',
    data
  });
};


// 附件上传
export const uploadVideo = async (data) => {
  return request({
    url: serviceUrl + `/upload`,
    method: 'post',
    data
  });
};

// 附件下载
export const downloadFile = async (id) => {
  return request({
    url: serviceUrl + `/download?kbId=`+id,
    method: 'get',
    responseType:'blob',
  });
}

// 附件删除 - 根据id
export const deleteFile = async (data) => {
  return request({
    url: serviceUrl + `/deleteFile`,
    method: 'post',
    data
  });
};


// ②整改复核工单待办 查询
// 保存
// 附件上传
// 附件删除


//3、工程管理经理主抽查页面 /省公司抽查节点
// 查询
// 保存
// 附件上传
// 附件删除
