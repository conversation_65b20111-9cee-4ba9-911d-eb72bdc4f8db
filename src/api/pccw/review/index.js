import request from '@/utils/request.js'
import qs from 'qs';
const serviceUrl = window.g.comprehensiveUrl + '/problemTemplate'
const serviceUrl2 = window.g.comprehensiveUrl + '/problemClassification'

// 问题分类查询
export const getProblemTemplateList = async (data) => {
  return request({
    url: serviceUrl + `/getProblemTemplateList`,
    method: 'post',
    data
  });
};


// 问题分类数查询
export const getProblemTemplate = async (data) => {
  return request({
    url: serviceUrl + `/getProblemTemplate`,
    method: 'post',
    data
  });
};


//省公司抽查问题列表
export const getProblemClassification = async  (query)=> {
  return request({
    url: serviceUrl2 + '/getProblemClassification',
    method: 'post',
    data: query
  })
}
