import request from '@/utils/request.js'
import qs from 'qs';
const serviceUrl = window.g.comprehensiveUrl + '/keyProcessReviewWorkOrder'
const serviceUrl2 = window.g.comprehensiveUrl + '/overdueForKeyProcess'

// 待阅
export const getMsgList = async (data) => {
  return request({
    url: serviceUrl2 + `/getList`,
    method: 'post',
    data
  });
};

// 工程质量抽查工单即将到达整改期限提醒
export const getRectificationFrontList = async (data) => {
  return request({
    url: serviceUrl2 + `/getRectificationFrontList`,
    method: 'post',
    data
  });
};
// 工程质量抽查工单已经到达整改期限提醒
export const getRectificationList = async (data) => {
  return request({
    url: serviceUrl2 + `/getRectificationList`,
    method: 'post',
    data
  });
};
// 工程质量抽查工单超期未处理/超期未整改提醒
export const getRectificationOverdueList = async (data) => {
  return request({
    url: serviceUrl2 + `/getRectificationOverdueList`,
    method: 'post',
    data
  });
};




// 退回代办工单(退回)
export const returnFlow = async (data) => {
  return request({
    url: serviceUrl + `/return`,
    method: 'post',
    data
  });
}

export const returnTask = async (data) => {
  return request({
    url: serviceUrl + `/returnTask`,
    method: 'post',
    data
  });
}
// 校验整改附件是否上传
export const checkFile = async (data) => {
  return request({
    url: serviceUrl + `/checkRectificationTaskAndFile?`+data,
    method: 'get'
  });
};
// 1.2.1关键工序抽查工单 [待办->工序质量抽查单据]

//1、省公司稽核人员抽查页面 2、工程实施经理（主）抽查页面

// ①抽查待办工单 查询
export const getReviewWorkOrder = async (params) => {
  // const serializedParams = qs.stringify(params); // 序列化参数对象
  return request({
    url: serviceUrl + '/getReviewWorkOrder',
    method: 'get',
    params
    // data: serializedParams, // 将序列化后的参数放入请求体中
  })
}

// 工序名称列表 查询
export const queryProcessNameByTemplate = async (params) => {
  return request({
    url: serviceUrl + `/queryProcessNameByTemplate`,
    method: 'get',
    params
  });
};

// 审核意见-查询  三个list
export const queryReviewWorkOpinion = async (params) => {
  return request({
    url: serviceUrl + `/queryReviewWorkOpinion`,
    method: 'get',
    params
  });
};

// 删除
export const deleteTasks = async (data) => {
  return request({
    url: serviceUrl + `/deleteTasks?ids=`+data,
    method: 'get'
  });
};

// 保存
export const submitReviewWorkOrder = async (data) => {
  return request({
    url: serviceUrl + `/submit`,
    method: 'post',
    data
  });
};

// 附件查询
export const queryUploadAttachment = async (data) => {
  return request({
    url: serviceUrl + `/queryUploadAttachment`,
    method: 'post',
    data
  });
};

// 附件上传
export const importReviewWorkOrderFile = async (data) => {
  return request({
    url: serviceUrl + `/importReviewWorkOrderFile`,
    method: 'post',
    data
  });
};
// 附件下载
export const downloadFile = async (id) => {
  return request({
    url: serviceUrl + `/download?kbId=`+id,
    method: 'get',
    responseType:'blob'
  });
}

// 附件删除 - 根据id
export const deleteFile = async (data) => {
  return request({
    url: serviceUrl + `/deleteFile`,
    method: 'post',
    data
  });
};


// ②整改复核工单待办 查询
// 保存
// 附件上传
// 附件删除


//3、工程管理经理主抽查页面 /省公司抽查节点
// 查询
// 保存
// 附件上传
// 附件删除
