import request from '@/utils/request'

const serviceUrl = window.g.comprehensiveUrl

// 查询报查询配置列表
export function listReportSelectConfig(query) {
  return request({
    url: serviceUrl + '/reportSelect/reportSelectConfig/list',
    method: 'post',
    params: query
  })
}

// 查询报查询配置详细
export function getReportSelectConfig(id) {
  return request({
    url: serviceUrl + '/reportSelect/reportSelectConfig/' + id,
    method: 'get'
  })
}

// 新增报查询配置
export function addReportSelectConfig(data) {
  return request({
    url: serviceUrl + '/reportSelect/reportSelectConfig/add',
    method: 'post',
    data: data
  })
}

// 修改报查询配置
export function updateReportSelectConfig(data) {
  return request({
    url: serviceUrl + '/reportSelect/reportSelectConfig/edit',
    method: 'post',
    data: data
  })
}

// 删除报查询配置
export function delReportSelectConfig(id) {
  return request({
    url: serviceUrl + '/reportSelect/reportSelectConfig/' + id,
    method: 'post'
  })
}
