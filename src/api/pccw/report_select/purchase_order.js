import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/warningReminder'


// 获取人员配置信息
export const getUserConfigList = async (data) => {
  return request({
    url: serviceUrl + `/getUserConfigList`,
    method: 'post',
    data,
  })
}

// 获取配置信息
export const getUnitConfigList = async (data) => {
  return request({
    url: serviceUrl + `/getUnitConfigList`,
    method: 'post',
    data,
  })
}

// 新增配置信息
export const insertConfig = async (data) => {
  return request({
    url: serviceUrl + `/insertConfig`,
    method: 'post',
    data,
  })
}

// 获取省公司人员列表
export const getUserList = async (data) => {
  return request({
    url: serviceUrl + `/getUserList`,
    method: 'post',
    data,
  })
}

// 删除配置信息
export const deleteConfig = async (data) => {
  return request({
    url: serviceUrl + `/deleteConfig`,
    method: 'post',
    data,
  })
}
