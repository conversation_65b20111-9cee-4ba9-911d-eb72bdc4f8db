import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/auditReport'


// 财务门户附件下载-列表
export const getFinaceList = async (data) => {
  return request({
    url: serviceUrl + `/getAuditReportFinance`,
    method: 'post',
    data,
  })
}

// ERP系统附件下载-列表
export const getERPList = async (data) => {
  return request({
    url: serviceUrl + `/getAuditReportERP`,
    method: 'post',
    data,
  })
}

// 财务门户附件下载-修改
export const updateFinace = async (data) => {
  return request({
    url: serviceUrl + `/updateAuditReportERP`,
    method: 'post',
    data,
  })
}

// ERP系统附件下载-修改
export const updateERP = async (data) => {
  return request({
    url: serviceUrl + `/updateAuditReportERP`,
    method: 'post',
    data,
  })
}
/////////////////////////
// 下载订单采购预警详情页列表  - 未完成接收费用订单列表
export const exportDetail = async (data) => {
  return request({
    url: serviceUrl + '/exportDetailPage?'+data,
    method: 'get',
    responseType:'blob',
  });
};

// 下载订单采购预警汇总报表列表
export const exportTotal = async (data) => {
  console.log("d",data);
  return request({
    url: serviceUrl + '/exportTotalPage?'+data,
    method: 'get',
    responseType:'blob'
  });
};
