import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/reportSelect'

//结算审减率报表
export const checkPermission = () => {
  return request({
    method: 'post',
    url: serviceUrl + '/settlementReductionRate/checkPermission',

  })
}
// 查询转资任务完成情况列表
export function settlementReductionRateSummaryList(query) {
  return request({
    url: serviceUrl + '/settlementReductionRate/summaryList',
    method: 'post',
    data: query
  })
}
//查询结算审核预警详情
export function getAuditAlertInfo(query) {
  return request({
    url: serviceUrl + '/settlementReductionRate/getAuditAlertInfo',
    method: 'post',
    data: query
  })
}
//导出结算审核预警详情
export function exportAuditAlertInfo() {
  return serviceUrl + '/settlementReductionRate/exportAuditAlertInfo'
}
