import request from '@/utils/request.js'

const serviceUrl = window.g.comprehensiveUrl + '/reportSelect'

//转资任务完成情况
//判断是否有权限查看
export const checkPermission = () => {
  return request({
    method: 'post',
    url: serviceUrl + '/quarterTransferTask/checkPermission',

  })
}
//数据过滤，获取当前有权限的地市数据
export function getCityList() {
  return request({
    method: 'get',
    url: serviceUrl + '/quarterTransferTask/getCityList',
  })
}

//查询转资任务各地市预算金额
export function quarterTransferTaskImportList(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/quarterTransferTask/queryQuarterTransferTaskImportList',
    data: query,
  })
}
//查询转资任务各地市预算金额
export function updateTransferTaskImportList(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/quarterTransferTask/updateTransferTaskImportList',
    data: query,
  })
}


//下载月度/季度导入模版
export function downloadTemplate(query) {
  return request({
    method: 'get',
    url: serviceUrl + '/quarterTransferTask/download/' + query,
    responseType: 'blob'
  })
}

// 上传附件
export const uploadFile = (data,fileType) => {
  return request({
    method: 'post',
    url: serviceUrl + '/quarterTransferTask/import/' + fileType,
    attFile: true,
    data: data,
    headers: {
      'Content-Type': 'multipart/formcenter-data',
    },
  })
}



// 查询转资任务完成情况列表
export function summaryList(query) {
  return request({
    url: serviceUrl + '/quarterTransferTask/summaryList',
    method: 'post',
    data: query
  })
}
// 查询转资任务完成情况列表-点评性描述
export function summaryDescription(query) {
  return request({
    url: serviceUrl + '/quarterTransferTask/summaryList/description',
    method: 'post',
    data: query
  })
}

//查询转资任务完成明细列表
export function detailList(query) {
  return request({
    url: serviceUrl + '/quarterTransferTask/detailsList',
    method: 'post',
    data: query
  })
}
//查询项目列表
export function selectProjectList(query) {
  return request({
    url: serviceUrl + '/quarterTransferTask/selectProjectList',
    method: 'post',
    data: query
  })
}
//更新项目与转资关系
export function updateProjectTransfer(query) {
  return request({
    url: serviceUrl + '/quarterTransferTask/updateProjectTransfer',
    method: 'post',
    data: query
  })
}



// 导出
export function download(query) {
  return request({
    url: serviceUrl + '/quarterTransferTask/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 导出
export function downloadService() {
  return serviceUrl + '/quarterTransferTask/export'
}

//查询季度转资预警详情
export function getQuarterAlertInfo(query) {
  return request({
    url: serviceUrl + '/quarterTransferTask/getQuarterAlertInfo',
    method: 'post',
    data: query
  })
}
//查询转资任务预警完成明细列表
export function alertDetailsList(query) {
  return request({
    url: serviceUrl + '/quarterTransferTask/alertDetailsList',
    method: 'post',
    data: query
  })
}
// 导出
//导出结算审核预警详情
export function exportAlert() {
  return serviceUrl + '/quarterTransferTask/exportAlert'
}
