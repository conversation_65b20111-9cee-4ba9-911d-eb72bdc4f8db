import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/reportSelect/quarterTransferTaskNotTime'


//2.任务转资提前滞后率统计报表页面 [验收环节->任务转资提前滞后率统计报表]
// 任务转资提前滞后率 统计报表 查询

// 任务转资提前滞后率统计报表 导出

// 任务转资提前滞后率统计报表 一键导出

// 3.报表下发通知 [待阅]
// 待阅-任务转资提前滞后率情况统计列表 查询

// 待阅-任务转资提前滞后率情况统计列表 导出

// 待阅-任务转资提前滞后率情况统计明细

// 4.转资不及时预警统计报表页面  [验收环节->[转资不及时预警统计报表]
// 转资不及时预警统计报表 查询(三个维度页面)

// 转资不及时预警统计报表 导出

// 转资不及时预警统计报表 一键导出

// 5.转资不及时预警统计报表 [待阅]
// 待阅-转资不及时 查询 ①工程管理经理（主）

// 待阅-转资不及时 查询 ②工程实施经理（主、辅）

// 待阅-转资不及时 明细

// 待阅-转资不及时 明细 导出


