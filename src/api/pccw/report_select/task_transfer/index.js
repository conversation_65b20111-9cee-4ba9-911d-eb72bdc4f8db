import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/reportSelect/quarterTransferTaskNotTime'


//2.任务转资提前滞后率统计报表页面 [验收环节->任务转资提前滞后率统计报表]
// 任务转资提前滞后率 统计报表 查询
export const summaryNotTimeList = async (data) => {
  return request({
    url: serviceUrl + `/summaryNotTimeList`,
    method: 'post',
    data
  });
};

// 任务转资提前滞后率统计报表 导出/一键导出
export const exportNotTimeList = async (data) => {
  return request({
    url: serviceUrl + `/exportNotTimeList?`+data,
    method: 'get',
    responseType:'blob',
  });
};

// 3.报表下发通知 [待阅]
// 待阅-任务转资提前滞后率情况统计列表 查询
export const summaryNotTimeAlertList = async (data) => {
  return request({
    url: serviceUrl + `/summaryNotTimeAlertList`,
    method: 'post',
    data
  });
};


// 待阅-任务转资提前滞后率情况统计列表 导出
export const exportNotTimeListAlert = async (data) => {
  return request({
    url: serviceUrl + `/exportNotTimeListAlert?`+data,
    method: 'get',
    responseType:'blob',
  });
};


// 待阅-任务转资提前滞后率情况统计明细
export const summaryNotTimeAlertDetailList = async (data) => {
  return request({
    url: serviceUrl + `/summaryNotTimeAlertDetailList`,
    method: 'post',
    data
  });
};

// 4.转资不及时预警统计报表页面  [验收环节->[转资不及时预警统计报表]
// 转资不及时预警统计报表 查询(三个维度页面)
export const summaryNotTimeWarningList = async (data) => {
  return request({
    url: serviceUrl + `/summaryNotTimeWarningList`,
    method: 'post',
    data
  });
};

// 转资不及时预警统计报表 导出
export const exportNotTimeWarningList = async (data) => {
  return request({
    url: serviceUrl + `/exportNotTimeWarningList?`+data,
    method: 'get',
    responseType:'blob',
  });
};

// 转资不及时预警统计报表 一键导出

// 5.转资不及时预警统计报表 [待阅]
// 待阅-转资不及时 查询 ①工程管理经理（主）

// 待阅-转资不及时 查询 ②工程实施经理（主、辅）

// 待阅-转资不及时 明细
export const detailsNotTimeWarningList = async (data) => {
  return request({
    url: serviceUrl + `/detailsNotTimeWarningList`,
    method: 'post',
    data
  });
};


// 待阅-转资不及时 明细 导出


