import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl
//省公司列表
export function getConfig (query) {
  return request({
    url: serviceUrl + '/staffing/getConfig',
    method: 'post',
    data: query
  })
}
//省公司删除
export function deleteConfig (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/staffing/deleteConfig?ids=' + query.ids,
    data: query
  })
}
//省公司新增
export function insertConfig (query) {
  return request({
    url: serviceUrl + '/staffing/insertConfig',
    method: 'post',
    data: query
  })
}
//省公司角色选择
export function getUser (query) {
  return request({
    url: serviceUrl + '/staffing/getUser',
    method: 'post',
    data: query
  })
}
//关键工序、视频验收工单抽查比例配置列表
export function getKeyProcessesRatio (query) {
  return request({
    url: serviceUrl + '/samplingRatio/getKeyProcessesRatio',
    method: 'post',
    data: query
  })
}
//关键工序、视频验收工单抽查比例配置删除
export function deleteKeyProcessesRatio (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/samplingRatio/deleteKeyProcessesRatio?ids=' + query.ids,
    data: query
  })
}
//关键工序、视频验收工单抽查比例配置新增
export function insertKeyProcessesRatio (query) {
  return request({
    url: serviceUrl + '/samplingRatio/insertKeyProcessesRatio',
    method: 'post',
    data: query
  })
}
//关键工序、视频验收工单抽查比例配置(工程管理)列表
export function getVideoAcceptanceRatio (query) {
  return request({
    url: serviceUrl + '/samplingRatio/getVideoAcceptanceRatio',
    method: 'post',
    data: query
  })
}
//关键工序、视频验收工单抽查比例配置(工程管理)删除
export function deleteVideoAcceptanceRatio (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/samplingRatio/deleteVideoAcceptanceRatio?ids=' + query.ids,
    data: query
  })
}
//关键工序、视频验收工单抽查比例配置新增
export function insertVideoAcceptanceRatio (query) {
  return request({
    url: serviceUrl + '/samplingRatio/insertVideoAcceptanceRatio',
    method: 'post',
    data: query
  })
}
//问题严重程度列表
export function getProblemSeverity (query) {
  return request({
    url: serviceUrl + '/problemSeverity/getProblemSeverity',
    method: 'post',
    data: query
  })
}
//问题严重程度删除
export function deleteProblemSeverity (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/problemSeverity/deleteProblemSeverity?ids=' + query.ids,
    data: query
  })
}
//问题严重程度新增
export function insertProblemSeverity (query) {
  return request({
    url: serviceUrl + '/problemSeverity/insertProblemSeverity',
    method: 'post',
    data: query
  })
}
//工序名称配置列表
export function getProcessName (query) {
  return request({
    url: serviceUrl + '/processName/getProcessName',
    method: 'post',
    data: query
  })
}
//工序名称配置删除
export function deleteProcessName (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/processName/deleteProcessName?ids=' + query.ids,
    data: query
  })
}
//工序名称配置新增
export function insertProcessName (query) {
  return request({
    url: serviceUrl + '/processName/insertProcessName',
    method: 'post',
    data: query
  })
}
//超期未处理待办的时长（自然日）可支持配置列表
export function getDurationConfiguration (query) {
  return request({
    url: serviceUrl + '/durationConfiguration/getDurationConfiguration',
    method: 'post',
    data: query
  })
}
//超期未处理待办的时长（自然日）可支持配置删除
export function deleteProblemClassificatio (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/durationConfiguration/deleteProblemClassificatio?ids=' + query.ids,
    data: query
  })
}
// 超期未处理待办的时长（自然日）可支持配置新增
export function insertDurationConfiguration (query) {
  return request({
    url: serviceUrl + '/durationConfiguration/insertDurationConfiguration',
    method: 'post',
    data: query
  })
}
// 超期未处理待办的时长（自然日）可支持配置修改
export function updateDurationConfiguration (query) {
  return request({
    url: serviceUrl + '/durationConfiguration/updateDurationConfiguration',
    method: 'post',
    data: query
  })
}

//省公司抽查问题列表
export function getProblemClassification (query) {
  return request({
    url: serviceUrl + '/problemClassification/getProblemClassification',
    method: 'post',
    data: query
  })
}
// //省公司抽查问题删除
export function deleteProblemClassification (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/problemClassification/deleteProblemClassification?ids=' + query.ids,
    data: query
  })
}
// //省公司抽查问题新增
export function insertProblemClassification (query) {
  return request({
    url: serviceUrl + '/problemClassification/insertProblemClassification',
    method: 'post',
    data: query
  })
}
// 关键工序抽查列表
export function queryReviewCheckDataByDimension (query) {
  return request({
    url: serviceUrl + `/keyProcessReviewWorkOrder/queryReviewCheckDataByDimension`,
    method: 'post',
    data: query
  })
}
//抽查问题分类模板配置列表
export function getProblemTemplate (query) {
  return request({
    url: serviceUrl + '/problemTemplate/getProblemTemplate',
    method: 'post',
    data: query
  })
}
//抽查问题分类模板配置删除
export function deleteProblemTemplate (query) {
  return request({
    url: serviceUrl + '/problemTemplate/deleteProblemTemplate?ids=' + query.ids,
    method: 'post',
    data: query
  })
}
//抽查问题分类模板配置新增
export function addProblemTemplate (query) {
  return request({
    url: serviceUrl + '/problemTemplate/addProblemTemplate',
    method: 'post',
    data: query
  })
}
// 抽查问题分类-导入
export const getRupload = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/problemTemplate/upload`,
    data: reqData
  })
}
//抽查问题分类模板下载
export function downloadTemplate () {
  return serviceUrl + '/problemTemplate/downloadTemplate'
}

//省公司质量管理员配置列表
export function getQualityAdministrator (query) {
  return request({
    url: serviceUrl + '/quality/getQualityAdministrator',
    method: 'post',
    data: query
  })
}

//省公司质量管理员配置保存
export function insertQualityAdministrator (query) {
  return request({
    url: serviceUrl + '/quality/insertQualityAdministrator',
    method: 'post',
    data: query
  })
}

//省公司质量管理员配置删除
export function deleteQualityAdministrator (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/quality/deleteQualityAdministrator?ids=' + query.ids,
    data: query
  })
}
// 工序名称模板下载
export function downloadProcessName () {
  return serviceUrl + '/processName/downloadProcessName'
}
// 工序名称模板导出
export const importProcessNameData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/processName/importProcessNameData`,
    data: reqData
  })
}
//关键工序、视频验收工单抽查比例配置下载
export function downloadKeyProcessesRatio () {
  return serviceUrl + '/samplingRatio/downloadKeyProcessesRatio'
}

// 关键工序、视频验收工单抽查比例配置-导入
export const samplingRatioImportProcessNameData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/samplingRatio/importProcessNameData`,
    data: reqData
  })
}

//关键工序、视频验收工单抽查比例配置(工程管理)下载
export function downloadVideoAcceptanceRatio () {
  return serviceUrl + '/samplingRatio/downloadVideoAcceptanceRatio'
}

// 关键工序、视频验收工单抽查比例配置(工程管理)-导入
export const importVideoAcceptanceRatio = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/samplingRatio/importVideoAcceptanceRatio`,
    data: reqData
  })
}

export function getAuditorProgress (query) {
  return request({
    url: serviceUrl + '/KeyProcessSpotChecks/getAuditorProgress',
    method: 'post',
    data: query
  })
}

export function getImplementationProgresss (query) {
  return request({
    url: serviceUrl + '/KeyProcessSpotChecks/getImplementationProgress',
    method: 'post',
    data: query
  })
}

export function getManageProgress (query) {
  return request({
    url: serviceUrl + '/KeyProcessSpotChecks/getManageProgress',
    method: 'post',
    data: query
  })
}
// 
export function getKeyProcessAuditorProgress (query) {
  return request({
    url: serviceUrl + `/keyProcessReviewWorkOrder/getKeyProcessAuditorProgress?page=${query.page}&limit=${query.limit}&workOrderType=工序质量&month=${query.month}&auditor=${query.auditor}`,
    method: 'get',
    data: query
  })
}
export function getKeyProcessImplementationProgress (query) {
  return request({
    url: serviceUrl + `/keyProcessReviewWorkOrder/getKeyProcessImplementationProgress?page=${query.page}&limit=${query.limit}&workOrderType=工序质量&month=${query.month}&implementationManager=${query.implementationManager}`,
    method: 'get',
    data: query
  })
}
export function getKeyProcessManageProgress (query) {
  return request({
    url: serviceUrl + `/keyProcessReviewWorkOrder/getKeyProcessManageProgress?page=${query.page}&limit=${query.limit}&workOrderType=工序质量&month=${query.month}&managementManager=${query.managementManager}`,
    method: 'get',
    data: query
  })
}