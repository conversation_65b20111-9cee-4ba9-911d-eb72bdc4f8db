import request from '@/utils/request.js'

const serviceUrl = window.g.comprehensiveUrl


//查询数据库列表-报表
export const getReportList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/opticalCableDoctor/getReportList',
    data: query
  })
}

//查询数据库列表
export const getList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/opticalCableDoctor/getList',
    data: query
  })
}
//取消工单接口
export const deleteOrder = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + `/opticalCableDoctor/deleteOrder`,
    data: query
  })
}
//创建工单接口
export const createOrder = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + `/opticalCableDoctor/createOrder/?projectCode=${query.projectCode}&taskCode=${query.taskCode}`,
    data: query
  })
}
// 导出-报表
export function reportExport () {
  return serviceUrl + '/opticalCableDoctor/reportExport'
}

// 导出
export function onExport () {
  return serviceUrl + '/opticalCableDoctor/export'
}
// 创建工单导出
export function createExport () {
  return serviceUrl + '/opticalCableDoctor/createExport'
}
// 创建工单导入模板
export function downloadCableDoctorCreateTemplatep () {
  return serviceUrl + '/opticalCableDoctor/downloadCableDoctorCreateTemplatep'
}
//导入创建工单导入模板
export const importCableDoctorCreate = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + '/opticalCableDoctor/importCableDoctorCreate',
    data: reqData
  })
}
