import request from '@/utils/request.js'
const serviceUrl = window.g.flowUrl + '/workflow'


// 流程删除
export const revoke = async (data) => {
  return request({
    url: serviceUrl + `/revoke`,
    method: 'post',
    data,
  })
}


// 流程删除
export const deleteflows = async (data) => {
  return request({
    url: serviceUrl + `/deleteflows`,
    method: 'post',
    data,
  })
}

// 发起流程
export const startFlow = async (data) => {
  return request({
    url: serviceUrl + `/start`,
    method: 'post',
    data,
  })
}

// 审批任务
export const approveTask = async (data) => {
  return request({
    url: serviceUrl + `/approve`,
    method: 'post',
    data,
  })
}
// 获取审批可退回节点列表
export const getReturnList = async (taskId) => {
  return request({
    url: serviceUrl + `/returnList/${taskId}`,
    method: 'get',
  });
};

// 获取下一节点审批人姓名列表
export const getNextNodeApproverNames = async (taskId) => {
  return request({
    url: serviceUrl + `/getNextNodeApproverNames/${taskId}`,
    method: 'get',
  });
};

// 审批退回
export const approveReturn = async (data) => {
  return request({
    url: serviceUrl + `/returnTask`,
    method: 'post',
    data,
  })
}

// 流程委派
export const delegateTask = async (data) => {
  return request({
    url: serviceUrl + `/delegateTask`,
    method: 'post',
    data,
  })
}

// 流程转办
export const transferTask = async (data) => {
  return request({
    url: serviceUrl + `/transferTask`,
    method: 'post',
    data,
  })
}
// 流程加签
export const addMultiInstance = async (data) => {
  return request({
    url: serviceUrl + `/approve`,
    method: 'post',
    data,
  })
}

// 流程减签
export const deleteMultiInstance = async (data) => {
  return request({
    url: serviceUrl + `/return`,
    method: 'post',
    data,
  })
}
// 根据流程对象获取表单信息
export const getFlowForm = async (params) => {
  return request({
    url: serviceUrl + `/getFlowForm`,
    method: 'get',
    params,
  })
}

// 根据流程对象获取审批记录
export const getRecordList = async (id) => {
  return request({
    url: serviceUrl + `/getRecordList/${id}`,
    method: 'get'
  })
}

// 根据流程对象获取流程图
export const getFlowXml = async (id) => {
  return request({
    url: serviceUrl + `/getFlowXml/${id}`,
    method: 'get'
  })
}

// 分页查询待办任务列表
export const queryTodoList = async (data) => {
  return request({
    url: serviceUrl + `/todoList`,
    method: 'post',
    data: data,
  })
}

// 分页查询我发起任务列表
export const queryMyList = async (data) => {
  return request({
    url: serviceUrl + `/myList`,
    method: 'post',
    data: data,
  })
}
// 分页查询我已办任务列表
export const queryMyFinised = async (data) => {
  return request({
    url: serviceUrl + `/myFinised`,
    method: 'post',
    data: data,
  })
}

// 分页查询可委托人员列表
export const enableDelegate = async (data) => {
  return request({
    url: serviceUrl + `/delegateList`,
    method: 'post',
    data: data,
  })
}
