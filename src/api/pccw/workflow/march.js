import request from '@/utils/request.js'
const serviceUrl = window.g.flowMaintenanceUrl + '/marchEmis'
const serviceUrl2 = window.g.flowMaintenanceUrl + '/marchEmis/variable'
const serviceUrl3 = window.g.flowUrl + '/record'


// 导出
export const exportData = async (id) => {
  return request({
    url: serviceUrl3 + `/export`,
    method: 'get',
    responseType:'blob'
  });
}

// 分页查询待办记录
export const getRecord = async (data) => {
  return request({
    url: serviceUrl3 + `/list`,
    method: 'post',
    data: data,
  })
}
//  根据id查询流程变量
export const getRecordById = async (id) => {
  return request({
    url: serviceUrl3 + `/${id}`,
    method: 'get'
  })
}
// 修改待办记录
export const updateRecord = async (data) => {
  return request({
    url: serviceUrl3 + `/edit`,
    method: 'post',
    data: data,
  })
}
// 批量删除
export const delBatchByidList = async (idList) => {
  return request({
    url: serviceUrl3 + `/delBatchByidList/${idList}`,
    method: 'delete'
  })
}



// 分页查询待办任务列表
export const queryTodoList = async (data) => {
  return request({
    url: serviceUrl + `/list`,
    method: 'post',
    data: data,
  })
}


// 分页查询我发起任务列表
export const queryMyList = async (data) => {
  return request({
    url: serviceUrl + `/list`,
    method: 'post',
    data: data,
  })
}
// 分页查询我已办任务列表
export const queryMyFinised = async (data) => {
  return request({
    url: serviceUrl + `/myFinised`,
    method: 'post',
    data: data,
  })
}

// 分页查询可委托人员列表
export const enableDelegate = async (data) => {
  return request({
    url: serviceUrl + `/delegateList`,
    method: 'post',
    data: data,
  })
}


// 查询运行时流程变量列表
export const getVariable = async (data) => {
  return request({
    url: serviceUrl2 + `/list`,
    method: 'post',
    data: data,
  })
}


//  根据id查询流程变量
export const getVariableById = async (id) => {
  return request({
    url: serviceUrl2 + `/${id}`,
    method: 'get'
  })
}

// export function get${BusinessName}(id) {
//   return request({
//     url: '/marchEmis/marchEmis/' + id,
//     method: 'get'
//   })
// }

// 新增运行时流程变量
export const addVariable = async (data) => {
  return request({
    url: serviceUrl2 + `/add`,
    method: 'post',
    data: data,
  })
}

// export function add${BusinessName}(data) {
//   return request({
//     url: '/marchEmis/marchEmis',
//     method: 'post',
//     data: data
//   })
// }

// // 修改运行时流程变量
export const updateVariable = async (data) => {
  return request({
    url: serviceUrl2 + `/edit`,
    method: 'post',
    data: data,
  })
}

// // 删除运行时流程变量
// export function del${BusinessName}(id) {
//   return request({
//     url: '/marchEmis/marchEmis/' + id,
//     method: 'delete'
//   })
// }
