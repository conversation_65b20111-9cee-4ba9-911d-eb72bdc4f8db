import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/warning'



// 订单接收入账预警-查询明细
export const getDatils = async (data) => {
  return request({
    url: serviceUrl + `/getDetailListByReceiver`,
    method: 'post',
    data,
  })
}

// 订单接收入账预警-获取最后一次计算时间
export const getLastTime = async (data) => {
  return request({
    url: serviceUrl + `/getLastTime`,
    method: 'get',
    data,
  })
}

// 订单接收入账预警-查询所有
export const getAll = async (data) => {
  return request({
    url: serviceUrl + `/getAllList`,
    method: 'post',
    data,
  })
}

// 未完成接收费用订单列表
export const getReceiverList = async (data) => {
  return request({
    url: serviceUrl + `/getDetailListByReceiver`,
    method: 'post',
    data,
  })
}

// 下载订单采购预警详情页列表  - 未完成接收费用订单列表
export const exportDetail = async (data) => {
  return request({
    url: serviceUrl + '/exportDetailPage?'+data,
    method: 'get',
    responseType:'blob',
  });
};

// 下载订单采购预警汇总报表列表
export const exportTotal = async (data) => {
  console.log("d",data);
  return request({
    url: serviceUrl + '/exportTotalPage?'+data,
    method: 'get',
    responseType:'blob'
  });
};
