import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/completion'
// const serviceUrl2 = window.g.comprehensiveUrl + '/completionCollection'
const serviceUrl4 = window.g.comprehensiveUrl + '/completionCollection'
const serviceUrl2 = window.g.comprehensiveUrl + '/comTaskList'
const serviceUrl3 = window.g.comprehensiveUrl + '/completionCollection2'

export function getReportById(id) {
  return request({
    url: serviceUrl3 + `/${id}`,
    method: 'get'
  });
}

// 更换施工派工人员
export function updateUser(data) {
  return request({
    url: serviceUrl3 + `/updateUser`,
    method: 'post',
    data,
  });
}

export function editReportFlow(data) {
  return request({
    url: serviceUrl3 + `/edit`,
    method: 'post',
    data,
  });
}

export function delReportFlow(id) {
  return request({
    url: serviceUrl3 + `/remove/${id}`,
    method: 'get'
  });
}

export const selectComReportFlowInfoList2 = async (data) => {
  return request({
    url: serviceUrl3 + `/list`,
    method: 'post',
    data,
  })
}


// 查询完工任务竣工流程启动记录    /新待阅查询 / 再次更新
export const selectComReportFlowInfoList = async (data) => {
  return request({
    url: serviceUrl + `/selectComReportFlowInfoList`,
    method: 'post',
    data,
  })
}
export const tablelist = async (data) => {
  return request({
    url: serviceUrl2 + `/tablelist`,
    method: 'post',
    data,
  })
}
export const editTask = async (data) => {
  return request({
    url: serviceUrl2 + `/edit`,
    method: 'post',
    data,
  })
}
export function getTaskById(id) {
  return request({
    url: serviceUrl2 + `/${id}`,
    method: 'get'
  });
}

export function exportTable(id) {
  return request({
    url: serviceUrl4 + `/table/${id}`,
    method: 'get',
    responseType:'blob'
  });
}


export const getCompletionReport = async (data) => {
  return request({
    url: serviceUrl2 + `/getCompletionReport`,
    method: 'post',
    data,
  })
}


export const toSendFlow = async (data) => {
  return request({
    url: serviceUrl4 + `/toSendFlow`,
    method: 'post',
    data,
  })
}

export const updateTaskList = async (data) => {
  return request({
    url: serviceUrl4 + `/updateTaskList`,
    method: 'post',
    data,
  })
}

export const errorCheck = async (data) => {
  return request({
    url: serviceUrl4 + `/errorCheck`,
    method: 'post',
    data,
  })
}
// 任务级竣工资料收集报表-查询明细    /判断是否可以审批
export const isCanApproval = async (data) => {
  return request({
    url: serviceUrl2 + `/isCanApproval`,
    method: 'post',
    data,
  })
}

// 任务级竣工资料收集报表-查询明细    /待办修改
export const edit = async (data) => {
  return request({
    url: serviceUrl2 + `/edit`,
    method: 'post',
    data,
  })
}

// 任务级竣工资料收集报表-查询明细    /待办查询
export const getList = async (data) => {
  return request({
    url: serviceUrl2 + `/list`,
    method: 'post',
    data,
  })
}

// 任务级竣工资料收集报表-导出
export const downloadCompletionExcel = async (data) => {
  return request({
    url: serviceUrl + `/downloadCompletionExcel`,
    method: 'post',
    responseType:'blob',
    data,
  })
}

// 任务级竣工资料-审批-更改是否审核通过状态
export const updatePass = async (data) => {
  return request({
    url: serviceUrl + `/updateAllCompletionDataListByProjectCode`,
    method: 'post',
    data,
  })
}
// 任务级竣工资料收集报表-查询明细    /新待阅查询
export const getDetailsToRead = async (data) => {
  return request({
    url: serviceUrl + `/getDetailsToRead`,
    method: 'post',
    data,
  })
}

// 任务级竣工资料收集报表-查询明细    /待办查询
export const getDatils = async (data) => {
  return request({
    url: serviceUrl + `/getCompletionDataListByProjectCode`,
    method: 'post',
    data,
  })
}

// 任务级竣工资料收集报表-按完工报告查询
export const getByReport = async (data) => {
  return request({
    url: serviceUrl + `/getCompletionByCompletionReport`,
    method: 'post',
    data,
  })
}

// 任务级竣工资料收集报表-按项目查询
export const getByProject = async (data) => {
  return request({
    url: serviceUrl + `/getCompletionByProjectCode`,
    method: 'post',
    data,
  })
}

// 下载任务级竣工资料收集明细模板
export const downloadTemplate = async () => {
  return request({
    url: serviceUrl + '/downloadTemplate',
    method: 'get',
    responseType:'blob',
  });
};


// 删除文件
export function delFile(kdId) {
  return request({
    url: serviceUrl + `/${kdId}`,
    method: 'delete'
  });
}

// 下载文件
export const downloadFile = async (kdId) => {
  return request({
    url: serviceUrl + `/download/${kdId}`,
    method: 'get',
    responseType:'blob'
  });
}

// 上传文件
export const uploadFile = async (data) => {
  return request({
    url: serviceUrl + `/upload`,
    method: 'post',
    data,
  })
}



//获取最后汇总时间
export const getLastSummaryTime = async () => {
  return request({
    url: serviceUrl + `/getLastSummaryTime`,
    method: 'post',
  })
}
