import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/analysis'
// 查询系统运行参数列表
export function queryListService(query) {
  return request({
    url: serviceUrl + '/list',
    method: 'get',
    data: query
  })
}

// 查询系统运行参数
export function getSystemOperationParameters(id) {
  return request({
    url: serviceUrl + '/' + id,
    method: 'get'
  })
}

// 新增系统运行参数
export function addSystemOperationParameters(data) {
  return request({
    url: serviceUrl,
    method: 'post',
    data: data
  })
}

// 修改系统运行参数
export function updateSystemOperationParameters(data) {
  return request({
    url: serviceUrl,
    method: 'put',
    data: data
  })
}

// 删除系统运行参数
export function delService(data) {
  return request({
    url: serviceUrl + '/delete',
    method: 'post',
    params: data
  })
}

// 保存
export function saveService(data) {
  return request({
    method: 'post',
    url: serviceUrl + '/saveSystemOperationParameters',
    data: data
  })
}
// 保存--全量
export function saveAllService(data) {
  return request({
    method: 'post',
    url: serviceUrl + '/save',
    data: data
  })
}
