import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/acceptance'
// 查询pccw-rpa-ERP采购订单信息列表
export function listAcceptance(query) {
  return request({
    url: serviceUrl + '/list',
    method: 'post',
    data: query
  })
}

// 查询pccw-rpa-ERP采购订单信息详细
export function getAcceptance(id) {
  return request({
    url: serviceUrl + '/' + id,
    method: 'get'
  })
}

// 新增pccw-rpa-ERP采购订单信息
export function addAcceptance(data) {
  return request({
    url: serviceUrl,
    method: 'post',
    data: data
  })
}

// 修改pccw-rpa-ERP采购订单信息
export function updateAcceptance(data) {
  return request({
    url: serviceUrl,
    method: 'put',
    data: data
  })
}

// 删除pccw-rpa-ERP采购订单信息
export function delAcceptance(id) {
  return request({
    url: serviceUrl + '/' + id,
    method: 'delete'
  })
}

// 导出
export function downloadService() {
  return serviceUrl + '/export'
}
