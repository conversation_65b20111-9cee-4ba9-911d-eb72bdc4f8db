import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl
//监理单位列表
export function getDetailBySupervisionUnit (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionFormDetail/getDetailBySupervisionUnit',
    method: 'post',
    data: query
  })
}
// 监理单位导材料
export function exportMaterialBySupervisionUnit () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportMaterialBySupervisionUnit'
}
// 监理单位导出
export function exportDetailBySupervisionUnit () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportDetailBySupervisionUnit'
}
// 监理单位导出
export function exportAllMaterial () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportAllMaterial'
}
//设计单位列表
export function getDetailByDesignUnit (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionFormDetail/getDetailByDesignUnit',
    method: 'post',
    data: query
  })
}
// 设计单位材料
export function exportMaterialByDesignUnit () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportMaterialByDesignUnit'
}
// 设计单位导出
export function exportDetailByDesignUnit () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportDetailByDesignUnit'
}

//施工单位列表
export function getDetailByConstructionUnit (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionFormDetail/getDetailByConstructionUnit',
    method: 'post',
    data: query
  })
}
// 设计单位材料
export function exportMaterialByConstructionUnit () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportMaterialByConstructionUnit'
}
// 施工单位导出
export function exportDetailByConstructionUnit () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportDetailByConstructionUnit'
}
//合作单位岗位责任清单履职列表
export function getDetailByPostResponsibility (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionFormDetail/getDetailByPostResponsibility',
    method: 'post',
    data: query
  })
}
// 合作单位岗位责任清单履职材料
export function exportMaterialByPostResponsibility () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportMaterialByPostResponsibility'
}
// 合作单位岗位责任清单履职导出
export function exportDetailByPostResponsibility () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportDetailByPostResponsibility'
}

//查询工单列表
export function getProductionFormList (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionFormDetail/getProductionFormList',
    method: 'post',
    data: query
  })
}
//查询工单催办
export function sendWaitForReading (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionFormDetail/sendWaitForReading',
    method: 'post',
    data: query
  })
}

//待阅
export function getSafetyWorkTableName (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionForm/getSafetyWorkTableName',
    method: 'post',
    data: query
  })
}

//待阅
export function getSafetyWorkPending (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionForm/getSafetyWorkPending',
    method: 'post',
    data: query
  })
}
// 待阅下发
export function beginSafetyWorkFlow (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionForm/beginSafetyWorkFlow',
    method: 'post',
    data: query
  })
}

// 月度安全管理模板列表
export function queryALLTemplateConfig (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionTemplate/queryALLTemplateConfig',
    method: 'post',
    data: query
  })
}
// 月度安全管理模板
export function queryTemplate (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionTemplate/queryTemplate',
    method: 'post',
    data: query
  })
}
//月度安全管理导出
export function exportTemplate (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/lpsUnitSafetyProductionTemplate/exportTemplate',
    responseType: 'blob',
    data: query
  })
}
// 月度安全管理修改
export function updateTemplate (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionTemplate/updateTemplate',
    method: 'post',
    data: query
  })
}
// 合作单位填报安全生产工作人员配置界面 新增
export function getConfig (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionInterface/getConfig',
    method: 'get',
    data: query
  })
}
// 合作单位填报安全生产工作人员配置界面 列表
export function queryConfig (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionInterface/queryConfig',
    method: 'post',
    data: query
  })
}
// 查询地市安全管理员 列表
export function queryPersonByCity (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionInterface/queryPersonByCity',
    method: 'post',
    data: query
  })
}
// 查询合作单位名称
export function queryCooperationCompanys (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionInterface/queryCooperationCompanys',
    method: 'post',
    data: query
  })
}
// 合作单位查询对应接口人
export function queryPersonByCooperationCompany (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionInterface/queryPersonByCooperationCompany',
    method: 'post',
    data: query
  })
}
// 合作单位保存
export function addConfig (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionInterface/addConfig',
    method: 'post',
    data: query
  })
}
// 合作单位删除
export function deleteConfig (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionInterface/deleteConfig',
    method: 'post',
    data: query
  })
}

// 合作单位-待办查询列表
export function getSafetyWorkTableAllMsg (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionForm/getSafetyWorkTableAllMsg',
    method: 'post',
    data: query
  })
}
// 合作单位-待办提交
export function saveSafetyWork (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionForm/saveSafetyWork',
    method: 'post',
    data: query
  })
}
//上传文件
export const uploadFile = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/lpsUnitSafetyProductionForm/uploadFile`,
    data: reqData
  })
}
//附件列表
export function getFileList (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionForm/getFileList',
    method: 'post',
    data: query
  })
}
//附件列表 删除
export function deleteFile (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionForm/deleteFile',
    method: 'post',
    data: query
  })
}
//附件下载
export function download () {
  return serviceUrl + '/lpsUnitSafetyProductionForm/download'
}

//质量汇总报表全部
export function exportFormList () {
  return serviceUrl + '/lpsUnitSafetyProductionFormDetail/exportFormList'
}
export function checkIsSystemAdmin (query) {
  return request({
    url: serviceUrl + '/lpsUnitSafetyProductionInterface/checkIsSystemAdmin',
    method: 'get',
    data: query
  })
}