import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '/qualityInspection';

//查询质监申报汇总报表
export function reportStatistics(query) {
  return request({
    url: serviceUrl + '/reportStatistics',
    method: 'post',
    data: query
  })
}
//导出质监申报汇总报表
export function reportStatisticsTotalExport() {
  return serviceUrl + '/reportStatisticsTotalExport'
}
//一键导出质监申报汇总报表
export function reportStatisticsSheetDownload() {
  return serviceUrl + '/reportStatisticsSheetDownload'
}
//查询质监申报整合表
export function qualityInspectionSetList(query) {
  return request({
    url: serviceUrl + '/qualityInspectionSetList',
    method: 'post',
    data: query
  })
}
//质检申报整合表导出
export function exportQualityInspection() {
  return serviceUrl + '/exportQualityInspection'
}
//质检申报整合表模板导出
export function downloadInspectionSetTemp() {
  return serviceUrl + '/downloadInspectionSetTemp'
}
//导入质监申报整合报表
export const importQualityLnspection = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/importQualityLnspection`,
    data: reqData
  })
}
//质检申报原始表查询
export function qualityInspectionList(query) {
  return request({
    url: serviceUrl + '/qualityInspectionList',
    method: 'post',
    data: query
  })
}
//质检申报原始表导出
export function exportQualityData() {
  return serviceUrl + '/exportQualityData'
}
//竣工备案原始表查询
export function qualityCompletedList(query) {
  return request({
    url: serviceUrl + '/qualityCompletedList',
    method: 'post',
    data: query
  })
}
//竣工备案原始表导出
export function exportQualityCompleted() {
  return serviceUrl + '/exportQualityCompleted'
}
//竣工备案整合表查询
export function qualityRecordSetList(query) {
  return request({
    url: serviceUrl + '/qualityRecordSetList',
    method: 'post',
    data: query
  })
}
//竣工备案整合表导出
export function exportQualityRecord() {
  return serviceUrl + '/exportQualityRecord'
}
//竣工备案整合表模板导出
export function downloadRecordComSetTemp() {
  return serviceUrl + '/downloadRecordComSetTemp'
}
//导入竣工备案整合报表
export const importLoadRecordCom = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/importLoadRecordCom`,
    data: reqData
  })
}
//查询竣工汇总报表
export function recordStatistics(query) {
  return request({
    url: serviceUrl + '/recordStatistics',
    method: 'post',
    data: query
  })
}
//导出竣工备案汇总报表
export function recordStatisticsTotalExport() {
  return serviceUrl + '/recordStatisticsTotalExport'
}
//一键导出竣工备案汇总报表
export function recordStatisticsSheetDownload() {
  return serviceUrl + '/recordStatisticsSheetDownload'
}
//查询项目信息整合表
export function queryProjectComprehensiveInfoAllList(query) {
  return request({
    url: serviceUrl + '/queryProjectComprehensiveInfoAllList',
    method: 'post',
    data: query
  })
}
//导出项目信息整合表
export function exportProjectComprehensiveInfoAllList() {
  return serviceUrl + '/exportProjectComprehensiveInfoAllList'
}
//查询节假日列表
export function queryHolidaysList(query) {
  return request({
    url: serviceUrl + '/queryHolidaysList',
    method: 'post',
    data: query
  })
}
//下载节假日导入模板
export function downloadHolidaysTemp() {
  return serviceUrl + '/downloadHolidaysTemp'
}
//导入节假日
export const importHolidays = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/importHolidays`,
    data: reqData
  })
}
//删除某个节假日
export function deleteHolidaysById(data) {
  return request({
    method: 'get',
    url: serviceUrl + '/deleteHolidaysById/' + data,
  })
}
