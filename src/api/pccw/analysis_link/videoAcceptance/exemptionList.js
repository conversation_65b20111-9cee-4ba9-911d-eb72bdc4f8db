import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '';

//视频验收豁免清单报表
export function getQuery (query) {
  return request({
    url: serviceUrl + '/lpsVideoAcceptanceExempt/query',
    method: 'post',
    data: query
  })
}
//视频验收豁免清单删除
export function lpsVideoAcceptanceDelete (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/lpsVideoAcceptanceExempt/delete?ids=' + query.ids,
    data: query
  })
}

// //预警配置列表
// export function warningTemplateList(query) {
//   return request({
//     url: serviceUrl + '/designDetailedOfTimely/getTimeConfigList',
//     method: 'post',
//     data: query
//   })
// }

///视频验收豁免清单保存
export function lpsVideoAcceptanceExemptAdd (query) {
  return request({
    url: serviceUrl + '/lpsVideoAcceptanceExempt/add',
    method: 'post',
    data: query
  })
}

// 导出
export function importTemplate (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/lpsVideoAcceptanceExempt/importTemplate',
    responseType: 'blob',
    data: query
  })
}
// 视频验收豁免清单 导出
export function getExport (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/lpsVideoAcceptanceExempt/export',
    responseType: 'blob',
    data: query
  })
}
// 列表-导入
export const importExcelService = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/lpsVideoAcceptanceExempt/importData`,
    data: reqData
  })
}

//视频验收汇总
export function queryDetailByDim (query) {
  return request({
    url: serviceUrl + '/lpsVideoAcceptanceExempt/queryByDim',
    method: 'post',
    data: query
  })
}
//查询备注列表
export function getNotesQuery (query) {
  return request({
    url: serviceUrl + '/lpsRemarkDicItem/query',
    method: 'post',
    data: query
  })
}
//查询备注修改
export function getNotesadd (query) {
  return request({
    url: serviceUrl + '/lpsRemarkDicItem/add',
    method: 'post',
    data: query
  })
}
//查询备注删除
export function getNotesdel (query) {
  return request({
    url: serviceUrl + '/lpsRemarkDicItem/del?ids=' + query.ids,
    method: 'post',
    data: query
  })
}
//查询用户角色列表
export function queryList (query) {
  return request({
    url: serviceUrl + '/lpsUserRoleConfig/queryList',
    method: 'post',
    data: query
  })
}
//查询用户角色新增
export function addRoleNmae (query) {
  return request({
    url: serviceUrl + '/lpsUserRoleConfig/addRoleNmae',
    method: 'post',
    data: query
  })
}
//查询用户角色保存
export function addRole (query) {
  return request({
    url: serviceUrl + '/lpsUserRoleConfig/addRole',
    method: 'post',
    data: query
  })
}
//查询用户角色删除
export function addRoleDel (query) {
  return request({
    url: serviceUrl + '/lpsUserRoleConfig/del?ids=' + query.ids,
    method: 'post',
    data: query
  })
}
//同步
export function synRoleByRPA (query) {
  return request({
    url: serviceUrl + '/lpsUserRoleConfig/synRoleByRPA',
    method: 'get',
    data: query
  })
}
// QIP视频验收 一键导出
// export function QIPexport (query) {
//   return request({
//     method: 'get',
//     url: serviceUrl + '/lpsVideoAcceptanceExempt/exportAll',
//     responseType: 'blob',
//     data: query
//   })
// }
export function QIPexport () {
  return serviceUrl + '/lpsVideoAcceptanceExempt/exportAll'
}
// // QIP视频验收 一键导出
// export function exportVideoStatisticsByDim (query) {
//   return request({
//     method: 'post',
//     url: serviceUrl + '/lpsVideoAcceptanceExempt/exportVideoStatisticsByDim',
//     responseType: 'blob',
//     data: query
//   })
// }
export function exportVideoStatisticsByDim () {
  return serviceUrl + '/lpsVideoAcceptanceExempt/exportVideoStatisticsByDim'
}
//