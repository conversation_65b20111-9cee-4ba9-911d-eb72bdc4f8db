import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '';

export function listCity(query) {
  return request({
    url: serviceUrl + '',
    method: 'post',
    data: query
  })
}
//建设单位查询
export function getListGroupByUnit(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getListGroupByUnit',
    method: 'post',
    data: query
  })
}
//工程实施经理查询
export function getListGroupByImpManager(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getListGroupByImpManager',
    method: 'post',
    data: query
  })
}
//工程管理经理查询
export function getListGroupByManManager(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getListGroupByManManager',
    method: 'post',
    data: query
  })
}
//详情查询
export function listDetail(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getListDetail',
    method: 'post',
    data: query
  })
}
//接收人员配置查询
export function getUserConfigList(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getUserConfigList',
    method: 'post',
    data: query
  })
}
//获取人员列表
export function userList(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getUserList',
    method: 'post',
    data: query
  })
}
//人员修改
export function updateConfig(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/updateUserConfig',
    method: 'post',
    data: query
  })
}
//人员删除
export function delService(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designDetailedOfTimely/deleteUserConfig',
    data: query
  })
}
//人员新增
export function insertConfig(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/insertUserConfig',
    method: 'post',
    data: query
  })
}
//预警配置列表
export function warningTemplateList(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getTimeConfigList',
    method: 'post',
    data: query
  })
}
//预警配置新增
export function insertTimeConfig(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/insertTimeConfig',
    method: 'post',
    data: query
  })
}
//预警配置修改
export function updateTimeConfig(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/updateTimeConfig',
    method: 'post',
    data: query
  })
}
//预警配置删除
export function deleteTimeConfig(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designDetailedOfTimely/deleteTimeConfig',
    data: query
  })
}
//获取标准工期专业分类列表
export function getDurationList(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getDurationList',
    method: 'post',
    data: query
  })
}
//设计时长配置列表
export function getDurationConfigList(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/getDurationConfigList',
    method: 'post',
    data: query
  })
}
//设计时长新增
export function insertDurationConfig(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/insertDurationConfig',
    method: 'post',
    data: query
  })
}
//设计时长修改
export function updateDurationConfig(query) {
  return request({
    url: serviceUrl + '/designDetailedOfTimely/updateDurationConfig',
    method: 'post',
    data: query
  })
}
//设计时长删除
export function deleteUserConfig(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designDetailedOfTimely/deleteDurationConfig',
    data: query
  })
}
// 导出
export function downloadService() {
  return serviceUrl + '/designDetailedOfTimely/export'
}
//设计批复预警
export function getWarningDetail(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designDetailedOfTimely/getWarningDetail',
    data: query
  })
}
//周报按地市汇总
export function getWeekReportDetail(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designDetailedOfTimely/getWeekReportDetail',
    data: query
  })
}
//周报提交
export function sendWeekReport(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designDetailedOfTimely/submitToOthers',
    data: query
  })
}
// 周报是否有提交
export function personnelCheck(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designDetailedOfTimely/personnelCheck',
    data: query
  })
}