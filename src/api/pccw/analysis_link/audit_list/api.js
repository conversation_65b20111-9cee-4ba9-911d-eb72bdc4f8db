import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl

//明细列表
export function getReviewAttachmentList (query) {
  return request({
    url: serviceUrl + '/auditReport/getReviewAttachmentList',
    method: 'post',
    data: query
  })
}
// 明细列表导出
export function downloadZip () {
  return serviceUrl + '/auditReport/downloadZip'
}
// 查看列表
export function getReviewAttachmentDetail (query) {
  return request({
    url: serviceUrl + '/auditReport/getReviewAttachmentDetail',
    method: 'post',
    data: query
  })
}
// 查看导出
export function downloadFile () {
  return serviceUrl + '/auditReport/downloadFile'
}