import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl
// 关键工序豁免清单列表
export function listQuery (query) {
  return request({
    url: serviceUrl + '/lpsCriticalProcessExempt/query',
    method: 'post',
    data: query
  })
}
// 关键工序豁免清单模板
export function downloadTemplate () {
  return serviceUrl + '/lpsCriticalProcessExempt/downloadTemplate'
}
// 关键工序豁免清单导出
export function criticalProcessExemptData () {
  return serviceUrl + '/lpsCriticalProcessExempt/CriticalProcessExemptData'
}
// 导入关键工序模板配置导出
export function getDownloadTemplate () {
  return serviceUrl + '/lpsCriticalProcessTemplateConfigure/downloadTemplate'
}
//关键工序豁免清单删除
export function inventoryDelete (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/lpsCriticalProcessExempt/delete?ids=' + query.ids,
    data: query
  })
}
// 关键工序豁免清单导入
export const importCriticalProcessExemptData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/lpsCriticalProcessExempt/importCriticalProcessExemptData`,
    data: reqData
  })
}
// 关键工序模板配置导入
export const importKeyProcessTemplateConfigure = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/lpsCriticalProcessTemplateConfigure/importKeyProcessTemplateConfigure`,
    data: reqData
  })
}
// 关键工序豁免清单保存
export function lpsCriticalProcessExemptAdd (query) {
  return request({
    url: serviceUrl + '/lpsCriticalProcessExempt/add',
    method: 'post',
    data: query
  })
}

// 施工派工豁免清单列表
export function dispatchQuery (query) {
  return request({
    url: serviceUrl + '/lpsConstructionDispatch/query',
    method: 'post',
    data: query
  })
}
// 关键工序上传与审核进度情况报表
export function getQueryList (query) {
  return request({
    url: serviceUrl + '/lpsCriticalProcessAuditStatusStatistics/query',
    method: 'post',
    data: query
  })
}

// 导出关键工序
export function cruxExport () {
  return serviceUrl + '/lpsCriticalProcessAuditStatusStatistics/exportAll'
}
// 一键导出关键工序
export function exportByDimension () {
  return serviceUrl + '/lpsCriticalProcessAuditStatusStatistics/exportByDimension'
}

// 施工派工豁免清单导出
export function constructionDispatchExemptData () {
  return serviceUrl + '/lpsConstructionDispatch/ConstructionDispatchExemptData'
}
// 施工派工豁免清单保存
export function lpsConstructionDispatchAdd (query) {
  return request({
    url: serviceUrl + '/lpsConstructionDispatch/add',
    method: 'post',
    data: query
  })
}
// 施工派工豁免清单删除
export function dispatchDelete (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/lpsConstructionDispatch/delete?ids=' + query.ids,
    data: query
  })
}
// 施工派工豁免清单模板
export function dispatchdownloadTemplate () {
  return serviceUrl + '/lpsConstructionDispatch/downloadTemplate'
}
// 施工派工豁免清单导入
export const importConstructionDispatchExemptData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/lpsConstructionDispatch/importConstructionDispatchExemptData`,
    data: reqData
  })
}

// 施工派工规范性情况统计报表
export function normativenessQuery (query) {
  return request({
    url: serviceUrl + '/lpsConstructionDispatchStatistics/query',
    method: 'post',
    data: query
  })
}
// 施工派工规范性情况统计一键导出
export function exportConstructionDispatchStatistics () {
  return serviceUrl + '/lpsConstructionDispatchStatistics/exportConstructionDispatchStatistics'
}
// 施工派工规范性情况统计导出
export function exportALL () {
  return serviceUrl + '/lpsConstructionDispatchStatistics/exportALL'
}

// 查询关键工序模板配置报表
export function templateList (query) {
  return request({
    url: serviceUrl + '/lpsCriticalProcessTemplateConfigure/query',
    method: 'post',
    data: query
  })
}
// 查询关键工序模板配置删除
export function templateDelete (query) {
  return request({
    method: 'post',
    url: serviceUrl + '/lpsCriticalProcessTemplateConfigure/delete?ids=' + query.ids,
    data: query
  })
}
// 查询关键工序模板配置导出
export function criticalProcessTemplateConfigure () {
  return serviceUrl + '/lpsCriticalProcessTemplateConfigure/CriticalProcessTemplateConfigure'
}
// 查询关键工序模板配置保存
export function lpsCriticalProcessTemplateConfigureAdd (query) {
  return request({
    url: serviceUrl + '/lpsCriticalProcessTemplateConfigure/add',
    method: 'post',
    data: query
  })
}
//关键工序查询备注列表
export function getNotesQuery (query) {
  return request({
    url: serviceUrl + '/lpsKeyProcessRemarkItem/query',
    method: 'post',
    data: query
  })
}
//关键工序查询备注修改
export function getNotesadd (query) {
  return request({
    url: serviceUrl + '/lpsKeyProcessRemarkItem/add',
    method: 'post',
    data: query
  })
}
//关键工序查询备注删除
export function getNotesdel (query) {
  return request({
    url: serviceUrl + '/lpsKeyProcessRemarkItem/del?ids=' + query.ids,
    method: 'post',
    data: query
  })
}

//施工派工查询备注列表
export function constructionQuery (query) {
  return request({
    url: serviceUrl + '/lpsConsdispatchRemarkItem/query',
    method: 'post',
    data: query
  })
}
//施工派工查询备注修改
export function constructionAdd (query) {
  return request({
    url: serviceUrl + '/lpsConsdispatchRemarkItem/add',
    method: 'post',
    data: query
  })
}
//施工派工查询备注删除
export function constructionDel (query) {
  return request({
    url: serviceUrl + '/lpsConsdispatchRemarkItem/del?ids=' + query.ids,
    method: 'post',
    data: query
  })
}

//通过项目编码查项目名称
export function getProjectNameByCode (query) {
  return request({
    url: serviceUrl + `/cooperationEvaluate/entryCheck/getProjectNameByCode?projectCode=${query.projectCode}`,
    method: 'get',
    data: query
  })
}