import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl

export function listProjectImplement (query) {
  return request({
    url: serviceUrl + '/materialArrivalTrack/getListByImpManager',
    method: 'post',
    data: query
  })
}

export function listProjectManage (query) {
  return request({
    url: serviceUrl + '/materialArrivalTrack/getListByManManager',
    method: 'post',
    data: query
  })
}

export function listCity (query) {
  return request({
    url: serviceUrl + '/materialArrivalTrack/getListByCity',
    method: 'post',
    data: query
  })
}

export function listDetail (query) {
  return request({
    url: serviceUrl + '/materialArrivalTrack/getListDetail',
    method: 'post',
    data: query
  })
}

// 地市导出
export function downloadCityService () {
  return serviceUrl + '/materialArrivalTrack/exportByCity'
}

// 项目管理经理导出
export function downloadManageService () {
  return serviceUrl + '/materialArrivalTrack/exportByManManager'
}

// 项目实施经理导出
export function downloadImplementService () {
  return serviceUrl + '/materialArrivalTrack/exportByImpManager'
}

// 明细导出
export function downloadDetailService () {
  return serviceUrl + '/materialArrivalTrack/exportDetail'
}

//超期订单预警工单列表
export function overdueOrderWarningWorkOrderList (query) {
  //warningId没有值就返回空数组
  if (!query.warningId) {
    return Promise.resolve([]);
  }
  return request({
    url: serviceUrl + '/materialArrivalTrack/getMessDetail',
    method: 'post',
    data: query
  })
}

//提交特殊原因接收订单
export function submitSpecialReasonForAcceptingOrder (query) {
  return request({
    url: serviceUrl + '/materialArrivalTrack/submitForApproval',
    method: 'post',
    data: query
  })
}

//特殊原因接收订单审批待办
export function getSpecialReasonOrderApprovalPendingDetails (query) {
  return request({
    url: serviceUrl + '/materialArrivalTrack/getDetailInFlow',
    method: 'post',
    data: query
  })
}


