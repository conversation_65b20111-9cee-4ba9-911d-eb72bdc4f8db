import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl
console.log(serviceUrl)
export function listCity(query) {
  return request({
    url: serviceUrl + '/designSurvey/getCitySummary',
    method: 'post',
    data: query
  })
}

export function listDesignUnit(query) {
    return request({
      url: serviceUrl + '/designSurvey/getDesignUnitSummary',
      method: 'post',
      data: query
    })
  }

export function listProjectManagement(query) {
  return request({
    url: serviceUrl + '/designSurvey/getProImpMgrSummary',
    method: 'post',
    data: query
  })
}
export function getProMgtMgrSummary(query) {
  return request({
    url: serviceUrl + '/designSurvey/getProMgtMgrSummary',
    method: 'post',
    data: query
  })
}
// 站点导出
export function downloadSite() {
  return serviceUrl + '/designSurvey/exportSiteInfo'
}
// 详情导出
export function downloadService() {
    return serviceUrl + '/designSurvey/exportDesignSurveyDetail'
}
// 地市导出
export function downloadCity() {
  return serviceUrl + '/designSurvey/exportCitySummary'
}
// 工程导出
export function downloadEngineering() {
  return serviceUrl + '/designSurvey/exportProMgtMgrSummary'
}
// 设计单位导出
export function downloadUnit() {
  return serviceUrl + '/designSurvey/exportDesignUnitSummary'
}
// 工程实施导出
export function downloadExportProImpMgrSummary() {
  return serviceUrl + '/designSurvey/exportProImpMgrSummary'
}
export function listDesignSurveySpecifyReportAlert(query) {
  return request({
    url: serviceUrl + '/designSurvey/getDesignSurveyDetail',
    method: 'post',
    data: query
  })
}
// 查询安全生产费任务详细表
export function checkTaskList(query) {
  return request({
    url: 'https://www.fastmock.site/mock/a170aa79badd9fa3e1c5746207bd2a03/pccw/api/safety_payment_alert',
    method: 'post',
    data: query
  })
}
//设计勘察审批人员配置界面查询
export function queryListService(query) {
  return request({
    url: serviceUrl + '/designSurvey/getConfig',
    method: 'get',
    data: query
  })
}
//设计勘察审批人员配置界面新增
export function insertConfig(query) {
  return request({
    url: serviceUrl + '/designSurvey/insertConfig',
    method: 'post',
    data: query
  })
}
//设计勘察审批人员配置界面保存
export function updateConfig(query) {
  return request({
    url: serviceUrl + '/designSurvey/updateConfig',
    method: 'post',
    data: query
  })
}
//设计勘察审批人员配置界面删除
export function delService(data) {
  return request({
    method: 'post',
    url: serviceUrl + '/designSurvey/deleteConfig',
    params: data
  })
}
//打卡导入批量删除
export function delBatchClock(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designSurvey/delBatchClock?ids=' + query.ids,
    data: query
  })
}
//设计勘察配置设计单位人员查询
export function queryDesignUnitPerson(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designSurvey/queryDesignUnitPerson',
    data: query
  })
}
//设计勘察配置设计统筹人员查询
export function queryDesignCoordinator(data) {
  return request({
    method: 'get',
    url: serviceUrl + '/designSurvey/queryDesignCoordinator',
    params: data
  })
}
//打卡任务清单列表
export function getClockTaskList(data) {
  return request({
    url: serviceUrl + '/designSurvey/getClockTaskList',
    method: 'get',
    params: data
  })
}
// 修改打卡任务清单
export function updateClockTaskList(data) {
  return request({
    method: 'post',
    url: serviceUrl + '/designSurvey/updateClockTaskList',
    data: data
  })
}
// 列表-导入
export const importExcelService = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/designSurvey/importClockInData`,
    data: reqData
  })
}
// 站点-导入
export const importExcelSite = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/designSurvey/importSiteInfo`,
    data: reqData
  })
}
//站点-列表
export function getSiteInfo(query) {
  return request({
    url: serviceUrl + '/designSurvey/getSiteInfo',
    method: 'post',
    data: query
  })
}
//设计勘察专业偏差标准配置 列表
export function getDeviationConfig(query) {
  return request({
    url: serviceUrl + '/designSurvey/getDeviationConfig',
    method: 'post',
    data: query
  })
}
//设计勘察专业偏差标准配置
export function getPrimaryMajor(data) {
  return request({
    method: 'get',
    url: serviceUrl + '/designSurvey/getPrimaryMajor',
    params: data
  })
}
//设计勘察专业偏差标准配置新增
export function inserDeviationtConfig(query) {
  return request({
    url: serviceUrl + '/designSurvey/inserDeviationtConfig',
    method: 'post',
    data: query
  })
}
//设计勘察专业偏差标准配置删除
export function deleteDeviationConfig(data) {
  return request({
    method: 'post',
    url: serviceUrl + '/designSurvey/deleteDeviationConfig',
    params: data
  })
}
//设计勘察专业偏差标准配置保存
export function updateDeviationConfig(query) {
  return request({
    url: serviceUrl + '/designSurvey/updateDeviationConfig',
    method: 'post',
    data: query
  })
}
//设计勘察专业偏差标准配置 项目管理专业查询
export function queryProjectManagementMajor(query) {
  return request({
    url: serviceUrl + '/designSurvey/queryProjectManagementMajor',
    method: 'post',
    data: query
  })
}
//第三方打卡数据查询
export function queryThirdClockData(query) {
  return request({
    url: serviceUrl + '/designSurvey/queryThirdClockData',
    method: 'post',
    data: query
  })
}
// 导出打卡信息模板
export function exportClockTemp() {
  return serviceUrl + '/designSurvey/exportClockTemp'
}
// 导出打卡数据
export function exportClockInData() {
  return serviceUrl + '/designSurvey/exportClockInData'
}
// 导入打卡数据-导入
export const importClockInData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/designSurvey/importClockInData`,
    data: reqData
  })
}
//站点导入批量删除
export function delBatch(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/designSurvey/delBatch?ids=' + query.ids,
    data: query
  })
}
// 设计院导入打卡数据-导入
export const importThirdData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/designSurvey/importThirdData`,
    data: reqData
  })
}
// PMS打卡数据查询
export const queryPmsClockData = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/designSurvey/queryPmsClockData`,
    data: reqData
  })
}
