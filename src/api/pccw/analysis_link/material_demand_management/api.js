import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl

//地区+项目编码+物料编码维度获取汇总表
export function listByMaterialAndUnitAndProject(query) {
  return request({
    url: serviceUrl + '/MaterialDemandMgt/getListByMaterialAndUnitAndProject',
    method: 'post',
    data: query
  })
}

//地区+物料编码维度获取汇总表
export function listByMaterialAndUnit(query) {
    return request({
      url: serviceUrl + '/MaterialDemandMgt/getListByMaterialAndUnit',
      method: 'post',
      data: query
    })
}

// 导出
export function downloadService() {
    return serviceUrl + '/MaterialDemandMgt/exportTotal'
}

//待办工单数据
export function listTodoService(query) {
  return request({
    url: serviceUrl + '/MaterialDemandMgt/getDetailListInFlow',
    method: 'post',
    data: query
  })
}
export function getMessageDetail(query) {
  return request({
    url: serviceUrl + '/MaterialDemandMgt/getMessageDetail',
    method: 'post',
    data: query
  })
}
