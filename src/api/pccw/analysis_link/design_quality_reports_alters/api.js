import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl
//明细列表
export function getAllDetail (query) {
  return request({
    url: serviceUrl + '/designStatistics/getAllDetail',
    method: 'post',
    data: query
  })
}
// 明细列表导出
export function exportDesignStatisticsAllDetail () {
  return serviceUrl + '/designStatistics/exportDesignStatisticsAllDetail'
}

//地市列表
export function getAreaDetails (query) {
  return request({
    url: serviceUrl + '/designStatistics/getAreaDetails',
    method: 'post',
    data: query
  })
}
// 地市列表导出
export function exportDesignStatisticsArea () {
  return serviceUrl + '/designStatistics/exportDesignStatisticsArea'
}
//设计单位列表
export function getDesignUnitDetailss (query) {
  return request({
    url: serviceUrl + '/designStatistics/getDesignUnitDetailss',
    method: 'post',
    data: query
  })
}
// 设计单位导出
export function exportDesignStatisticsDesignUnit () {
  return serviceUrl + '/designStatistics/exportDesignStatisticsDesignUnit'
}