import request from '@/utils/request.js'

const serviceUrl = window.g.comprehensiveUrl + '/databaseView'
//查询数据库列表
export const dbList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/db/list',
    data: query
  })
}
//查询数据列表
export const selectDataList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/db/data/list',
    data: query
  })
}

//查询数据表字段列表
export function columnList(tableName) {
  return request({
    method: 'get',
    url: serviceUrl + '/db/column/list/' + tableName,
  })
}
//查询数据列表
export const selectLogFileList = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/logfile/list',
    data: query
  })
}
//上传日志文件
export const uploadLogFile = (query) => {
  return request({
    method: 'post',
    url: serviceUrl + '/logfile/upload',
    data: query
  })
}
