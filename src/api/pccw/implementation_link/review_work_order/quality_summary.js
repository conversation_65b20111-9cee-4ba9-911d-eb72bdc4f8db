import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl

//质量汇总报表条件
export function exportByDimension () {
  return serviceUrl + '/summaryReport/export'
}

//质量汇总报表全部
export function exportAll () {
  return serviceUrl + '/summaryReport/exportAll'
}
//质量汇总报列表
export const getTotalList = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/summaryReport/getTotalList`,
    data: reqData
  })
}