import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl + '';

// 查询安全生产费支付情况表
export function listSafetyPaymentQuery(query) {
  return request({
    url: serviceUrl + '/safetyCost/querySafetyCost',
    method: 'post',
    data: query
  })
}

// 导出
export function download(){
  return serviceUrl + '/safetyCost/download'
}
// 列表-导出
export function getExport(){
  return serviceUrl + '/safetyCost/export'
}
// 导出
export function workFlowListExport(){
  return serviceUrl + '/safetyCost/workFlowListExport'
}
// 列表-导出压缩包
export function downloadZip(){
  return serviceUrl + '/safetyCost/downloadZip'
}
//预警配置列表
export function warningTemplateList(query) {
  return request({
    url: serviceUrl + '/safetyCost/getTimeConfigList',
    method: 'post',
    data: query
  })
}
//预警配置新增
export function insertTimeConfig(query) {
  return request({
    url: serviceUrl + '/safetyCost/insertTimeConfig',
    method: 'post',
    data: query
  })
}
//预警配置修改
export function updateTimeConfig(query) {
  return request({
    url: serviceUrl + '/safetyCost/updateTimeConfig',
    method: 'post',
    data: query
  })
}
//预警配置删除
export function deleteTimeConfig(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/safetyCost/deleteTimeConfig',
    data: query
  })
}
export function workFlowList(query) {
  return request({
    url: serviceUrl + '/safetyCost/workFlowList',
    method: 'post',
    data: query
  })
}
//查询豁免清单
export function getRemitList(query) {
  return request({
    url: serviceUrl + '/safetyCost/getRemitList',
    method: 'post',
    data: query
  })
}
//豁免清单删除
export function deleteRemitLis(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/safetyCost/deleteRemitList',
    data: query
  })
}
//豁免清单新增
export function insertRemitList(query) {
  return request({
    url: serviceUrl + '/safetyCost/insertRemitList',
    method: 'post',
    data: query
  })
}
//豁免清单修改
export function updateRemitList(query) {
  return request({
    url: serviceUrl + '/safetyCost/updateRemitList',
    method: 'post',
    data: query
  })
}
//查询工单号
export function getProjectInformation(query) {
  return request({
    method: 'post',
    url: serviceUrl + '/safetyCost/getProjectInformation',
    data: query
  })
}