import request from '@/utils/request.js'
const serviceUrl = window.g.comprehensiveUrl
// 查询安全生产费任务详细表
export function listSafetyPaymentAlert(query) {
  return request({
    url: 'https://www.fastmock.site/mock/a170aa79badd9fa3e1c5746207bd2a03/pccw/api/safety_payment_alert',
    method: 'post',
    data: query
  })
}
// 列表-导出
export function download (){
  return serviceUrl + '/safetyCost/download'
}
// 安全生产费报账待办工单上部分
export function getSafetyCost(query) {
  return request({
    url: serviceUrl + '/safetyCost/getSafetyCost',
    method: 'post',
    data: query
  })
}
// 安全生产费报账待办工单下部分
export function getSafetyCostDetail(query) {
  return request({
    url: serviceUrl + '/safetyCost/getSafetyCostDetail',
    method: 'post',
    data: query
  })
}

// 安全生产费报账待阅列表
export function getPendingSafetyCost(query) {
  return request({
    url: serviceUrl + '/safetyCost/getPendingSafetyCost',
    method: 'post',
    data: query
  })
}
// 安全生产费报账待办-导入
export const importExcelupload = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/safetyCost/upload`,
    data: reqData
  })
} 
// 安全生产费报账待办-导入
export const saveSafetyCost = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/safetyCost/saveSafetyCost`,
    data: reqData
  })
}
// 安全生产费报账待办-删除
export const deleteFile = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/safetyCost/deleteFile`,
    data: reqData
  })
}
// 安全生产费暂挂
export const pendingSafetyCost = (reqData) => {
  return request({
    method: 'post',
    url: serviceUrl + `/safetyCost/pendingSafetyCost`,
    data: reqData
  })
}