import request from '@/utils/request.js'
const serviceUrl = window.g.systemUrl + '/sysUser'
// 查询sysUser列表
export function listSysUser(query) {
  return request({
    url: serviceUrl + '/list',
    method: 'get',
    params: query
  })
}

// 查询sysUser详细
export function getSysUser(userId) {
  return request({
    url: serviceUrl + '/' + userId,
    method: 'get'
  })
}

// 新增sysUser
export function addSysUser(data) {
  return request({
    url: serviceUrl,
    method: 'post',
    data: data
  })
}

// 修改sysUser
export function updateSysUser(data) {
  return request({
    url: serviceUrl,
    method: 'put',
    data: data
  })
}

// 删除sysUser
export function delSysUser(userId) {
  return request({
    url: serviceUrl + '/' + userId,
    method: 'delete'
  })
}

// 导出sysUser
export function downloadService() {
  return serviceUrl + '/export'
}
