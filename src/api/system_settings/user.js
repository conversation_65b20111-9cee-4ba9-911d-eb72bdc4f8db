/**
 * 用户管理API
 */
import request from '@/utils/request.js'
const adminBaseUrl = `${window.g.baseUrl}/admin`
const sysUserService = `${window.g.baseUrl}/admin` + '/sysuser'
const SysDeptService = `${window.g.baseUrl}/admin` + '/sysdept'
const SysServiceBase = `${window.g.baseUrl}/admin` + '/sysplatforms'
const sysRoleGroupService = `${window.g.baseUrl}/admin` + '/sysrolegroup' // 角色组管理
const SysRoleService = `${window.g.baseUrl}/admin` + '/sysrole' // 角色管理
const sysUseRroleService = `${window.g.baseUrl}/admin` + '/sysuserrole' // 人员关系管理

/**
 * table列表
 */
export function tableListService(data) {
  return request({
    method: 'get',
    url: adminBaseUrl + '/eomssysuser/page',
    params: data
  })
}

/**
 * 新增编辑列表
 */
export function addService(method, data) {
  return request({
    method: method,
    url: sysUserService,
    data: data
  })
}

/**
 * 删除
 */
export function delService(data) {
  return request({
    method: 'delete',
    url: sysUserService + '/' + data
  })
}

/**
 * 查询子账号
 */
export function getChildService(data) {
  return request({
    method: 'get',
    url: sysUserService + '/child/info/' + data
  })
}

/**
 * 设为主账号
 */
export function toMainService(data) {
  return request({
    method: 'put',
    url: sysUserService + '/change',
    params: data
  })
}

/**
 * 角色调岗
 */
export function transferService(data) {
  return request({
    method: 'put',
    url: sysUserService + '/transfer',
    params: data
  })
}

/**
 * 重置密码
 */
export function resetPwdService(data) {
  return request({
    method: 'put',
    url: sysUserService + '/reset/' + data
  })
}

/**
 * 获取组织列表
 */
export function getDeptListService(data) {
  return request({
    method: 'get',
    url: SysDeptService + '/tree',
    params: data
  })
}

/**
 * 获取系统列表
 */
export function chooseSystemService(data) {
  return request({
    method: 'get',
    url: SysServiceBase + '/list',
    params: data
  })
}

// 人员绑定角色的操作----------------------------------

/**
 * 获取角色树状列表
 */
export function queryTypeTree(data) {
  return request({
    method: 'get',
    url: sysRoleGroupService + '/tree',
    params: data
  })
}

/**
 * 获取角色table列表
 */
export function tableEmpListService(data) {
  return request({
    method: 'get',
    url: SysRoleService + '/group/' + data
  })
}

/**
 * 根据角色组获取分页角色列表
 */
export function getRolePageService(params) {
  return request({
    method: 'get',
    url: SysRoleService + '/page',
    params
  })
}

/**
 * 根据用户查询角色
 */
export function userRoleService(data) {
  return request({
    method: 'get',
    url: SysRoleService + '/user/' + data
  })
}

/**
 * 给用户绑定角色
 */
export function bindRoleService(data) {
  return request({
    method: 'post',
    url: sysUseRroleService + '/saveUserRoleByUserId',
    data
  })
}

/**
 * 禁用
 */
export function disableUserService(data) {
  return request({
    method: 'put',
    url: sysUserService + '/disable',
    params: data
  })
}

/**
 * 启用
 */
export function enableUserService(data) {
  return request({
    method: 'put',
    url: sysUserService + '/enable',
    params: data
  })
}

/**
   * 根据用户id查用户信息
   * */
export function getUserInfoService(data) {
  return request({
    method: 'GET',
    url: `${sysUserService}/` + data
  })
}

/**
   * 部门树
   */
export function getDeptTreeService(data) {
  return request({
    method: 'GET',
    url: adminBaseUrl + '/sysdept/tree/all',
    params: data
  })
}

/**
   * 获取角色列表（复制子角色）
   */
export function findRoleService(data) {
  return request({
    method: 'POST',
    url: adminBaseUrl + '/eomssysuser/findRoles',
    data: data
  })
}

/**
   * 获取角色树
   * @returns
   */
export function getRoleTreeService(data) {
  return request({
    method: 'GET',
    url: adminBaseUrl + '/eomssysrolegroup/tree',
    params: data
  })
}

/**
   * 复制子角色
   */
export function copyRoleService(data) {
  return request({
    method: 'POST',
    url: adminBaseUrl + '/eomssysuser/copyRoles',
    params: data
  })
}
// 导入
export function excelImport(data) {
  return request({
    url: '/admin/sysuser/import',
    method: 'post',
    data: data
  })
}
