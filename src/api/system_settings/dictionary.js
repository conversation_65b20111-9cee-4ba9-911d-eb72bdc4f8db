/*
 * @Descripttion:
 * @version:
 * @Author: sueRimn
 * @Date: 2020-12-21 15:09:31
 * @LastEditors: sueRimn
 * @LastEditTime: 2021-05-20 15:30:58
 */
/**
 * 字典管理API
 */
import request from '@/utils/request.js'
const DictionaryBase = `${window.g.baseUrl}/admin` + '/sysdict'

/**
  * 获取字典列表的数据
  * @param params
  * @returns {AxiosPromise}
  */
export function getSysDicList(params) {
  return request({
    url: DictionaryBase + '/list',
    method: 'get',
    params: params
  })
}
// 新增
export function addSysDic(data) {
  return request({
    url: DictionaryBase,
    method: 'post',
    data: data
  })
}
// 修改
export function updateSysDic(data) {
  return request({
    url: DictionaryBase,
    method: 'put',
    data: data
  })
}
// 删除
export function delSysDic(dictId) {
  return request({
    url: DictionaryBase + '/' + dictId,
    method: 'delete'
  })
}

