/**
 * 角色管理API
 */
import request from '@/utils/request.js'

const SysRoleService = `${window.g.baseUrl}/admin` + '/eomssysrole' // 角色管理
const Sysarea = `${window.g.baseUrl}/admin` + '/sysarea' // 区域机构管理
const EOMSSysRoleService = `${window.g.baseUrl}/admin` + '/eomssysrole' // 新增了角色等级的角色管理接口
const sysRoleGroupService = `${window.g.baseUrl}/admin` + '/eomssysrolegroup' // 角色组管理
const SysServiceBase = `${window.g.baseUrl}/admin` + '/sysplatforms' // 接入系统管理
const SysDeptService = `${window.g.baseUrl}/admin` + '/sysdept' // 单位管理
const sysUserService = `${window.g.baseUrl}/admin` + '/eomssysuser' // 用户管理
const sysUseRroleService = `${window.g.baseUrl}/admin` + '/eomssysuserrole' // 人员关系管理
const sysResourceService = `${window.g.baseUrl}/admin` + '/sysresource' // 菜单管理
const sysRroleUseService = `${window.g.baseUrl}/admin` + '/sysroleresource' // 角色绑定菜单
const admin = `${window.g.baseUrl}/admin` + '/sysdictext' // 网络分类下拉数据接口

/**
 * table列表
 */
export function tableListService(data) {
  return request({
    method: 'get',
    url: SysRoleService + '/page',
    params: data
  })
}

/**
 * 新增、编辑
 */
export function addService(method, data) {
  return request({
    method: method,
    url: SysRoleService,
    data: data
  })
}

/**
 * 通过id查询
 */
export function getByIdService(data) {
  return request({
    method: 'get',
    url: SysRoleService + '/' + data
    // params: data
  })
}

/**
 * 通过id查询组织机构
 */
export function DeptNameByIdService(data) {
  return request({
    method: 'get',
    url: Sysarea + '/' + data
    // params: data
  })
}

/**
 * 删除
 */
export function delService(data) {
  return request({
    method: 'delete',
    url: SysRoleService + '/' + data
  })
}

/**
 * 角色等级下拉
 */
export function levelSelectService(data) {
  return request({
    method: 'get',
    url: EOMSSysRoleService + '/roleLevelList',
    params: data
  })
}

// 网络分类接口
export function getNetType(data) {
  return request({
    method: 'get',
    url: admin + '/tree',
    params: data
  })
}

/**
 * 角色树状列表
 */
export function queryTypeTree(data) {
  return request({
    method: 'get',
    url: sysRoleGroupService + '/tree',
    params: data
  })
}

/**
 * 获取系统
 */
export function chooseSystemService(data) {
  return request({
    method: 'get',
    url: SysServiceBase + '/list',
    params: data
  })
}

/**
 * 角色类型新增
 */
export function addTypeService(data) {
  return request({
    method: 'post',
    url: sysRoleGroupService,
    data: data
  })
}

/**
 * 角色类型修改
 */
export function editTypeService(data) {
  return request({
    method: 'put',
    url: sysRoleGroupService,
    data: data
  })
}

/**
 * 角色类型删除
 */
export function delTypeService(data) {
  return request({
    method: 'delete',
    url: sysRoleGroupService + '/' + data
  })
}

/**
 * 获取角色组信息
 */
export function getRoleService(data) {
  return request({
    method: 'get',
    url: sysRoleGroupService + '/' + data
  })
}

// 绑定角色部分-----------------------------------------

/**
 * 获取公司列表
 */
export function getCompanyService(data) {
  return request({
    method: 'get',
    url: SysDeptService + '/tree',
    params: data
  })
}

/**
 * 获取用户列表
 */
export function getOutUserService(data) {
  return request({
    method: 'get',
    url: sysUserService + '/page',
    params: data
  })
}

/**
 * 绑定用户状态提交
 */
export function bindUserService(data) {
  return request({
    method: 'put',
    url: sysUseRroleService + '/role',
    data: data
  })
}

// 角色绑定菜单部分---------------------------

/**
 * 获取菜单列表
 */
export function getSpecService(data) {
  return request({
    method: 'get',
    url: sysResourceService + '/tree',
    params: data
  })
}

/**
 * 获取已绑定菜单
 */
export function getSelectService(data) {
  return request({
    method: 'get',
    url: sysResourceService + '/tree/role',
    params: data
  })
}

/**
 * 绑定菜单
 */
export function bindSpecService(data) {
  return request({
    method: 'POST',
    url: sysRroleUseService + '/update',
    data
    // params: data
  })
}

/**
 * 检验是否为流程角色
 */
export function isWorkFlowRoleService(data) {
  return request({
    method: 'GET',
    url: sysRoleGroupService + '/iswfRole/' + data
    // params: data
  })
}

// 设置菜单菜单部分---------------------------

/**
 * 复制菜单
 */
export function copyMenusService(data) {
  return request({
    method: 'post',
    url: sysRroleUseService + '/copy',
    params: data
  })
}

export function referenceMenusService(data) {
  return request({
    method: 'post',
    url: sysRroleUseService + '/reference',
    params: data
  })
}

export function excelImport(data) {
  return request({
    method: 'post',
    url: '/admin/sysrolegroup/import',
    data: data
  })
}
export function excelImportRole(data) {
  return request({
    method: 'post',
    url: '/admin/eomssysrole/importRole',
    data: data
  })
}
// 左移右移
export function bindUsersService(data) {
  return request({
    method: 'post',
    url: window.g.baseUrl+'/admin/sysrole/binduser',
    data: data
  })
}
/**
 * 获取根
 */
export function getRootService(id) {
  return request({
    method: 'get',
    url: `${SysDeptService}/${id}` ,
  })
}
