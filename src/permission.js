import router from './router'
import store from './store'
import { Message, Notification } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken, setToken } from '@/utils/auth' // get token from cookie
import { encryption } from '@/utils/encryption'
import { login, getUserMenuService,logpagevisit } from '@/api/user'
import {confirmMessageService} from "@/api/message_manager/message_manager_api.js";

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start()

  // determine whether the user has logged in
  const hasToken = getToken()
  console.log(hasToken, '=====')
  if (hasToken) {
    let logMenu = sessionStorage.getItem("logMenu")?JSON.parse(sessionStorage.getItem("logMenu")):{}
    let { path ,name ,id } = logMenu
    if(to.path == '/home'){
      let obj = {
        clientType:1,
        visitPageCode:'jxfzhome',
        visitPageName:'首页',
        visitUri:'/home',
      }
      logpagevisit(obj).then(res=>{
        if(res && res.code == '0000'){

        }
      }).catch(e=>{
      })
    }else if(path && path.indexOf(to.path)>-1){
      let index = path.indexOf(to.path)
      let obj = {
        clientType:1,
        visitPageCode:id[index],
        visitPageName:name[index],
        visitUri:to.path,
      }
      logpagevisit(obj).then(res=>{
        if(res && res.code == '0000'){

        }
      }).catch(e=>{
      })
    }
    // 检查地址里是否含有portalMessageId，如果有就先消待办，再跳转，并且通知父页面刷新待办
    if(to.query && to.query.messageId){
      confirmMessageService({msgId: to.query.messageId}).finally(_ => {
        next()
        try{
          var targetWindow = window.opener;
          targetWindow.postMessage("refreshTodo","*");
        }catch(e){}
      })
    }else{
      next();
    }
  } else {
    /* has no token*/
    //门户单点登录url &portalFlag=1
    if (to.query && to.query.portalFlag == 1) {
       let acc = to.query.accountSsoCas;
       let fromEmailName = to.query.fromEmailName;
         //没账号时，重定向获取当前账号
        if (!acc) {
          if(fromEmailName){
            //fromEmailName 为moa请求，账号加密
            window.location.href = (window.g.auth_cas_url || '') + '/cas/redirectMoa?url=' + encodeURIComponent(window.location.href);
          }
          else{
            // portal请求
            window.location.href = (window.g.auth_cas_url || '') + '/cas/redirect?url=' + encodeURIComponent(window.location.href);
          }
          return
        }

      const dataParam = {
        account: acc,
        grant_type: "account",
        scope: "server",
        platformId: "epms",
        client_id: encryption({
          data: window.g.client_id,
          key: "****************",
        }),
        client_secret: encryption({
          data: window.g.client_secret,
          key: "****************",
        }),
      };
      if (window.g.ssoParams && window.g.ssoParams.length) {
        window.g.ssoParams.forEach(item => {
          if (to.query[item]) {
            dataParam[item] = to.query[item]
          }
        })
      }
      //通过账号获取token
      login(dataParam)
        .then((res) => {
          if (res && res.data && res.data.access_token) {
            const { data } = res
            const { realName, userId, deptId, deptName, firstDeptId, secondDeptId, authorities } = data
            setToken(data.access_token)
            sessionStorage.setItem('realName', realName)
            sessionStorage.setItem('userId', userId)
            sessionStorage.setItem('deptId', deptId)
            sessionStorage.setItem('deptName', deptName)
            sessionStorage.setItem('firstDeptId', firstDeptId)
            sessionStorage.setItem('secondDeptId', secondDeptId)
            sessionStorage.setItem('authorities', JSON.stringify(authorities))
            // 储存用户菜单信息
            getUserMenuService({
              queryAll: true,
              rootId: 'root-jx-fz',
              type: '0'
            }).then((res) => {
              if (res && res.data) {
                sessionStorage.setItem(
                  'private_menu_pts',
                  JSON.stringify(res.data)
                )
                let req = {}
                for (const key in to.query) {
                  if (key != 'accountSsoCas' && key != 'ept') {
                    req[key] = to.query[key]
                  }
                }

                // 检查地址里是否含有portalMessageId，如果有就先消待办，再跳转，并且通知父页面刷新待办
                if(to.query && to.query.messageId){
                  confirmMessageService({msgId: to.query.messageId}).finally(_ => {
                    next(
                      {
                        path: to.path,
                        query: req
                      }
                    ); //跳转到home页面
                    try{
                      var targetWindow = window.opener;
                      targetWindow.postMessage("refreshTodo","*");
                    }catch(e){}
                  })
                }else{
                  next(
                    {
                      path: to.path,
                      query: req
                    }
                  ); //跳转到home页面
                }
              }else{
                next(`/login?redirect=${to.path}`)
                NProgress.done()
              }
            });
          } else {
            next(`/login?redirect=${to.path}`)
            NProgress.done()
          }
        })
        .catch(() => {
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        });
    } else if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
router.onError((error) => {
  if (error) {
    // 去登录
    // 如果是路由错误，则跳转到登录页
    Notification({
      title: '',
      customClass: 'appversion-change-notification',
      message: '<div><p>检测到版本更新，请<a class="btn" href="javascript:location.replace(\'/login\');">重新登录</a></p></div>',
      position: 'bottom-right',
      dangerouslyUseHTMLString: true
    })
  }
})
