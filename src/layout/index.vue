<template>
  <div :class="classObj" class="app-wrapper">
    <headerbar class="headerbar-container" v-if="needHeader"/>
    <div class="main-container">
      <sidebar class="sidebar-container" v-if="needSidebar" />
      <div class="sub-main-container" :class="{hasTagsView:needTagsView}">
        <div class="sub-main-container--tagsview" :class="{'fixed-header':fixedHeader}">
          <tags-view v-if="needTagsView" :needSidebar="needSidebar" :opened="sidebar.opened" />
        </div>
        <div v-if="$route.query.messageType !== undefined" class="message-btn-wrapper">
          <el-button v-if="$route.query.messageType == 1" type="success" @click="messageConfirmHandle">已阅</el-button>
        </div>
        <app-main />
      </div>
    </div>
  </div>
</template>

<script>
import { AppMain, Headerbar, Sidebar, TagsView } from './components'
import { mapState } from 'vuex'
import {confirmMessageService} from "@/api/message_manager/message_manager_api";

export default {
  name: 'Layout',
  components: {
    AppMain,
    Headerbar,
    Sidebar,
    TagsView
  },
  computed: {
    ...mapState({
      headerbar: state => state.app.headerbar,
      sidebar: state => state.app.sidebar,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader,
      needHeader: state => state.settings.needHeader,
      needSidebar: state => state.settings.needSidebar
    }),
    classObj() {
      return {
        hideHeaderbar: !this.headerbar.opened,
        openHeaderbar: this.headerbar.opened,
        hideSidebar: !this.sidebar.opened && this.needSidebar,
        openSidebar: this.sidebar.opened && this.needSidebar,
        noHeader: !this.needHeader,
        noSidebar: !this.needSidebar,
        withoutAnimation: this.sidebar.withoutAnimation,
      }
    },
  },
  watch: {
    '$route.path': {
      handler(n, o){
        // 是否需要header(不需要的话是直接不加载，而非显示隐藏)
        let whetherNeedHeader = false
        if (!(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))) {
          whetherNeedHeader = (n !== '/home' && n !== '/message/view')
        }
        this.$store.dispatch('settings/changeSetting', {
          key: 'needSidebar',
          value:whetherNeedHeader
        })

        // 是否需要header(不需要的话是直接不加载，而非显示隐藏),（ 为了防止在其他页面移除了页签，这个页面都手动显示页签）
        let whetherNeedTagsView = false
        if (!(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))) {
          whetherNeedTagsView = true
        }
        this.$store.dispatch('settings/changeSetting', {
          key: 'tagsView',
          value: whetherNeedTagsView
        })
      },
      immediate: true
    },
    '$route.query.messageType'(n, o){
      if (n === '0' || n === 0) {
        const id = this.$route.query.messageId
        confirmMessageService({msgId: id })
          .then(res => {})
      }
    }
  },
  methods: {
    // 确认消息
    messageConfirmHandle(){
      const id = this.$route.query.messageId
      confirmMessageService({msgId: id })
        .then(res => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  @import "~@/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$sideBarWidth});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px)
  }
  .sub-main-container--tagsview{
    position: fixed;
    top: 70px;
    width: 100%;
    z-index: 999;
  }
  .message-btn-wrapper{
    position: absolute;
    top: calc(#{$appMainPaddingTop});
    z-index: 100;
    left: $appMainPaddingLeft;
  }
</style>
