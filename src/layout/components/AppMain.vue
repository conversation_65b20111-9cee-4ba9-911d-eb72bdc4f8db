<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.generatePathKey(this.$route.path, this.$route.query)
    }
  },
  methods: {
    // 为每个路径重新生成一个唯一的key值
    generatePathKey(path, query = {}){
      const queryKeys = Object.keys(query)
      if(queryKeys && queryKeys.length){
        const sortedKeys = queryKeys.sort()
        let newPath = `${path}?`
        sortedKeys.forEach(it => {
          newPath += `it=${query[it]}&`
        })
        return newPath
      }else{
        return path
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";
.app-main {
  min-height: calc(100vh - #{$headerBarHeight});
  width: 100%;
  position: relative;
  overflow: hidden;
  background: $bgMainColor;
  padding: $appMainPaddingTop $appMainPaddingLeft $appMainPaddingRight 20px;
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .fixed-header+.app-main {
    padding-top: 84px;
  }
}

.noHeader {
  .app-main{
    padding-top: 20px;
    min-height: 100vh;
  }
}

@media only screen and (max-width: 600px) {
  .app-main{
    overflow-x: auto;
    min-width: 600px;// 设置这个宽度的目的，目前来说，是考虑到在小屏幕时，有内容的正文页如果显示为100%的话只能看到几个中文，所以设置了最小宽度，超出的话自己滚动
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
