/**
* permission_routes -> mockRoutes
*/
<template>
  <div>
<!--    <div class="search-container">-->
<!--      <el-input />-->
<!--    </div>-->
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item v-for="route in todoList" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem },
  computed: {
    ...mapGetters([
      'permission_routes',
      'sidebar'
    ]),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    },
  },
  watch:{
    $route: {
      handler(n, o){
        let routeDatas = JSON.parse(sessionStorage.getItem('private_menu_pts')).filter((item)=>{
          return item.path == this.$route.meta.key
        })
        this.todoList=  routeDatas[0]?this.dealPath( routeDatas[0].children):[]
      },
      immediate: true,
    },
  },
  data() {
    return {
      todoList:[]
    }
  },
  methods:{
    dealPath(data) {
      return data.map(item => {
        const { id, parentId, children, ...rest } = item;
        const newItem = {
          path: rest.path,
          meta: {
            title: rest.label,
            icon:'icon-guanlirenyuan'
          },
        };

        if (children && children.length > 0) {
          newItem.children = this.dealPath(children);
        }

        return newItem;
      });
    }
  }
}
</script>
