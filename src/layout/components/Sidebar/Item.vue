<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    if (icon) {
      if (icon.includes('el-icon')) {
        vnodes.push(<i class={[icon, 'sub-el-icon']} />)
      } else {
        vnodes.push(<i class={['sub-el-icon iconfont', icon]} />)
      }
    }

    if (title) {
      vnodes.push(<span slot='title' class='menu-item-text' title={[title]}>{(title)}</span>)
    }
    return vnodes
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";
.sub-el-icon {
  color: $color-main;
  width: 1em;
  height: 1em;
}
</style>
