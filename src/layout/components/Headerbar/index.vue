/**
* @author: ty
* @date: 2023-06-25
* @description: 网页头部
*/
<template>
  <div class="headerbar">
    <img class="logo-img" src="@/assets/images/logo.png" alt="头部logo" />
    <div class="middle-menu-box">
      <div class="middle-menu-list" ref="menuWrapper">
        <div class="middle-menu" ref="menu">
          <div
            class="middle-menu-item"
            :class="{ 'is-active': -1 === activeMenu }"
            @click="middleMenuClick(home, -1)"
            ref="menuItems"
          >首页</div>
          <div
            v-for="(it, index) in menus"
            :key="index"
            class="middle-menu-item"
            :class="{ 'is-active': index === activeMenu }"
            @click="middleMenuClick(it, index)"
            ref="menuItems"
          >{{ it.name }}</div>
        </div>
      </div>
      <div class="nextBut" v-show="showLeftButton" @click="scrollLeft"><i class="el-icon-arrow-left"></i></div>
      <div class="nextBut" v-show="showRightButton" @click="scrollRight"><i class="el-icon-arrow-right"></i></div>
    </div>

    <div class="right-menu">
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="hover" placement="bottom-start">
        <div class="avatar-wrapper">
          <img src="@/assets/images/user-avatar.png" class="user-avatar" alt="用户头像"/>
          <div id="header-username" class="right-menu-item">{{ realName }}</div>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-menu">
<!--          <router-link to="/">
            <el-dropdown-item>首页</el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="changePsw">
            <span style="display: block">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display: block">退出登录</span>
          </el-dropdown-item> -->
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <!-- 修改密码弹框 -->
    <RecivePsd  ref="RecivePsd" />
  </div>
</template>

<script>
import RecivePsd from "./ChangePassword.vue";
export default {
  name: 'Headerbar',
  components:{
    RecivePsd
  },
  watch: {
    $route: {
      handler(n, o) {
        let index = JSON.parse(
          sessionStorage.getItem('private_menu_pts')
        ).findIndex((item) => {
          return item.path == this.$route.meta.key
        })
        this.activeMenu = index || 0
      },
      immediate: true
    },
    menus() {
      this.$nextTick(() => {
        this.checkScrollButtons();
      });
    }
  },
  data() {
    return {
      activeMenu: 0, // 当前选中的菜单的索引
      menus: [],
      home:{
        name:'首页',
        label:'首页',
        path:'/home',
      },
      showLeftButton: false,
      showRightButton: false,
      menuItems: []
    }
  },
  computed: {
    realName() {
      return sessionStorage.getItem('realName')
    }
  },
  mounted() {
    let enablePwdCheck = sessionStorage.getItem('enablePwdCheck')
    let enablePwdExpired = sessionStorage.getItem('enablePwdExpired')
    if(enablePwdCheck){
      this.psdmessage  = '您设置的密码不符合安全要求，请修改'
      this.$refs.RecivePsd.init(this.psdmessage)
    }else if(enablePwdExpired){
      this.psdmessage = '您的密码已过期，请修改'
      this.$refs.RecivePsd.init(this.psdmessage)
    }
    this.menus = JSON.parse(sessionStorage.getItem('private_menu_pts'))
    this.$nextTick(() => {
      this.menuItems = this.$refs.menuItems || [];
      this.checkScrollButtons();
      window.addEventListener('resize', this.checkScrollButtons);
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkScrollButtons);
  },
  methods: {
    async logout() {
      this.$confirm('是否确认退出登录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async (_) => {
          await this.$store.dispatch('user/logout')
          this.$router.push(`/login?redirect=${this.$route.fullPath}`)
        })
        .catch(() => {})
    },
    middleMenuClick(data, index) {
      if (this.activeMenu !== index) {
        // 如果是首页，直接跳转首页，其他的一级菜单需要调用接口查询该一级菜单下的子菜单
        if (data.path === '/home') {
          this.$router.push('/home')
        } else {
          sessionStorage.setItem(
            'detailMenu',
            JSON.stringify(data.children || [])
          )
          switch (data.path) {
            case 'cooperation_company':
              this.$router.push({ path: this.getFirstPath(data) })
              break
            case 'inbound_line':
              this.$router.push({ path: this.getFirstPath(data) })
              break
            case 'project_task_visa':
              this.$router.push({ path: this.getFirstPath(data) })
              break
            case 'staging_point':
              this.$router.push({ path: this.getFirstPath(data) })
              break
            case 'monthly_report':
              this.$router.push({ path: this.getFirstPath(data) })
              break
            case 'pccw_menu':
              this.$router.push({ path: this.getFirstPath(data) })
              break
            case 'community_network':
              this.$router.push({ path: this.getFirstPath(data) })
              break
            case 'dwep_manager':
              this.$router.push({ path: this.getFirstPath(data) })
              break
          }
        }
      }
    },
    getFirstPath(arr) {
      let data
      let fn = (obj) => {
        if (obj.hasChlid && obj.children.length) {
          obj = obj.children[0]
          fn(obj)
        } else {
          data = obj.path
        }
        return data
      }
      return fn(arr)
    },
    changePsw(){
      this.$refs.RecivePsd.init()
    },
    checkScrollButtons() {
      if (!this.$refs.menuWrapper || !this.$refs.menu) return;
      const scrollLeft = this.$refs.menu.scrollLeft;
      const wrapperWidth = this.$refs.menuWrapper.clientWidth;
      const menuWidth = this.$refs.menu.scrollWidth;

      this.showLeftButton = scrollLeft > 0;
      this.showRightButton = Math.ceil(menuWidth) > Math.ceil(scrollLeft + wrapperWidth);
    },

    // 向左滚动（每次一个菜单项）
    scrollLeft() {
      if (!this.$refs.menu || !this.$refs.menuItems?.length) return;
      const menu = this.$refs.menu;
      const wrapper = this.$refs.menuWrapper;
      const menuItems = this.$refs.menuItems;
      const currentScroll = menu.scrollLeft;
      const wrapperWidth = wrapper.clientWidth;

      // 1. 找到当前完全或部分可见的第一个菜单项
      let firstVisibleIndex = -1;
      for (let i = 0; i < menuItems.length; i++) {
        const item = menuItems[i];
        if (item.offsetLeft + item.offsetWidth > currentScroll) {
          firstVisibleIndex = i;
          break;
        }
      }

      if (firstVisibleIndex === -1) return;

      // 2. 计算目标滚动位置
      let targetScroll;
      if (firstVisibleIndex === 0) {
        // 如果第一个可见项已经是首页，确保完全显示
        targetScroll = 0;
      } else {
        // 否则滚动到前一个菜单项的开头
        const targetItem = menuItems[firstVisibleIndex - 1];
        targetScroll = targetItem.offsetLeft;

        // 3. 特殊处理 - 防止一次滚动过远
        // 计算当前最后一个完全可见的菜单项
        let lastFullyVisibleIndex = -1;
        for (let i = menuItems.length - 1; i >= 0; i--) {
          const item = menuItems[i];
          if (item.offsetLeft + item.offsetWidth <= currentScroll + wrapperWidth) {
            lastFullyVisibleIndex = i;
            break;
          }
        }
        // 如果是从最右侧开始滚动，确保只滚动一个菜单项的距离
        if (lastFullyVisibleIndex === menuItems.length - 1) {
          const visibleItems = [];
          for (let i = 0; i < menuItems.length; i++) {
            const item = menuItems[i];
            if (item.offsetLeft + item.offsetWidth > currentScroll &&
              item.offsetLeft < currentScroll + wrapperWidth) {
              visibleItems.push(i);
            }
          }

          // 如果可见的菜单项超过1个，调整目标位置
          if (visibleItems.length > 1) {
            targetScroll = menuItems[visibleItems[0]].offsetLeft;
          }
        }
      }

      // 确保不会滚动到负值
      targetScroll = Math.max(0, targetScroll);
      // 执行平滑滚动
      menu.scrollTo({
        left: targetScroll,
        behavior: 'smooth'
      });

      // 滚动结束后更新按钮状态
      setTimeout(() => this.checkScrollButtons(), 300);
    },

    // 向右滚动（每次一个菜单项）
    scrollRight() {
      if (!this.$refs.menuWrapper || !this.$refs.menu || !this.$refs.menuItems?.length) {
        return;
      }
      const menu = this.$refs.menu;
      const wrapper = this.$refs.menuWrapper;
      const menuItems = this.$refs.menuItems;
      const currentScroll = menu.scrollLeft;
      const wrapperWidth = wrapper.clientWidth;
      const menuWidth = menu.scrollWidth;
      // 1. 首先检查最后一个菜单项是否已经部分可见
      const lastItem = menuItems[menuItems.length - 1];
      const lastItemRight = lastItem.offsetLeft + lastItem.offsetWidth;
      const lastItemVisiblePart = (currentScroll + wrapperWidth) - lastItem.offsetLeft;

      // 如果最后一个菜单项部分可见（即使只有1px）
      if (lastItemVisiblePart > 0 && lastItemVisiblePart < lastItem.offsetWidth) {
        // 直接滚动到完全显示最后一个菜单项
        const targetScroll = lastItemRight - wrapperWidth;
        menu.scrollTo({
          left: targetScroll,
          behavior: 'smooth'
        });
        setTimeout(() => this.checkScrollButtons(), 300);
        return;
      }

      // 2. 如果不是处理最后一个菜单项的情况，使用原有逻辑
      let targetIndex = -1;
      for (let i = 0; i < menuItems.length; i++) {
        const item = menuItems[i];
        const itemRight = item.offsetLeft + item.offsetWidth;

        if (itemRight > currentScroll + wrapperWidth + 1) {
          targetIndex = i;
          break;
        }
      }
      if (targetIndex === -1) return;
      const targetItem = menuItems[targetIndex];
      let targetScroll = targetItem.offsetLeft;
      if (targetItem.offsetWidth > wrapperWidth) {
        targetScroll = targetItem.offsetLeft;
      } else {
        targetScroll = targetItem.offsetLeft + targetItem.offsetWidth - wrapperWidth;
      }

      targetScroll = Math.min(targetScroll, menuWidth - wrapperWidth);

      menu.scrollTo({
        left: targetScroll,
        behavior: 'smooth'
      });

      setTimeout(() => this.checkScrollButtons(), 300);
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
@import '~@/styles/variables.scss';
.middle-menu-box{
  width: calc(98% - 447px);
  float: left;
  margin-left: 2%;
}
.nextBut{
  width:35px;
  height: 35px;
  background-color: rgba(255,255,255,0.4);
  border-radius: 50%;
  font-size: 22px;
  color: #FFFFFF;
  text-align: center;
  line-height: 35px;
  float: left;
  margin-top: calc((70px - 35px)* 0.5);
  margin-left: 10px;
  cursor: pointer;
}
.nextBut:hover{
  background-color: rgba(255,255,255,0.7);
}
.middle-menu-list{
  margin-left: 2%;
  width: calc(100% - 115px);
}
.headerbar {
  .logo-img {
    float: left;
    height: 40px;
    margin-top: calc((#{$headerBarHeight} - 40px) * 0.5);
    margin-left: 12px;
  }
  .middle-menu {
    display: inline-block;
    height: 100%;
    line-height: $headerBarHeight;
    position: relative;
     width: 100%;
    float: left;
    white-space: nowrap;
    overflow: hidden;
    &:focus {
      outline: none;
    }
    .middle-menu-item {
      display: inline-block;
      padding: 0 18px;
      height: 100%;
      vertical-align: text-bottom;
      font-size: 16px;
      color: #fff;
      cursor: pointer;
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      &.is-active {
        transition: background 0.3s;
        background: $color-sub1;
      }
    }
  }
  .right-menu {
    float: right;
    height: 100%;
    line-height: $headerBarHeight;
    margin-right: 1px;
    &:focus {
      outline: none;
    }
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      vertical-align: text-bottom;
      font-size: 14px;
      color: #fff;
      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
    .avatar-container {
      overflow: hidden;
      .avatar-wrapper {
        position: relative;

        .user-avatar {
          width: 40px;
          height: 40px;
          margin-bottom: calc((#{$headerBarHeight} - 40px) * 0.5);
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
  /* 当宽度小于等于1366px时 */
  @media screen and (max-width: 1366px) {
    .middle-menu {
      margin-left: 40px;//76px;
    }
  }

  /* 当宽度小于等于1333px时 */
  @media screen and (max-width: 1333px) {
    .middle-menu {
      margin-left: 24px;
    }
  }
  /* 当宽度小于等于1320px时 */
  @media screen and (max-width: 1320px) {
    .middle-menu {
      margin-left: 10px;
    }
  }
}
.user-menu{
  .el-dropdown-menu__item--divided{
    margin-top: 0;
    border: none;
    &::before{
      display: none;
    }
  }
}
</style>
