<!--
 * @Descripttion: 修改密码页面
-->
<template>
  <div class="change_psd">
    <el-dialog
      :visible.sync="dialogRevise"
      :title="'修改密码'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="40rem"
      :modal="true"
      append-to-body
      @closed="closed"
    >
      <div v-if="psdmessage" style="text-align: center;color:#FF4D4F">{{psdmessage}}</div>
      <mssForm
        ref="basicForm"
        :key="formKey"
        :config="basicConfig"
        :rules="basicRules"
        :form="basicForm"
        labelWidth="120px"
      ></mssForm>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submit">确 定</el-button>
        <el-button size="mini" @click="exit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { changePasswordService } from '@/api/user.js'
export default {
  data() {
    let validcodeName = (rule, value, callback) => {
      if (
        this.$refs.basicForm.modelForm.newPassword ==
        this.$refs.basicForm.modelForm.newPasswordCheck
      ) {
        return callback()
      } else {
        return callback(new Error('两次输入密码不一致'))
      }
    }
    let validPassword = (rule, value, callback) => {
      if (
        window.g.enablePwdCheck &&
        window.g.pwdCheckRule &&
        window.g.pwdCheckRule.regex
      ) {
        if (!window.g.pwdCheckRule.regex.test(value)) {
          return callback(new Error(window.g.pwdCheckRule.failedMsg))
        } else {
          return callback()
        }
      } else {
        return callback()
      }
    }
    return {
      dialogRevise: false,
      basicForm: {},
      basicRules: {
        password: { required: true, message: '请输入原密码', trigger: 'blur' },
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validPassword, trigger: 'blur' }
        ],
        newPasswordCheck: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validcodeName, trigger: 'blur' }
        ]
      },
      placeholder: '',
      psdmessage: '',
      formKey: 1
    }
  },
  computed: {
    basicConfig() {
      return [
        {
          label: '原密码',
          type: 'input',
          prop: 'password',
          mode: 'password',
          span: 24
        },
        {
          label: '新密码',
          type: 'input',
          prop: 'newPassword',
          mode: 'password',
          span: 24,
          placeholder: this.placeholder
        },
        {
          label: '确认新密码',
          type: 'input',
          prop: 'newPasswordCheck',
          mode: 'password',
          span: 24,
          placeholder: this.placeholder
        }
      ]
    }
  },
  methods: {
    // 初始化事件
    init(psdmessage) {
      this.basicForm = {
        password: '',
        newPassword: '',
        newPasswordCheck: ''
      }
      this.psdmessage = psdmessage || ''
      this.placeholder =
        window.g.enablePwdCheck && window.g.pwdCheckRule
          ? window.g.pwdCheckRule.failedMsg ||
            '密码应包含字母大小写、特殊字符、数字，长度大于7位'
          : ''
      this.dialogRevise = true
    },

    // 表单提交事件
    submit() {
      this.$refs.basicForm.$refs.form.validate((valid) => {
        if (valid) {
          let req = {
            password: this.$refs.basicForm.modelForm.password,
            newPassword: this.$refs.basicForm.modelForm.newPassword,
            userId: sessionStorage.getItem('userId')
          }
          changePasswordService(req).then((res) => {
            if (res && res.code == '0000') {
              this.$message.success('修改成功')
              sessionStorage.removeItem('enablePwdCheck')
              sessionStorage.removeItem('enablePwdExpired')
              this.exit()
              setTimeout(() => {
                this.$store.dispatch('user/logout')
                this.$router.push(`/login`)
              }, 1000)
            }
          })
        }
      })
    },

    // 关闭弹框
    exit() {
      this.dialogRevise = false
    },
    closed() {
      //密码不符且不修改直接关闭，则回到登录页
      let enablePwdCheck = sessionStorage.getItem('enablePwdCheck')
      let enablePwdExpired = sessionStorage.getItem('enablePwdExpired')
      if (enablePwdCheck || enablePwdExpired) {
        this.$store.dispatch('user/logout')
        this.$router.push(`/login`)
      }
      this.formKey++
    }
  }
}
</script>