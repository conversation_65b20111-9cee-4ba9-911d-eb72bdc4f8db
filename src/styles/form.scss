@import "element-variables";
// 表单form的样式（包括查询条件表单、编写提交表单(修改、详情）
$--border-color-input: #D9D9D9;//目前只有这个文件使用

.search-card .el-card__body{
  padding:20px 0 4px;
}
.search-form{
  *{
    box-sizing: border-box;
  }
  .el-form-item{
    margin: 0 0 16px 0;
  }
  .el-row .el-col{
    padding:0 30px;
  }
  .el-form-item__label{
    height: 26px;
    line-height: 26px;
    background: rgba(245,245,245,0.5);
    border: 1px solid $--border-color-input;
    font-size: 12px;
    color: $font-color-primary;
    font-weight: 400;
    padding-left: 10px;
    border-radius: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-right: none;
    text-align: left;
  }
  .el-input__inner{
    height: 26px;
    line-height: 26px;
    font-size: 12px;
    border: 1px solid $--border-color-input;
    border-radius: 2px;
  }
  .el-form-item__content {
    line-height: normal;
    .el-select{
      width: 100%;

      .el-tag{
        height: 18px;
        line-height: 18px;
      }
    }
  }
  .textarea .el-textarea{
    height: 26px;
  }
  .textarea .el-textarea__inner{
    min-height: 26px !important;
  }
  .daterange .el-date-editor,.date1 .el-date-editor{
    width: 100%;
  }
  .date2 .el-form-item__content .el-date-editor{
    width: calc(50% - 8px);
  }
  .date2 .el-form-item__content{
    .datepicker1 .el-input__inner {
      border-right: none;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    .datepicker2 .el-input__inner {
      border-left: none;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .date2 .line{
    display: inline-block;
    width: 16px;
    font-size: 12px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    vertical-align: top;
    border-top:1px solid $--border-color-input;
    border-bottom:1px solid $--border-color-input;
  }
  .date2.is-error .line{
    border-top:1px solid $--color-danger;
    border-bottom:1px solid $--color-danger;
  }
  .el-date-editor{
    padding-top: 0;
    padding-bottom: 0;
  }
  .el-date-editor{
    .el-range__icon,
    .el-range-separator,
    .el-range__close-icon,
    .el-input__icon
    {
      line-height: 26px;
    }
  }
}
.common-form{
  .el-date-editor{
    .el-range__icon,
    .el-range-separator,
    .el-range__close-icon,
    .el-input__icon
    {
      line-height: 26px;
      height: 26px;
    }
  }
  .el-date-editor.el-input, .el-date-editor.el-input__inner{
    width: 100%;
  }
  .el-form--label-top .el-form-item{
    margin-bottom: 15px;
    .is-disabled{
      margin-left: 0;
    }
     .el-select{
      .is-disabled.el-input{
        margin-left: 0;
      }
     }
  }

  // .el-form--label-top .is-error,.el-form--label-top .is-error{
  //   margin-bottom: 22px
  // }
  .el-form-item{
    margin-bottom: 6px;
  }
  .el-col{
    padding-left: 35px;
    padding-right: 35px;
    .el-input__inner{
      // 该元素的外层元素.el-input设置了line-height: 26px;这里设置24px，是为了保证在手机端出现高度不一致
      line-height: 24px;
      height: 24px;
    }
  }
  .el-form-item__label{
    font-size: 14px;
    color: $font-color-primary;
    font-weight: 400;
    line-height: 26px;
    padding: 0;
  }
  .el-form-item__content{
    line-height: 26px;
    .el-select{
      width: 100%;
    }
  }
  .el-input__inner{
    height: 26px;
    line-height: 26px;
    font-size: 14px;
  }
  .el-textarea__inner{
    font-size: 14px;
  }
  .el-form--label-top .el-form-item__label{
    padding: 0;
  }
  .el-form--label-left .el-form-item__label{
    text-align: right;
  }

  .el-form--label-left .el-input.is-disabled .el-input__inner,.el-form--label-left .el-textarea.is-disabled .el-textarea__inner{
    padding-left: 0;
  }
  // 详情(针对预览型表单的样式)
 .detail{
     .el-row{
       & > .el-col{
         padding-left: 50px;
       }
     }
    .el-form-item{
      margin-bottom: 2px;
      display: flex;
      .el-select {
        display: block;
        .el-select__tags-text{
          font-size: 14px;
        }
        .is-disabled.el-input{
          margin-left: 1px;
        }
      }
      .el-date-editor{
        display: block;
        .el-input__inner{
          display: block;
        }
      }
      .el-form-item__content{
        overflow: hidden;
        flex: 1;
      }
    }
    .el-input__inner,.el-textarea__inner{
      border: none;
    }
    .el-input__suffix,.el-input__prefix{
      display: none;
    }
    .el-input.is-disabled .el-input__inner,.el-textarea.is-disabled .el-textarea__inner{
      background-color: #fff;
      color: $font-color-sub;

    }
     .content-input{
       background-color: #fff;
       color: #666;
       margin-left: 1px;
       white-space: nowrap;
       text-overflow: ellipsis;
       overflow: hidden;
     }
  }
  //.el-form--label-left .el-form-item__label
  .el-form--label-left{
    .el-form-item__label{
      white-space: nowrap;
    }
    // select多选
    .el-select{
      line-height: 26px;
      height: 26px;
    }
    .el-tag.el-tag--info{
      background-color: #fff;
      border-color: #fff;
      color:#666;
      line-height: 26px;
    }
  }
}

@media only screen and (max-width: 600px) {
  .common-form .detail .el-row > .el-col{
    padding-left: 35px;
    padding-right: 20px;
  }
}
