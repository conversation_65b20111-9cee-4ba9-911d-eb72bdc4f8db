// font
$font-family: "Microsoft YaHei", 微软雅黑, <PERSON><PERSON>, "PingFang SC", sans-serif;

// base color
$color-main: #0084CF;
$color-sub-main: #8DC21F;
$color-sub1: #33ACFB;
$color-sub2: #CBF1FE;
$color-sub3: #E4E4E4;
$color-sub4: #F5F5F5;
// basefontcolor
$font-color-primary:#333;
$font-color-sub:#666;

// 流程工单提示色,可用于流程工单中农的背景色、字体色等
$wf-color-primary: #33acfb;
$wf-color-success: #3cc878;
$wf-color-warning: #fca742;
$wf-color-danger: #f24444;

// background
$bgMainColor: #F4F8F9;

// sidebar
$sidebarLineHeight: 30px;
$menuText:#333333;
$menuFontSize: 14px;
$menuFontWeight: bolder;

$menuBg:#eff4f7;
//$menuHover:#263445;
$menuHover: linear-gradient(90deg, rgba(156,234,0,0.2), rgba(0,159,255,0.4));

$subMenuBg:#eff4f7;
//$subMenuHover:#001528;
$subMenuHover: linear-gradient(90deg, rgba(156,234,0,0.2), rgba(0,159,255,0.4));;

$sideBarWidth: 200px;

// headerbar
$headerbarBg: $color-main;
$headerBarHeight: 70px;

// tagsview
$tagsViewHeight: 40px;
$tagsViewShadowColor: rgba($color-main, 0.2);
$tagsViewActiveBorderColor: rgba($color-sub1, 0.5);
$tagsViewActiveFontColor: $color-sub1;

// app-main
$appMainPaddingTop: 54px;
$appMainPaddingLeft: 20px;
$appMainPaddingRight: 20px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuFontSize: $menuFontSize;
  menuFontWeight: $menuFontWeight;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  headerBarHeight: $headerBarHeight;
}
