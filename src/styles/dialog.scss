$default-button-font-color: #666;
$default-button-border-color: #d9d9d9;
.el-dialog__header{
  background: rgba($color-sub2, .3);
  padding: 0 20px;
  line-height: 45px;
  height: 45px;
  .el-dialog__title{
    color: #333;
    font-size: 14px;
    font-weight: 400;
  }
}
.el-dialog__headerbtn{
  top: 14px;
}
.el-dialog__body{
  padding: 16px;
}
.el-dialog__footer{
  text-align: center;
}
.dialog-footer{
  .el-button--primary{
    background-color: $color-main;
    border-color: $color-main;
    &:hover{
      background-color: rgba($color-main, .8);
      border-color: rgba($color-main, .8);
    }
  }
  .el-button--default{
    background-color: #fff;
    color: $default-button-font-color;
    border-color: $default-button-border-color;
    &:hover{
      background-color: rgba($color-sub1, .1);
      border-color: rgba($color-sub1, .2);
    }
  }
  .el-button + .el-button{
    margin-left: 20px;
  }
}

.is-mobile-dialog{
  transform: scale(0.9);
  .choose{
    .chooseTitle{
      & > span{
        display: block;
        font-size: 12px;
        line-height: 16px;
      }
    }
  }
}
