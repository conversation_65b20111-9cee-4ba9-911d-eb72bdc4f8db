/**
* elementui 自定义主题样式的变量。
* 如果你需要重写elementui默认样式，请在这个文件里重写变量值。
**/
@import "variables";
/* font size 字体 */
$--font-size-base: 12px;

/* theme color */
$--color-primary: $color-sub1;
$--color-success: #3cc878;
$--color-warning: #fca742;
$--color-danger: #f24444;
// $--color-info: #1E1E1E;

// $--color-text-regular: #1f2d3d;

$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;


$--table-border: 1px solid #dfe6ec;

/* Input
-------------------------- */
$--input-height: 26px;

/* DatePicker
--------------------------*/


/* Button
-------------------------- */
$--button-font-weight: 400;
$--button-font-size: $--font-size-base;
$--button-padding-vertical: 6px;
$--button-padding-horizontal: 12px;
$--button-border-radius: 2px;

$--button-default-font-color: $--color-primary;
$--button-default-border-color: $--color-primary;

$--button-success-border-color: $color-sub-main;
$--button-success-background-color: $color-sub-main;

/* Table
-------------------------- */
$--table-header-background-color:  $color-sub2;

/* Dialog
-------------------------- */


/* icon font path, required */
$--font-path: "~element-ui/lib/theme-chalk/fonts";

//@import "~element-ui/packages/theme-chalk/src/index";
@import "~element-ui/packages/theme-chalk/src/common/transition.scss";
@import "~element-ui/packages/theme-chalk/src/pagination.scss";
@import "~element-ui/packages/theme-chalk/src/dialog.scss";
@import "~element-ui/packages/theme-chalk/src/autocomplete.scss";
@import "~element-ui/packages/theme-chalk/src/dropdown.scss";
@import "~element-ui/packages/theme-chalk/src/dropdown-menu.scss";
@import "~element-ui/packages/theme-chalk/src/dropdown-item.scss";
@import "~element-ui/packages/theme-chalk/src/menu.scss";
@import "~element-ui/packages/theme-chalk/src/submenu.scss";
@import "~element-ui/packages/theme-chalk/src/menu-item.scss";
@import "~element-ui/packages/theme-chalk/src/menu-item-group.scss";
@import "~element-ui/packages/theme-chalk/src/input.scss";
@import "~element-ui/packages/theme-chalk/src/input-number.scss";
@import "~element-ui/packages/theme-chalk/src/radio.scss";
@import "~element-ui/packages/theme-chalk/src/radio-group.scss";
@import "~element-ui/packages/theme-chalk/src/radio-button.scss";
@import "~element-ui/packages/theme-chalk/src/checkbox.scss";
@import "~element-ui/packages/theme-chalk/src/checkbox-button.scss";
@import "~element-ui/packages/theme-chalk/src/checkbox-group.scss";
@import "~element-ui/packages/theme-chalk/src/switch.scss";
@import "~element-ui/packages/theme-chalk/src/select-dropdown.scss";
@import "~element-ui/packages/theme-chalk/src/select.scss";
@import "~element-ui/packages/theme-chalk/src/button.scss";
@import "~element-ui/packages/theme-chalk/src/button-group.scss";
@import "~element-ui/packages/theme-chalk/src/table.scss";
@import "~element-ui/packages/theme-chalk/src/table-column.scss";
@import "~element-ui/packages/theme-chalk/src/date-picker.scss";
@import "~element-ui/packages/theme-chalk/src/time-select.scss";
@import "~element-ui/packages/theme-chalk/src/time-picker.scss";
@import "~element-ui/packages/theme-chalk/src/popover.scss";
@import "~element-ui/packages/theme-chalk/src/tooltip.scss";
@import "~element-ui/packages/theme-chalk/src/message-box.scss";
@import "~element-ui/packages/theme-chalk/src/breadcrumb.scss";
@import "~element-ui/packages/theme-chalk/src/breadcrumb-item.scss";
@import "~element-ui/packages/theme-chalk/src/form.scss";
@import "~element-ui/packages/theme-chalk/src/form-item.scss";
@import "~element-ui/packages/theme-chalk/src/tabs.scss";
@import "~element-ui/packages/theme-chalk/src/tab-pane.scss";
@import "~element-ui/packages/theme-chalk/src/tag.scss";
@import "~element-ui/packages/theme-chalk/src/tree.scss";
@import "~element-ui/packages/theme-chalk/src/alert.scss";
@import "~element-ui/packages/theme-chalk/src/notification.scss";
@import "~element-ui/packages/theme-chalk/src/slider.scss";
@import "~element-ui/packages/theme-chalk/src/loading.scss";
@import "~element-ui/packages/theme-chalk/src/row.scss";
@import "~element-ui/packages/theme-chalk/src/col.scss";
@import "~element-ui/packages/theme-chalk/src/upload.scss";
@import "~element-ui/packages/theme-chalk/src/progress.scss";
@import "~element-ui/packages/theme-chalk/src/spinner.scss";
@import "~element-ui/packages/theme-chalk/src/message.scss";
@import "~element-ui/packages/theme-chalk/src/badge.scss";
@import "~element-ui/packages/theme-chalk/src/card.scss";
@import "~element-ui/packages/theme-chalk/src/rate.scss";
@import "~element-ui/packages/theme-chalk/src/steps.scss";
@import "~element-ui/packages/theme-chalk/src/step.scss";
@import "~element-ui/packages/theme-chalk/src/carousel.scss";
@import "~element-ui/packages/theme-chalk/src/scrollbar.scss";
@import "~element-ui/packages/theme-chalk/src/carousel-item.scss";
@import "~element-ui/packages/theme-chalk/src/collapse.scss";
@import "~element-ui/packages/theme-chalk/src/collapse-item.scss";
@import "~element-ui/packages/theme-chalk/src/cascader.scss";
@import "~element-ui/packages/theme-chalk/src/color-picker.scss";
@import "~element-ui/packages/theme-chalk/src/transfer.scss";
@import "~element-ui/packages/theme-chalk/src/container.scss";
@import "~element-ui/packages/theme-chalk/src/header.scss";
@import "~element-ui/packages/theme-chalk/src/aside.scss";
@import "~element-ui/packages/theme-chalk/src/main.scss";
@import "~element-ui/packages/theme-chalk/src/footer.scss";
@import "~element-ui/packages/theme-chalk/src/timeline.scss";
@import "~element-ui/packages/theme-chalk/src/timeline-item.scss";
@import "~element-ui/packages/theme-chalk/src/link.scss";
@import "~element-ui/packages/theme-chalk/src/divider.scss";
@import "~element-ui/packages/theme-chalk/src/image.scss";
@import "~element-ui/packages/theme-chalk/src/calendar.scss";
@import "~element-ui/packages/theme-chalk/src/backtop.scss";
@import "~element-ui/packages/theme-chalk/src/infinite-scroll.scss";
@import "~element-ui/packages/theme-chalk/src/page-header.scss";
@import "~element-ui/packages/theme-chalk/src/cascader-panel.scss";
@import "~element-ui/packages/theme-chalk/src/avatar.scss";
@import "~element-ui/packages/theme-chalk/src/drawer.scss";
@import "~element-ui/packages/theme-chalk/src/popconfirm.scss";
@import "~element-ui/packages/theme-chalk/src/skeleton.scss";
@import "~element-ui/packages/theme-chalk/src/skeleton-item.scss";
@import "~element-ui/packages/theme-chalk/src/empty.scss";
@import "~element-ui/packages/theme-chalk/src/descriptions.scss";
@import "~element-ui/packages/theme-chalk/src/descriptions-item.scss";
@import "~element-ui/packages/theme-chalk/src/result.scss";


// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--color-primary;
}
