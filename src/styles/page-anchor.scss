$default-color: #CED1D2;
$active-color: $color-sub-main;
$anchor-item-width: 47px;
$anchor-item-height: 42px;

.page-anchor-parentpage{
  padding-right: 70px;
  .operate-btn{
    text-align: right;
    margin-bottom: 10px;
  }
}

.page-anchor-point{
  margin-top: -110px;
  padding-top: 110px;
}
.page-anchor-wrapper{
  position: fixed;
  right: 0;
  z-index: 10;
}
.page-anchor{
  position: relative;
  border-right: 1px solid $default-color;
  list-style: none;
  padding: 1px 10px;
  margin-right: 10px;
  &:before{
    content: "";
    position: absolute;
    right: -4px;
    top: 0;
    width: 7px;
    display: block;
    height: 7px;
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 50%;
  }
  &:after{
    content: "";
    position: absolute;
    right: -4px;
    bottom: 0;
    width: 7px;
    display: block;
    height: 7px;
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 50%;
  }
  .page-anchor-item{
    cursor: pointer;
    position: relative;
    margin: 13px 0;
    width: $anchor-item-width;
    height: $anchor-item-height;
    color: #fff;
    background: url("~@/assets/images/anchor-point-white.png");
    background-size: 100% 100%;
    &:after{
      content: "";
      width: 7px;
      display: block;
      height: 7px;
      background: $default-color;
      position: absolute;
      right: -14px;
      top: 45%;
      border-radius: 50%;
    }
    &.is-active{
      background: url("~@/assets/images/anchor-point-green.png");
      background-size: 100% 100%;
      &:after{
        background: $active-color;
        box-shadow: 0 0 0px 4px rgba($active-color, .2);
      }
      .page-anchor-item--text{
        color: #fff;
      }
    }
  }
  .page-anchor-item--text{
    display: table-cell;
    width: calc(#{$anchor-item-width} - 5px);
    height: $anchor-item-height;
    text-align: center;
    vertical-align: middle;
    font-size: 12px;
    color: #666;
  }
}

/* 当高度小于等于600px时 */
@media screen and (max-height: 600px) {
  .page-anchor-wrapper{
    top: 25%;
  }
}

/* 当高度大于600px时 */
@media screen and (min-height: 601px) {
  .page-anchor-wrapper{
    top: 40%;
  }
}
