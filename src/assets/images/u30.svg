<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="164px" height="54px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="178px" y="145px" width="164px" height="54px" filterUnits="userSpaceOnUse" id="filter94">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.843137254901961  0 0 0 0 0.843137254901961  0 0 0 0 0.843137254901961  0 0 0 0.349019607843137 0  " in="shadowComposite" />
    </filter>
    <g id="widget95">
      <image preserveAspectRatio="none" style="overflow:visible" width="154" height="44" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJoAAAAsCAYAAACdbKF/AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEISURBVHhe7d2xysIwFIbh3P91ilaLHUREHHok/o6ZfsM3PYVnadrtpemU0+p7vV6v2ratzudzHQ4H+LdlWep6vdbz+fzWVfUJ7X6/D1+AX91ut9r3vVr/ko0egFkej0e1vl2OFmGWy+VSzT8ZCW10E2YTGhFCI0JoRAiNCKERITQihEaE0IgQGhFCI0JoRAiNCKERITQihEaE0IgQGhFCI0JoRAiNCKERITQihEaE0IgQGhFCI0JoRAiNCKERITQihEaE0IgQGhGtH9U9WoCZWj8PfrQAsxyPx2p96MBoEWbpu+ZnoEUfOjB6AH51Op1qXde/0Ppkiz50wFHwzNK3y/4l65Gt61pvluUhzCxsrX0AAAAASUVORK5CYII=" x="178px" y="145px" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -178 -145 )">
    <use xlink:href="#widget95" filter="url(#filter94)" />
    <use xlink:href="#widget95" />
  </g>
</svg>