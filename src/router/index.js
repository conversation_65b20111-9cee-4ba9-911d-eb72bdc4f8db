import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import cooperationCompany from './modules/cooperation_company/index.js'
import inboundLineRouter from './modules/inbound_line/index.js'
import engineeringManagementRouter from './modules/engineering_management/index'
import stagingPoint from './modules/staging_point/index'
import material from './modules/material/index.js'
import monthlyReport from './modules/monthly_report/index.js'
import designLinkRouter from './modules/design_link/index.js'
import systemSettings from './modules/system_settings/index.js'
import pccwRouter from './modules/pccw/index.js'
import extendMenuRouter from './modules/extend_menu/index.js'
import communityNetworkRouter from './modules/community_network/index.js'
import dwepManagerRouter from './modules/dwep_manager/index.js'


/**
   * Note: sub-menu only appear when route children.length >= 1
   * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
   *
   * hidden: true                   if set true, item will not show in the sidebar(default is false)
   * alwaysShow: true               if set true, will always show the root menu
   *                                if not set alwaysShow, when item has more than one children route,
   *                                it will becomes nested mode, otherwise not show the root menu
   * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
   * name:'router-name'             the name is used by <keep-alive> (must set!!!).tips: 1.same as the property name in .vue file. 2.Case Sensitive
 * meta : {
      roles: ['admin','editor']    control the page roles (you can set multiple roles)
      title: 'title'               the name show in sidebar and breadcrumb (recommend set)
      icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
      noCache: true                if set true, the page will no be cached(default is false)
      affix: true                  if set true, the tag will affix in the tags-view
      breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
      activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
    }
   */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '',
        component: () => import('@/views/error-page/404')
      }
    ]
  },
  {
    path: '/401',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '',
        component: () => import('@/views/error-page/401')
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { title: '待阅', icon: 'dashboard', affix: true }
      },
      {
        path: 'home',
        component: () => import('@/views/home/<USER>'),
        name: 'Home',
        meta: { title: '首页', icon: 'dashboard', key: 'home', affix: true }
      },
      ...inboundLineRouter,
      ...cooperationCompany,
      ...designLinkRouter,
      ...material,
      ...systemSettings,
      ...pccwRouter,
      ...extendMenuRouter,
      ...communityNetworkRouter,
      ...dwepManagerRouter

    ]
  },
  {
    path: '/message', // 待阅消息/已阅消息
    component: Layout,
    redirect: '/message/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/message_manager/index'),
        name: 'messageIndex',
        meta: {
          title: '待阅消息',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view',
        component: () => import('@/views/message_manager/details'),
        name: 'messageDetails',
        meta: {
          title: '消息详情',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  ...stagingPoint,
  ...monthlyReport,
  ...engineeringManagementRouter,
  { path: '*', redirect: '/404', hidden: true }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = []

const createRouter = () => new Router({
  mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}
// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = Router.prototype.push
const originalReplace = Router.prototype.replace
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => {
  })
}
Router.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch(err => {
  })
}

export default router
