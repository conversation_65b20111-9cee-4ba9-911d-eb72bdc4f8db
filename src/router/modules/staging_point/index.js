/**
 * @author: lzp
 * @date: 2023-07-06
 * @description: 暂存点路由
 */
import Layout from '@/layout'

const staging_point = [
  {
    path: '/staging_point',
    component: Layout,
    redirect: '/staging_point/roles_list',
    alwaysShow: true,
    name: 'stagingPoint',
    meta: {
      title: '暂存点管理',
      icon: 'lock',
      key:'staging_point',
      roles: ['admin', 'editor']
    },
    children: [
      {
        path: 'roles_list',
        component: () => import('@/views/staging_point/roles/index.vue'),
        name: 'rolesList',
        meta: {
          title: '角色配置管理',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'work_order_list',
        component: () => import('@/views/staging_point/work_order/index.vue'),
        name: 'StagingPointWorkOrder',
        meta: {
          title: '暂存点工单管理列表',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'work_order_edit',
        component: () => import('@/views/staging_point/work_order/edit.vue'),
        name: 'StagingPointWorkOrderEdit',
        meta: {
          title: '暂存点处理页',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'inbound_outbound_list',
        component: () => import('@/views/staging_point/inbound_outbound/index.vue'),
        name: 'inboundOutboundList',
        meta: {
          title: '物资出入库及退库管理列表',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'material_outbound_edit',
        component: () => import('@/views/staging_point/inbound_outbound/outbound.vue'),
        name: 'materialOutboundEdit',
        meta: {
          title: '出库申请处理页',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'material_inbound_edit',
        component: () => import('@/views/staging_point/inbound_outbound/inbound.vue'),
        name: 'materialInboundEdit',
        meta: {
          title: '入库申请处理页',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'material_return_edit',
        component: () => import('@/views/staging_point/inbound_outbound/return.vue'),
        name: 'materialReturnEdit',
        meta: {
          title: '退库申请处理页',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'ledger_management_list',
        component: () => import('@/views/staging_point/ledger_management/index.vue'),
        name: 'ledgerManagementList',
        meta: {
          title: '台账信息管理',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'ledger_management_info',
        component: () => import('@/views/staging_point/ledger_management/info.vue'),
        name: 'ledgerManagementInfo',
        meta: {
          title: '暂存点详情',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'merterial_history_deteail',
        component: () => import('@/views/staging_point/ledger_management/merterial_history_deteail.vue'),
        name: 'material_history_detail',
        meta: {
          title: '暂存点物资历史详情',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'material_warehousing_info',
        component: () => import('@/views/staging_point/ledger_management/material_warehousing_info.vue'),
        name: 'material_warehousing_info',
        meta: {
          title: '物料入库信息',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'inventory_management_list',
        component: () => import('@/views/staging_point/inventory_management/index.vue'),
        name: 'inventoryManagementList',
        meta: {
          title: '暂存点盘点工单管理列表',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'inventory_management_info',
        component: () => import('@/views/staging_point/inventory_management/info.vue'),
        name: 'inventoryManagementInfo',
        meta: {
          title: '暂存点盘点详情页',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'ledger_statistics_list',
        component: () => import('@/views/staging_point/ledger_statistics/index.vue'),
        name: 'ledgerStatisticsList',
        meta: {
          title: '暂存点台账统计管理',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'ledger_statistics_detail',
        component: () => import('@/views/staging_point/ledger_statistics/detail.vue'),
        name: 'ledgerStatisticsDetail',
        meta: {
          title: '暂存点任务详情页',
          key:'staging_point',
          roles: ['admin', 'editor']
        }
      },
    ]
  }
]

export default staging_point
