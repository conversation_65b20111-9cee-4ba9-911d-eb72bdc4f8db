/**
 * @author: ty
 * @date: 2023-07-05
 * @description: 工程管理路由
 */
import Layout from '@/layout'

export default [
  // 工程签证
  {
    path: '/project_task_visa',
    component: Layout,
    redirect: '/project_task_visa/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'projectTaskVisa',
    meta: {
      title: '工程签证',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/project_task_visa/index.vue'),
        name: 'ProjectTaskVisaIndex',
        meta: {
          title: '合作列表（移动）列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'find_page_coop',
        component: () => import('@/views/engineering_management/project_task_visa/list_cooperate.vue'),
        name: 'ProjectTaskVisaFindPageCoop',
        meta: {
          title: '合作列表（合作）列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view',
        component: () => import('@/views/engineering_management/project_task_visa/view_form.vue'),
        name: 'ProjectTaskVisaViewForm',
        meta: {
          title: '工程签证详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'edit',
        component: () => import('@/views/engineering_management/project_task_visa/edit_form.vue'),
        name: 'ProjectTaskVisaEditForm',
        meta: {
          title: '工程签证处理页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 质监申报
  {
    path: '/quality_declaration',
    component: Layout,
    redirect: '/quality_declaration/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'qualityDeclaration',
    meta: {
      title: '质监申报',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/quality_declaration/index.vue'),
        name: 'QualityDeclarationFindPage',
        meta: {
          title: '质监申报列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'edit_form',
        component: () => import('@/views/engineering_management/quality_declaration/edit_form.vue'),
        name: 'QualityDeclarationEditForm',
        meta: {
          title: '质监申报处理页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/quality_declaration/view_form.vue'),
        name: 'QualityDeclarationViewForm',
        meta: {
          title: '质监申报详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 施工派工
  {
    path: '/construction_dispatch',
    component: Layout,
    redirect: '/construction_dispatch/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'constructionDispatch',
    meta: {
      title: '施工派工',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/construction_dispatch/index.vue'),
        name: 'ConstructionDispatchFindPage',
        meta: {
          title: '施工派工列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/construction_dispatch/view_form.vue'),
        name: 'ConstructionDispatchViewForm',
        meta: {
          title: '施工派工详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 监理派工
  {
    path: '/supervision_dispatch',
    component: Layout,
    redirect: '/supervision_dispatch/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'supervisionDispatch',
    meta: {
      title: '监理派工',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/supervision_dispatch/index.vue'),
        name: 'SupervisionDispatchFindPage',
        meta: {
          title: '监理派工列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'edit_form',
        component: () => import('@/views/engineering_management/supervision_dispatch/edit_form.vue'),
        name: 'supervisionDispatchEditForm',
        meta: {
          title: '监理派工处理页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/supervision_dispatch/view_form.vue'),
        name: 'SupervisionDispatchViewForm',
        meta: {
          title: '监理派工详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 设计交底
  {
    path: '/design_disclosure',
    component: Layout,
    redirect: '/design_disclosure/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'designDisclosure',
    meta: {
      title: '设计交底',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/design_disclosure/index.vue'),
        name: 'DesignDisclosureFindPage',
        meta: {
          title: '设计交底列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'edit_form',
        component: () => import('@/views/engineering_management/design_disclosure/edit_form.vue'),
        name: 'designDisclosureEditForm',
        meta: {
          title: '设计交底处理页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/design_disclosure/view_form.vue'),
        name: 'DesignDisclosureViewForm',
        meta: {
          title: '设计交底详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 施工组织设计
  {
    path: '/construction_org_design',
    component: Layout,
    redirect: '/construction_org_design/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'constructionOrgDesign',
    meta: {
      title: '施工组织设计列表',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/construction_org_design/index.vue'),
        name: 'ConstructionOrgDesignFindPage',
        meta: {
          title: '施工组织设计列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'edit_form',
        component: () => import('@/views/engineering_management/construction_org_design/edit_form.vue'),
        name: 'constructionOrgDesignEditForm',
        meta: {
          title: '施工组织设计处理页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/construction_org_design/view_form.vue'),
        name: 'ConstructionOrgDesignViewForm',
        meta: {
          title: '施工组织设计详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 开工启动会
  {
    path: '/kickoff_meeting',
    component: Layout,
    redirect: '/kickoff_meeting/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'kickoffMeeting',
    meta: {
      title: '开工启动会',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/kickoff_meeting/index.vue'),
        name: 'KickoffMeetingFindPage',
        meta: {
          title: '开工启动会列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/kickoff_meeting/view_form.vue'),
        name: 'KickoffMeetingViewForm',
        meta: {
          title: '开工启动会详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 开工报告
  {
    path: '/project_start',
    component: Layout,
    redirect: '/project_start/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'projectStart',
    meta: {
      title: '开工报告',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/project_start/index.vue'),
        name: 'ProjectStartFindPage',
        meta: {
          title: '开工报告列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'edit_form',
        component: () => import('@/views/engineering_management/project_start/edit_form.vue'),
        name: 'ProjectStartEditForm',
        meta: {
          title: '开工报告处理页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/project_start/view_form.vue'),
        name: 'ProjectStartViewForm',
        meta: {
          title: '开工报告详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 项目完工
  {
    path: '/project_completion',
    component: Layout,
    redirect: '/project_completion/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'projectCompletion',
    meta: {
      title: '项目完工',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/project_completion/index.vue'),
        name: 'ProjectCompletionFindPage',
        meta: {
          title: '项目完工列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/project_completion/view_form.vue'),
        name: 'ProjectCompletionViewForm',
        meta: {
          title: '项目完工详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
    ]
  },
  // 割接交维
  {
    path: '/intersect_dimension',
    component: Layout,
    redirect: '/intersect_dimension/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'intersectDimension',
    meta: {
      title: '割接交维',
      key:'project_task_visa',
      icon: 'lock',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/intersect_dimension/index.vue'),
        name: 'IntersectDimensionFindPage',
        meta: {
          title: '割接交维列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/intersect_dimension/view_form.vue'),
        name: 'IntersectDimensionViewForm',
        meta: {
          title: '割接交维详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 验收测试
  {
    path: '/acceptance_test',
    component: Layout,
    redirect: '/acceptance_test/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'acceptanceTest',
    meta: {
      title: '验收测试',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/acceptance_test/index.vue'),
        name: 'AcceptanceTestFindPage',
        meta: {
          title: '验收测试列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view_form',
        component: () => import('@/views/engineering_management/acceptance_test/view_form.vue'),
        name: 'AcceptanceTestViewForm',
        meta: {
          title: '验收测试详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 监理费用确认
  {
    path: '/supervision_fee_confirm',
    component: Layout,
    redirect: '/supervision_fee_confirm/find_page',
    alwaysShow: true, // will always show the root menu
    name: 'supervisionFeeConfirm',
    meta: {
      title: '监理费用确认',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'find_page',
        component: () => import('@/views/engineering_management/supervision_fee_confirm/index.vue'),
        name: 'SupervisionFeeConfirmFindPage',
        meta: {
          title: '监理费用确认列表',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'edit',
        component: () => import('@/views/engineering_management/supervision_fee_confirm/edit_form.vue'),
        name: 'SupervisionFeeConfirmEditForm',
        meta: {
          title: '监理费用确认处理页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view',
        component: () => import('@/views/engineering_management/supervision_fee_confirm/view_form.vue'),
        name: 'SupervisionFeeConfirmViewForm',
        meta: {
          title: '监理费用确认详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  // 转资及时性
  {
    path: '/transfertimeliness',
    component: Layout,
    alwaysShow: true, // will always show the root menu
    name: 'transfertimeliness',
    meta: {
      title: '转资及时性',
      icon: 'lock',
      key: 'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: '/transfertimeliness/:id',
        component: () => import('@/views/engineering_management/transfertimeliness/view.vue'),
        name: 'TransfertimelinessView',
        meta: {
          title: '转资及时性详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view/:id',
        component: () => import('@/views/engineering_management/transfertimeliness/view2.vue'),
        name: 'TransfertimelinessView2',
        meta: {
          title: '转资及时性详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
    ]
  },
  // 完工及时性
  {
    path: '/completetimeliness',
    component: Layout,
    alwaysShow: true, // will always show the root menu
    name: 'completetimeliness',
    meta: {
      title: '完工及时性',
      icon: 'lock',
      key: 'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: '/completetimeliness/:id',
        component: () => import('@/views/engineering_management/completetimeliness/view.vue'),
        name: 'CompletetimelinessView',
        meta: {
          title: '完工及时性详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view/:id',
        component: () => import('@/views/engineering_management/completetimeliness/view2.vue'),
        name: 'CompletetimelinessView2',
        meta: {
          title: '完工及时性详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      }
    ]
  },
  {
    path: '/starttimeliness',
    component: Layout,
    alwaysShow: true, // will always show the root menu
    name: 'starttimeliness',
    meta: {
      title: '开工及时性',
      icon: 'lock',
      key: 'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: '/starttimeliness/:id',
        component: () => import('@/views/engineering_management/starttimeliness/view.vue'),
        name: 'StarttimelinessView',
        meta: {
          title: '开工及时性详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'view/:id',
        component: () => import('@/views/engineering_management/starttimeliness/view2.vue'),
        name: 'StarttimelinessView2',
        meta: {
          title: '开工及时性详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
    ]
  },
    // 数据爬取
    {
      path: '/data_crawling',
      component: Layout,
      redirect:'/data_crawling/find_page',
      alwaysShow: true, // will always show the root menu
      name: 'dataCrawling',
      meta: {
        title: '数据爬取',
        icon: 'lock',
        key:'project_task_visa',
        roles: ['admin', 'editor'] // you can set roles in root nav
      },
      children:[
        {
          path: 'find_page',
          component: () => import('@/views/engineering_management/data_crawling/index.vue'),
          name: 'DataCrawlingFindPage',
          meta: {
            title: '数据爬取列表',
            key:'project_task_visa',
            roles: ['admin', 'editor']
          }
        },
      ]
    },
  
  // 项目工程待阅
  {
    path: '/project_message',
    component: Layout,
    alwaysShow: true, // will always show the root menu
    name: 'projectMessage',
    meta: {
      title: '工程管理待阅',
      icon: 'lock',
      key:'project_task_visa',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'construction_dispatch/message/:id',
        component: () => import('@/views/engineering_management/project_message/construction_dispatch.vue'),
        name: 'ConstructionDispatchMessage',
        meta: {
          title: '施工派工待阅',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'supervision_dispatch/message/:id',
        component: () => import('@/views/engineering_management/project_message/supervision_dispatch.vue'),
        name: 'SupervisionDispatchMessage',
        meta: {
          title: '监理派工待阅',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'design_disclosure/message/:id',
        component: () => import('@/views/engineering_management/project_message/design_disclosure.vue'),
        name: 'DesignDisclosureMessage',
        meta: {
          title: '设计交底待阅',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
      {
        path: '/project_completion_view_msg/:id',
        component: () => import('@/views/engineering_management/project_message/project_completion.vue'),
        name: 'ProjectCompletionViewMsg',
        meta: {
          title: '完工报告完成时间待阅详情页',
          key:'project_task_visa',
          roles: ['admin', 'editor']
        }
      },
    ]
  },
]
