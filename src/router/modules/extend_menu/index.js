/**
 * 集客专线管理路由
 */

 import Layout from '@/layout'
const extendMenuRouter = [
 //二期新增菜单
  { // 归类池
    path: '/inbound_line/extend/sorting_tank',
    component: () => import('@/views/inbound_line/extend/sorting_tank/index.vue'),
    name: 'extend_sorting_tank_list',
    meta: { title: '专线投资类归类池列表', icon: 'sorting_tank',key:'inbound_line'}
  },
  { // 归类池--处理
    path: '/inbound_line/extend/sorting_tank/edit',
    component: () => import('@/views/inbound_line/extend/sorting_tank/edit.vue'),
    name: 'extend_sorting_tank_detail',
    meta: { title: '专线投资类归类池处理页', icon: 'sorting_tank_edit',key:'inbound_line'}
  },
  { // 归类池--查看
    path: '/inbound_line/extend/sorting_tank/view',
    component: () => import('@/views/inbound_line/extend/sorting_tank/view.vue'),
    name: 'extend_sorting_tank_look',
    meta: { title: '专线投资类归类池详情页', icon: 'sorting_tank_view',key:'inbound_line'}
  },
  { // 归类池--统计报表
    path: '/inbound_line/extend/sorting_tank/statistics',
    component: () => import('@/views/inbound_line/extend/sorting_tank/statistics.vue'),
    name: 'extend_sortingTankStatistics',
    meta: { title: '专线归类情况统计列表', icon: 'sorting_tank_statistics',key:'inbound_line'}
  },

  { // 分派
    path: '/inbound_line/extend/construction_assignment',
    component: () => import('@/views/inbound_line/extend/construction_assignment/index.vue'),
    name: 'extend_construction_assignment_list',
    meta: { title: '专线成本类施工分派列表', icon: '',key:'inbound_line'}
  },
  { // 分派--处理
    path: '/inbound_line/extend/construction_assignment/edit',
    component: () => import('@/views/inbound_line/extend/construction_assignment/edit.vue'),
    name: 'extend_construction_assignment_detail',
    meta: { title: '专线成本类施工分派处理页', icon: '',key:'inbound_line'}
  },
  { // 分派--查看
    path: '/inbound_line/extend/construction_assignment/view',
    component: () => import('@/views/inbound_line/extend/construction_assignment/view.vue'),
    name: 'extend_construction_assignment_look',
    meta: { title: '专线成本类施工分派详情页', icon: '',key:'inbound_line'}
  },
  { // 投资分派
    path: '/inbound_line/extend/invest_construction_assignment',
    component: () => import('@/views/inbound_line/extend/invest_construction_assignment/index.vue'),
    name: 'extend_invest_construction_assignment_list',
    meta: { title: '专线投资类施工分派列表', icon: 'construction_assignment',key:'inbound_line'}
  },
  { // 投资分派--处理
    path: '/inbound_line/extend/invest_construction_assignment/edit',
    component: () => import('@/views/inbound_line/extend/invest_construction_assignment/edit.vue'),
    name: 'extend_invest_construction_assignment_detail',
    meta: { title: '专线投资类施工分派处理页', icon: 'construction_assignment_edit',key:'inbound_line'}
  },
  { // 投资分派--查看
    path: '/inbound_line/extend/invest_construction_assignment/view',
    component: () => import('@/views/inbound_line/extend/invest_construction_assignment/view.vue'),
    name: 'extend_invest_construction_assignment_look',
    meta: { title: '专线投资类施工分派详情页', icon: 'construction_assignment_view',key:'inbound_line'}
  },
  { // 投资类完工
    path: '/inbound_line/extend/invest_completion',
    component: () => import('@/views/inbound_line/extend/invest_completion/index.vue'),
    name: 'extend_invest_completion_list',
    meta: { title: '专线投资类完工列表', icon: 'invest_completion',key:'inbound_line'}
  },
  { // 投资类完工--处理
    path: '/inbound_line/extend/invest_completion/edit',
    component: () => import('@/views/inbound_line/extend/invest_completion/edit.vue'),
    name: 'extend_invest_completion_detail',
    meta: { title: '专线投资类完工处理页', icon: 'invest_completion_edit',key:'inbound_line'}
  },
  { // 投资类完工--查看
    path: '/inbound_line/extend/invest_completion/view',
    component: () => import('@/views/inbound_line/extend/invest_completion/view.vue'),
    name: 'extend_invest_completion_look',
    meta: { title: '专线投资类完工详情页', icon: 'invest_completion_view',key:'inbound_line'}
  },
  { // 成本类完工
    path: '/inbound_line/extend/cost_completion',
    component: () => import('@/views/inbound_line/extend/cost_completion/index.vue'),
    name: 'extend_cost_completion_list',
    meta: { title: '专线成本类完工列表', icon: 'cost_completion',key:'inbound_line'}
  },
  { // 成本类完工--处理
    path: '/inbound_line/extend/cost_completion/edit',
    component: () => import('@/views/inbound_line/extend/cost_completion/edit.vue'),
    name: 'extend_cost_completion_detail',
    meta: { title: '专线成本类完工处理页', icon: 'cost_completion_edit',key:'inbound_line'}
  },
  { // 成本类完工--查看
    path: '/inbound_line/extend/cost_completion/view',
    component: () => import('@/views/inbound_line/extend/cost_completion/view.vue'),
    name: 'extend_cost_completion_look',
    meta: { title: '专线成本类完工详情页', icon: 'cost_completion_view',key:'inbound_line'}
  },
  { // 商机勘察
    path: '/inbound_line/extend/survey_construction',
    component: () => import('@/views/inbound_line/extend/survey_construction/index.vue'),
    name: 'extend_invest_completion_list',
    meta: { title: '商机勘察列表', icon: 'invest_completion',key:'inbound_line'}
  },
  { // 商机勘察--处理
    path: '/inbound_line/extend/survey_construction/edit',
    component: () => import('@/views/inbound_line/extend/survey_construction/edit.vue'),
    name: 'extend_invest_completion_detail',
    meta: { title: '商机勘察处理页', icon: 'invest_completion_edit',key:'inbound_line'}
  },
  { // 商机勘察--查看
    path: '/inbound_line/extend/survey_construction/view',
    component: () => import('@/views/inbound_line/extend/survey_construction/view.vue'),
    name: 'extend_invest_completion_look',
    meta: { title: '商机勘察详情页', icon: 'invest_completion_view',key:'inbound_line'}
  },
  { // 预覆盖建设
    path: '/inbound_line/extend/precovering_construction',
    component: () => import('@/views/inbound_line/extend/precovering_construction/index.vue'),
    name: 'extend_invest_completion_list',
    meta: { title: '预覆盖建设列表', icon: 'invest_completion',key:'inbound_line'}
  },
  { // 预覆盖建设--处理
    path: '/inbound_line/extend/precovering_construction/edit',
    component: () => import('@/views/inbound_line/extend/precovering_construction/edit.vue'),
    name: 'extend_invest_completion_detail',
    meta: { title: '预覆盖建设处理页', icon: 'invest_completion_edit',key:'inbound_line'}
  },
  { // 预覆盖建设--查看
    path: '/inbound_line/extend/precovering_construction/view',
    component: () => import('@/views/inbound_line/extend/precovering_construction/view.vue'),
    name: 'extend_invest_completion_look',
    meta: { title: '预覆盖建设详情页', icon: 'invest_completion_view',key:'inbound_line'}
  },
  { // 工单建设明细列表
    path: '/inbound_line/extend/work_order_detail',
    component: () => import('@/views/inbound_line/extend/work_order_detail/index.vue'),
    name: 'extend_work_order_detail_list',
    meta: { title: '工单建设明细列表', icon: 'work_order_detail',key:'inbound_line'}
  },
  { // 工单建设统计报表
    path: '/inbound_line/extend/work_order_count',
    component: () => import('@/views/inbound_line/extend/work_order_count/index.vue'),
    name: 'extend_work_order_count_list',
    meta: { title: '工单建设统计报表', icon: 'work_order_count',key:'inbound_line'}
  },
  { // 工单开通情况统计报表
    path: '/inbound_line/extend/work_order_open',
    component: () => import('@/views/inbound_line/extend/work_order_open/index.vue'),
    name: 'extend_work_order_open_list',
    meta: { title: '工单开通情况统计报表', icon: 'work_order_open',key:'inbound_line'}
  },
  { // 商机勘察、预覆盖建设明细
    path: '/inbound_line/extend/construction_work_detail',
    component: () => import('@/views/inbound_line/extend/construction_work_detail/index.vue'),
    name: 'extend_construction_work_detail_list',
    meta: { title: '商机勘察、预覆盖建设明细列表', icon: 'construction_work_detail',key:'inbound_line'}
  },
  { // 紧急建设需求区县人员列表
    path: '/inbound_line/extend/urgent_county_user',
    component: () => import('@/views/inbound_line/extend/urgent_county_user/index.vue'),
    name: 'extend_urgent_county_user_list',
    meta: { title: '紧急建设需求区县人员列表', icon: 'urgent_county_user',key:'inbound_line'}
  },
  { // 投资分派
    path: '/inbound_line/extend/one_yard_end',
    component: () => import('@/views/inbound_line/extend/one_yard_end/index.vue'),
    name: 'extend_one_yard_end_list',
    meta: { title: '一码到底派单列表', icon: 'one_yard_end',key:'inbound_line'}
  },
  { // 投资分派--处理
    path: '/inbound_line/extend/one_yard_end/edit',
    component: () => import('@/views/inbound_line/extend/one_yard_end/edit.vue'),
    name: 'extend_one_yard_end_detail',
    meta: { title: '专一码到底派单处理页', icon: 'one_yard_end_edit',key:'inbound_line'}
  },
  { // 投资分派--查看
    path: '/inbound_line/extend/one_yard_end/view',
    component: () => import('@/views/inbound_line/extend/one_yard_end/view.vue'),
    name: 'extend_one_yard_end_look',
    meta: { title: '一码到底派单详情页', icon: 'one_yard_end_view',key:'inbound_line'}
  },
]

export default extendMenuRouter
