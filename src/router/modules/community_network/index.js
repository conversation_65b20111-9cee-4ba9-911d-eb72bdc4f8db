/**
 * 小区入网路由
 */

 import Layout from '@/layout'
const communityNetworkRouter = [
  { // 小区入网
    path: '/inbound_line/community_network/order_list',
    component: () => import('@/views/inbound_line/extend/community_network/order_list/index.vue'),
    name: 'order_list',
    meta: { title: '小区入网工单列表', icon: 'order',key:'community_network'}
  },
  { // 小区入网新增
    path: '/inbound_line/community_network/order_list/add',
    component: () => import('@/views/inbound_line/extend/community_network/order_list/add.vue'),
    name: 'order_add',
    meta: { title: '小区入网新增', icon: 'order',key:'community_network'}
  },
]

export default communityNetworkRouter
