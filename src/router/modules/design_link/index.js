/**
 * 集客专线管理路由
 */


const designLinkRouter = [
  { // 设计环节预警
    path: '/design_link/warning',
    component: () => import('@/views/design_link/warning/index.vue'),
    name: 'design_warning',
    meta: { title: '设计环节预警列表', icon: 'warning',key:'project_task_visa'}
  },
  { // 设计费确认
    path: '/design_link/fee_confirm',
    component: () => import('@/views/design_link/fee_confirm/index.vue'),
    name: 'design_fee_confirm',
    meta: { title: '设计费确认代办列表', icon: 'fee_confirm',key:'project_task_visa'}
  },
  { // 设计费确认---处理页
    path: '/design_link/fee_confirm/edit',
    component: () => import('@/views/design_link/fee_confirm/edit.vue'),
    name: 'fee_confirm_detail',
    meta: { title: '设计费确认处理页', icon: 'fee_confirm_edit',key:'project_task_visa'}
  },
  { // 设计费确认---查看页
    path: '/design_link/fee_confirm/view',
    component: () => import('@/views/design_link/fee_confirm/view.vue'),
    name: 'fee_confirm_look',
    meta: { title: '设计费确认详情页', icon: 'fee_confirm_view',key:'project_task_visa'}
  },
  { // 设计费变更
    path: '/design_link/fee_modify',
    component: () => import('@/views/design_link/fee_modify/index.vue'),
    name: 'design_fee_modify',
    meta: { title: '设计费变更申请列表', icon: 'fee_modify',key:'project_task_visa'}
  },
  { // 设计费变更---处理页
    path: '/design_link/fee_modify/edit',
    component: () => import('@/views/design_link/fee_modify/edit.vue'),
    name: 'fee_modify_detail',
    meta: { title: '设计费变更处理页', icon: 'fee_modify_edit',key:'project_task_visa'}
  },
  { // 设计费变更---详情页
    path: '/design_link/fee_modify/view',
    component: () => import('@/views/design_link/fee_modify/view.vue'),
    name: 'fee_modify_look',
    meta: { title: '设计费变更详情页', icon: 'fee_modify_view',key:'project_task_visa'}
  },
  { // 设计费人工确认
    path: '/design_link/fee_confirm_person',
    component: () => import('@/views/design_link/fee_confirm_person/index.vue'),
    name: 'design_fee_confirm_person',
    meta: { title: '设计费人工确认列表', icon: 'fee_confirm_person',key:'project_task_visa'}
  },
  { // 设计费确认进度
    path: '/design_link/progress',
    component: () => import('@/views/design_link/progress/index.vue'),
    name: 'design_progress',
    meta: { title: '设计费确认进度列表', icon: 'progress',key:'project_task_visa'}
  },
  { // 设计费报表统计
    path: '/design_link/fee_list',
    component: () => import('@/views/design_link/fee_list/index.vue'),
    name: 'design_fee_list',
    meta: { title: '设计费报表统计列表', icon: 'fee_list',key:'project_task_visa'}
  },

]

export default designLinkRouter
