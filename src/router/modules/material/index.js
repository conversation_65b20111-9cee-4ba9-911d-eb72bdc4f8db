/*
 * @File name:
 * @Author: LT
 * @Date: 2023-07-07 15:52:12
 * @Description: 物资管理路由
 */

import Layout from '@/layout'

export default [
	{
		path: '/material/completion_audit',
		component: () => import('@/views/material/completion_audit'),
		name: 'meterial_completion_audit',
		meta: {
			title: '完工稽核',
      key:'staging_point',
			roles: ['admin', 'editor']
		}
	},
	{
		path: '/material/completion_audit_detail',
		component: () => import('@/views/material/completion_audit/detail'),
		name: 'completionAuditDetail',
		meta: {
			title: '完工稽核详情',
      key:'staging_point',
			roles: ['admin', 'editor']
		}
	},
	{
		path: '/material/usage_rpt_list',
		component: () => import('@/views/material/usage_rpt/index.vue'),
		name: 'UsageRpt',
		meta: {
			title: '物资使用填报管理列表',
      key:'staging_point',
		}
	},
	{
		path: '/material/usage_rpt_edit',
		component: () => import('@/views/material/usage_rpt/edit.vue'),
		name: 'usageRptEdit',
		meta: {
			title: '物资使用填报任务详情页',
      key:'staging_point',
		}
	},
	{
		path: '/material/usage_rpt_mod_list',
		component: () => import('@/views/material/usage_rpt_mod/index.vue'),
		name: 'UsageRptMod',
		meta: {
			title: '物资使用修订管理列表',
      key:'staging_point',
		}
	},
	{
		path: '/material/usage_rpt_mod_edit',
		component: () => import('@/views/material/usage_rpt_mod/edit.vue'),
		name: 'usageRptEdit',
		meta: {
			title: '物资使用修订任务详情页',
      key:'staging_point',
		}
	},
	{
		path: '/material/receive_audit',
		component: () => import('@/views/material/receive_audit'),
		name: 'ReceiveAudit',
		meta: {
			title: '领用稽核',
      key:'staging_point',
		}
	},
	{
		path: '/material/receive_audit_detail',
		component: () => import('@/views/material/receive_audit/detail'),
		name: 'CompletionAuditDetail',
		meta: {
			title: '领用稽核详情',
      key:'staging_point',
		}
	}
]
