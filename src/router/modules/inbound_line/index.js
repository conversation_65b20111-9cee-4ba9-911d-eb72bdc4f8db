/**
 * 集客专线管理路由
 */

 import Layout from '@/layout'
const inboundLineRouter = [
  { // 勘查
    path: '/inbound_line/prospecting',
    component: () => import('@/views/inbound_line/prospecting/index.vue'),
    name: 'prospecting_list',
    meta: { title: '专线勘查列表', icon: 'prospecting',key:'inbound_line'}
  },
  { // 勘查--处理
    path: '/inbound_line/prospecting/edit',
    component: () => import('@/views/inbound_line/prospecting/edit.vue'),
    name: 'prospecting_detail',
    meta: { title: '专线勘查处理页', icon: 'prospecting_edit',key:'inbound_line'}
  },
  { // 勘查--查看
    path: '/inbound_line/prospecting/view',
    component: () => import('@/views/inbound_line/prospecting/view.vue'),
    name: 'prospecting_look',
    meta: { title: '专线勘查详情页', icon: 'prospecting_view',key:'inbound_line'}
  },
  { // 归类池
    path: '/inbound_line/sorting_tank',
    component: () => import('@/views/inbound_line/sorting_tank/index.vue'),
    name: 'sorting_tank_list',
    meta: { title: '专线投资类归类池列表', icon: 'sorting_tank',key:'inbound_line'}
  },
  { // 归类池--处理
    path: '/inbound_line/sorting_tank/edit',
    component: () => import('@/views/inbound_line/sorting_tank/edit.vue'),
    name: 'sorting_tank_detail',
    meta: { title: '专线投资类归类池处理页', icon: 'sorting_tank_edit',key:'inbound_line'}
  },
  { // 归类池--查看
    path: '/inbound_line/sorting_tank/view',
    component: () => import('@/views/inbound_line/sorting_tank/view.vue'),
    name: 'sorting_tank_look',
    meta: { title: '专线投资类归类池详情页', icon: 'sorting_tank_view',key:'inbound_line'}
  },
  { // 归类池--统计报表
    path: '/inbound_line/sorting_tank/statistics',
    component: () => import('@/views/inbound_line/sorting_tank/statistics.vue'),
    name: 'sortingTankStatistics',
    meta: { title: '专线归类情况统计列表', icon: 'sorting_tank_statistics',key:'inbound_line'}
  },
  { // 方案批复
    path: '/inbound_line/proposal_approval',
    component: () => import('@/views/inbound_line/proposal_approval/index.vue'),
    name: 'proposal_approval_list',
    meta: { title: '专线成本类方案批复列表', icon: 'proposal_approval',key:'inbound_line'}
  },
  { // 方案批复--处理
    path: '/inbound_line/proposal_approval/edit',
    component: () => import('@/views/inbound_line/proposal_approval/edit.vue'),
    name: 'proposal_approval_detail',
    meta: { title: '专线成本类方案批复处理页', icon: 'proposal_approval_edit',key:'inbound_line'}
  },
  { // 方案批复--查看
    path: '/inbound_line/proposal_approval/view',
    component: () => import('@/views/inbound_line/proposal_approval/view.vue'),
    name: 'proposal_approval_look',
    meta: { title: '专线成本类方案批复详情页', icon: 'proposal_approval_view',key:'inbound_line'}
  },
  { // 分派
    path: '/inbound_line/construction_assignment',
    component: () => import('@/views/inbound_line/construction_assignment/index.vue'),
    name: 'construction_assignment_list',
    meta: { title: '专线成本类施工分派列表', icon: 'construction_assignment',key:'inbound_line'}
  },
  { // 分派--处理
    path: '/inbound_line/construction_assignment/edit',
    component: () => import('@/views/inbound_line/construction_assignment/edit.vue'),
    name: 'construction_assignment_detail',
    meta: { title: '专线成本类施工分派处理页', icon: 'construction_assignment_edit',key:'inbound_line'}
  },
  { // 分派--查看
    path: '/inbound_line/construction_assignment/view',
    component: () => import('@/views/inbound_line/construction_assignment/view.vue'),
    name: 'construction_assignment_look',
    meta: { title: '专线成本类施工分派详情页', icon: 'construction_assignment_view',key:'inbound_line'}
  },
  { // 投资类完工
    path: '/inbound_line/invest_completion',
    component: () => import('@/views/inbound_line/invest_completion/index.vue'),
    name: 'invest_completion_list',
    meta: { title: '专线投资类完工列表', icon: 'invest_completion',key:'inbound_line'}
  },
  { // 投资类完工--处理
    path: '/inbound_line/invest_completion/edit',
    component: () => import('@/views/inbound_line/invest_completion/edit.vue'),
    name: 'invest_completion_detail',
    meta: { title: '专线投资类完工处理页', icon: 'invest_completion_edit',key:'inbound_line'}
  },
  { // 投资类完工--查看
    path: '/inbound_line/invest_completion/view',
    component: () => import('@/views/inbound_line/invest_completion/view.vue'),
    name: 'invest_completion_look',
    meta: { title: '专线投资类完工详情页', icon: 'invest_completion_view',key:'inbound_line'}
  },
  { // 成本类完工
    path: '/inbound_line/cost_completion',
    component: () => import('@/views/inbound_line/cost_completion/index.vue'),
    name: 'cost_completion_list',
    meta: { title: '专线成本类完工列表', icon: 'cost_completion',key:'inbound_line'}
  },
  { // 成本类完工--处理
    path: '/inbound_line/cost_completion/edit',
    component: () => import('@/views/inbound_line/cost_completion/edit.vue'),
    name: 'cost_completion_detail',
    meta: { title: '专线成本类完工处理页', icon: 'cost_completion_edit',key:'inbound_line'}
  },
  { // 成本类完工--查看
    path: '/inbound_line/cost_completion/view',
    component: () => import('@/views/inbound_line/cost_completion/view.vue'),
    name: 'cost_completion_look',
    meta: { title: '专线成本类完工详情页', icon: 'cost_completion_view',key:'inbound_line'}
  },
  { // 成本项目
    path: '/inbound_line/cost_item',
    component: () => import('@/views/inbound_line/cost_item/index.vue'),
    name: 'cost_item_list',
    meta: { title: '专线成本类项目信息维护列表', icon: 'cost_item',key:'inbound_line'}
  },
  { // 成本类在途统计列表
    path: '/inbound_line/order_overdue',
    component: () => import('@/views/inbound_line/statistic_report/order_overdue/index.vue'),
    name: 'order_overdue',
    meta: { title: '专线成本类在途统计列表', icon: 'order_overdue',key:'inbound_line'}
  },
  { // 成本类在途统计详情
    path: '/inbound_line/order_overdue/detail',
    component: () => import('@/views/inbound_line/statistic_report/order_overdue/detail.vue'),
    name: 'order_overdue_detail',
    meta: { title: '专线成本类在途统计详情', icon: 'order_overdue_detail',key:'inbound_line'}
  },
  { // 成本类耗时统计列表
    path: '/inbound_line/order_time',
    component: () => import('@/views/inbound_line/statistic_report/order_time/index.vue'),
    name: 'response_progress',
    meta: { title: '专线成本类耗时统计列表', icon: 'order_time',key:'inbound_line'}
  },
  { // 成本类耗时统计详情
    path: '/inbound_line/order_time/detail',
    component: () => import('@/views/inbound_line/statistic_report/order_time/detail.vue'),
    name: 'order_time_detail',
    meta: { title: '专线成本类耗时统计详情', icon: 'order_time_detail',key:'inbound_line'}
  },
  { // 宽带响应进度
    path: '/inbound_line/response_progress',
    component: () => import('@/views/inbound_line/statistic_report/response_progress/index.vue'),
    name: 'response_progress_list',
    meta: { title: '宽带一天响应进展统计列表', icon: 'response_progress',key:'inbound_line'}
  },
  { // 宽带响应进度---下转
    path: '/inbound_line/response_progress/detail',
    component: () => import('@/views/inbound_line/statistic_report/response_progress/detail.vue'),
    name: 'response_progress_detail',
    meta: { title: '宽带一天响应进展统计详情', icon: 'response_progress_detail',key:'inbound_line'}
  },
  { // 集客专线进度统计报表
    path: '/inbound_line/progress_statistics',
    component: () => import('@/views/inbound_line/statistic_report/progress_statistics/index.vue'),
    name: 'progress_statistics_list',
    meta: { title: '专线进度统计列表', icon: 'progress_statistics',key:'inbound_line'}
  },
  { // 集客专线进度详情报表
    path: '/inbound_line/progress_statistics/detail',
    component: () => import('@/views/inbound_line/statistic_report/progress_statistics/detail.vue'),
    name: 'progress_statistics_detail',
    meta: { title: '专线进度统计详情', icon: 'progress_statistics_detail',key:'inbound_line'}
  },
  { // 投资类-在途集客专线统计报表
    path: '/inbound_line/invest_transit',
    component: () => import('@/views/inbound_line/statistic_report/invest_transit/index.vue'),
    name: 'invest_transit_list',
    meta: { title: '专线投资类在途统计列表', icon: 'invest_transit',key:'inbound_line'}
  },
  { // 投资类-在途集客专线详情报表
    path: '/inbound_line/invest_transit/detail',
    component: () => import('@/views/inbound_line/statistic_report/invest_transit/detail.vue'),
    name: 'invest_transit_detail',
    meta: { title: '专线投资类在途详情列表', icon: 'invest_transit_detail',key:'inbound_line'}
  },
  { // 投资类-集客专线耗时统计报表
    path: '/inbound_line/invest_time_consuming',
    component: () => import('@/views/inbound_line/statistic_report/invest_time_consuming/index.vue'),
    name: 'invest_time_consuming',
    meta: { title: '专线投资类耗时统计列表', icon: 'invest_time_consuming',key:'inbound_line'}
  },
  { //投资类-集客专线耗时详情报表
    path: '/inbound_line/invest_time_consuming/detail',
    component: () => import('@/views/inbound_line/statistic_report/invest_time_consuming/detail.vue'),
    name: 'invest_time_consuming_detail',
    meta: { title: '专线投资类耗时详情', icon: 'invest_time_consuming_detail',key:'inbound_line'}
  },
  { //6.宽带建设各环节时长统计
    path: '/inbound_line/band_construction_time',
    component: () => import('@/views/inbound_line/statistic_report/band_construction_time/index.vue'),
    name: 'band_construction_time',
    meta: { title: '宽带建设各环节时长统计列表', icon: 'band_construction_time',key:'inbound_line'}
  },
  { //宽带建设各环节时长详情统计
    path: '/inbound_line/band_construction_time/detail',
    component: () => import('@/views/inbound_line/statistic_report/band_construction_time/detail.vue'),
    name: 'band_construction_time_detail',
    meta: { title: '宽带建设各环节时长详情', icon: 'band_construction_time_detail',key:'inbound_line'}
  },
  { //宽带进度统计报表
    path: '/inbound_line/band_progress_query',
    component: () => import('@/views/inbound_line/statistic_report/band_progress_query/index.vue'),
    name: 'band_progress_query',
    meta: { title: '宽带进度统计列表', icon: 'band_progress_query',key:'inbound_line'}
  },
  { //宽带进度详情报表
    path: '/inbound_line/band_progress_query/detail',
    component: () => import('@/views/inbound_line/statistic_report/band_progress_query/detail.vue'),
    name: 'band_progress_query_detail',
    meta: { title: '宽带进度详情', icon: 'band_progress_query_detail',key:'inbound_line'}
  },
]

export default inboundLineRouter
