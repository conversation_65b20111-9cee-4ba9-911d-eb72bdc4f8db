/**
 * 数智赋能前端菜单路由
 */

const dwepManagerRouter = [
  {
    // 白名单
    path: '/dwep_manager/whitelist',
    component: () => import('@/views/dwep_manager/whitelist/index.vue'),
    name: 'order_list',
    meta: { title: '白名单管理', icon: 'order',key:'dwep_manager'}
  },
  {
    // 物资预警主页
    path: '/dwep_manager/completion_report',
    component: () => import('@/views/dwep_manager/completion_report/index.vue'),
    name: 'material_list',
    meta: { title: '物资预警管理', icon: 'material',key:'dwep_manager'}
  },
  { // 物资预警--处理
    path: '/dwep_manager/completion_report/edit',
    component: () => import('@/views/dwep_manager/completion_report/edit.vue'),
    name: 'material_detail',
    meta: { title: '物资预警处理页', icon: 'material_edit',key:'dwep_manager'}
  },
  { // 物资预警--查看
    path: '/dwep_manager/completion_report/view',
    component: () => import('@/views/dwep_manager/completion_report/view.vue'),
    name: 'material_look',
    meta: { title: '物资预警查看', icon: 'material_view',key:'dwep_manager'}
  },

  {
    // 物资平衡预警主页
    path: '/dwep_manager/completion_balance',
    component: () => import('@/views/dwep_manager/completion_balance/index.vue'),
    name: 'material_list',
    meta: { title: '物资平衡管理', icon: 'material',key:'dwep_manager'}
  },
  { // 物资平衡处理
    path: '/dwep_manager/completion_balance/edit',
    component: () => import('@/views/dwep_manager/completion_balance/edit.vue'),
    name: 'material_detail',
    meta: { title: '物资平衡处理', icon: 'material_edit',key:'dwep_manager'}
  },
  { // 物资平衡查看
    path: '/dwep_manager/completion_balance/view',
    component: () => import('@/views/dwep_manager/completion_balance/view.vue'),
    name: 'material_look',
    meta: { title: '物资平衡查看', icon: 'material_view',key:'dwep_manager'}
  },
  {
    // 资产类别与资源类型映射关系管理
    path: '/dwep_manager/assetResourceManage/index',
    component: () => import('@/views/dwep_manager/assetResourceManage/index.vue'),
    name: 'asset_resource_manage',
    meta: { title: '资产类别与资源类型映射关系管理', icon: 'asset_resource_manage',key:'dwep_manager'}
  },

]

export default dwepManagerRouter
