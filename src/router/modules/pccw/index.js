/*
 * @File name:
 * @Author: LT
 * @Date: 2023-07-07 15:52:12
 * @Description: 二期功能路由
 */
const pccwRouter = [
  //实施环节-安全生产费提醒工单
  {
    path: '/pccw_menu/implementation_link/safety_payment_alert',
    component: () => import('@/views/pccw/implementation_link/safety_payment_alert/index.vue'),
    name: 'SafetyPaymentAlert',
    meta: {
      title: '安全生产费提醒工单',
      key: 'pccw_menu'
    }
  },
  //实施环节-安全生产费待办工单
  {
    path: '/pccw_menu/implementation_link/secure_produce_pendingSave',
    component: () => import('@/views/pccw/implementation_link/secure_produce_pending/save.vue'),
    name: 'secureProducePending',
    meta: {
      title: '安全生产费待办工单',
      key: 'pccw_menu'
    }
  },
  // //实施环节-工序质量反馈
  // {
  //   path: '/pccw_menu/implementation_link/process_quality_feedback',
  //   component: () => import('@/views/pccw/implementation_link/process_quality_feedback/index.vue'),
  //   name: 'ProcessQualityFeedback',
  //   meta: {
  //     title: '工序质量反馈',
  //     key: 'pccw_menu'
  //   }
  // },
  //实施环节-安全生产费支付情况查询
  {
    path: '/pccw_menu/implementation_link/safety_payment_query',
    component: () => import('@/views/pccw/implementation_link/safety_payment_query/index.vue'),
    name: 'SafetyPaymentQuery',
    meta: {
      title: '安全生产费支付情况查询',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/implementation_link/urge_processing',
    component: () => import('@/views/pccw/implementation_link/urge_processing/index.vue'),
    name: 'urgeProcessing',
    meta: {
      title: '安全生产费流程跟踪',
      key: 'pccw_menu'
    }
  },
  //数据分析-设计审核情况统计-设计审查规范报表自动生产
  {
    path: '/pccw_menu/analysis_link/disign_audit_statistics/design_review_report',
    component: () => import('@/views/pccw/analysis_link/design_audit_statistics/design_review_report.vue'),
    name: 'DesignReviewReport',
    meta: {
      title: '设计审查规范报表自动生产',
      key: 'pccw_menu'
    }
  },
  //数据分析-物资需求管理
  {
    path: '/pccw_menu/analysis_link/material_demand_management',
    component: () => import('@/views/pccw/analysis_link/material_demand_management/index.vue'),
    name: 'MaterialDemandManagement',
    meta: {
      title: '物资需求管理',
      key: 'pccw_menu'
    }
  },
  //数据分析-物资需求管理-物资需求采购代办工单
  {
    path: '/pccw_menu/analysis_link/material_demand_handle',
    component: () => import('@/views/pccw/analysis_link/material_demand_management/material_demand_handle.vue'),
    name: 'materialDemandHandle',
    meta: {
      title: '物资需求采购代办工单',
      key: 'pccw_menu'
    }
  },
  //数据分析-物资需求管理-物资需求采购代办工单
  {
    path: '/pccw_menu/analysis_link/material_demand_remind',
    component: () => import('@/views/pccw/analysis_link/material_demand_management/material_demand_remind.vue'),
    name: 'material_demand_remind',
    meta: {
      title: '物资需求采购提醒工单',
      key: 'pccw_menu'
    }
  },
  //数据分析-物资需求管理-待办待阅
  {
    path: '/pccw_menu/analysis_link/readList',
    component: () => import('@/views/pccw/analysis_link/material_demand_management/readList.vue'),
    name: 'readList',
    meta: {
      title: '待办待阅',
      key: 'pccw_menu'
    }
  },
  //数据分析-物资到货跟踪
  {
    path: '/pccw_menu/analysis_link/material_arrival_tracking',
    component: () => import('@/views/pccw/analysis_link/material_arrival_tracking/index.vue'),
    name: 'MaterialArrivalTracking',
    meta: {
      title: '物资到货跟踪',
      key: 'pccw_menu'
    }
  },
  //数据分析-物资到货跟踪-待阅列表
  {
    path: '/pccw_menu/analysis_link/read_list',
    component: () => import('@/views/pccw/analysis_link/material_arrival_tracking/read_list.vue'),
    name: 'read_list',
    meta: {
      title: '待办待阅',
      key: 'pccw_menu'
    }
  },
  //数据分析-统计报表-设计及时性报表和预警
  {
    path: '/pccw_menu/analysis_link/real_time_reports_alerts',
    component: () => import('@/views/pccw/analysis_link/real_time_reports_alerts/index.vue'),
    name: 'RealTimeReportsAlerts',
    meta: {
      title: '设计及时性报表和预警',
      key: 'pccw_menu'
    }
  },
  //数据分析-统计报表-周报按省工建项目经理汇总模板
  {
    path: '/pccw_menu/analysis_link/weekly_provincial_overall',
    component: () => import('@/views/pccw/analysis_link/real_time_reports_alerts/weekly_provincial_overall.vue'),
    name: 'weeklyProvincialOverall',
    meta: {
      title: '周报按省工建项目经理汇总模板',
      key: 'pccw_menu'
    }
  },
  //数据分析-统计报表-周报按地市汇总模板
  {
    path: '/pccw_menu/analysis_link/weekly_city_overall',
    component: () => import('@/views/pccw/analysis_link/real_time_reports_alerts/weekly_city_overall.vue'),
    name: 'weeklyCityOverall',
    meta: {
      title: '周报按地市汇总模板',
      key: 'pccw_menu'
    }
  },
  //数据分析-统计报表-第一批设计批复预警
  {
    path: '/pccw_menu/analysis_link/approval_warning',
    component: () => import('@/views/pccw/analysis_link/real_time_reports_alerts/approval_warning.vue'),
    name: 'approvalWarning',
    meta: {
      title: '第一批设计批复预警',
      key: 'pccw_menu'
    }
  },
  //数据分析-统计报表-整体设计批复预警
  {
    path: '/pccw_menu/analysis_link/overall_approval_warning',
    component: () => import('@/views/pccw/analysis_link/real_time_reports_alerts/overall_approval_warning.vue'),
    name: 'overallApprovalWarning',
    meta: {
      title: '整体设计批复预警',
      key: 'pccw_menu'
    }
  },
  //数据分析-统计报表-设计勘查规范报表和预警
  {
    path: '/pccw_menu/analysis_link/design_survey_specify_report_alert',
    component: () => import('@/views/pccw/analysis_link/design_survey_specify_report_alert/index.vue'),
    name: 'DesignSurveySpecifyReportAlert',
    meta: {
      title: '设计勘查规范报表和预警',
      key: 'pccw_menu'
    }
  },
  //数据分析-统计报表-设计单位人员打卡清单
  {
    path: '/pccw_menu/analysis_link/check_task_list',
    component: () => import('@/views/pccw/analysis_link/design_survey_specify_report_alert/check_task_list.vue'),
    name: 'DesignSurveySpecifyReportAlert',
    meta: {
      title: '打卡信息清单',
      key: 'pccw_menu'
    }
  },
  // //数据分析-统计报表-设计单位人员打卡清单审批
  // {
  //   path: '/pccw_menu/analysis_link/check_task_view',
  //   component: () => import('@/views/pccw/analysis_link/design_survey_specify_report_alert/check_task_view.vue'),
  //   name: 'DesignSurveySpecifyReportAlert',
  //   meta: {
  //     title: '补充打卡信息清单',
  //     key: 'pccw_menu'
  //   }
  // },
  //数据分析-质监申报-质监申报报表查询
  {
    path: '/pccw_menu/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_query',
    component: () => import(
      '@/views/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_query/index.vue'),
    name: 'QualityDeclareReportQuery',
    meta: {
      title: '质监申报报表查询',
      key: 'pccw_menu'
    }
  },
  //数据分析-质监申报-质监申报整合表
  {
    path: '/pccw_menu/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_integration',
    component: () => import(
      '@/views/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_integration/index.vue'
      ),
    name: 'QualityDeclareReportIntegration',
    meta: {
      title: '质监申报整合表',
      key: 'pccw_menu'
    }
  },
  //数据分析-质监申报-质监申报原始表
  {
    path: '/pccw_menu/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_original',
    component: () => import(
        '@/views/pccw/analysis_link/quality_declare_report_auto_statistics/quality_declare_report_original/index.vue'
        ),
    name: 'QualityDeclareReportOriginal',
    meta: {
      title: '质监申报原始表',
      key: 'pccw_menu'
    }
  },
  //数据分析-质监申报-竣工备案整合表
  {
    path: '/pccw_menu/analysis_link/quality_declare_report_auto_statistics/completion_record_integration',
    component: () => import(
      '@/views/pccw/analysis_link/quality_declare_report_auto_statistics/completion_record_integration/index.vue'),
    name: 'CompletionRecordIntegration',
    meta: {
      title: '竣工备案整合表',
      key: 'pccw_menu'
    }
  },
  //数据分析-质监申报-质监备案原始表
  {
    path: '/pccw_menu/analysis_link/quality_declare_report_auto_statistics/completion_record_original',
    component: () => import(
      '@/views/pccw/analysis_link/quality_declare_report_auto_statistics/completion_record_original/index.vue'),
    name: 'CompletionRecordOriginal',
    meta: {
      title: '竣工备案原始表',
      key: 'pccw_menu'
    }
  },
  //数据分析-质监申报-项目信息整合表
  {
    path: '/pccw_menu/analysis_link/quality_declare_report_auto_statistics/project_information_integration',
    component: () => import(
        '@/views/pccw/analysis_link/quality_declare_report_auto_statistics/project_information_integration/index.vue'
        ),
    name: 'ProjectInformationIntegration',
    meta: {
      title: '项目信息整合表',
      key: 'pccw_menu'
    }
  },
  //数据分析-质监申报-节假日配置
  {
    path: '/pccw_menu/analysis_link/quality_declare_report_auto_statistics/holiday_configuration',
    component: () => import(
      '@/views/pccw/analysis_link/quality_declare_report_auto_statistics/holiday_configuration/index.vue'),
    name: 'HolidayConfiguration',
    meta: {
      title: '节假日配置',
      key: 'pccw_menu'
    }
  },

  //数据分析-统计报表-设计质量报表和预警
  {
    path: '/pccw_menu/analysis_link/design_quality_reports_alters',
    component: () => import('@/views/pccw/analysis_link/design_quality_reports_alters/index.vue'),
    name: 'designQualityReportsAlters',
    meta: {
      title: '设计质量报表和预警',
      key: 'pccw_menu'
    }
  },
  //数据分析-专线勘察统计报表
  {
    path: '/pccw_menu/analysis_link/special_line_survey_statistical_report',
    component: () => import('@/views/pccw/analysis_link/special_line_survey_statistical_report/index.vue'),
    name: 'specialLineSurveyStatisticalReport',
    meta: {
      title: '专线勘察统计报表',
      key: 'pccw_menu'
    }
  },
  // 验收环节-订单接收入账信息
  // pccw-rpa-ERP采购订单信息—列表
  {
    path: '/pccw_menu/comprehensive/acceptance',
    component: () => import('@/views/pccw/comprehensive/acceptance/index.vue'),
    name: 'AcceptanceList',
    meta: {
      title: '采购订单接收入账信息列表',
      key: 'pccw_menu'
    }
  },




  // 验收环节-订单接收入账预警
  {
    path: '/pccw_menu/acceptance_link/purchase_order/warning_list',
    component: () => import('@/views/pccw/acceptance_link/purchase_order/warning_list.vue'),
    name: 'PurchaseOrderWarningList',
    meta: {
      title: '未完成接收汇总报表',
      key: 'pccw_menu'
    }
  },
  // 验收环节-任务级竣工资料收集
  {
    path: '/pccw_menu/comprehensive/task_level_completion',
    component: () => import('@/views/pccw/comprehensive/task_level_completion/index.vue'),
    name: 'TaskLevelCompletion',
    meta: {
      title: '任务级竣工资料收集',
      key: 'pccw_menu'
    }
  },
  // 验收环节-任务级竣工材料收集明细
  {
    path: '/pccw_menu/comprehensive/task_level_completion/details',
    component: () => import('@/views/pccw/comprehensive/task_level_completion/details.vue'),
    name: 'TaskLevelCompletionDetails',
    meta: {
      title: '任务级竣工材料收集明细',
      key: 'pccw_menu'
    }
  },
  // 待阅->任务级竣工材料收集明细
  {
    path: '/pccw_menu/comprehensive/task_level_completion/details_warning',
    component: () => import('@/views/pccw/comprehensive/task_level_completion/details_warning.vue'),
    name: 'TaskLevelCompletionDetails',
    meta: {
      title: '任务级竣工材料收集明细',
      key: 'pccw_menu'
    }
  },
  // 验收环节-竣工资料收集不及时列表
  {
    path: '/pccw_menu/comprehensive/task_level_completion/warning_list',
    component: () => import('@/views/pccw/comprehensive/task_level_completion/warning_list.vue'),
    name: 'TaskLevelCompletionDetails',
    meta: {
      title: '竣工资料收集不及时列表',
      key: 'pccw_menu'
    }
  },
  // 验收环节-竣工资料收集-待办下发记录
  {
    path: '/pccw_menu/comprehensive/task_level_completion/report_flow_list_log',
    component: () => import('@/views/pccw/comprehensive/task_level_completion/report_flow_list_log.vue'),
    name: 'ReportFlowListLog',
    meta: {
      title: '任务竣工待办流程启动记录',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/comprehensive/task_level_completion/report_flow_list_log_admin',
    component: () => import('@/views/pccw/comprehensive/task_level_completion/report_flow_list_log_admin.vue'),
    name: 'ReportFlowListLog',
    meta: {
      title: '任务竣工待办流程启动记录-管理',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/comprehensive/task_level_completion/task_list',
    component: () => import('@/views/pccw/comprehensive/task_level_completion/task_list.vue'),
    name: 'TaskListLog',
    meta: {
      title: '任务列表',
      key: 'pccw_menu'
    }
  },
  // 数据分析-系统运行参数配置
  {
    path: '/pccw_menu/analysis_link/system_operation_parameters',
    component: () => import('@/views/pccw/analysis_link/system_operation_parameters/index.vue'),
    name: 'SystemOperationParameters',
    meta: {
      title: '系统运行参数',
      key: 'pccw_menu'
    }
  },
  // 数据分析-设计批复超期情况省项目经理汇总
  {
    path: '/pccw_menu/analysis_link/design_approval_overdue/pm_list',
    component: () => import('@/views/pccw/analysis_link/design_approval_overdue/pm_list.vue'),
    name: 'DesignApprovalOverduePMList',
    meta: {
      title: '设计批复超期情况省项目经理汇总',
      key: 'pccw_menu'
    }
  },
  // 数据分析-设计批复超期情况地市汇总
  {
    path: '/pccw_menu/analysis_link/design_approval_overdue/area_list',
    component: () => import('@/views/pccw/analysis_link/design_approval_overdue/area_list.vue'),
    name: 'DesignApprovalOverdueAreaList',
    meta: {
      title: '设计批复超期情况地市汇总',
      key: 'pccw_menu'
    }
  },
  // 数据分析-设计批复及时性预警
  {
    path: '/pccw_menu/analysis_link/design_approval_timeliness/warning_list',
    component: () => import('@/views/pccw/analysis_link/design_approval_timeliness/warning_list.vue'),
    name: 'DesignApprovalTimelinessWarning',
    meta: {
      title: '设计批复及时性预警',
      key: 'pccw_menu'
    }
  },
  // 验收环节-QIP视频验收-视频验收豁免清单
  {
    path: '/pccw_menu/analysis_link/video_acceptance/lpsVideoAcceptanceExempt',
    component: () => import('@/views/pccw/analysis_link/video_acceptance/lpsVideoAcceptanceExempt/index.vue'),
    name: 'lpsVideoAcceptanceExempt',
    meta: {
      title: '视频验收豁免清单',
      key: 'pccw_menu'
    }
  },
  // 验收环节-QIP视频验收-QIP视频验收推进情况报表
  {
    path: '/pccw_menu/analysis_link/video_acceptance/videoAcceptancePromotion',
    component: () => import('@/views/pccw/analysis_link/video_acceptance/videoAcceptancePromotion/index.vue'),
    name: 'videoAcceptancePromotion',
    meta: {
      title: 'QIP视频验收推进情况报表',
      key: 'pccw_menu'
    }
  },
  // 验收环节-QIP视频验收-地市质量管理员配置
  {
    path: '/pccw_menu/analysis_link/video_acceptance/cityAdminList',
    component: () => import('@/views/pccw/analysis_link/video_acceptance/cityAdminList.vue'),
    name: 'cityAdminList',
    meta: {
      title: '地市质量管理员配置',
      key: 'pccw_menu'
    }
  },
  // 验收环节-QIP视频验收-省公司质量管理员配置
  {
    path: '/pccw_menu/analysis_link/video_acceptance/provincialAdminList',
    component: () => import('@/views/pccw/analysis_link/video_acceptance/provincialAdminList.vue'),
    name: 'provincialAdminList',
    meta: {
      title: '省公司质量管理员配置',
      key: 'pccw_menu'
    }
  },
  // sysUser—列表
  {
    path: '/pccw_menu/system/sysUser',
    component: () => import('@/views/pccw/system/sysUser/index.vue'),
    name: 'SysUserList',
    meta: {
      title: '用户信息',
      key: 'pccw_menu'
    }
  },

  // 报表查询
  {
    path: '/pccw_menu/report_select/financial_attachment_download',
    component: () => import('@/views/pccw/report_select/financial_attachment_download/index.vue'),
    name: 'FinancialAttachmentDownload',
    meta: {
      title: '综合财务门户附件下载参数配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/erp_attachment_download',
    component: () => import('@/views/pccw/report_select/erp_attachment_download/index.vue'),
    name: 'ErpAttachmentDownload',
    meta: {
      title: 'ERP系统附件下载参数配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/purchase_order',
    component: () => import('@/views/pccw/report_select/purchase_order/index.vue'),
    name: 'PurchaseOrderConfig',
    meta: {
      title: '订单接收入账预警与报表配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/purchase_order/unit',
    component: () => import('@/views/pccw/report_select/purchase_order/unit.vue'),
    name: 'PurchaseOrderConfig',
    meta: {
      title: '订单接收入账省本部建设单位配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/purchase_order/add',
    component: () => import('@/views/pccw/report_select/purchase_order/add.vue'),
    name: 'AddPurchaseOrderConfig',
    meta: {
      title: '新增订单接收入账角色管理配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/purchase_order/unit/add',
    component: () => import('@/views/pccw/report_select/purchase_order/unit_add.vue'),
    name: 'AddPurchaseOrderConfig',
    meta: {
      title: '新增订单接收入账省本部建设单位配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/purchase_order/summary_report',
    component: () => import('@/views/pccw/report_select/purchase_order/summary_report/index.vue'),
    meta: {
      title: '订单未完成接收汇总报表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/quarter_transfer_task',
    component: () => import('@/views/pccw/report_select/quarter_transfer_task/index.vue'),
    name: 'QuarterTransferTask',
    meta: {
      title: '季度转资任务完成报表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/settlement_reduction_rate',
    component: () => import('@/views/pccw/report_select/settlement_reduction_rate/index.vue'),
    name: 'SettlementReductionRate',
    meta: {
      title: '结算审减率报表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/config_management/quarter_transfer_task',
    component: () => import('@/views/pccw/report_select/quarter_transfer_task/config.vue'),
    name: 'QuarterTransferTaskConfig',
    meta: {
      title: '季度转资与转资不及时配置管理',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/config_management/quarter_transfer_task_import',
    component: () => import('@/views/pccw/report_select/quarter_transfer_task/task_import.vue'),
    name: 'QuarterTransferTaskImport',
    meta: {
      title: '季度转资任务导入',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/quarter_transfer_task/alert_info',
    component: () => import('@/views/pccw/report_select/quarter_transfer_task/alert_info.vue'),
    name: 'QuarterTransferTaskAlert',
    meta: {
      title: '季度转资任务预警信息'
    }
  },
  {
    path: '/pccw_menu/report_select/config_management/settlement_reduction_rate',
    component: () => import('@/views/pccw/report_select/settlement_reduction_rate/config.vue'),
    name: 'SettlementReductionRateConfig',
    meta: {
      title: '结算审减率报表配置管理',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/report_select/settlement_reduction_rate/alert_info',
    component: () => import('@/views/pccw/report_select/settlement_reduction_rate/alert_info.vue'),
    name: 'SettlementReductionRateConfig',
    meta: {
      title: '结算审减率预警信息'
    }
  },
  // 统一待办
  {
    path: '/pccw_menu/workflow_emis/record',
    component: () => import('@/views/pccw/workflow/work_record/index'),
    name: 'work_record',
    meta: {
      title: '流程记录',
      key: 'pccw_menu'
    }
    },
  // 流程审批
  {
    path: '/pccw_menu/pccw_workflow/work_supervisory',
    component: () => import('@/views/pccw/workflow/work_supervisory/index'),
    name: 'work_supervisory',
    meta: {
      title: '流程监控',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/work_operation',
    component: () => import('@/views/pccw/workflow/work_operation/index'),
    name: 'work_operation',
    meta: {
      title: '流程操作',
      key: 'pccw_menu'
    }
  },
  // 运行时流程变量—列表
  {
    path: '/pccw_menu/workflow/work_variable',
    component: () => import('@/views/pccw/workflow/work_variable/index.vue'),
    name: 'MarchEmisList',
    meta: {
      title: '运行时流程变量信息列表',
      key: 'pccw_menu'
    }
  },
  // // 运行时流程变量—添加
  // {
  //   path: '/pccw_menu/workflow/work_variable/add',
  //   component: () => import('@/views/pccw/workflow/work_variable/add.vue'),
  //   name: 'AddMarchEmis',
  //   meta: {
  //     title: '添加运行时流程变量',
  //     key: 'pccw_menu'
  //   }
  // },
  // // 运行时流程变量—修改
  // {
  //   path: '/pccw_menu/workflow/work_variable/edit',
  //   component: () => import('@/views/pccw/workflow/work_variable/edit.vue'),
  //   name: 'EditMarchEmis',
  //   meta: {
  //     title: '修改运行时流程变量',
  //     key: 'pccw_menu'
  //   }
  // },
  {
    path: '/pccw_menu/pccw_workflow/work_todo',
    component: () => import('@/views/pccw/workflow/work_todo/index'),
    name: 'WorkTodo',
    meta: {
      title: '我的待办',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/work_finished',
    component: () => import('@/views/pccw/workflow/work_finished/index'),
    name: 'WorkFinished',
    meta: {
      title: '我的已办',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/work_own',
    component: () => import('@/views/pccw/workflow/work_own/index'),
    name: 'WorkTodo',
    meta: {
      title: '我的发起',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/approve_detail',
    component: () => import('@/views/pccw/workflow/approve/flowableDetails/index'),
    name: 'WorkFlow',
    meta: {
      title: '流程审批',
      key: 'pccw_menu'
    }
  },
  // 决算环节->审计材料提取
  {
    path: '/pccw_menu/settlement_link/audit_material_extraction',
    component: () => import('@/views/pccw/settlement_link/audit_material_extraction/index.vue'),
    meta: {
      title: '审计材料提取',
      key: 'pccw_menu'
    }
  },
  // 决算环节->审计材料明细
  {
    path: '/pccw_menu/settlement_link/audit_material_extraction_details',
    component: () => import('@/views/pccw/settlement_link/audit_material_extraction/details.vue'),
    meta: {
      title: '审计材料提取',
      key: 'pccw_menu'
    }
  },
  // 流程测试
  {
    path: '/pccw_menu/pccw_workflow/test',
    component: () => import('@/views/pccw/workflow/test/index'),
    name: 'wtest',
    meta: {
      title: '流程测试',
      key: 'pccw_menu'
    }
  },
  // 流程公共页面
  {
    path: '/pccw_menu/pccw_workflow/common_page',
    component: () => import('@/views/pccw/workflow/common_page/index'),
    name: 'common_page',
    meta: {
      title: '流程页面',
      key: 'pccw_menu'
    }
  },
  // 流程完工任务级竣工资料收集
  {
    path: '/pccw_menu/pccw_workflow/task_completion',
    component: () => import('@/views/pccw/workflow/task_completion/taskLevelCompletionData/index'),
    name: 'task_completion',
    meta: {
      title: '流程页面',
      key: 'pccw_menu'
    }
  },
  // 任务转资
  {
    path: '/pccw_menu/pccw_workflow/maintain_unfinished',
    component: () => import('@/views/pccw/workflow/task_transfer/maintain_unfinished/index'),
    meta: {
      title: '交维未完成',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/maintain_finished',
    component: () => import('@/views/pccw/workflow/task_transfer/maintain_finished/index'),
    meta: {
      title: '交维已完成',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/supervise',
    component: () => import('@/views/pccw/workflow/task_transfer/supervise/index'),
    meta: {
      title: '督办整改',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/control',
    component: () => import('@/views/pccw/workflow/task_transfer/control/index'),
    meta: {
      title: '错转管控',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/control_report',
    component: () => import('@/views/pccw/workflow/task_transfer/control_report/index'),
    meta: {
      title: '结算审减后转资数量修改情况查询表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/task_transfer/warning',
    component: () => import('@/views/pccw/workflow/task_transfer/warning/index'),
    meta: {
      title: '任务转资提前滞后率待阅',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/pccw_workflow/task_transfer/warning/details',
    component: () => import('@/views/pccw/workflow/task_transfer/warning/details'),
    meta: {
      title: '任务转资提前滞后率明细列表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/acceptance_link/transfer_lag_warning',
    component: () => import('@/views/pccw/workflow/task_transfer/warning_untimely/statistical_statement/index'),
    meta: {
      title: '转资不及时预警统计报表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/acceptance_link/transfer_advance_lag_rate',
    component: () => import('@/views/pccw/workflow/task_transfer/advance_lag_rate/index'),
    meta: {
      title: '任务转资提前滞后率统计报表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/acceptance_link/task_transfer/warning/untimely/management_manager',
    component: () => import('@/views/pccw/workflow/task_transfer/warning_untimely/index'),
    meta: {
      title: '待阅-转资不及时-工程管理经理（主）',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/acceptance_link/task_transfer/warning/untimely/implementation_manager',
    component: () => import('@/views/pccw/workflow/task_transfer/warning_untimely/index'),
    meta: {
      title: '待阅-转资不及时-工程实施经理（主、辅）',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/acceptance_link/task_transfer/warning_untimely/details',
    component: () => import('@/views/pccw/workflow/task_transfer/warning_untimely/details'),
    meta: {
      title: '待阅-转资不及时-明细',
      key: 'pccw_menu'
    }
  },
  // // 物资需求管理待办
  // {
  //   path: '/pccw_menu/pccw_workflow/material_demand_management/todo',
  //   component: () => import('@/views/pccw/workflow/material_demand_management/index'),
  //   name: 'material_demand_management_todo',
  //   meta: {
  //     title: '物资需求采购待办',
  //     key: 'pccw_menu'
  //   }
  // },
  // // 物资需求管理待阅
  // {
  //   path: '/pccw_menu/pccw_workflow/material_demand_management/toRead',
  //   component: () => import('@/views/pccw/workflow/material_demand_management/toRead'),
  //   name: 'material_demand_management_toRead',
  //   meta: {
  //     title: '物资需求采购待阅',
  //     key: 'pccw_menu'
  //   }
  // },
  // 物资到货跟踪超期订单预警工单
  {
    path: '/pccw_menu/pccw_workflow/material_arrival_tracking/overdue_order_warning_work_order',
    component: () => import('@/views/pccw/workflow/material_arrival_tracking/overdue_order_warning_work_order'),
    name: 'overdue_order_warning_work_order',
    meta: {
      title: '超期订单预警工单',
      key: 'pccw_menu'
    }
  },
  // 物资到货特殊原因接收订单审批工单
  {
    path: '/pccw_menu/pccw_workflow/material_arrival_tracking/special_reasonOrder_approval_order',
    component: () => import('@/views/pccw/workflow/material_arrival_tracking/MaterialApproval'),
    name: 'special_reasonOrder_approval_order',
    meta: {
      title: '特殊原因接收订单审批工单',
      key: 'pccw_menu'
    }
  },
  // 物资到货跟踪审批待办工单
  {
    path: '/pccw_menu/pccw_workflow/material_arrival_tracking/todo',
    component: () => import('@/views/pccw/workflow/material_arrival_tracking/todo'),
    name: 'overdue_order_warning_work_order',
    meta: {
      title: '物资到货跟踪审批工单',
      key: 'pccw_menu'
    }
  },
  // 物资到货跟踪通报超期接收订单工单
  {
    path: '/pccw_menu/pccw_workflow/material_arrival_tracking/toRead',
    component: () => import('@/views/pccw/workflow/material_arrival_tracking/toRead'),
    name: 'material_arrival_tracking_toRead',
    meta: {
      title: '通报超期接收订单工单',
      key: 'pccw_menu'
    }
  },
  // 数据分析->工序质量反馈->关键工序上传与审核进度情况报表
  {
    path: '/pccw_menu/process_quality_feedback/crux_process_upload',
    component: () => import('@/views/pccw/analysis_link/process_quality_feedback/crux_process_upload'),
    name: 'crux_process_upload',
    meta: {
      title: '关键工序上传与审核进度情况报表',
      key: 'pccw_menu'
    }
  },
  // 数据分析->工序质量反馈->关键工序豁免清单
  {
    path: '/pccw_menu/process_quality_feedback/exemption_list',
    component: () => import('@/views/pccw/analysis_link/process_quality_feedback/exemption_list'),
    name: 'exemption_list',
    meta: {
      title: '关键工序豁免清单',
      key: 'pccw_menu'
    }
  },
  // 数据分析->工序质量反馈->关键工序模板名称配置
  {
    path: '/pccw_menu/process_quality_feedback/template_name',
    component: () => import('@/views/pccw/analysis_link/process_quality_feedback/template_name'),
    name: 'template_name',
    meta: {
      title: '关键工序模板名称配置',
      key: 'pccw_menu'
    }
  },
  // 数据分析->工序质量反馈->施工派工规范性情况统计报表
  {
    path: '/pccw_menu/process_quality_feedback/construction_dispatch_list',
    component: () => import('@/views/pccw/analysis_link/process_quality_feedback/construction_dispatch_list'),
    name: 'construction_dispatch_list',
    meta: {
      title: '施工派工规范性情况统计报表',
      key: 'pccw_menu'
    }
  },
  // 数据分析->工序质量反馈->施工派工豁免清单
  {
    path: '/pccw_menu/process_quality_feedback/construction_dispatch_exemption',
    component: () => import('@/views/pccw/analysis_link/process_quality_feedback/construction_dispatch_exemption'),
    name: 'construction_dispatch_exemption',
    meta: {
      title: '施工派工豁免清单',
      key: 'pccw_menu'
    }
  },
  // 复核工单->工序质量反馈
  {
    path: '/pccw_menu/review_work_order/processQualityFeedback',
    component: () => import('@/views/pccw/implementation_link/review_work_order/processQualityFeedback/index'),
    name: 'processQualityFeedback',
    meta: {
      title: '抽查配置页面',
      key: 'pccw_menu'
    }
  },
  // 复核工单->质量汇总报表
  {
    path: '/pccw_menu/review_work_order/qualitySummary',
    component: () => import('@/views/pccw/implementation_link/review_work_order/qualitySummary/index'),
    name: 'qualitySummary',
    meta: {
      title: '质量汇总报表',
      key: 'pccw_menu'
    }
  },
  // 复核工单->关键工序抽查主页
  {
    path: '/pccw_menu/review_work_order/spotCheck',
    component: () => import('@/views/pccw/implementation_link/review_work_order/spotCheck/index'),
    name: 'spotCheck',
    meta: {
      title: '关键工序抽查',
      key: 'pccw_menu'
    }
  },
  // 流程 -> 工序质量
  {
    path: 'pccw_menu/workflow/review/process_quality/check',
    component: () => import('@/views/pccw/workflow/review/process_quality/check'),
    name: 'process_quality_todo',
    meta: {
      title: '工序质量抽查待办',
      key: 'pccw_menu'
    }
  },
  // 待阅 -> 工序质量
  {
    path: 'pccw_menu/workflow/review/process_quality/msg',
    component: () => import('@/views/pccw/workflow/review/process_quality/msg/index'),
    name: 'process_quality_msg',
    meta: {
      title: '工序质量待阅',
      key: 'pccw_menu'
    }
  },
  // 流程 -> 视频验收
  {
    path: 'pccw_menu/workflow/review/video_check/check',
    component: () => import('@/views/pccw/workflow/review/video_quality/check'),
    // component: () => import('@/views/pccw/workflow/review/video_check/index'),
    name: 'video_quality_todo',
    meta: {
      title: '视频验收待办',
      key: 'pccw_menu'
    }
  },
  // 待阅 -> 视频验收
  {
    path: 'pccw_menu/workflow/review/video_check/msg',
    component: () => import('@/views/pccw/workflow/review/video_quality/msg/index'),
    name: 'video_check_msg',
    meta: {
      title: '视频验收待阅',
      key: 'pccw_menu'
    }
  },
  {
    path: 'pccw_menu/video_acceptance/cityQualityAllocation',
    component: () => import(
      '@/views/pccw/analysis_link/video_acceptance/lpsVideoAcceptanceExempt/cityQualityAllocation.vue'),
    meta: {
      title: '地市质量管理员配置',
      key: 'pccw_menu'
    }
  },
  {
    path: 'pccw_menu/video_acceptance/provinceQualityAllocation',
    component: () => import(
      '@/views/pccw/analysis_link/video_acceptance/lpsVideoAcceptanceExempt/provinceQualityAllocation.vue'),
    meta: {
      title: '省公司质量管理员',
      key: 'pccw_menu'
    }
  },
  //安全工作-列表
  {
    path: 'pccw_menu/safety_production/safety_work',
    component: () => import('@/views/pccw/safety_production/safety_work/index.vue'),
    meta: {
      title: '安全工作',
      key: 'pccw_menu'
    }
  },
  //安全工作-列表查看
  {
    path: 'pccw_menu/safety_production/safety_work/templateDetails',
    component: () => import('@/views/pccw/safety_production/safety_work/templateDetails.vue'),
    meta: {
      title: '安全工作',
      key: 'pccw_menu'
    }
  },
  // 安全工作-待阅
  {
    path: 'pccw_menu/safety_production/safety_work/taskList',
    component: () => import('@/views/pccw/safety_production/safety_work/taskList.vue'),
    meta: {
      title: '工作填报工单',
      key: 'pccw_menu'
    }
  },
  // 安全工作-工单进度-催办
  {
    path: 'pccw_menu/safety_production/workOrderProgress',
    component: () => import('@/views/pccw/safety_production/workOrderProgress/index.vue'),
    meta: {
      title: '工单进度',
      key: 'pccw_menu'
    }
  },
  // 安全工作-工单待办
  {
    path: 'pccw_menu/safety_production/workdOrderPending',
    component: () => import('@/views/pccw/safety_production/workdOrderPending/index.vue'),
    meta: {
      title: '工单待办',
      key: 'pccw_menu'
    }
  },
  //合作单位后评估
  {
    path: '/pccw_menu/cooperation_evaluate/external_assessment_item',
    component: () => import('@/views/pccw/cooperation_evaluate/external_assessment_item/index.vue'),
    name: 'ExternalAssessmentItem',
    meta: {
      title: '外部录入考核事项',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/problem_feedback_query',
    component: () => import('@/views/pccw/cooperation_evaluate/problem_feedback_query/index.vue'),
    name: 'ProblemFeedbackQuery',
    meta: {
      title: '问题反馈提交情况查询',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config',
    component: () => import('@/views/pccw/cooperation_evaluate/config/constructionNucleusConfig.vue'),
    name: 'CooperationEvaluateConfig',
    meta: {
      title: '配置管理',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/constructionNucleusConfig',
    component: () => import('@/views/pccw/cooperation_evaluate/config/constructionNucleusConfig.vue'),
    name: 'constructionNucleusConfig',
    meta: {
      title: '建设单位合作单位考核接口人配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/countyTimeoutConfig',
    component: () => import('@/views/pccw/cooperation_evaluate/config/countyTimeoutConfig.vue'),
    name: 'countyTimeoutConfig',
    meta: {
      title: '区县超时待办失效时长设置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/constructionAdminAndMaintenanceScoreConfig',
    component: () => import(
      '@/views/pccw/cooperation_evaluate/config/constructionAdminAndMaintenanceScoreConfig.vue'),
    name: 'constructionAdminAndMaintenanceScoreConfig',
    meta: {
      title: '建设单位合作单位管理员、维护部门评分人配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/provinceAdminConfig',
    component: () => import('@/views/pccw/cooperation_evaluate/config/provinceAdminConfig.vue'),
    name: 'provinceAdminConfig',
    meta: {
      title: '省公司合作单位管理员配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/countyFeedbackConfig',
    component: () => import('@/views/pccw/cooperation_evaluate/config/countyFeedbackConfig.vue'),
    name: 'countyFeedbackConfig',
    meta: {
      title: '区县反馈人配置页面',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/countyMaintainConfig',
    component: () => import('@/views/pccw/cooperation_evaluate/config/countyMaintainConfig.vue'),
    name: 'countyMaintainConfig',
    meta: {
      title: '区县建维主管配置页面',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/countyBranchingConfig',
    component: () => import('@/views/pccw/cooperation_evaluate/config/countyBranchingConfig.vue'),
    name: 'countyBranchingConfig',
    meta: {
      title: '区县分管领导配置页面',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/projectTaskStageAssessment',
    component: () => import('@/views/pccw/cooperation_evaluate/config/projectTaskStageAssessment.vue'),
    name: 'projectTaskStageAssessment',
    meta: {
      title: '项目任务所在阶段考核表模板配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/config/superviseIssueTypeConfig',
    component: () => import('@/views/pccw/cooperation_evaluate/config/superviseIssueTypeConfig.vue'),
    name: 'superviseIssueTypeConfig',
    meta: {
      title: '合作单位监督发现问题分类配置',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/database/index',
    component: () => import('@/views/pccw/database_view/index.vue'),
    name: 'databaseListIndex',
    meta: {
      title: '数据库可视化界面',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/log/index',
    component: () => import('@/views/pccw/database_view/logIndex.vue'),
    name: 'logListIndex',
    meta: {
      title: '日志下载界面',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/problem_feedback_query/supervise_list',
    component: () => import('@/views/pccw/cooperation_evaluate/problem_feedback_query/supervise_list.vue'),
    name: 'supervise_list',
    meta: {
      title: '合作单位监督发现问题报表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/cooperation_evaluate/problem_feedback_query/check_list',
    component: () => import('@/views/pccw/cooperation_evaluate/problem_feedback_query/check_list.vue'),
    name: 'check_list',
    meta: {
      title: '外部录入考核事项报表',
      key: 'pccw_menu'
    }
  },
  // 合作单位后评估-考核问题收集
  {
    path: '/pccw_menu/workflow/cooperation_evaluate/assess_questions_collection/',
    component: () => import('@/views/pccw/workflow/cooperation_evaluate/assess_questions_collection/index'),
    name: 'assess_questions_collection',
    meta: {
      title: '考核问题收集',
      key: 'pccw_menu'
    }
  },
  // 考核问题收集流程-项目考评表
  {
    path: '/pccw_menu/workflow/cooperation_evaluate/assess_questions_collection/stage_assessment',
    component: () => import(
      '@/views/pccw/workflow/cooperation_evaluate/assess_questions_collection/stage_assessment/index.vue'),
    name: 'stage_assessment',
    meta: {
      title: '考核问题收集流程-项目考评表',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/workflow/cooperation_evaluate/assess_questions_collection/report',
    component: () => import('@/views/pccw/workflow/cooperation_evaluate/assess_questions_collection/report/index'),
    name: 'assess_questions_collection_report',
    meta: {
      title: '合作单位考核问题台账报表',
      key: 'pccw_menu'
    }
  },
  // 合作单位后评估-考核问题催办
  {
    path: '/pccw_menu/workflow/cooperation_evaluate/assess_questions_collection/urge_processing',
    component: () => import('@/views/pccw/workflow/cooperation_evaluate/assess_questions_collection/urge_processing/index'),
    name: 'assess_questions_urge_processing',
    meta: {
      title: '考核问题收集流程催办',
      key: 'pccw_menu'
    }
  },
  // 合作单位后评估-申诉主页面
  {
    path: '/pccw_menu/workflow/cooperation_evaluate/assess_questions_appeal/',
    component: () => import('@/views/pccw/workflow/cooperation_evaluate/assess_questions_appeal/index'),
    name: 'assess_questions_appeal',
    meta: {
      title: '考核问题申诉',
      key: 'pccw_menu'
    }
  },
  // 待阅
  {
    path: '/pccw_menu/workflow/assess_questions_appeal/msg',
    component: () => import('@/views/pccw/workflow/cooperation_evaluate/assess_questions_appeal/msg/index'),
    name: 'assess_questions_msg',
    meta: {
      title: '建设工程申诉处理后考核问题',
      key: 'pccw_menu'
    }
  },
  //光缆医生
  {
    path: '/pccw_menu/optical_cable_doctor/batchList',
    component: () => import('@/views/pccw/optical_cable_doctor/batchList.vue'),
    name: 'check_list',
    meta: {
      title: '分析结果批量查询',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/optical_cable_doctor/opticalCableDetailsList',
    component: () => import('@/views/pccw/optical_cable_doctor/report.vue'),
    name: 'check_list',
    meta: {
      title: '报表查询',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/optical_cable_doctor/createWorkOrder',
    component: () => import('@/views/pccw/optical_cable_doctor/createWorkOrder.vue'),
    name: 'check_list',
    meta: {
      title: '创建工单',
      key: 'pccw_menu'
    }
  },
  // 数据分析-审计报表提取列表
  {
    path: '/pccw_menu/analysis_link/audit_list',
    component: () => import('@/views/pccw/analysis_link/audit_list/index.vue'),
    name: 'auditList',
    meta: {
      title: '审计报表提取',
      key: 'pccw_menu'
    }
  },
  {
    path: '/pccw_menu/appeals_process/stay_read',
    component: () => import('@/views/pccw/workflow/cooperation_evaluate/appeals_process/stay_read.vue'),
    name: 'stay_read',
    meta: {
      title: '待阅',
      key: 'pccw_menu'
    }
  },
]

export default pccwRouter
