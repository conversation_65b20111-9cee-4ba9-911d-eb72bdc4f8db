/*
 * @File name:
 * @Author: LT
 * @Date: 2023-07-07 15:52:12
 * @Description: 月度报表路由
 */

import Layout from '@/layout'

export default [
  {
    path: '/monthly_report',
    component: Layout,
    redirect: 'safe_check/safe_check_temp/list',
    name: 'safeCheckTemp',
    meta: {
      title: '模板管理',
      key: 'monthly_report',
      roles: ['admin', 'editor']
    },
    children: [
      {
        path: 'safe_check/safe_check_temp/list',
        component: () => import('@/views/monthly_report/safe_check_temp/index.vue'),
        name: 'SafeCheckTempFindPage',
        meta: {
          title: '模板管理列表',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check_temp/edit',
        component: () => import('@/views/monthly_report/safe_check_temp/edit_form.vue'),
        name: 'SafeCheckTempEditForm',
        meta: {
          title: '模板管理处理页',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check_temp/view',
        component: () => import('@/views/monthly_report/safe_check_temp/view_form.vue'),
        name: 'SafeCheckTempViewForm',
        meta: {
          title: '模板管理详情页',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/user_staff/list',
        component: () => import('@/views/monthly_report/user_staff/index.vue'),
        name: 'user_staff',
        meta: {
          title: '人员配置管理列表',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check/list',
        component: () => import('@/views/monthly_report/safe_check/index.vue'),
        name: 'safe_check',
        meta: {
          title: '安全检查工单下发列表',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check/edit',
        component: () => import('@/views/monthly_report/safe_check/edit_form.vue'),
        name: 'SafeCheckEditForm',
        meta: {
          title: '安全检查工单下发处理页',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check/view',
        component: () => import('@/views/monthly_report/safe_check/view_form.vue'),
        name: 'SafeCheckViewForm',
        meta: {
          title: '安全检查工单下发详情页',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check_back/list',
        component: () => import('@/views/monthly_report/safe_check_back/index.vue'),
        name: 'SafeCheckBackFindPage',
        meta: {
          title: '安全检查工单反馈列表',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check_back/edit',
        component: () => import('@/views/monthly_report/safe_check_back/edit_form.vue'),
        name: 'SafeCheckBackEditForm',
        meta: {
          title: '安全检查工单反馈处理页',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check_back/view',
        component: () => import('@/views/monthly_report/safe_check_back/view_form.vue'),
        name: 'SafeCheckBackViewForm',
        meta: {
          title: '安全检查工单反馈详情页',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check_warning/list',
        component: () => import('@/views/monthly_report/safe_check_warning/index.vue'),
        name: 'safe_check_warning',
        meta: {
          title: '安全生产费预警配置列表',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check_warning/view',
        component: () => import('@/views/monthly_report/safe_check_warning/view_list.vue'),
        name: 'safe_check_warning——',
        meta: {
          title: '安全生产费预警配置详情页列表',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'safe_check/safe_check_progress/list',
        component: () => import('@/views/monthly_report/safe_check_progress/index.vue'),
        name: 'SafeCheckProgressFindPage',
        meta: {
          title: '安全检查进度查询列表',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'config_manage/time_configuration',
        component: () => import('@/views/monthly_report/time_configuration'),
        name: 'time_configuration',
        meta: {
          title: '自定义派发管理',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'config_manage/process_link_config',
        component: () => import('@/views/monthly_report/process_link_config'),
        name: 'processLinkConfig',
        meta: {
          title: '流程环节配置管理',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'config_manage/supervision_city_config',
        component: () => import('@/views/monthly_report/supervision_city_config'),
        name: 'supervisionCityConfig',
        meta: {
          title: '监理与地市关系配置管理',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'progress_query',
        component: () => import('@/views/monthly_report/progress_query'),
        name: 'progressQuery',
        meta: {
          title: '进度查询',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: 'progress_query/detail',
        component: () => import('@/views/monthly_report/progress_query/detail'),
        name: 'progress_query_detail',
        meta: {
          title: '进度查询详情',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: '/monthly_report/work_reporting/work_order_list',
        component: () => import('@/views/monthly_report/work_order/index.vue'),
        name: 'ReportWorkOrderList',
        meta: {
          title: '月工作上报工单管理列表',
          key: 'monthly_report'
        }
      },
      {
        path: '/monthly_report/work_reporting/work_order_edit',
        component: () => import('@/views/monthly_report/work_order/edit.vue'),
        name: 'ReportWorkOrderEdit',
        meta: {
          title: '任务详情',
          key: 'monthly_report'
        }
      },
      {
        path: '/monthly_report/work_reporting_summary/work_order_list',
        component: () => import('@/views/monthly_report/summary_work_order/index.vue'),
        name: 'ReportSummaryWorkOrderList',
        meta: {
          title: '月工作上报汇总工单管理列表',
          key:'monthly_report',
        }
      },
      {
        path: '/monthly_report/work_reporting_summary/work_order_edit',
        component: () => import('@/views/monthly_report/summary_work_order/edit.vue'),
        name: 'summaryWorkOrderEdit',
        meta: {
          title: '任务详情',
          key:'monthly_report',
        }
      },
      {
        path: '/monthly_report/work_reporting/provincial_summary_list',
        component: () => import('@/views/monthly_report/provincial_summary/index.vue'),
        name: 'ProvincialSummaryList',
        meta: {
          title: '月工作上报省级汇总列表',
          key: 'monthly_report'
        }
      },
      {
        path: '/monthly_report/work_reporting/provincial_summary_edit',
        component: () => import('@/views/monthly_report/provincial_summary/edit.vue'),
        name: 'ProvincialSummaryEdit',
        meta: {
          title: '月工作上报省级汇总详情页',
          key: 'monthly_report'
        }
      },
      {
        path: '/monthly_report/work_reporting/template_management_list',
        component: () => import('@/views/monthly_report/template_management/index.vue'),
        name: 'TemplateManagementList',
        meta: {
          title: '月工作上报模板管理列表',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      {
        path: '/monthly_report/work_reporting/template_management_info',
        component: () => import('@/views/monthly_report/template_management/info.vue'),
        name: 'TemplateManagementInfo',
        meta: {
          title: '月工作上报模板管理详情页',
          key: 'monthly_report',
          roles: ['admin', 'editor']
        }
      },
      { // 月工作上报任务填报人员关系配置
        path: 'config_manage/personnel_allocation',
        component: () => import('@/views/monthly_report/personnel_allocation/index.vue'),
        name: 'PersonnelAllocation',
        meta: {
          title: '月工作上报任务填报人员关系配置',
          key: 'monthly_report'
        }
      },
      { // 及时性分值配置
        path: 'config_manage/timeliness_score_configuration',
        component: () => import('@/views/monthly_report/timeliness_score_configuration/index.vue'),
        name: 'timelyRiskAllocation',
        meta: {
          title: '及时性分值配置',
          key: 'monthly_report'
        }
      },
      { // 月安全工作记录
        path: 'config_manage/safety_work_record',
        component: () => import('@/views/monthly_report/safety_work_record/index.vue'),
        name: 'SafetyWorkRecord',
        meta: {
          title: '月安全工作记录',
          key: 'monthly_report'
        }
      },
      { // 月安全工作记录-编辑
        path: 'config_manage/safety_work_record/edit',
        component: () => import('@/views/monthly_report/safety_work_record/edit.vue'),
        name: 'SafetyWorkRecordEdit',
        meta: {
          title: '编辑月安全工作记录',
          key: 'monthly_report'
        }
      },
      {// PS: 设计任务综资入口就是账实相符，只是改了名字。2023年08月22客户要求的（谭中辉）
        path: '/account_agree/report_find_page',
        component: () => import('@/views/monthly_report/account_agree/report_index.vue'),
        name: 'AccountAgreeControllerFindPage',
        meta: {
          title: '账实相符进度查询列表',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      {
        // 账实相符列表页（不区分类型）
        path: '/account_agree/find_page',
        component: () => import('@/views/monthly_report/account_agree/index.vue'),
        name: 'AccountAgreeControllerFindPage',
        meta: {
          title: '账实相符列表',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      {
        path: '/account_agree/edit',
        component: () => import('@/views/monthly_report/account_agree/edit_form.vue'),
        name: 'AccountAgreeEditForm',
        meta: {
          title: '账实相符处理页',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      {
        path: '/account_agree/view',
        component: () => import('@/views/monthly_report/account_agree/view_form.vue'),
        name: 'AccountAgreeViewForm',
        meta: {
          title: '账实相符详情页',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      {
        // 账实相符列表页（新建类型）
        path: '/account_agree_add/find_page',
        component: () => import('@/views/monthly_report/account_agree/account_agree_add/find_page.vue'),
        name: 'AccountAgreeAddFindPage',
        meta: {
          title: '新增空间资源入网',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      { // 账实相符处理页(新建类型）
        path: '/account_agree_add/edit',
        component: () => import('@/views/monthly_report/account_agree/account_agree_add/edit_form.vue'),
        name: 'AccountAgreeAddEditForm',
        meta: {
          title: '新增空间资源入网处理页',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      {// 账实相符查看页(新建类型）
        path: '/account_agree_add/view',
        component: () => import('@/views/monthly_report/account_agree/account_agree_add/view_form.vue'),
        name: 'AccountAgreeAddViewForm',
        meta: {
          title: '新增空间资源入网详情页',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      {
        // 账实相符列表页（改扩建）
        path: '/account_agree_stock/find_page',
        component: () => import('@/views/monthly_report/account_agree/account_agree_stock/find_page.vue'),
        name: 'AccountAgreeStockFindPage',
        meta: {
          title: '存量空间名称比对',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      { // 账实相符处理页(改扩建类型）
        path: '/account_agree_stock/edit',
        component: () => import('@/views/monthly_report/account_agree/account_agree_stock/edit_form.vue'),
        name: 'AccountAgreeStockEditForm',
        meta: {
          title: '存量空间名称比对处理页',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      {// 账实相符查看页(改扩建类型）
        path: '/account_agree_stock/view',
        component: () => import('@/views/monthly_report/account_agree/account_agree_stock/view_form.vue'),
        name: 'AccountAgreeStockViewForm',
        meta: {
          title: '存量空间名称比对详情页',
          key:'project_task_visa',
          icon: 'lock',
          roles: ['admin', 'editor']
        }
      },
      {// 空间入网资源列表页
        path: '/account_agree_view/list',
        component: () => import('@/views/monthly_report/account_agree/account_agree_view/index.vue'),
        name: 'account_agree_view_list',
        meta: {
          title: '新增空间入网资源列表页',
          key:'project_task_visa',
          icon: 'account_agree_view_list',
          roles: ['admin', 'editor']
        }
      },
      { // 安全生产费用管理-项目维度统计
        path: 'fee_payment_status/project',
        component: () => import('@/views/monthly_report/safe_production_manage/fee_payment_status_project.vue'),
        name: 'fee_payment_status_project',
        meta: { title: '安全生产费项目维度列表', icon: 'fee_payment_status_project',key:'monthly_report'}
      },
      { // 安全生产费用管理-批次维度统计
        path: 'fee_payment_status/batch',
        component: () => import('@/views/monthly_report/safe_production_manage/fee_payment_status_batch.vue'),
        name: 'fee_payment_status_batch',
        meta: { title: '安全生产费批次维度列表', icon: 'fee_payment_status_project',key:'monthly_report'}
      },
      { // 安全生产费用管理-任务维度统计
        path: 'fee_payment_status/task',
        component: () => import('@/views/monthly_report/safe_production_manage/fee_payment_status_task.vue'),
        name: 'fee_payment_status_task',
        meta: { title: '安全生产费任务维度列表', icon: 'fee_payment_status_project',key:'monthly_report'}
      },
    ]
  }
]
