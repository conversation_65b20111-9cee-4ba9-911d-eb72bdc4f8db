/**
 * 合作单位管理路由
 */

const cooperation_company = [
  // 合作单位信息维护
  {
    path: '/cooperation_company/company_info',
    component: () => import('@/views/cooperation_company/company_info/index.vue'),
    name: 'CompanyInfoList',
    meta: {
      title: '合作单位信息列表',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/company_info/edit',
    component: () => import('@/views/cooperation_company/company_info/edit.vue'),
    name: 'CooperativeUnitEdit',
    meta: {
      title: '合作单位信息处理页',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/company_info/view',
    component: () => import('@/views/cooperation_company/company_info/detail.vue'),
    name: 'CooperativeUnitDetail',
    meta: {
      title: '合作单位信息详情页',
      key: 'cooperation_company'
    }
  },
  // 分包单位信息维护
  {
    path: '/cooperation_company/subcontractor_info',
    component: () => import('@/views/cooperation_company/subcontractor_info/index.vue'),
    name: 'SubcontractorInfoList',
    meta: {
      title: '分包单位信息列表',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/subcontractor_info/edit',
    component: () => import('@/views/cooperation_company/subcontractor_info/edit.vue'),
    name: 'SubcontractorInfoEdit',
    meta: {
      title: '分包单位信息处理页',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/subcontractor_info/view',
    component: () => import('@/views/cooperation_company/subcontractor_info/detail.vue'),
    name: 'SubcontractorInfoDetail',
    meta: {
      title: '分包单位信息详情页',
      key: 'cooperation_company'
    }
  },
  // 合作单位人员信息维护
  {
    path: '/cooperation_company/personnel_info',
    component: () => import('@/views/cooperation_company/personnel_info/index.vue'),
    name: 'PersonnelInfoList',
    meta: {
      title: '合作人员信息列表',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/personnel_info/edit',
    component: () => import('@/views/cooperation_company/personnel_info/edit.vue'),
    name: 'PersonnelInfoEdit',
    meta: {
      title: '合作人员信息处理页',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/personnel_info/view',
    component: () => import('@/views/cooperation_company/personnel_info/detail.vue'),
    name: 'PersonnelInfoDetail',
    meta: {
      title: '合作人员信息详情页',
      key: 'cooperation_company'
    }
  },
  // 合作单位黑白名单管理
  {
    path: '/cooperation_company/company_balcklist_whitelist',
    component: () => import('@/views/cooperation_company/company_balcklist_whitelist/index.vue'),
    name: 'CompanyBalcklistWhitelistList',
    meta: {
      title: '合作单位黑白名单列表',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/company_balcklist_whitelist/edit',
    component: () => import('@/views/cooperation_company/company_balcklist_whitelist/edit.vue'),
    name: 'CompanyBalcklistWhitelistEdit',
    meta: {
      title: '合作单位黑白名单处理页',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/company_balcklist_whitelist/view',
    component: () => import('@/views/cooperation_company/company_balcklist_whitelist/detail.vue'),
    name: 'CompanyBalcklistWhitelistDetail',
    meta: {
      title: '合作单位黑白名单详情页',
      key: 'cooperation_company'
    }
  },
  // 人员黑白名单管理
  {
    path: '/cooperation_company/personnel_blacklist_whitelist',
    component: () => import('@/views/cooperation_company/personnel_blacklist_whitelist/index.vue'),
    name: 'PersonnelBlacklistWhitelistList',
    meta: {
      title: '人员黑白名单列表',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/personnel_blacklist_whitelist/edit',
    component: () => import('@/views/cooperation_company/personnel_blacklist_whitelist/edit.vue'),
    name: 'PersonnelBlacklistWhitelistEdit',
    meta: {
      title: '人员黑白名单处理页',
      key: 'cooperation_company'
    }
  },
  {
    path: '/cooperation_company/personnel_blacklist_whitelist/view',
    component: () => import('@/views/cooperation_company/personnel_blacklist_whitelist/detail.vue'),
    name: 'PersonnelBlacklistWhitelistDetail',
    meta: {
      title: '人员黑白名单详情页',
      key: 'cooperation_company'
    }
  },
  // 考核任务模板管理
  {
    path: '/cooperation_company/eval_template',
    component: () => import('@/views/cooperation_company/eval_template/index'),
    name: 'TemplateList',
    meta: { title: '考核任务模板列表', icon: 'eval_template', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/eval_template/edit',
    component: () => import('@/views/cooperation_company/eval_template/edit'),
    name: 'AddTemplate',
    meta: { title: '考核任务模板编辑', icon: 'evalTemplateEdit', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/eval_template/view',
    component: () => import('@/views/cooperation_company/eval_template/detail'),
    name: 'TemplateDetail',
    meta: { title: '考核任务模板详情页', icon: 'evalTemplateDetail', key: 'cooperation_company' }
  },
  // 单位考核
  {
    path: '/cooperation_company/company_evaluation',
    component: () => import('@/views/cooperation_company/company_evaluation/index'),
    name: 'UnitevaluationList',
    meta: { title: '单位考核列表', icon: 'templateList', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/company_evaluation/edit',
    component: () => import('@/views/cooperation_company/company_evaluation/evalscore.vue'),
    name: 'Evalscore',
    meta: { title: '单位考核得分处理页', icon: 'unitEvaluationList', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/company_evaluation/view',
    component: () => import('@/views/cooperation_company/company_evaluation/detail.vue'),
    name: 'UnitEvaluation',
    meta: { title: '单位考核得分详情页', icon: 'unitEvaluationList', key: 'cooperation_company' }
  },
  // 合作单位考核-审计单位考核
  {
    path: '/cooperation_company/audit_company_evaluation',
    component: () => import('@/views/cooperation_company/audit_company_evaluation'),
    name: 'AuditCompanyEvaluation',
    meta: { title: '合作单位考核-审计单位考核', icon: 'auditCompanyEvaluation', key: 'cooperation_company' }
  },
  // 合作单位月度考评对应关系表
  {
    path: '/cooperation_company/relation_table',
    component: () => import('@/views/cooperation_company/relation_table'),
    name: 'MonthlyRelationTable',
    meta: { title: '合作单位月度考评对应关系表', key: 'cooperation_company' }
  },
  // 合作单位月度考评对应关系表的新增or编辑
  {
    path: '/cooperation_company/relation_table/edit',
    component: () => import('@/views/cooperation_company/relation_table/edit.vue'),
    name: 'MonthlyRelationTableEdit',
    meta: { title: '合作单位月度考评对应关系表处理页', key: 'cooperation_company' }
  },
  // 合作单位月度考评对应关系表的查看
  {
    path: '/cooperation_company/relation_table/view',
    component: () => import('@/views/cooperation_company/relation_table/detail.vue'),
    name: 'MonthlyRelationTableDetail',
    meta: { title: '合作单位月度考评对应关系表详情页', key: 'cooperation_company' }
  },
  // 合作单位月度得分
  {
    path: '/cooperation_company/monthly_score/good',
    component: () => import('@/views/cooperation_company/monthly_score'),
    name: 'MonthlyScore',
    meta: { title: '考核月度得分列表', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/monthly_score/detail',
    component: () => import('@/views/cooperation_company/monthly_score/detail'),
    name: 'monthlyscoreDetail',
    meta: { title: '考核月度得分详情', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/monthly_score/bad',
    component: () => import('@/views/cooperation_company/monthly_score'),
    name: 'MonthlyScoreBad',
    meta: { title: '考核月度扣分列表', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/monthly_score/score_settings',
    component: () => import('@/views/cooperation_company/monthly_score/score_settings'),
    name: 'scorePerSettings',
    meta: { title: '合作单位评分权重配置页', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/period_score/conSupervision',
    component: () => import('@/views/cooperation_company/period_score'),
    name: 'periodScore',
    meta: { title: '考核项目阶段得分(施工/监理)列表', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/period_score/design',
    component: () => import('@/views/cooperation_company/period_score'),
    name: 'periodScore',
    meta: { title: '考核项目阶段得分(设计)列表', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/year_score',
    component: () => import('@/views/cooperation_company/year_score'),
    name: 'yearScore',
    meta: { title: '考核年度得分列表', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/year_score/upload',
    component: () => import('@/views/cooperation_company/year_score/upload_files'),
    name: 'yearScoreUploadFiles',
    meta: { title: '附件管理', key: 'cooperation_company' }
  },
  {
    path: '/cooperation_company/framework_score',
    component: () => import('@/views/cooperation_company/framework_score'),
    name: 'frameworkScore',
    meta: { title: '框架合同月度考核得分', key: 'cooperation_company' }
  },
]

export default cooperation_company
