<!--
 * @Descripttion: 搜索条件（注意：采用span传入宽度，未传就是8）
 * @Author: hcm
 * @Date: 2023-06-30
-->
<template>
  <div>
    <el-card class="search-card card">
      <div slot="header" class="title">
        <div class="left-wrap">
          <i class="iconfont icon-dasuolvetuliebiao" />
          <span>{{ title }}</span>
        </div>
        <div class="right-wrap">
          <div v-show="defaultBtn" class="default-btn">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button v-if="resetFlag" @click="handleReset">重置</el-button>
          </div>
          <!-- 可自定义按钮 -->
          <slot name="customBtn" />
        </div>
      </div>
      <div class="content">
        <!-- class="search-form" -->
        <el-form
          v-show="isDefaultForm"
          ref="searchForm"
          class=""
          :model="searchForm"
          :label-width="labelWidth"
          @submit.native.prevent
        >
          <!-- <el-row style="width: 100%;"> -->
          <el-row style="display: flex; flex-wrap: wrap;" :gutter="10">
            <!-- span= 24/行数 -->
            <el-col
              v-for="(item,index) in searchConfig"
              :key="index"
              :span="item.span || 6"
              :offset="item.offset || 0"
              style="flex: 0 0 25%;"
            >
            <!-- 动态计算行数 :span="item.span || 6" -->
            <!-- 防止跨列 -->
              <el-form-item
                :label="item.label+':'"
                :title="item.label"
                :class="item.type"
                :prop="item.fieldName"
                :rules="item.rules"
              >
              <!-- 纯文本框 -->
              <div  v-if="item.type==='text'">
                {{searchForm[item.fieldName]}}
              </div>
<!--              <el-input
                v-if="item.type==='text'"
                v-model="searchForm[item.fieldName]"
                type="textarea"
                rows="1"
                class="textarea-width"
                :disabled="item.disabled"
              /> -->
                <!-- input框 -->
                <el-input
                  v-if="item.type==='input'"
                  v-model="searchForm[item.fieldName]"
                  maxlength="100"
                  :name="item.label"
                  :disabled="true"
                  @keyup.enter.native="handleSearch"
                />
            </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
  </div>
</template>
<script>

export default {
  name: 'mssCheckForm',
  props: {
    labelWidth: {
      type: String,
      default: '140px' // 字段宽度
    },
    title: { // 默认title
      type: String,
      default: '基础信息'
    },
    defaultBtn: { // 默认按钮
      type: Boolean,
      default: false
    },
    isDefaultForm: { // 默认表单
      type: Boolean,
      default: true
    },
    searchConfig: { // 表单配置
      type: Array,
      default: () => []
    },
    btnLoading: { // 按钮的loading
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => {}
    },
    resetFlag:{
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      weeklyDate:'',
      searchForm: { ...this.form }, // 查询表单
      startDate: { // 开始时间范围限定
        disabledDate: time => {
          if (this.searchForm[endDate] && this.endDate) {
            return time.getTime() > this.endDate
          } else {
            return false
          }
        }
      },
      endDate: { // 结束时间范围限定
        disabledDate: time => {
          if (this.startDate && this.startDate) {
            return time.getTime() < this.startDate
          } else {
            return false
          }
        }
      }
    }
  },
  watch: {
    form: {
      handler(newValue, oldValue) {
        this.searchForm = Object.assign({}, this.searchForm, newValue)
      },
      deep: true
    }
  },
  mounted() {
    // console.log(this.searchConfig,'config')
  },
  methods: {
    pickerOptionSet(code,type){
      return {
        disabledDate: (time) => {
          if(this.searchForm[code]){
            return type=='start'?this.$moment(time).diff(this.searchForm[code], 'days') > 0:this.$moment(time).diff(this.searchForm[code], 'days')  < 0
          }
        }
      }
    },
    // 弹框
    openDialog(val) {
      this.$emit('openDialog', val)
    },
    // 重置
    handleReset() {
      this.searchConfig.forEach(v => {
        if (v.type === 'date2') {
          this.$set(this.searchForm, v.fieldName, v.value)
          this.$set(this.searchForm, v.fieldName2, v.value2)
        } else {
          this.$set(this.searchForm, v.fieldName, v.value)
        }
      })
      this.$emit('reset', this.searchForm)
    },
    // 查询
    handleSearch() {
      this.$refs.searchForm.validate(v => {
        if(v){
          this.$emit('search', this.searchForm)
        }
      })
    },
    // 下拉框事件
    changeSelect(name, val) {
      this.$emit('changeSelect', name, val)
    },
  }
}
</script>
<style lang="scss" scoped>
  .content{
    margin: 5px 20px;
  }

  ::v-deep .el-form-item__label{
    width: 100px;
    // background: rgba(245, 245, 245, 0.5) ;
    // border: 1px solid #D9D9D9 ;
  }
</style>
