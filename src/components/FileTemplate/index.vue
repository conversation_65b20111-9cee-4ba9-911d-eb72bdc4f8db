/**
 * @Description:  附件列表
 * <AUTHOR>
 * @date 23/8/17
*/
<template>
  <div class="file-template">
    <ul class="file-list">
        <li v-for="item in fileList" :key="item.id">
          <el-checkbox v-if="showCheckBox" v-model="item.checked"></el-checkbox>
          <span class="file-name" @click="getDown(item)">{{ item.name }}</span>
        </li>
    </ul>
  </div>
</template>

<script>
import {
  queryFilesListService,
  getFilesDownloadService2,
  getFilesBatchDownloadService,
  getFilesBatchDownloadService2
} from '@/api/attachment'
export default {
  name: "FileTemplate",
  props: {
    customParams: {
      type: Object,
      default() {
        return {};
      }
    },
    showCheckBox:{
      type: Boolean,
      default() {
        return true;
      }
    }
  },
  data() {
    return {
      checkedFiles: [],
      fileList: [] // 附件列表
    };
  },
  created() {
    this.getfileList(); // 初始化附件模板列表
  },
  methods: {
    /**
     * 获取上传文件后的列表
     */
    getfileList() {
      let req =
      {
        ids:this.customParams.ids
      };
      queryFilesListService(req).then(res => {
        if (res && res.code == "0000") {
          this.fileList = res.data.data;
          this.fileList = this.fileList.map(item=>{
            return {...item,checked:false}
          })
        }
      });
    },
    /**
     * 下载
     * @param item
     * @returns {string}
     */
     getDown(item) {
      getFilesDownloadService2(item.id)
      .then(res => {
        const url = window.URL.createObjectURL(res.data);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', item.name); // 设定下载文件的名字
        document.body.appendChild(link);
        link.click()
        // 清理和释放资源
        link.parentNode.removeChild(link)
        window.URL.revokeObjectURL(url) // 释放掉blob对象
      })
      .catch(e=>{})
    },
    // 去除文件名的后缀
    removeExtension(filename) {
      return filename.replace(/\.[^/.]+$/, '')
    },
    /**
     * 批量下载
     * @param item
     * @returns {string}
     */
    //
    getFilesBatchDownload() {
      let selectFiles = [];
      this.fileList.forEach(item=>{
        if(item.checked){
          selectFiles.push(item.id)
        }
      })
      if (selectFiles.length) {
        getFilesBatchDownloadService2({
          ids: selectFiles.join()
        })
          .then(res => {
            // 创建blob链接
            const url = window.URL.createObjectURL(new Blob([res.data]));
            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;

            // 可以设置文件名(这里用第一个文件的名字+.zip)
            // 第一个文件的名字，但是要去掉.后缀
            let firstFileName = selectFiles[0].name
            let downloadFileName = this.removeExtension(firstFileName) + '.zip'
            link.setAttribute('download', downloadFileName) // 设定下载文件的名字

            // 模拟点击下载
            document.body.appendChild(link);
            link.click()

            // 清理和释放资源
            link.parentNode.removeChild(link)
            window.URL.revokeObjectURL(url) // 释放掉blob对象
          })
          .catch(e=>{})
      } else {
        this.$message.warning('请选择附件')
      }
    },
  }
};
</script>
<style lang="scss" scoped>
::v-deep .file-list {
  margin: 0;
  padding: 0;
  li{
  text-align: left;
  display: flex;
  width: 100%;
  .el-checkbox{
    .el-checkbox__input{
      display: inline-block;
    }
    .el-checkbox__label{
      display: none;
    }
  }
  .file-name{
    margin-left: 10px;
    display: inline-block;
    width: calc(100% - 14px - 10px);
    cursor: pointer;
    &:hover{
      color:#33ACFB;
    }
  }
}}
</style>
