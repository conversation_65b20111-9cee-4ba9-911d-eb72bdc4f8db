<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="100%"
    :modal="false"
    custom-class="progress-dialog"
  >
    <div class="progress-bar stripes animated reverse">
      <el-progress class="progress" :text-inside="true" :stroke-width="26" :percentage="percentage"></el-progress>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'index',
  data() {
    return {
      dialogVisible: false,
      percentage: 0,
      intervalId: null
    }
  },
  methods: {
    open() {
      this.percentage = 0
      this.dialogVisible = true
      this.intervalId = null
      this.startAnimate()
    },
    close() {
      clearInterval(this.intervalId)
      this.dialogVisible = false
    },
    startAnimate() {
      this.intervalId = setInterval(() => {
        if (this.percentage < 99) {
          this.percentage = this.percentage + 1
        }
      }, 100)
    },
  }
}
</script>

<style scoped lang="scss">
::v-deep .progress-dialog {
  height: 100% !important;
  margin: 0 !important;
  background: transparent !important;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    height: 100%;
    position: relative;
  }
  .progress-bar {
    position: relative;
    top: 50%;
    left: 50%;
    margin-left: -225px;
    width: 450px;
  }
}
</style>
