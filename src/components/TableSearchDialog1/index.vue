/**
* @author: ty
* @date: 2023-07-07
* @description: dialog+table的复合型组件。应用场景：比如工程签证的项目查询
*/
<template>
  <el-dialog
    class="table-search-dialog"
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose">
    <div class="table-search-dialog---content">
      <div class="search-form">
        <el-form :model="searchFormModel" ref="searchForm" :label-width="labelWidth">
          <el-row style="float: left;width: calc(100% - 100px);">
            <el-col
              v-for="(item,index) in searchFields"
              :key="index"
              :span="item.span || 8"
              :offset="item.offset || 0"
            >
              <el-form-item
                :label="item.label"
                :title="item.label"
                :class="item.type"
                :prop="item.fieldName"
                :rules="item.rules"
              >
                <!-- input框 -->
                <el-input
                  v-if="item.type==='input'"
                  v-model="searchFormModel[item.fieldName]"
                  maxlength="100"
                  :name="item.label"
                  :placeholder="item.label"
                  :disabled="item.disabled"
                />
                <!-- 文本域框 -->
                <el-input
                  v-if="item.type==='textarea'"
                  v-model="searchFormModel[item.fieldName]"
                  type="textarea"
                  rows="1"
                  class="textarea-width"
                  :placeholder="item.label"
                  :disabled="item.disabled"
                />
                <!-- 数字输入框 -->
                <el-input-number
                  v-if="item.type ==='number'"
                  v-model="searchFormModel[item.fieldName]"
                  :placeholder="item.label"
                  :disabled="item.disabled"
                  :controls-position="item.controlsPosition || 'right'"
                />
                <!-- 弹框 -->
                <el-input
                  v-if="item.type==='dialog'"
                  v-model="searchFormModel[item.fieldName]"
                  readonly="readonly"
                  :name="item.label"
                  :disabled="item.disabled"
                  @click.native="openDialog(item.handleDialog)"
                />
                <!-- 复选框 -->
                <el-checkbox-group
                  v-else-if="item.type==='checkbox'"
                  v-model="searchFormModel[item.fieldName]"
                >
                  <template v-for="(value,index) in item.options">
                    <el-checkbox
                      :key="index"
                      :disabled="item.disabled"
                      :label="value.value"
                      :name="item.fieldName"
                    >{{ value.label }}
                    </el-checkbox>
                  </template>
                </el-checkbox-group>
                <!-- 单选框 -->
                <el-radio-group
                  v-else-if="item.type==='radio'"
                  v-model="searchFormModel[item.fieldName]"
                >
                  <template v-for="(value,index) in item.options">
                    <el-radio :key="index" :label="value.value" :disabled="value.disabled">{{ value.label }}</el-radio>
                  </template>
                </el-radio-group>
                <!-- 下拉框 -->
                <el-select
                  v-else-if="item.type==='select'"
                  v-model="searchFormModel[item.fieldName]"
                  :placeholder="'请选择' + item.label"
                  :multiple="item.selectMultiple || false"
                  :disabled="item.disabled"
                  :clearable="item.clearable"
                  :filterable="item.filterable"
                  :value-key="item.itemValue ? item.itemValue : 'value'"
                  @change="(v) => changeSelect(item.fieldName, v)"
                >
                  <template v-for="(option,index) in item.options">
                    <el-option :key="index"
                               :label="option[item.itemLabel ? item.itemLabel : 'label']"
                               :value="
                    item.itemAs
                      ? option
                      : option[item.itemValue ? item.itemValue : 'value']
                  "/>
                  </template>
                </el-select>
                <el-date-picker
                  v-else-if="item.type==='date1'"
                  v-model="searchFormModel[item.fieldName]"
                  :disabled="item.disabled"
                  :type="item.dateType || 'date'"
                  placeholder="选择日期"
                  :format="item.format || 'yyyy-MM-dd'"
                  :value-format="item.valueFormat || 'yyyy-MM-dd'"
                />
                <!-- 起止时间选择框 -->
                <el-date-picker
                  v-else-if="item.type==='daterange'"
                  v-model="searchFormModel[item.fieldName]"
                  :type="item.dateType || 'daterange'"
                  range-separator="-"
                  start-placeholder="开始日期"
                  :disabled="item.disabled"
                  end-placeholder="结束日期"
                  :format="item.format || 'yyyy-MM-dd HH:mm:ss'"
                  :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                  :picker-options="item.pickerOptions"
                />
                <!-- 起止时间选择框(两个框的) -->
                <template v-else-if="item.type==='date2'">
                  <el-date-picker
                    v-model="searchFormModel[item.fieldName]"
                    class="datepicker1"
                    :disabled="item.disabled"
                    :type="item.dateType || 'date'"
                    :format="item.format || 'yyyy-MM-dd'"
                    :value-format="item.valueFormat || 'yyyy-MM-dd'"
                    :picker-options="item.pickerOptionStart||pickerOptionSet(item.fieldName2,'start')"
                    placeholder="开始时间"
                  />
                  <span class="line">-</span>
                  <el-date-picker
                    v-model="searchFormModel[item.fieldName2]"
                    class="datepicker2"
                    :type="item.dateType || 'date'"
                    :disabled="item.disabled"
                    :format="item.format || 'yyyy-MM-dd'"
                    :value-format="item.valueFormat || 'yyyy-MM-dd'"
                    placeholder="结束时间"
                    :picker-options="item.pickerOptionEnd||pickerOptionSet(item.fieldName,'end')"
                  />
                </template>
                <!-- 周期选择框 -->
                <el-date-picker
                  v-else-if="item.type==='cycleDate'"
                  v-model="searchFormModel[item.fieldName]"
                  :disabled="item.disabled"
                  :type="item.dateType || 'date'"
                  placeholder="选择日期"
                  :format="item.format || 'yyyy-MM-dd'"
                  :value-format="item.valueFormat || 'yyyy-MM-dd'"
                  :clearable="item.clearable"
                  :picker-options="item.pickerOptions"
                />
                <!-- 季度选择框 -->
                <template v-else-if="item.type==='quarter'">
                  <el-quarter-picker
                    v-model="searchFormModel[item.fieldName]"
                    :disabled="item.disabled"
                    placeholder="选择季度"
                    :format="item.format || 'yyyy年第Q季度'"
                    :value-format="item.valueFormat || 'yyyy-qq'"
                    :clearable="item.clearable"
                    :picker-options="item.pickerOptions"
                  />
                </template>
                <template v-else-if="item.type==='year'">
                  <el-date-picker
                    v-model="searchFormModel[item.fieldName]"
                    :disabled="item.disabled"
                    type="year"
                    :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                    placeholder="选择年">
                  </el-date-picker>
                </template>
                <template v-else-if="item.type==='month'">
                  <el-date-picker
                    v-model="searchFormModel[item.fieldName]"
                    :disabled="item.disabled"
                    type="month"
                    :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                    placeholder="选择月">
                  </el-date-picker>
                </template>
                <template v-else-if="item.type==='monthrange'">
                  <el-date-picker
                    v-model="searchFormModel[item.fieldName]"
                    type="monthrange"
                    :disabled="item.disabled"
                    :format="item.format || 'yyyy-MM-dd'"
                    :value-format="item.valueFormat || 'yyyy-MM-dd'"
                    placeholder="结束时间"
                    :picker-options="item.pickerOptionEnd||pickerOptionSet(item.fieldName,'end')">
                  </el-date-picker>
                </template>
              </el-form-item>

            </el-col>
          </el-row>
          <!-- 可自定义按钮 -->
          <slot name="searchField"/>
        </el-form>
        <!-- <span class="search-icon el-icon el-icon-search" @click="searchHandle"></span> -->
        <el-button class="search-btn" type="primary" @click="searchHandle">查询</el-button>
      </div>
      <mssTable
        class="search-list"
        v-if="tableShow"
        ref="table"
        :autoCall="autoCall"
        :api="tableApi"
        :columns="tableColumns"
        :rowKey="rowKey"
        :staticSearchParam="tableStaticSearchParam"
        border
        selection
        :single-choice="tableSingleChoice"
        @tableDataChange="tableDataChangeHandle"
      >
      </mssTable>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmHandle">确定</el-button>
      <el-button @click="cancleHandle">取消</el-button>
  </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'TableSearchDialog',
  props: {
    labelWidth: {
      type: String,
      default: '130px'
    },
    dialogTitle: {
      type: String,
      default: '选择项目'
    },
    dialogWidth: {
      type: String,
      default: '30%'
    },
    // 是否自动调用传入api
    autoCall: {
      type: Boolean,
      default: true
    },
    // 列表的查询接口请求
    tableApi: {
      type: Function,
      default: () => {
      }
    },
    // 列表的查询参数。这个是除开分页和搜索框之外的的查询参数
    tableQueryParams: {
      type: Object,
      default() {
        return {}
      }
    },
    rowKey: {
      type: String,
      default: () => 'id'
    },
    // 查询条件的key值
    searchFields: {
      type: Array,
      default() {
        return [
          {
            label: '项目名称',
            fieldName: 'projectName'
          }
        ]
      }
    },
    searchInputPlaceholder: {
      type: String,
      default: '输入项目名称查询'
    },
    tableColumns: {
      type: Array,
      default() {
        return [
          {
            label: '项目名称',
            prop: 'projectName',
            minWidth: 200
          },
          {
            label: '项目编码',
            prop: 'projectCode',
            minWidth: 100
          },
          {
            label: '施工单位编码',
            prop: 'providerNum',
            minWidth: 100
          },
          {
            label: '建设单位名称',
            prop: 'buildUnitName',
            minWidth: 200
          }
        ]
      }
    },
    // 表格是否单选
    tableSingleChoice: {
      type: Boolean,
      default: false
    },
    // 表格中已经勾选的数据。一般用于在第一次打开表格时，需要勾选的数据
    tableSelectedData: {
      type: Array,
      default() {
        return []
      }
    },
    tableDataKey: {
      type: String,
      default: 'id'
    }
  },
  computed: {
    // tableStaticSearchParam(){
    //   return this.tableQueryParams
    // }
  },
  watch: {
    tableQueryParams(n, o) {
      this.tableStaticSearchParam = JSON.parse(JSON.stringify(n))
    }
  },
  data() {
    return {
      dialogVisible: false,
      tableShow: false,
      searchFormModel: {},
      tableStaticSearchParam: {}
    }
  },
  methods: {
    tableDataChangeHandle(tableData) {
      if (this.tableSelectedData && this.tableSelectedData.length) {
        const selectedIds = this.tableSelectedData.map(it => it[this.tableDataKey])
        const selectedRows = tableData.filter(it => {
          return selectedIds.includes(it[this.tableDataKey])
        })
        this.$nextTick(_ => {
          this.$refs.table.$refs.table.clearSelection()
          if (selectedRows && selectedRows.length) {
            this.$nextTick(_ => {
              selectedRows.forEach(row => {
                this.$refs.table.$refs.table.toggleRowSelection(row)
              })
            })
          }
        })
      }
    },
    /**
     * 显示弹窗，
     * @param opts 预留配置字段，如果有需要在打开弹窗时携带的配置项
     */
    openDialog(opts = {}) {
      this.tableStaticSearchParam = JSON.parse(JSON.stringify(this.tableQueryParams))
      this.dialogVisible = true
      this.tableShow = true
    },
    searchHandle() {
      this.$refs.searchForm.validate(v => {
        if (v) {
          this.$refs.table.page.current = 1
          const searchData = {
            ...this.searchFormModel
          }
          // this.staticSearchParam = searchData
          this.tableStaticSearchParam = Object.assign(this.tableStaticSearchParam, searchData)
          this.$refs.table.getTableData(searchData)
        }
      })
    },
    // 下拉框事件
    changeSelect(name, val) {
      this.$emit('changeSelect', name, val)
    },
    resetAllBeforeClose() {
      this.tableShow = false
      this.searchFormModel = {}
    },
    handleClose(done) {
      this.resetAllBeforeClose()
      done()
    },
    // 点击【取消】
    cancleHandle() {
      this.resetAllBeforeClose()
      this.$emit('cancle')
      this.dialogVisible = false
    },
    // 点击【确定】
    confirmHandle() {
      this.resetAllBeforeClose()
      this.$emit('confirm', this.$refs.table.multipleSelection)
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.search-form {
  overflow: hidden;

  .inner-input {
    width: 200px;
    float: left;
    margin-right: 7px;
  }

  .search-btn {
    float: right;
  }

  .search-icon {
    display: block;
    width: 26px;
    text-align: center;
    height: 26px;
    line-height: 26px;
    float: right;
    cursor: pointer;
  }
}

.search-list {
  margin-top: 10px;
}

@media only screen and (max-width: 600px) {
  .search-list {
    ::v-deep .pagination {
      .el-pagination__sizes {
        margin-right: 0;
      }

      .el-pagination__jump {
        display: none;
      }
    }
  }
}
</style>
