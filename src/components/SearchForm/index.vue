<!--
 * @Descripttion: 搜索条件（注意：采用span传入宽度，未传就是8）
 * @Author: hcm
 * @Date: 2023-06-30
-->
<template>
  <div>
    <el-card class="search-card card">
      <div slot="header" class="title">
        <div class="left-wrap">
          <i class="iconfont icon-dasuolvetuliebiao" />
          <span>{{ title }}</span>
        </div>
        <div class="right-wrap">
          <div v-show="defaultBtn" class="default-btn">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button v-if="resetFlag" @click="handleReset">重置</el-button>
          </div>
          <!-- 可自定义按钮 -->
          <slot name="customBtn" />
        </div>
      </div>
      <div class="content">
        <el-form
          v-show="isDefaultForm"
          ref="searchForm"
          class="search-form"
          :model="searchForm"
          :label-width="labelWidth"
          @submit.native.prevent
        >
          <el-row style="width: 100%;">
            <el-col
              v-for="(item,index) in searchConfig"
              :key="index"
              :span="item.span || 8"
              :offset="item.offset || 0"
            >
              <el-form-item
                :label="item.label"
                :title="item.label"
                :class="item.type"
                :prop="item.fieldName"
                :rules="item.rules"
              >
                <!-- input框 -->
                <el-input
                  v-if="item.type==='input'"
                  v-model="searchForm[item.fieldName]"
                  maxlength="100"
                  :name="item.label"
                  :disabled="item.disabled"
                  @keyup.enter.native="handleSearch"
                />
                <!-- 文本域框 -->
                <el-input
                  v-if="item.type==='textarea'"
                  v-model="searchForm[item.fieldName]"
                  type="textarea"
                  rows="1"
                  class="textarea-width"
                  :disabled="item.disabled"
                />
                <!-- 数字输入框 -->
                <el-input-number
                  v-if="item.type ==='number'"
                  v-model="searchForm[item.fieldName]"
                  :disabled="item.disabled"
                  :controls-position="item.controlsPosition || 'right'"
                />
                <!-- 弹框 -->
                <el-input
                  v-if="item.type==='dialog'"
                  v-model="searchForm[item.fieldName]"
                  readonly="readonly"
                  :name="item.label"
                  :disabled="item.disabled"
                  @click.native="openDialog(item.handleDialog)"
                />
                <!-- 复选框 -->
                <el-checkbox-group
                  v-else-if="item.type==='checkbox'"
                  v-model="searchForm[item.fieldName]"
                >
                  <template v-for="(value,index) in item.options">
                    <el-checkbox
                      :key="index"
                      :disabled="item.disabled"
                      :label="value.value"
                      :name="item.fieldName"
                    >{{ value.label }}</el-checkbox>
                  </template>
                </el-checkbox-group>
                <!-- 单选框 -->
                <el-radio-group
                  v-else-if="item.type==='radio'"
                  v-model="searchForm[item.fieldName]"
                >
                  <template v-for="(value,index) in item.options">
                    <el-radio :key="index" :label="value.value" :disabled="value.disabled">{{ value.label }}</el-radio>
                  </template>
                </el-radio-group>
                <!-- 下拉框 -->
                <el-select
                  v-else-if="item.type==='select'"
                  v-model="searchForm[item.fieldName]"
                  placeholder="--请选择--"
                  :multiple="item.selectMultiple || false"
                  :disabled="item.disabled"
                  :clearable="item.clearable"
                  :filterable="item.filterable"
                  :value-key="item.itemValue ? item.itemValue : 'value'"
                  @change="(v) => changeSelect(item.fieldName, v)"
                >
                  <template v-for="(option,index) in item.options">
                    <el-option :key="index"
                  :label="option[item.itemLabel ? item.itemLabel : 'label']"
                  :value="
                    item.itemAs
                      ? option
                      : option[item.itemValue ? item.itemValue : 'value']
                  " />
                  </template>
                </el-select>
                <el-date-picker
                  v-else-if="item.type==='date1'"
                  v-model="searchForm[item.fieldName]"
                  :disabled="item.disabled"
                  :type="item.dateType || 'date'"
                  placeholder="选择日期"
                  :format="item.format || 'yyyy-MM-dd'"
                  :value-format="item.valueFormat || 'yyyy-MM-dd'"
                />
                <!-- 起止时间选择框 -->
                <el-date-picker
                  v-else-if="item.type==='daterange'"
                  v-model="searchForm[item.fieldName]"
                  :type="item.dateType || 'daterange'"
                  range-separator="-"
                  start-placeholder="开始日期"
                  :disabled="item.disabled"
                  end-placeholder="结束日期"
                  :format="item.format || 'yyyy-MM-dd HH:mm:ss'"
                  :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                  :picker-options="item.pickerOptions"
                />
                <!-- 起止时间选择框(两个框的) -->
                <template v-else-if="item.type==='date2'">
                  <el-date-picker
                    v-model="searchForm[item.fieldName]"
                    class="datepicker1"
                    :disabled="item.disabled"
                    :type="item.dateType || 'date'"
                    :format="item.format || 'yyyy-MM-dd'"
                    :value-format="item.valueFormat || 'yyyy-MM-dd'"
                    :picker-options="item.pickerOptionStart||pickerOptionSet(item.fieldName2,'start')"
                    placeholder="开始时间"
                  />
                  <span class="line">-</span>
                  <el-date-picker
                    v-model="searchForm[item.fieldName2]"
                    class="datepicker2"
                    :type="item.dateType || 'date'"
                    :disabled="item.disabled"
                    :format="item.format || 'yyyy-MM-dd'"
                    :value-format="item.valueFormat || 'yyyy-MM-dd'"
                    placeholder="结束时间"
                    :picker-options="item.pickerOptionEnd||pickerOptionSet(item.fieldName,'end')"
                  />
                </template>
                <!-- 周期选择框 -->
                <el-date-picker
                  v-else-if="item.type==='cycleDate'"
                  v-model="searchForm[item.fieldName]"
                  :disabled="item.disabled"
                  :type="item.dateType || 'date'"
                  placeholder="选择日期"
                  :format="item.format || 'yyyy-MM-dd'"
                  :value-format="item.valueFormat || 'yyyy-MM-dd'"
                  :clearable="item.clearable"
                  :picker-options="item.pickerOptions"
                />
                <!-- 季度选择框 -->
                <template v-else-if="item.type==='quarter'">
                  <el-quarter-picker
                    v-model="searchForm[item.fieldName]"
                    :disabled="item.disabled"
                    placeholder="选择季度"
                    :format="item.format || 'yyyy年第Q季度'"
                    :value-format="item.valueFormat || 'yyyy-qq'"
                    :clearable="item.clearable"
                    :picker-options="item.pickerOptions"
                  />
                </template>
                <template v-else-if="item.type==='week'">
                  <el-date-picker :format="startDate + ' 至 ' + endDate"
                        v-model="searchForm[item.fieldName]"
                        :disabled="item.disabled"
                        :picker-options="{'firstDayOfWeek': 1}"
                        @change="newDateWeekHandle"
                        placeholder="请选择周"
                        style="width: 300px">
                  </el-date-picker>
                </template>
                <template v-else-if="item.type==='year'">
                  <el-date-picker
                    v-model="searchForm[item.fieldName]"
                    :disabled="item.disabled"
                    type="year"
                    :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                    placeholder="选择年">
                  </el-date-picker>
                </template>
                <template v-else-if="item.type==='month'">
                  <el-date-picker
                    v-model="searchForm[item.fieldName]"
                    :disabled="item.disabled"
                    type="month"
                    :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                    placeholder="选择月">
                  </el-date-picker>
                </template>
                <template v-else-if="item.type==='monthrange'">
                  <el-date-picker
                    v-model="searchForm[item.fieldName]"
                    type="monthrange"
                    :disabled="item.disabled"
                    :format="item.format || 'yyyy-MM-dd'"
                    :value-format="item.valueFormat || 'yyyy-MM-dd'"
                    placeholder="结束时间"
                    :picker-options="item.pickerOptionEnd||pickerOptionSet(item.fieldName,'end')">
                  </el-date-picker>
                </template>
              </el-form-item>

            </el-col>
          </el-row>
        </el-form>

        <!-- 可自定义表单 -->
        <slot name="customForm" />

      </div>
    </el-card>
  </div>
</template>
<script>

import ElQuarterPicker from '@/components/ElQuarterPicker/index.vue'
export default {
  name: 'Search',
  components: {ElQuarterPicker},
  props: {
    labelWidth: {
      type: String,
      default: '130px'
    },
    title: { // 默认title
      type: String,
      default: '查询条件'
    },
    defaultBtn: { // 默认按钮
      type: Boolean,
      default: true
    },
    isDefaultForm: { // 默认表单
      type: Boolean,
      default: true
    },
    searchConfig: { // 表单配置
      type: Array,
      default: () => []
    },
    btnLoading: { // 按钮的loading
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => {}
    },
    resetFlag:{
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      weeklyDate:'',
      searchForm: { ...this.form }, // 查询表单
      startDate: { // 开始时间范围限定
        disabledDate: time => {
          if (this.searchForm[endDate] && this.endDate) {
            return time.getTime() > this.endDate
          } else {
            return false
          }
        }
      },
      endDate: { // 结束时间范围限定
        disabledDate: time => {
          if (this.startDate && this.startDate) {
            return time.getTime() < this.startDate
          } else {
            return false
          }
        }
      }
    }
  },
  watch: {
    form: {
      handler(newValue, oldValue) {
        this.searchForm = Object.assign({}, this.searchForm, newValue)
      },
      deep: true
    }
  },
  mounted() {
  },
  methods: {
    pickerOptionSet(code,type){
      return {
        disabledDate: (time) => {
          if(this.searchForm[code]){
            return type=='start'?this.$moment(time).diff(this.searchForm[code], 'days') > 0:this.$moment(time).diff(this.searchForm[code], 'days')  < 0
          }
        }
      }
    },
    // 弹框
    openDialog(val) {
      this.$emit('openDialog', val)
    },
    // 重置
    handleReset() {
      this.searchConfig.forEach(v => {
        if (v.type === 'date2') {
          this.$set(this.searchForm, v.fieldName, v.value)
          this.$set(this.searchForm, v.fieldName2, v.value2)
        } else {
          this.$set(this.searchForm, v.fieldName, v.value)
        }
      })
      this.$emit('reset', this.searchForm)
    },
    // 查询
    handleSearch() {
      this.$refs.searchForm.validate(v => {
        if(v){
          this.$emit('search', this.searchForm)
        }
      })
    },
    // 下拉框事件
    changeSelect(name, val) {
      this.$emit('changeSelect', name, val)
    },
    // 周选择
    newDateWeekHandle () {
      this.weeklyDate = this.searchForm.selectDate;
      console.log(this.weeklyDate, 'weeklyDate');
      const date = new Date(this.weeklyDate);
      const now = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
      const nowTime = now.getTime();
      const day = now.getUTCDay();
      const oneDayTime = 24 * 60 * 60 * 1000;
      const mondayTime = nowTime - ((day + 6) % 7) * oneDayTime;
      // 周一0点到下周一0点
      const nextMondayTime = mondayTime + 7 * oneDayTime;
      // 周一0点到周日23:59:59
      const sundayTime = nextMondayTime - 1;
      this.startDate = this.$moment.utc(mondayTime).format('YYYY-MM-DD HH:mm:ss');
      this.endDate = this.$moment.utc(sundayTime).format('YYYY-MM-DD HH:mm:ss');
      console.log(this.startDate, 'this.startDate ');
      console.log(this.endDate, 'this.endDate ');
      // 将 startDate 和 endDate 存储在 searchForm 中
      this.searchForm.startDate = this.startDate;
      this.searchForm.endDate = this.endDate;
    },

  }
}
</script>
