<!--
 * @Descripttion: 表格组件
 * @Author: lzp
 * @Date: 2023-06-27 14:16:11
-->
<template>
  <div id="commonTable" :class="wrapperClass">
    <el-table
      ref="table"
      v-loading="tableLoading"
      class="table"
      :show-summary="showSummary"
      :row-key="rowKey"
      :height="height"
      :stripe="stripe"
      :border="border"
      :size="tableSize"
      :show-header="showHeader"
      tooltip-effect="dark"
      :row-class-name="rowClassName"
      :cell-class-name="cellClassName"
      :row-style="rowStyle"
      :data="tableData"
      :summary-method="getSummaries"
      :span-method="objectSpanMethod"
      element-loading-spinner="el-icon-loading"
      @sort-change="sortChange"
      @selection-change="selectionChange"
      @select="selectHandle"
    >
      <template slot="empty">
        <div class="tableNodata">
          <span>暂无数据</span>
        </div>
      </template>
      <el-table-column
        v-if="selection"
        type="selection"
        :selectable="selectable"
        :reserve-selection="reserveSelection"
        width="50"
        align="center"
      ></el-table-column>
      <el-table-column v-if="serial" label="序号" type="index" width="50" align="center">
        <template slot-scope="scope">
          {{
            (page.current - 1) * page.size + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <template v-for="(column, index) in tableColumn">
        <el-table-column
          v-if="column.multiseriate && column.multiseriate.length"
          :key="column.prop"
          :label="column.label || ''"
          :formatter="column.formatter"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed || false"
          :sortable="column.sortable"
          :align="column.align || 'center'"
          :show-overflow-tooltip="(column.tooltip || false) && tooltip"
          :class-name="!tooltip?'no-tooltip':''"
        >
          <el-table-column
            v-for="(ele, i) in column.multiseriate"
            :key="i"
            :prop="ele"
          ></el-table-column>
        </el-table-column>
        <ColumnItem2 v-if="column.useHtml" :key="index" :column="column" :tooltip="tooltip" :useHtml="column.useHtml"></ColumnItem2>
        <ColumnItem v-else :key="index" :column="column" :tooltip="tooltip"></ColumnItem>
      </template>
    </el-table>
    <div v-if="pagination" class="pagination">
      <el-pagination
        :current-page.sync="page.current"
        :page-sizes="[5, 10, 20, 30, 50, 100]"
        :page-size="page.size"
        background
        :layout="layout"
        :total="page.total"
        @current-change="currentChange"
        @size-change="sizeChange"
      />
    </div>
  </div>
</template>

<script>
import ColumnItem from './columnItem.vue'
import ColumnItem2 from './columnItem2.vue'
export default {
  name: 'CommonTable',
  components: {
    ColumnItem,
    ColumnItem2
  },
  props: {
    wrapperClass: {
      type: String,
      default: 'base-table'
    },
    showSummary: { // 是否显示合计
      type: Boolean,
      default: false
    },
    rowKey: {
      type: String,
      default: () => 'id'
    },
    height: {
      type: Number,
      default: null
    },
    stripe: {
      type: Boolean,
      default: true
    },
    border: {
      type: Boolean,
      default: true
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    rowClassName: {
      type: Function,
      default: null
    },
    cellClassName: {
      type: Function,
      default: null
    },
    rowStyle: {
      type: Function,
      default: null
    },
    // 表格的size属性配置medium / small / mini
    tableSize: {
      type: String,
      default: ''
    },
    // 是否展示多选框
    selection: {
      type: Boolean,
      default: false
    },
    // 多选框是否可勾选
    selectable: {
      type: Function,
      default: () => true
    },
    // 表格数据更新后是否保留之前勾选的数据
    reserveSelection: {
      type: Boolean,
      default: true
    },
    // 是否展示序号
    serial: {
      type: Boolean,
      default: true
    },
    // 是否有分页
    pagination: {
      type: Boolean,
      default: true
    },
    // 分页功能
    layout: {
      type: String,
      default: 'total,  prev, pager, next,sizes, jumper'
    },
    // 是否触发勾选事件selection
    getChange: {
      type: Boolean,
      default: false
    },
    columns: {
      type: Array,
      default: () => []
    },
    api: {
      type: Function,
      default: null
    },
    // 固定查询条件
    staticSearchParam: {
      type: Object,
      default: () => {}
    },
    dealData: {
      type: Function,
      default: null
    },
    stationary: {
      type: Array,
      default: () => null
    },
    // 是否自动调用传入api
    autoCall: {
      type: Boolean,
      default: true
    },
    // 是否单选
    singleChoice: {
      type: Boolean,
      default: false
    },
    getSummaries: { // 自定义的合计计算方法
      type: Function,
      default: () => null
    },
    objectSpanMethod: { // 合并单元格
      type: Function
    },
    // 是否隐藏全选框
    hideAllSelection: {
      type: Boolean,
      default: false
    },
    // 用户可自定义的初始的分页参数
    cuzPaginationOpt: {
      type: [Object],
      default() {
        return {};
      },
    },
    customSize:{
      type:Number,
      default:10
    }
  },
  data() {
    return {
      tableLoading: false, // 表格loading
      tableColumn: [], // 表格表头
      page: {
        total: 0,
        size: this.customSize,
        current: 1
      }, // 分页参数
      tableData: [], // 表格数据
      multipleSelection: [], // 勾选的数据
      tooltip:true
    }
  },
  watch: {
    columns: {
      handler(n, o) {
        this.tableColumn = [...n]
      },
      deep: true,
      immediate: true
    },
    stationary(n, o) {
      this.tableData = this.stationary
    },
    //监听props的cuzPaginationOpt,自定义分页用于
    cuzPaginationOpt(n, o) {
      this.page.size = this.cuzPaginationOpt.size;
      this.page.current = this.cuzPaginationOpt.current;
      this.page.total = this.cuzPaginationOpt.total;
    },
  },
  activated(){
    this.$refs.table.doLayout()
    this.tooltip = true
  },
  deactivated(){
    this.tooltip = false
  },
  created() {
    // 优先处理分页参数，因为查询表格需要用分页
    if (Object.keys(this.cuzPaginationOpt).length) {
      this.page.size = this.cuzPaginationOpt.size;
      this.page.current = this.cuzPaginationOpt.current;
      this.page.total = this.cuzPaginationOpt.total;
    }

    if (this.api) {
      this.autoCall && this.getTableData()
    } else {
      this.tableData = this.stationary || []
    }
    // 单选的给隐藏表头的全选选择框
    if (this.singleChoice || this.hideAllSelection) {
      this.$nextTick((_) => {
        const eleHeader =
          this.$refs.table.$el.getElementsByClassName('el-table__header')
        const eleCheckbox = eleHeader[0]
          .getElementsByTagName('th')[0]
          .getElementsByClassName('cell')[0]
        eleCheckbox.style.display = 'none'
      })
    }
  },
  methods: {
    /**
     * 获取表格数据,params额外参数
     * @param params
     */
    getTableData(params = {}) {
      const req = {
        ...this.staticSearchParam,
        ...params
      }
      if (this.pagination) {
        req.page = this.page.current
        req.limit = this.page.size
      }
      this.tableLoading = true
      this.api(req)
        .then((res) => {
          this.tableLoading = false
          if (res && res.code == '0000' && res.data) {
            if (res.data.data) {
              this.tableData = this.dealData
                ? this.dealData(res.data.data)
                : res.data.data
            } else if (res.data) {
              this.tableData = this.dealData
                ? this.dealData(res.data)
                : res.data
            }
            if (this.pagination) {
              this.page = {
                current: parseInt(res.data.page),
                size: parseInt(res.data.limit),
                total: parseInt(res.data.total)
              }
            }
            this.$emit('tableDataChange', this.tableData,this.page,res.data)
           this.$bus.$emit('tableDataChange', this.tableData,this.page,res.data)
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: res.msg
            })
            this.tableData = []
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    /**
     * 页码点击
     * @param current
     */
    currentChange(current) {
      this.page = {
        ...this.page,
        current
      }
      if (this.api) {
        this.getTableData()
      }
      // 执行自定义的页码改变事件
      this.$emit('pageNumChange', this.page)
    },
    /**
     * 页数大小点击
     * @param size
     */
    sizeChange(size) {
      this.page = {
        ...this.page,
        current: 1,
        size
      }
      if (this.api) {
        this.getTableData()
      }
      // 执行自定义的页码改变事件
      this.$emit('pageNumChange', this.page)
    },
    /**
     * 表格勾选
     * @param selection
     */
    selectionChange(selection) {
      this.multipleSelection = selection
      if (this.getChange) {
        this.$emit('getChange', selection)
      }
    },
    /**
     * checkbox实现单选
     * @param val
     */
    selectHandle(selection, row) {
      if (this.singleChoice && selection.length) {
        this.$nextTick(() => {
          this.$refs.table.clearSelection()
          this.$refs.table.toggleRowSelection(row)
        })
      }
      this.$emit('rowChecked', selection, row)
    },
    /**
     * 表头排序
     * @param column
     * @param prop
     * @param order
     */
    sortChange({ column, prop, order }) {
      if (this.api) {
        if (order) {
          const sortOrder = {
            ascending: 'asc',
            descending: 'desc'
          }
          const params = {
            sort: prop,
            order: sortOrder[order]
          }
          this.getTableData(params)
        } else {
          this.getTableData()
        }
      } else {
        this.$emit('sortChange', { column, prop, order })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#commonTable {
  .pagination {
    margin-top: 16px;
    text-align: right;
  }
  ::v-deep .no-tooltip {
    .cell {
      white-space: nowrap !important;
    }
  }
}

</style>
