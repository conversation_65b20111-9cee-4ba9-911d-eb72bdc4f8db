<!-- 选择部门 -->
<template>
  <div>
    <el-dialog
      :custom-class="'ChooseSpec'"
      :visible.sync="showDialog"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      :modal="true"
      append-to-body
    >
      <div class="choose" :style="{ width: '48%' }">
        <div class="chooseTitle">
          <span class="iconfont">选择专业</span>
        </div>
        <div class="chooseBody includeFoot">
          <el-input v-model="selName" placeholder="专业名称">
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="reloadTree"
            ></el-button>
          </el-input>
          <el-tree
            ref="tree"
            class="specialty-tree"
            :props="defaultProps"
            :data="treeData"
            node-key="id"
            :default-checked-keys="defKeys"
            :check-strictly="true"
            show-checkbox
            @check-change="handleCheckChange"
          >
            <span slot-scope="{ node, data }">
              <span
                class="custom-tree-node-text"
                :data-leaf="data.leaf"
                data-nodeKey="id"
              >{{ data[defaultProps.label] }}
              </span>
            </span>
          </el-tree>
        </div>
        <div class="chooseFoot">
        </div>
      </div>
      <div class="choose" :style="{ width: '48%' }">
        <div class="chooseTitle">
          <span class="iconfont">选择结果</span>
          <span class="tips">(双击名字删除)</span>
        </div>
        <div class="chooseBody">
          <el-checkbox-group v-model="delList">
            <el-checkbox
              v-for="(item, index) in checkList"
              :key="index"
              class="showList"
              :label="item.name"
            >
              <span @dblclick="delSpec(item, 'single')">
                {{
                  item.name
                }}
              </span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="chooseFoot">
          <el-checkbox v-model="checkedAll" @change="checkAll">全选</el-checkbox>
          <span class="del" @click="delSpec(delList, 'multiple')">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitOrder">确 定</el-button>
        <el-button size="mini" style="margin-left: 10px" @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getSpecialtyService } from '@/api/choose_specialty'
export default {
  name: 'ChooseSpecialty',
  props: {
    // 是否多选
    multSelect: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      title: '选择专业',
      checkList: [],
      delList: [],
      defKeys: [],
      checkedAll: false, // 是否全选
      defaultProps: {
        // 懒加载树状下拉框
        label: 'name',
        children: 'children',
        isLeaf: 'leaf'
      },
      showDialog: false,
      selName: '',
      treeData: []
    }
  },
  methods: {
    /**
     * 弹框初始化 selectList选择的部门数据
     * @param selectList
     */
    init(selectList) {
      this.checkList = selectList
      this.treeCheck()
      this.showDialog = true
      this.selName = ''
      this.getTreeData()
    },

    /**
     * 提交表单
     */
    submitOrder() {
      if (!this.multSelect && this.checkList.length > 1) {
        this.$message({ type: 'warning', message: '只能选择一个专业' })
        return
      }
      this.$emit('showCheckList', {
        checkList: this.checkList
      })
      this.defKeys = []
      this.showDialog = false
    },
    getTreeData() {
      const params = {
        name: this.selName
      }
      getSpecialtyService(params).then(res => {
        if (res.code === '0000') {
          this.treeData = res.data
          this.$nextTick(() => {
            this.hideTreeCheckbox()
          })
        }
      })
    },
    // 隐藏树节点上的checkbox，仅当节点的leaf为真时，可以勾选,如果值为all，表示所有节点都可以选
    hideTreeCheckbox() {
      try {
        const eleTree = this.$refs.tree.$el
        const nodes = eleTree.getElementsByClassName('custom-tree-node-text')
        for (let i = 0, len = nodes.length; i < len; i++) {
          const eleNode = nodes[i]
          const dataLeaf = eleNode.dataset.leaf
          eleNode.parentNode.previousElementSibling.style.display = dataLeaf? 'block' : 'none'
        }
      } catch (e) {
        console.error(e)
      }
    },
    // 树搜索事件
    reloadTree() {
      this.getTreeData()
    },
    handleCheckChange(data, check) {
      if (check) {
        if (this.multSelect) {
          let flag = false
          this.checkList.forEach((item) => {
            if (item.id === data.id) {
              flag = true
            }
          })
          !flag && this.checkList.push(data)
        } else {
          // 单选
          this.$refs.tree.setCheckedNodes([data])
          this.checkList.push(data)
        }
      } else {
        // 删除取消选择的数据
        for (let index = 0; index < this.checkList.length; index++) {
          const element = this.checkList[index]
          if (element.id === data.id) {
            this.checkList.splice(index, 1)
            break
          }
        }
      }
    },
    /**
     * 删除事件
     * @param val
     * @param type
     */
    delSpec(val, type) {
      this.defKeys = [] // 树回显数据
      // 单个删除
      if (type === 'single') {
        this.checkList.splice(this.checkList.indexOf(val), 1)
      } else if (type === 'multiple') {
        for (var j = 0; j < val.length; j++) {
          for (var i = 0; i < this.checkList.length; i++) {
            if (this.checkList[i].name === val[j]) {
              this.checkList.splice(i, 1)
              i -= 1
            }
          }
        }
      }
      this.checkedAll = false // 重置全选
      this.delList = []
      this.treeCheck()
    },

    /**
     * 树回显勾选事件
     */
    treeCheck() {
      this.defKeys = []
      this.checkList.forEach((item) => {
        this.defKeys.push(item.id)
      })
      this.defKeys.concat()
      this.$nextTick(function() {
        this.$refs.tree.setCheckedKeys(this.defKeys)
      })
    },

    /**
     * 全选事件
     * @param val
     */
    checkAll(val) {
      if (val) {
        this.checkList.forEach((item) => {
          this.delList.push(item.name)
        })
      } else {
        this.delList = []
      }
    },
    closeDialog() {
      this.showDialog = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .ChooseSpec{
  .el-dialog__body{
    display: flex;
    justify-content: space-between;
  }
}
::v-deep .choose {
  .iconfont {
    color: #5887ff;
    font-weight: 400;
    margin-right: 0.5rem;
  }
  .chooseTitle {
    height: 20px;
    line-height: 20px;
    color: #000;
    font-weight: bold;
    padding-left: 0.5rem;
    .iconfont {
      font-size: 13px;
    }
    .tips {
      font-size: 12px;
      font-weight: normal;
      color: #ff5a09;
      margin-left: 0.5rem;
    }
  }
  .chooseBody {
    width: 100%;
    height: 23rem;
    background: #ffffff;
    border: 1px solid #dddddd;
    margin-top: 0.7rem;
    padding: 0.2rem;
    overflow: auto;
    .showList {
      display: block;
      padding: 0.2rem 1rem;

      &:hover {
        background: #ebf1ff;
      }
    }
  }
  .chooseFoot {
    height: 2rem;
    line-height: 2rem;
    width: 100%;
    background: #f9fbff;
    border: 1px solid #dddddd;
    border-top: none;
    padding: 0 1rem;
    .el-pagination {
      padding: 0;
      .el-pager {
        height: 22px;
      }
    }
    .el-checkbox__label {
      font-size: 0.6rem;
    }
  }
  .del {
    float: right;
    cursor: pointer;
    font-size: 1.4rem;
    color: #5887ff;
  }
}
</style>
