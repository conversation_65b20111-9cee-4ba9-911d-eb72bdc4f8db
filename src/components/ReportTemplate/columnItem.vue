<template>
  <el-table-column
    v-if="!column.hidden"
    :prop="column.prop || ''"
    :label="column.label || ''"
    :formatter="column.formatter"
    :width="column.width"
    :min-width="column.minWidth"
    :fixed="column.fixed || false"
    :sortable="column.sortable"
    :align="column.align || 'center'"
    :show-overflow-tooltip="column.tooltip || false"
  >
    <template v-if="column.multilevelColumn && column.multilevelColumn.length">
      <columnItem
        v-for="(columnItem, index) in column.multilevelColumn"
        :key="index"
        :tableData="tableData"
        :column="columnItem"
        :isView="isView"
        :tableCode="tableCode"
        :templateForm="templateForm"
        :isHis="isHis"
        :editStatus="editStatus"
      />
    </template>
    <template slot-scope="scope">
      <el-form-item :prop='column.prop+scope.$index' :label="column.label + scope.$index">
      <div class="file" v-if="column.colType == 'image'">
        <el-upload
          v-if="!isView"
          class="fileUpload"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          accept=".png, .jpg"
          :http-request="(params)=>{uploadNewFile(params,scope,'image')}"
        >
          <el-button type="primary">点击上传</el-button>
        </el-upload>
        <div v-if="fileList[scope.row[column.prop]] && fileList[scope.row[column.prop]].length">
          <div
            class="fileList"
            v-for="(fileItem, fileIndex) in fileList[scope.row[column.prop]]"
            :key="fileIndex"
          >
            <el-image
              style="margin-bottom:5px"
              :src="fileItem.url"
              :preview-src-list="[fileItem.url]"
              :fit="'fill'"
            ></el-image>
            <span
              v-if="!isView"
              @click.stop="delBtn(fileItem,scope,'图片')"
              title="删除图片"
              class="del-file"
            >删除</span>
          </div>
        </div>
      </div>
      <div class="file" v-else-if="column.colType == 'file'">
        <el-upload
          v-if="!isView"
          class="fileUpload"
          action="string"
          ref="newFile"
          :show-file-list="false"
          :auto-upload="true"
          :http-request="(params)=>{uploadNewFile(params,scope)}"
        >
          <el-button type="primary">点击上传</el-button>
        </el-upload>
        <div v-if="fileList[scope.row[column.prop]] && fileList[scope.row[column.prop]].length">
          <div
            class="fileList"
            v-for="(fileItem, fileIndex) in fileList[scope.row[column.prop]]"
            :key="fileIndex"
          >
            <a :href="getDownURL(fileItem)" download title="下载">{{fileItem.name}}</a>
            <span
              v-if="!isView"
              @click.stop="delBtn(fileItem,scope,'附件')"
              title="删除附件"
              class="del-file"
            >删除</span>
          </div>
        </div>
      </div>
      <div
        v-else-if="column.prop == 'finishinfo' && scope.$index == 14 && scope.row.isdeduction == '是' && !isView"
      >
        <el-input
          class="finishinfo"
          @click.native="openProjectDeduction(scope.row)"
          readonly
          :title="scope.row[column.prop]"
          v-model="scope.row[column.prop]"
        ></el-input>
        <i class="el-icon-delete" title="清空" @click="delHandel(scope.row)"></i>
        <!-- 选择项目的弹窗   -->
        <mssTableSearchDialog
          ref="projectNameSearchDialog"
          :dialog-width="dialogWidth"
          :table-api="tableApi"
          :table-query-params="tableQueryParams"
          :search-fields="searchFieldList"
          :table-single-choice="false"
          :table-selected-data="tableSelectedData"
          :table-data-key="tableDataKey"
          :table-columns="tableColumns"
          @confirm="projectConfirm"
        ></mssTableSearchDialog>
      </div>
      <div v-else-if="column.colType == 'input' && !isView">
        <el-input
          v-if="column.dataType == 'number'"
          placeholder="请输入数字"
          v-model="scope.row[column.prop]"
          @input="numChange(scope.row)"
          @change="(val)=>{inputChage(column.prop,scope.$index,val)}"
        ></el-input>
        <el-input v-else v-model="scope.row[column.prop]" @change="(val)=>{inputChage(column.prop,scope.$index,val)}"></el-input>
      </div>
      <div v-else-if="column.colType == 'select' && !isView">
        <el-select v-model="scope.row[column.prop]">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div v-else-if="column.colType == 'project' && !isView">
        <el-input
          @click.native="openProjectSelectDialog(scope.row,scope.$index)"
          readonly
          v-model="scope.row[column.prop]"
        ></el-input>
        <!-- 选择项目的弹窗   -->
        <mssTableSearchDialog
          ref="projectNameSearchDialog"
          :dialog-width="dialogWidth"
          :table-api="tableApi"
          :table-query-params="tableQueryParams"
          :search-fields="searchFieldList"
          :table-single-choice="true"
          :table-selected-data="tableSelectedData"
          :table-data-key="tableDataKey"
          :table-columns="tableColumns"
          @confirm="projectNameSearchConfirmHandle"
        ></mssTableSearchDialog>
      </div>
      <div v-else-if="column.colType == 'deduction' && !isView && scope.$index == 14">
        <el-select v-model="scope.row[column.prop]" @change="delHandel(scope.row)">
          <el-option
            v-for="item in deductionOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div v-else>
        <el-input
          v-if="editStatus && column.colType == 'readOnly'"
          class="editStatus"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 3 }"
          resize="none"
          v-model="scope.row[column.prop]">
        </el-input>
        <span v-else class="text">{{ scope.row[column.prop] }}</span>
      </div>
    </el-form-item>
    </template>
  </el-table-column>
</template>
<script>
import {
  getFilesDownloadService,
  delFiles,
  getFilesService,
  uploadFiles
} from '@/api/attachment'
import { findPageByMonthReportTemplateService } from '@/api/report_template_api'
export default {
  name: 'columnItem',
  props: {
    column: {
      type: Object,
      default: () => {}
    },
    tableData: {
      type: Array,
      default: () => []
    },
    isView: {
      type: Boolean,
      default: false
    },
    isHis: {
      type: Boolean,
      default: false
    },
    tableCode: {
      type: String,
      default: ''
    },
    templateForm:{
      type:Object,
      default:()=>{}
    },
    editStatus:{
      type:Boolean,
      default:false
    }
  },
  watch: {
    tableData: {
      handler(n, o) {
        if (n && n.length) {
          if (this.column.colType == 'file' || this.column.colType == 'image') {
            this.tableData.forEach((item, index) => {
              if (item[this.column.prop]) {
                this.getNewFileList(item[this.column.prop],this.column.prop,index)
              }
            })
          }
        }
      }
    }
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return '96%'
      }else{
        return '85%'
      }
    }
  },
  data() {
    return {
      fileList: {},
      options: [],
      boId: this.$route.query.boId,
      tableApi: findPageByMonthReportTemplateService,
      tableQueryParams: {},
      searchFieldList: [
        {
          label: '项目名称',
          fieldName: 'projectName'
        },
        {
          label: '项目编码',
          fieldName: 'projectCode'
        }
      ],
      tableSelectedData: [],
      tableDataKey: 'id',
      tableColumns: [
        {
          label: '项目名称',
          prop: 'projectName',
          tooltip:true
        },
        {
          label: '项目编码',
          prop: 'projectCode',
          tooltip:true,
          width:160
        },
        {
          label: '专业',
          prop: 'specialtyName',
          tooltip:true
        },
        {
          label: '施工单位',
          prop: 'constructOrgName',
          tooltip:true
        },
        {
          label: '设计单位',
          prop: 'designOrgName',
          tooltip:true
        },
        {
          label: '监理单位',
          prop: 'supervisorOrgName',
          tooltip:true
        },
        {
          label: '开工时间',
          prop: 'actualStartDate',
          tooltip:true,
          width:140
        },
        {
          label: '完工时间',
          prop: 'actualEndDate',
          tooltip:true,
          width:140
        }
      ],
      curRow: {},
      deductionOption: [
        {
          label: '是',
          value: '是'
        },
        {
          label: '否',
          value: '否'
        }
      ],
      projectId: {},
      numRules:{}
    }
  },
  created() {},
  methods: {
    uploadNewFile(params, scope, type) {
      if (type == 'image') {
        let fileName = params.file.name
        let pos = fileName.lastIndexOf('.')
        let lastName = fileName.substring(pos, fileName.length)
        let upLoadFileType = ['.jpg', '.png']
        if (!upLoadFileType.includes(lastName)) {
          this.$message({
            showClose: true,
            message: `只能上传jgp,png图片类型格式`,
            type: 'warning'
          })
          return false
        }
      }
      if (!scope.row[this.column.prop]) {
        scope.row[this.column.prop] =
          new Date().getTime() + Math.floor(Math.random() * 10)
      }
      let param = new FormData()
      param.append('files', params.file)
      param.append('businessType', this.column.prop.toLowerCase())
      param.append('boId', scope.row[this.column.prop])
      param.append('access_token', sessionStorage.getItem('access_token'))
      uploadFiles(param)
        .then((res) => {
          if (res && res.data && res.code == '0000') {
            // 上传成功
            this.$message({
              showClose: true,
              message: '上传成功',
              type: 'success'
            })
            this.getNewFileList(scope.row[this.column.prop],this.column.prop,scope.$index)
          }
        })
        .catch(() => {
          this.$message({
            showClose: true,
            message: '上传失败',
            type: 'warning'
          })
        })
    },
    getNewFileList(id,prop,index) {
      getFilesService(id).then((res) => {
        if (res && res.code == '0000') {
          let _fileList = res.data || []
          _fileList.forEach(it=>{
            if(it.isImg){
              it.url = window.g.baseUrl + `/minio/senondownndrp/epms-jx/${it.path}`
              console.log(it.url)
            }
          })
          this.$set(this.fileList, id, _fileList)
          if(this.templateForm && this.templateForm[prop+index] !== undefined){
            if(_fileList.length){
              this.$set(this.templateForm,prop+index,id.toString())
            }else{
              this.$set(this.templateForm,prop+index,"")
            }
          }
        }
      })
    },
    getDownURL(row) {
      let downUrl =
        getFilesDownloadService() +
        row.id +
        '?access_token=' +
        sessionStorage.getItem('access_token')
      return downUrl
    },
    delBtn(row, scope, type) {
      this.$confirm(`确定删除选中${type}？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'inquiry',
        iconClass: 'el-icon-question',
        modal: false,
        center: false,
        subMessage: '' // 小消息，会以另一种样式显示
      }).then(() => {
        let req = {
          ids: row.id
        }
        delFiles(req).then((res) => {
          if (res && res.code == '0000') {
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            })
            this.getNewFileList(scope.row[this.column.prop],this.column.prop,scope.$index)
          }
        })
      })
    },
    openProjectSelectDialog(row,index) {
      if (row[this.column.prop]) {
        let req = {
          projectName: row[this.column.prop],
          id: this.projectId[row[this.column.prop]]
        }
        this.tableSelectedData = [req]
      } else {
        this.tableSelectedData = []
      }
      this.curRow = row
      this.$index = index
      this.tableQueryParams = {}
      this.$nextTick((_) => {
        this.$refs.projectNameSearchDialog.openDialog()
      })
    },
    openProjectDeduction(row) {
      this.tableSelectedData = []
      this.curRow = row
      this.tableQueryParams = {}
      this.$nextTick((_) => {
        this.$refs.projectNameSearchDialog.openDialog()
      })
    },
    projectNameSearchConfirmHandle(data) {
      let curData = {}
      if (data && data[0]) {
        curData = data[0]
      }
      this.projectId[curData.projectName] = curData.id
      this.curRow[this.column.prop] = curData.projectName || ''
      this.$set(this.templateForm,this.column.prop+this.$index,this.curRow[this.column.prop])
      this.curRow.special = curData.specialtyName || ''
      this.curRow.documentsconstruction = curData.constructOrgName || ''
      if (this.tableCode == 'm_t_safetyofficer_issuesbuild') {
        this.curRow.projectstarttime = curData.actualStartDate || ''
        this.curRow.designunitname = curData.designOrgName || ''
        this.curRow.supervisionunitname = curData.supervisorOrgName || ''
      } else if (this.tableCode == 'm_t_safetyofficer_issuescom') {
        this.curRow.projectcompletetime = curData.actualEndDate || ''
      }
    },
    projectConfirm(data) {
      let finishinfo = []
      if (this.curRow.finishinfo) {
        finishinfo = this.curRow.finishinfo.split(',')
      }
      if (data && data.length) {
        data.forEach((item) => {
          if (!finishinfo.includes(item.projectName)) {
            finishinfo.push(item.projectName)
          }
        })
      }
      this.curRow.finishinfo = finishinfo.join()
      this.$set(this.templateForm,'finishinfo14',this.curRow.finishinfo)
    },
    delHandel(row) {
      row.finishinfo = ''
    },
    numChange(row) {
      if (row[this.column.prop] && isNaN(Number(row[this.column.prop]))) {
        row[this.column.prop] = this.numRules[this.column.prop] || ''
      }else{
        this.numRules[this.column.prop] = row[this.column.prop]
      }
    },
    inputChage(prop,index,val){
      if(this.templateForm && this.templateForm[prop+index] !== undefined){
        this.$set(this.templateForm,prop+index,val)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.file {
  text-align: left;
  .fileUpload {
    display: inline-block;
    margin-bottom: 5px;
    margin-left: 8px;
  }
  .fileList {
    display: flex;
    justify-content: space-between;
  }
  .del-file {
    color: red;
    cursor: pointer;
    flex-shrink: 0;
    margin-left: 5px;
    &:hover {
      font-weight: bold;
    }
  }
}
.finishinfo {
  width: 80% !important;
  margin-right: 2px;
  margin-left: 5px;
}
.el-icon-delete {
  color: red;
  cursor: pointer;
}
.text {
  min-height: 23px;
  display: inline-block;
}
::v-deep .el-dialog__body{
  .el-input__inner{
    border-color: #D9D9D9 !important;
  }
}
.editStatus{
  width: 95%;
}
</style>

