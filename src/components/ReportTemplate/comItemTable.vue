<template>
  <div>
    <template v-if="templateCode=='safetyfinal'">
      <el-form
        class="safetyfinal"
        ref="form"
        :model="safetyfinalForm"
        :rules="safetyfinalRules"
        label-width="80px"
        label-position="top"
        :show-message="false"
        :validate-on-rule-change="false"
        :disabled="isView"
      >
        <el-form-item label=" " label-width="0" prop="firstphase">
          <el-input
            type="textarea"
            v-model="safetyfinalForm.firstphase"
            placeholder="请输入"
            :rows="3"
            resize="none"
          ></el-input>
        </el-form-item>
        <el-form-item :label="''" class="filed-type">
          <el-upload
            v-if="!isView"
            class="fileUpload"
            action="string"
            ref="newFile"
            :show-file-list="false"
            :auto-upload="true"
            accept=".png, .jpg"
            :http-request="(params) =>{uploadNewFile(params,'firstphasefileid')}"
          >
            <el-button type="primary">图片上传</el-button>
          </el-upload>
          <div class="fileList" v-if="firstphasefileid && firstphasefileid.length">
            <div
              class="fileItme"
              v-for="(fileItem, fileIndex) in firstphasefileid"
              :key="fileIndex"
            >
              <el-image :src="fileItem.url" :preview-src-list="[fileItem.url]" :fit="'fill'"></el-image>
              <span
                v-if="!isView"
                @click.stop="delBtn(fileItem,'firstphasefileid')"
                title="删除图片"
                class="del-file"
              >删除</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="一、风险防范情况（隐患排查整改）" prop="riskinfo">
          <el-input
            type="textarea"
            v-model="safetyfinalForm.riskinfo"
            placeholder="请输入"
            :rows="3"
            resize="none"
          ></el-input>
        </el-form-item>
        <el-form-item :label="''" class="filed-type">
          <el-upload
            v-if="!isView"
            class="fileUpload"
            action="string"
            ref="newFile"
            :show-file-list="false"
            :auto-upload="true"
            accept=".png, .jpg"
            :http-request="(params) =>{uploadNewFile(params,'riskinfofileid')}"
          >
            <el-button type="primary">图片上传</el-button>
          </el-upload>
          <div class="fileList" v-if="riskinfofileid && riskinfofileid.length">
            <div
              class="fileItme"
              v-for="(fileItem, fileIndex) in riskinfofileid"
              :key="fileIndex"
            >
              <el-image :src="fileItem.url" :preview-src-list="[fileItem.url]" :fit="'fill'"></el-image>
              <span
                v-if="!isView"
                @click.stop="delBtn(fileItem,'riskinfofileid')"
                title="删除图片"
                class="del-file"
              >删除</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="（一）检查情况" class="md-title" prop="jcqk">
          <el-input
            v-if="boType=='report'"
            type="textarea"
            v-model="safetyfinalForm.jcqk"
            placeholder="系统填写"
            disabled
            :rows="3"
            resize="none"
          ></el-input>
          <template v-else>
            <div>以下是{{curMonth}}月工程建设领域安全隐患排查情况：</div>
            <mssTable
              wrapperClass="projects-table"
              ref="table"
              :columns="columns"
              :pagination="false"
              :serial="false"
              :stationary="projects"
            ></mssTable>
          </template>
        </el-form-item>
        <el-form-item label="（二）整改情况" class="md-title" prop="zgqk">
          <el-input
            v-if="boType=='report'"
            type="textarea"
            v-model="safetyfinalForm.zgqk"
            placeholder="系统填写"
            disabled
            :rows="3"
            resize="none"
          ></el-input>
          <template v-else>
            <template v-if="projects.length">
              <div class="dealProjects" v-for="(item,i) in projects" :key="i">
                <div class="item">{{ item.text }}</div>
                <div class="item">
                  <span style="vertical-align:top">图片：</span>
                  <el-image
                    v-for="ele in item.afterphotoList"
                    class="dealCheckImage"
                    :key="ele.id"
                    :src="ele.url"
                    :preview-src-list="[ele.url]"
                    :fit="'fill'"
                  ></el-image>
                  <el-image
                    v-for="ele in item.beforephotoList"
                    class="dealCheckImage"
                    :key="ele.id"
                    :src="ele.url"
                    :preview-src-list="[ele.url]"
                    :fit="'fill'"
                  ></el-image>
                </div>
              </div>
            </template>
            <div v-else>无</div>
          </template>
        </el-form-item>
        <el-form-item label="（三）考核处罚" class="md-title" prop="khcf">
          <el-input
            v-if="boType=='report'"
            type="textarea"
            v-model="safetyfinalForm.khcf"
            placeholder="系统填写"
            disabled
            :rows="3"
            resize="none"
          ></el-input>
          <template v-else>
            <template v-if="punishs.length">
              <div class="dealProjects" v-for="(item,i) in punishs" :key="i">
                <div class="item">{{ item.text }}</div>
                <div class="item">
                  <!-- <span style="vertical-align:top">图片：</span> -->
                  <el-image
                    v-for="ele in item.photoList"
                    class="dealCheckImage"
                    :key="ele.id"
                    :src="ele.url"
                    :preview-src-list="[ele.url]"
                    :fit="'fill'"
                  ></el-image>
                </div>
              </div>
            </template>
            <div v-else>无</div>
          </template>
        </el-form-item>
        <el-form-item label="二、制度落实情况" prop="zdlsqk">
          <el-input
            v-if="boType=='report'"
            type="textarea"
            v-model="safetyfinalForm.zdlsqk"
            placeholder="系统填写"
            disabled
            :rows="3"
            resize="none"
          ></el-input>
          <template v-else>
            <template v-if="orders.length">
              <div class="dealProjects" v-for="(item,i) in orders" :key="i">
                <div class="item">{{ item.text }}</div>
                <div class="item">
                  <!-- <span style="vertical-align:top">图片：</span> -->
                  <el-image
                    v-for="ele in item.photoList"
                    class="dealCheckImage"
                    :key="ele.id"
                    :src="ele.url"
                    :preview-src-list="[ele.url]"
                    :fit="'fill'"
                  ></el-image>
                </div>
              </div>
            </template>
            <div v-else>无</div>
          </template>
        </el-form-item>
        <el-form-item label="三、安全责任压实"></el-form-item>
        <el-form-item label="（一）内部责任清单落实情况" class="md-title" prop="indutyinfo">
          <el-input
            type="textarea"
            v-model="safetyfinalForm.indutyinfo"
            placeholder="请输入"
            :rows="3"
            resize="none"
          ></el-input>
        </el-form-item>
        <el-form-item :label="''" class="filed-type">
          <el-upload
            v-if="!isView"
            class="fileUpload"
            action="string"
            ref="newFile"
            :show-file-list="false"
            :auto-upload="true"
            accept=".png, .jpg"
            :http-request="(params) =>{uploadNewFile(params,'indutyinfofileid')}"
          >
            <el-button type="primary">图片上传</el-button>
          </el-upload>
          <div class="fileList" v-if="indutyinfofileid && indutyinfofileid.length">
            <div
              class="fileItme"
              v-for="(fileItem, fileIndex) in indutyinfofileid"
              :key="fileIndex"
            >
              <el-image :src="fileItem.url" :preview-src-list="[fileItem.url]" :fit="'fill'"></el-image>
              <span
                v-if="!isView"
                @click.stop="delBtn(fileItem,'indutyinfofileid')"
                title="删除图片"
                class="del-file"
              >删除</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="（二）参建单位责任清单落实情况" class="md-title" prop="orgdutyinfo">
          <el-input
            type="textarea"
            v-model="safetyfinalForm.orgdutyinfo"
            placeholder="请输入"
            :rows="3"
            resize="none"
          ></el-input>
        </el-form-item>
        <el-form-item :label="''" class="filed-type">
          <el-upload
            v-if="!isView"
            class="fileUpload"
            action="string"
            ref="newFile"
            :show-file-list="false"
            :auto-upload="true"
            accept=".png, .jpg"
            :http-request="(params) =>{uploadNewFile(params,'orgdutyinfofileid')}"
          >
            <el-button type="primary">图片上传</el-button>
          </el-upload>
          <div class="fileList" v-if="orgdutyinfofileid && orgdutyinfofileid.length">
            <div
              class="fileItme"
              v-for="(fileItem, fileIndex) in orgdutyinfofileid"
              :key="fileIndex"
            >
              <el-image :src="fileItem.url" :preview-src-list="[fileItem.url]" :fit="'fill'"></el-image>
              <span
                v-if="!isView"
                @click.stop="delBtn(fileItem,'orgdutyinfofileid')"
                title="删除图片"
                class="del-file"
              >删除</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="四、安全标准动作"></el-form-item>
        <el-form-item label="（一）培训" class="md-title" prop="px">
          <el-input
            v-if="boType=='report'"
            type="textarea"
            v-model="safetyfinalForm.px"
            placeholder="系统填写"
            :rows="3"
            disabled
            resize="none"
          ></el-input>
          <template v-else>
            <template v-if="trains.length">
              <div class="dealProjects" v-for="(item,i) in trains" :key="i">
                <div class="item">{{ item.text }}</div>
                <div class="item">
                  <span style="vertical-align:top">图片：</span>
                  <el-image
                    v-for="ele in item.photoList"
                    class="dealCheckImage"
                    :key="ele.id"
                    :src="ele.url"
                    :preview-src-list="[ele.url]"
                    :fit="'fill'"
                  ></el-image>
                </div>
              </div>
            </template>
            <div v-else>无</div>
          </template>
        </el-form-item>
        <el-form-item label="（二）应急演练" class="md-title" prop="yjyl">
          <el-input
            v-if="boType=='report'"
            type="textarea"
            v-model="safetyfinalForm.yjyl"
            placeholder="系统填写"
            :rows="3"
            disabled
            resize="none"
          ></el-input>
          <template v-else>
            <template v-if="emergencys.length">
              <div class="dealProjects" v-for="(item,i) in emergencys" :key="i">
                <div class="item">{{ item.text }}</div>
                <div class="item">
                  <span style="vertical-align:top">图片：</span>
                  <el-image
                    v-for="ele in item.photoList"
                    class="dealCheckImage"
                    :key="ele.id"
                    :src="ele.url"
                    :preview-src-list="[ele.url]"
                    :fit="'fill'"
                  ></el-image>
                </div>
              </div>
            </template>
            <div v-else>无</div>
          </template>
        </el-form-item>
        <el-form-item label="五、安全专项行动" prop="safetyaction">
          <el-input
            type="textarea"
            v-model="safetyfinalForm.safetyaction"
            placeholder="请输入"
            :rows="3"
            resize="none"
          ></el-input>
        </el-form-item>
        <el-form-item :label="''" class="filed-type">
          <el-upload
            v-if="!isView"
            class="fileUpload"
            action="string"
            ref="newFile"
            :show-file-list="false"
            :auto-upload="true"
            accept=".png, .jpg"
            :http-request="(params) =>{uploadNewFile(params,'safetyactionfileid')}"
          >
            <el-button type="primary">图片上传</el-button>
          </el-upload>
          <div class="fileList" v-if="safetyactionfileid && safetyactionfileid.length">
            <div
              class="fileItme"
              v-for="(fileItem, fileIndex) in safetyactionfileid"
              :key="fileIndex"
            >
              <el-image :src="fileItem.url" :preview-src-list="[fileItem.url]" :fit="'fill'"></el-image>
              <span
                v-if="!isView"
                @click.stop="delBtn(fileItem,'safetyactionfileid')"
                title="删除图片"
                class="del-file"
              >删除</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="六、工作亮点" prop="highlights">
          <el-input
            type="textarea"
            v-model="safetyfinalForm.highlights"
            placeholder="请输入"
            :rows="3"
            resize="none"
          ></el-input>
        </el-form-item>
        <el-form-item :label="''" class="filed-type">
          <el-upload
            v-if="!isView"
            class="fileUpload"
            action="string"
            ref="newFile"
            :show-file-list="false"
            :auto-upload="true"
            accept=".png, .jpg"
            :http-request="(params) =>{uploadNewFile(params,'highlightsfileid')}"
          >
            <el-button type="primary">图片上传</el-button>
          </el-upload>
          <div class="fileList" v-if="highlightsfileid && highlightsfileid.length">
            <div
              class="fileItme"
              v-for="(fileItem, fileIndex) in highlightsfileid"
              :key="fileIndex"
            >
              <el-image :src="fileItem.url" :preview-src-list="[fileItem.url]" :fit="'fill'"></el-image>
              <span
                v-if="!isView"
                @click.stop="delBtn(fileItem,'highlightsfileid')"
                title="删除图片"
                class="del-file"
              >删除</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="七、下一步安全工作计划" prop="plan">
          <el-input
            type="textarea"
            v-model="safetyfinalForm.plan"
            placeholder="请输入"
            :rows="3"
            resize="none"
          ></el-input>
        </el-form-item>
        <el-form-item :label="''" class="filed-type">
          <el-upload
            v-if="!isView"
            class="fileUpload"
            action="string"
            ref="newFile"
            :show-file-list="false"
            :auto-upload="true"
            accept=".png, .jpg"
            :http-request="(params) =>{uploadNewFile(params,'fileid')}"
          >
            <el-button type="primary">图片上传</el-button>
          </el-upload>
          <div class="fileList" v-if="fileid && fileid.length">
            <div class="fileItme" v-for="(fileItem, fileIndex) in fileid" :key="fileIndex">
              <el-image :src="fileItem.url" :preview-src-list="[fileItem.url]" :fit="'fill'"></el-image>
              <span
                v-if="!isView"
                @click.stop="delBtn(fileItem,'fileid')"
                title="删除图片"
                class="del-file"
              >删除</span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </template>
    <template v-else>
      <div class="add-row-btn">
        <el-button v-if="!isView" type="primary" @click="save">保存</el-button>
        <el-button v-if="editTemFlag" type="primary" @click="edit">编辑</el-button>
        <el-button v-if="editStatus" type="primary" @click="saveTem">保存</el-button>
        <el-button v-if="editStatus" @click="cancel">取消</el-button>
        <el-button v-if="tableType==2&&!isView" type="primary" @click="addRow">添加行</el-button>
        <el-button v-if="tableType==2&&!isView" @click="delRow">删除</el-button>
      </div>
      <el-form
        :ref="tableCode"
        class="templateForm"
        :model="templateForm"
        :rules="!isView ? templateRules : null"
        :show-message="false"
        :validate-on-rule-change="false"
        label-position="left"
      >
        <el-table
          class="table"
          ref="table"
          border
          tooltip-effect="dark"
          :data="tableData"
          :span-method="toMergeColumn"
          v-loading="tableLoading"
          @selection-change="selectionChange"
          element-loading-spinner="el-icon-loading"
        >
          <template slot="empty">
            <div class="tableNodata">
              <span>暂无数据</span>
            </div>
          </template>
          <el-table-column
            v-if="tableType==2&&!isView"
            type="selection"
            :reserve-selection="false"
            width="50"
            align="center"
          ></el-table-column>
          <ColumnItem
            v-for="(column, index) in tableColumn"
            :key="index"
            :tableData="tableData"
            :column="column"
            :tableCode="tableCode"
            :isView="isView"
            :templateForm="templateForm"
            :isHis="isHis"
            :editStatus="editStatus"
          ></ColumnItem>
        </el-table>
      </el-form>
    </template>
  </div>
</template>

<script>
import ColumnItem from './columnItem.vue'
import { delFiles, getFilesService, uploadFiles } from '@/api/attachment'
import { getSafetyFinalDtoService } from '@/api/monthly_report/summary_work_order_api.js'
import {
  getDataService,
  saveDataService,
  getHisDataService,
  getTemplateDataService,
  deleteDataService,
  saveTemplateDataService
} from '@/api/report_template_api'
import {generateUUID} from "@/utils";
export default {
  name: 'tableItem',
  components: { ColumnItem },
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    templateCode: {
      type: String,
      default: ''
    },
    boId: {
      type: String,
      default: ''
    },
    isView: {
      type: Boolean,
      default: false
    },
    isHis: {
      type: Boolean,
      default: false
    },
    isTemplate: {
      type: Boolean,
      default: false
    },
    yearMonth: {
      type: String,
      default: ''
    },
    reporterId: {
      type: String,
      default: ''
    },
    cityName: {
      type: String,
      default: ''
    },
    cityId: {
      type: String,
      default: ''
    },
    boType: {
      type: String,
      default: 'report'
    },
    reportTemplageTab:{
      type:Object,
      default:null
    },
    singleTable:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      tableLoading: false, //表格loading
      tableColumn: [], //表格表头
      tableData: [], //表格数据
      tableCode: '',
      tableName: '',
      mergeColumn: [],
      row: {},
      tableInitData: {},
      noReadOnly: [],
      monthTotal: [],
      tableType: '',
      hisType: '',
      safetyfinalForm: {},
      fileid: [],
      highlightsfileid: [],
      safetyactionfileid: [],
      orgdutyinfofileid: [],
      indutyinfofileid: [],
      firstphasefileid:[],
      riskinfofileid:[],
      curMonth: '',
      columns: [
        {
          label: '项目名称',
          prop: 'projectName',
          width: 200
        },
        {
          label: '问题描述',
          prop: 'problemDesc',
          width: 100
        },
        {
          label: '问题主体',
          prop: 'problemSubject',
          width: 100,
          formatter: (row) => {
            return <span>施工单位</span>
          }
        },
        {
          label: '是否制定具体整改措施',
          prop: 'isModifyPlan',
          width: 150
        },
        {
          label: '发现问题是否完成整改',
          prop: 'finishModify',
          width: 150
        },
        {
          label: '检查情况说明',
          prop: 'checkInfo',
          formatter: (row) => {
            return (
              <span>
                <span>{row.checkInfo}，</span>
                <span>整改前照片：</span>
                <span>
                  {row.beforephotoList &&
                    row.beforephotoList.map((ele, i) => {
                      return (
                        <el-image
                          class="checkImage"
                          key={i}
                          src={ele.url}
                          fit="fill"
                          preview-src-list={[ele.url]}
                        ></el-image>
                      )
                    })}
                </span>
                <span>整改后照片：</span>
                <span>
                  {row.afterphotoList &&
                    row.afterphotoList.map((ele, i) => {
                      return (
                        <el-image
                          class="checkImage"
                          key={i}
                          src={ele.url}
                          fit="fill"
                          preview-src-list={[ele.url]}
                        ></el-image>
                      )
                    })}
                </span>
              </span>
            )
          }
        }
      ],
      projects: [], //风险防范情况列表
      orders: [], //制度落实情况列表
      emergencys: [], //应急演练列表
      punishs: [], //考核处罚列表
      trains: [], //教育培训列表
      multipleSelection: [],
      curYear: '',
      templateForm: {},
      rulesCode: [],
      templateRules: {},
      safetyfinalRules:{},
      rulesType: ['input', 'project','image','file'],
      powerData:[],
      editStatus:false,
      tableDataCope:[]
    }
  },
  watch:{
    isView:{
      handler(n,o){
        if(!this.isView && this.templateCode == 'safetyfinal'){
          for (let index = 0; index < this.item.columns.length; index++){
            const element = this.item.columns[index]
            if(element.must &&
              !this.rulesCode.includes(element.code) &&
              this.rulesType.includes(element.type)){
                this.$set(this.safetyfinalRules,element.code,{
                      required: true,
                      validator:(rule, value, callback)=>{
                        if (!value) {
                            callback(`${element.name}未填写`)
                          } else if (value.length < 50 && element.code != 'highlights') {
                            callback(`${element.name}，填写内容少于最低50个字符的数字限制`)
                          } else {
                            callback()
                          }
                      },
                      trigger: ['blur']
                    })
              }
          }
        }
      },
      immediate:true
    }
  },
  computed:{
    editTemFlag(){
       return this.$route.path == '/monthly_report/work_reporting/template_management_info'
       && !this.editStatus
       && this.powerData['template_management_update']
       && this.tableInitData.rows
       && this.tableInitData.rows.length
    }
  },
  created() {
    this.getPower()
    if (!this.isHis) {
      if (!window[this.boId]) {
        window[this.boId] = {}
      }
      const thisId = generateUUID()
      window[this.boId][thisId] = this
    }
    this.curMonth = this.yearMonth.slice(4).replace(/\b(0+)/gi, '')
    this.curYear = this.yearMonth.slice(0, 4)
    if (this.item.titles && this.item.titles.length) {
      this.tableCode = this.item.code
      this.tableName = this.item.name
      this.tableType = this.item.tableType
      this.hisType = this.item.hisType
      if (this.hisType == '2' && this.isHis) {
        this.item.columns.unshift({
          name: '检查月份',
          code: 'inspectMonth',
          merge: false,
          readOnly: true,
          type: 'readOnly',
          dataType: 'string'
        })
        this.item.titles.unshift({
          name: '检查月份',
          code: 'inspectMonth',
          children: []
        })
      }
      if (this.templateCode != 'safetyfinal') {
        this.dealHeader(this.item.titles, this.tableColumn)
      }
      if (this.isHis) {
        this.getHisDataFn()
      } else if (this.isTemplate) {
        this.getTemplateFn()
      } else if (this.boId) {
        this.getDataFn()
      }
    }
    if (this.boType == 'reportcity' && this.templateCode == 'safetyfinal') {
      let req = {
        cityId: this.cityId,
        yearMonth: this.yearMonth
      }
      getSafetyFinalDtoService(req).then((res) => {
        if (res && res.code == '0000') {
          this.projects = res.data.projects || []
          this.orders = res.data.orders || []
          this.emergencys = res.data.emergencys || []
          this.punishs = res.data.punishs || []
          this.trains = res.data.trains || []
          this.projects.forEach((item, index) => {
            if (item.afterphoto) {
              getFilesService(item.afterphoto).then((res) => {
                if (res && res.code == '0000') {
                  let fileList = []
                  res.data.forEach((ele) => {
                    if (ele.isImg) {
                      fileList.push({
                        url: window.g.baseUrl + `/minio/senondownndrp/epms-jx/${ele.path}`,
                        id: ele.id
                      })
                    }
                  })
                  this.$set(item, 'afterphotoList', fileList)
                }
              })
            }
            if (item.beforephoto) {
              getFilesService(item.beforephoto).then((res) => {
                if (res && res.code == '0000') {
                  let fileList = []
                  res.data.forEach((ele) => {
                    if (ele.isImg) {
                      fileList.push({
                        url: window.g.baseUrl + `/minio/senondownndrp/epms-jx/${ele.path}`,
                        id: ele.id
                      })
                    }
                  })
                  this.$set(item, 'beforephotoList', fileList)
                }
              })
            }
            let text = `${index + 1}、${this.curYear}年${this.curMonth}月“${
              item.projectName
            }”，${item.problemDesc}，问题数【1】，整改数${
              item.finishModify == '是' ? 1 : 0
            }；`
            this.$set(item, 'text', text)
          })
          this.orders.forEach((item, index) => {
            getFilesService(item.id).then((res) => {
              if (res && res.code == '0000') {
                let fileList = []
                res.data.forEach((ele) => {
                  if (ele.isImg) {
                    fileList.push({
                      url: window.g.baseUrl + `/minio/senondownndrp/epms-jx/${ele.path}`,
                      id: ele.id
                    })
                  }
                })
                this.$set(item, 'photoList', fileList)
              }
            })
            let text = `${index + 1}、${
              item.countyName ? item.countyName + '：' : ''
            }${
              this.$moment(item.safetyDate).format('yyyy年MM月DD日') +
              item.content
            }；`
            this.$set(item, 'text', text)
          })
          this.trains.forEach((item, index) => {
            getFilesService(item.id).then((res) => {
              if (res && res.code == '0000') {
                let fileList = []
                res.data.forEach((ele) => {
                  if (ele.isImg) {
                    fileList.push({
                      url: window.g.baseUrl + `/minio/senondownndrp/epms-jx/${ele.path}`,
                      id: ele.id
                    })
                  }
                })
                this.$set(item, 'photoList', fileList)
              }
            })
            let text = `${index + 1}、${
              item.countyName ? item.countyName + '：' : ''
            }${this.$moment(item.safetyDate).format(
              'yyyy年MM月DD日'
            )}，开展安全生产教育培训次数：${
              item.numOne
            }，参加安全生产教育培训人次：${item.numTwo}，开展情况：${
              item.content
            }；`
            this.$set(item, 'text', text)
          })
          this.emergencys.forEach((item, index) => {
            getFilesService(item.id).then((res) => {
              if (res && res.code == '0000') {
                let fileList = []
                res.data.forEach((ele) => {
                  if (ele.isImg) {
                    fileList.push({
                      url: window.g.baseUrl + `/minio/senondownndrp/epms-jx/${ele.path}`,
                      id: ele.id
                    })
                  }
                })
                this.$set(item, 'photoList', fileList)
              }
            })
            let text = `${index + 1}、${
              item.countyName ? item.countyName + '：' : ''
            }${this.$moment(item.safetyDate).format(
              'yyyy年MM月DD日'
            )}，开展应急预案演练次数-专项应急预案：${
              item.numThree
            }，参加应急预案演练人次-专项应急预案：${
              item.numFive
            }，开展应急预案演练次数-现场处置方案：${
              item.numFour
            }，参加应急预案演练人次-现场处置方案：${item.numSix}，说明：${
              item.content
            };`
            this.$set(item, 'text', text)
          })
          this.punishs.forEach((item, index) => {
            getFilesService(item.id).then((res) => {
              if (res && res.code == '0000') {
                let fileList = []
                res.data.forEach((ele) => {
                  if (ele.isImg) {
                    fileList.push({
                      url: window.g.baseUrl + `/minio/senondownndrp/epms-jx/${ele.path}`,
                      id: ele.id
                    })
                  }
                })
                this.$set(item, 'photoList', fileList)
              }
            })
            let text = `本月对合作单位安全生产工作开出${item.numOne}张处罚单，根据合作单位负面清单行为考核罚款金额共计${item.totalPenaltiesAmount}万元，各单位已按要求缴纳罚款。`
            this.$set(item, 'text', text)
          })
        }
      })
    }
  },
  methods: {
    getPower(){
      JSON.parse(sessionStorage.getItem('authorities')).forEach((item)=>{
        this.powerData[item.authority]=true
      })
    },
    //处理表头
    dealHeader(data, tableColumn) {
      data.forEach((item) => {
        let multilevelColumn = []
        if (item.children && item.children.length) {
          this.dealHeader(item.children, multilevelColumn)
        }
        let column = {}
        for (let index = 0; index < this.item.columns.length; index++) {
          const element = this.item.columns[index]
          if (element.type == 'city') {
            this.$set(this.row, element.code, this.cityName)
          } else {
            this.$set(this.row, element.code, '')
          }
          if (
            element.must &&
            !this.rulesCode.includes(element.code) &&
            this.rulesType.includes(element.type)
          ) {
            this.rulesCode.push(element.code)
          }
          if (element.code == item.code) {
            column = JSON.parse(JSON.stringify(element))
            break
          }
        }
        if (column.merge && !this.mergeColumn.includes(column.code)) {
          this.mergeColumn.push(column.code)
        }
        if (
          column.type &&
          column.type != 'readOnly' &&
          column.type != 'hide' &&
          column.code &&
          !this.noReadOnly.includes(column.code)
        ) {
          this.noReadOnly.push(column.code)
        }
        tableColumn.push({
          label: item.name,
          prop: item.code,
          multilevelColumn: multilevelColumn,
          width: item.name == '序号' ? '60' : '',
          minWidth: item.name != '序号' ? '120' : '',
          hidden: column.type == 'hide',
          colType: column.type,
          dataType: column.dataType
        })
      })
    },
    //处理历史数据表头
    dealHisHeader(tableColumn) {
      let monthTotal = [],
        num = 0,
        i = null
      tableColumn.forEach((ele, index) => {
        if (ele.multilevelColumn && ele.multilevelColumn.length) {
          this.dealHisHeader(ele.multilevelColumn)
        }
        if (ele.prop && this.noReadOnly.includes(ele.prop)) {
          if (this.monthTotal.length) {
            if (!i) {
              i = index
            }
            num++
            this.monthTotal.forEach((item) => {
              monthTotal.push({
                colType: ele.colType,
                dataType: ele.dataType,
                minWidth: ele.minWidth,
                multilevelColumn: ele.multilevelColumn,
                prop: ele.prop + item,
                label: `${item}月${ele.label}`,
                width: ele.width,
                hidden: ele.hidden
              })
            })
          }
        }
      })
      if (monthTotal.length && num) {
        // tableColumn.splice(i, num, ...monthTotal)
        tableColumn.splice(i)
        tableColumn.push(...monthTotal)
      }
    },
    //查询模板数据
    getTemplateFn() {
      let req = {
        tableCode: this.tableCode,
        templateCode: this.templateCode
      }
      getTemplateDataService(req).then((res) => {
        if (res && res.code == '0000') {
          this.tableInitData = res.data
          let data = res.data.rows || []
          let tableData = []
          data.forEach((item) => {
            let req = {
              id: item.id || "",
              ...this.row
            }
            item.columns.forEach((ele) => {
              req[ele.code] = ele.value || ''
            })
            tableData.push(req)
          })
          this.tableData = tableData
          this.tableDataCope = JSON.parse(JSON.stringify(tableData))
          this.merage()
        }
      })
    },
    //获取历史数据
    getHisDataFn() {
      let req = {
        yearMonth: this.yearMonth,
        tableCode: this.tableCode,
        templateCode: this.templateCode,
        creatorId: this.reporterId,
        boType: this.boType,
        cityId: this.cityId
      }
      getHisDataService(req).then((res) => {
        if (res && res.code == '0000') {
          this.tableInitData = res.data || []
          this.tableInitData.forEach((ele) => {
            let month = ele.yearMonth.slice(4).replace(/\b(0+)/gi, '')
            this.monthTotal.push(month)
          })
          if (this.hisType == '2') {
            let tableData = []
            this.tableInitData.forEach((ele, i) => {
              ele.rows.forEach((row) => {
                let req = {
                  ...this.row,
                  id: row.id,
                  inspectMonth: this.monthTotal[i] + '月份'
                }
                row.columns.forEach((col) => {
                  req[col.code] = col.value || ''
                })
                tableData.push(req)
              })
            })
            this.tableData = tableData
            this.merage()
          } else {
            this.dealHisHeader(this.tableColumn)
            this.$nextTick(() => {
              if (this.tableInitData.length > 0) {
                let data = this.tableInitData[0].rows || []
                let tableData = []
                data.forEach((item) => {
                  let req = {
                    ...this.row
                  }
                  item.columns.forEach((ele) => {
                    req[ele.code] = ele.value || ''
                  })
                  tableData.push(req)
                })
                for (let i = 0; i < this.tableInitData.length; i++) {
                  const element = this.tableInitData[i]
                  for (let j = 0; j < element.rows.length; j++) {
                    const columns = element.rows[j].columns
                    columns.forEach((ele) => {
                      if (this.noReadOnly.includes(ele.code)) {
                        tableData[j][ele.code + this.monthTotal[i]] = ele.value
                      }
                    })
                  }
                }
                this.tableData = tableData
              }
              this.merage()
            })
          }
        }
      })
    },
    //获取表格数据
    getDataFn() {
      let req = {
        boId: this.boId,
        tableCode: this.tableCode,
        templateCode: this.templateCode,
        creatorId: this.reporterId
      }
      getDataService(req).then((res) => {
        if (res && res.code == '0000') {
          this.tableInitData = res.data
          if (this.templateCode == 'safetyfinal') {
            if (this.tableInitData.rows && this.tableInitData.rows.length) {
              this.safetyfinalForm.id = this.tableInitData.rows[0].id
              this.tableInitData.rows[0].columns.forEach((item) => {
                this.$set(this.safetyfinalForm, item.code, item.value)
              })
              if (this.safetyfinalForm.fileid) {
                this.getNewFileList(this.safetyfinalForm.fileid, 'fileid')
              }
              if (this.safetyfinalForm.firstphasefileid) {
                this.getNewFileList(
                  this.safetyfinalForm.firstphasefileid,
                  'firstphasefileid'
                )
              }
              if (this.safetyfinalForm.riskinfofileid) {
                this.getNewFileList(
                  this.safetyfinalForm.riskinfofileid,
                  'riskinfofileid'
                )
              }
              if (this.safetyfinalForm.indutyinfofileid) {
                this.getNewFileList(
                  this.safetyfinalForm.indutyinfofileid,
                  'indutyinfofileid'
                )
              }
              if (this.safetyfinalForm.orgdutyinfofileid) {
                this.getNewFileList(
                  this.safetyfinalForm.orgdutyinfofileid,
                  'orgdutyinfofileid'
                )
              }
              if (this.safetyfinalForm.safetyactionfileid) {
                this.getNewFileList(
                  this.safetyfinalForm.safetyactionfileid,
                  'safetyactionfileid'
                )
              }
              if (this.safetyfinalForm.highlightsfileid) {
                this.getNewFileList(
                  this.safetyfinalForm.highlightsfileid,
                  'highlightsfileid'
                )
              }
            }
          } else {
            let data = res.data.rows || []
            let tableData = []
            let mustCode
            if( this.tableCode == 'm_t_reporttask_threemonthly'){
              mustCode = 'monthtotal'
            }else{
              mustCode = 'finishinfo'
            }
            data.forEach((item,index) => {
              let req = {
                id: item.id,
                ...this.row
              }
              item.columns.forEach((ele) => {
                req[ele.code] = ele.value || ''
                if(ele.code == 'must' && ele.value == '1'){
                  this.$set(this.templateForm, `${mustCode}${index}`, req[mustCode] || "")
                  this.$set(this.templateRules, `${mustCode}${index}`, {
                    required: true,
                    message: '该字段不能为空',
                    trigger: ['blur', 'change']
                  })
                }else if(ele.code =='filemust' && ele.value == '1'){
                  this.$set(this.templateForm, `fileid${index}`, req.fileid || "")
                  this.$set(this.templateRules, `fileid${index}`, {
                    required: true,
                    message: '该字段不能为空',
                    trigger: ['blur', 'change']
                  })
                }
              })
              tableData.push(req)
            })
            this.tableData = tableData
            this.commonsetRule()
            this.merage()
          }
        }
      })
    },
    //合并单元格调用方法
    toMergeColumn({ row, column, rowIndex, columnIndex }) {
      if (
        this.mergeColumn.includes(column.property) &&
        this.tableData.length > 1
      ) {
        const row1 = this[column.property + 'Arr'][rowIndex]
        const col1 = row1 > 0 ? 1 : 0 // 如果被合并了row = 0; 则他这个列需要取消
        return {
          rowspan: row1,
          colspan: col1
        }
      }
    },
    merage() {
      this.merageInit()
      for (let i = 0; i < this.tableData.length; i++) {
        if (i === 0) {
          this.mergeColumn.forEach((item) => {
            this[item + 'Arr'].push(1)
            this[item + 'Pos'] = 0
          })
        } else {
          this.mergeColumn.forEach((item) => {
            if (this.tableData[i][item + 'flag']) {
              if (
                this.tableData[i][item] == this.tableData[i - 1][item] &&
                this.tableData[i][item + 'flag'] ==
                  this.tableData[i - 1][item + 'flag']
              ) {
                this[item + 'Arr'][this[item + 'Pos']]++
                this[item + 'Arr'].push(0)
              } else {
                this[item + 'Arr'].push(1)
                this[item + 'Pos'] = i
              }
            } else {
              if (this.tableData[i][item] == this.tableData[i - 1][item]) {
                this[item + 'Arr'][this[item + 'Pos']]++
                this[item + 'Arr'].push(0)
              } else {
                this[item + 'Arr'].push(1)
                this[item + 'Pos'] = i
              }
            }
          })
        }
      }
    },
    merageInit() {
      this.mergeColumn.forEach((item) => {
        this[item + 'Arr'] = []
        this[item + 'Pos'] = 0
      })
    },
    save(cb, flag = false) {
      let validateFlag = false
      let refsForm
      if(this.templateCode === 'safetyfinal'){
        refsForm = this.$refs.form
      }else{
        refsForm = this.$refs[this.tableCode]
      }
      refsForm.validate((valid,obj) => {
        if (valid) {
          validateFlag = true
        } else {
          if(!flag){
            if(this.templateCode === 'safetyfinal' && obj){
              let errAry = Object.values(obj)
              let errMsg = "安全生产月度总结报告：</br>"
              errAry.forEach(item=>{
                errMsg += item[0].message + '</br>'
              })
              this.$alert(`${errMsg}请按照要求填写后再提交！`,'提示',{
                confirmButtonText: '确定',
                type: 'warning',
                dangerouslyUseHTMLString: true
              })
            }else{
              this.$alert(`存在必填项未填写，请填写后再保存！`,'提示',{
                confirmButtonText: '确定',
                type: 'warning',
                dangerouslyUseHTMLString: true
              })
            }
          }
          validateFlag = false
        }
      })
      if (validateFlag) {
        let req = {
          boId: this.boId,
          boType: this.boType,
          code: this.tableCode,
          name: this.tableName,
          creatorId: this.reporterId,
          yearMonth: this.yearMonth
        }
        let rows = [],
          saveFlag = true,
          msg = ''
        if (this.templateCode == 'safetyfinal') {
          let obj = {
            id: this.safetyfinalForm.id,
            columns: []
          }
          for (let index = 0; index < this.item.columns.length; index++) {
            const element = this.item.columns[index]
            obj.columns.push({
              code: element.code,
              dataType: element.dataType,
              value: this.safetyfinalForm[element.code] || ''
            })
          }
          rows.push(obj)
        } else {
          if (this.tableData.length == 0) {
            saveFlag = false
            msg = '请先新增数据后再进行保存'
          } else {
            this.tableData.forEach((item, i) => {
              let obj = {
                id: item.id,
                columns: []
              }
              for (let index = 0; index < this.item.columns.length; index++) {
                const element = this.item.columns[index]
                if (this.tableCode == 'm_t_jobtask_officer') {
                  if (i == 14 && element.type == 'deduction') {
                    if (item[element.code] == '是' && !item.finishinfo) {
                      saveFlag = false
                      msg = '请先选择第八行所在的完成情况'
                    }
                  }
                } else if (this.tableCode == 'm_t_superviser_sum') {
                  if (element.code == 'evaluate' && !item[element.code]) {
                    saveFlag = false
                    msg = '请填写做得好的地方'
                  }
                }
                obj.columns.push({
                  code: element.code,
                  dataType: element.dataType,
                  value: item[element.code] || ''
                })
              }
              rows.push(obj)
            })
          }
        }
        req.rows = rows
        if (saveFlag) {
          if (flag) {
            return saveDataService(req)
          } else {
            saveDataService(req).then((res) => {
              if (res && res.code == '0000') {
                if (cb && cb.constructor === Function) {
                  cb()
                } else {
                  this.$message({
                    type: 'success',
                    message: '保存成功！'
                  })
                  if (this.tableType == 2) {
                    this.getDataFn()
                  }
                }
              }
            })
          }
        } else {
          this.$message({
            showClose: true,
            message: msg,
            type: 'warning'
          })
        }
      } else {
        if(flag){
          if(this.reportTemplageTab){
            if(this.singleTable){
              return this.reportTemplageTab.reportTemplateName + '-' + this.reportTemplageTab.reporterName
            }else{
              return this.reportTemplageTab.reportTemplateName + '-' + this.reportTemplageTab.reporterName + '--' + this.item.name
            }
          }else{
            return this.item.name
          }
        }
      }
    },
    addRow() {
      let row = {
        ...this.row,
        boforeId: generateUUID()
      }
      this.tableData.push(row)
      this.commonsetRule()
    },
    delRow() {
      if (this.multipleSelection.length) {
        this.$confirm('是否确认删除选择数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then((res) => {
          let ids = [],
            boforeId = []
          this.multipleSelection.forEach((item) => {
            if (item.id) {
              ids.push(item.id)
            } else {
              boforeId.push(item.boforeId)
            }
          })
          let delFn = () => {
            for (let index = this.tableData.length - 1; index >= 0; index--) {
              const item = this.tableData[index]
              if (item.id && ids.includes(item.id)) {
                this.tableData.splice(index, 1)
              }
              if (item.boforeId && boforeId.includes(item.boforeId)) {
                this.tableData.splice(index, 1)
              }
            }
            this.commonsetRule()
          }
          if (ids.length) {
            deleteDataService({
              ids: ids.join(),
              tableCode: this.tableCode
            }).then((res) => {
              if (res && res.code == '0000') {
                this.$message.success('删除成功！')
                delFn()
              }
            })
          } else {
            this.$message.success('删除成功！')
            delFn()
          }
        })
      } else {
        this.$message.warning('请勾选数据！')
      }
    },
    selectionChange(selection) {
      this.multipleSelection = selection
    },
    uploadNewFile(params, type) {
      let fileName = params.file.name
      let pos = fileName.lastIndexOf('.')
      let lastName = fileName.substring(pos, fileName.length)
      let upLoadFileType = ['.jpg', '.png']
      if (!upLoadFileType.includes(lastName)) {
        this.$message({
          showClose: true,
          message: `只能上传jgp,png图片类型格式！`,
          type: 'warning'
        })
        return false
      }
      if (!this.safetyfinalForm[type]) {
        this.safetyfinalForm[type] = generateUUID()
      }
      let param = new FormData()
      param.append('files', params.file)
      param.append('businessType', type)
      param.append('boId', this.safetyfinalForm[type])
      param.append('access_token', sessionStorage.getItem('access_token'))
      uploadFiles(param)
        .then((res) => {
          if (res && res.data && res.code == '0000') {
            // 上传成功
            this.$message({
              showClose: true,
              message: '上传成功！',
              type: 'success'
            })
            this.getNewFileList(this.safetyfinalForm[type], type)
          }
        })
        .catch(() => {
          this.$message({
            showClose: true,
            message: '上传失败！',
            type: 'warning'
          })
        })
    },
    getNewFileList(id, type) {
      getFilesService(id).then((res) => {
        if (res && res.code == '0000') {
          this[type] = res.data || []
          this[type].forEach(ele => {
            if (ele.isImg) {
              let url = window.g.baseUrl + `/minio/senondownndrp/epms-jx/${it.path}`
              this.$set(ele,'url',url)
            }
          })
        }
      })
    },
    delBtn(row, type) {
      this.$confirm(`确定删除该图片？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'inquiry',
        iconClass: 'el-icon-question',
        modal: false,
        center: false,
        subMessage: '' // 小消息，会以另一种样式显示
      }).then(() => {
        let req = {
          ids: row.id
        }
        delFiles(req).then((res) => {
          if (res && res.code == '0000') {
            this.$message({
              showClose: true,
              message: '删除成功！',
              type: 'success'
            })
            this.getNewFileList(this.safetyfinalForm[type], type)
          }
        })
      })
    },
    //设置必填校验
    commonsetRule() {
      this.tableData.forEach((item, index) => {
          for (const key in item) {
            if (this.rulesCode.includes(key)) {
              this.$set(this.templateForm, `${key}${index}`, item[key])
              this.$set(this.templateRules, `${key}${index}`, {
                required: true,
                message: '该字段不能为空',
                trigger: ['blur', 'change']
              })
            }
          }
        })
    },
    //编辑模板
    edit(){
      this.editStatus = !this.editStatus
    },
    //保存模板
    saveTem(){
      let rows = []
      let merageCode = {}
      this.tableData.forEach((item, i) => {
        let obj = {
          columns: []
        }
        for (let index = 0; index < this.item.columns.length; index++) {
          const element = this.item.columns[index]
          if(element.codeField){
            let value = item[element.code]
            if(this.mergeColumn.includes(element.code) && this[element.code+'Arr'][i] != 0){
              value = merageCode[element.code] = item[element.code]
            }else if(this.mergeColumn.includes(element.code) && this[element.code+'Arr'][i] == 0){
              value = merageCode[element.code]
            }
            obj.columns.push({
              code: element.code,
              dataType: element.dataType,
              value,
              codeField:element.codeField
            })
          }
        }
        rows.push(obj)
      })
      saveTemplateDataService({rows}).then(res=>{
        if( res && res.code == '0000' ){
          this.$message({
            type: 'success',
            message: '保存成功！'
          })
          this.editStatus = !this.editStatus
        }
      })
    },
    cancel(){
      this.tableData = JSON.parse(JSON.stringify(this.tableDataCope))
      this.editStatus = !this.editStatus
    }
  }
}
</script>

<style lang="scss" scoped>
.add-row-btn {
  text-align: right;
  margin-bottom: 5px;
}
.safetyfinal {
  .el-form-item {
    margin-bottom: 10px;
    ::v-deep .el-form-item__label {
      padding: 0;
      line-height: 32px;
      font-size: 18px;
      color: #333;
    }
  }
  .md-title {
    ::v-deep .el-form-item__label {
      padding: 0;
      line-height: 32px;
      font-size: 16px;
      color: #333;
    }
  }
  .filed-type {
    margin-top: -5px;
  }
}
.templateForm {
  ::v-deep .cell {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  ::v-deep .el-form-item {
    margin-bottom: 0px;
    .el-form-item__label {
      visibility: hidden;
      height: 0;
    }
    &.is-required .el-form-item__label::before {
      visibility: visible;
    }
    .el-input {
      width: 86%;
    }
  }
}
.projects-table {
  ::v-deep .el-table__cell {
    line-height: 23px;
  }
}
.checkImage {
  width: 80px;
  height: 80px;
  margin-right: 5px;
}
.dealCheckImage {
  text-indent: 0;
  width: 120px;
  height: 120px;
  margin-right: 10px;
}
.dealProjects {
  .item {
    text-indent: 2em;
  }
}
.fileList {
  display: flex;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  .fileItme {
    width: 10%;
    display: flex;
    justify-content: space-between;
    margin: 0 5px 5px 0;
  }
}
.del-file {
  color: red;
  cursor: pointer;
  flex-shrink: 0;
  margin-left: 5px;
  height: 32px;
  line-height: 32px;
  &:hover {
    font-weight: bold;
  }
}
</style>
