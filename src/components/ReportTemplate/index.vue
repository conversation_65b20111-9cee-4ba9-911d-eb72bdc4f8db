<!--
 * @Descripttion: 报告模板
 * @Author: lzp
 * @Date: 2023-07-19 09:20:11
-->
<template>
  <div class="ReportTemplate">
    <div v-if="reporterNameFlg" class="tempateDto-title">{{ tempateDto.name }}</div>
    <template v-if="multipleTable">
      <el-tabs v-model="activeName" stretch type="border-card">
        <el-tab-pane
          v-for="tab in tempateDto.tables"
          :key="tab.code"
          :label="tab.name"
          :name="tab.code"
          lazy
        >
          <ComItemTable
            :ref="tab.code"
            :item="tab"
            :isView="isView"
            :isHis="isHis"
            :isTemplate="isTemplate"
            :templateCode="templateCode"
            :yearMonth="yearMonth"
            :cityName="cityName"
            :cityId="cityId"
            :reporterId="reporterId"
            :boId="boId"
            :boType="boType"
            :reportTemplageTab="reportTemplageTab"
          ></ComItemTable>
        </el-tab-pane>
      </el-tabs>
    </template>
    <template v-else-if="singleTable">
      <ComItemTable
        :ref="tempateDto.tables[0].code"
        :item="tempateDto.tables[0]"
        :isView="isView"
        :isHis="isHis"
        :isTemplate="isTemplate"
        :templateCode="templateCode"
        :yearMonth="yearMonth"
        :cityName="cityName"
        :cityId="cityId"
        :reporterId="reporterId"
        :boId="boId"
        :boType="boType"
        :reportTemplageTab="reportTemplageTab"
        :singleTable="singleTable"
      ></ComItemTable>
    </template>
  </div>
</template>

<script>
import ComItemTable from './comItemTable.vue'
import { getTempateDtoByCodeService } from '@/api/report_template_api'
export default {
  name: 'ReportTemplate',
  components: { ComItemTable },
  props: {
    templateCode: {
      type: String,
      default: ''
    },
    boId: {
      type: String,
      default: ''
    },
    isView: {
      type: Boolean,
      default: false
    },
    isHis: {
      type: Boolean,
      default: false
    },
    isTemplate: {
      type: Boolean,
      default: false
    },
    yearMonth: {
      type: String,
      default: ''
    },
    reporterId: {
      type: String,
      default: ''
    },
    reporterNameFlg:{
      type:Boolean,
      default:true
    },
    cityName: {
      type: String,
      default: ''
    },
    cityId: {
      type: String,
      default: ''
    },
    boType: {
      type: String,
      default: 'report'
    },
    reportTemplageTab:{
      type:Object,
      default:null
    }
  },
  data() {
    return {
      multipleTable: false,
      singleTable: false,
      tempateDto: {},
      activeName: '',
    }
  },
  created() {
    let req = {
      code: this.templateCode
    }
    this.getTempateDtoByCode(req)
  },
  methods: {
    getTempateDtoByCode(data) {
      getTempateDtoByCodeService(data).then((res) => {
        if (res && res.code == '0000') {
          this.tempateDto = JSON.parse(JSON.stringify(res.data))
          if (this.tempateDto.tables && this.tempateDto.tables.length > 1) {
            this.multipleTable = true
            this.activeName = this.tempateDto.tables[0].code
          } else if (
            this.tempateDto.tables &&
            this.tempateDto.tables.length == 1
          ) {
            this.singleTable = true
            this.activeName = this.tempateDto.tables[0].code
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ReportTemplate {
  background-color: #fff;
  padding: 20px;
  .tempateDto-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #333;
  }
}
</style>