// 将component里面的公共组件统一引入
import Vue from 'vue'
const requireComponents = require.context('@/components', true, /.vue$/)
// 遍历出每个组件的路径
requireComponents.keys().forEach(fileName => {
  // 组件实例
  const reqCom = requireComponents(fileName)
  const arr = fileName.split('/')
  arr.pop()
  // 截取路径作为组件名
  const reqComName =
    'mss' +
    arr.pop()
      .split('.')[0]
  //  Vue.component(组件名称，组件名称对应的路径)
  Vue.component(reqComName, reqCom.default || reqCom)
})
