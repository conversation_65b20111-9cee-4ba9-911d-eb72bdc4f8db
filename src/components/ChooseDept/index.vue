<!-- 选择部门 -->
<template>
  <div>
    <el-dialog
      :visible.sync="showDialog"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :width="dialogWidth"
      :custom-class="customClass"
      :modal="true"
      append-to-body
    >
      <div class="choose" :style="{ width: '48%' }">
        <div class="chooseBody includeFoot">
          <el-tree
            v-if="showTree"
            ref="tree"
            :props="defaultProps"
            :load="loadNode"
            lazy
            node-key="id"
            :default-checked-keys="defKeys"
            :check-strictly="true"
            show-checkbox
            @check-change="handleCheckChange"
          >
            <span slot-scope="{ data }">
              <span
                class="custom-tree-node-text"
                :data-leaf="data.leaf"
                :data-check="data.check"
                data-nodeKey="id"
              >{{ data[defaultProps.label] }}
              </span>
            </span>
          </el-tree>
        </div>
        <div class="chooseFoot">
          <div class="choose"></div>
        </div>
      </div>
      <div class="choose" :style="{ width: '48%' }">
        <div class="chooseTitle">
          <span class="iconfont">选择结果</span>
          <span class="tips">(双击名字删除)</span>
        </div>
        <div class="chooseBody">
          <el-checkbox-group v-model="delList">
            <el-checkbox
              v-for="(item, index) in checkList"
              :key="index"
              class="showList"
              :label="item.text"
            >
              <span @dblclick="delDept(item, 'single')">
                {{
                  item.text
                }}
              </span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="chooseFoot">
          <el-checkbox v-model="checkedAll" @change="checkAll">全选</el-checkbox>
          <span class="del" @click="delDept(delList, 'multiple')">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitOrder">确 定</el-button>
        <el-button size="mini" style="margin-left: 10px" @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDeptService } from '@/api/choose_dept'
export default {
  name: 'ChooseDept',
  props: {
    // 是否多选
    multSelect: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 自定义title
    titleName: {
      type: String,
      default: () => {
        return '选择单位'
      }
    },
    checkLevel: { // 自定义勾选的层级
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return '100%'
      }else{
        return '50%'
      }
    },
    customClass(){
      // 自动检测是否为手机端，手机端的加一个class
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return 'ChooseDept is-mobile-dialog'
      }else{
        return 'ChooseDept'
      }
    },
  },
  data() {
    return {
      showTree: false,
      title: this.titleName || '选择合作单位',
      checkList: [],
      delList: [],
      defKeys: [],
      checkedAll: false, // 是否全选
      resolveData: [],
      areaTreeData: '', // 组织结构数据
      defaultProps: {
        // 懒加载树状下拉框
        label: 'text',
        children: 'children',
        isLeaf: 'leaf'
      },
      showDialog: false,
      selName: '',
      deptParams: {}
    }
  },
  methods: {
    /**
     * 弹框初始化 item
     * @param item
     */
    init(checkList, deptParams) {
      this.showTree = true
      this.checkList = checkList
      this.selName = ''
      this.treeCheck()
      this.showDialog = true
      this.deptParams = deptParams
    },

    /**
     * 提交表单
     */
    submitOrder() {
      this.$emit('showCheckList', {
        checkList: this.checkList
      })
      this.defKeys = []
      this.showDialog = false
    },
    // 树状下拉
    loadNode(node, resolve) {
      const level = node.level
      let params
      if (node.level === 0) {
        this.getTreeList(
          { isReturnUser: '0', parentId: '', rootId: '-1' },
          () => {
            return resolve(this.areaTreeData)
          }
          , level)
      } else {
        if (node.data && node.data.id) {
          params = {
            isReturnUser: '0',
            parentId: node.data.id,
            rootId: '-1'
          }
        } else {
          params = {
            isReturnUser: '0',
            parentId: '',
            rootId: '-1'
          }
        }
        setTimeout(() => {
          this.getTreeList(params, () => {
            if (this.areaTreeData.length > 0) {
              resolve(this.areaTreeData)
            } else {
              return resolve([])
            }
          }, level)
        }, 200)
      }
    },
    // 获取树状列表数据
    getTreeList(params, callback, level) {
      this.tableLoading = true
      getDeptService({ ...params, ...this.deptParams })
        .then(res => {
          this.tableLoading = false
          if (res && res.code === '0000' && res.data) {
            const data = res.data || []
            this.areaTreeData = data.map(item => {
              if (this.checkLevel.includes(level)) {
                return { ...item, check: true }
              } else {
                return { ...item, check: false }
              }
            })
          }
          callback()
          this.$nextTick(() => {
            this.hideTreeCheckbox()
          })
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 隐藏树节点上的checkbox，仅当节点的leaf为真时，可以勾选,如果值为all，表示所有节点都可以选
    hideTreeCheckbox() {
      try {
        const eleTree = this.$refs.tree.$el
        const nodes = eleTree.getElementsByClassName('custom-tree-node-text')
        for (let i = 0, len = nodes.length; i < len; i++) {
          const eleNode = nodes[i]
          const dataLeaf = eleNode.dataset.leaf
          const dataCheck = eleNode.dataset.check
          if (this.checkLevel.length > 0) {
            eleNode.parentNode.previousElementSibling.style.display = dataCheck ? 'block' : 'none'
          } else {
            eleNode.parentNode.previousElementSibling.style.display = dataLeaf ? 'block' : 'none'
          }
        }
      } catch (e) {
        console.error(e)
      }
    },
    // 树搜索事件
    reloadTree() {
      this.showTree = false
      setTimeout(() => {
        this.showTree = true
      }, 500)
    },
    handleCheckChange(data, check) {
      if (check) {
        if (this.multSelect) {
          // 多选
          let flag = false
          this.checkList.forEach((item) => {
            if (item.id === data.id) {
              flag = true
            }
          })
          !flag && this.checkList.push(data)
        } else {
          // 单选
          this.$refs.tree.setCheckedNodes([data])
          this.checkList.push(data)
        }
      } else {
        // 删除取消选择的数据
        for (let index = 0; index < this.checkList.length; index++) {
          const element = this.checkList[index]
          if (element.id === data.id) {
            this.checkList.splice(index, 1)
            break
          }
        }
      }
    },
    /**
     * 删除事件
     * @param val
     * @param type
     */
    delDept(val, type) {
      this.defKeys = [] // 树回显数据
      // 单个删除
      if (type === 'single') {
        this.checkList.splice(this.checkList.indexOf(val), 1)
      } else if (type === 'multiple') {
        for (var j = 0; j < val.length; j++) {
          for (var i = 0; i < this.checkList.length; i++) {
            if (this.checkList[i].name === val[j]) {
              this.checkList.splice(i, 1)
              i -= 1
            }
          }
        }
      }
      this.checkedAll = false // 重置全选
      this.delList = []
      this.treeCheck()
    },

    /**
     * 树回显勾选事件
     */
    treeCheck() {
      this.defKeys = []
      this.checkList.forEach((item) => {
        this.defKeys.push(item.id)
      })
      this.defKeys.concat()
      this.$nextTick(function() {
        this.$refs.tree.setCheckedKeys(this.defKeys)
      })
    },

    /**
     * 全选事件
     * @param val
     */
    checkAll(val) {
      if (val) {
        this.checkList.forEach((item) => {
          this.delList.push(item.name)
        })
      } else {
        this.delList = []
      }
    },
    closeDialog() {
      this.showDialog = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .choose {
  .iconfont {
    color: #5887ff;
    font-weight: 400;
    margin-right: 0.5rem;
  }
  .chooseTitle {
    height: 20px;
    line-height: 20px;
    color: #000;
    font-weight: bold;
    padding-left: 0.5rem;
    .iconfont {
      font-size: 13px;
    }
    .tips {
      font-size: 12px;
      font-weight: normal;
      color: #ff5a09;
      margin-left: 0.5rem;
    }
  }
  .chooseBody {
    width: 100%;
    height: 23rem;
    background: #ffffff;
    border: 1px solid #dddddd;
    margin-top: 0.7rem;
    padding: 0.2rem;
    overflow: auto;
    .showList {
      display: block;
      padding: 0.2rem 1rem;

      &:hover {
        background: #ebf1ff;
      }
    }
  }
  .chooseFoot {
    height: 2rem;
    line-height: 2rem;
    width: 100%;
    background: #f9fbff;
    border: 1px solid #dddddd;
    border-top: none;
    padding: 0 1rem;
    .el-pagination {
      padding: 0;
      .el-pager {
        height: 22px;
      }
    }
    .el-checkbox__label {
      font-size: 0.6rem;
    }
  }
  .del {
    float: right;
    cursor: pointer;
    font-size: 1.4rem;
    color: #5887ff;
  }
}
</style>
