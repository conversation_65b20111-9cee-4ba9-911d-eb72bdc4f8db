<template>
  <div>
    <el-dialog
      ref="commonDialog"
      :visible.sync="showDialog"
      :title="title"
      :width="dialogWidth"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal="true"
      append-to-body
    >
      <el-tabs type="border-card" v-model="activeName" @tab-click="tabClick">
        <el-tab-pane
          v-for="(item, index) in optionArr"
          :key="index"
          :label="item.text"
          :name="item.name"
        >
          <div v-if="showOpinionArea">
            <el-form ref="form" :model="flowForm" label-width="100px">
              <el-form-item label="意见" prop="content" :required="true">
                <el-input type="textarea" v-model="flowForm.content"></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div>
            <el-form :model="flowForm">
              <el-table
                v-if="showFlowPath"
                ref="flowPathTable"
                :data="tableData"
                style="width: 100%"
                border
                @selection-change="handleSelectionChange"
                @select="selectHandle"
              >
                <el-table-column label="序号" type="index" width="55" align="center"></el-table-column>
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column label="审批环节" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.nodeName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="规则" align="center">
                  <template slot-scope="{ row }">
                    <span>{{ row.ruleName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="处理人" align="center">
                  <template slot-scope="{ row, $index }">
                    <span :key="$index">
                      <i
                        @click="showChoosePersonDialog(row, $index)"
                        :style="{ cursor: 'pointer' }"
                        v-if="
                          row.nodeType != 5 &&
                          row.canSelectUser == 1 &&
                          row.nodeType != 4 &&
                          row.canSelectUser == 1
                        "
                        class="icon el-icon-user"
                      ></i>
                      <span class="person">{{ row.excuterNames }}</span>
                    </span>
                  </template>
                </el-table-column>
              </el-table>
              <el-col :sapn="12">
                <el-form-item
                  v-if="currentOp == 'transfer'"
                  label="选择转办人："
                  prop="excuterNames"
                  label-width="100px"
                >
                  <el-input v-model="flowForm.excuterNames" @focus="showChoosePersonDialog('')"></el-input>
                </el-form-item>
              </el-col>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="submitDisabled" @click="submitOrder">确 定</el-button>
        <el-button size="mini" @click="closeDialog" style="margin-left: 10px">取 消</el-button>
      </span>
    </el-dialog>
    <mssChooseUser
      ref="ChooseFlowUser"
      @showCheckList="showCheckList"
      :multSelect="multSelectUserFlag"
    />
  </div>
</template>

<script>
import { getPathZhText } from '@/utils/index'
import axios from 'axios'
import {
  initWorkFlowService,
  initNextPathService,
  queryNodeAccessService
} from '@/api/workFlow'
import request from '@/utils/request.js'
export default {
  props: {
    boId: {
      type: String,
      default: () => {
        return ''
      }
    },
    workflowCode: {
      type: String,
      default: () => {
        return ''
      }
    },
    actionUrl: {
      type: String,
      default: () => {
        return ''
      }
    },
    returnAddress: {
      type: String,
      default: '/home'
    },
    //提交前是否保存主表单
    saveMainForm: {
      type: Boolean,
      default: true
    },
    // 选人组件部门过滤条件
    deptParams: {
      type: Object,
      default: null
    },
    // 选人组件部门过滤条件，但是仅当transfer时生效
    deptParamsOnlyTransfer: {
      type: Object,
      default: null
    },
    // 选人组件人员过滤条件
    personParams: {
      type: Object,
      default: null
    },
    // 选人组件人员过滤条件，仅当transfer时生效
    personParamsOnlyTransfer: {
      type: Object,
      default: null
    },
    saveMonthly: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogWidth() {
      if ((/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))){
        return '80%'
      } else {
        return '52%'
      }
    }
  },
  data() {
    return {
      title: '流程处理',
      flowForm: {
        content: '同意'
      },
      canSubmit: true, // 确认按钮
      showOpinionArea: true,
      optionArr: [
        {
          text: '提交',
          name: 'submit'
        },
        {
          text: '转办',
          name: 'transfer'
        },
        {
          text: '驳回',
          name: 'reject'
        },
        {
          text: '终止',
          name: 'terminate'
        }
      ],
      tableData: [],
      activeName: 'submit',
      showFlowPath: true,
      userId: sessionStorage.getItem('userId'),
      taskid: null,
      flowTypeName: '',
      auditOptionController: false, // 是否填写会签意见
      beforeHandFormId: null,
      nextPathListDataIndexes: [],
      nextPathListData: [],
      opList: [],
      tableDataIsChange: false,
      hasSnapshot: false,
      currentOp: 'submit', // 当前流程节点
      pathData: [], // 选择的流程路径
      editIndex: null,
      multSelectUserFlag: false,
      chooseNames: [], // 保存处理人名称
      chooseIds: [], // // 保存处理人id
      nextNodeIds: [],
      currentNodeId: '',
      setDefaultUserFlag: false,
      submitDisabled: false,
      nodeType: '',
      nodeTypeFlagS: true,
      nodeTypeFlagR: true,
      showDialog: false
    }
  },
  watch: {
    activeName: {
      async handler(n, o) {
        switch (n) {
          case 'submit':
            this.submitDisabled = true
            let msg1 = ''
            if (this.$parent.beforeSubmit) {
              msg1 = await this.$parent.beforeSubmit()
            }
            if (msg1 != '') {
              this.$alert(msg1, '提示', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: ''
              })
            } else {
              this.submitDisabled = false
            }
            break
          case 'reject':
            this.submitDisabled = true
            let msg2 = ''
            if (this.$parent.beforeReject) {
              msg2 = this.$parent.beforeReject()
            }
            if (msg2 != '') {
              this.$alert(msg2, '提示', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: ''
              })
            } else {
              this.submitDisabled = false
            }
            break
          case 'terminate':
            this.submitDisabled = true
            let msg3 = ''
            if (this.$parent.beforeTerminate) {
              msg3 = this.$parent.beforeTerminate()
            }
            if (msg3 != '') {
              this.$alert(msg3, '提示', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: ''
              })
            } else {
              this.submitDisabled = false
            }
            break
          case 'transfer':
            this.submitDisabled = true
            let msg4 = ''
            if (this.$parent.beforeTransfer) {
              msg4 = this.$parent.beforeTransfer()
            }
            if (msg4 != '') {
              this.$alert(msg4, '提示', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: ''
              })
            } else {
              this.submitDisabled = false
            }
            break
        }
      }
    },
    nodeType: {
      handler(n, o) {
        if (n == 3 || n == 8) {
          this.nodeTypeFlagS = false
        }
        if (n == 7 || n == 8) {
          this.nodeTypeFlagR = false
        }
      },
      deep: true
    }
  },
  methods: {
    init() {
      this.manageWorkFlow()
    },
    hideEleCheckbox() {
      // 单选的给隐藏表头的全选选择框
      this.$nextTick((_) => {
        let flowPathTable = Array.from(this.$refs.flowPathTable)
        flowPathTable.forEach((ele) => {
          let eleHeader = ele.$el.getElementsByClassName('el-table__header')
          let eleCheckbox = eleHeader[0]
            .getElementsByTagName('th')[1]
            .getElementsByClassName('cell')[0]
          eleCheckbox.style.display = 'none'
        })
      })
    },
    async modelVerify() {
      switch (this.activeName) {
        case 'submit':
          this.submitDisabled = true
          let msg1 = ''
          if (this.$parent.beforeSubmit) {
            msg1 = await this.$parent.beforeSubmit()
            if (this.$parent.beforeNode2) {
              let data = JSON.parse(JSON.stringify(this.nextPathListData))
              this.nextPathListData = this.$parent.beforeNode2(data)
            }
          }
          if (msg1 != '') {
            this.$alert(msg1, '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: ''
            })
          } else {
            this.submitDisabled = false
          }
          break
        case 'reject':
          this.submitDisabled = true
          let msg2 = ''
          if (this.$parent.beforeReject) {
            msg2 = this.$parent.beforeReject()
          }
          if (msg2 != '') {
            this.$alert(msg2, '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: ''
            })
          } else {
            this.submitDisabled = false
          }
          break
        case 'terminate':
          this.submitDisabled = true
          let msg3 = ''
          if (this.$parent.beforeTerminate) {
            msg3 = this.$parent.beforeTerminate()
          }
          if (msg3 != '') {
            this.$alert(msg3, '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: ''
            })
          } else {
            this.submitDisabled = false
          }
          break
        case 'transfer':
          this.submitDisabled = true
          let msg4 = ''
          if (this.$parent.beforeTransfer) {
            msg4 = this.$parent.beforeTransfer()
          }
          if (msg4 != '') {
            this.$alert(msg4, '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: ''
            })
          } else {
            this.submitDisabled = false
          }
          break
      }
    },

    continueTemplate() {
      // 校验是否有boid
      if (this.boId == '') {
        this.$message({
          showClose: true,
          message: '主表单没有id,请先保存',
          type: 'warning'
        })
        return false
      }

      let msg = ''
      if (this.$parent.beforeApprove && this.$parent.beforeApprove()) {
        msg = this.$parent.beforeApprove()
      }
      if (msg !== '') {
        return false
      }

      return true
    },

    /**
     * 提交或者转办时，选择处理人
     * 备注：提交时，入参格式为：row:JSON对象，index:具体的行数从0开始
     * 转办时，入参格式为：row:''，index：没有，所以是undefined
     */
    showChoosePersonDialog(row, index) {
      if (row !== '') {
        this.editIndex = index
        let deptIds = ''
        if (row.firstDeptId) {
          deptIds = row.firstDeptId
        }
        if (row.secondDeptId) {
          deptIds = row.secondDeptId
        }
        if (row.thirdDeptId) {
          deptIds = row.thirdDeptId
        }
        let deptParams = {
          roleIds: row.roleIds || '',
          userIds: row.userIds || '',
          roleGroupIds: row.roleGroupIds || '',
          deptIds: deptIds
        }
        // 外部参数
        if (this.deptParams) {
          deptParams = { ...deptParams, ...this.deptParams }
        }
        let personParams = {
          roleIds: row.roleIds || '',
          userIds: row.userIds || '',
          roleGroupIds: row.roleGroupIds || '',
          firstDeptId: row.firstDeptId,
          secondDeptId: row.secondDeptId,
          thirdDeptId: row.thirdDeptId
        }
        // 外部参数
        if (this.personParams) {
          personParams = { ...personParams, ...this.personParams }
        }
        this.$refs.ChooseFlowUser.init(row, deptParams, personParams)
      }else{
        let deptParams = {}
        // 外部参数
        if (this.deptParamsOnlyTransfer){
          deptParams = { ...deptParams, ...this.deptParamsOnlyTransfer }
        }
        let personParams = {};
        if(this.personParamsOnlyTransfer){
          personParams = { ...personParams, ...this.personParamsOnlyTransfer }
        }
        this.$refs.ChooseFlowUser.init(row, deptParams, personParams)
      }
    },

    handleSelectionChange(row) {
      this.pathData = row
    },
    selectHandle(selection, row) {
      let num = this.activeName == 'submit' ? '0' : '1'
      if (
        ((this.nodeTypeFlagS && this.activeName == 'submit') ||
          (this.nodeTypeFlagR && this.activeName == 'reject')) &&
        selection.length
      ) {
        this.$nextTick(() => {
          this.$refs.flowPathTable[num].clearSelection()
          this.$refs.flowPathTable[num].toggleRowSelection(row)
        })
      } else if (
        (!this.nodeTypeFlagS && this.activeName == 'submit') ||
        (!this.nodeTypeFlagR && this.activeName == 'reject')
      ) {
        if (
          selection.length > 1 &&
          selection[0].sequencenumber != row.sequencenumber
        ) {
          this.$nextTick(() => {
            this.$refs.flowPathTable[num].clearSelection()
            this.$refs.flowPathTable[num].toggleRowSelection(row)
          })
        }
      }
    },
    // 设置默认处理人
    setSubmitUser(userInfo) {
      this.setDefaultUserFlag = true
      return userInfo
    },

    showCheckList(data) {
      const checkList = data.checkList
      const names = checkList
        .map((item) => {
          return item.realName
        })
        .join()
      const ids = checkList
        .map((item) => {
          return item.userId
        })
        .join()

      this.chooseNames[this.editIndex] = names
      this.chooseIds[this.editIndex] = ids

      if (this.editIndex != null) {
        this.$set(this.tableData[this.editIndex], 'excuterNames', names)
        this.$set(this.tableData[this.editIndex], 'excuterList', names)
        this.$set(this.tableData[this.editIndex], 'excuterIds', ids)
      }
      if (this.currentOp == 'transfer') {
        this.$set(this.flowForm, 'excuterNames', names)
        this.flowForm.excuterNames = names
        this.flowForm.excuterIds = ids
      }
    },

    // 根据状态不同，处理流程初始化或者是归档
    manageWorkFlow() {
      const that = this
      if (!that.userId) {
        this.$message({ message: '没有用户id', type: 'error' })
        return
      }
      let req = {
        userId: that.userId,
        workflowCode: that.workflowCode
      }
      if (that.boId) {
        req.boId = that.boId
      }
      if (that.taskid) {
        req.taskId = that.taskid
      }
      if (that.$route.query.status == 'completed') {
        //归档无需调用流程的接口
        this.overWorkflow(req)
      } else {
        this.initWorkFlow(req)
      }
    },

    //初始化流程
    initWorkFlow(req) {
      const that = this
      initWorkFlowService(req)
        .then((res) => {
          let n = res
          // 查询成功的情况
          if (res && res.code == '0000') {
            that.$emit('getNodeData', n.data)
            that.auditOptionController = n.data.auditOptionController
            that.flowTypeName = n.data.flowTypeName
            that.opList = n.data
            that.subInstanceId = n.data.subInstanceId
            that.taskid = n.data.taskId
            that.nodeCode = n.data.nodeCode || 'draft'
            that.nodeName = n.data.nodeName
            that.nodeType = n.data.nodeType
            that.currentNodeId = n.data.nodeId
            that.workflowId = n.data.workflowId
            that.dealFormat(n.data.fieldhash) //处理必填校验
            that.setPersonNodes = n.data.setPersonNodes
          }
          // 查询失败的情况
          else {
            if(that.$parent.dealPage){
              that.$parent.dealPage = false
            }else{
              let url = that.$route.fullPath.replace('edit', 'view')
              that.$closeTagView({
                close: that.$route.fullPath,
                to: url
              })
            }
            that.edit = false
          }
        })
        .catch((e) => {
          this.$message({ message: e, type: 'error' })
        })
    },
    overWorkflow() {},
    /**
     * 打开提交弹窗并设置默认用户
     * @param userInfo
     */
    opendialogInitNextPathHasDefaultUser(userInfo) {
      this.chooseNames[0] = userInfo.realName
      this.chooseIds[0] = userInfo.userId
      this.opendialogInitNextPath()
    },
    opendialogInitNextPath() {
      if (
        this.$parent &&
        this.$parent.$refs.mainForm &&
        this.$parent.$refs.mainForm.$refs.form
      ) {
        let mainForm = this.$parent.$refs.mainForm.$refs.form
        mainForm.validate((valid) => {
          if (valid) {
            this.getAllPaths(this.boId || this.beforeHandFormId, this.opList)
          }
        })
      } else {
        this.getAllPaths(this.boId || this.beforeHandFormId, this.opList)
      }
    },
    // 查询下一步们
    getAllPaths(boId, opData) {
      const that = this
      this.nextPathListDataIndexes = []
      this.nextPathListData = []
      this.currentOp = 'submit'
      this.showFlowPath = true
      let reqList = [] // 请求的集合
      let reqParams = {
        account: '',
        boId: boId,
        opStr: '',
        pwd: '',
        userId: this.userId,
        workflowCode: this.workflowCode,
        baParams: this.getBaParams(this.flowTypeName),
        wfParams: this.getChangedWfParams(),
        taskId: this.taskid
      }
      // 如果有签收，那么最多可能有个驳回。签收不需要调用initNextpath,签收直接根据签收子表单code查询子表单即可
      if (opData.signin) {
        this.nextPathListDataIndexes.push('signin')
        this.nextPathListData.push({
          lineName: getPathZhText('signin'),
          formCode: opData.signinFormCode
        })

        // 有签收的时候的reject代表驳回
        if (opData.reject) {
          this.nextPathListDataIndexes.push('rejectNonLine')
          this.nextPathListData.push({
            lineName: getPathZhText('rejectNonLine'),
            formCode: opData.rejectFormCode
          })
        }
      }
      // 没有签收的情况
      else {
        opData.reject = true
        let _reqParamsList = []
        if (opData.submit && opData.reject) {
          let _reqParams = JSON.parse(JSON.stringify(reqParams))
          _reqParams.opStr = ''
          _reqParamsList.push(_reqParams)
        } else {
          if (opData.submit) {
            let _reqParams = JSON.parse(JSON.stringify(reqParams))
            _reqParams.opStr = 'submit'
            _reqParamsList.push(_reqParams)
          }
          if (opData.reject) {
            let _reqParams = JSON.parse(JSON.stringify(reqParams))
            _reqParams.opStr = 'reject'
            _reqParamsList.push(_reqParams)
          }
        }
        _reqParamsList.forEach((reqP) => {
          reqList.push(initNextPathService(reqP))
        })
      }
      if (reqList.length) {
        axios
          .all(reqList)
          .then(
            axios.spread(function () {
              for (let i = 0, len = arguments.length; i < len; i++) {
                let _res = { data: arguments[i] }
                if (_res && _res.data && _res.data.code == '0000') {
                  if (that.continueTemplate()) {
                    that.showDialog = true
                    that.hideEleCheckbox()
                    that.modelVerify()
                  }
                  let data = _res.data.data || []
                  data.forEach((item) => {
                    let linkType = item.linkType == '0' ? 'submit' : 'reject'
                    let pathZhText = getPathZhText(linkType)
                    if (typeof item.msgUserNames === 'undefined') {
                      item.msgUserNames = '' // 抄送人
                    }
                    that.nextPathListDataIndexes.push(linkType)
                    if (!item.lineName && !item.nodeName) {
                      item.lineName = pathZhText
                    }
                    that.nextNodeIds.push(item.nodeId)

                    that.multSelectUserFlag =
                      item.personModel == '1' ? true : false
                    if (that.$parent.beforeNode) {
                      const obj = that.$parent.beforeNode()
                      if (obj && obj[item.nodeCode]) {
                        item.excuterNames = obj[item.nodeCode].userName
                        item.excuterIds = obj[item.nodeCode].userId
                        if (obj[item.nodeCode].userIds) {
                          item.userIds = obj[item.nodeCode].userIds
                        }
                      }
                    }
                  })
                  let pathData = data
                  that.nextPathListData = that.nextPathListData.concat(pathData)
                } else if (_res && _res.data.code == '6000') {
                  that.$message({
                    showClose: true,
                    message: _res.data.msg,
                    type: 'warning'
                  })
                  return
                }
              }
              // 按照dcl0730的要求，这几个排序在后面
              if (!opData.signin) {
                if (opData.transfer) {
                  that.nextPathListDataIndexes.push('transfer')
                }
                if (opData.reply) {
                  that.nextPathListDataIndexes.push('reply')
                }
                if (opData.terminate) {
                  that.nextPathListDataIndexes.push('terminate')
                }
                // 会签
                if (opData.sign) {
                  that.nextPathListDataIndexes.push('sign')
                }
              }

              if (that.nextPathListDataIndexes.length > 0) {
                const optionArrCopy = that.optionArr
                  .map((tab) => {
                    if (that.nextPathListDataIndexes.includes(tab.name)) {
                      return tab
                    }
                  })
                  .filter((item) => {
                    return item
                  })
                that.optionArr = optionArrCopy // 流程页签显示/隐藏
                that.activeName = that.optionArr[0] && that.optionArr[0].name //默认激活第一个
              }

              if (that.nextPathListData.length > 0) {
                if (that.currentOp == 'reject') {
                  that.tableData = that.nextPathListData.filter((item) => {
                    return item.linkType == '1'
                  })
                } else {
                  that.tableData = that.nextPathListData.filter((item) => {
                    return item.linkType == '0'
                  })
                }
              }
              // 设置默认选择路径 defaultPath为true为默认，如果都为false,设置第一条为默认
              if (that.tableData.length > 0) {
                that.$nextTick(() => {
                  if (!that.nodeTypeFlagS) {
                    let sequencenumber
                    that.tableData.forEach((item, index) => {
                      if (index == 0) {
                        sequencenumber = item.sequencenumber
                      }
                      if (item.defaultPath) {
                        sequencenumber = item.sequencenumber
                      }

                      if (that.chooseIds.length > 0 && that.chooseIds[index]) {
                        item.excuterIds = that.chooseIds[index]
                      }
                      if (that.chooseNames.length > 0 && that.chooseNames[index]) {
                        item.excuterNames = that.chooseNames[index]
                      }
                    })
                    that.tableData.forEach((item) => {
                      if (item.sequencenumber == sequencenumber) {
                        that.$refs.flowPathTable[0].toggleRowSelection(
                          item,
                          true
                        )
                      }
                    })
                  } else {
                    that.$refs.flowPathTable[0].toggleRowSelection(
                      that.tableData.find((item, index) => {
                        if (that.chooseIds.length > 0 && that.chooseIds[index]) {
                          item.excuterIds = that.chooseIds[index]
                        }
                        if (that.chooseNames.length > 0 && that.chooseNames[index]) {
                          item.excuterNames = that.chooseNames[index]
                        }
                        return item.defaultPath || that.tableData[0]

                      }),
                      true
                    )
                  }
                })
              }
            })
          )
          .catch((e) => {
            this.$message({ message: e, type: 'error' })
          })
      }
    },
    // 找到流程的业务参数
    getBaParams(flowTypeName) {
      let baPrams = []
      if (this.$parent.setWorkFlowPrams && this.$parent.setWorkFlowPrams()) {
        baPrams = this.$parent.setWorkFlowPrams()
      }
      let name = baPrams.filter((it) => {
        return it.code === 'name'
      })
      if (name && name.length) {
        return JSON.stringify({ name: name[0].value })
      } else if (flowTypeName) {
        return JSON.stringify({ name: flowTypeName })
      } else {
        return ''
      }
    },

    // 找到流程参数的字段们
    getWfParams(list, values) {
      let obj = {}
      list.forEach((it) => {
        if (it.isWfParam) {
          obj[it.prop] = values[it.prop]
        }
      })
      return JSON.stringify(obj)
    },

    // initFlow中获取此节点需要校验的规则（必填、数字等）
    dealFormat(param) {
      let obj = {}
      // 11可写字段，14必填字段。由于表单默认都是可写字段，所以此处不需要特别处理可写字段，另外,必填是通过表单的rules来控制的，故此处也不需要特别处理
      this.readonlyList = [] // 只读字段的集合（12）
      this.hideList = [] // 隐藏字段的集合(13)
      this.mustList = [] // 必填字段
      if (param && param.jsp && param.jsp.length) {
        param.jsp.forEach((item) => {
          if (item.fields && item.fields.length) {
            item.fields.forEach((param) => {
              obj[param.fieldName] = []
              // access说明：11-可写 12-只读 13-隐藏 14-必填.
              if (param.access == '12') {
                this.readonlyList.push(param.fieldName)
              } else if (param.access == '13') {
                this.hideList.push(param.fieldName)
              } else if (param.access == '14') {
                //判断是否必填
                this.mustList.push({
                  code: param.fieldName,
                  cnName: param.cnName,
                  type: param.type
                })
                obj[param.fieldName] = [
                  {
                    required: true,
                    message: `请输入必填信息`,
                    trigger: ['change', 'blur']
                  }
                ]
              }
              if (param.dataType == '2') {
                //判断整形（暂未判断浮点）
                obj[param.fieldName].push({
                  type: 'number',
                  message: `${param.cnName}必须为整形`,
                  trigger: 'blur'
                })
              }
            })
          }
        })
      }
      this.formRules = obj
      this.$emit('initFormRules', this.formRules)
      this.$emit('getReadonlyList', this.readonlyList)
      this.$emit('getHideList', this.hideList)
    },

    // 和工单初始值比较，找到有变化的流程参数的字段们
    getChangedWfParams() {
      let obj = {}
      let workFlowPrams = []
      if (this.$parent.setWorkFlowPrams) {
        workFlowPrams = this.$parent.setWorkFlowPrams()
      }
      const arr = workFlowPrams.filter((item) => item.code != 'name')
      if (arr.length) {
        arr.forEach((item) => {
          obj[item.code] = item.value
        })
      }

      return JSON.stringify(obj)
    },

    tabClick(data) {
      this.currentOp = data.name
      if (this.currentOp == 'terminate' || this.currentOp == 'transfer')
        this.showFlowPath = false
      else this.showFlowPath = true

      if (this.currentOp == 'reject') {
        this.flowForm.content = '不同意'
        this.tableData = this.nextPathListData.filter((item) => {
          return item.linkType == '1'
        })
        this.$nextTick(() => {
          this.$refs.flowPathTable[1].toggleRowSelection(
            this.tableData.find((item) => {
              return item.defaultPath || this.tableData[0]
            }),
            true
          )
        })
      } else {
        this.flowForm.content = '同意'
        this.tableData = this.nextPathListData.filter((item) => {
          return item.linkType == '0'
        })
        this.showFlowPath && this.$nextTick(() => {
          this.$refs.flowPathTable[0].toggleRowSelection(
            this.tableData.find((item) => {
              return item.defaultPath || this.tableData[0]
            }),
            true
          )
        })
      }
      this.editIndex = null
    },

    submitOrder() {
      this.confirmWorkflowHandle()
    },
    closeDialog() {
      this.showDialog = false
    },
    confirmWorkflowHandle() {
      const that = this
      // 表单校验成功后需要做的事情，
      // 调用子表单的校验方法(子表单校验成功后执行cb)（当主表单和子表单都校验通过后，再执行提交或是保存提交）
      const mainFormAfterSubmit = () => {}
      // j检查主表单是否有改变，有则先执行保存
      const saveMainFormIfChanged = (flowOp, data, cb) => {
        // 先保存，再提交子表单
        if (this.saveMonthly && this.currentOp == 'submit') {
          that.$parent.autoSave(function () {
            if (cb && cb.constructor === Function) {
              cb(mainFormAfterSubmit)
            }
          })
        } else if (this.saveMainForm) {
          that.$parent.save(function () {
            if (cb && cb.constructor === Function) {
              cb(mainFormAfterSubmit)
            }
          })
        }
        // 没有变化，直接提交子表单
        else {
          if (cb && cb.constructor === Function) {
            cb(mainFormAfterSubmit) // 主表单的提交后事件作为参数传入，子表单处理完自己的提交后会执行主表单的提交后事件
          }
        }
      }
      let api = null
      switch (that.currentOp) {
        case 'submit':
          api = that.getActionService('completeTask')
          break
        case 'transfer':
          api = that.getActionService('reassignTask')
          break
        case 'reject':
          api = that.getActionService('completeTask')
          break
        case 'terminate':
          api = that.getActionService('terminateTask')
      }
      that.confirmHand(that, saveMainFormIfChanged, api)
    },

    // 操作流程业务接口
    getActionService(action) {
      const that = this
      const actionService = (reqData) => {
        return request({
          method: 'post',
          url: window.g.baseUrl + that.actionUrl + `/${action}`,
          data: reqData
        })
      }
      return actionService
    },

    confirmHand(scope, cb, api) {
      const that = this
      let validateSuccessHandle = function () {
        if (that.currentOp == 'transfer') {
          if (!that.flowForm.excuterNames || that.flowForm.excuterNames == '') {
            that.$message({
              showClose: true,
              message: '请选择转办人',
              type: 'warning'
            })
            return false
          }
        }
        if (
          (!that.pathData || JSON.stringify(that.pathData) == '[]') &&
          that.currentOp != 'transfer'
        ) {
          that.$message({
            showClose: true,
            message: '请至少选择一条流程路径',
            type: 'warning'
          })
          return false
        }
        let queryData = {
          boId: that.boId,
          content: that.flowForm.content,
          op: that.currentOp,
          workflowCode: that.workflowCode,
          userId: that.userId,
          historyData: that.generateHistoryData(),
          taskId: that.taskid
        }
        let datas = []
        let sendMsg = []
        if (that.currentOp == 'transfer') {
          queryData.excuterIds = that.flowForm.excuterIds
          queryData.excuterNames = that.flowForm.excuterNames
        } else {
          that.pathData.forEach((ele, index) => {
            if (that.chooseNames.length > 0 && that.chooseIds[index]) {
              ele.excuterIds = that.chooseIds[index]
            }
            if (that.chooseNames.length > 0 && that.chooseNames[index]) {
              ele.excuterNames = that.chooseNames[index]
            }
            if (!ele.excuterNames && ele.nodeType != 5 && ele.nodeType != 4) {
              sendMsg.push(ele.lineName || ele.nodeName)
            }

            datas.push(ele)
          })
        }
        if (
          sendMsg.length > 0 &&
          that.currentOp != 'transfer' &&
          that.currentOp != 'terminate'
        ) {
          that.$message({
            showClose: true,
            message: `审批环节：${sendMsg.join()}，请选择处理人！`,
            type: 'warning'
          })
          return false
        }
        queryData.datas = JSON.stringify(datas)
        if (cb && cb.constructor === Function) {
          cb(that.currentOp, queryData, (cb) => {
            that.completeTaskToService(scope, queryData, api, cb)
          })
        }
      }
      let innerForm = that.$refs.form[0]
      innerForm.validate((valid, obj) => {
        if (valid) {
          validateSuccessHandle()
        }
      })
    },
    // 提交流程到后端
    completeTaskToService(scope, queryData, api, cb) {
      const that = this
      // 以目前的业务来说，主表单的提交前事件是作为cb传入的，在此处执行一下
      cb && cb.constructor === Function && cb()
      const msgMap = {
        submit: '提交成功',
        transfer: '移交成功',
        reject: '提交成功',
        terminate: '终止成功'
      }
      that.submitDisabled = true
      api(queryData)
        .then((res) => {
          let n = res
          let msg = ''
          that.submitDisabled = false
          // 提交成功
          if (n && n.code == '0000' && n.msg == 'success') {
            that.showDialog = false
            that.$message({
              showClose: true,
              message: msgMap[that.currentOp],
              type: 'success'
            })
            that.flowForm.excuterNames = ''
            that.flowForm.excuterIds = ''
            that.chooseNames = []
            that.chooseIds = []
            var targetWindow = window.opener
            let portalFlag = that.$route.query.portalFlag
            if (portalFlag == '1' && targetWindow != null) {
              //门户待办调转处理(尽量在portal已办同步之后)
              setTimeout(function () {
                targetWindow.postMessage('refreshTodo', '*')
                window.close()
              }, 1000)
            } else {
              // 返回到列表页
              setTimeout(function () {
                that.backHome()
              }, 200)
            }
          }
          // 提交失败
          else {
            // 查询失败
            let errMsg = n.data.msg || '提交失败'
            that.$message({
              showClose: true,
              message: errMsg,
              type: 'warning'
            })
          }
        })
        .catch((e) => {
          this.canSubmit = true
          that.submitDisabled = false
        })
    },

    /**
     * 生成historyData
     * @param {Object} option - 参数对象
     * @param {Boolean} option.activetemplateId - 是否需要当前路径，若需要，则会在返回数据中加上一个activetemplateId：当前路径
     */
    generateHistoryData(option = { activetemplateId: false }) {
      let { activetemplateId } = option
      let list = []
      this.pathData.forEach((ele) => {
        let listChild = []
        listChild.push(
          {
            name: ele.nodeName,
            code: 'nodeName',
            value: ele.nodeName
          },
          {
            name: ele.ruleName,
            code: 'ruleName',
            value: ele.ruleName
          }
        )
        if (activetemplateId) {
          listChild.push({
            name: '发起节点',
            code: 'activetemplateId',
            value: ele.nodeCode
          })
        }
        list.push(listChild)
      })

      return JSON.stringify(list)
    },

    backHome() {
      this.$closeTagView({
        close: this.$route.fullPath,
        to: this.returnAddress
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.icon {
  display: inline-block;
  margin-right: 10px;
}
.dialog-footer {
  .el-button--primary.is-disabled {
    color: #ffffff;
    background-color: #99d6fd;
    border-color: #99d6fd;
    &:hover {
      color: #ffffff;
      background-color: #99d6fd;
      border-color: #99d6fd;
    }
  }
}
</style>
