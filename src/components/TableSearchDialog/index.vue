/**
* @author: ty
* @date: 2023-07-07
* @description: dialog+table的复合型组件。应用场景：比如工程签证的项目查询
*/
<template>
  <el-dialog
    class="table-search-dialog"
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose">
    <div class="table-search-dialog---content">
      <div class="search-form">
        <el-form v-model="searchFormModel">
          <el-input
            v-for="(it, index) in searchFields"
            :key="index"
            v-model="searchFormModel[it.fieldName]"
            :placeholder="it.label"
            class="inner-input"
            clearable></el-input>
        </el-form>
        <!-- <span class="search-icon el-icon el-icon-search" @click="searchHandle"></span> -->
        <el-button class="search-btn" type="primary" @click="searchHandle">查询</el-button>
      </div>
      <mssTable
        class="search-list"
        v-if="tableShow"
        ref="table"
        :api="tableApi"
        :columns="tableColumns"
        :rowKey="rowKey"
        :staticSearchParam="tableStaticSearchParam"
        border
        selection
        :single-choice="tableSingleChoice"
        @tableDataChange="tableDataChangeHandle"
      >
      </mssTable>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmHandle">确定</el-button>
      <el-button @click="cancleHandle">取消</el-button>
  </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'TableSearchDialog',
  props: {
    dialogTitle: {
      type: String,
      default: '选择项目'
    },
    dialogWidth: {
      type: String,
      default: '30%'
    },
    // 列表的查询接口请求
    tableApi: {
      type: Function,
      default: () => {}
    },
    // 列表的查询参数。这个是除开分页和搜索框之外的的查询参数
    tableQueryParams: {
      type: Object,
      default() {
        return {}
      }
    },
    rowKey: {
      type: String,
      default: () => 'id'
    },
    // 查询条件的key值
    searchFields: {
      type: Array,
      default() {
        return [
          {
            label: '项目名称',
            fieldName: 'projectName'
          }
        ]
      }
    },
    searchInputPlaceholder: {
      type: String,
      default: '输入项目名称查询'
    },
    tableColumns: {
      type: Array,
      default() {
        return [
          {
            label: '项目名称',
            prop: 'projectName',
            minWidth: 200
          },
          {
            label: '项目编码',
            prop: 'projectCode',
            minWidth: 100
          },
          {
            label: '施工单位编码',
            prop: 'providerNum',
            minWidth: 100
          },
          {
            label: '建设单位名称',
            prop: 'buildUnitName',
            minWidth: 200
          }
        ]
      }
    },
    // 表格是否单选
    tableSingleChoice: {
      type: Boolean,
      default: false
    },
    // 表格中已经勾选的数据。一般用于在第一次打开表格时，需要勾选的数据
    tableSelectedData: {
      type: Array,
      default() {
        return []
      }
    },
    tableDataKey: {
      type: String,
      default: 'id'
    }
  },
  computed: {
    // tableStaticSearchParam(){
    //   return this.tableQueryParams
    // }
  },
  watch:{
    tableQueryParams(n,o){
      this.tableStaticSearchParam=JSON.parse(JSON.stringify(n))
    }
  },
  data() {
    return {
      dialogVisible: false,
      tableShow: false,
      searchFormModel: {},
      tableStaticSearchParam: {}
    }
  },
  methods: {
    tableDataChangeHandle(tableData){
      if (this.tableSelectedData && this.tableSelectedData.length) {
        const selectedIds = this.tableSelectedData.map(it => it[this.tableDataKey])
        const selectedRows = tableData.filter(it => {
          return selectedIds.includes(it[this.tableDataKey])
        })
        this.$nextTick(_ => {
          this.$refs.table.$refs.table.clearSelection()
          if (selectedRows && selectedRows.length) {
            this.$nextTick(_ => {
              selectedRows.forEach(row => {
                this.$refs.table.$refs.table.toggleRowSelection(row)
              })
            })
          }
        })
      }
    },
    /**
     * 显示弹窗，
     * @param opts 预留配置字段，如果有需要在打开弹窗时携带的配置项
     */
    openDialog(opts = {}) {
      this.tableStaticSearchParam = JSON.parse(JSON.stringify(this.tableQueryParams))
      this.dialogVisible = true
      this.tableShow = true
    },
    searchHandle() {
      this.$refs.table.page.current = 1
      const searchData = {
        ...this.searchFormModel
      }
      // this.staticSearchParam = searchData
      this.tableStaticSearchParam = Object.assign(this.tableStaticSearchParam, searchData)
      this.$refs.table.getTableData(searchData)
    },
    resetAllBeforeClose() {
      this.tableShow = false
      this.searchFormModel = {}
    },
    handleClose(done) {
      this.resetAllBeforeClose()
      done()
    },
    // 点击【取消】
    cancleHandle() {
      this.resetAllBeforeClose()
      this.$emit('cancle')
      this.dialogVisible = false
    },
    // 点击【确定】
    confirmHandle() {
      this.resetAllBeforeClose()
      this.$emit('confirm', this.$refs.table.multipleSelection)
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
  @import "~@/styles/mixin.scss";
  @import "~@/styles/variables.scss";
  .search-form{
    overflow: hidden;
    .inner-input{
      width: 200px;
      float: left;
      margin-right: 7px;
    }
    .search-btn{
      float: right;
    }
    .search-icon{
      display: block;
      width: 26px;
      text-align: center;
      height: 26px;
      line-height: 26px;
      float: right;
      cursor: pointer;
    }
  }
  .search-list{
    margin-top: 10px;
  }
  @media only screen and (max-width: 600px) {
    .search-list{
      ::v-deep .pagination{
        .el-pagination__sizes{
          margin-right: 0;
        }
        .el-pagination__jump{
          display: none;
        }
      }
    }
  }
</style>
