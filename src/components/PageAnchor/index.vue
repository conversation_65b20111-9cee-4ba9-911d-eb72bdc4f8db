/**
* @author: ty
* @date: 2023-07-03
* @description: 页面锚点，注意这个是根据页面元素id来定位的，需要定位的元素务必设置上id
* 使用方式：
* <mssPageAnchor :config="pageAnchorConfig"></mssPageAnchor>
* ......
* pageAnchorConfig: [{text: '基本\<\br\>信息',id: 'sectionBasic',show: true | false},]
* 参数：show: 可选，默认true，显示与否
*/
<template>
  <div class="page-anchor-wrapper">
    <ul class="page-anchor">
      <li
        v-for="(it, index) in config"
        v-show="it.show || true"
        :key="index"
        class="page-anchor-item"
        :class="{'is-active': index === activeIndex}"
        @click="scrollToSection(it.id, index)">
        <span class="page-anchor-item--text" v-html="it.text"></span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'PageAnchor',
  props: {
    config: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      activeIndex: 0
    }
  },
  mounted() {

  },
  methods: {
    scrollToSection(sectionId, index) {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth',block: 'start' }) // 使用平滑滚动效果
        if(index !== undefined){
          this.activeIndex = index
        }
      }
    }
  }
}
</script>
