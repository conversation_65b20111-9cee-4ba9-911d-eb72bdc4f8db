<!--
系统管理公共弹出框组件
create by hcm on 21/4/19
参数说明
1.dialogTitle-----弹框名字
2.dialogWidth-----弹框宽度
3.canSubmit-------是否展示确认按钮
4.footer---------是否自定义footer
5.customClass-----弹框class名
6.needModal----	是否需要遮罩层
 -->
<template>
  <div class="system-dialog">
    <el-dialog
      :visible.sync="showDialog"
      :title="dialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :width="dialogWidth"
      :modal="needModal"
      :show-close="false"
      :top="dialogTop"
      :custom-class="customClass"
      append-to-body
      @closed="closed"
    >
      <i aria-hidden="true" class="dialog__headerbtn" @click="closeModal">
        <span class="el-icon-close"></span>
      </i>
      <div class="sys-dialog-body">
        <slot></slot>
      </div>

      <!-- footer -->
      <span v-if="footer" slot="footer" class="dialog-footer">
        <el-button
          v-if="canSubmit"
          fill-primary
          @click="submitOrder"
        >确 定</el-button>
        <el-button primary border @click="closeDialog">取 消</el-button>
      </span>

      <span v-if="!footer" slot="footer" class="dialog-footer">
        <slot name="footer"></slot>
      </span>

    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SystemDialog',
  props: {
    dialogTitle: {
      type: String,
      default: () => {
        return ''
      }
    },
    dialogTop: {
      type: String,
      default: () => {
        return '15vh'
      }
    },
    dialogWidth: {
      type: String,
      default: () => {
        return '40%'
      }
    },
    canSubmit: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    needModal: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    customClass: {
      type: String,
      default: () => {
        return 'dialogClass'
      }
    },
    footer: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    showDialog: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
    }
  },
  watch: {
  },
  methods: {
    /**
     * 内部关闭弹框
     */
    closeModal() {
      this.$emit('update:showDialog', false)
    },

    /**
     * 触发外部的关闭弹框
     */
    closeDialog() {
      this.$emit('closeDialog')
    },

    /**
     * 确认
     */
    submitOrder() {
      this.$emit('submitOrder', true)
    },
    /**
     * 关闭动画结束时的回调
     */
    closed() {
      this.$emit('closed')
    }
  }
}
</script>

<style lang='scss'>
.dialog__headerbtn {
  cursor: pointer;
    position: absolute;
    top:6px !important;
    right: 10px !important;
    span {
      font-size: 16px;
      font-weight: bold;
    }
  }
</style>
