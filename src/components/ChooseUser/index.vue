<!-- 普通的选择组件 -->
<template>
  <div class="ChooseUser">
    <el-dialog
      :visible.sync="showDialog"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :width="dialogWidth"
      :custom-class="customClass"
      :modal="true"
      append-to-body
      @close="close"
    >
      <div class="choose" :style="{ width: '30%' }">
        <div class="chooseBody includeFoot">
          <el-tree
            v-if="showTree"
            ref="tree"
            :props="defaultProps"
            :load="loadNode"
            lazy
            node-key="id"
            :default-checked-keys="defKeys"
            :check-strictly="true"
            @node-click="handleCheckChange"
          >
            <span slot-scope="{ data }" class="custom-tree-node">
              <span v-if="!data.leaf">
                {{ data.text }}
              </span>
              <span v-if="data.leaf">
                {{ data.text }}
              </span>
            </span>
          </el-tree>
        </div>
        <div class="chooseFoot">
          <div class="choose"></div>
        </div>
      </div>

      <div class="choose" :style="{ width: '40%' }">
        <div class="chooseBody includeFoot">
          <el-input
            v-model="keyInput"
            class="key-search"
            placeholder="请输入关键字查询"
            @keyup.enter.native="keyWordsSearch"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              style="cursor: pointer"
              title="查询"
              @click="keyWordsSearch"
            ></i>
          </el-input>
          <mssTable
            ref="table"
            get-change
            :serial="false"
            :columns="columns"
            row-key="userId"
            selection
            hideAllSelection
            :stationary="tableData"
            :single-choice="!multSelect"
            table-size="mini"
            highlight-current-row
            :pagination="false"
            @rowChecked="selectHandle"
          />
        </div>
        <div class="chooseFoot" style="padding:0">
          <el-pagination
            small
            :current-page.sync="page.current"
            :page-size="page.size"
            background
            :pager-count="5"
            :layout="pageLayout"
            :total="page.total"
            @current-change="currentChange"
          />
        </div>
      </div>
      <div class="choose" :style="{ width: '30%' }">
        <div class="chooseTitle">
          <span class="iconfont">选择结果</span>
          <span class="tips">(双击名字删除)</span>
        </div>
        <div class="chooseBody">
          <el-checkbox-group v-model="delList">
            <el-checkbox
              v-for="(item, index) in checkList"
              :key="index"
              class="showList"
              :label="item.realName"
            >
              <span @dblclick="delPeople(item, 'single')">
                {{
                  item.realName
                }}
              </span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="chooseFoot">
          <el-checkbox v-model="checkedAll" @change="checkAll">全选</el-checkbox>
          <span class="del" @click="delPeople(delList, 'multiple')">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitOrder">确 定</el-button>
        <el-button size="mini" style="margin-left: 10px" @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDeptService, getUserListService } from '@/api/choose_dept'
import {
  getRootService,// 获取根
} from '@/api/system_settings/role'
import { validEmail } from '@/utils/validate'
export default {
  name: 'ChooseUser',
  props: {
    // 是否多选
    multSelect: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return '100%'
      }else{
        return '50%'
      }
    },
    customClass(){
      // 自动检测是否为手机端，手机端的加一个class
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return 'is-mobile-dialog'
      }
    },
    pageLayout(){
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return "prev,next,total"
      }else{
        return "prev,pager,next,total"
      }
    }
  },
  data() {
    return {
      showTree: false,
      title: '选择人员',
      checkList: [],
      delList: [],
      defKeys: [],
      checkedAll: false, // 是否全选
      resolveData: [],
      areaTreeData: '', // 组织结构数据
      defaultProps: {
        label: 'text',
        children: 'children',
        isLeaf: 'leaf'
      },
      columns: [{ prop: 'realName', label: '名称' }],
      tableData: [],
      tableParams: {},
      page: {
        total: 0,
        size: 10,
        current: 1
      },
      keyInput: '',
      deptParams: {},
      personParams: {},
      showDialog: false
    }
  },
  methods: {
    /**
     * 弹框初始化
     * @param item
     * @param val
     */
    init(item, deptParams, personParams) {
      this.showTree = true
      this.checkList = []
      this.keyInput = ''
      if (item && item.excuterNames&& item.excuterIds) {
        const excuterNames = item.excuterNames.split(',')
        const excuterIds = item.excuterIds.split(',')
        excuterNames.forEach((ele, index) => {
          this.checkList.push({
            realName: ele,
            userId: excuterIds[index]
          })
        })
      }
      this.treeCheck()
      this.showDialog = true
      this.deptParams = deptParams
      this.personParams = personParams
      this.tableParams = { orgChildId: deptParams?.orgChildId || '-1',status:'1' }
      this.getUserList(this.tableParams)
    },

    /**
     * 提交表单
     */
    submitOrder() {
      this.showDialog = false
      this.$emit('showCheckList', {
        checkList: this.checkList
      })
      this.defKeys = []
    },

    /**
     * 树状图展开
     * @param node
     * @param resolve
     * @returns {*}
     */
    loadNode(node, resolve) {
      let params
      if (node.level === 0) {
        if(this.deptParams && this.deptParams.rootId && this.deptParams.rootId !='-1'){
          getRootService(this.deptParams.rootId).then(res=>{
            if(res.code=='0000'){
              this.areaTreeData=[{
                "id": res.data.deptId,
                "parentId": res.data.parentDeptId,
                "children": [],
                "text": res.data.deptName,
                "simpleName": res.data.deptSimpleName,
                "code": res.data.deptCode,
                "status": res.data.status,
                "hasChlid": true
              }]
              return resolve(this.areaTreeData)
            }
          })
        }else{
          this.getTreeList(
            { isReturnUser: '0', parentId: '', rootId: '-1' },
            () => {
              return resolve(this.areaTreeData)
            }
          )
        }
      } else {
        if (node.data && node.data.id) {
          params = {
            isReturnUser: '0',
            parentId: node.data.id,
            rootId: '-1'
          }
        } else {
          params = {
            isReturnUser: '0',
            parentId: '',
            rootId: '-1'
          }
        }
        setTimeout(() => {
          this.getTreeList(params, () => {
            if (this.areaTreeData.length > 0) {
              resolve(this.areaTreeData)
            } else {
              return resolve([])
            }
          })
        }, 200)
      }
    },

    /**
     * 获取树状列表数据
     * @param params
     * @param callback
     */
    getTreeList(params, callback) {
      getDeptService({ ...params, ...this.deptParams })
        .then((res) => {
          if (res && res.code === '0000') {
            this.areaTreeData = res.data || []
          }
          callback()
        })
        .catch(() => {
          this.tableLoading = false
        })
    },

    /**
     * 获取人员列表数据
     * @param params
     */
    getUserList(params) {
      getUserListService({ ...params, ...this.personParams })
        .then((res) => {
          if (res.data && res.code === '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = data
          } else {
            this.tableData = []
          }
          this.tableCheck()
        })
        .catch(() => {
          this.tableLoading = false
          this.tableData = []
        })
    },

    keyWordsSearch() {
      this.getUserList({
        ...this.tableParams,
        searchValue: this.keyInput
      })
    },
    selectHandle(selection, row) {
      if (!this.multSelect) {
        this.checkList = [row]
      } else {
        let flag = false
        // 是否选中
        selection.forEach((item) => {
          if (item.userId === row.userId) {
            flag = true
          }
        })
        if (flag) {
          // 查询是否存在数组中，添加没有之前选择的数据
          let flag1 = false
          this.checkList.forEach((item) => {
            if (item.userId === row.userId) {
              flag1 = true
            }
          })
          !flag1 && this.checkList.push(row)
        } else {
          // 删除取消选择的数据
          for (let index = 0; index < this.checkList.length; index++) {
            const element = this.checkList[index]
            if (element.userId === row.userId) {
              this.checkList.splice(index, 1)
              break
            }
          }
        }
      }
    },
    /**
     * 树数据复选框点击事件
     * @param val
     * @param checked
     */
    handleCheckChange(val, checked) {
      this.tableParams.orgChildId = val.id
      this.getUserList({ ...this.tableParams, searchValue: this.keyInput })
    },

    currentChange(page) {
      this.getUserList({
        ...this.tableParams,
        limit: 10,
        page: page,
        searchValue: this.keyInput
      })
    },

    /**
     * 删除事件
     * @param val
     * @param type
     */
    delPeople(val, type) {
      this.defKeys = [] // 树回显数据
      // 单个删除
      if (type === 'single') {
        this.checkList.splice(this.checkList.indexOf(val), 1)
      } else if (type === 'multiple') {
        for (var j = 0; j < val.length; j++) {
          for (var i = 0; i < this.checkList.length; i++) {
            if (this.checkList[i].realName === val[j]) {
              this.checkList.splice(i, 1)
              i -= 1
            }
          }
        }
      }
      this.checkedAll = false // 重置全选
      this.delList = []
      this.treeCheck()
      this.tableCheck()
    },

    /**
     * 树回显勾选事件
     */
    treeCheck() {
      this.defKeys = []
      this.checkList.forEach((item) => {
        this.defKeys.push(item.userId)
      })
      this.defKeys.concat()
      this.$nextTick(function() {
        this.$refs.tree.setCheckedKeys(this.defKeys)
      })
    },
    // 表格勾选
    tableCheck() {
      this.$nextTick(() => {
        this.$refs.table.$refs.table.clearSelection()
        this.tableData.forEach(item => {
          this.checkList.forEach(item2 => {
            if (item.userId === item2.userId) {
              this.$refs.table.$refs.table.toggleRowSelection(item, true)
            }
          })
        })
      })
    },

    /**
     * 全选事件
     * @param val
     */
    checkAll(val) {
      if (validEmail) {
        this.checkList.forEach((item) => {
          this.delList.push(item.realName)
        })
      } else {
        this.delList = []
      }
    },
    closeDialog() {
      this.showDialog = false
    },
    close(){
      this.showTree = false
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .choose {
  display: inline-block;
  .iconfont {
    color: #5887ff;
    font-weight: 400;
    margin-right: 0.5rem;
  }
  .chooseTitle {
    height: 20px;
    line-height: 20px;
    color: #000;
    font-weight: bold;
    padding-left: 0.5rem;
    .iconfont {
      font-size: 13px;
    }
    .tips {
      font-size: 12px;
      font-weight: normal;
      color: #ff5a09;
      margin-left: 0.5rem;
    }
  }
  .chooseBody {
    width: 100%;
    height: 23rem;
    background: #ffffff;
    border: 1px solid #dddddd;
    margin-top: 0.7rem;
    padding: 0.2rem;
    overflow: auto;
    .showList {
      display: block;
      padding: 0.2rem 1rem;

      &:hover {
        background: #ebf1ff;
      }
    }
  }
  .chooseFoot {
    height: 2rem;
    line-height: 2rem;
    width: 100%;
    background: #f9fbff;
    border: 1px solid #dddddd;
    border-top: none;
    padding: 0 1rem;
    .el-pagination {
      padding: 0;
      .el-pager {
        height: 22px;
      }
    }
    .el-checkbox__label {
      font-size: 0.6rem;
    }
  }
  .del {
    float: right;
    cursor: pointer;
    font-size: 1.4rem;
    color: #5887ff;
  }
}
</style>
