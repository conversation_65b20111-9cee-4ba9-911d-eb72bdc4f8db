<!--
 * @Descripttion: card组件
 * @Author: hcm
 * @Date: 2023-06-29
-->
<template>
  <el-card class="card">
    <div slot="header" class="title">
      <div class="left-wrap">
        <i class="iconfont icon-dasuolvetuliebiao"></i>
        <span>{{title}}</span>
      </div>
      <div class="right-wrap">
        <slot name="headerBtn"></slot>
      </div>
    </div>
    <div class="content">
      <slot name="content"></slot>
    </div>
  </el-card>
</template>
<script>
export default {
  name:"Card",
  props:{
    title:{
      type:String,
      default:"card头部头部"
    }
  },
}
</script>