<template>
  <div class="WorkFlowHistory">
    <el-button class="flow-btn" size="mini" type="primary" @click="openFlow">查看流程图</el-button>
    <mssTable
      ref="commonTable"
      :columns="tableHeader"
      :stationary="tableData"
      :pagination="false"
      :serial="false"
    ></mssTable>
    <flowDialog :key="flowKey" ref="flowDialog" @closeFlowDialog="closeFlowDialog"></flowDialog>
  </div>
</template>

<script>
import {
  getHistorysService,
  getCurrentTasksService,
  getWorkflowByBoId
} from '@/api/workFlow'
import flowDialog from '@/views/home/<USER>'
export default {
  name: 'WorkFlowHistory',
  components: { flowDialog },
  props: {
    boId: {
      type: String,
      default: () => ''
    },
    workflowCode: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      tableHeader: [
        {
          prop: 'orgName',
          label: '部门',
          align: 'center'
        },
        {
          prop: 'nodeName',
          label: '流程环节',
          align: 'center'
        },
        {
          prop: 'userName',
          label: '处理人',
          align: 'center'
        },
        {
          prop: 'handerDate',
          label: '处理时间',
          align: 'center'
        },
        {
          prop: 'opstr',
          label: '选择决策',
          align: 'center'
        },
        {
          prop: 'handlerContent',
          label: '处理意见',
          align: 'center'
        }
      ],
      tableData: [],
      flowKey:1
    }
  },
  created() {
    this.getWorkFlowHistory()
  },
  methods: {
    getWorkFlowHistory() {
      const req = {
        boId: this.boId,
        workflowCode: this.workflowCode,
        userId: sessionStorage.getItem('userId')
      }
      getHistorysService(req).then((res) => {
        if (res && res.code === '0000' && res.data) {
          this.tableData = res.data
          getCurrentTasksService(req).then((res) => {
            if (res && res.code === '0000' && res.data) {
              this.tableData.push(...res.data)
            }
          })
        }
      })
    },
    openFlow() {
      const req = {
        boId: this.boId,
        workflowCode: this.workflowCode
      }
      getWorkflowByBoId(req).then((res) => {
        if (res && res.code == '0000') {
          let data = {
            ...req,
            workflowId: res.data
          }
          this.$refs.flowDialog.init(data)
        }
      })
    },
    closeFlowDialog() {
      this.flowKey++
    },
  }
}
</script>
<style lang="scss">
.el-select {
  width: 80%;
  margin-right: 20px;
}
.flow-btn {
  float: right;
  margin-top: -57px;
}
.fileInput {
  display: none;
}
</style>
