<template>
  <div class="common-form" :class="wrapperClass">
    <el-form
      ref="form"
      :model="modelForm"
      :label-width="labelPosition=='left'?'': labelWidth"
      :disabled="disableForm"
      :rules="labelPosition=='left'? {} : rules"
      :class="labelPosition=='left'?'detail':'edit'"
      :label-position="labelPosition"
    >
      <el-row style="width: 100%;">
        <template v-for="(item, index) in config">
          <el-col
            v-if="item.show||true"
            v-show="!item.hide"
            :key="index"
            :span="item.span || 12"
            :offset="item.offset || 0"
          >
            <el-form-item
              :ref="item.prop"
              :label="item.label"
              :class="item.class"
              :prop="item.prop"
              :rules="item.rules"
              :title="item.title"
            >
              <div v-if="item.type === 'input' && labelPosition=='left'"
                   class="content-input"
                   :title="modelForm[item.prop]">{{modelForm[item.prop]}}</div>
              <el-input
                v-if="(item.type === 'input' && labelPosition!='left') || item.type === 'textarea'"
                v-model="modelForm[item.prop]"
                :type="item.mode || 'text'"
                :maxlength="item.maxlength"
                :show-word-limit="item.showWordLimit"
                :show-password="item.showPassword"
                :clearable="item.clearable"
                :prefix-icon="item.prefixIcon"
                :suffix-icon="item.suffixIcon"
                :rows="item.rows || 2"
                :autosize="item.autosize"
                :resize="item.resize"
                :disabled="item.disabled"
                :readonly="item.readonly || false"
                :placeholder="item.placeholder || ''"
                v-on="item.eventListeners"
                @click.native="item.clickNative ? clickNative(item) : ''"
              />

              <!-- 单选 -->
              <el-radio-group
                v-if="item.type === 'radio'"
                v-model="modelForm[item.prop]"
                :disabled="item.disabled"
                v-on="item.eventListeners"
              >
                <el-radio
                  v-for="(option, index) in item.options"
                  :key="index"
                  :label="option.value"
                  :border="item.border"
                  :name="item.prop"
                  :disabled="option.disabled"
                >{{ option.label }}</el-radio>
              </el-radio-group>
              <!-- 多选 -->
              <el-checkbox-group
                v-if="item.type === 'checkbox'"
                v-model="modelForm[item.prop]"
                :disabled="item.disabled"
                v-on="item.eventListeners"
              >
                <el-checkbox
                  v-for="(option, index) in item.data"
                  :key="index"
                  :border="item.border"
                  :label="
                    item.itemAs
                      ? option
                      : option[item.itemValue ? item.itemValue : 'id']
                  "
                  :name="item.prop"
                  :disabled="option.disabled"
                >{{ option[item.itemLabel ? item.itemLabel : "name"] }}</el-checkbox>
              </el-checkbox-group>
              <!-- 下拉选择 -->
              <el-select
                v-if="item.type === 'select'"
                v-model="modelForm[item.prop]"
                :placeholder="item.placeholder || ''"
                filterable
                :clearable="item.clearable"
                :value-key="item.itemValue ? item.itemValue : 'id'"
                :multiple="item.multiple"
                :multiple-limit="item.multipleLimit"
                :collapse-tags="item.collapseTags"
                :disabled="item.disabled"
                v-on="item.eventListeners"
              >
                <el-option
                  v-for="option in item.options"
                  :key="option.value"
                  :label="option[item.itemLabel ? item.itemLabel : 'label']"
                  :value="
                    item.itemAs
                      ? option
                      : option[item.itemValue ? item.itemValue : 'value']
                  "
                  :disabled="option.disabled"
                />
              </el-select>
              <!-- 开关 -->
              <el-switch
                v-if="item.type === 'switch'"
                v-model="modelForm[item.prop]"
                :width="item.width || 60"
                :active-color="item.activeColor"
                :inactive-color="item.inactiveColor"
                :active-text="item.activeText"
                :inactive-text="item.inactiveText"
                v-on="item.eventListeners"
              />
              <!-- 时间选择 -->
              <el-time-picker
                v-if="item.type === 'timePicker'"
                v-model="modelForm[item.prop]"
                :is-range="item.isRange"
                :readonly="item.readonly"
                :disabled="item.disabled"
                :arrow-control="item.arrowControl"
                :value-format="item.valueFormat || 'HH:mm:ss'"
                :picker-options="{ selectableRange: item.selectableRange }"
                :placeholder="item.placeholder"
                v-on="item.eventListeners"
              />
              <!-- 日期选择 -->
              <div v-if="item.type === 'datePicker'">
                <el-date-picker
                  v-if="item.daterange"
                  v-model="modelForm[item.prop]"
                  :placeholder="item.placeholder"
                  :type="item.dateType || 'daterange'"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :format="item.format || 'yyyy-MM-dd HH:mm:ss'"
                  :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                  :disabled="item.disabled"
                  :readonly="item.readonly || false"
                  :picker-options="item.pickerOptions"
                  v-on="item.eventListeners"
                />
                <el-date-picker
                  v-else
                  v-model="modelForm[item.prop]"
                  :placeholder="item.placeholder"
                  :type="item.dateType || 'date'"
                  :format="item.format || 'yyyy-MM-dd'"
                  :value-format="item.valueFormat || 'yyyy-MM-dd'"
                  :disabled="item.disabled"
                  :picker-options="item.pickerOptions"
                  :clearable="item.clearable"
                  :readonly="item.readonly || false"
                  v-on="item.eventListeners"
                />
              </div>
            </el-form-item>
          </el-col>
        </template>

      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'CommonForm',
  props: {
    wrapperClass: {
      type: String,
      default: 'base-form'
    },
    rules: {
      type: Object,
      default: () => {
        return {}
      }
    },
    config: {
      type: Array,
      default: () => []
    },
    form: {
      type: Object,
      default: () => {}
    },
    isChange: {
      type: Boolean,
      default: false
    },
    disableForm: { // 是否禁用该表单内的所有组件。默认不禁用
      type: Boolean,
      default: false
    },
    labelPosition: { // label的方向
      type: String,
      default: 'top'
    },
    labelWidth: {
      type: String,
      default: '120px'
    }
  },
  data() {
    return {
      modelForm: { ...this.form }
    }
  },
  watch: {
    form: {
      handler(newValue, oldValue) {
        this.modelForm = Object.assign({}, this.modelForm, newValue)
      },
      deep: true
    },
    modelForm: {
      handler(newValue, oldValue) {
        if (this.isChange) {
          this.$emit('onChange', newValue)
        }
      },
      deep: true
    }
  },
  beforeDestroy() {
    this.$refs.form.resetFields()
  },
  methods: {
     // 输入框的点击事件，可以打开选择框
    clickNative(obj){
      this.$emit('clickNative', obj)
    },
  }
}
</script>
<style  lang="scss">
  .el-input.is-disabled .el-input__inner ,.el-textarea.is-disabled .el-textarea__inner{
    color: #000 ;
  }
</style>
