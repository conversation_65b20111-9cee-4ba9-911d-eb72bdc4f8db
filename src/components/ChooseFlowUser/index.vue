<!-- 选择流程人员 -->
<template>
  <div class="ChooseFlowUser">
    <el-dialog
      :visible.sync="showDialog"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :width="dialogWidth"
      :custom-class="customClass"
      :modal="true"
      append-to-body
    >
      <div class="choose" :style="{ width: '30%' }">
        <div class="chooseBody includeFoot">
          <el-tree
            ref="tree"
            v-if="showTree"
            :props="defaultProps"
            :load="loadNode"
            lazy
            node-key="deptId"
            :default-checked-keys="defKeys"
            :check-strictly="true"
            @node-click="handleCheckChange"
          >
            <span class="custom-tree-node" slot-scope="{ data }">
              <span v-if="data.hasChild">
                {{ data.text }}
              </span>
              <span v-if="!data.hasChild">
                {{ data.text }}
              </span>
            </span>
          </el-tree>
        </div>
        <div class="chooseFoot">
          <div class="choose"></div>
        </div>
      </div>

      <div class="choose" :style="{ width: '40%' }">
        <div class="chooseBody includeFoot">
          <el-input
            v-model="keyInput"
            class="key-search"
            placeholder="请输入关键字查询"
            @keyup.enter.native="keyWordsSearch"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              style="cursor: pointer"
              @click="keyWordsSearch"
              title="查询"
            ></i>
          </el-input>
          <mssTable
            getChange
            :serial="false"
            :columns="columns"
            rowKey="userId"
            selection
            :stationary="tableData"
            :singleChoice="!multSelect"
            tableSize="mini"
            highlight-current-row
            :pagination="false"
            @rowChecked="selectHandle"
          />
        </div>
        <div class="chooseFoot">
          <el-pagination
            small
            :current-page.sync="page.current"
            :page-size="page.size"
            background
            :layout="pageLayout"
            :total="page.total"
            @current-change="currentChange"
          />
        </div>
      </div>
      <div class="choose" :style="{ width: '30%' }">
        <div class="chooseTitle">
          <span class="iconfont">选择结果</span>
          <span class="tips">(双击名字删除)</span>
        </div>
        <div class="chooseBody">
          <el-checkbox-group v-model="delList">
            <el-checkbox
              class="showList"
              v-for="(item, index) in checkList"
              :label="item.name"
              :key="index"
            >
              <span @dblclick="delPeople(item, 'single')">
                {{
                item.name
                }}
              </span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="chooseFoot">
          <el-checkbox @change="checkAll" v-model="checkedAll">全选</el-checkbox>
          <span class="del" @click="delPeople(delList, 'multiple')">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitOrder">确 定</el-button>
        <el-button size="mini" @click="closeDialog" style="margin-left: 10px">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDeptTreeListService, getUserListService } from '@/api/workFlow'
export default {
  name: 'ChooseFlowUser',
  props: {
    // 是否多选
    multSelect: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  computed: {
    dialogWidth() {
      // 自动检测是否为手机端，手机端这个弹窗显示大一些
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return '100%'
      }else{
        return '50%'
      }
    },
    customClass(){
      // 自动检测是否为手机端，手机端的加一个class
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return 'is-mobile-dialog'
      }
    },
    pageLayout(){
      if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){
        return "prev,next,total"
      }else{
        return "prev,pager,next,total"
      }
    }
  },
  data() {
    return {
      showTree: false,
      title: '选择人员',
      checkList: [],
      delList: [],
      defKeys: [],
      checkedAll: false, //是否全选
      resolveData: [],
      areaTreeData: '', // 组织结构数据
      defaultProps: {
        label: 'name',
        children: 'children',
        isLeaf: 'hasChild'
      },
      columns: [{ prop: 'name', label: '名称' }],
      tableData: [],
      tableParams: {},
      page: {
        total: 0,
        size: 10,
        current: 1
      },
      keyInput: '',
      deptParams: {},
      personParams: {},
      showDialog: false
    }
  },
  methods: {
    /**
     * 弹框初始化
     * @param item
     * @param val
     */
    init(item, deptParams, personParams) {
      this.showTree = true
      this.checkList = []
      this.keyInput = ''
      if (item && item.excuterNames) {
        let excuterNames = item.excuterNames.split(',')
        let excuterIds = item.excuterIds.split(',')
        excuterNames.forEach((ele, index) => {
          this.checkList.push({
            name: ele,
            userId: excuterIds[index]
          })
        })
      }
      this.treeCheck()
      this.showDialog = true
      this.deptParams = deptParams
      this.personParams = personParams
      this.tableParams = { orgId: '-2' }
      this.getUserList(this.tableParams)
    },

    /**
     * 提交表单
     */
    submitOrder() {
      this.showDialog = false
      this.$emit('showCheckList', {
        checkList: this.checkList
      })
      this.defKeys = []
    },

    /**
     * 树状图展开
     * @param node
     * @param resolve
     * @returns {*}
     */
    loadNode(node, resolve) {
      let params
      if (node.level === 0) {
        params = {
          parentId: '-1'
        }
      } else {
        params = {
          parentId: node.data.id
        }
      }
      this.getTreeList(params, () => {
        resolve(this.areaTreeData)
      })
    },

    /**
     * 获取树状列表数据
     * @param params
     * @param callback
     */
    getTreeList(params, callback) {
      getDeptTreeListService({ ...params, ...this.deptParams })
        .then((res) => {
          if (res.data && res.code == '0000') {
            this.areaTreeData = res.data
            this.areaTreeData.forEach((item, index) => {
              if (item.hasChlid) {
                item.leaf = false
              } else {
                item.leaf = true
              }
            })
          }
          callback()
        })
        .catch(() => {
          this.tableLoading = false
        })
    },

    /**
     * 获取人员列表数据
     * @param params
     */
    getUserList(params) {
      getUserListService({ ...params, ...this.personParams })
        .then((res) => {
          if (res.data && res.code == '0000' && res.data.data) {
            const data = res.data.data
            this.page.total = Number(res.data.total)
            this.tableData = data
          } else {
            this.tableData = []
          }
        })
        .catch(() => {
          this.tableLoading = false
          this.tableData = []
        })
    },

    keyWordsSearch() {
      this.getUserList({
        ...this.tableParams,
        searchValue: this.keyInput
      })
    },
    selectHandle(selection, row) {
      if (!this.multSelect) {
        this.checkList = [row]
      } else {
        let flag = false
        // 是否选中
        selection.forEach((item) => {
          if (item.userId == row.userId) {
            flag = true
          }
        })
        if (flag) {
          // 查询是否存在数组中，添加没有之前选择的数据
          let flag1 = false
          this.checkList.forEach((item) => {
            if (item.userId == row.userId) {
              flag1 = true
            }
          })
          !flag1 && this.checkList.push(row)
        } else {
          // 删除取消选择的数据
          for (let index = 0; index < this.checkList.length; index++) {
            const element = this.checkList[index]
            if (element.userId == row.userId) {
              this.checkList.splice(index, 1)
              break
            }
          }
        }
      }
    },
    /**
     * 树数据复选框点击事件
     * @param val
     * @param checked
     */
    handleCheckChange(val, checked) {
      this.tableParams.orgId = val.id
      this.getUserList(this.tableParams)
    },

    currentChange(page) {
      this.getUserList({
        ...this.tableParams,
        limit: 10,
        page: page,
        searchValue: this.keyInput
      })
    },

    /**
     * 删除事件
     * @param val
     * @param type
     */
    delPeople(val, type) {
      this.defKeys = [] //树回显数据
      //单个删除
      if (type === 'single') {
        this.checkList.splice(this.checkList.indexOf(val), 1)
      } else if (type === 'multiple') {
        for (var j = 0; j < val.length; j++) {
          for (var i = 0; i < this.checkList.length; i++) {
            if (this.checkList[i].name == val[j]) {
              this.checkList.splice(i, 1)
            }
          }
        }
      }
      this.checkedAll = false // 重置全选
      this.delList = []
      this.treeCheck()
    },

    /**
     * 树回显勾选事件
     */
    treeCheck() {
      this.defKeys = []
      this.checkList.forEach((item) => {
        this.defKeys.push(item.userId)
      })
      this.defKeys.concat()
      this.$nextTick(function () {
        this.$refs.tree.setCheckedKeys(this.defKeys)
      })
    },

    /**
     * 全选事件
     * @param val
     */
    checkAll(val) {
      if (val == true) {
        this.checkList.forEach((item) => {
          this.delList.push(item.name)
        })
      } else if (val == false) {
        this.delList = []
      }
    },
    closeDialog() {
      this.showDialog = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .choose {
  display: inline-block;
  .iconfont {
    color: #5887ff;
    font-weight: 400;
    margin-right: 0.5rem;
  }
  .chooseTitle {
    height: 20px;
    line-height: 20px;
    color: #000;
    font-weight: bold;
    padding-left: 0.5rem;
    .iconfont {
      font-size: 13px;
    }
    .tips {
      font-size: 12px;
      font-weight: normal;
      color: #ff5a09;
      margin-left: 0.5rem;
    }
  }
  .chooseBody {
    width: 100%;
    height: 23rem;
    background: #ffffff;
    border: 1px solid #dddddd;
    margin-top: 0.7rem;
    padding: 0.2rem;
    overflow: auto;
    .showList {
      display: block;
      padding: 0.2rem 1rem;

      &:hover {
        background: #ebf1ff;
      }
    }
  }
  .chooseFoot {
    height: 2rem;
    line-height: 2rem;
    width: 100%;
    background: #f9fbff;
    border: 1px solid #dddddd;
    border-top: none;
    padding: 0 1rem;
    .el-pagination {
      padding: 0;
      .el-pager {
        height: 22px;
      }
    }
    .el-checkbox__label {
      font-size: 0.6rem;
    }
  }
  .del {
    float: right;
    cursor: pointer;
    font-size: 1.4rem;
    color: #5887ff;
  }
}
</style>
