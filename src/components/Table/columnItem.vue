<template>
  <el-table-column
    v-if="!column.hidden"
    :prop="column.prop || ''"
    :label="column.label || ''"
    :formatter="column.formatter"
    :width="column.width"
    :min-width="column.minWidth"
    :fixed="column.fixed || false"
    :sortable="column.sortable"
    :align="column.align || 'center'"
    :show-overflow-tooltip="(column.tooltip || false) && tooltip"
    :class-name="!tooltip?'no-tooltip':''"
  >
  
    <!--    表头自定义化，接入提示语 #header是新写法与slot='header' slot-scope="slot"是一样的效果-->
    <template v-if="column.headerTitle" #header>
      <div>{{ column.label }}
        <span>
            <el-tooltip placement="bottom">
              <div slot="content">
                <span>{{ column.headerTitle }}</span>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
         </span>
      </div>
    </template>
    <template v-else #header>
      <span>{{ column.label.split('/')[0] }}</span>
      <br>
      <span>{{ column.label.split('/')[1] }}</span>
    </template>
    <template v-if="column.multilevelColumn && column.multilevelColumn.length">
      <template v-for="(columnItem, index) in column.multilevelColumn">
        <columnItem :key="index" :column="columnItem" />
      </template>
    </template>
    <template v-for="item in column.children">
      <tableColumn
        v-if="item.children && item.children.length"
        :key="item.id"
        :coloumn-header="item"
      ></tableColumn>
      <el-table-column
        v-else
        :key="item.prop"
        :label="item.label"
        :formatter="item.formatter"
        :min-width="item.minWidth"
        :prop="item.prop"
        align="center"
        :width="item.width"
        >
      </el-table-column>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: "columnItem",
  props: {
    column: {
      type: Object,
      default: () => {},
    },
    log: {
      type: String,
      default: "",
    },
    tooltip:{
      type:Boolean,
      default:true
    }
  },
  data() {
    return {};
  },
};
</script>

<style></style>
