import 'core-js/stable'
import 'regenerator-runtime/runtime'

import Vue from 'vue'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/icon.css'
import './styles/element-variables.scss'
import './styles/icon-fix.css'
import '@/assets/font/iconfont.css'// 图标
import '@/styles/index.scss' // global css

import App from './App'
import '@/api/common.js'
import store from './store'
import router from './router'

import './permission' // permission control

import * as filters from './filters' // global filters

import '@/components/index.js'
Vue.use(Element)
import moment from 'moment'// 不提供按需导入所以在Vue.config.js 配置 moment.js按需导入各类语言包
Vue.prototype.$moment = moment
// 引入Echarts
import echarts from 'echarts'
Vue.prototype.$echarts = echarts
Vue.prototype.$closeTagView = ({close, to}) => {
  if((/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))){
    try{
      window.nativeFunction.closeCurrentVC();
    }catch(e){}

    return false;
  }

  const fullPath = close
  const viewsList = store.state.tagsView.visitedViews
  const currentView = viewsList.find(it => {
    return it.fullPath === fullPath
  })
  store.dispatch('tagsView/delView', currentView).then(({ visitedViews }) => {
    try {
      if(fullPath && fullPath.includes('&fromPath=unifyTodo')){
        router.push('/home')
      }else{
        router.push(to)
      }
    } catch (e) {
    }
  })
}
import {getDictService} from '@/api/sysdict.js'
import {validateFormFileds} from '@/utils/validate'
const originalMessage = Vue.prototype.$message
Vue.prototype.$message = function(options){
  options.offset = 100
  return originalMessage.call(this, options);
}

// 创建全局EventBus
var EventBus = new Vue();
Object.defineProperties(Vue.prototype, {
    $bus: {
        get: function () {
            return EventBus
        }
    }
})
import { Base64 } from 'js-base64';
Vue.prototype.$base64 = Base64;

Vue.prototype.$message.success = function(options){
  return originalMessage.call(this, {
    type: 'success',
    message: options,
    offset: 100
  })
}

Vue.prototype.$message.warning = function(options){
  return originalMessage.call(this, {
    type: 'warning',
    message: options,
    offset: 100
  })
}

Vue.prototype.$message.info = function(options){
  return originalMessage.call(this, {
    type: 'info',
    message: options,
    offset: 100
  })
}

Vue.prototype.$message.error = function(options){
  return originalMessage.call(this, {
    type: 'error',
    message: options,
    offset: 100,
  })
}

Vue.prototype.$dictOptions =async (param) => {
  let options=[]
  await getDictService(param).then(res=>{
    if(res.data && res.data.length){
      options=res.data.map(item=>{
        return {
          label:item.lable,
          value:item.value
        }
      })
    }
  })
  return options
}
const originalElForm = Vue.component('ElForm')
const newElForm = originalElForm.extend({
  methods: {
    validateScroll(callback){
      this.validate((valid, obj) => {
        if(!valid && obj && Object.keys(obj)){
          validateFormFileds(this.$el, obj)
        }
        callback(valid, obj)
      })
    }
  }
})
Vue.component('ElForm', newElForm)

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
//复制组件文件 ElQuarterPicker.vue 到项目（依赖element-ui），组件源码在后面
import ElQuarterPicker from '@/components/ElQuarterPicker/index.vue'
Vue.component('ElQuarterPicker', ElQuarterPicker)
