{"name": "epms-cmcc-mics-jx-vue", "version": "1.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "buildReport": "vue-cli-service build --report"}, "dependencies": {"axios": "^0.21.1", "babel-polyfill": "^6.26.0", "bpmn-js": "^15.2.2", "bpmn-js-properties-panel": "^5.6.1", "core-js": "^3.44.0", "crypto-js": "^4.1.1", "echarts": "^5.4.3", "element-ui": "2.15.6", "form-making": "^1.2.11", "jquery": "^3.7.1", "js-cookie": "^2.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "normalize.css": "7.0.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "regenerator-runtime": "^0.14.1", "sortablejs": "^1.15.2", "vue": "^2.7.16", "vue-router": "^3.6.5", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-vuex": "~4.5.13", "@vue/cli-service": "~4.5.13", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "^6.7.2", "eslint-plugin-vue": "^9.26.0", "html-webpack-plugin": "3.2.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10", "wl-core": "^1.1.9"}, "browserslist": ["> 1%", "last 2 versions", "not ie < 10"], "engines": {"node": ">= 16.17.1", "npm": ">= 8.15.0"}, "description": "江西PMS辅助管理系统前端\r # 开发环境：\r vue2\r elementui\r node:16.17.1\r ie10+", "main": ".eslintrc.js", "repository": {"type": "git", "url": "http://**************:28083/JXProject/jx-cmcc-mics/bocoVue.git"}, "keywords": [], "author": "", "license": "ISC"}