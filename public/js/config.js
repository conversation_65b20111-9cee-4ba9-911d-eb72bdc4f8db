

/*
 * 开发环境配置文件
 */
var base = 'http://192.168.188.23:9999'//业务地址
window.g = {
  baseUrl: base,
  tokenParams: 'bocoToken',//用于请求头token的参数名
  timeout: 30000,//请求超时时限，单位毫秒，默认30秒
  loginType: 'account_pwd', // 判断是用户密码登录（默认值：account_pwd） ，还是是短信验证码登录（account_pwd_sms_code ），
  platformId: 'epms',// 登录时的platformId参数的值，如果这里不配置，将会采用默认值eoms
  client_id: 'epms',// 登录时配置的参数
  client_secret: 'pig',// 登录时配置的参数
  flowPreviewUrl: 'http://192.168.11.206:19888', //流程图地址
  groupId: '1688841303134167041',//暂存点角色管理组id
  groupName: '暂存点角色组',//暂存点角色管理组name
  sgdzRoleCode: 'zcd-sgdz', // 施工队长角色编码
  sgfzrRoleCode: 'zcd-sgdwfzr', // 施工单位负责人角色编码
  sgdwRootId: '3001', // 施工单位 组织树id
  ssoParams: ["ept"], //单点参数动态补充（后端url设置）
  auth_cas_url: base + "/auth-cas", //cas认证地址
  documentTitle: '江西移动数智赋能平台', // 浏览器显示网站的名字，若不设置则默认是“江西pms辅助管理系统”，可自定义设置
  enablePwdCheck: false,//是否开启密码规则校验
  pwdCheckRule: {
    regex: /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)([^(0-9a-zA-Z)]|[a-z]|[A-Z]|[0-9]){8,20}$/,//校验规则
    failedMsg: "密码应包含字母大小写、特殊字符、数字，长度大于8位",//提示消息
  },
  enablePwdExpired: false,//是否开启密码有效期限制
}

/*
 * 测试环境配置文件
 */
// var base = 'http://10.183.161.37:8886'//业务地址
// window.g = {
//   baseUrl: base,
//   tokenParams: 'bocoToken',//用于请求头token的参数名
//   timeout: 30000,//请求超时时限，单位毫秒，默认30秒
//   loginType: 'account_pwd', // 判断是用户密码登录（默认值：account_pwd） ，还是是短信验证码登录（account_pwd_sms_code ），
//   platformId: 'epms',// 登录时的platformId参数的值，如果这里不配置，将会采用默认值eoms
//   client_id: 'epms',// 登录时配置的参数
//   client_secret: 'pig',// 登录时配置的参数
//   flowPreviewUrl: 'http://10.183.161.37:8888', //流程图地址
//   groupId: '1688841303134167041',//暂存点角色管理组id
//   groupName: '暂存点角色组',//暂存点角色管理组name
//   sgdzRoleCode: 'zcd-sgdz', // 施工队长角色编码
//   sgfzrRoleCode: 'zcd-sgdwfzr', // 施工单位负责人角色编码
//   sgdwRootId: '3001', // 施工单位 组织树id
//   ssoParams: ["ept"], //单点参数动态补充（后端url设置）
//   auth_cas_url: base + "/auth-cas", //cas认证地址
//   documentTitle: '', // 浏览器显示网站的名字，若不设置则默认是“江西pms辅助管理系统”，可自定义设置
//   enablePwdCheck: false,//是否开启密码规则校验
//   pwdCheckRule: {
//     regex: /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)([^(0-9a-zA-Z)]|[a-z]|[A-Z]|[0-9]){8,20}$/,//校验规则
//     failedMsg: "密码应包含字母大小写、特殊字符、数字，长度大于8位",//提示消息
//   },
//   enablePwdExpired: false,//是否开启密码有效期限制
//   showGrayDay: false, // 是否显示哀悼模式，即特殊节日或国家领导人去世，页面置灰得效果，默认为false不启动
// }


/*
 * 生产环境配置文件
 */
// var base = '4LyPxhkwSpFckJtg5tix9tELDNFJX7x8QTrW31A96xw='//业务地址
// window.g = {
//   baseUrl: base,
//   tokenParams: 'bocoToken',//用于请求头token的参数名
//   timeout: 60000,//请求超时时限，单位毫秒，默认30秒
//   loginType: 'account_pwd', // 判断是用户密码登录（默认值：account_pwd） ，还是是短信验证码登录（account_pwd_sms_code ），
//   platformId: 'epms',// 登录时的platformId参数的值，如果这里不配置，将会采用默认值eoms
//   client_id: 'epms',// 登录时配置的参数
//   client_secret: 'pig',// 登录时配置的参数
//   flowPreviewUrl: '4LyPxhkwSpFckJtg5tix9ii8uoHNZXHJPqX9ZBI8ADs=', //流程图地址
//   groupId: '1688841303134167041',//暂存点角色管理组id
//   groupName: '暂存点角色组',//暂存点角色管理组name
//   sgdzRoleCode: 'zcd-sgdz', // 施工队长角色编码
//   sgfzrRoleCode: 'zcd-sgdwfzr', // 施工单位负责人角色编码
//   sgdwRootId: '3001', // 施工单位 组织树id
//   ssoParams: ["ept"], //单点参数动态补充（后端url设置）
//   // auth_cas_url: base + "/common-center-jx", //cas认证地址
//   auth_cas_url: '4LyPxhkwSpFckJtg5tix9pCdJk9pJsQI0zFVWRO695Gmk4Nrq/OqvS8OkIJ6JRWY' ,//cas认证地址
//   documentTitle: '江西移动工建辅助系统', // 浏览器显示网站的名字，若不设置则默认是“江西pms辅助管理系统”，可自定义设置
//   enablePwdCheck: true,//是否开启密码规则校验
//   pwdCheckRule: {
//     regex: /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)([^(0-9a-zA-Z)]|[a-z]|[A-Z]|[0-9]){8,20}$/,//校验规则
//     failedMsg: "密码应包含字母大小写、特殊字符、数字，长度大于8位",//提示消息
//   },
//   enablePwdExpired: false,//是否开启密码有效期限制
//   showGrayDay: false, // 是否显示哀悼模式，即特殊节日或国家领导人去世，页面置灰得效果，默认为false不启动
// }



/*
 * 外网-生产环境配置文件
 */
// var base = 'wAuAcrad0Ph5kdiPK+moEQ15xB/haoQfepYkHY+c4e4='//业务地址
// window.g = {
//   baseUrl:base,
//   tokenParams:'bocoToken',//用于请求头token的参数名
//   timeout:30000,//请求超时时限，单位毫秒，默认30秒
//   loginType:'account_pwd', // 判断是用户密码登录（默认值：account_pwd） ，还是是短信验证码登录（account_pwd_sms_code ），
//   platformId: 'epms',// 登录时的platformId参数的值，如果这里不配置，将会采用默认值eoms
//   client_id :'epms',// 登录时配置的参数
//   client_secret:'pig',// 登录时配置的参数
//   flowPreviewUrl:'4LyPxhkwSpFckJtg5tix9ii8uoHNZXHJPqX9ZBI8ADs=', //流程图地址
//   groupId:'1688841303134167041',//暂存点角色管理组id
//   groupName:'暂存点角色组',//暂存点角色管理组name
//   sgdzRoleCode:'zcd-sgdz', // 施工队长角色编码
//   sgfzrRoleCode:'zcd-sgdwfzr', // 施工单位负责人角色编码
//   sgdwRootId:'3001', // 施工单位 组织树id
//   ssoParams: ["ept"], //单点参数动态补充（后端url设置）
//   auth_cas_url: base + "/common-center-jx", //cas认证地址
//   documentTitle:'江西移*******', // 浏览器显示网站的名字，若不设置则默认是“江西p********”，可自定义设置
//   enablePwdCheck:true,//是否开启密码规则校验
//   pwdCheckRule:{
//     regex:/^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)([^(0-9a-zA-Z)]|[a-z]|[A-Z]|[0-9]){8,20}$/,//校验规则
//     failedMsg:"密码应包含字母大小写、特殊字符、数字，长度大于8位",//提示消息
//   },
//   enablePwdExpired:false,//是否开启密码有效期限制
//
// }



