江西PMS辅助管理系统前端
# 开发环境：
vue2
elementui
node:16.17.1
ie10+

# 文件目录结构

/styles 样式文件夹。包含全局都要使用的基础组件的样式、样式变量这些。例如sidebar.scss headerbar.scss , variables.scss
关于样式变量：有element-variables.scss（用于elementuiui默认样式重写），variables.scss（非elementuiui默认样式重写情况下的变量）

/components 公共vue子组件的文件夹。本项目并非全局注册，所以每个页面需要自己引入需要用的子组件。例如：
```javascript

<script>
  import { Sidebar } from './components'

  .....
  export default {
      components: {
        Sidebar,
      }
  }
</script>

```

# 前端公共组件

## 查询条件（查询表单）

### 1.竖着两列布局、且提交按钮在右侧的
1.基础结构：给elform设置inline属性、加上自定义样式名search-form,search-form-inputs，search-form-btns，search-form-btns--content这是保证样式的必需条件。

```vue
    <el-form ref="form" inline :model="form" label-width="100px" class="search-form">
      <div class="search-form-inputs">
       
      </div>
      <div class="search-form-btns">
        <div class="search-form-btns--content">
          <el-button type="primary" class="search-btn" @click="onSubmit">查询</el-button>
          <el-button class="default-btn">重置</el-button>
        </div>
      </div>
    </el-form>
```

2.根据实际业务要求，添加单行文本输入框、下拉框等，例如：

```vue
    <el-form ref="form" inline :model="form" label-width="100px" class="search-form">
      <div class="search-form-inputs">
        // 在这里加入查询输入框
        <el-form-item label="活动名称" class="search-form-inputitem">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="活动区域" class="search-form-inputitem">
          <el-select v-model="form.region" placeholder="请选择活动区域">
            <el-option label="区域一" value="shanghai"></el-option>
            <el-option label="区域二" value="beijing"></el-option>
          </el-select>
        </el-form-item>
       
      </div>
      <div class="search-form-btns">
        <div class="search-form-btns--content">
          <el-button type="primary" class="search-btn" @click="onSubmit">查询</el-button>
          <el-button class="default-btn">重置</el-button>
        </div>
      </div>
    </el-form>
```
### 2.公共查询表单：

demo todo